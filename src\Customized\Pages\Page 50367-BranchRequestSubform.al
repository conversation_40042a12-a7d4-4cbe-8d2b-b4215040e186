page 50367 BranchRequestSubform
{
    Caption = 'Branch Request Subform';
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = ListPart;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = BranchRequestLine;

    layout
    {
        area(Content)
        {
            Repeater(Control1102152000)
            {
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var
                        BranchReqLine: record BranchRequestLine;
                    begin
                        BranchReqLine.Reset();
                        BranchReqLine.SetRange("Document No.", "Document No.");
                        BranchReqLine.SetRange("Item No.", "Item No.");
                        if BranchReqLine.FindFirst() then
                            Error('Item %1 Already Exists %2');
                    end;

                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Requested Quantity"; "Requested Quantity")
                {
                    ApplicationArea = all;
                }
                field("Wt. of the Qty Loading in Tons"; "Wt. of the Qty Loading in Tons")
                {
                    ApplicationArea = all;
                    Editable = false;//PKONJ18
                }

                field(ItemInventory; itemAv.Inventory)
                {
                    ApplicationArea = Location;
                    Caption = 'Inventory';
                    DecimalPlaces = 0 : 5;
                    DrillDown = true;
                    Editable = false;
                    ToolTip = 'Specifies the inventory level of an item.';

                    trigger OnDrillDown()
                    begin
                        SetItemFilter;
                        //Message('%1', itemAv."No.");
                        ItemAvailFormsMgt.ShowItemLedgerEntries(itemAv, false);
                    end;
                }
                field(ProjAvailableBalance; ProjAvailableBalance)
                {
                    ApplicationArea = Location;
                    Caption = 'Projected Available Balance';
                    DecimalPlaces = 0 : 5;
                    ToolTip = 'Specifies the item''s availability. This quantity includes all known supply and demand but does not include anticipated demand from demand forecasts or blanket sales orders or suggested supplies from planning or requisition worksheets.';
                    Editable = false;
                    trigger OnDrillDown()
                    begin
                        ShowItemAvailLineList(4);
                    end;
                }
                //Balu 05142021>>
                field("Issued Quantity"; "Issued Quantity")
                {
                    ApplicationArea = all;
                }
                field("Unit Of Measure"; "Unit Of Measure")
                {
                    ApplicationArea = all;
                }
                field("Qty Per UOM"; "Qty Per UOM")
                {
                    ApplicationArea = all;
                }
                field("Requested Qty(Base)"; "Requested Qty(Base)")
                {
                    ApplicationArea = all;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    trigger OnModifyRecord(): Boolean

    begin
        BranchGVar.Get("Document No.");
        if BranchGVar.TransferOrderCreated then
            Error('you can not modify branch request. Already transfer order is created.');
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        BranchGVar.Get("Document No.");
        if BranchGVar.TransferOrderCreated then
            Error('you can not deleted branch request. Already transfer order is created.');
    end;
    //Balu 05142021>>
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    begin
        BranchGVar.Get("Document No.");
        BranchGVar.TESTFIELD("From Location");
        BranchGVar.TESTFIELD("To Location");
        "From Location" := BranchGVar."From Location";
        "To Location" := BranchGVar."To Location";
    end;
    //Balu 05142021<<
    trigger OnInit()
    begin
        PeriodStart := 0D;
        PeriodEnd := WorkDate();
    end;

    local procedure CalcAvailQuantities(var GrossRequirement: Decimal; var PlannedOrderRcpt: Decimal; var ScheduledRcpt: Decimal; var PlannedOrderReleases: Decimal; var ProjAvailableBalance: Decimal; var ExpectedInventory: Decimal; var AvailableInventory: Decimal)
    var
        DummyQtyAvailable: Decimal;
    begin
        SetItemFilter;
        ItemAvailFormsMgt.CalcAvailQuantities(
          itemAv, AmountType = AmountType::"Balance at Date",
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, DummyQtyAvailable, AvailableInventory);
    end;

    local procedure SetItemFilter()
    begin
        itemAv.RESEt;
        itemAv.SetRange("No.", "Item No.");
        /*if AmountType = AmountType::"Net Change" then
            itemAv.SetRange("Date Filter", PeriodStart, PeriodEnd)
        else*/
        itemAv.SetRange("Date Filter", 0D, WorkDate());
        itemAv.SetRange("Location Filter", "From Location");
        IF itemAv.findfirst then
            itemAv.CalcFields(Inventory);
    end;

    local procedure ShowItemAvailLineList(What: Integer)
    begin
        SetItemFilter;
        ItemAvailFormsMgt.ShowItemAvailLineList(itemAv, What);
    end;

    var
        myInt: Integer;
        BranchGVar: Record BranchRequest;
        item: Record Item;
        itemAv: Record Item;
        ItemAvailFormsMgt: Codeunit "Item Availability Forms Mgt";
        PlannedOrderReleases: Decimal;
        GrossRequirement: Decimal;
        PlannedOrderRcpt: Decimal;
        ScheduledRcpt: Decimal;
        ProjAvailableBalance: Decimal;
        PeriodStart: Date;
        PeriodEnd: Date;
        AmountType: Option "Net Change","Balance at Date";
        ExpectedInventory: Decimal;
        QtyAvailable: Decimal;

}