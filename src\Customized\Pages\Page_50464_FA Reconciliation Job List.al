page 50464 "FA Reconciliation Job List"
{
    // version TRKIT
    Editable = false;
    CardPageId = "Fa Reconciliation Job";
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = List;
    SourceTable = FA_VERIFICATION_JOB;
    UsageCategory = Administration;
    ApplicationArea = All;


    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Verification Task Number"; "Verification Task Number")
                {
                    ApplicationArea = All;
                }
                field("Verification Task Desc"; "Verification Task Desc")
                {
                    ApplicationArea = All;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    ApplicationArea = All;
                }
                field("Navision_Sync_ Time"; "Navision_Sync_ Time")
                {
                    ApplicationArea = All;
                }
                field("No. Series"; "No. Series")
                {
                    ApplicationArea = All;
                }
                field("Verification Task Code"; "Verification Task Code")
                {
                    ApplicationArea = All;
                }
                field("FA Location Code"; "FA Location Code")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
    }
}

