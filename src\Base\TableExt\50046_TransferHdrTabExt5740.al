tableextension 50046 TransferHdr extends "Transfer Header"
{
    fields
    {
        field(50001; "Transfer Type"; Enum "Transfer Request Type")
        {
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if "Created Date" = 0DT then
                    setCreatedDate();
            end;
        }
        field(50002; "Loading Advice Printed"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }
        //B2BMS
        field(50013; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50014; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50015; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50016; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
        field(50004; "Transfer to Resp. Center"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center";
        }
        modify("Transfer-from Code")
        {
            trigger OnAfterValidate()
            var
                LocationLrec: Record Location;
            begin
                //Balu 05122021 >>
                if LocationLrec.Get("Transfer-from Code") then
                    LocationLrec.TestField(Blocked, false);
                //Balu 05122021<<
            end;
        }
        modify("Transfer-to Code")
        {
            trigger OnAfterValidate()
            var
                LocRec: Record Location;
            begin
                //HO1.0
                IF LocRec.GET("Transfer-to Code") THEN BEGIN
                    if "Transfer Type" <> "Transfer Type"::"Transfer Ticket" then begin
                        LocRec.TestField(Blocked, false);//Balu 05122021<<
                        LocRec.TESTFIELD("Responsibility Center");
                    end;
                    "Transfer to Resp. Center" := LocRec."Responsibility Center";
                END;
                //HO1.0                
            end;
        }
        field(50005; "MRS Ref. No."; Code[20])
        {
            Description = 'HO1.0';
            DataClassification = CustomerContent;
            TableRelation = MRSHeader WHERE("Transfer Order Ref. No." = FIELD("No."));
        }

        field(50006; "MRS Category Code"; Code[20])
        {
            Description = 'Auto_Prod_MRS';
            Editable = false;
            DataClassification = CustomerContent;
            TableRelation = "Item Category";
        }
        field(50007; "Production Batch No."; Code[20])
        {
            Caption = 'Production Batch No.';
            DataClassification = CustomerContent;
            Description = 'HO1.0';
            Editable = false;
        }
        field(50008; "Production Order No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50009; "Branch Request No"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "Maintenance Job Card No"; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50011; "Manual MRS No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50012; "order status"; Enum OrderStat)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50018; "Released By"; Code[50])
        {
            DataClassification = ToBeClassified;
        }//B2BSPON22OCT18
        //G2S 11/06/24
        field(50019; FromBranchReqst; Boolean)
        {
            DataClassification = ToBeClassified;
        }
    }
    //B2BMS
    trigger OnInsert()
    begin
        "Created By" := UserId;
        "Created Date" := CreateDateTime(WorkDate, Time);
    end;

    trigger OnModify()
    begin
        "Modified By" := UserId;
        "Modified date" := CreateDateTime(WorkDate, Time);
    end;
    //B2BMS


    procedure GetGateEntryLines()
    var
        PostedGateEntryLine: Record "Posted Gate Entry Line";
        GateEntryAttachment: Record "Gate Entry Attachment";
    begin
        PostedGateEntryLine.MODIFYALL(Mark, FALSE);

        PostedGateEntryLine.RESET;
        PostedGateEntryLine.SETCURRENTKEY("Entry Type", "Source Type", "Source No.", Status);
        PostedGateEntryLine.SETRANGE("Entry Type", PostedGateEntryLine."Entry Type"::Inward);
        PostedGateEntryLine.SETRANGE("Source Type", PostedGateEntryLine."Source Type"::"Transfer Receipt");
        PostedGateEntryLine.SETRANGE("Source No.", "No.");
        PostedGateEntryLine.SETRANGE(Status, PostedGateEntryLine.Status::Open);
        GateEntryAttachment.SETCURRENTKEY("Source Type", "Source No.", "Entry Type", "Gate Entry No.", "Line No.");
        IF PostedGateEntryLine.FINDSET THEN
            REPEAT
                GateEntryAttachment.SETRANGE("Source No.", PostedGateEntryLine."Source No.");
                GateEntryAttachment.SETRANGE("Gate Entry No.", PostedGateEntryLine."Gate Entry No.");
                GateEntryAttachment.SETRANGE("Line No.", PostedGateEntryLine."Line No.");
                IF NOT GateEntryAttachment.FINDFIRST THEN BEGIN
                    PostedGateEntryLine.Mark := TRUE;
                    PostedGateEntryLine.MODIFY;
                    COMMIT;
                END;
            UNTIL PostedGateEntryLine.NEXT = 0;

        PostedGateEntryLine.RESET;
        PostedGateEntryLine.SETCURRENTKEY("Entry Type", "Source Type", "Source No.", Status);
        PostedGateEntryLine.SETRANGE(Mark, TRUE);

        IF PostedGateEntryLine.FINDFIRST THEN BEGIN
            PostedGateEntryLineList.SETTABLEVIEW(PostedGateEntryLine);
            IF PAGE.RUNMODAL(PAGE::"Posted Gate Entry Line List", PostedGateEntryLine) = ACTION::LookupOK THEN BEGIN
                GateEntryAttachment.INIT;
                GateEntryAttachment."Source Type" := PostedGateEntryLine."Source Type";
                GateEntryAttachment."Source No." := PostedGateEntryLine."Source No.";
                GateEntryAttachment."Entry Type" := PostedGateEntryLine."Entry Type";
                GateEntryAttachment."Gate Entry No." := PostedGateEntryLine."Gate Entry No.";
                GateEntryAttachment."Line No." := PostedGateEntryLine."Line No.";
                GateEntryAttachment.INSERT;
            END;
        END;
    end;

    procedure setCreatedDate()
    begin
        "Created Date" := CreateDateTime(WorkDate, Time);
    end;

    var
        PostedGateEntryLineList: Page "Posted Gate Entry Line List";

}
