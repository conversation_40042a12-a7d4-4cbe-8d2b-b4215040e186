﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>9c156933-6987-4ca3-9b80-cee01672dd3f</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2.38278cm</Height>
        <Style />
      </Body>
      <Width>24.468cm</Width>
      <Page>
        <PageHeader>
          <Height>2.06375cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>7.42813cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>21cm</PageHeight>
        <PageWidth>29.7cm</PageWidth>
        <LeftMargin>2cm</LeftMargin>
        <RightMargin>2cm</RightMargin>
        <TopMargin>2.1cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="MATERIALDISPOSALVOUCHERCaption">
          <DataField>MATERIALDISPOSALVOUCHERCaption</DataField>
        </Field>
        <Field Name="DATEOFMDVCaption">
          <DataField>DATEOFMDVCaption</DataField>
        </Field>
        <Field Name="MDVBatchCodeCaption">
          <DataField>MDVBatchCodeCaption</DataField>
        </Field>
        <Field Name="RequestingAccountLocationCaption">
          <DataField>RequestingAccountLocationCaption</DataField>
        </Field>
        <Field Name="NoCaption">
          <DataField>NoCaption</DataField>
        </Field>
        <Field Name="DescriptionCaption">
          <DataField>DescriptionCaption</DataField>
        </Field>
        <Field Name="RequisitionAcctLocCaption">
          <DataField>RequisitionAcctLocCaption</DataField>
        </Field>
        <Field Name="ShortcutDimension2CodeCaption">
          <DataField>ShortcutDimension2CodeCaption</DataField>
        </Field>
        <Field Name="LocationCodeCaption">
          <DataField>LocationCodeCaption</DataField>
        </Field>
        <Field Name="QtyBatchedNotDisposedCaption">
          <DataField>QtyBatchedNotDisposedCaption</DataField>
        </Field>
        <Field Name="QuantityCaption">
          <DataField>QuantityCaption</DataField>
        </Field>
        <Field Name="AvailableStockCaption">
          <DataField>AvailableStockCaption</DataField>
        </Field>
        <Field Name="UnitCostCaption">
          <DataField>UnitCostCaption</DataField>
        </Field>
        <Field Name="AmountCaption">
          <DataField>AmountCaption</DataField>
        </Field>
        <Field Name="PREPAREDBYCaption">
          <DataField>PREPAREDBYCaption</DataField>
        </Field>
        <Field Name="HODUSERDEPTCaption">
          <DataField>HODUSERDEPTCaption</DataField>
        </Field>
        <Field Name="QCMGRPRDUCTIONMGRCaption">
          <DataField>QCMGRPRDUCTIONMGRCaption</DataField>
        </Field>
        <Field Name="FINANCEMGRCaption">
          <DataField>FINANCEMGRCaption</DataField>
        </Field>
        <Field Name="HSEMGRCaption">
          <DataField>HSEMGRCaption</DataField>
        </Field>
        <Field Name="RSCaption">
          <DataField>RSCaption</DataField>
        </Field>
        <Field Name="AUTHORISEDBYCaption">
          <DataField>AUTHORISEDBYCaption</DataField>
        </Field>
        <Field Name="MDVReleasedByCaption">
          <DataField>MDVReleasedByCaption</DataField>
        </Field>
        <Field Name="NOTECaption">
          <DataField>NOTECaption</DataField>
        </Field>
        <Field Name="AnyamendmentCaption">
          <DataField>AnyamendmentCaption</DataField>
        </Field>
        <Field Name="CommentCaption">
          <DataField>CommentCaption</DataField>
        </Field>
        <Field Name="COMPANYNAME">
          <DataField>COMPANYNAME</DataField>
        </Field>
        <Field Name="FORMAT_TODAY_0_4">
          <DataField>FORMAT_TODAY_0_4</DataField>
        </Field>
        <Field Name="Document_Date">
          <DataField>Document_Date</DataField>
        </Field>
        <Field Name="MDV_No_">
          <DataField>MDV_No_</DataField>
        </Field>
        <Field Name="RequestingBu">
          <DataField>RequestingBu</DataField>
        </Field>
        <Field Name="Comment">
          <DataField>Comment</DataField>
        </Field>
        <Field Name="tamt">
          <DataField>tamt</DataField>
        </Field>
        <Field Name="tamtFormat">
          <DataField>tamtFormat</DataField>
        </Field>
        <Field Name="PRNSCDENGGCaption">
          <DataField>PRNSCDENGGCaption</DataField>
        </Field>
        <Field Name="AmountInWordsCaption">
          <DataField>AmountInWordsCaption</DataField>
        </Field>
        <Field Name="PageCaption">
          <DataField>PageCaption</DataField>
        </Field>
        <Field Name="AmountInword">
          <DataField>AmountInword</DataField>
        </Field>
        <Field Name="Shortcut_Dimension_1_Code">
          <DataField>Shortcut_Dimension_1_Code</DataField>
        </Field>
        <Field Name="Shortcut_Dimension_2_Code">
          <DataField>Shortcut_Dimension_2_Code</DataField>
        </Field>
        <Field Name="No_">
          <DataField>No_</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="Location_Code">
          <DataField>Location_Code</DataField>
        </Field>
        <Field Name="Qty__Batched_Not_Disposed">
          <DataField>Qty__Batched_Not_Disposed</DataField>
        </Field>
        <Field Name="Qty__Batched_Not_DisposedFormat">
          <DataField>Qty__Batched_Not_DisposedFormat</DataField>
        </Field>
        <Field Name="Quantity">
          <DataField>Quantity</DataField>
        </Field>
        <Field Name="QuantityFormat">
          <DataField>QuantityFormat</DataField>
        </Field>
        <Field Name="Available_Stock">
          <DataField>Available_Stock</DataField>
        </Field>
        <Field Name="Available_StockFormat">
          <DataField>Available_StockFormat</DataField>
        </Field>
        <Field Name="Unit_Cost">
          <DataField>Unit_Cost</DataField>
        </Field>
        <Field Name="Unit_CostFormat">
          <DataField>Unit_CostFormat</DataField>
        </Field>
        <Field Name="amt">
          <DataField>amt</DataField>
        </Field>
        <Field Name="amtFormat">
          <DataField>amtFormat</DataField>
        </Field>
        <Field Name="Document_No_">
          <DataField>Document_No_</DataField>
        </Field>
        <Field Name="Line_No_">
          <DataField>Line_No_</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>