page 50701 "Pos. Im.C Purchase Credit Memo"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created for Posted Import Purchase Charge Cr Memo.

    Caption = 'Pos. Im.C Purchase Credit Memo';
    InsertAllowed = false;
    PageType = Document;
    RefreshOnActivate = true;
    SourceTable = "Purch. Cr. Memo Hdr.";
    SourceTableView = WHERE("Purchase Type" = FILTER(Local));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    Editable = false;
                }
                field("Buy-from Vendor No."; "Buy-from Vendor No.")
                {
                    Editable = false;
                }
                field("Buy-from Contact No."; "Buy-from Contact No.")
                {
                    Editable = false;
                }
                field("Buy-from Vendor Name"; "Buy-from Vendor Name")
                {
                    Editable = false;
                }
                field("Buy-from Address"; "Buy-from Address")
                {
                    Editable = false;
                }
                field("Buy-from Address 2"; "Buy-from Address 2")
                {
                    Editable = false;
                }
                field("Buy-from Post Code"; "Buy-from Post Code")
                {
                    Caption = 'Buy-from Post Code/City';
                    Editable = false;
                }
                field("Buy-from City"; "Buy-from City")
                {
                    Editable = false;
                }
                field("Buy-from Contact"; "Buy-from Contact")
                {
                    Editable = false;
                }
                field("Posting Date"; "Posting Date")
                {
                    Editable = false;
                }
                field("Document Date"; "Document Date")
                {
                    Editable = false;
                }
                field("Pre-Assigned No."; "Pre-Assigned No.")
                {
                    Editable = false;
                }
                field("Vendor Cr. Memo No."; "Vendor Cr. Memo No.")
                {
                    Editable = false;
                }
                field("Order Address Code"; "Order Address Code")
                {
                    Editable = false;
                }
                field("Purchaser Code"; "Purchaser Code")
                {
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    Editable = false;
                }
                field("No. Printed"; "No. Printed")
                {
                    Editable = false;
                }
            }
            part(PurchCrMemoLines; "Post. Im.C Pur. Cr. Mem Sform")
            {
                SubPageLink = "Document No." = FIELD("No.");
            }
            group(Invoicing)
            {
                Caption = 'Invoicing';
                field("Pay-to Vendor No."; "Pay-to Vendor No.")
                {
                    Editable = false;
                }
                field("Pay-to Name"; "Pay-to Name")
                {
                    Editable = false;
                }
                field("Pay-to Contact No."; "Pay-to Contact No.")
                {
                    Editable = false;
                }
                field("Pay-to Address"; "Pay-to Address")
                {
                    Editable = false;
                }
                field("Pay-to Address 2"; "Pay-to Address 2")
                {
                    Editable = false;
                }
                field("Pay-to Post Code"; "Pay-to Post Code")
                {
                    Caption = 'Pay-to Post Code/City';
                    Editable = false;
                }
                field("Pay-to City"; "Pay-to City")
                {
                    Editable = false;
                }
                field("Pay-to Contact"; "Pay-to Contact")
                {
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    Editable = false;
                }
                field("Applies-to Doc. Type"; "Applies-to Doc. Type")
                {
                    Editable = false;
                }
                field("Applies-to Doc. No."; "Applies-to Doc. No.")
                {
                    Editable = false;
                }
            }
            group(Shipping)
            {
                Caption = 'Shipping';
                field("Ship-to Name"; "Ship-to Name")
                {
                    Editable = false;
                }
                field("Ship-to Address"; "Ship-to Address")
                {
                    Editable = false;
                }
                field("Ship-to Address 2"; "Ship-to Address 2")
                {
                    Editable = false;
                }
                field("Ship-to Post Code"; "Ship-to Post Code")
                {
                    Caption = 'Ship-to Post Code/City';
                    Editable = false;
                }
                field("Ship-to City"; "Ship-to City")
                {
                    Editable = false;
                }
                field("Ship-to Contact"; "Ship-to Contact")
                {
                    Editable = false;
                }
                field("Location Code"; "Location Code")
                {
                    Editable = false;
                }
            }
            group("Foreign Trade")
            {
                Caption = 'Foreign Trade';
                field("Currency Code"; "Currency Code")
                {

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        ChangeExchangeRate.EDITABLE(false);
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            "Currency Factor" := ChangeExchangeRate.GetParameter;
                            MODIFY;
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
            }
            /*group(BizTalk)
            {
                Caption = 'BizTalk';
                field("BizTalk Purchase Credit Memo";"BizTalk Purchase Credit Memo")
                {
                    Editable = false;
                }
                field("Date Received";"Date Received")
                {
                    Editable = false;
                }
                field("Time Received";"Time Received")
                {
                    Editable = false;
                }
            }*/
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Cr. Memo")
            {
                Caption = '&Cr. Memo';
                action(Statistics)
                {
                    Caption = 'Statistics';
                    RunObject = Page "Purch. Credit Memo Statistics";
                    RunPageLink = "No." = FIELD("No.");
                    ShortCutKey = 'F9';
                }
                action("Co&mments")
                {
                    Caption = 'Co&mments';
                    RunObject = Page "Purch. Comment Sheet";
                    RunPageLink = "Document Type" = CONST("Posted Credit Memo"),
                                  "No." = FIELD("No."),
                                  "Document Line No." = CONST(0);
                }
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries: Page "Posted Approval Entries";
                    begin
                        //PostedApprovalEntries.Setfilters(DATABASE::"Purch. Cr. Memo Hdr.","No.");
                        PostedApprovalEntries.RUN;
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    CurrPage.SETSELECTIONFILTER(PurchCrMemoHeader);
                    PurchCrMemoHeader.PrintRecords(true);
                end;
            }
            action("&Navigate")
            {
                Caption = '&Navigate';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Navigate;
                end;
            }
        }
    }

    var
        PurchCrMemoHeader: Record "Purch. Cr. Memo Hdr.";
        ChangeExchangeRate: Page "Change Exchange Rate";
}

