tableextension 50043 InvoicePostExt extends "Invoice Post. Buffer"
{
    fields
    {
        field(50015; "Old_PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "Last Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Current Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50013; "Description 2"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        //RFC22
        field(50018; "Return Reason Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //RFC22
    }

    var
        myInt: Integer;
}