page 50938 "Customer Credit Limit Appros"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Customer Credit Limit Approval";
    SourceTable = "Customer Credit Approvals";
    Caption = 'Customer Credit Limit Approvals List';
    //P.K on 15.05.2021

    layout
    {
        area(Content)
        {
            repeater(Control1102152000)
            {
                field("No."; "No.")
                {
                    ApplicationArea = All;

                }
                field("Description"; "Description")
                {
                    ApplicationArea = All;

                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;

                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;

                }
                field(Status; Status)
                {
                    ApplicationArea = All;

                }
            }
        }
    }
}