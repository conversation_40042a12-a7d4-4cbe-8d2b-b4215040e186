pageextension 50325 SalesQuotSub extends "Sales Quote Subform"
{
    layout
    {
        addafter(Quantity)
        {
            field("Wt. of the Qty Loading in Tons"; "Wt. of the Qty Loading in Tons")//PKONJ3
            {

            }
        }
        addafter(Type)
        {
            field("Item Category"; "Item Category")
            {
                ApplicationArea = all;//b2bpksalecorr12
            }
            field("Category Wise Items"; "Category Wise Items")
            {
                ApplicationArea = all;//b2bpksalecorr12
            }
            field(ItemInventory; itemAv.Inventory)
            {
                ApplicationArea = Location;
                Caption = 'Inventory';
                DecimalPlaces = 0 : 5;
                DrillDown = true;
                Editable = false;
                ToolTip = 'Specifies the inventory level of an item.';//B2BPKON200521 Move entire field

                trigger OnDrillDown()
                begin
                    SetItemFilter;
                    //Message('%1', itemAv."No.");
                    ItemAvailFormsMgt.ShowItemLedgerEntries(itemAv, false);
                end;
            }
            field(ProjAvailableBalance; ProjAvailableBalance)
            {
                ApplicationArea = Location;
                Caption = 'Projected Available Balance';//B2BPKON200521 Move entire field
                DecimalPlaces = 0 : 5;
                ToolTip = 'Specifies the item''s availability. This quantity includes all known supply and demand but does not include anticipated demand from demand forecasts or blanket sales orders or suggested supplies from planning or requisition worksheets.';
                Editable = false;
                trigger OnDrillDown()
                begin
                    ShowItemAvailLineList(4);
                end;
            }
        }
        addafter("Line Amount")
        {
            field("Fixed Rebate Amount"; "Fixed Rebate Amount")
            {
                Editable = false;
            }
        }
        modify("No.")
        {
            trigger OnAfterValidate()
            var
                SaleliLrec: Record "Sales Line";
            begin
                IF type = type::Item then
                    Error('Please select the item from category wise items field.');//b2bpksalecorr12
                                                                                    //PKONSE17>>
                SaleliLrec.RESET;
                SaleliLrec.SetRange("Document Type", SaleliLrec."Document Type"::Quote);//PKONSE20
                SaleliLrec.SetRange("Document No.", "Document No.");
                SaleliLrec.SetRange(Type, SaleliLrec.Type::Item);//PKONSE20
                SaleliLrec.SetRange("No.", "No.");
                SaleliLrec.SetRange("Gift Item", false);
                IF SaleliLrec.FindFirst() THEN
                    IF NOT "Gift Item" THEN
                        Error('Already Same Item is selected in line no. %1', SaleliLrec."Line No.");
                //PKONSE17<<
            end;

        }
        modify("Unit Price")
        {

            trigger OnBeforeValidate()//PKONAU11 whole trigger
            var
                SalHdr: Record "sales header";
            BEGIN
                if Rec.Type = rec.Type::Item then
                    error('You can not modify the unit price.')
            END;

        }
        modify("Line Amount")
        {
            Editable = false;//b2bpksalecorr10
        }
        modify("Location Code")
        {
            Editable = false;//b2bpksalecorr11
        }
        modify("Line Discount %")
        {
            Editable = false;//b2bpksalecorr11
        }
        modify("Line Discount Amount")
        {
            Editable = false;//b2bpksalecorr11
        }


    }


    actions
    {
        // Add changes to page actions here

    }

    trigger OnAfterGetRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    local procedure CalcAvailQuantities(var GrossRequirement: Decimal; var PlannedOrderRcpt: Decimal; var ScheduledRcpt: Decimal; var PlannedOrderReleases: Decimal; var ProjAvailableBalance: Decimal; var ExpectedInventory: Decimal; var AvailableInventory: Decimal)
    var
        DummyQtyAvailable: Decimal;
    begin
        SetItemFilter;
        ItemAvailFormsMgt.CalcAvailQuantities(
          itemAv, AmountType = AmountType::"Balance at Date",
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, DummyQtyAvailable, AvailableInventory);
    end;

    local procedure SetItemFilter()
    begin
        itemAv.RESEt;
        itemAv.SetRange("No.", "No.");
        /*if AmountType = AmountType::"Net Change" then
            itemAv.SetRange("Date Filter", PeriodStart, PeriodEnd)
        else*/
        itemAv.SetRange("Date Filter", 0D, WorkDate());
        itemAv.SetRange("Location Filter", "Location Code");
        IF itemAv.findfirst then
            itemAv.CalcFields(Inventory);
    end;

    local procedure ShowItemAvailLineList(What: Integer)
    begin
        SetItemFilter;
        ItemAvailFormsMgt.ShowItemAvailLineList(itemAv, What);
    end;

    var
        myInt: Integer;
        LotFilterText: Text;
        itemAv: Record Item;
        ProjAvailableBalance: Decimal;
        ItemAvailFormsMgt: Codeunit "Item Availability Forms Mgt";
        PlannedOrderReleases: Decimal;
        GrossRequirement: Decimal;
        PlannedOrderRcpt: Decimal;
        ScheduledRcpt: Decimal;
        PeriodStart: Date;
        PeriodEnd: Date;
        ExpectedInventory: Decimal;
        QtyAvailable: Decimal;
        AmountType: Option "Net Change","Balance at Date";
}