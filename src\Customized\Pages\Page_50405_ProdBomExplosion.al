page 50405 "Prod. Bom Explosion"
{

    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    PageType = Card;
    SourceTable = "Prod. BOM Explosion";
    UsageCategory = tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            field(YearM; YearM)
            {
                APPLICATIONAREA = ALL;
                trigger OnValidate()
                begin
                    IF YearM <> 0 THEN
                        SETRANGE(Date, DMY2DATE(1, 1, YearM), DMY2DATE(31, 12, YearM))
                    ELSE
                        SETRANGE(Date);
                END;
            }
            field(MonthM; MonthM)
            {
                APPLICATIONAREA = ALL;
                trigger OnValidate()
                BEGIN

                    IF YearM > 0 THEN
                        IF MonthM > 0 THEN
                            SETRANGE(Date, DMY2DATE(1, MonthM, YearM), CALCDATE('1M', DMY2DATE(1, MonthM, YearM)) - 1);
                END;
            }

            repeater(Control1)
            {
                ShowCaption = FALSE;
                field("Item No."; "Item No.")
                {
                    ApplicationArea = all;
                }
                field("Budget Name"; Rec."Budget Name")
                {
                    ApplicationArea = All;
                }
                field("Current Stock"; Rec."Current Stock")
                {
                    ApplicationArea = All;
                }
                field(Date; Rec.Date)
                {
                    ApplicationArea = All;
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                }
                field("Final Budget Quantity"; Rec."Final Budget Quantity")
                {
                    ApplicationArea = All;
                }
                field("LOT Qty"; Rec."LOT Qty")
                {
                    ApplicationArea = All;
                }
                field("Lead Time Quantity"; Rec."Lead Time Quantity")
                {
                    ApplicationArea = All;
                }
                field("Lead Time(Days)"; Rec."Lead Time(Days)")
                {
                    ApplicationArea = All;
                }
                field("Line No."; Rec."Line No.")
                {
                    ApplicationArea = All;
                }
                field("Location Filter"; Rec."Location Filter")
                {
                    ApplicationArea = All;
                }
                field("Min. Stock (Days)"; Rec."Min. Stock (Days)")
                {
                    ApplicationArea = All;
                }
                field("Min. Stock (Qty.)"; Rec."Min. Stock (Qty.)")
                {
                    ApplicationArea = All;
                }
                field("Outstanding Order"; Rec."Outstanding Order")
                {
                    ApplicationArea = All;
                }
                field("Outstanding Orders"; Rec."Outstanding Orders")
                {
                    ApplicationArea = All;
                }
                field("PR No."; Rec."PR No.")
                {
                    ApplicationArea = All;
                }
                field("Procurement Budget"; Rec."Procurement Budget")
                {
                    ApplicationArea = All;
                }
                field("Prod. Bom No."; Rec."Prod. Bom No.")
                {
                    ApplicationArea = All;
                }
                field("Prod. Item No."; Rec."Prod. Item No.")
                {
                    ApplicationArea = All;
                }
                field("Select for PR"; Rec."Select for PR")
                {
                    ApplicationArea = All;
                }
                field("Shortfall / Excess"; Rec."Shortfall / Excess")
                {
                    ApplicationArea = All;
                }
                field("Stock Available"; Rec."Stock Available")
                {
                    ApplicationArea = All;
                }
                field("Total Consumption"; Rec."Total Consumption")
                {
                    ApplicationArea = All;
                }
                field("Total Exposure (Days)"; Rec."Total Exposure (Days)")
                {
                    ApplicationArea = All;
                }
                field("Total Exposure (Qty.)"; Rec."Total Exposure (Qty.)")
                {
                    ApplicationArea = All;
                }
                field("Unit of Measure"; Rec."Unit of Measure")
                {
                    ApplicationArea = All;
                }
                field("Balance Quantity"; Rec."Balance Quantity")
                {
                    ApplicationArea = All;
                }
                field("Actual Tolerance %"; Rec."Actual Tolerance %")
                {
                    ApplicationArea = All;
                }
            }
        }


    }

    actions
    {
        area(navigation)
        {
            group("&Requisition")
            {
                Caption = '&Requisition';
                action(CreatePR)
                {
                    ApplicationArea = all;
                    Caption = 'CreatePR';
                    ShortCutKey = 'Shift+Ctrl+D';
                    Image = Create;
                    trigger OnAction();
                    begin
                        CreatePurchR;
                    end;
                }

            }
        }
    }

    trigger OnOpenPage();
    begin

    end;

    Procedure CreatePurchR()
    BEGIN
        IF CONFIRM(Text00001, FALSE) THEN BEGIN
            ProdBomEx.RESET;
            ProdBomEx.SETRANGE("Select for PR", TRUE);
            ProdBomEx.SETFILTER("LOT Qty", '<>%1', 0);
            ProdBomEx.SETFILTER("PR No.", '=%1', '');
            IF ProdBomEx.FINDFIRST THEN BEGIN
                PurchReqHdr.INIT;
                PurchReqHdr."No." := '';
                PurchReqHdr."Document Date" := TODAY;
                PurchReqLin.LOCKTABLE;
                PurchReqHdr.INSERT(TRUE);
                PurchReqHdr.VALIDATE("Posting Date", TODAY);
                PurchReqHdr.VALIDATE(Status, PurchReqHdr.Status::Open);
                IF InventorySet.GET THEN;
                PurchReqHdr.VALIDATE("No. Series", InventorySet."Requisition Nos.");
                PurchReqHdr.VALIDATE("Created By", USERID);
                PurchReqHdr.VALIDATE("Created Date", CURRENTDATETIME);
                PurchReqHdr.VALIDATE(Comment, 'SNOP Purchase Requisition');
                PurchReqHdr.MODIFY;
                ProdBomEx.RESET;
                ProdBomEx.SETRANGE("Select for PR", TRUE);
                ProdBomEx.SETFILTER("LOT Qty", '<>%1', 0);
                IF ProdBomEx.FINDFIRST THEN
                    REPEAT
                        PurchReqLin.INIT;
                        PurchReqLin."Document No." := PurchReqHdr."No.";

                        PurchReqLin2.RESET;
                        PurchReqLin2.SETRANGE("Document No.", PurchReqHdr."No.");
                        IF PurchReqLin2.FINDLAST THEN;
                        PurchReqLin."Line No." := PurchReqLin2."Line No." + 10000;
                        PurchReqLin.VALIDATE(Type, PurchReqLin.Type::Item);
                        IF ItemRec.GET(ProdBomEx."Item No.") AND (ItemRec.Blocked = FALSE) THEN BEGIN
                            PurchReqLin.VALIDATE("Item Category Code", ItemRec."Item Category Code");
                            PurchReqLin.VALIDATE("No.", ProdBomEx."Item No.");
                            PurchReqLin.VALIDATE("Unit of Measure Code", ProdBomEx."Unit of Measure");
                            PurchReqLin.VALIDATE("Qty. to Order", ProdBomEx."LOT Qty" * ItemRec."SNOP LOT Qty");
                        END;
                        PurchReqLin.INSERT(TRUE);
                        ProdBomEx."PR No." := PurchReqHdr."No.";
                        ProdBomEx.MODIFY;
                    UNTIL ProdBomEx.NEXT = 0;
                MESSAGE('Purchase Requisition %1 Created Succesfully', PurchReqHdr."No.");
            END;
        END;
    END;


    var
        PurchReqHdr: Record "Purch. Req Header";
        PurchReqLin: Record "Purch. Requisition Lines";
        PurchReqLin2: Record "Purch. Requisition Lines";
        Text00001: Label 'Do you want to create PR for selected items?';
        ItemRec: Record item;
        ProdBomEx: Record "Prod. BOM Explosion";
        InventorySet: Record "Inventory Setup";
        YearM: Integer;
        MonthM: Option JAN,FEB,MAR,APR,MAY,JUN,JUL,AUG,SEP,OCT,NOV,DEC;
        DaysM: Integer;
        LocText: Text[1024];
        WFilter: Option ,W1,W2,W3,W4,W5;
}

