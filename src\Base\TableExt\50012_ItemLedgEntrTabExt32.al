tableextension 50012 ItemLedgEntrTabExt extends "Item Ledger Entry"
{
    fields
    {
        field(50003; "Manual MRS No."; Code[30])
        {
            DataClassification = CustomerContent;
        }
        field(50004; "MRS No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Previous Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50101; "In-Transit"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50102; "To Location_SNOP"; code[50])
        {
            DataClassification = CustomerContent;
            TableRelation = Location.Code;
        }/* Not Required
        field(50103; "Exclude SNOP"; Boolean)
        {
            DataClassification = CustomerContent;
        }*/
        field(50010; "Br. Cust. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50011; "Cust. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50015; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "FA No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Res. Gatepass Created"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50018; "Res. Gatepass No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50020; "Production Batch No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //PhaniFeb182021>>
        field(50080; "Description 2"; text[50])
        {
            DataClassification = customercontent;
        }
        //PhaniFeb182021<<
        field(50085; "Clearing File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //Balu 05172021>>
        field(50086; "To Location"; Code[20])
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Lookup("Transfer Header"."Transfer-to Code" WHERE("No." = FIELD("Order No.")));

        }
        field(50087; "Transfer Order No."; Code[20])
        {
            DataClassification = CustomerContent;

        }
        field(50088; "From Location"; Code[20])
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup("Transfer Header"."Transfer-from Code" WHERE("No." = FIELD("Order No.")));
        }
        //Balu 05172021<<
        field(50089; "User ID"; Code[50])
        {
            Editable = false;
            DataClassification = CustomerContent;
        }
        //Fix12Jul2021CWIP>>
        field(50090; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50091; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50092; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        //Fix12Jul2021CWIP<<
        field(50093; "Manufacturing Date"; Date)//>>>> G2S 25/03/25 CAS-01412-X4D4V7
        {
            DataClassification = CustomerContent;
        }

    }

}