pageextension 50113 ItemSub extends "Item Substitution Entry"
{
    layout
    {
        addafter(Condition)
        {
            field("Period Start"; "Period Start")
            {
                ApplicationArea = all;
            }
            field("Period End"; "Period End")
            {
                ApplicationArea = all;
            }
            field("Sub. Item Blocked"; "Sub. Item Blocked")
            {
                ApplicationArea = all;
            }
            field("Last Modified By"; "Last Modified By")
            {
                ApplicationArea = all;
            }
            field("Last Modified Date Time"; "Last Modified Date Time")
            {
                ApplicationArea = all;
            }
        }
    }
}