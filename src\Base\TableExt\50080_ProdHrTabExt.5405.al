tableextension 50080 ProdOrdExt extends "Production Order"
{
    fields
    {
        field(50001; "Prod. Sheet Ref."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50005; "Prod. Sheet Ref. Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Production Batch No."; Code[20])
        {
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                ProdOrderLine.RESET();
                ProdOrderLine.SETRANGE("Prod. Order No.", "No.");
                if ProdOrderLine.FINDSET() then
                    repeat
                        ProdOrderLine."Production Batch No." := "Production Batch No.";
                        ProdOrderLine."External Document No." := "External Document No.";
                        if not ProdOrderLine.MODIFY() then
                            ProdOrderLine.INSERT();
                    until ProdOrderLine.NEXT() = 0;
            end;
        }
        field(50007; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            //PkOnEXT
            trigger OnValidate();
            var
                ProductionOrder: Record "Production Order";
            begin
                ProductionOrder.Reset();
                ProductionOrder.SetRange("External Document No.", "External Document No.");
                if ProductionOrder.FindFirst() then
                    Message('FGTN Already Exits for Production Order No. %1', ProductionOrder."No.");
                ProdOrderLine.RESET();
                ProdOrderLine.SETRANGE("Prod. Order No.", "No.");
                if ProdOrderLine.FINDSET() then
                    repeat
                        ProdOrderLine."Production Batch No." := "Production Batch No.";
                        ProdOrderLine."External Document No." := "External Document No.";
                        if not ProdOrderLine.MODIFY() then
                            ProdOrderLine.INSERT();
                    until ProdOrderLine.NEXT() = 0;
            end;
        }
        //B2BMS
        field(50008; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50009; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50010; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50011; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
        //Project Leap SAA
        field(50012; "Finished Quantity"; Decimal)
        {
            CalcFormula = Sum("Prod. Order Line"."Finished Quantity" WHERE("Prod. Order No." = FIELD("No.")));
            Caption = 'Finished Quantity';
            Editable = false;
            FieldClass = FlowField;
        }

        field(50013; "Remaining Quantity"; Decimal)
        {
            CalcFormula = Sum("Prod. Order Line"."Remaining Quantity" WHERE("Prod. Order No." = FIELD("No.")));
            Caption = 'Remaining Quantity';
            Editable = false;
            FieldClass = FlowField;
        }
        //G2S >>>>> 311023
        field(50014; "Barcode Printed?"; Boolean)
        {
            Editable = false;
        }
        field(50015; "Barcode Details Changed?"; Boolean)
        {
            Editable = false;
        }
        field(50016; "Factory Code"; Code[10])
        {
            //TableRelation = "Work Center Group";
        }
        field(50017; "Line No."; Code[10])
        {
            // TableRelation = "Machine Center";
            //>>>> G2S 14.02.24
            // TableRelation = "Machine Center";
            TableRelation = "Item Production Lines"."Line No." where("Item No." = field("Source No."));
            trigger OnValidate()
            var
                ItemProdLine: Record "Item Production Lines";
            begin
                Rec.TestField("Source No.");
                ItemProdLine.Reset();
                ItemProdLine.SetRange("Item No.", Rec."Source No.");
                ItemProdLine.SetRange("Line No.", Rec."Line No.");
                if ItemProdLine.FindFirst() then
                    "Factory Code" := ItemProdLine."Factory Code";
            end;
            //<<<< G2S 14.02.24
        }
        field(50018; "Shift Code"; Enum ShiftCodeEnum)
        {
            DataClassification = ToBeClassified;
        }
        field(50019; "Refresh Count"; Integer)
        {
            DataClassification = ToBeClassified;
        }

        field(50020; "Error Exists on Refresh?"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        // >>>>>> G2S 04/12/2024  CAS-01377-X2T5S4
        field(50021; "Transfered Quantity"; Decimal)
        {
            CalcFormula = Sum("Prod. Order Line"."Qty Transfered" WHERE("Prod. Order No." = FIELD("No.")));
            Caption = 'Transfered Quantity';
            Editable = false;
            FieldClass = FlowField;
        }
        //G2S <<<<< 311023
        //Project Leap SAA
        modify("Location Code")
        {
            trigger OnAfterValidate()
            var
                Location: Record Location;
                LocationErr: Label 'Location is sub contracting location cannot select in production';
            begin
                /*Location.Get("Location Code");
                if Location."Subcontractor No." <> '' then
                    Error(LocationErr);*/
                if Location.Get("Location Code") then BEGIN
                    Location.TestField(Blocked, false);//Balu 05122021
                    if Location."Subcontractor No." <> '' then
                        Error(LocationErr);
                end;
            end;
        }
    }

    var
        ProdOrderLine: Record "Prod. Order Line";

    trigger OnInsert()
    begin
        "Created By" := UserId;
        "Created Date" := CreateDateTime(WorkDate, Time);
    end;

    trigger OnModify()
    begin
        "Modified By" := UserId;
        "Modified date" := CreateDateTime(WorkDate, Time);
    end;
    //B2BMS

    trigger onDelete()
    var
        ProdOrderLine: Record "Prod. Order Line";
        Text001: Label 'Released order cannot be deleted as output has been posted for the order %1. Please change the status to ''Finished'' instead.';
        ProdOrderComponent: Record "Prod. Order Component";
    begin
        ProdOrderLine.Reset();
        ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
        if ProdOrderLine.FindFirst() then begin
            if ProdOrderLine."Finished Quantity" > 0 then
                Error(Text001, Rec."No.");
            //delete Prod Order Comp
            ProdOrderComponent.Reset();
            ProdOrderComponent.SetRange("Prod. Order No.", Rec."No.");
            ProdOrderComponent.DeleteAll();
        end;
    end;

    procedure CreateTT()
    var
        ProdLineLvar: Record "Prod. Order Line";
        TranHdrLvar: Record "Transfer Header";
        TranLineLvar: Record "Transfer Line";
        Invset: Record "Inventory Setup";
        LinenO: Integer;
        SignFactor: Boolean;
    begin
        Invset.get();
        Invset.TestField("TT InTransit Location");
        TestField("Location Code");

        CheckRemainingQtyonLedgerEntry();     // >>>>>>>G2S>>>>13/06/25 9254_CAS-01418-M5L2V9

        ProdLineLvar.Reset();
        ProdLineLvar.SetRange(Status, Status);
        ProdLineLvar.SetRange("Prod. Order No.", "No.");
        ProdLineLvar.Setfilter("Qty. To Transfer", '>%1', 0);
        IF ProdLineLvar.FindSet() then begin
            ProdLineLvar.TestField("Warehouse Location");
            //project leap >>>>
            CheckSFBOMMaterialsAvalability();
            //project leap <<<<
            TranHdrLvar.INIT();
            TranHdrLvar."Transfer Type" := TranHdrLvar."Transfer Type"::"Transfer Ticket";
            TranHdrLvar.Insert(true);
            TranHdrLvar.Validate("Transfer-from Code", ProdLineLvar."Location Code");
            TranHdrLvar.Validate("Transfer-to Code", ProdLineLvar."Warehouse Location");
            TranHdrLvar.Validate("In-Transit Code", Invset."TT InTransit Location");
            TranHdrLvar.Validate("Posting Date", WorkDate());
            TranHdrLvar."Production Order No." := "No.";
            TranHdrLvar."Production Batch No." := "Production Batch No.";

            TranHdrLvar.Modify();
            LinenO := 10000;
            repeat
                TranLineLvar.INIT();
                TranLineLvar."Document No." := TranHdrLvar."No.";
                TranLineLvar."Line No." := LinenO;
                LinenO := LinenO + 10000;
                TranLineLvar.Insert();
                TranLineLvar.Validate("Item No.", ProdLineLvar."Item No.");
                TranLineLvar.Validate(Quantity, ProdLineLvar."Qty. To Transfer");
                TranLineLvar."Production Order No." := ProdLineLvar."Prod. Order No.";
                TranLineLvar."Production Order Line No." := ProdLineLvar."Line No.";
                IF ProdLineLvar."Warehouse Bin Code" <> '' then
                    TranLineLvar."Transfer-To Bin Code" := ProdLineLvar."Warehouse Bin Code";
                TranLineLvar.Modify();

                // G2S CAS-01293-V3X9Q5 qty transferred >>>>>>>>>>>>>>>>>>>>
                SignFactor := false;
                CreateReservationEntry(TranLineLvar, SignFactor);
                SignFactor := true;
                CreateReservationEntry(TranLineLvar, SignFactor);
                //ProdLineLvar."Qty Transfered" += ProdLineLvar."Qty. To Transfer";
                // G2S CAS-01293-V3X9Q5 qty transferred <<<<<<<<<<<<<<<<<<<<<<<<<
                CLEAR(ProdLineLvar."Qty. To Transfer");
                ProdLineLvar.Modify();
            until ProdLineLvar.next() = 0;
            Message('Transfer Ticket no. %1 is created.', TranHdrLvar."No.");
        end else
            Message('There is nothing to Create');
    end;
    //Project leap 30/03/24
    procedure CheckSFBOMMaterialsAvalability()
    var
        SFQtyBal, PendingReceiptQty, UnprocessedMRSQty, QtytoCheck, Tqtytocheck : Decimal;
        ProdComp: Record "Prod. Order Component";
        TransferLine: Record "Transfer Line";
        MRSLineRec: Record MRSLine;
        ProdOrderLine: record "Prod. Order Line";
        BOMItem, ItemRec : record item;
        BOMItemwithinsufficientqtylist, PendingTransferList, QtyRequiredList : List of [Text];
        BOMItemList, BOMItemsrequiredqtylist, PendingTransfers, QtysRequired : Text;
        BOMItemwithinsufficientqty: record item temporary;
        UOMgt: Codeunit "Unit of Measure Management";
        itemspending, mrspending : Boolean;
        InsufficentErrormsg: Label 'The following items %1 are still pending in transfer orders for you to receive these quantities %2';
        MRSpendingmsg: Label 'The following items %1 are still pending in Production MRS for you to receive these quantities %2';
    begin
        ProdOrderLine.SetRange(Status, Status);
        ProdOrderLine.SetRange("Prod. Order No.", "No.");
        if ProdOrderLine.FindFirst() then begin
            QtytoCheck := 0;
            SFQtyBal := 0;
            itemspending := false;
            QtytoCheck := ProdOrderLine."Qty. To Transfer" + (ProdOrderLine."Qty Transfered" - ProdOrderLine."Posted Consumption Qty.");

            if QtytoCheck > 0 then begin
                ProdComp.SetRange(status, status);
                ProdComp.SetRange("Prod. Order No.", "no.");
                Prodcomp.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");
                if ProdComp.FindSet() then
                    repeat
                        Tqtytocheck := 0;
                        ProdComp.CalcFields("Available Qty");
                        ItemRec.get(ProdComp."Item No.");
                        SFQtyBal := ProdComp."Available Qty";
                        TQtytoCheck := (QtytoCheck * ProdComp."Quantity per") * UOMgt.GetQtyPerUnitOfMeasure(ItemRec, prodcomp."Unit of Measure Code");
                        if Tqtytocheck > SFQtyBal then begin
                            // BOMItemwithinsufficientqtylist.add(ProdComp."Item No.")
                            BOMItemwithinsufficientqty.init;
                            BOMItemwithinsufficientqty."No." := ProdComp."Item No.";
                            BOMItemwithinsufficientqty.Description := ProdComp.Description;
                            BOMItemwithinsufficientqty."Unit Price" := round(Tqtytocheck - SFQtyBal, 1, '>');
                            BOMItemwithinsufficientqty."Base Unit of Measure" := prodcomp."Unit of Measure Code";
                            BOMItemwithinsufficientqty.Insert();
                            itemspending := true;
                        end;
                    until prodcomp.next() = 0;
                //check transfer shipments not received
                if BOMItemwithinsufficientqty.FindSet() then
                    repeat
                        mrspending := false;
                        TransferLine.reset;
                        TransferLine.SetFilter("Production Batch No.", '<>%1', '');
                        TransferLine.SetFilter("Transfer-to Code", "Location Code");
                        TransferLine.SetRange("Item No.", BOMItemwithinsufficientqty."No.");
                        TransferLine.CalcSums("Quantity Shipped", "Quantity Received", Quantity);
                        //if TransferLine.findset then
                        // repeat
                        if TransferLine.Quantity > 0 then begin
                            if (TransferLine."Quantity Shipped" > TransferLine."Quantity Received") or (TransferLine."Quantity Shipped" = 0) then Begin
                                BOMItemwithinsufficientqtylist.Add(BOMItemwithinsufficientqty."No.");
                                BOMItem.get(BOMItemwithinsufficientqty."No.");
                                // bOMItemwithinsufficientqty."Unit Price" := BOMItemwithinsufficientqty."Unit Price" *
                                //UOMgt.GetQtyPerUnitOfMeasure(BOMItem, BOMItemwithinsufficientqty."Base Unit of Measure");
                                QtyRequiredList.Add(format(BOMItemwithinsufficientqty."Unit Price"));
                            end;
                        end else begin                        //  until TransferLine.Next() = 0;
                            MRSLineRec.Setfilter("Production Batch No.", '<>%1', '');
                            MRSLineRec.SetRange("Line CLosed", false);
                            mrslinerec.SetRange("Allocated From Location", 0);
                            MRSLineRec.setrange("No.", BOMItemwithinsufficientqty."No.");
                            if MRSLineRec.findset then begin
                                BOMItemwithinsufficientqtylist.Add(BOMItemwithinsufficientqty."No.");
                                // BOMItem.get(BOMItemwithinsufficientqty."No.");
                                // bOMItemwithinsufficientqty."Unit Price" := BOMItemwithinsufficientqty."Unit Price" *
                                //UOMgt.GetQtyPerUnitOfMeasure(BOMItem, BOMItemwithinsufficientqty."Base Unit of Measure");
                                QtyRequiredList.Add(format(BOMItemwithinsufficientqty."Unit Price"));
                                mrspending := true;
                            end;
                        end;


                    until BOMItemwithinsufficientqty.Next() = 0;

                foreach bomitemlist in bomitemwithinsufficientqtylist do begin
                    BOMItemsrequiredqtylist += BOMItemList + '|';
                end;
                BOMItemList := '';
                foreach bomitemlist in qtyrequiredlist do begin
                    QtysRequired += BOMItemList + '|';
                end;
                BOMItemList := '';
                foreach bomitemlist in PendingTransferList do begin
                    PendingTransfers += BOMItemList + '|';
                end;
                if itemspending and not mrspending then
                    Error(InsufficentErrormsg, BOMItemsrequiredqtylist, QtysRequired);
                if mrspending then
                    Error(MRSPendingmsg, BOMItemsrequiredqtylist, QtysRequired);
            end;
        end;

    end;
    // 30/03/24 

    // G2S CAS-01292-S2W5C5 Auto Lotting >>>>>>>>>>>>>>>>>>>>
    local procedure CreateReservationEntry(TransferLine: Record "Transfer Line"; Positive: Boolean)
    var
        TempReservEntry: Record "Reservation Entry" temporary;
        TransLine: Record "Transfer Line";
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ItemTrackingMgt: Codeunit "Item Tracking Management";
        ReservStatus: Enum "Reservation Status";
        CurrentSourceRowID: Text[250];
        SecondSourceRowID: Text[250];
        ReservationEntry: Record "Reservation Entry";
        ItemLedgEntry: Record "Item Ledger Entry";
        ItemTrackingManagement: Codeunit "Item Tracking Management";
        TrackingSpec: Record "Tracking Specification";
        LotNo: Code[20];
        entryNo: Integer;
    begin
        ReservationEntry.Init();
        ReservationEntry.Positive := Positive;
        ReservationEntry."Item No." := TransferLine."Item No.";
        ReservationEntry."Reservation Status" := "Reservation Status"::Surplus;
        ReservationEntry."Source Type" := 5741;
        ReservationEntry."Source ID" := TransferLine."Document No.";
        ReservationEntry."Source Ref. No." := TransferLine."Line No.";
        ReservationEntry."Lot No." := "Production Batch No.";
        ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";
        ReservationEntry."Qty. per Unit of Measure" := TransferLine."Qty. per Unit of Measure";
        if Positive then begin
            ReservationEntry."Location Code" := TransferLine."Transfer-to Code";
            ReservationEntry."Source Subtype" := 1;
            ReservationEntry."Expected Receipt Date" := TransferLine."Receipt Date";
            ReservationEntry.Quantity := TransferLine.Quantity;
            ReservationEntry."Quantity (Base)" := TransferLine."Quantity (Base)";
            ReservationEntry."Qty. to Handle (Base)" := TransferLine."Quantity (Base)";
            ReservationEntry."Qty. to Invoice (Base)" := TransferLine."Quantity (Base)";
            if ItemTrackingManagement.GetLotSNDataSet(ReservationEntry."Item No.", TransferLine."Variant Code",
                "Production Batch No.", '', ItemLedgEntry) then
                ReservationEntry."Expiration Date" := ItemLedgEntry."Expiration Date";
        end else begin
            ReservationEntry."Location Code" := TransferLine."Transfer-from Code";
            ReservationEntry."Source Subtype" := 0;
            ReservationEntry."Shipment Date" := TransferLine."Shipment Date";
            ReservationEntry.Quantity := -TransferLine.Quantity;
            ReservationEntry."Quantity (Base)" := -TransferLine."Quantity (Base)";
            ReservationEntry."Qty. to Handle (Base)" := -TransferLine."Quantity (Base)";
            ReservationEntry."Qty. to Invoice (Base)" := -TransferLine."Quantity (Base)";
        end;
        // LotNo := ReservationEntry."Lot No.";
        ReservationEntry.Insert(true);
    end;

    // G2S CAS-01292-S2W5C5 Auto Lotting <<<<<<<<<<<<<<<<<<<<<<<<<

    // >>>>>>>G2S>>>>22/05/25 9254_CAS-01418-M5L2V9
    procedure ItemLedgerExist(): Boolean
    var
        ProdOrderLine: Record "Prod. Order Line";
        ItemLedgEntry: Record "Item Ledger Entry";
        KeyLotFieldsEdit: Boolean;
    begin
        KeyLotFieldsEdit := false;
        ProdOrderLine.Reset();
        ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
        IF ProdOrderLine.FindSet() THEN
            Repeat
                ItemLedgEntry.Reset();
                ItemLedgEntry.SetRange("Document No.", ProdOrderLine."Prod. Order No.");
                IF NOT ItemLedgEntry.IsEmpty then
                    KeyLotFieldsEdit := true;
            Until ProdOrderLine.Next() = 0;

        if KeyLotFieldsEdit then
            exit(true) else begin
            exit(false);
        end;
    end;

    procedure CheckRemainingQtyonLedgerEntry()
    var
        ProdOrderLine: Record "Prod. Order Line";
        ItemLedgEntry: Record "Item Ledger Entry";
        RemainingQty: Decimal;
    begin
        ProdOrderLine.Reset();
        ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
        IF ProdOrderLine.FindSet() THEN
            Repeat
                RemainingQty := 0;
                ItemLedgEntry.Reset();
                ItemLedgEntry.SetCurrentKey("Item No.", "Document No.", "Lot No.");
                ItemLedgEntry.SetRange("Document No.", ProdOrderLine."Prod. Order No.");
                ItemLedgEntry.SetRange("Lot No.", rec."Production Batch No.");
                ItemLedgEntry.SetRange("Item No.", ProdOrderLine."Item No.");
                IF ItemLedgEntry.FindSet() then
                    Repeat
                        RemainingQty += ItemLedgEntry."Remaining Quantity";
                    until ItemLedgEntry.Next() = 0;

                IF ProdOrderLine."Qty. To Transfer" > RemainingQty then
                    Error('Qty. To Transfer %1 is greater then Remaining Qty %2 on Itemledger Entry', ProdOrderLine."Qty. To Transfer", RemainingQty);
            Until ProdOrderLine.Next() = 0;

    end;
    // >>>>>>>G2S>>>>22/05/25 9254_CAS-01418-M5L2V9
}
