page 50191 "Container Details"
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Container Details";
    AutoSplitKey = true;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Loading Slip No."; "Loading Slip No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                    Visible = FALSE;
                }
                field("Loading SLip Line No."; "Loading SLip Line No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                    Visible = FALSE;
                }
                field("Entry type"; "Entry type")
                {
                    ApplicationArea = ALL;
                    Visible = FALSE;
                    Editable = false;
                }
                field("Container Size"; "Container Size")
                {
                    ApplicationArea = ALL;
                }
                field("Tonnage Capacity"; "Tonnage Capacity")
                {
                    ApplicationArea = All;
                }
                field("Ware house Shipment No."; "Ware house Shipment No.")
                {
                    ApplicationArea = All;
                    Visible = FALSE;
                }
                field("Ware house Shipment Line No."; "Ware house Shipment Line No.")
                {
                    ApplicationArea = All;
                    Visible = FALSE;
                }
                field("Source No."; "Source No.")
                {
                    ApplicationArea = All;
                    Editable = FALSE;
                }
                field("Source Line No."; "Source Line No.")
                {
                    ApplicationArea = All;
                    Editable = FALSE;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;
                }
                field("Container No."; "Container No.")
                {
                    ApplicationArea = All;
                }
                field("No. of Packages"; "No. of Packages")
                {
                    ApplicationArea = All;
                }
                field("Kind of Package"; "Kind of Package")
                {
                    ApplicationArea = All;
                }
                field("Serial No."; "Serial No.")
                {
                    ApplicationArea = All;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                }
            }
        }
    }
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    var
        myInt: Integer;
        WarShpmntLne: Record "Warehouse Shipment Line";
        SalHd: Record "Sales Header";
    begin
        WarShpmntLne.reset;
        WarShpmntLne.SetRange("No.", "Ware house Shipment No.");
        WarShpmntLne.SetRange("Line No.", "Ware house Shipment Line No.");
        WarShpmntLne.SetRange("Source Document", WarShpmntLne."Source Document"::"Sales Order");
        IF WarShpmntLne.findfirst then begin
            IF SalHd.GET(SalHd."Document Type"::Order, WarShpmntLne."Source No.") then
                SalHd.TestField("Sales Type", SalHd."Sales Type"::Export);
            "Source No." := WarShpmntLne."Source No.";
            "Source Line No." := WarShpmntLne."Source Line No.";
        end;

    end;
}
