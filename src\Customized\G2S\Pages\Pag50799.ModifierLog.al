page 50799 "Modifier Log"
{
    ApplicationArea = All;
    Caption = 'Modifier Log';
    PageType = List;
    SourceTable = "Purchase Mod log";
    UsageCategory = Lists;
    Permissions = tabledata "Purchase Mod log" = rimd;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Modifier No."; Rec."Modifier No.")
                {
                    ApplicationArea = All;
                    Caption = 'Modifier No.';
                }
                field("Document No."; Rec."Document No.")
                {
                    ApplicationArea = All;
                    Caption = 'Document No.';
                }
                field("Document Status"; Rec."Document Status")
                {
                    ApplicationArea = All;
                    Caption = 'Document Status';
                }
                field("Created Time"; Rec."Created Time")
                {
                    ApplicationArea = All;
                    Caption = 'Created Time';
                }
                field(Adjusted; Rec.Adjusted)
                {
                    ApplicationArea = All;
                    Caption = 'Adjusted';
                }
                field("Adjusted Time"; Rec."Adjusted Time")
                {
                    ApplicationArea = All;
                    Caption = 'Adjusted Time';
                }
                field("Line No"; Rec."Line No")
                {
                    ApplicationArea = All;
                    Caption = 'Line No';
                }
            }
        }
    }
}
