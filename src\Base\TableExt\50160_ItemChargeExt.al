tableextension 50160 ItemChargeExt extends "Item Charge"
{
    fields
    {
        // Add changes to table fields here
        field(50000; "Inventory Posting Setup"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Inventory Posting Group";
            Description = 'PROV1.0';
        }
        field(50001; "Process Document Type"; Option)
        {
            OptionMembers = " ",Duty,"Importation Cost Related Bill","IPO Facility",Facility,Clearing,Demurage,Insurance,NAFDAC,Freight;
        }
    }

    var
        myInt: Integer;
}