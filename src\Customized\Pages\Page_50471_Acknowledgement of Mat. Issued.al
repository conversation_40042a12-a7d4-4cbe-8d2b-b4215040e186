page 50471 "Acknowledgement of <PERSON>. Issued"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Ltd
    // HO      :  Henry <PERSON>
    // SAA     :  Saheed A. Adeosun
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL       29-Dec-11    -> Form created for Material Requisitions.
    // 
    // 1.0      HO        22-Feb-12    -> Code added to "OnOpenForm()"
    // 3.0      SAA       13-Mar-13    -> Removed "MRS Date" to only show 1 date "Document Date".

    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Document;
    SourceTable = MRSHeader;
    /*SourceTableView = SORTING("MRS No.")
                      ORDER(Ascending)
                      WHERE(Status=FILTER(Released|Posted),
                            Sample=FILTER(false));
                            UsageCategory=Documents;*/
    SourceTableView = SORTING("MRS No.")
                      ORDER(Ascending)
                      WHERE(Status =FILTER(Released));
                            UsageCategory=Documents;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MRS No.";"MRS No.")
                {
                    Editable = false;
                }
                field("Manual MRS No.";"Manual MRS No.")
                {
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code";"Shortcut Dimension 1 Code")
                {
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code";"Shortcut Dimension 2 Code")
                {
                    Editable = false;
                }
                field(Comment;Comment)
                {
                    Editable = false;
                }
                field("Purch. Req. Ref. No.";"Purch. Req. Ref. No.")
                {
                    Editable = false;
                }
                field(Status;Status)
                {
                    Editable = false;
                }
                field("Document Date";"Document Date")
                {
                    Editable = false;
                }
                field("Issued Date";"Issued Date")
                {
                    Editable = false;
                }
                field("Expected Delivery Date";"Expected Delivery Date")
                {
                    Editable = false;
                }
            }
            part(PostedMatReqLineSubform;"Posted Mat. Req. Lines Subform")
            {
                SubPageLink = "Document No."=FIELD("MRS No.");
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Requisition")
            {
                Caption = '&Requisition';
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1102152037)
                {
                }
                action("&Approvals")
                {
                    Caption = '&Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries : Page "Posted Approval Entries";
                    begin
                        /*
                        ApprovalEntries.Setfilters(DATABASE::"MRS Header",21,"MRS No.");
                        ApprovalEntries.RUN;*///CHI WF
                    end;
                }
            }
        }
    }

    trigger OnOpenPage();
    begin
        /*
        // HO1.0 <<
        if UserMg.GetMRSFilter <> '' then begin
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMg.GetMRSFilter);
          FILTERGROUP(0);
        end;
        // HO1.0 >>*/
    end;

    var
        ApprovalEntries : Page 658;
        MRSLineRec : Record MRSLine;
        Text50200 : Label 'Purchase Requisition not required when Stock available is greater than MRS Quantity.';
        Text50201 : Label 'Qty to Issue must not exceed %1.';
        UserMgt : Codeunit "User Setup Management";
        UserMg:Codeunit "User Setup Management Ext";
}

