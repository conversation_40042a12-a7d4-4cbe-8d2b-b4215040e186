page 50157 "Posted Original Teller Receipt"
{

    Caption = 'Bank Confirmed Tellers Register - Posted';
    DeleteAllowed = false;
    //Editable = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Confirmed Teller Receipt";
    SourceTableView = WHERE("Original Teller Posted" = FILTER(true));
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posted By"; "Posted By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posted Date"; "Posted Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Issued"; "Bank Issued")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cheque No."; "Cheque No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Cheque Date"; "Cheque Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Chq Value Date"; "Chq Value Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Created BRV No."; "Created BRV No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Original Teller Recpt."; "Original Teller Recpt.")
                {
                    ApplicationArea = all;
                }
                field("Original Teller Recvd. by"; "Original Teller Recvd. by")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Original Teller Posted"; "Original Teller Posted")
                {
                    ApplicationArea = all;
                }
                field("Original Teller Recpt. Date"; "Original Teller Recpt. Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Original Teller Recpt. Time"; "Original Teller Recpt. Time")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Original Teller Posted by"; "Original Teller Posted by")
                {
                    ApplicationArea = all;
                }
                field("Original Teller Posted Date"; "Original Teller Posted Date")
                {
                    ApplicationArea = all;
                }
                field("Original Teller Posted Time"; "Original Teller Posted Time")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnOpenPage();
    begin
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);
    end;

    var
        BankConfirmTellersRec: Record "Confirmed Teller Receipt";
        BankTellerConfirmationRec: Record "Request Teller Receipt";
        Linecount: Integer;
        Window: Dialog;
        OldBankTellerConfirmationRec: Record "Request Teller Receipt";
        Text50200: Label 'Reverse Confirmed Bank Teller must not be false';
        Text50201: Label 'You do not have permission to Reverse Confirmed Bank Tellers';
        Text50202: Label 'Posting lines         #2######';
        UserSetup: Record "User Setup";
        Text50203: Label 'You do not have permission For confirmed Teller/Cheque.';
        UserMgt: Codeunit "User Setup Management";
        RespCentFilter: Codeunit "Responsibility Center Filter";
        BuildFilter: Text[250];
}

