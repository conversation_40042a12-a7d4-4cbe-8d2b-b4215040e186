tableextension 50155 BankAccRecExt extends "Bank Acc. Reconciliation"
{
    fields
    {
        field(50000; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        //PhaniFeb182021>> added only extra filters
        field(50001; "Out. Debit Balance Navision"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = sum("Bank Account Ledger Entry"."Debit Amount" WHERE("Bank Account No." = field("Bank Account No."), Open = const(true), "Debit Amount" = filter(<> 0)));PK on 24.02
            CalcFormula = Sum("BRS Outstanding Data"."Debit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Navision)));
        }

        field(50002; "Out. Credit Balance Navision"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = - SUM("Bank Account Ledger Entry"."Credit Amount" WHERE("Bank Account No." = field("Bank Account No."), Open = const(true), "Credit Amount" = filter(<> 0)));PK on 24.02
            CalcFormula = - Sum("BRS Outstanding Data"."Credit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Navision)));
        }
        //PhaniFeb182021<<
        field(50003; "Out. Debit Balance Statement"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum("Original Bank Statement"."Bank Debit Amount" WHERE(Code = FIELD("Bank Account No."), "Statement No." = FIELD("Statement No.")));
            //CalcFormula = SUM("Bank Account Statement"."Statement Ending Balance" WHERE("Bank Account No." = field("Bank Account No."), "Statement Ending Balance" = filter(< 0)));PK on 24.02
            //CalcFormula = Sum("BRS Outstanding Data"."Debit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Statement)));

        }
        field(50004; "Out. Credit Balance Statement"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum("Original Bank Statement"."Bank Credit Amount" WHERE(Code = FIELD("Bank Account No."), "Statement No." = FIELD("Statement No.")));
            //CalcFormula = SUM("Bank Account Statement"."Statement Ending Balance" WHERE("Bank Account No." = field("Bank Account No."), "Statement Ending Balance" = filter(> 0)));PK on 24.02
            //CalcFormula = Sum("BRS Outstanding Data"."Credit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Statement)));

        }
        //PhaniFeb182021>> //added extra filters
        field(50005; "Out. Debit Balance Nav (LCY)"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = SUM("Bank Account Ledger Entry"."Debit Amount (LCY)" WHERE("Bank Account No." = field("Bank Account No."), Open = const(true), "Debit Amount (LCY)" = filter(<> 0)));PK on 24.02
            CalcFormula = Sum("BRS Outstanding Data"."Debit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Statement)));
        }

        field(50006; "Out. Credit Balance Nav (LCY)"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = - SUM("Bank Account Ledger Entry"."Credit Amount (LCY)" WHERE("Bank Account No." = field("Bank Account No."), Open = const(true), "Credit Amount (LCY)" = filter(<> 0)));
        }
        //PhaniFeb182021<<
        field(50007; "Closing Balance"; decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50008; "Opening Balance"; decimal)
        {
            DataClassification = CustomerContent;
            Editable = TRUE;
        }
        field(50009; "Opening Balance BANK"; Decimal)
        {
            DataClassification = CustomerContent;

        }
        field(50010; "Closing Balance BANK"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Outstanding Entry Downloaded"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Statement Debit"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = Sum("Original Bank Statement"."Bank Debit Amount" WHERE(Code = FIELD("Bank Account No."), "Statement No." = FIELD("Statement No.")));
            //CalcFormula = SUM("Bank Account Statement"."Statement Ending Balance" WHERE("Bank Account No." = field("Bank Account No."), "Statement Ending Balance" = filter(< 0)));PK on 24.02
            CalcFormula = Sum("BRS Outstanding Data"."Debit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Statement)));

        }
        field(50016; "Statement Credit"; decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = Sum("Original Bank Statement"."Bank Credit Amount" WHERE(Code = FIELD("Bank Account No."), "Statement No." = FIELD("Statement No.")));
            //CalcFormula = SUM("Bank Account Statement"."Statement Ending Balance" WHERE("Bank Account No." = field("Bank Account No."), "Statement Ending Balance" = filter(> 0)));PK on 24.02
            CalcFormula = Sum("BRS Outstanding Data"."Credit Amount" WHERE("Bank No" = FIELD("Bank Account No."), Reconciled = FILTER(False), Type = CONST(Statement)));

        }
       

    }

}