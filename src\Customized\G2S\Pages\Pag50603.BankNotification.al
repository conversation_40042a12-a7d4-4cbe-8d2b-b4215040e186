/// G2S Providus Integration 7th Aug 2024 
page 50603 "Bank Notifications"
{
    ApplicationArea = All;
    Caption = 'Bank Notifications';
    PageType = List;
    SourceTable = "Bank Notifications";
    UsageCategory = History;
    Editable = false;
    ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("voucher No."; "voucher No.")
                {
                    ApplicationArea = basic, suite;
                }
                // field("Bank Code"; "Bank Code")
                // {
                //     ApplicationArea = basic, suite;
                // }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = basic, suite;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = basic, suite;
                }
                field("Date Time"; "Date Time")
                {
                    ApplicationArea = basic, suite;
                }
                field("Response Code"; "Response Code")
                {
                    ApplicationArea = basic, suite;
                }
                field("Response Desc."; "Response Desc.")
                {
                    ApplicationArea = basic, suite;
                }
                field("Payment Status"; "Payment Status")
                {
                    ApplicationArea = basic, suite;
                }
                field("Txn Ref"; "Txn Ref")
                {
                    ApplicationArea = basic, suite;
                }
                field("Beneficiary Bank Code"; "Beneficiary Bank Code")
                {
                    ApplicationArea = basic, suite;
                }
                field("Beneficiary Bank Name"; "Beneficiary Bank Name")
                {
                    ApplicationArea = basic, suite;
                }
                field("Beneficiary Acct. No."; "Beneficiary Acct. No.")
                {
                    ApplicationArea = basic, suite;
                }
            }
        }
    }
}
///Providus Integration 7th Aug 2024 
