Page 50460 "Clearing List"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO       15-Feb-12    -> Form created for IPO Clearing.

    Caption = 'Clearing List';
    CardPageID = Clearing;
    Editable = false;
    PageType = List;
    SourceTable = "Clearing Header";
    SourceTableView = SORTING("No.")
                      ORDER(Ascending);
    UsageCategory = Lists;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("No."; "No.")
                {
                }
                field("Import File No."; "Import File No.")
                {
                }
                field("Operational File No."; "Operational File No.")
                {
                }
                field("Scanned Shipping Doc. Date"; "Scanned Shipping Doc. Date")
                {
                }
                field("BL No."; "BL No.")
                {
                }
                field("BL Date"; "BL Date")
                {
                }
                field("Port of Loading"; "Port of Loading")
                {
                }
                field("Port of Discharge"; "Port of Discharge")
                {
                }
                field("Vessel/Voyage"; "Vessel/Voyage")
                {
                }
                field("Approval Status"; "Approval Status")
                {
                }
                field("Actual Sailng Date"; "Actual Sailng Date")
                {
                }
                field("Expected Date of Arrival"; "Expected Date of Arrival")
                {
                }
                field("Clearing Agent"; "Clearing Agent")
                {
                }
                field("Duty Certificate"; "Duty Certificate")
                {
                }
                field("Assessment No."; "Assessment No.")
                {
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action(Card)
                {
                    Caption = 'Card';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        /*if Status <> Status :: Posted then
                          PAGE.RUN(PAGE::Clearing,Rec)
                        else
                          PAGE.RUN(PAGE::"Posted Clearing",Rec)*/
                    end;
                }
            }
        }
    }
}

