///Providus Integration 7th Aug 2024 
page 50604 "Bank Payment Code"
{
    ApplicationArea = All;
    Caption = 'Bank Payment Code';
    PageType = List;
    SourceTable = "Bank Payment Code";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Bank couting Code"; Rec."Bank Routing Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Name field.', Comment = '%';
                }
                field("Bank Name"; Rec."Bank Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Name field.', Comment = '%';
                }
                field("Providus Bank Code"; Rec."Providus Bank Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Code field.', Comment = '%';
                }
                field("First Bank Code"; Rec."First Bank Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Code field.', Comment = '%';
                }
                field("Zenith Bank Code"; "Zenith Bank Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Code field.', Comment = '%';
                }
            }
        }
    }
}
///Providus Integration 7th Aug 2024 
