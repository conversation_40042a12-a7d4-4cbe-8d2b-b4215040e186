codeunit 50007 POAutomationWorkFlow
{
    trigger OnRun()
    begin

    end;

    //Material Req start

    [IntegrationEvent(false, false)]
    Procedure OnSendMaterialRequestForApproval(var MRSHeader: Record MRSHeader)
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelMaterialRequestForApproval(var MRSHeader: Record MRSHeader)
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendMRSHeaderforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnMRSHeaderforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::POAutomationWorkFlow, 'OnSendMaterialrequestForApproval', '', false, false)]
    local procedure RunworkflowonsendMaterialRequestForApproval(var MRSHeader: Record MRSHeader)
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendMRSHeaderforApprovalCode(), MRSHeader);
    end;

    procedure RunworkflowOnCancelMaterialRequestforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelMaterialRequestForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::POAutomationWorkFlow, 'OncancelMaterialRequestForApproval', '', false, false)]

    local procedure RunworkflowonCancelMaterialRequestForApproval(var MRSHeader: Record MRSHeader)
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelmaterialRequestforApprovalCode(), MRSHeader);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryMR();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendMRSHeaderforApprovalCode(), DATABASE::MRSHeader,
          CopyStr(MRsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelMaterialRequestforApprovalCode(), DATABASE::MRSHeader,
          CopyStr(MRrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryMR(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelMaterialrequestforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelMaterialrequestforApprovalCode(), RunworkflowOnSendMRSHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendMRSHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendMRSHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendMRSHeaderforApprovalCode());
        end;
    end;

    procedure ISmaterialrequestworkflowenabled(var MRSHeader: Record MRSHeader): Boolean
    begin
        if MRSHeader.Status <> MRSHeader.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(MRSHeader, RunworkflowOnSendMRSHeaderforApprovalCode()));
    end;

    Procedure CheckMaterialRequestApprovalsWorkflowEnabled(VAR MRSHeader: Record MRSHeader): Boolean
    begin
        IF not ISMaterialrequestworkflowenabled(MRSHeader) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentMR(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        MRSHeader: Record MRSHeader;
    begin
        case RecRef.Number() of
            Database::MRSHeader:
                begin
                    RecRef.SetTable(MRSHeader);
                    ApprovalEntryArgument."Document No." := FORMAT(MRSHeader."MRS No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentMR(RecRef: RecordRef; var Handled: boolean)
    var
        MRSHeader: Record MRSHeader;
    begin
        case RecRef.Number() of
            Database::MRSHeader:
                begin
                    RecRef.SetTable(MRSHeader);
                    MRSHeader.Status := MRSHeader.Status::Open;
                    MRSHeader.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentMR(RecRef: RecordRef; var Handled: boolean)
    var
        MRSHeader: Record MRSHeader;
    begin
        case RecRef.Number() of
            Database::MRSHeader:
                begin
                    RecRef.SetTable(MRSHeader);
                    MRSHeader.VALIDATE(Status, MRSHeader.Status::Released);
                    MRSHeader.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalMR(RecRef: RecordRef; var IsHandled: boolean)
    var
        MRSHeader: Record MRSHeader;
    begin
        case RecRef.Number() of
            Database::MRSHeader:
                begin
                    RecRef.SetTable(MRSHeader);
                    MRSHeader.Status := MRSHeader.Status::"Pending for Approval";
                    MRSHeader.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryMR(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendMRSHeaderforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendMRSheaderforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelMaterialRequestforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelMaterialRequestforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryMR()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(MRCategoryTxt, 1, 20), CopyStr(MRCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsMR()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::MRSHeader, 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateMR()
    begin
        InsertMRApprovalworkflowtemplate();
    end;

    local procedure InsertMRApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(MRIncDocOCRWorkflowCodeTxt, 1, 17), CopyStr(MRApprWorkflowDescTxt, 1, 100), CopyStr(MRCategoryTxt, 1, 20));
        InsertMRApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertMRApprovalworkflowDetails(var workflow: record Workflow);
    var
        MRSHeader: Record MRSHeader;
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildMRtypecondition(MRSHeader.Status::open), RunworkflowOnSendMRSHeaderforApprovalCode(), BuildMRtypecondition(MRSHeader.Status::"Pending for Approval"), RunworkflowOnCancelMaterialRequestforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildMRtypecondition(status: integer): Text
    var
        MRSHeader: Record MRSHeader;
    Begin
        MRSHeader.SetRange(status, status);
        exit(StrSubstNo(MRTypeCondnTxt, workflowsetup.Encode(MRSHeader.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidMR(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidMR(RecordRef)
    end;

    local procedure GetConditionalcardPageidMR(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::MRSHeader:
                exit(page::"Material Requisitions.");
        end;
    end;

    //Material Req End


    //Purch Req start

    [IntegrationEvent(false, false)]
    Procedure OnSendPurchaseRequestForApproval(var PRHeader: Record "Purch. Req Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPurchaseRequestForApproval(var PRHeader: Record "Purch. Req Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendPRSHeaderforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnPRSHeaderforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::POAutomationWorkFlow, 'OnSendPurchaserequestForApproval', '', false, false)]
    local procedure RunworkflowonsendPurchaseRequestForApproval(var PRHeader: Record "Purch. Req Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPRSHeaderforApprovalCode(), PRHeader);
    end;

    procedure RunworkflowOnCancelPurchaseRequestforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPurchaseRequestForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::POAutomationWorkFlow, 'OncancelPurchaseRequestForApproval', '', false, false)]

    local procedure RunworkflowonCancelPurchaseRequestForApproval(var PRHeader: Record "Purch. Req Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPurchaseRequestforApprovalCode(), PRHeader);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPR();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPRSHeaderforApprovalCode(), DATABASE::"Purch. Req Header",
          CopyStr(PRsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPurchaseRequestforApprovalCode(), DATABASE::"Purch. Req Header",
          CopyStr(PRrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPR(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPurchaserequestforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPurchaserequestforApprovalCode(), RunworkflowOnSendPRSHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPRSHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPRSHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPRSHeaderforApprovalCode());
        end;
    end;

    procedure ISPurchaserequestworkflowenabled(var PRHeader: Record "Purch. Req Header"): Boolean
    begin
        if PRHeader.Status <> PRHeader.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(PRHeader, RunworkflowOnSendPRSHeaderforApprovalCode()));
    end;

    Procedure CheckPurchaseRequestApprovalsWorkflowEnabled(var PRHeader: Record "Purch. Req Header"): Boolean
    begin
        IF not ISPurchaserequestworkflowenabled(PRHeader) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPR(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PRSHeader: Record "Purch. Req Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Req Header":
                begin
                    RecRef.SetTable(PRSHeader);
                    ApprovalEntryArgument."Document No." := FORMAT(PRSHeader."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPR(RecRef: RecordRef; var Handled: boolean)
    var
        PRSHeader: Record "Purch. Req Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Req Header":
                begin
                    RecRef.SetTable(PRSHeader);
                    PRSHeader.Status := PRSHeader.Status::Open;
                    PRSHeader.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPR(RecRef: RecordRef; var Handled: boolean)
    var
        PRSHeader: Record "Purch. Req Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Req Header":
                begin
                    RecRef.SetTable(PRSHeader);
                    PRSHeader.Status := PRSHeader.Status::Released;
                    PRSHeader.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalPR(RecRef: RecordRef; var IsHandled: boolean)
    var
        PRSHeader: Record "Purch. Req Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Req Header":
                begin
                    RecRef.SetTable(PRSHeader);
                    PRSHeader.Status := PRSHeader.Status::"Pending for Approval";
                    PRSHeader.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPR(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPRSHeaderforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPRSheaderforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPurchaseRequestforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPurchaseRequestforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPR()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PRCategoryTxt, 1, 20), CopyStr(PRCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPR()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Purch. Req Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePR()
    begin
        InsertPRApprovalworkflowtemplate();
    end;

    local procedure InsertPRApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(PRIncDocOCRWorkflowCodeTxt, 1, 17), CopyStr(PRApprWorkflowDescTxt, 1, 100), CopyStr(PRCategoryTxt, 1, 20));
        InsertPRApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPRApprovalworkflowDetails(var workflow: record Workflow);
    var
        PRSHeader: Record "Purch. Req Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPRtypecondition(PRSHeader.Status::open), RunworkflowOnSendPRSHeaderforApprovalCode(), BuildPRtypecondition(PRSHeader.Status::"Pending for Approval"), RunworkflowOnCancelPurchaseRequestforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPRtypecondition(status: integer): Text
    var
        PRSHeader: Record "Purch. Req Header";
    Begin
        PRSHeader.SetRange(status, status);
        exit(StrSubstNo(PRTypeCondnTxt, workflowsetup.Encode(PRSHeader.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPR(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidPR(RecordRef)
    end;

    local procedure GetConditionalcardPageidPR(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Purch. Req Header":
                exit(page::"Purchase Requisitions-Released");
        end;
    end;

    //Purch Req End

    //qUOTATIONcOMPARISION sTART

    [IntegrationEvent(false, false)]
    Procedure OnSendQuotationComparisionForApproval(var QCompHeader: Record QuotCompHdr)
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelQuotationComparisionForApproval(var QCompHeader: Record QuotCompHdr)
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendQuotationComparisionforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnQuotationComparisionforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::POAutomationWorkFlow, 'OnSendQuotationComparisionForApproval', '', false, false)]
    local procedure RunworkflowonsendQuotationComparisionForApproval(var QCompHeader: Record QuotCompHdr)
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendQuotationComparisionforApprovalCode(), QCompHeader);
    end;

    procedure RunworkflowOnCancelQuotationComparisionforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelQuotationComparisionForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::POAutomationWorkFlow, 'OncancelQuotationComparisionForApproval', '', false, false)]

    local procedure RunworkflowonCancelQuotationComparisionForApproval(var QCompHeader: Record QuotCompHdr)
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelQuotationComparisionforApprovalCode(), QCompHeader);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryQuotationComparision();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendQuotationComparisionforApprovalCode(), DATABASE::QuotCompHdr,
          CopyStr(QuotationComparisionsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelQuotationComparisionforApprovalCode(), DATABASE::QuotCompHdr,
          CopyStr(QuotationComparisionrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryQuotationComparision(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelQuotationComparisionforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelQuotationComparisionforApprovalCode(), RunworkflowOnSendQuotationComparisionforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendQuotationComparisionforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendQuotationComparisionforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendQuotationComparisionforApprovalCode());
        end;
    end;

    procedure ISQuotationComparisionworkflowenabled(var QCompHeader: Record QuotCompHdr): Boolean
    begin
        if QCompHeader.Status <> QCompHeader.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(QCompHeader, RunworkflowOnSendQuotationComparisionforApprovalCode()));
    end;

    Procedure CheckQuotationComparisionApprovalsWorkflowEnabled(var QCompHeader: Record QuotCompHdr): Boolean
    begin
        IF not ISQuotationComparisionworkflowenabled(QCompHeader) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentQuotationComparision(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        QCompHeader: Record QuotCompHdr;
    begin
        case RecRef.Number() of
            Database::QuotCompHdr:
                begin
                    RecRef.SetTable(QCompHeader);
                    ApprovalEntryArgument."Document No." := FORMAT(QCompHeader."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentQuotationComparision(RecRef: RecordRef; var Handled: boolean)
    var
        QCompHeader: Record QuotCompHdr;
    begin
        case RecRef.Number() of
            Database::QuotCompHdr:
                begin
                    RecRef.SetTable(QCompHeader);
                    QCompHeader.Status := QCompHeader.Status::open;
                    QCompHeader.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentQuotationComparision(RecRef: RecordRef; var Handled: boolean)
    var
        QCompHeader: Record QuotCompHdr;
    begin
        case RecRef.Number() of
            Database::QuotCompHdr:
                begin
                    RecRef.SetTable(QCompHeader);
                    QCompHeader.Status := QCompHeader.Status::Released;
                    QCompHeader.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalQuotationComparision(RecRef: RecordRef; var IsHandled: boolean)
    var
        QCompHeader: Record QuotCompHdr;
    begin
        case RecRef.Number() of
            Database::QuotCompHdr:
                begin
                    RecRef.SetTable(QCompHeader);
                    QCompHeader.Status := QCompHeader.Status::"Pending for Approval";
                    QCompHeader.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryQuotationComparision(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendQuotationComparisionforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendQuotationComparisionforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelQuotationComparisionforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelQuotationComparisionforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryQuotationComparision()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(QuotationComparisionCategoryTxt, 1, 20), CopyStr(QuotationComparisionCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsQuotationComparision()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::QuotCompHdr, 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateQuotationComparision()
    begin
        InsertQuotationComparisionApprovalworkflowtemplate();
    end;

    local procedure InsertQuotationComparisionApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(QuotationComparisionIncDocOCRWorkflowCodeTxt, 1, 17), CopyStr(QuotationComparisionApprWorkflowDescTxt, 1, 100), CopyStr(QuotationComparisionCategoryTxt, 1, 20));
        InsertQuotationComparisionApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertQuotationComparisionApprovalworkflowDetails(var workflow: record Workflow);
    var
        QCompHeader: Record QuotCompHdr;
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildQuotationComparisiontypecondition(QCompHeader.Status::open), RunworkflowOnSendQuotationComparisionforApprovalCode(), BuildQuotationComparisiontypecondition(QCompHeader.Status::"Pending for Approval"), RunworkflowOnCancelQuotationComparisionforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildQuotationComparisiontypecondition(status: integer): Text
    var
        QCompHeader: Record QuotCompHdr;
    Begin
        QCompHeader.SetRange(status, status);
        exit(StrSubstNo(QuotationComparisionTypeCondnTxt, workflowsetup.Encode(QCompHeader.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidQuotationComparision(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidQuotationComparision(RecordRef)
    end;

    local procedure GetConditionalcardPageidQuotationComparision(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::QuotCompHdr:
                exit(page::"Quotation Comparision Doc");
        end;
    end;
    //qUOTATION cOMPARISION eND

    var
        WorkflowManagement: Codeunit "Workflow Management";
        WorkflowevenHandling: Codeunit "Workflow Event Handling";
        workflowsetup: codeunit "Workflow Setup";
        CheckValuesendforapprovaleventdescTxt: Label 'Approval of a ValueBase document is requested';
        CheckValuerequestcanceleventdescTxt: Label 'Approval of a ValueBase document is Cancelled';
        NoworkfloweableErr: Label 'No Approval workflow for this record type is enabled.';
        ValueBaseCategoryTxt: Label 'ValueBase';
        ValueBaseCategoryDescTxt: Label 'ValueBaseDocuments';
        ValueBaseApprWorkflowDescTxt: Label 'ValuBase Approval Workflow';
        IncDocOCRWorkflowCodeTxt: Label 'INCDOC-ValuBase';
        ValueBaseTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="ValueBaseWF">%1</DataItem></DataItems></ReportParameters>';
        MRsendforapprovaleventdescTxt: Label 'Approval of a MaterialRequest document is requested';
        MRrequestcanceleventdescTxt: Label 'Approval of a MaterialRequest document is Cancelled';
        MRCategoryTxt: Label 'MaterialRequest';
        MRCategoryDescTxt: Label 'MaterialRequestDocuments';
        MRApprWorkflowDescTxt: Label 'MaterialRequest Approval Workflow';
        MRIncDocOCRWorkflowCodeTxt: Label 'INCDOC-Material Request';
        MRTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="MRS Header">%1</DataItem></DataItems></ReportParameters>';
        PRsendforapprovaleventdescTxt: Label 'Approval of a Purchase Requisition document is requested';
        PRrequestcanceleventdescTxt: Label 'Approval of a Purchase Requisition document is Cancelled';
        PRCategoryTxt: Label 'PurchaseRequest';
        PRCategoryDescTxt: Label 'PurchaseRequestDocuments';
        PRApprWorkflowDescTxt: Label 'PurchaseRequest Approval Workflow';
        PRIncDocOCRWorkflowCodeTxt: Label 'INCDOC-Purchase Request';
        PRTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="Purch. Req Header">%1</DataItem></DataItems></ReportParameters>';
        QuotationComparisionsendforapprovaleventdescTxt: Label 'Approval of a Quotation Comparision document is requested';
        QuotationComparisionrequestcanceleventdescTxt: Label 'Approval of a Quotation Comparision document is Cancelled';
        QuotationComparisionCategoryTxt: Label 'QuotationComparisionRequest';
        QuotationComparisionCategoryDescTxt: Label 'QuotationComparisioRequestDocuments';
        QuotationComparisionApprWorkflowDescTxt: Label 'QuotationComparisioRequest Approval Workflow';
        QuotationComparisionIncDocOCRWorkflowCodeTxt: Label 'INCDOC-Quotation Comparisio Request';
        QuotationComparisionTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="QuotCompHdr">%1</DataItem></DataItems></ReportParameters>';
}