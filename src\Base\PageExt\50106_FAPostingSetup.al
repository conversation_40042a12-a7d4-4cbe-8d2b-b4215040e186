pageextension 50118 FAPostingGrpExt extends "FA Posting Groups"
{
    layout
    {
        modify("Sales Acc. on Disp. (Loss)")
        {
            visible = true;
        }
        addafter("Acquisition Cost Account")
        {
            field("No. Series"; "No. Series")
            {
                ApplicationArea = ALL;
            }
            field("GRN Adj. Accural Ac. (Interim)"; "GRN Adj. Accural Ac. (Interim)")
            {
                ApplicationArea = all;
            }
            field("GRN (Interim) Acct"; "GRN (Interim) Acct")
            {
                ApplicationArea = all;
            }

        }
        addafter(Code)
        {
            field("CWIP Posting Type"; "CWIP Posting Type")
            {
                ApplicationArea = all;
            }
            field("Capital Work in Progress"; "Capital Work in Progress")
            {
                ApplicationArea = all;
            }
        }
        addafter("Custom 2 Expense Acc.")
        {
            field("Salvage Perentage"; "Salvage Perentage")
            {
                ApplicationArea = all;
            }
        }
        addafter("Allocated Maintenance %")
        {
            field("No. of Depreciation Years"; "No. of Depreciation Years")
            {
                ApplicationArea = all;
            }
        }

    }

}

