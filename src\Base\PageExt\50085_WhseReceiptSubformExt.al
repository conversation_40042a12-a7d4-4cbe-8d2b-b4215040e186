pageextension 50085 "Whse. Receipt Subform Ext" extends "Whse. Receipt Subform"
{
    layout
    {

        addafter("Item No.")
        {
            field("Posted Loading Slip No."; "Posted Loading Slip No.")
            {
                ApplicationArea = ALL;
                trigger OnValidate()
                var
                    PostdLoadSlpGRec: record "Posted Loading Slip Line";
                BEGIN
                    /*
                    CurrPage.Update();
                    IF "Posted Loading Slip No." <> '' then BEGIN
                        PostdLoadSlpGRec.reset;
                        PostdLoadSlpGRec.SetRange("No.", "No.");
                        PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                        PostdLoadSlpGRec.SetRange("Document No.", "Posted Loading Slip No.");
                        IF PostdLoadSlpGRec.findfirst then BEGIN
                            "Posted Loading Slip Line No." := PostdLoadSlpGRec."Line No.";
                            VALIDATE("Qty. to Receive", PostdLoadSlpGRec."Qty. Loading");
                        end;
                    end else begin
                        Clear("Posted Loading Slip Line No.");
                        Validate("Qty. to Receive", 0);
                    end;*/ //Prasanna.
                end;
            }
            field("Posted Loading Slip Line No."; "Posted Loading Slip Line No.")
            {
                ApplicationArea = all;
            }
            //Balu 05212021>>
            field("No. 2"; "No. 2")
            {
                ApplicationArea = all;
            }
            //Balu 05212021>>
        }

        modify("Qty. to Receive")
        {
            Editable = QtyVisibleGVar;
            trigger OnBeforeValidate()
            BEGIN

                case "Source Document" of
                    "Source Document"::"Sales Order":
                        BEGIN
                            SalHdrGRec.reset;
                            SalHdrGRec.SetRange("No.", "Source No.");
                            IF SalHdrGRec.findfirst then begin
                                IF (SalHdrGRec."Loading Slip Required") THEN
                                    error('You cannot enter Qty to Receive manually Sale order is Loading Slip required.');
                            end
                        End;
                    "Source Document"::"Inbound Transfer":
                        BEGIN
                            TraHdrGRec.reset;
                            TraHdrGRec.SetRange("No.", "Source No.");
                            IF TraHdrGRec.findfirst then begin
                                IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then
                                    message('You cannot enter Qty to Receive Manually Transfer type is Branch Request.');
                            end;
                        END;
                    "Source Document"::"Sales Return Order":
                        BEGIN
                            SalHdrGRec.reset;
                            SalHdrGRec.SetRange("No.", "Source No.");
                            IF SalHdrGRec.findfirst then begin
                                IF (SalHdrGRec."Loading Slip Required") THEN
                                    error('You cannot enter Qty to Receive manually Sale Return order is Loading Slip required.');
                            end
                        END;
                end;
            END;
        }
    }

    actions
    {
        addafter(ItemTrackingLines)
        {
            action("Item Scrap Details list")
            {
                Caption = 'Item Scrap Details list';
                ApplicationArea = all;
                RunObject = Page "Scrap Item List";
                RunPageLink = "Warehouse Doc No." = field("No."),
                "Warehouse Source No." = field("Source No."),
                "Warehouse Source Line No." = field("Source Line No."),
                            "Warehouse Line No." = field("Line No.");
            }
        }
    }
    procedure CreateItemJournalLines(WrhRcptHeader: Record "Warehouse Receipt Header")
    var
        WrhRcptLine: Record "Warehouse Receipt Line";
        ScrapItem: Record "Scrap Items";
        ItemJnlLine: Record "Item Journal Line";
        ItemGRec: Record Item;
        ItemJnlPostLine: Codeunit "Item Jnl.-Post";
        LineNoLVar: Integer;
        InventorySetup: Record "Inventory Setup";
        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
    begin
        InventorySetup.Get();
        WrhRcptLine.Reset();
        WrhRcptLine.SetRange("No.", WrhRcptHeader."No.");
        if WrhRcptLine.FindSet() then
            repeat
                ScrapItem.Reset();
                ScrapItem.SetRange("Warehouse Doc No.", WrhRcptLine."No.");
                //ScrapItem.SetRange("Warehouse Line No.", WrhRcptLine."Line No.");
                //ScrapItem.SetRange("Warehouse Source No.", WrhRcptLine."Source No.");
                //ScrapItem.SetRange("Warehouse Source Line No.", WrhRcptLine."Source Line No.");
                ScrapItem.SetFilter("Qty. to Receive", '>%1', 0);
                if ScrapItem.FindSet() then begin
                    ItemJnlLine.Reset();
                    ItemJnlLine.setrange("Journal Template Name", 'ITEM');
                    ItemJnlLine.setrange("Journal Batch Name", 'DEFAULT');
                    if ItemJnlLine.FindSet() then
                        ItemJnlLine.DeleteAll();
                    //LineNoLVar := ItemJnlLine."Line No.";
                    repeat
                        ItemJnlLine.INIT;
                        ItemJnlLine."Journal Template Name" := 'ITEM';
                        ItemJnlLine."Journal Batch Name" := 'DEFAULT';
                        //ItemJnlLine."Line No." := LineNoLVar + 10000;
                        ItemJnlLine."Line No." := ScrapItem."Line No.";
                        ItemJnlLine.Insert();
                        ItemJnlLine.validate("Entry Type", ItemJnlLine."Entry Type"::"Positive Adjmt.");
                        ItemJnlLine."Document No." := ScrapItem."Warehouse Doc No.";
                        ItemJnlLine."Document Line No." := ScrapItem."Line No.";
                        ItemJnlLine.VALIDATE("Item No.", ScrapItem."Scrap Item");
                        ItemJnlLine.Description := ScrapItem.Description;
                        ItemJnlLine.VALIDATE("Posting Date", WrhRcptHeader."Posting Date");
                        ItemGRec.Get(ScrapItem."Scrap Item");
                        ItemJnlLine.VALIDATE("Unit of Measure Code", ScrapItem."Unit Of Measure");
                        ItemJnlLine.VALIDATE(Quantity, ScrapItem."Qty. to Receive");
                        ItemJnlLine.Validate("Location Code", WrhRcptLine."Location Code");
                        ItemJnlLine.VALIDATE("Gen. Prod. Posting Group", ItemGRec."Gen. Prod. Posting Group");
                        ItemJnlLine.VALIDATE("Shortcut Dimension 1 Code", ScrapItem."Shortcut Dimension 1 Code");
                        ItemJnlLine.VALIDATE("Shortcut Dimension 2 Code", ScrapItem."Shortcut Dimension 2 Code");
                        ItemJnlLine."Dimension Set ID" := ScrapItem."Dimension Set ID";
                        ItemJnlLine.Modify();
                        if ItemGRec."Item Tracking Code" <> '' then
                            UpdateReservationEntry(ItemJnlLine);
                        ScrapItem."Received Quantity" += ScrapItem."Qty. to Receive";
                        ScrapItem."Received Quantity(Base)" += ScrapItem."Qty. to Receive(Base)";
                        ScrapItem.Validate("Qty. to Receive", 0);
                        ScrapItem.Modify();
                    until ScrapItem.Next() = 0;
                    ItemJnlPostBatch.Run(ItemJnlLine);
                end;
            until WrhRcptLine.Next() = 0;
        //ItemJnlPostLine.RUN(ItemJnlLine);
    end;

    procedure UpdateReservationEntry(ItemJournalLineLPar: Record "Item Journal Line")
    var
        ReservationEntry: Record "Reservation Entry";
        ReservationEngineMgt: Codeunit "Reservation Engine Mgt.";
        TransferQty: Decimal;
        CreateReserveEntry: Codeunit "Create Reserv. Entry";
    begin
        ReservationEntry.Reset();
        ReservationEntry.SetRange("Source Type", Database::"Scrap Items");
        ReservationEntry.SetRange("Source Batch Name", 'DEFAULT');
        ReservationEntry.SetRange("Source ID", ItemJournalLineLPar."Document No.");
        ReservationEntry.SetRange("Source Ref. No.", ItemJournalLineLPar."Document Line No.");
        ReservationEntry.SetRange("Source Subtype", ReservationEntry."Source Subtype"::"0");
        if ReservationEngineMgt.InitRecordSet(ReservationEntry) then
            repeat
                ReservationEntry.TestField("Location Code", ItemJournalLineLPar."Location Code");
                ReservationEntry.TestField("Item No.", ItemJournalLineLPar."Item No.");
                //ReservationEntry."New Lot No." := ReservationEntry."Lot No.";
                //ReservationEntry."New Serial No." := ReservationEntry."Serial No.";
                TransferQty := CreateReserveEntry.TransferReservEntry(Database::"Item Journal Line", ItemJournalLineLPar."Entry Type",
                ItemJournalLineLPar."Journal Template Name", ItemJournalLineLPar."Journal Batch Name", ReservationEntry."Source Prod. Order Line", ItemJournalLineLPar."Document Line No.",
                ItemJournalLineLPar."Qty. per Unit of Measure", ReservationEntry, ItemJournalLineLPar."Quantity (Base)");
            until (ReservationEngineMgt.NEXTRecord(ReservationEntry) = 0) or (TransferQty = 0);
    end;

    //> Prasanna Added
    trigger OnAfterGetRecord()
    BEGIN
        VisibleQty;
    END;

    trigger OnAfterGetCurrRecord()

    BEGIN
        VisibleQty;
    END;

    trigger OnModifyRecord(): Boolean
    var
    BEGIN
        VisibleQty;
    END;

    Procedure VisibleQty()
    BEGIN
        QtyVisibleGVar := TRUE;
        case "Source Document" of
            "Source Document"::"Sales Order":
                BEGIN

                    SalHdrGRec.reset;
                    SalHdrGRec.SetRange("No.", "Source No.");
                    IF SalHdrGRec.findfirst then begin
                        IF (SalHdrGRec."Loading Slip Required") THEN
                            QtyVisibleGVar := false;
                    end
                End;
            "Source Document"::"Inbound Transfer":
                BEGIN
                    TraHdrGRec.reset;
                    TraHdrGRec.SetRange("No.", "Source No.");
                    IF TraHdrGRec.findfirst then begin
                        IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then
                            QtyVisibleGVar := FALSE;
                    end;
                END;
        end;
    END;
    //< Prasanna Added

    var
        QtyVisibleGVar: Boolean;
        SalHdrGRec: Record "Sales Header";
        TraHdrGRec: record "Transfer header";
}