﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>44c10963-d03b-4f39-afac-72de93751f39</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="Rectangle2">
            <ReportItems>
              <Textbox Name="Textbox40">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Fields!Name.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>0.64063cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>8.66336cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox42">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Fields!Address.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>1.06396cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>8.66336cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox43">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Fields!Address_2.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>1.48729cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>8.66336cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox45">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Fields!AttentionCaptionLbl.Value+" : "+Fields!Contact.Value</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>Underline</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox45</rd:DefaultName>
                <Top>2.41333cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>4.37711cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox48">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value xml:space="preserve"> </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>None</TextDecoration>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>CHI LIMITED'S RELATIONSHIP WITH ITS CREDIT CUSTOMER: RESTATEMENT OF POLICIES</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>Underline</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox45</rd:DefaultName>
                <Top>2.90722cm</Top>
                <Left>2.15286cm</Left>
                <Height>11pt</Height>
                <Width>16.76484cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox49">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Fields!DearSirCaption.Value</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>3.65594cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>4.37711cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox50">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Fields!SubjectCaption.Value</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>4.07927cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>6.4144cm</Width>
                <ZIndex>6</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox51">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>We write to you, as esteemed Credit Customers in order to reiterate certain policies governing the existing</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>relationship between CHI Limited ("CHI" or the "Company") and your good selves, being one of the key account holders</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>to whom the Company extends credit sales ("Credit Customers"). The policies are as follows:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox51</rd:DefaultName>
                <Top>4.64372cm</Top>
                <Left>0.21732cm</Left>
                <Height>1.235cm</Height>
                <Width>18.70039cm</Width>
                <ZIndex>7</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox52">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value xml:space="preserve"> </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>A.  CREDIT SALES:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>Underline</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>6.21799cm</Top>
                <Left>0.49953cm</Left>
                <Height>11pt</Height>
                <Width>4.37711cm</Width>
                <ZIndex>8</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox53">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>            CHI Limited extends credit sales to its Credit Customers for a designated credit period and a specified </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>            credit limit. The   Credit Customers shall ensure at all times that it meets it payment obligations within the</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>            credit period granted by the Company. Where any Credit Customer fails to make payment for the</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>            Company's product before the expiration of the credit period, the Company may be constrained to</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>            terminate such credit relationship with the Credit Customer, and to treat such customer as a regular</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>            customer with no access to credit sales.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox53</rd:DefaultName>
                <Top>6.60604cm</Top>
                <Left>0.49953cm</Left>
                <Height>2.51382cm</Height>
                <Width>18.41818cm</Width>
                <ZIndex>9</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox54">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>B. NO CASH HANDLING POLICY:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>Underline</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>9.11986cm</Top>
                <Left>0.49953cm</Left>
                <Height>11pt</Height>
                <Width>6.46732cm</Width>
                <ZIndex>10</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox55">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           CHI Limited prohibits its Credit Customers from paying cash to, or extending any credit facility to the </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           Company's sales staff, its employees or its representatives. All payments made by the Credit Customers</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           shall be made either by cheque or by bank transfer, into the Company's designated bank account. All</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           transcations involving payments to the Company are to be carried out strictly in compliance with this</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           policy. The Company will not bear responsibility for any loss suffered by a Credit Customer in defiance</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           of this 'no cash handling' rule, and all cash given to our sales representatives will be at the sole risk of</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>           such Credit Customer.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox55</rd:DefaultName>
                <Top>9.54319cm</Top>
                <Left>0.49953cm</Left>
                <Height>2.96583cm</Height>
                <Width>18.41818cm</Width>
                <ZIndex>11</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox56">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>C.  POLICY ON DELIVERY OF PRODUCT:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>Underline</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>12.50902cm</Top>
                <Left>0.49953cm</Left>
                <Height>11pt</Height>
                <Width>6.46732cm</Width>
                <ZIndex>12</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox57">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>i . </Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>     At the point of delivery, the Company insists that all Credit Customers or their authorized representatives</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          shall sign acknowledging receipt of the products and shall duly endorse the copy of the invoice as </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          proof of delivery. Where the Credit Customer or its authorized representative fails or refuses to endorse</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          the copy of the invoice as acknowledgement of receipt of the products, the products shall not be delivered </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          to the Credit Customer, but shall be returned to the Company's warehouse.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value />
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>ii.     </Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>It shall be the responsibility of the Credit Customer to ensure that the proof of delivery matches the quantity</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          of goods delivered to the Credit Customer, and the Company shall treat the proof of delivery as conclusive</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          proof that the products were duly delivered in full.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value />
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>iii.    </Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>Under no circumstances whatsoever should any Credit Customer give the Company's products to any</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          of the Company's sales representatives. The Company will not bear responsibility for any loss suffered by a </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          Credit Customer in defiance of this rule, and all products given to our sales representatives will be at the</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>          sole risk of such Credit Customer.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox55</rd:DefaultName>
                <Top>12.89708cm</Top>
                <Left>0.49956cm</Left>
                <Height>5.98842cm</Height>
                <Width>18.41818cm</Width>
                <ZIndex>13</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox58">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        Products delivered to Credit Customers are made to the Company's set standard of quality and as such, products </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        represented by the Credit Customer as defective or having been damaged in transit shall not form the subject of any claim</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        by the Credit Customer or of any incidental or consequential loss, damage or liability howsoever arising indirectly from  </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        such defects; but such products, if returned to the Company within seven (7) calendar days of its delivery and accepted by </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        the Company as defective or damaged, will at the request of the Credit Customer and if practicable, be replaced as </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        originally ordered; provided however that defects in quality of products or damage of products in any delivery shall not be a </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>        ground for cancellation of the remainder of the order or contract.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value />
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <ListLevel>1</ListLevel>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>         Please be assured that we value your business and seek protection of your interest in furtherance of the mutually </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>         beneficial relationship.</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value />
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <ListLevel>1</ListLevel>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>         In the event that you require any clarification on any of these policies, or any issue related to your business with us, </Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>         kindlycontact on by email or telephone as follows:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Normal</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox55</rd:DefaultName>
                <Top>19.34411cm</Top>
                <Left>0.49952cm</Left>
                <Height>5.98208cm</Height>
                <Width>18.41818cm</Width>
                <ZIndex>14</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox59">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>D.   	POLICY ON DEFECTIVE PRODUCTS AND PRODUCTS DAMAGED IN TRANSIT:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <TextDecoration>Underline</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox40</rd:DefaultName>
                <Top>18.8855cm</Top>
                <Left>0.49956cm</Left>
                <Height>11pt</Height>
                <Width>15.54253cm</Width>
                <ZIndex>15</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox39">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>The Managing Director / Chief Finance Office</Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox39</rd:DefaultName>
                <Top>0.18202cm</Top>
                <Left>0.21732cm</Left>
                <Height>11pt</Height>
                <Width>8.66336cm</Width>
                <ZIndex>16</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox46">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Today</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Format>MMMM d, yyyy</Format>
                          <TextDecoration>None</TextDecoration>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox45</rd:DefaultName>
                <Top>0.18202cm</Top>
                <Left>12.19411cm</Left>
                <Height>11pt</Height>
                <Width>6.72357cm</Width>
                <ZIndex>17</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>0.03528cm</Top>
            <Left>0cm</Left>
            <Height>25.32619cm</Height>
            <Width>18.91774cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Fields!EmailName.Value</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>25.39675cm</Top>
            <Left>0.49956cm</Left>
            <Height>0.49416cm</Height>
            <Width>7.36687cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox12">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Yours faithfully,</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox12</rd:DefaultName>
            <Top>26.85201cm</Top>
            <Left>0.49956cm</Left>
            <Height>0.6cm</Height>
            <Width>6.46729cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox14">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>For:</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox12</rd:DefaultName>
            <Top>27.45201cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.6cm</Height>
            <Width>1.11389cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox15">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Fields!CompanyInfo_Name.Value</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox15</rd:DefaultName>
            <Top>27.45201cm</Top>
            <Left>1.64869cm</Left>
            <Height>0.6cm</Height>
            <Width>5.31816cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox16">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox16</rd:DefaultName>
            <Top>28.05201cm</Top>
            <Left>0.49956cm</Left>
            <Height>0.86458cm</Height>
            <Width>6.46729cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox18">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Fields!SobhanMukherjee.Value</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox18</rd:DefaultName>
            <Top>28.95187cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.54709cm</Height>
            <Width>6.46733cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox19">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>UNDERTAKING</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>Underline</TextDecoration>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>:</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox19</rd:DefaultName>
            <Top>30.30153cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.6cm</Height>
            <Width>4.09491cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="We, "+ Fields!Name.Value +"  hereby confirm that we have read and understood the contents of the above letter, and we agree to be bound and undertake to abide by all the terms set out in this letter."</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox6</rd:DefaultName>
            <Top>30.93681cm</Top>
            <Left>0.49952cm</Left>
            <Height>1.26146cm</Height>
            <Width>18.41818cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="Signed for and on behalf of :  "+Fields!Name.Value</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>32.23354cm</Top>
            <Left>0.49956cm</Left>
            <Height>0.75875cm</Height>
            <Width>17.40527cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox20">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Name:  </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>33.1334cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox21">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Name:  </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>33.1334cm</Top>
            <Left>9.27754cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox22">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Position:   </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>33.7334cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox23">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Position:  </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>33.7334cm</Top>
            <Left>9.27754cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox24">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Signature:   </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>34.36868cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>14</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox25">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Signature:   </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>34.3334cm</Top>
            <Left>9.27754cm</Left>
            <Height>0.63528cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>15</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox26">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Date:   </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>35.00396cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>16</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox27">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Date:  </Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>____________________________</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>35.00396cm</Top>
            <Left>9.27754cm</Left>
            <Height>0.6cm</Height>
            <Width>8.74274cm</Width>
            <ZIndex>17</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox28">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Fields!PhoneNumber.Value</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>25.92619cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.49416cm</Height>
            <Width>7.36687cm</Width>
            <ZIndex>18</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox29">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Fields!NationalSalesDirector.Value</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox18</rd:DefaultName>
            <Top>29.49896cm</Top>
            <Left>0.49952cm</Left>
            <Height>0.54709cm</Height>
            <Width>6.46733cm</Width>
            <ZIndex>19</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>35.60396cm</Height>
        <Style />
      </Body>
      <Width>18.91774cm</Width>
      <Page>
        <PageHeader>
          <Height>5.95315cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Rectangle Name="Rectangle1">
              <ReportItems>
                <Textbox Name="Textbox2">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_Name.Value</Value>
                          <Style>
                            <FontSize>22pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox2</rd:DefaultName>
                  <Top>0cm</Top>
                  <Left>12.19411cm</Left>
                  <Height>1.03189cm</Height>
                  <Width>6.7236cm</Width>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox3">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!OfficeAddressCaptionLbl.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox3</rd:DefaultName>
                  <Top>1.57869cm</Top>
                  <Left>12.19412cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>1</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox1">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_Address.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox1</rd:DefaultName>
                  <Top>2.1255cm</Top>
                  <Left>12.19412cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>2</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox4">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_Addess2.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox4</rd:DefaultName>
                  <Top>2.63704cm</Top>
                  <Left>12.19412cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>3</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox5">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>TEL: +234-1-903-9000-29,774-0383</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>3.18384cm</Top>
                  <Left>12.19412cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.72359cm</Width>
                  <ZIndex>4</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox7">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!WebsiteCaptionLbl.Value+" : "+Fields!CompanyInfo_HomePage.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>4.82427cm</Top>
                  <Left>12.19412cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.72359cm</Width>
                  <ZIndex>5</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox9">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!EmailCaptionLbl.Value+" : "+Fields!CompanyInfo_EMail.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>4.20691cm</Top>
                  <Left>12.19412cm</Left>
                  <Height>0.58207cm</Height>
                  <Width>6.72359cm</Width>
                  <ZIndex>6</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox11">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value> FAX: +234-1-271-9265, 01-271-9266</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>3.69538cm</Top>
                  <Left>12.19413cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.72358cm</Width>
                  <ZIndex>7</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox13">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value />
                          <Style />
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox13</rd:DefaultName>
                  <Top>0.51153cm</Top>
                  <Left>0.49953cm</Left>
                  <Height>3.21029cm</Height>
                  <Width>3.16146cm</Width>
                  <ZIndex>8</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Image Name="Image1">
                  <Source>Database</Source>
                  <Value>=First(Fields!CompanyInfo_Picture.Value, "DataSet_Result")</Value>
                  <MIMEType>image/jpeg</MIMEType>
                  <Sizing>FitProportional</Sizing>
                  <Top>0.51153cm</Top>
                  <Left>0.49953cm</Left>
                  <Height>3.21029cm</Height>
                  <Width>3.16146cm</Width>
                  <ZIndex>9</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                  </Style>
                </Image>
                <Textbox Name="Textbox17">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_RegistrationNo.Value</Value>
                          <Style>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Right</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox2</rd:DefaultName>
                  <Top>1.03189cm</Top>
                  <Left>12.19413cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>10</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Line Name="Line1">
                  <Top>5.63245cm</Top>
                  <Left>0.21732cm</Left>
                  <Height>0cm</Height>
                  <Width>18.70036cm</Width>
                  <ZIndex>11</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                  </Style>
                </Line>
              </ReportItems>
              <KeepTogether>true</KeepTogether>
              <Height>2.32987in</Height>
              <Width>7.44793in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Rectangle>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>1cm</LeftMargin>
        <RightMargin>1cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="CompanyInfo_Picture">
          <DataField>CompanyInfo_Picture</DataField>
        </Field>
        <Field Name="CompanyInfo_Name">
          <DataField>CompanyInfo_Name</DataField>
        </Field>
        <Field Name="CompanyInfo_Address">
          <DataField>CompanyInfo_Address</DataField>
        </Field>
        <Field Name="CompanyInfo_Addess2">
          <DataField>CompanyInfo_Addess2</DataField>
        </Field>
        <Field Name="CompanyInfo_PhoneNo">
          <DataField>CompanyInfo_PhoneNo</DataField>
        </Field>
        <Field Name="CompanyInfo_PhoneNo2">
          <DataField>CompanyInfo_PhoneNo2</DataField>
        </Field>
        <Field Name="CompanyInfo_RegistrationNo">
          <DataField>CompanyInfo_RegistrationNo</DataField>
        </Field>
        <Field Name="CompanyInfo_FaxNo">
          <DataField>CompanyInfo_FaxNo</DataField>
        </Field>
        <Field Name="CompanyInfo_EMail">
          <DataField>CompanyInfo_EMail</DataField>
        </Field>
        <Field Name="CompanyInfo_HomePage">
          <DataField>CompanyInfo_HomePage</DataField>
        </Field>
        <Field Name="OfficeAddressCaptionLbl">
          <DataField>OfficeAddressCaptionLbl</DataField>
        </Field>
        <Field Name="FAXCaptionLbl">
          <DataField>FAXCaptionLbl</DataField>
        </Field>
        <Field Name="EmailCaptionLbl">
          <DataField>EmailCaptionLbl</DataField>
        </Field>
        <Field Name="TelCaptionLbl">
          <DataField>TelCaptionLbl</DataField>
        </Field>
        <Field Name="WebsiteCaptionLbl">
          <DataField>WebsiteCaptionLbl</DataField>
        </Field>
        <Field Name="AttentionCaptionLbl">
          <DataField>AttentionCaptionLbl</DataField>
        </Field>
        <Field Name="CHILtdCaption">
          <DataField>CHILtdCaption</DataField>
        </Field>
        <Field Name="DearSirCaption">
          <DataField>DearSirCaption</DataField>
        </Field>
        <Field Name="SubjectCaption">
          <DataField>SubjectCaption</DataField>
        </Field>
        <Field Name="EmailName">
          <DataField>EmailName</DataField>
        </Field>
        <Field Name="PhoneNumber">
          <DataField>PhoneNumber</DataField>
        </Field>
        <Field Name="SobhanMukherjee">
          <DataField>SobhanMukherjee</DataField>
        </Field>
        <Field Name="NationalSalesDirector">
          <DataField>NationalSalesDirector</DataField>
        </Field>
        <Field Name="Contact">
          <DataField>Contact</DataField>
        </Field>
        <Field Name="Name">
          <DataField>Name</DataField>
        </Field>
        <Field Name="Address">
          <DataField>Address</DataField>
        </Field>
        <Field Name="Address_2">
          <DataField>Address_2</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>