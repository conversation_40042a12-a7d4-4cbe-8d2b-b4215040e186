/// <summary>
/// Page Custom Journal Batch Setup (ID 50584).
/// </summary>
page 50584 "Custom Journal Batch Setup"
{
    Caption = 'Custom Journal Batch Setup';
    PageType = List;
    SourceTable = "Custom Journal Batch Setup";
    ApplicationArea = All;
    UsageCategory = Lists;


    layout
    {
        area(content)
        {
            repeater(General)
            {
                field(UserID; Rec."User ID")
                {
                    ApplicationArea = All;
                }
                field("Batch Name"; Rec."Batch Name")
                {
                    ApplicationArea = All;
                }
                field(Enabled; Rec.Enabled)
                {
                    ApplicationArea = All;

                    trigger OnValidate()
                    var
                        ItemJnlBatch: Record "Item Journal Batch";
                        CustomSetup: Record "Custom Setup";
                        Txt001: Label 'Journal batch has been successfully  %1 for %2';
                    begin
                        ItemJnlBatch.Reset();
                        CustomSetup.Reset();
                        CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
                        CustomSetup.SetFilter("Consumption Journal Temp.", '<>%1', '');
                        If CustomSetup.FindFirst() then begin
                            if Rec.Enabled = true then begin
                                ItemJnlBatch.Init();
                                ItemJnlBatch.Name := Rec."Batch Name";
                                ItemJnlBatch."Journal Template Name" := CustomSetup."Consumption Journal Temp.";
                                ItemJnlBatch.Description := StrSubstNo('Consumption Journal Batch for %1', Rec."User ID");
                                ItemJnlBatch."Template Type" := ItemJnlBatch."Template Type"::Consumption;
                                if Not ItemJnlBatch.Insert() then ItemJnlBatch.Modify();
                                Message(Txt001, 'created', "User ID");
                            end else begin
                                ItemJnlBatch.Reset();
                                ItemJnlBatch.SetRange("Journal Template Name", CustomSetup."Consumption Journal Temp.");
                                ItemJnlBatch.SetRange(Name, Rec."Batch Name");
                                if ItemJnlBatch.FindFirst() then begin
                                    ItemJnlBatch.Delete();
                                    Message(Txt001, 'deleted', "User ID");
                                end;
                            end;
                        end;
                    end;
                }
                // field("Can Edit Cons. Jnl?"; "Can Edit Cons. Jnl?")
                // {
                //     ApplicationArea = All;
                // }
            }
        }
    }
}
