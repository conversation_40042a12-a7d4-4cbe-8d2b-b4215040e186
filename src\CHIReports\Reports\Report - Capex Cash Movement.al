report 50149 "Capex Cash Movement"
{
    // version Capex

    DefaultLayout = RDLC;
    //RDLCLayout = 'Reports\Layout\Capex Cash Movement.rdl';
    RDLCLayout = './Capex Cash Movement.rdl';
    ApplicationArea = all;
    UsageCategory = ReportsAndAnalysis;
    Caption = 'Capex Cash Movement_50149';
    dataset
    {
        dataitem("G/L Entry"; "G/L Entry")
        {
            DataItemTableView = WHERE("Source Code" = FILTER('CPV|BPV|PURCHASES'));
            RequestFilterFields = "Posting Date", "Global Dimension 2 Code", "Global Dimension 1 Code";
            column(FORMAT_TODAY_0_4_; FORMAT(TODAY, 0, 4))
            {
            }
            column(COMPANYNAME; COMPANYNAME)
            {
            }
            column(CurrReport_PAGENO; '')
            {
            }
            column(USERID; USERID)
            {
            }
            column(Reportfilter; Reportfilter)
            {
            }
            column(G_L_Entry__Capex_No__; "Capex No.")
            {
            }
            column(Desc; Desc)
            {
            }
            column(G_L_Entry__Description_2_; "Description 2")//
            {
            }
            column(AppAmount; AppAmount)
            {
            }
            column(G_L_Entry__Document_No__; "Document No.")
            {
            }
            column(G_L_Entry__Posting_Date_; "Posting Date")
            {
            }
            column(G_L_Entry__Global_Dimension_1_Code_; "Global Dimension 1 Code")
            {
            }
            column(G_L_Entry__Global_Dimension_2_Code_; "Global Dimension 2 Code")
            {
            }
            column(CapexAmt; CapexAmt)
            {
            }
            column(G_L_Entry__G_L_Account_No__; "G/L Account No.")
            {
            }
            column(AppAmount_Control1000000030; AppAmount)
            {
            }
            column(SubCapexAmt; SubCapexAmt)
            {
            }
            column(CAPEX_CASH_MOVEMENTCaption; CAPEX_CASH_MOVEMENTCaptionLbl)
            {
            }
            column(CurrReport_PAGENOCaption; CurrReport_PAGENOCaptionLbl)
            {
            }
            column(Report_ID____50604Caption; Report_ID____50604CaptionLbl)
            {
            }
            column(G_L_Entry__Capex_No__Caption; FIELDCAPTION("Capex No."))
            {
            }
            column(DescriptionCaption; DescriptionCaptionLbl)
            {
            }
            column(Payment_DescriptionCaption; Payment_DescriptionCaptionLbl)
            {
            }
            column(Amount_Caption; Amount_CaptionLbl)
            {
            }
            column(G_L_Entry__Document_No__Caption; FIELDCAPTION("Document No."))
            {
            }
            column(G_L_Entry__Posting_Date_Caption; FIELDCAPTION("Posting Date"))
            {
            }
            column(G_L_Entry__Global_Dimension_1_Code_Caption; FIELDCAPTION("Global Dimension 1 Code"))
            {
            }
            column(G_L_Entry__Global_Dimension_2_Code_Caption; FIELDCAPTION("Global Dimension 2 Code"))
            {
            }
            column(Capex_AmountCaption; Capex_AmountCaptionLbl)
            {
            }
            column(G_L_Account_No_Caption; G_L_Account_No_CaptionLbl)
            {
            }
            column(SUB_TOTALCaption; SUB_TOTALCaptionLbl)
            {
            }
            column(G_L_Entry_Entry_No_; "Entry No.")
            {
            }

            trigger OnAfterGetRecord();
            begin
                CLEAR(GLAccount);
                GLAccount := "G/L Account No.";
                CheckAccountAQorCWIP;
                IF (NOT AQ) AND (NOT CWIP) THEN
                    CurrReport.SKIP;

                CLEAR(Desc);
                CLEAR(CapexAmt);
                CLEAR(AppAmount);

                BudgLineRec.RESET;
                BudgLineRec.SETRANGE("Document Type", BudgLineRec."Document Type"::Capex);
                BudgLineRec.SETRANGE("Document No.", "Capex No.");
                IF BudgLineRec.FINDFIRST THEN BEGIN
                    CapexAmt := BudgLineRec."Amount(LCY)";
                    SubCapexAmt := BudgLineRec."Amount(LCY)";
                    Desc := BudgLineRec.Description;
                END;


                //Get PDS/BPV Amount
                IF (GLAcct <> "G/L Account No.") AND ("Source Code" = 'PURCHASES') THEN BEGIN
                    GLAcct := "G/L Account No.";
                    VLEntry.RESET;
                    VLEntry.SETRANGE("Document No.", "Document No.");
                    //VLEntry.SETRANGE("Vendor No.","Source No.");
                    VLEntry.SETRANGE("Source Code", 'PURCHASES');
                    VLEntry.SETFILTER("Document Type", '%1', VLEntry."Document Type"::Invoice);
                    IF VLEntry.FINDFIRST THEN BEGIN
                        VLEntry2.RESET;
                        VLEntry2.SETRANGE("Vendor No.", VLEntry."Vendor No.");
                        VLEntry2.SETRANGE("Source Code", 'BPV');
                        VLEntry2.SETRANGE("External Document No.", VLEntry."External Document No.");
                        VLEntry2.SETFILTER("Document Type", '%1', VLEntry."Document Type"::Payment);
                        IF VLEntry2.FINDFIRST THEN BEGIN
                            VLEntry2.CALCFIELDS(Amount);
                            AppAmount := VLEntry2.Amount;
                            SubAppAmount += VLEntry2.Amount;
                        END;
                    END;
                END;

                //Get CPV
                IF "Source Code" = 'CPV' THEN BEGIN
                    AppAmount := Amount;
                    SubAppAmount += Amount;
                END;

                // IF AppAmount = 0 THEN
                //     CurrReport.SHOWOUTPUT(false);

                IF (PrintExcel) and (AppAmount <> 0) THEN BEGIN
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT("Capex No."), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 2, FORMAT(Desc), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 3, FORMAT("G/L Account No."), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT(CapexAmt), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 5, FORMAT("Posting Date"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 6, FORMAT("Document No."), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 7, FORMAT("Description 2"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT("Global Dimension 1 Code"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 9, FORMAT("Global Dimension 2 Code"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 10, FORMAT(AppAmount), FALSE, FALSE, FALSE);
                END;
            end;

            trigger OnPostDataItem();
            begin

                IF PrintExcel THEN BEGIN
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT('SUB TOTAL'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT(SubCapexAmt), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, FORMAT(AppAmount), TRUE, FALSE, FALSE);
                END;

            end;

            trigger OnPreDataItem();
            begin
                SetCurrentKey("Posting Date", "Source Code", "Transaction No.");
                IF PrintExcel THEN BEGIN
                    TempExcelBuffer.DELETEALL;
                    CLEAR(TempExcelBuffer);

                    RowNo := 1;
                    EnterCell(RowNo, 1, 'Capex Cash Movement Report', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT(TODAY, 0, 4), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(UPPERCASE(COMPANYNAME)), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT(USERID), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(Reportfilter), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, 'Capex No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 2, FORMAT('Description'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 3, FORMAT('G/L Account No.'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT('Capex Amount'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 5, FORMAT('Posting Date'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 6, FORMAT('Document No.'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 7, FORMAT('Payment Description'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT('Accounting Location'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 9, FORMAT('Cc Code'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, FORMAT('Amount'), TRUE, FALSE, FALSE);
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
            area(content)
            {
                field(PrintExcel; PrintExcel)
                {
                    Caption = 'Print In Excel';
                    ApplicationArea = all;
                }
            }
        }

        actions
        {
        }
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        Reportfilter := "G/L Entry".GETFILTERS();
    end;

    trigger OnPostReport()
    begin
        IF PrintExcel THEN BEGIN
            TempExcelBuffer.CreateNewBook('Capex Cash Movement Report');
            TempExcelBuffer.WriteSheet('Capex Cash Movement Report', CompanyName(), UserId());
            TempExcelBuffer.CloseBook();
            TempExcelBuffer.OpenExcel();
        END;
    end;

    var

        Desc: Text[200];
        CapexAmt: Decimal;
        Reportfilter: Text;
        PrintExcel: Boolean;
        RowNo: Integer;
        ColumnNo: Integer;

        TempExcelBuffer: Record 370 temporary;
        FAPostingGroup: Record 5606;
        BudgLineRec: Record "Budget Line";
        AQ: Boolean;
        CWIP: Boolean;
        GLAccount: Code[10];
        VLEntry: Record 25;
        VLEntry2: Record 25;
        AppAmount: Decimal;
        GLAcct: Code[20];
        SubAppAmount: Decimal;
        SubCapexAmt: Decimal;
        CAPEX_CASH_MOVEMENTCaptionLbl: Label 'CAPEX CASH MOVEMENT';
        CurrReport_PAGENOCaptionLbl: Label 'Page';
        Report_ID____50604CaptionLbl: Label 'Report ID::>>50104';
        DescriptionCaptionLbl: Label 'Description';
        Payment_DescriptionCaptionLbl: Label 'Payment Description';
        Amount_CaptionLbl: Label '"Amount "';
        Capex_AmountCaptionLbl: Label 'Capex Amount';
        G_L_Account_No_CaptionLbl: Label 'G/L Account No.';
        SUB_TOTALCaptionLbl: Label 'SUB TOTAL';

    procedure EnterCell(RowNo: Integer; ColumnNo: Integer; Cellvalue: Text[250]; Bold: Boolean; Italic: Boolean; UnderLine: Boolean);
    begin
        TempExcelBuffer.INIT;
        TempExcelBuffer.VALIDATE("Row No.", RowNo);
        TempExcelBuffer.VALIDATE("Column No.", ColumnNo);
        TempExcelBuffer."Cell Value as Text" := Cellvalue;
        TempExcelBuffer.Formula := '';
        TempExcelBuffer.Bold := Bold;
        TempExcelBuffer.Italic := Italic;
        TempExcelBuffer.Underline := UnderLine;
        TempExcelBuffer.INSERT;
    end;

    procedure CheckAccountAQorCWIP();
    begin
        AQ := FALSE;
        CWIP := FALSE;

        FAPostingGroup.RESET;
        FAPostingGroup.SETCURRENTKEY(Code);
        FAPostingGroup.SETFILTER(FAPostingGroup."CWIP Posting Type", '%1', TRUE);
        FAPostingGroup.SETRANGE("Acquisition Cost Account", GLAccount);
        IF FAPostingGroup.FINDFIRST THEN
            AQ := TRUE;


        FAPostingGroup.RESET;
        FAPostingGroup.SETCURRENTKEY(Code);
        FAPostingGroup.SETFILTER(FAPostingGroup."CWIP Posting Type", '%1', TRUE);
        FAPostingGroup.SETRANGE("Capital Work in Progress", GLAccount);
        IF FAPostingGroup.FINDFIRST THEN
            CWIP := TRUE;
    end;
}

