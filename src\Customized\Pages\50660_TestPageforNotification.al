page 50666 "Test Page"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            group(GroupName)
            {
                field(Name; NameSource)
                {
                    ApplicationArea = All;

                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Run notifications")
            {
                ApplicationArea = All;

                trigger OnAction()
                var
                    Notifications: Codeunit "Notification Entry Dispatch 2";
                    Jobqueue: Record "Job Queue Entry";
                begin
                    Notifications.Run(Jobqueue);
                end;
            }
        }
    }

    var
        myInt: Integer;
        NameSource: text[100];
}