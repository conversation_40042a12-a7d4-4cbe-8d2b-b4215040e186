tableextension 50050 BankAccTabExt extends "Bank Account"
{
    fields
    {
        field(50000; "Cheque No."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Bank Cheque Details"."Cheque No.";
        }
        field(50001; "Last Cheque No."; Code[10])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50002; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
        }
        //<<B2BSB.1.0
        field(50003; "Account Type"; Enum AccountType)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "Teller Bank Name"; enum "Teller Bank Name")
        {
            DataClassification = CustomerContent;
        }//>>B2BSB.1.0
        field(50005; "Responsibility Center"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center";
        }
        field(50006; "Cash Account Type"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = '" ,Main,Petty"';
            OptionMembers = " ",Main,Petty;
        }
        field(50007; "Enable API File"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50008; "API Code"; Code[20])
        {
            //Editable = false;
            DataClassification = CustomerContent;
        }
        field(50009; "Bank Code"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50010; "BNK Statemnt End Bal(External)"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        //>>>>>> G2S 21st Aug., 2023
        //CR: RFC#39  
        //Signed: 31st Oct., 2023
        //Name: Go2Solve Nig. Ltd
        //Published: 10th Nov., 2023
        field(50011; "BankAPI Source Bank Code"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        //<<<<<< end
    }
}