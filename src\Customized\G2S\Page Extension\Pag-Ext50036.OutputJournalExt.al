/// <summary>
/// PageExtension Output Journal Ext (ID 50017) extends Record Output Journal.
/// </summary>
pageextension 50036 "Output Journal Ext" extends "Output Journal"
{
    layout
    {
    }
    actions
    {
        //g2s
        modify(Post)
        {
            trigger OnBeforeAction()
            var

            begin
                //validate item tracking
                // CustomCodeUnit.AssignItemTracking(Rec);
                AssignItemTracking();
                //validate external no
                CustomCodeUnit.CheckExternalDocNo(Rec);
                //validate output against  order
                ProdOrderLine.Reset();
                ProdOrderLine.SetRange("Prod. Order No.", Rec."Document No.");
                if ProdOrderLine.FindFirst() then begin
                    if Rec.Quantity > ProdOrderLine."Remaining Quantity" then
                        Error(InvalidOutput, Rec."Document No.");
                end;
            end;

            // trigger OnAfterAction()
            // var
            //     myInt: Integer;
            //     ProdOrder:  Page "Released Production Order";
            // begin
            //     ProdOrder.Activate(true);
            //     ProdOrder.Update(true);
            // end;


        }

        modify("Post and &Print")
        {

            trigger OnBeforeAction()
            var

            begin
                //validate item tracking
                AssignItemTracking();
                //validate external no
                CustomCodeUnit.CheckExternalDocNo(Rec);

                //validate output against  order
                ProdOrderLine.Reset();
                ProdOrderLine.SetRange("Prod. Order No.", Rec."Document No.");
                if ProdOrderLine.FindFirst() then begin
                    if Rec.Quantity > ProdOrderLine."Remaining Quantity" then
                        Error(InvalidOutput, Rec."Document No.");
                end;
            end;
        }
    }


    /// <summary>
    /// ValidateEditableFields.
    /// </summary>
    /// <param name="ItemJnl">Record "Item Journal Line".</param>
    procedure ValidateEditableFields(ItemJnl: Record "Item Journal Line")
    var
    begin
        // Item2.Reset();
        // Item2.SetRange("No.", ItemJnl."Source No.");
        // Item2.SetRange("License Plate Enabled?", true);
        // if Item2.FindFirst() then begin
        ItemJnl.SetRange("Journal Batch Name", 'SCANNER');
        If ItemJnl.FindFirst() then begin
            LPEnabledItem := true;
        end;
        // CurrentJnlBatchName := '';
        // Error(Txt001, ItemJnl."Order No.");
        // end;
    end;


    /// <summary>
    /// AssignItemTracking.
    /// </summary>
    procedure AssignItemTracking()
    var
        ItemJournalLines: Record "Item Journal Line";
        Item: Record Item;
        ReservationEntry: Record "Reservation Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;
        ReservationEntry2: Record "Reservation Entry";
    begin
        ItemJournalLines.Reset();
        ItemJournalLines.SetRange("Journal Template Name", "Journal Template Name");
        ItemJournalLines.SetRange("Journal Batch Name", "Journal Batch Name");
        ItemJournalLines.SetRange("Document No.", "Document No.");
        ItemJournalLines.SetRange("Entry Type", ItemJournalLines."Entry Type"::Output);
        if ItemJournalLines.FindSet() then
            repeat
                if Item.Get(ItemJournalLines."Item No.") and (Item."Item Tracking Code" <> '') then begin
                    LineQty := ItemJournalLines."Quantity (Base)";
                    ReservationEntry2.RESET;
                    IF ReservationEntry2.FINDLAST THEN
                        EntryNum := ReservationEntry2."Entry No." + 1
                    ELSE
                        EntryNum := 1;
                    ReservationEntry2.Reset();
                    ReservationEntry2.SetRange("Source ID", ItemJournalLines."Journal Template Name");
                    ReservationEntry2.SetRange("Source Subtype", 5);
                    ReservationEntry2.SetRange("Source Type", DATABASE::"Item Journal Line");
                    ReservationEntry2.SetRange("Source Ref. No.", ItemJournalLines."Line No.");
                    ReservationEntry2.SetRange("Source Batch Name", ItemJournalLines."Journal Batch Name");
                    if ReservationEntry2.FindSet() then
                        ReservationEntry2.DeleteAll();
                    ItemLedgerEntry.RESET;
                    ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
                    ItemLedgerEntry.SETRANGE("Item No.", ItemJournalLines."Item No.");
                    ItemLedgerEntry.SETRANGE("Location Code", ItemJournalLines."Location Code");
                    ItemLedgerEntry.SETRANGE("Variant Code", ItemJournalLines."Variant Code");
                    ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
                    IF ItemLedgerEntry.FINDSET THEN
                        REPEAT

                            IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                                AssignQty := LineQty;
                                LineQty := 0;
                            END ELSE BEGIN
                                AssignQty := (ItemLedgerEntry."Remaining Quantity");
                                LineQty -= AssignQty;
                            END;
                            ReservationEntry.INIT;
                            ReservationEntry."Entry No." := EntryNum;
                            ReservationEntry.VALIDATE(Positive, FALSE);
                            ReservationEntry.VALIDATE("Item No.", ItemJournalLines."Item No.");
                            ReservationEntry.VALIDATE("Location Code", ItemJournalLines."Location Code");
                            ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                            ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / ItemJournalLines."Qty. per Unit of Measure"));
                            ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                            ReservationEntry.VALIDATE("Creation Date", WorkDate());
                            ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
                            ReservationEntry.VALIDATE("Source Subtype", 5);
                            ReservationEntry.VALIDATE("Source ID", ItemJournalLines."Journal Template Name");

                            ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLines."Line No.");
                            ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                            ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                            ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                            ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                            ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                            ReservationEntry.Validate("Source Batch Name", ItemJournalLines."Journal Batch Name");
                            ReservationEntry."Created By" := USERID;
                            ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                            ReservationEntry.VALIDATE(Correction, FALSE);
                            ReservationEntry.INSERT;
                            EntryNum += 1;
                        until (ItemLedgerEntry.Next() = 0) OR (LineQty = 0);
                end;
            until ItemJournalLines.Next() = 0;
    end;

    trigger OnOpenPage()
    var
        myInt: Integer;
    begin
        ValidateEditableFields(Rec);
    end;

    var
        ItemJournalLrec: Record "Item Journal Line";
        Item2: Record Item;
        LPEnabledItem: Boolean;
        ItemJournalBatchPage: Code[30];
        CustomCodeUnit: Codeunit "Custom-G2S";
        InvalidOutput: Label 'Kindly review the quantity stated in the journal as this is more than the remaining quantity for order %1';
        ProdOrderLine: Record "Prod. Order Line";
        ItemJnlLine: Record "Item Journal Line";
}