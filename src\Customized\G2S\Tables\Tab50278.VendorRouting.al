//G2S>>> 07/05/25>>> CAS-01435-X5M1P2
table 50379 "Vendor Routing"
{
    Caption = 'Vendor Routing';
    // DataClassification = ToBeClassified;

    fields
    {
        field(1; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            TableRelation = Vendor."No.";
        }
        field(2; "Routing Code"; Code[20])
        {
            Caption = 'Routing Code';
            TableRelation = "Bank Routing Code";
            trigger OnValidate()
            var
                BankRtngCode: Record "Bank Routing Code";
            begin
                IF xRec."Routing Code" <> Rec."Routing Code" THEN BEGIN
                    BankRtngCode.Reset();
                    BankRtngCode.SetRange("Routing Code", Rec."Routing Code");
                    IF BankRtngCode.FINDFIRST() THEN BEGIN
                        Rec."Bank Name" := BankRtngCode."Bank Name";
                        Rec."Bank Code" := BankRtngCode."Bank Code";
                    END;
                END;
            end;
        }
        field(3; "Bank Name"; Text[50])
        {
            Caption = 'Bank Name';
        }
        field(4; "Bank Code"; Option)
        {
            Caption = 'Bank Code';
            OptionCaption = '" ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB,HERITAGE,JAIZ,RMB,Polaris,PROVIDUS,CORONATION,TTB';
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB,HERITAGE,JAIZ,RMB,Polaris,PROVIDUS,CORONATION,TTB;
        }
        field(5; "Bank No."; Code[20])
        {
            Caption = 'Bank No.';
        }
    }
    keys
    {
        key(PK; "Routing Code", "Vendor No.")
        {
            Clustered = true;
        }
    }
    fieldgroups
    {
        fieldgroup(Dropdown; "Routing Code", "Bank Name", "Bank No.") { }
    }
}
