report 50077 "Capex Report"
{
    DefaultLayout = RDLC;
    RDLCLayout = './CHIReports\Reports\Layout\CapexReport.rdl';
    Caption = 'Capex Report_50077';
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;

    dataset
    {
        dataitem("Budget Line"; "Budget Line")
        {
            DataItemTableView = SORTING("Document No.", "FA No.", "Line No.");
            RequestFilterFields = "Document No.", "FA No.", "Budget Name";
            column(FORMAT_TODAY_0_4_; FORMAT(TODAY, 0, 4))
            {
            }
            column(COMPANYNAME; COMPANYNAME)
            {
            }
            /*column(CurrReport_PAGENO; CurrReport.PAGENO)
            {
            }*/
            column(USERID; USERID)
            {
            }
            column(Budget_Line__Budget_Name_; "Budget Name")
            {
            }
            column(Budget_Line__Document_No__; "Document No.")
            {
            }
            column(Budget_Line__Line_No__; "Line No.")
            {
            }
            column(Budget_Line__FA_No__; "FA No.")
            {
            }
            column(Budget_Line__Shortcut_Dimension_1_Code_; "Shortcut Dimension 1 Code")
            {
            }
            column(Budget_Line__Shortcut_Dimension_2_Code_; "Shortcut Dimension 2 Code")
            {
            }
            column(Budget_Line__Amount_LCY__; "Amount(LCY)")
            {
            }
            column(FATotPoRlsAmt; FATotPoRlsAmt)
            {
            }
            column(FATotalAdvPd; FATotalAdvPd)
            {
            }
            column(FATotBudgUtilize; FATotBudgUtilize)
            {
            }
            column(FATotPoRlsAmt_FATotBudgUtilize; FATotPoRlsAmt - FATotBudgUtilize)
            {
            }
            column(Budget_LineCaption; Budget_LineCaptionLbl)
            {
            }
            column(CurrReport_PAGENOCaption; CurrReport_PAGENOCaptionLbl)
            {
            }
            column(Budget_Line__Budget_Name_Caption; FIELDCAPTION("Budget Name"))
            {
            }
            column(Budget_Line__Document_No__Caption; FIELDCAPTION("Document No."))
            {
            }
            column(Budget_Line__Line_No__Caption; FIELDCAPTION("Line No."))
            {
            }
            column(Budget_Line__FA_No__Caption; FIELDCAPTION("FA No."))
            {
            }
            column(Budget_Line__Shortcut_Dimension_1_Code_Caption; FIELDCAPTION("Shortcut Dimension 1 Code"))
            {
            }
            column(Budget_Line__Shortcut_Dimension_2_Code_Caption; FIELDCAPTION("Shortcut Dimension 2 Code"))
            {
            }
            column(Amount__LCY_Caption; Amount__LCY_CaptionLbl)
            {
            }
            column(FATotalAdvPdCaption; FATotalAdvPdCaptionLbl)
            {
            }
            column(FATotBudgUtilizeCaption; FATotBudgUtilizeCaptionLbl)
            {
            }
            column(FATotPoRlsAmt_FATotBudgUtilizeCaption; FATotPoRlsAmt_FATotBudgUtilizeCaptionLbl)
            {
            }
            column(FATotPoRlsAmtCaption; FATotPoRlsAmtCaptionLbl)
            {
            }
            column(Budget_Line_Document_Type; "Document Type")
            {
            }
            column(WrongPLn; WrongPLn)
            {

            }
            column(showdetails; showdetails)
            {

            }
            dataitem("Purchase Line"; "Purchase Line")
            {
                DataItemLink = "No." = FIELD("FA No."),
                               "Capex No." = FIELD("Document No."),
                               "Capex Line No." = FIELD("Line No.");
                DataItemTableView = SORTING("Document Type", "Document No.", "Line No.");
                column(Purchase_Line__Document_No__; "Document No.")
                {
                }
                column(Purchase_Line__Capex_No__; "Capex No.")
                {
                }
                column(Purchase_Line__Capex_Line_No__; "Capex Line No.")
                {
                }
                column(Purchase_Line__No__; "No.")
                {
                }
                column(Purchase_Line__Amount_Including_VAT_; "Amount Including VAT")
                {
                }
                column(PlineAdvPd; PlineAdvPd)
                {
                }
                column(TotPLinePd; TotPLinePd)
                {
                }
                column(Amount_Including_VAT__TotPLinePd; "Amount Including VAT" - TotPLinePd)
                {
                }
                column(Purchase_Line_Document_Type; "Document Type")
                {
                }
                column(Purchase_Line_Line_No_; "Line No.")
                {
                }

                trigger OnAfterGetRecord();
                begin
                    PlineAdvPd := 0;
                    TotPLinePd := 0;
                    CLEAR(PAmtLCY);
                    CLEAR(PAmtLCY1);
                    CLEAR(PAmtLCY2);


                    PAmtLCY := "Unit Cost (LCY)" * Quantity;

                    PurchInvHeader.RESET;
                    PurchInvHeader.SETRANGE(PurchInvHeader."Prepayment Order No.", "Document No.");
                    IF PurchInvHeader.FINDSET THEN BEGIN
                        REPEAT
                            PurchInvLine.RESET;
                            PurchInvLine.SETRANGE(PurchInvLine."Document No.", PurchInvHeader."No.");
                            PurchInvLine.SETRANGE(PurchInvLine."Line No.", "Line No.");
                            IF PurchInvLine.FINDFIRST THEN BEGIN
                                PlineAdvPd += PurchInvLine."Amount Including VAT";
                                PAmtLCY1 += (PurchInvLine."Unit Cost (LCY)" * PurchInvLine.Quantity);
                            END;
                        UNTIL PurchInvHeader.NEXT = 0;
                    END;

                    PurchInvHeader.RESET;
                    PurchInvHeader.SETRANGE(PurchInvHeader."Order No.", "Document No.");
                    //PurchInvHeader.SETRANGE(PurchInvHeader."Prepayment Order No.",'');
                    IF PurchInvHeader.FINDSET THEN BEGIN
                        REPEAT
                            PurchInvLine.RESET;
                            PurchInvLine.SETRANGE(PurchInvLine."Document No.", PurchInvHeader."No.");
                            PurchInvLine.SETRANGE(PurchInvLine."Line No.", "Line No.");
                            IF PurchInvLine.FINDFIRST THEN BEGIN
                                TotPLinePd += PurchInvLine."Amount Including VAT";
                                PAmtLCY2 += (PurchInvLine."Unit Cost (LCY)" * PurchInvLine.Quantity);
                            END;
                        UNTIL PurchInvHeader.NEXT = 0;
                    END;
                    IF NOT showdetails THEN
                        CurrReport.Skip();

                    IF WrongPLn THEN
                        CurrReport.Skip();

                    IF PrintExcel THEN BEGIN
                        TempExcelBuffer.NewRow();
                        //B2BMSOn20Sep21>>
                        TempExcelBuffer.AddColumn(FORMAT("Budget Name"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        //B2BMSOn20Sep21<<
                        TempExcelBuffer.AddColumn(FORMAT("Capex No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Capex Line No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(fa.Description), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Document No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Purchase Line".Description), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 1 Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 2 Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(Amount), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(PlineAdvPd), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(TotPLinePd), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(Amount - TotPLinePd), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Currency Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(PAmtLCY), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(PAmtLCY1), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(PAmtLCY2), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    END;
                end;
            }
            dataitem(PurchaseLine2; "Purchase Line")
            {
                DataItemLink = "No." = FIELD("FA No."),
                               "Capex No." = FIELD("Document No.");
                DataItemTableView = SORTING("Document Type", "Document No.", "Line No.");
                column(PurchaseLine2__Document_No__; "Document No.")
                {
                }
                column(PurchaseLine2__Capex_No__; "Capex No.")
                {
                }
                column(PurchaseLine2__Capex_Line_No__; "Capex Line No.")
                {
                }
                column(PurchaseLine2__No__; "No.")
                {
                }
                column(PurchaseLine2__Amount_Including_VAT_; "Amount Including VAT")
                {
                }
                column(PlineAdvPde; PlineAdvPde)
                {
                }
                column(TotPLinePde; TotPLinePde)
                {
                }
                column(Amount_Including_VAT__TotPLinePde; "Amount Including VAT" - TotPLinePde)
                {
                }
                column(PurchaseLine2_Document_Type; "Document Type")
                {
                }
                column(PurchaseLine2_Line_No_; "Line No.")
                {
                }

                trigger OnAfterGetRecord();
                begin
                    PlineAdvPde := 0;
                    TotPLinePde := 0;
                    BudgetLine.RESET;
                    BudgetLine.SETRANGE("FA No.", "No.");
                    BudgetLine.SETRANGE("Document No.", "Capex No.");
                    BudgetLine.SETRANGE("Line No.", "Capex Line No.");
                    IF BudgetLine.FINDFIRST THEN
                        CurrReport.SKIP;

                    PurchInvHeader.RESET;
                    PurchInvHeader.SETRANGE(PurchInvHeader."Prepayment Order No.", "Document No.");
                    IF PurchInvHeader.FINDSET THEN BEGIN
                        REPEAT
                            PurchInvLine.RESET;
                            PurchInvLine.SETRANGE(PurchInvLine."Document No.", PurchInvHeader."No.");
                            PurchInvLine.SETRANGE(PurchInvLine."Line No.", "Line No.");
                            IF PurchInvLine.FINDFIRST THEN
                                PlineAdvPde += PurchInvLine."Amount Including VAT";
                        UNTIL PurchInvHeader.NEXT = 0;
                    END;

                    PurchInvHeader.RESET;
                    PurchInvHeader.SETRANGE(PurchInvHeader."Order No.", "Document No.");
                    //PurchInvHeader.SETRANGE(PurchInvHeader."Prepayment Order No.",'');
                    IF PurchInvHeader.FINDSET THEN BEGIN
                        REPEAT
                            PurchInvLine.RESET;
                            PurchInvLine.SETRANGE(PurchInvLine."Document No.", PurchInvHeader."No.");
                            PurchInvLine.SETRANGE(PurchInvLine."Line No.", "Line No.");
                            IF PurchInvLine.FINDFIRST THEN
                                TotPLinePde += PurchInvLine."Amount Including VAT";
                        UNTIL PurchInvHeader.NEXT = 0;
                    END;


                    //PurchaseLine2, Body(1) - OnPreSection()

                    IF NOT WrongPLn THEN
                        CurrReport.Skip();
                    IF PrintExcel THEN BEGIN
                        TempExcelBuffer.NewRow();
                        //B2BMSOn20Sep21>>
                        TempExcelBuffer.AddColumn(FORMAT("Budget Name"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        //B2BMSOn20Sep21<<
                        TempExcelBuffer.AddColumn(FORMAT("Capex No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Capex Line No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(fa.Description), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Document No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Purchase Line".Description), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 1 Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 2 Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(Amount), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(PlineAdvPde), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(TotPLinePde), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT(Amount - TotPLinePde), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                        TempExcelBuffer.AddColumn(FORMAT("Currency Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    end;
                end;
            }

            trigger OnAfterGetRecord();
            var
                AmountLCY: Decimal;
                CurrencyExchangeRate: Record "Currency Exchange Rate";
                PurchHeader: Record "Purchase Header";
            begin
                CLEAR(FATotPoRlsAmt);
                CLEAR(FATotalAdvPd);
                CLEAR(FATotBudgUtilize);
                CLEAR(AmtLCY);
                CLEAR(AmtLCY1);
                CLEAR(AmtLCY2);

                BudgetHeader.SETRANGE(BudgetHeader."No.", "Document No.");
                IF BudgetHeader.FINDFIRST THEN;

                IF fa.GET("FA No.") THEN;



                // Calculate Amount Release
                PurchOrdLine.RESET;
                //PurchOrdLine.SETRANGE("No.", "FA No.");
                PurchOrdLine.SETRANGE("Capex No.", "Document No.");
                PurchOrdLine.SETRANGE("Capex Line No.", "Line No.");
                IF PurchOrdLine.FINDSET THEN BEGIN
                    IF PurchOrdLine."Currency Code" = '' THEN
                        REPEAT
                            //PurchaseHeader.RESET;
                            //PurchaseHeader.SETRANGE(PurchaseHeader."No.",PurchOrdLine."Document No.");
                            //PurchaseHeader.SETRANGE(PurchaseHeader.Status,PurchaseHeader.Status::Released);
                            //IF PurchaseHeader.FINDFIRST THEN
                            //B2B Start
                            PurchHeader.Get(PurchOrdLine."Document Type", PurchOrdLine."Document No.");
                            AmountLCY := CurrencyExchangeRate.ExchangeAmtFCYToLCY(PurchHeader."Document Date", PurchHeader."Currency Code", PurchOrdLine.Amount, PurchHeader."Currency Factor");
                            IF AmountLCY = 0 THEN
                                FATotPoRlsAmt += PurchOrdLine."Amount Including VAT"
                            ELSE
                                FATotPoRlsAmt += AmountLCY;
                            //B2B End    
                            AmtLCY += (PurchOrdLine."Unit Cost (LCY)" * PurchOrdLine.Quantity);

                            PurchInvHeader.RESET;
                            PurchInvHeader.SETRANGE("Prepayment Order No.", PurchOrdLine."Document No.");
                            IF PurchInvHeader.FINDSET THEN BEGIN
                                REPEAT
                                    PurchInvLine.RESET;
                                    PurchInvLine.SETRANGE("Document No.", PurchInvHeader."No.");
                                    PurchInvLine.SETRANGE("Line No.", PurchOrdLine."Line No.");
                                    IF PurchInvLine.FINDFIRST THEN BEGIN
                                        FATotalAdvPd += PurchInvLine."Amount Including VAT";
                                        AmtLCY1 += (PurchInvLine."Unit Cost (LCY)" * PurchInvLine.Quantity);
                                    END;
                                UNTIL PurchInvHeader.NEXT = 0;
                            END;


                            PurchInvHeader.RESET;
                            PurchInvHeader.SETRANGE(PurchInvHeader."Order No.", PurchOrdLine."Document No.");
                            //PurchInvHeader.SETRANGE(PurchInvHeader."Prepayment Order No.",'');
                            IF PurchInvHeader.FINDSET THEN BEGIN
                                REPEAT
                                    PurchInvLine.RESET;
                                    PurchInvLine.SETRANGE(PurchInvLine."Document No.", PurchInvHeader."No.");
                                    PurchInvLine.SETRANGE(PurchInvLine."Line No.", PurchOrdLine."Line No.");
                                    IF PurchInvLine.FINDFIRST THEN BEGIN
                                        FATotBudgUtilize += PurchInvLine."Amount Including VAT";
                                        AmtLCY2 += (PurchInvLine."Unit Cost (LCY)" * PurchInvLine.Quantity);
                                    END;
                                UNTIL PurchInvHeader.NEXT = 0;
                            END;
                        UNTIL PurchOrdLine.NEXT = 0
                END;


                PurchInvLine.RESET;
                PurchInvLine.SETRANGE(PurchInvLine."Capex No.", "Budget Line"."Document No.");
                PurchInvLine.SETRANGE(PurchInvLine."Capex Line No.", "Line No.");
                IF PurchInvLine.FINDFIRST THEN BEGIN
                    FATotBudgUtilize += PurchInvLine."Amount Including VAT";
                    AmtLCY2 += (PurchInvLine."Unit Cost (LCY)" * PurchInvLine.Quantity);
                END;


                IF WrongPLn THEN
                    CurrReport.Skip();

                IF PrintExcel THEN BEGIN
                    /*
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT("Budget Name"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 2, FORMAT("Document No."), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 3, FORMAT("Line No."), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT("FA No."), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 5, FORMAT(fa.Description), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT("Shortcut Dimension 1 Code"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 9, FORMAT("Shortcut Dimension 2 Code"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 10, FORMAT("Amount(LCY)"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 11, FORMAT(FATotPoRlsAmt), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 12, FORMAT(FATotalAdvPd), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 13, FORMAT(FATotBudgUtilize), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 14, FORMAT(FATotPoRlsAmt - FATotBudgUtilize), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 15, FORMAT("Currency Code"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 16, FORMAT(BudgetHeader.Status), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 17, FORMAT(AmtLCY), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 18, FORMAT(AmtLCY1), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 19, FORMAT(AmtLCY2), TRUE, FALSE, FALSE);
                    */
                    TempExcelBuffer.NewRow;
                    TempExcelBuffer.AddColumn(FORMAT("Budget Name"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Document No."), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Line No."), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("FA No."), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(fa.Description), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 1 Code"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 2 Code"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Amount(LCY)"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(FATotPoRlsAmt), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(FATotalAdvPd), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(FATotBudgUtilize), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(FATotPoRlsAmt - FATotBudgUtilize), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Currency Code"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(BudgetHeader.Status), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(AmtLCY), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(AmtLCY1), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(AmtLCY2), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                END;
            end;

            trigger OnPostDataItem();
            begin
                /*
                IF PrintExcel THEN BEGIN
                         TempExcelBuffer.CreateBook;
                        TempExcelBuffer.CreateSheet('Capex Report', 'Capex Report', COMPANYNAME, USERID);
                        TempExcelBuffer.GiveUserControl; 
                    TempExcelBuffer.CreateNewBook('Capex Report');
                    TempExcelBuffer.WriteSheet('Capex Report', CompanyName, UserId);
                    TempExcelBuffer.CloseBook();
                    TempExcelBuffer.OpenExcel();
                END; 
                */
            end;

            trigger OnPreDataItem();
            begin
                LastFieldNo := FIELDNO("FA No.");

                IF PrintExcel THEN BEGIN
                    TempExcelBuffer.DELETEALL;
                    CLEAR(TempExcelBuffer);
                    /*
                    RowNo := 1;
                    EnterCell(RowNo, 1, 'Capex Report', TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(UPPERCASE(COMPANYNAME)), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 15, FORMAT(USERID), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(Mfilter), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, 'Budget Name', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 2, 'Capex No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 3, 'Capex Line No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, 'FA No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 5, 'FA Name', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 6, 'PO No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 7, 'PO Description', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, 'Accounting Location', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 9, 'CC Code', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, 'Capex Amount', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 11, 'Total Po Amount', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 12, 'Total Advance Paid', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 13, 'Total Amount Paid', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 14, 'Outstanding Po Balance', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 15, 'Currency Code', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 16, 'Capex Status', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 17, 'PO Amount LCY', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 18, 'Adv Paid Amount LCY', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 19, 'Total Amount Paid LCY', TRUE, FALSE, FALSE);
                    */
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn('Capex Report', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn(FORMAT(UPPERCASE(COMPANYNAME)), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(USERID), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn(FORMAT(Mfilter), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn('Budget Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex Line No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('FA No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('FA Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('PO No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('PO Description', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Accounting Location', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('CC Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex Amount', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Total Po Amount', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Total Advance Paid', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Total Amount Paid', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Outstanding Po Balance', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Currency Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex Status', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('PO Amount LCY', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Adv Paid Amount LCY', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Total Amount Paid LCY', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
            area(content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(showdetails; showdetails)
                    {
                        ApplicationArea = all;
                        Caption = 'Show Details';
                    }
                    field(WrongPLn; WrongPLn)
                    {
                        ApplicationArea = all;
                        Caption = 'Wrong Capex Line No.';
                        Visible = false;
                    }
                    field(PrintExcel; PrintExcel)
                    {
                        ApplicationArea = all;
                        Caption = 'Copy to Excel';
                    }
                }
            }
        }

        actions
        {
        }
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        Mfilter := "Budget Line".GETFILTERS;
    end;

    trigger OnPostReport()
    begin
        IF PrintExcel THEN BEGIN
            TempExcelBuffer.CreateNewBook('Capex Report');
            TempExcelBuffer.WriteSheet('Capex Report', CompanyName, UserId);
            TempExcelBuffer.CloseBook();
            TempExcelBuffer.OpenExcel();
        END;
    end;

    var
        LastFieldNo: Integer;
        FooterPrinted: Boolean;
        TotalFor: Label 'Total for';
        FATotPoRlsAmt: Decimal;
        FATotalAdvPd: Decimal;
        FATotBudgUtilize: Decimal;
        PurchaseHeader: Record "Purchase Header";
        PurchInvHeader: Record "Purch. Inv. Header";
        PurchInvLine: Record "Purch. Inv. Line";
        PurchOrdLine: Record "Purchase Line";
        showdetails: Boolean;
        PrintExcel: Boolean;
        RowNo: Integer;
        ColumnNo: Integer;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        Mfilter: Text[250];
        PlineAdvPd: Decimal;
        TotPLinePd: Decimal;
        BudgetHeader: Record "Budget Header";
        fa: Record "Fixed Asset";
        PlineAdvPde: Decimal;
        TotPLinePde: Decimal;
        BudgetLine: Record "Budget Line";
        WrongPLn: Boolean;
        AmtLCY: Decimal;
        AmtLCY1: Decimal;
        AmtLCY2: Decimal;
        PAmtLCY: Decimal;
        PAmtLCY1: Decimal;
        PAmtLCY2: Decimal;
        Budget_LineCaptionLbl: Label 'Budget Line';
        CurrReport_PAGENOCaptionLbl: Label 'Page';
        Amount__LCY_CaptionLbl: Label 'Amount (LCY)';
        FATotalAdvPdCaptionLbl: Label 'Total Adv Pd';
        FATotBudgUtilizeCaptionLbl: Label 'Total Amount Paid';
        FATotPoRlsAmt_FATotBudgUtilizeCaptionLbl: Label 'Outstanding PO balance';
        FATotPoRlsAmtCaptionLbl: Label 'Total Released PO Amount (LCY)';

    procedure EnterCell(RowNo: Integer; ColumnNo: Integer; CellValue: Text[500]; Bold: Boolean; Italic: Boolean; Underline: Boolean);
    begin
        TempExcelBuffer.INIT;
        TempExcelBuffer.VALIDATE("Row No.", RowNo);
        TempExcelBuffer.VALIDATE("Column No.", ColumnNo);
        TempExcelBuffer."Cell Value as Text" := CellValue;
        TempExcelBuffer.Formula := '';
        TempExcelBuffer.Bold := Bold;
        TempExcelBuffer.Italic := Italic;
        TempExcelBuffer.Underline := Underline;
        TempExcelBuffer.INSERT;
    end;
}

