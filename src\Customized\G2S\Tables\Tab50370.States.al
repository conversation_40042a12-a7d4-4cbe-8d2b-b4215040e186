table 50370 States
{
    Caption = 'States';
    DataClassification = ToBeClassified;
    Permissions = tabledata 50370 = RIMD;

    fields
    {
        field(1; State; Code[50])
        {
            Caption = 'State';
        }
        field(2; "Capital"; Code[50])
        {
            Caption = 'Capital';
        }
    }

    keys
    {
        key(PK; State)
        {
            Clustered = true;
        }
    }

    fieldgroups
    {
        fieldgroup(DropDown; State, Capital)
        {

        }
        fieldgroup(Brick; State, Capital)
        {

        }
    }
}
