tableextension 50047 TransferShipmntHdr extends "Transfer Shipment Header"
{
    fields
    {
        field(50001; "Transfer Type"; Enum "Transfer Request Type")
        {
            DataClassification = CustomerContent;
        }

        field(50002; "Production Batch No."; Code[20])
        {
            Caption = 'Production Batch No.';
            Description = 'HO1.0';
            DataClassification = CustomerContent;
        }

        field(50003; "MRS Category Code"; Code[20])
        {
            Description = 'Auto_Prod_MRS';
            Editable = false;
            TableRelation = "Item Category";
            DataClassification = CustomerContent;
        }
        field(50008; "Production Order No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50009; "Branch Request No"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "Maintenance Job Card No"; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50011; "Manual MRS No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
    }
}
