page 50864 "Retail Stock Update List"
{
    ApplicationArea = All;
    Caption = 'Retail Stock Update List';
    PageType = List;
    SourceTable = "Retail Stock update";
    UsageCategory = Lists;

    layout
    {
        area(content)
        {
            repeater(Group)
            {
                field("Entry No."; "Entry No.")
                {
                }

                field("Product ID"; "Product ID")
                {
                }

                field("Quantity"; "Quantity")
                {
                }

                field("Location code"; "Location code")
                {

                }

                field("Price"; "Price")
                {
                }

                field("Expiry Date"; "Expiry Date")
                {
                }

                field("Batch Number"; "Batch Number")
                {
                }

                field("Date Created"; "Date Created")
                {
                }
                field("Item No."; "Item No.")
                {

                }

                field("Item description"; "item description")
                {

                }

                field("Ledger Entry No."; "Ledger Entry No.")
                {

                }
                field(PushedtoAPI; PushedtoAPI)
                {

                }
                field("Response message"; "Response message")
                {

                }
                field(Outlet; Outlet)
                {
                    Caption = 'Outlet Location';
                }




            }
        }
    }

    actions
    {
        area(processing)
        {
            action(PostData)
            {
                Caption = 'Post Data';
                ApplicationArea = All;
                Image = Export;

                trigger OnAction()
                var
                //PostDataCodeunit: Codeunit "Post Data to Endpoint";
                begin
                    // PostDataCodeunit.PostDataToEndpoint();
                end;
            }
        }
    }
}
