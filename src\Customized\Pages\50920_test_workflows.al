page 50920 TestWOrkFLowEvents
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = 1520;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Table ID"; "Table ID")
                {
                    ApplicationArea = All;

                }
                field("Function Name"; "Function Name")
                {
                    ApplicationArea = All;

                }
                field(Description; Description)
                {
                    ApplicationArea = All;

                }
            }
        }
        area(Factboxes)
        {

        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction();
                begin

                end;
            }
        }
    }
}