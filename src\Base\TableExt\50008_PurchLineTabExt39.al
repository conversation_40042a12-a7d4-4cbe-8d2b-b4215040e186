//RFC7CAPEXGo2SolveJuly2023
/// <summary>
/// TableExtension PurchLineTabExt39 (ID 50008) extends Record Purchase Line.
/// </summary>
tableextension 50008 PurchLineTabExt39 extends "Purchase Line"
{
    fields
    {
        modify("No.")
        {
            trigger OnAfterValidate()
            var
                FixedAsset: Record "Fixed Asset";
                ItemLv: Record Item;
            begin
                if Type = Type::"Fixed Asset" then
                    if FixedAsset.Get("No.") then begin
                        FixedAsset.TestField("Approval Status", FixedAsset."Approval Status"::Released);
                        FixedAsset.TestField(Blocked, false);
                    end;
                if Type = Type::Item then
                    if ItemLv.Get("No.") then
                        "No.2" := ItemLv."No. 2";
            end;
        }
        modify("Location Code")
        {
            trigger OnAfterValidate()
            var
                LocationLrec: Record Location;
            begin
                //Balu 05122021 >>
                if LocationLrec.Get("Location Code") then
                    LocationLrec.TestField(Blocked, false);
                //Balu 05122021<<
            end;
        }
        modify(Quantity)
        {
            trigger OnAfterValidate()
            var
                purchHdr: Record "Purchase Header";
            begin
                /*purchHdr.Reset();
                purchHdr.SetRange("Document Type", "Document Type");
                purchHdr.SetRange("No.", "Document No.");
                if purchHdr.FindFirst() THEN BEGIN 
                    IF ("Sub Document Type" = '') OR () OR () THEN 
                    error();
                END;*/
                purchHdr.Get("Document Type", "Document No.");
                "Import File No." := purchHdr."Import File No.";
                "Clearing File No." := purchHdr."Clearing File No.";//FIX30jun2021
                if "Document Type" = "Document Type"::Order then begin
                    IF "Order Status" <> "Order Status"::"Short Closed" THEN BEGIN //PKONDE7
                        TestField("Sub Document Type", "Sub Document Type"::" ");
                        TestField("Sub Document No.", '');
                    end;//PKONDE7
                end;

                //>>>>>>>> G2S 07042023  RFC#11
                IF (Type = Type::"Fixed Asset") AND ("Capex No." <> '') THEN
                    "Capex Line No." := 0;
                //Message(Text004);
                //<<<<<<<< G2S 07042023  RFC#11
            end;
        }
        field(50000; "Order Status"; enum PurcLineOrdrStatus)
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Contract Start Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "End Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Min Qty"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Sub Document Type"; Enum SubDocumentType)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Sub Document No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Sub Document Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }

        field(50009; "PMS No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            trigger OnValidate()
            begin
                CheckValues();
            end;

        }
        field(50010; "Last Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Current Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                PmsCard: record PMSManagement;
            begin
                PmsCard.Reset();
                PmsCard.SetRange("No.", "PMS No.");
                PmsCard.SetFilter("Last Meter Reading", '<>%1', 0);
                if PmsCard.FindFirst() then begin
                    TESTFIELD("Last Meter Reading");
                    TESTFIELD("Date PMS Availed");
                    IF ("Current Meter Reading" <= "Last Meter Reading") THEN
                        ERROR(Text50207, "Current Meter Reading", "Last Meter Reading");
                end;
                IF "Last Meter Reading" <> 0 THEN
                    IF ("Current Meter Reading" <= "Last Meter Reading") THEN
                        ERROR(Text50207, "Current Meter Reading", "Last Meter Reading");
                "Km Covered" := "Current Meter Reading" - "Last Meter Reading";
            end;
        }
        field(50012; "Km Covered"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "PMS Unit Price"; Decimal)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                Validate("Direct Unit Cost", "PMS Unit Price");
            end;
        }
        field(50017; "Km per Ltr"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Fuel Avail"; Decimal)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                PurchSetup.GET;

                IF "Fuel Avail" <= 0 THEN
                    ERROR('');

                IF "Fuel Avail" > PurchSetup."Max Fuel Availed" THEN BEGIN
                    IF UserSetup.GET(USERID) THEN
                        ERROR(Text50222, PurchSetup."Max Fuel Availed");
                END;
                TESTFIELD("Last Meter Reading");
                TESTFIELD("Current Meter Reading");
                "Km per Ltr" := ROUND("Km Covered" / "Fuel Avail", 0.00001);
                VALIDATE(Quantity, "Fuel Avail");
                VALIDATE("Qty. to Receive", "Fuel Avail");
            end;
        }
        field(50015; "PMS Receipt No."; Code[20])
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                CheckValues();
            end;
        }
        field(50016; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50019; "Date PMS Availed"; Date)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                CheckValues();
            end;
        }
        field(50020; "QTY. Excluding Tolerance"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50025; "New Vendor No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor;
        }
        field(50035; Select; Boolean)
        {
            DataClassification = CustomerContent;
        }
        Field(50036; "Approved PO Created"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }

        field(50026; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Header"."No." WHERE(Status = CONST(Released), "Document Type" = CONST(Capex));
            //Editable = false;
            trigger Onvalidate()
            var
                CapBudLine: Record "Budget Line";
                CapBudHdr: Record "Budget Header";
                CapYear: Integer;
                PurchaseHdr: Record "Purchase Header";
            begin
                //Feb162021>>
                // CapBudLine.RESEt;
                // CapBudLine.SetRange("Document No.", "Capex No.");
                // IF CapBudLine.findfirst then
                //     "Budget Name" := CapBudLine."Budget Name";
                //Feb162021<<
                //>>>>G2S 7/2/24 CAS-01398-B9R3L8 
                PurchaseHdr.Reset();
                PurchaseHdr.SetRange("No.", "Document No.");
                IF PurchaseHdr.FindFirst() THEN BEGIN
                    CapBudHdr.Reset();
                    CapBudHdr.SetCurrentKey("No.", Status);
                    CapBudHdr.SetRange("No.", Rec."Capex No.");
                    CapBudHdr.SetFilter(Status, 'Released');
                    If CapBudHdr.FindFirst() THEN BEGIN
                        CapYear := Date2DMY(CapBudHdr."Document Date", 3);
                        IF CapYear <> Date2DMY(PurchaseHdr."Document Date", 3) Then
                            Error('Capex selected %1 is of previous Year %2', Rec."Capex No.", CapYear);
                        "Budget Name" := CapBudHdr."Budget Name";
                    END;
                    IF "Sub Document No." <> '' then
                        error('You can not modify this field, it is created for Document type %1 and Document No %2', "Sub Document Type", "Sub Document No.");
                END;
                //>>>>G2S 7/2/24 CAS-01398-B9R3L8 
            end;
        }
        field(50027; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Line"."Line No." WHERE("Document No." = FIELD("Capex No."));
            //Editable = false;
            trigger Onvalidate()
            // <<<<<< G2S 290623  RFC#11
            begin
                IF "Sub Document No." <> '' then
                    error('You can not modify this field, it is created for Document type %1 and Document No %2', "Sub Document Type", "Sub Document No.");
                // >>>>>> G2S 290623 RFC#11
                CheckCapexBudget()
                // <<<<<< G2S 290623 RFC#11
            end;
        }
        // <<<<<< G2S 070224 Capex_Budget_Control 
        modify("Direct Unit Cost")
        {
            trigger OnAfterValidate()
            begin
                if (Type = Rec.Type::"Fixed Asset") and (Rec."FA Posting Type" = Rec."FA Posting Type"::"Acquisition Cost") then begin
                    Rec.TestField("Capex Line No.");
                    CheckCapexBudget()
                end;
            end;
        }
        // <<<<<< G2S 070224 Capex_Budget_Control 
        field(50028; "Budget Name"; code[20])
        {
            DataClassification = CustomerContent;
        }

        field(50029; "Under Ord FORM-M Opened-No Lc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Under Order No LC"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Under Ship LC Opened-Aw Desp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Under Ship LC Opened-g Desp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50038; Status; enum SubConStatus)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50039; "Deliver Comp. For"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50040; "Qty. to Reject (Rework)"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50041; "Delivery Challan Date"; Date)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50042; "Qty. Rejected (Rework)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50043; "Qty. to Reject (V.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50044; "Qty. Rejected (V.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50045; "Delivery Challan Posted"; Integer)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50046; "Qty. to Reject (C.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50047; "Qty. Rejected (C.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50048; "Vendor Shipment No."; Code[20])
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50049; "Posting Date"; Date)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50050; Subcontracting; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50051; SubConSend; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50052; SubConReceive; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50053; "Released Production Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Production Order"."No." WHERE(Status = CONST(Finished), "No." = FIELD("Prod. Order No."));
            Description = 'SUBCON1.0';
        }
        field(50055; "FA Posting Group"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "FA Posting Group";
            Editable = false;
            Description = 'FA Posting Group';
        }
        field(50056; "Tariff No."; Code[20])
        {
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Tariff No." WHERE("No." = FIELD("No.")));
            Editable = false;
        }
        field(50057; "Material req No.s"; Code[250])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50060; "Under Shpmnt LC Opnd-Goods Dsp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50061; "Under Order LC Opened Aw. Desp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50062; "Under Order FORM-M Opnd -No LC"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        /*field(50075; "Capex No."; code[20])
        {
            DataClassification = CustomerContent;

        }*/
        field(50063; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "CWIP Masters"."CWIP No." where(Status = const(Release), "Capitalized To FA" = const(FALSE));
            trigger onvalidate()
            var
                CWIPMaster: record "CWIP Masters";
                Valusel: Text[20];
            begin
                Type := Type::"G/L Account";
                //"CWIP No." := "CWIP No.";
                Valusel := "CWIP No.";
                //Message('%1....%2', "CWIP No.", Valusel);                
                IF CWIPMaster.Get("CWIP No.") then BEGIN
                    Validate("No.", CWIPMaster."GL Account No.");
                    //"CWIP No." := CWIPMaster."CWIP No.";
                    Description := CWIPMaster."CWIP Name";
                end;
                "CWIP No." := Valusel;
                //Message('%1....%2', "CWIP No.", Valusel);                
                //Message('%1  %2  %3', CWIPMaster."CWIP No.", CWIPMaster."GL Account No.", CWIPMaster."CWIP Name");
            end;
        }

        field(50070; "WHT Applicable"; Boolean)
        {
            DataClassification = CustomerContent;
            trigger Onvalidate()
            begin
                TestField("WHT Group");//B2BPK270521
            end;
        }
        field(50801; "HS Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50802; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50803; "Clearing No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50804; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50805; "No.2"; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50806; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;//B2BPK270521
            trigger OnValidate()
            var
                WHTSetUp: Record WHTSetUp;
            begin
                Clear("WHT %");
                if WHTSetUp.Get("WHT Group") then
                    if WHTSetUp.Percentage = 0 then
                        WHTSetUp.TestField(Percentage)
                    else
                        "WHT %" := WHTSetUp.Percentage;
                if "WHT Group" <> '' then
                    "WHT Amount" := Amount
                else
                    "WHT Amount" := 0;
                "WHT Amount 2" := "WHT Amount" * ("WHT %" / 100);
            end;
        }
        field(50807; "WHT %"; Decimal)
        {
            DataClassification = CustomerContent;//B2BPK270521

        }
        //FIX05Jun2021>>
        field(50808; "WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'WHT Base Amount';
            trigger OnValidate()
            begin
                "WHT Amount 2" := "WHT Amount" * ("WHT %" / 100);
            end;
        }
        field(50811; "WHT Amount 2"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'WHT Amount';
            Editable = false;

        }
        //FIX05Jun2021<<
        //Service08Jul2021>>
        field(50071; "Service Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Service Vendor Rate"."Service Code" where("Vendor Code" = field("Buy-from Vendor No."), Released = const(true));
            trigger Onvalidate()
            var
                SerVendRate: Record "Service Vendor Rate";
            begin
                TestStatusOpen();
                TestField(Type, Type::"G/L Account");
                IF SerVendRate.GET("Service Code", "Buy-from Vendor No.") THEN
                    if (SerVendRate."Start Date" < "Order Date") and (SerVendRate."End Date" > "Order Date") then
                        VALIDATE("Direct Unit Cost", SerVendRate."Service Rate")
                    else
                        Error('Service is expired');
            end;
        }
        //Service08Jul2021<<
        field(50821; "Posted Loading Slip No."; code[20])//PKONJU19
        {
            DataClassification = CustomerContent;

        }
        modify(Amount)
        {
            trigger OnAfterValidate()
            begin
                if "WHT Applicable" then begin
                    "WHT Amount" := Amount;
                    "WHT Amount 2" := "WHT Amount" * ("WHT %" / 100);
                end;
            end;
        }

    }
    trigger OnAfterModify()
    var
        PurcaseHeaderLRec: record "Purchase Header";
    begin
        PurcaseHeaderLRec.Reset();
        PurcaseHeaderLRec.SetRange("Document Type", "Document Type");
        PurcaseHeaderLRec.SetRange("No.", "Document No.");
        PurcaseHeaderLRec.SetRange("Order Status", PurcaseHeaderLRec."Order Status"::"Short Closed");
        if PurcaseHeaderLRec.FindFirst() then
            Error('Order is Short Closed');
    end;

    trigger OnAfterInsert()
    var
        PurcaseHeaderLRec: record "Purchase Header";
    begin
        PurcaseHeaderLRec.Reset();
        PurcaseHeaderLRec.SetRange("Document Type", "Document Type");
        PurcaseHeaderLRec.SetRange("No.", "Document No.");
        PurcaseHeaderLRec.SetRange("Order Status", PurcaseHeaderLRec."Order Status"::"Short Closed");
        if PurcaseHeaderLRec.FindFirst() then
            Error('Order is Short Closed');
    end;


    var
        MaintenanceLedgerEntry: Record "Maintenance Ledger Entry";
        myInt: Integer;
        PurchSetup: Record "Purchases & Payables Setup";
        UserSetup: Record "User Setup";
        Text50222: label 'You do not have permission to exceed the maximum fuel availed of %1';
        Text50207: Label 'Current Meter Reading %1 cannot be less than Last Meter Reading %2';
        // >>>>>> G2S 290623  RFC#11
        Text004: Label 'Please select the capex line no again.';
        //amount greater than remaining bud amt. prompt error message
        Text003: Label 'Line Amount is greater than the budget balance. Kindly review the line amount value again';
        //budget fully utilized. prompt error message
        Text002: Label 'Budget for the Capex Line No.: %1 has been fully utilized.';
        //line amount yet to be populated. prompt error message
        Text001: Label 'Line amount value yet to be populated. Kindly populate necessary fields';
    // <<<<<< G2S 290623 end;  RFC#11

    /// <summary>
    /// ToleranceCalc.
    /// </summary>
    procedure ToleranceCalc()
    var
        ToleranceMsg: Label 'Do you want to include the tolerance?';
        PurchHeaderLRec: Record "Purchase Header";
    begin
        "QTY. Excluding Tolerance" := Quantity;
        PurchHeaderLRec.Reset();
        PurchHeaderLRec.SetRange("Document Type", "Document Type");
        PurchHeaderLRec.SetRange("No.", "Document No.");
        PurchHeaderLRec.SetRange("Purchase Tolerance", true);
        IF PurchHeaderLRec.FindFirst() then
            If Not Confirm(ToleranceMsg, False) then
                "QTY. Excluding Tolerance" := Quantity
            else begin
                "QTY. Excluding Tolerance" := Quantity;
                Validate(Quantity, Round((Quantity + (Quantity * (PurchHeaderLRec."Purchase Tol Percentage" / 100))), 1, '='));
            end;
    end;

    //Function created from "Capex Line No." <<<<<< G2S 070224 Capex_Budget_Control
    procedure CheckCapexBudget()
    // >>>>>> G2S 290623 RFC#11
    var
        BudLine: Record "Budget Line";
        RemainingBudAmt: Decimal;
    begin
        RemainingBudAmt := 0;
        BudLine.Reset();
        BudLine.SetRange("Document No.", "Capex No.");
        BudLine.SetRange("Line No.", "Capex Line No.");
        // if "Line Amount" <> 0 then begin
        if BudLine.FindFirst() then begin
            BudLine.CalcFields("Budget Utilized");
            RemainingBudAmt := BudLine."Amount(LCY)" - BudLine."Budget Utilized";
            if (RemainingBudAmt > 0) then begin
                //amount greater than remaining bud amt. prompt error message
                if not ("Line Amount" <= RemainingBudAmt) then begin
                    Error(Text003);
                end;
            end else
                //budget fully utilized. prompt error message
                Error(Text002, "Capex Line No.");
        end;
        // end else
        //line amount yet to be populated. prompt error message
        // Error(Text001);
    end;
    // >>>>>> G2S 290623 RFC#11

    procedure CheckValues()
    var
        PurchaseLineLRec: record "Purchase Line";
        PMSLedgerEntries: Record PMSCardLedger;
    begin
        PurchaseLineLRec.reset();
        PurchaseLineLRec.SetRange("PMS No.", "PMS No.");
        PurchaseLineLRec.setrange("Date PMS Availed", "Date PMS Availed");
        PurchaseLineLRec.SetRange("PMS Receipt No.", "PMS Receipt No.");
        PurchaseLineLRec.SetFilter("Document No.", '<>%1', "Document No.");
        if PurchaseLineLRec.FindFirst() then
            Error('Already Exisits for this %1 Record', PurchaseLineLRec."Document No.");
        //FIX25May2021>>
        PMSLedgerEntries.Reset();
        PMSLedgerEntries.SetRange("PMS No.", "PMS No.");
        PMSLedgerEntries.setrange("Date PMS Availed", "Date PMS Availed");
        PMSLedgerEntries.SetRange("Receipt No.", "PMS Receipt No.");
        if PMSLedgerEntries.FindFirst() then
            Error('Pms Already posted with Receipt no. %1, Date  PMS Avalied', "Receipt No.", "Date PMS Availed");
        //FIX25May2021<<
    end;

    procedure ShowItemChargeAssgnt2()
    var
        ItemChargeAssgntPurch: Record "Item Charge Assignment (Purch)";
        AssignItemChargePurch: Codeunit "Item Charge Assgnt. (Purch.)";
        ItemChargeAssgnts: Page "Item Charge Assig (purch) copy";
        ItemChargeAssgntLineAmt: Decimal;
        IsHandled: Boolean;
        ItemChargeAssignmentErr: Label 'You can only assign Item Charges for Line Types of Charge (Item).';
        Currency: Record Currency;
        PurchHeader: Record "Purchase Header";
    begin
        Get("Document Type", "Document No.", "Line No.");
        TestField("No.");
        TestField(Quantity);

        if Type <> Type::"Charge (Item)" then begin
            Message(ItemChargeAssignmentErr);
            exit;
        end;

        PurchHeader.Get("Document Type", "Document No.");
        if PurchHeader."Currency Code" = '' then
            Currency.InitRoundingPrecision
        else
            Currency.Get(PurchHeader."Currency Code");
        if ("Inv. Discount Amount" = 0) and
           ("Line Discount Amount" = 0) and
           (not PurchHeader."Prices Including VAT")
        then
            ItemChargeAssgntLineAmt := "Line Amount"
        else
            if PurchHeader."Prices Including VAT" then
                ItemChargeAssgntLineAmt :=
                  Round(CalcLineAmount / (1 + "VAT %" / 100), Currency."Amount Rounding Precision")
            else
                ItemChargeAssgntLineAmt := CalcLineAmount;

        ItemChargeAssgntPurch.Reset();
        ItemChargeAssgntPurch.SetRange("Document Type", "Document Type");
        ItemChargeAssgntPurch.SetRange("Document No.", "Document No.");
        ItemChargeAssgntPurch.SetRange("Document Line No.", "Line No.");
        ItemChargeAssgntPurch.SetRange("Item Charge No.", "No.");
        if not ItemChargeAssgntPurch.FindLast then begin
            ItemChargeAssgntPurch."Document Type" := "Document Type";
            ItemChargeAssgntPurch."Document No." := "Document No.";
            ItemChargeAssgntPurch."Document Line No." := "Line No.";
            ItemChargeAssgntPurch."Import File No." := "Import File No.";//ImportFile
            ItemChargeAssgntPurch."Item Charge No." := "No.";
            ItemChargeAssgntPurch."Unit Cost" :=
              Round(ItemChargeAssgntLineAmt / Quantity,
                Currency."Unit-Amount Rounding Precision");
        end;


        IsHandled := false;
        if not IsHandled then
            ItemChargeAssgntLineAmt :=
                Round(ItemChargeAssgntLineAmt * ("Qty. to Invoice" / Quantity), Currency."Amount Rounding Precision");

        if IsCreditDocType then
            AssignItemChargePurch.CreateDocChargeAssgnt(ItemChargeAssgntPurch, "Return Shipment No.")
        else
            AssignItemChargePurch.CreateDocChargeAssgnt(ItemChargeAssgntPurch, "Receipt No.");
        Clear(AssignItemChargePurch);
        Commit();

        ItemChargeAssgnts.Initialize(Rec, ItemChargeAssgntLineAmt);
        ItemChargeAssgnts.RunModal;

        CalcFields("Qty. to Assign");
    end;

}
//RFC7CAPEXGo2SolveJuly2023