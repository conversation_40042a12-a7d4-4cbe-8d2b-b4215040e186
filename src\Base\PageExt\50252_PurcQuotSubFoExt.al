pageextension 50252 PurQtSub extends "Purchase Quote Subform"
{
    layout
    {
        addafter("No.")
        {
            field("No.2"; "No.2")
            {

            }
        }
        addafter("Location Code")
        {
            field("Service Code"; "Service Code")
            {
                ApplicationArea = all;
            }
            field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
            {
                Visible = true;
            }
            field("Tariff No."; "Tariff No.")
            {
                ApplicationArea = all;
            }
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = ALL;
            }
            field("Capex No."; "Capex No.")
            {
                ApplicationArea = ALL;
            }
            field("Capex Line No."; "Capex Line No.")
            {
                ApplicationArea = ALL;
            }
        }
    }

    actions
    {
        // Add changes to page actions here
    }

    var
        myInt: Integer;
}