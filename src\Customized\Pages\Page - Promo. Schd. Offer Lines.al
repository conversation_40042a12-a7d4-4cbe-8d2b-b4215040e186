page 50123 "Promo. Schd. Offer Lines"
{
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = List;
    SourceTable = "Promo. Schedule Offer Line";
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Promotion Type"; "Promotion Type")
                {
                    ApplicationArea = all;
                }
                field("Min. Quantity"; "Min. Quantity")
                {
                    ApplicationArea = all;
                }
                field("Gift Item No."; "Gift Item No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field("Promotion Quantity"; "Promotion Quantity")
                {
                    ApplicationArea = all;
                }
                field(Active; Active)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

