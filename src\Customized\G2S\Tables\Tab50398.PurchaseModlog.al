table 50369 "Purchase Mod log"
{
    Caption = 'Purchase Mod log';
    DataClassification = ToBeClassified;
    LookupPageId = "Modifier Log";
    Permissions = tabledata "Purchase Mod log" = RIMD;

    fields
    {
        field(1; "Line No"; Integer)
        {
            Caption = 'Line No';
            AutoIncrement = true;
            DataClassification = ToBeClassified;
        }
        field(2; "Modifier No."; Code[20])
        {
            Caption = 'Modifier No.';
            DataClassification = ToBeClassified;
            TableRelation = "Purchase Doc Modifier"."No.";
        }
        field(3; "Document Status"; Enum "Purchase Document Status")
        {
            Caption = 'Document Status';
            DataClassification = ToBeClassified;
        }
        field(4; Adjusted; Boolean)
        {
            Caption = 'Adjusted';
            DataClassification = ToBeClassified;
        }
        field(5; "Created Time"; DateTime)
        {
            DataClassification = ToBeClassified;
        }
        field(6; "Adjusted Time"; DateTime)
        {
            DataClassification = ToBeClassified;
        }
        field(7; "Document No."; Code[20])
        {
            DataClassification = ToBeClassified;
        }
    }
    keys
    {
        key(PK; "Line No", "Modifier No.")
        {
            Clustered = true;
        }
    }
}
