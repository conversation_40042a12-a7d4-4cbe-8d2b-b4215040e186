pageextension 50008 PurchOrderExt50 extends "Purchase Order"
{
    layout
    {

        addbefore("No.")
        {
            field("Purchase Type"; "Purchase Type")
            {
                ApplicationArea = all;
                Editable = false;
            }
        }
        addafter("Due Date")
        {
            field("FA Tagging Not Required"; "FA Tagging Not Required")
            {
                ApplicationArea = ALL;
            }

            field("Contract Start Date"; "Contract Start Date")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Prepayment No. Series"; "Prepayment No. Series")
            {
                ApplicationArea = ALL;
            }
            field("End Date"; "End Date")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Blanket Order Ref No"; "Blanket Order Ref No")
            {
                ApplicationArea = all;
                Editable = false;
            }

            field("Purch Req. Ref. No."; "Purch Req. Ref. No.")
            {
                ApplicationArea = all;
            }

            field("Purchase Tolerance"; "Purchase Tolerance")
            {
                ApplicationArea = all;
                trigger OnValidate()
                begin
                    PurchTolPercEditUpdate();
                end;
            }
            field("Purchase Tol Percentage"; "Purchase Tol Percentage")
            {
                ApplicationArea = all;
                Editable = "Purchase Tol Percentage.Editable";
            }
            //B2BMS
            field("Created By"; "Created By")
            {
                ApplicationArea = All;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = All;
            }
            field("Modified By"; "Modified By")
            {
                ApplicationArea = All;
            }
            field("Modified date"; "Modified date")
            {
                ApplicationArea = All;
            }
            //B2BMS
        }
        addafter(Status)
        {
            field("Purchase Order Tracking"; "Purchase Order Tracking")
            {
                ApplicationArea = ALL;
            }
            field("Order Type"; "Order Type")
            {
                ApplicationArea = all;
            }

        }
        modify("Expected Receipt Date")
        {
            Editable = FieldEditable;
        }
    }

    actions
    {
        //BaluonDec7>>
        addafter(Print)
        {
            group(Marketing)
            {
                action(PrintForMarketing)
                {
                    image = Print;
                    ApplicationArea = all;
                    Caption = 'Print For Marketing';
                    trigger OnAction()
                    var
                        Ph: Record "Purchase Header";
                    begin
                        Ph.Reset();
                        ph.SetRange("No.", "No.");
                        Report.RunModal(50763, true, false, Ph);
                    end;
                }
            }
        }
        //BaluonDec7<<
        addafter(CalculateInvoiceDiscount)
        {
            action("Short Close Order")
            {
                image = CloseDocument;
                ApplicationArea = all;
                Caption = 'Short Close Order';

                trigger OnAction();
                begin
                    //PKONJU27>>
                    Rec.ShortCloseCheck();
                    CurrPage.Update();
                    //PKONJU27<<
                    ShortClosePurchOrder;
                end;
            }



        }
        addafter(CalculateInvoiceDiscount)
        {
            action("Get Gate Entry Lines")
            {
                Caption = 'Get Gate Entry Lines';
                ApplicationArea = all;
                image = GetEntries;
                trigger OnAction()
                begin
                    GetGateEntryLines;
                end;
            }
        }
        addafter(Warehouse)
        {
            action("Attached Gate Entry")
            {
                Caption = 'Attached Gate Entry';
                ApplicationArea = all;
                Image = InwardEntry;
                RunObject = page "Gate Entry Attachment List";
                RunPageLink = "Source No." = FIELD("No."), "Source Type" = CONST("Purchase Order"), "Entry Type" = CONST(Inward);

            }

        }
        addafter(CopyDocument)
        {
            action(CreateApprovedPO)
            {
                Caption = 'Create Approved PO';
                Image = CreateDocument;
                ApplicationArea = all;
                trigger OnAction()
                Var
                    FirstNoGVar: Code[20];
                    ToNoGVar: COde[20];
                    Window: Dialog;
                    PurchLine: Record "Purchase Line";
                    VendorNoGVar: code[20];
                    POCreated: Boolean;
                    PurcLine: Record "Purchase Line";
                BEGIN
                    TestField(Status, Status::Released);


                    PurcLine.RESET();
                    PurcLine.SETRANGE("Document No.", "No.");
                    PurcLine.SETRANGE(Select, true);
                    PurcLine.SETRANGE(Type, PurcLine.Type::"Charge (Item)");
                    PurcLine.SETFILTER("No.", '<>%1', '');
                    PurcLine.SETFILTER(Quantity, '<>%1', 0);
                    PurcLine.SETFILTER("New Vendor No.", '<>%1', '');
                    PurcLine.SETRANGE("Approved PO Created", FALSE);
                    IF NOT PurcLine.FINDSET THEN
                        ERROR(NoLinesError);


                    IF NOT CONFIRM(POCreateCnfm) THEN
                        EXIT;

                    CLEAR(FirstNoGVar);
                    CLEAR(ToNoGVar);
                    clear(VendorNoGVar);
                    Window.OPEN('Creating Purchase Order #1########');

                    PurchLine.reset;
                    PurchLine.SetCurrentKey("New Vendor No.");
                    PurchLine.SetRange("Document No.", "No.");
                    PurchLine.SetRange(Select, true);
                    PurchLine.SetRange("Approved PO Created", false);
                    IF PurchLine.findset then
                        repeat

                            IF PurchLine."New Vendor No." <> VendorNoGVar THEN BEGIN
                                VendorNoGVar := PurchLine."New Vendor No.";
                                IF FirstNoGVar = '' THEN BEGIN
                                    FirstNoGVar := CreateApprovedPurchOrder(VendorNoGVar, "No.");
                                    Window.UPDATE(1, FirstNoGVar);
                                END ELSE BEGIN
                                    ToNoGVar := CreateApprovedPurchOrder(VendorNoGVar, "No.");
                                    Window.UPDATE(1, ToNoGVar);
                                END;
                            END;
                            POCreated := TRUE;
                        UNTIL PurchLine.NEXT = 0;

                    IF ToNoGVar = '' THEN
                        ToNoGVar := FirstNoGVar;
                    Window.CLOSE();

                    IF POCreated AND (FirstNoGVar <> '') THEN BEGIN
                        MESSAGE(Text011, FirstNoGVar, ToNoGVar);
                    END;
                END;
            }
            /*action(AutoShortClose)
            {
                trigger OnAction()
                BEGIN
                    CU2.RUN;
                END;


            }*/
            action(TransferBookValuetoAccount)
            {
                image = TransferFunds;
                ApplicationArea = all;
                trigger OnAction()
                BEGIN
                    IF NOT CONFIRM(Text0010, FALSE) THEN
                        EXIT;
                    TransferBookValuetoAcc();

                END;
            }
        }
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()

            BEGIN
                TestCapex();
                TestField("Order Status", 0);
                TestField("Order Date");//Baluon Apr 18 2022
                MatchLocations();
                IF UserSetup.GET(UserId) THEN BEGIN
                    IF ("Posting Date" <> WorkDate()) AND (NOT UserSetup.AllowPostingDateModify) THEN
                        ERROR('Posting date must be equal to Workdate.');
                end;
                IF "Purchase Type" <> "Purchase Type"::PMS THEN BEGIN
                    PurchLn.reset;
                    PurchLn.SetRange("Document No.", "No.");
                    PurchLn.SetFilter(Type, '<>%1', PurchLn.type::"G/L Account");
                    //PurchLn.SetRange(Type, PurchLn.Type::Item);
                    if PurchLn.findset then
                        repeat
                            TestField("Location Code");
                            PurchLn.TestField("Location Code");
                        until PurchLn.next = 0;
                end;

                CheckExpectedReceiptDate();
            END;
        }
        modify(Reopen)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
            END;
        }

        modify(Release)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
                MatchLocations();
                PurchLn.reset;
                PurchLn.SetRange("Document Type", "Document Type");
                PurchLn.SetRange("Document No.", "No.");
                PurchLn.SetFilter(Type, '<>%1', PurchLn.type::"G/L Account");
                //PurchLn.SetRange(Type, PurchLn.Type::Item);
                if PurchLn.findset then
                    repeat
                        TestField("Location Code");
                        PurchLn.TestField("Location Code");
                    until PurchLn.next = 0;

                CheckExpectedReceiptDate();
            END;
        }
        modify("Create &Whse. Receipt")
        {
            trigger OnBeforeAction()
            var
                PurchLiLvar: Record "Purchase Line";
                FAScard: Record "Fixed Asset";
            BEGIN
                IF ("Document Type" = "Document Type"::Order) AND ("FA Tagging Not Required" = FALSE) then begin
                    PurchLiLvar.Reset();
                    PurchLiLvar.SetRange("Document Type", "Document Type");
                    PurchLiLvar.SetRange("Document No.", "No.");
                    PurchLiLvar.SetRange(Type, PurchLiLvar.Type::"Fixed Asset");
                    PurchLiLvar.Setfilter("Qty. to Receive", '<>%1', 0);
                    IF PurchLiLvar.findset then
                        repeat
                            FAScard.get(PurchLiLvar."No.");
                            FAScard.TestField("FA Tagging Code");
                        until PurchLiLvar.next = 0;
                end;
            END;
        }
        modify(PostAndNew)
        {
            trigger OnBeforeAction()
            var
                PurchLinLvar: Record "Purchase Line";
                FAcard: Record "Fixed Asset";
                PurchLn: Record "Purchase Line";
                GLAcc: Record "G/L Account";
                InvPostingSetup: Record "Inventory Posting Setup";
                GenPostingSetup: Record "General Posting Setup";
            BEGIN
                /*PurchLn.RESET;
                PurchLn.SetRange("Document No.", "No.");
                PurchLn.Setfilter("Qty. to Receive", '<>%1', 0);
                //PurchLn.SetRange(Type,PurchLn.type::"G/L Account");
                IF PurchLn.findset then
                    repeat
                        IF (PurchLn.Type = PurchLn.Type::"Charge (Item)") OR (PurchLn.Type = PurchLn.Type::"G/L Account") THEN BEGIN
                            IF InvPostingSetup.get(PurchLn."Location Code", GLAcc."Inventory Posting Setup") THEN;
                            IF GenPostingSetup.get(PurchLn."Gen. Bus. Posting Group", PurchLn."Gen. Prod. Posting Group") THEN;
                            InvPostingSetup.TestField("GL Services (Interim)");
                            GenPostingSetup.TestField("GL Ser Accural (Interim)");
                        END;
                    until PurchLn.next = 0;*/



                IF ("Document Type" = "Document Type"::Order) AND ("FA Tagging Not Required" = FALSE) then begin
                    PurchLinLvar.Reset();
                    PurchLinLvar.SetRange("Document Type", "Document Type");
                    PurchLinLvar.SetRange("Document No.", "No.");
                    PurchLinLvar.SetRange(Type, PurchLinLvar.Type::"Fixed Asset");
                    PurchLinLvar.Setfilter("Qty. to Receive", '<>%1', 0);
                    IF PurchLinLvar.findset then
                        repeat
                            FAcard.get(PurchLinLvar."No.");
                            FAcard.TestField("FA Tagging Code");
                        until PurchLinLvar.next = 0;
                end;
                TestField("Order Status", 0);
                TestField("Shortcut Dimension 1 Code");
                TestField("Shortcut Dimension 2 Code");
                TestField(Status, Status::Released);
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");

                PurchLinLvar.Reset();
                PurchLinLvar.SetRange("Document Type", "Document Type");
                PurchLinLvar.SetRange("Document No.", "No.");
                PurchLinLvar.Setfilter("Qty. to Invoice", '<>%1', 0);
                PurchLinLvar.Setrange("WHT Applicable", false);
                IF PurchLinLvar.findset then
                    repeat
                        IF NOT Confirm('There is no WHT Applicable for Lines in this Order', false) then
                            error('Please give WHT Applicable for for order %1', PurchLinLvar."Document No.");
                    until PurchLinLvar.next = 0;


                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;
        }
        modify("Post and &Print")
        {
            trigger OnBeforeAction()
            var
                PurchLinLvar: Record "Purchase Line";
                FAcard: Record "Fixed Asset";
            BEGIN
                IF ("Document Type" = "Document Type"::Order) AND ("FA Tagging Not Required" = FALSE) then begin
                    PurchLinLvar.Reset();
                    PurchLinLvar.SetRange("Document Type", "Document Type");
                    PurchLinLvar.SetRange("Document No.", "No.");
                    PurchLinLvar.SetRange(Type, PurchLinLvar.Type::"Fixed Asset");
                    PurchLinLvar.Setfilter("Qty. to Receive", '<>%1', 0);
                    IF PurchLinLvar.findset then
                        repeat
                            FAcard.get(PurchLinLvar."No.");
                            FAcard.TestField("FA Tagging Code");
                        until PurchLinLvar.next = 0;
                end;
                TestField("Order Status", 0);
                TestField(Status, Status::Released);
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;

        }
        modify(Preview)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
            END;
        }


    }

    var
        Purchline: Record "Purchase Line";
        "Purchase Tol Percentage.Editable": Boolean;
        Subform: Page "Purchase Order Subform";


    trigger OnOpenPage()
    begin
        PurchTolPercEditUpdate();
        FieldEditable := SetEditable(Rec);
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    var
        DefaultOption: Integer;
        Selection: Integer;
        ShipInvoiceQst: Label '&Local,&Import';
    begin
        IF "No." = '' then begin
            if DefaultOption <= 0 then
                DefaultOption := 1;
            Selection := StrMenu(ShipInvoiceQst, DefaultOption);
            Case Selection of
                1:
                    "Purchase Type" := "Purchase Type"::"Local";
                2:
                    "Purchase Type" := "Purchase Type"::Import;
            end;
        end;
    end;

    trigger OnAfterGetCurrRecord()
    begin
        PurchTolPercEditUpdate();
        FieldEditable := SetEditable(Rec);
    end;

    local procedure PurchTolPercEditUpdate()
    begin
        IF "Purchase Tolerance" = true then
            "Purchase Tol Percentage.Editable" := true
        else
            "Purchase Tol Percentage.Editable" := false;
    end;

    local procedure TransferBookValuetoAcc()
    Var
        GenJournle: Record "Gen. Journal Line";
        ItmJounLne: Record "Item Journal Line";
        ItmJounLine: Record "Item Journal Line";
        PurchLne: Record "Purchase Line";
        NextLineNo: Integer;
        GenJourn: Record "Gen. Journal Line";
        DepGRec: Record "FA Depreciation Book";
        QtyPurch: Decimal;
        NextGLLineNo: Integer;
        GenPosSetup: Record "General Posting Setup";
    BEGIN
        ItmJounLine.reset;
        ItmJounLine.SetRange("Journal Batch Name", 'DEFAULT');
        ItmJounLine.SetRange("Journal Template Name", 'ITEM');
        IF ItmJounLine.FindLast() then
            NextLineNo := ItmJounLine."Line No."
        else
            NextLineNo := 0;

        DepGRec.reset;
        DepGRec.SetRange("FA No.", PurchLne."No.");
        IF DepGRec.findfirst then;


        PurchLne.reset;
        PurchLne.SetRange("Document No.", "No.");
        IF PurchLne.findset then
            repeat
                ItmJounLne.init;
                ItmJounLne."Journal Batch Name" := 'DEFAULT';
                ItmJounLne."Journal Template Name" := 'ITEM';
                ItmJounLne."Line No." := NextLineNo + 10000;
                ItmJounLne.VALIDATE("Posting Date", WorkDate());
                ItmJounLne."Entry Type" := ItmJounLne."Entry Type"::"Negative Adjmt.";
                ItmJounLne."Document No." := "No.";
                ItmJounLne.VALIDATE("Item No.", PurchLne."No.");
                ItmJounLne.VALIDATE("Location Code", PurchLne."Location Code");
                ItmJounLne.VALIDATE(Quantity, PurchLne.Quantity);
                ItmJounLne.Insert();
            until PurchLne.next = 0;

        CODEUNIT.Run(CODEUNIT::"Item Jnl.-Post", ItmJounLne);



        GenJourn.reset;
        GenJourn.SetRange("Journal Batch Name", 'ASSET');
        GenJourn.SetRange("Journal Template Name", 'ASSETS');
        IF GenJourn.FindLast() then
            NextGLLineNo := GenJourn."Line No." + 10000
        else
            NextGLLineNo := 10000;

        PurchLne.reset;
        PurchLne.SetRange("Document No.", "No.");
        PurchLne.SetFilter(Quantity, '<>%1', 0);
        IF PurchLne.findset then
            repeat
                QtyPurch := 1;
                GenPosSetup.reset;
                GenPosSetup.SetRange("Gen. Bus. Posting Group", PurchLne."Gen. Bus. Posting Group");
                GenPosSetup.SetRange("Gen. Prod. Posting Group", PurchLne."Gen. Prod. Posting Group");
                IF GenPosSetup.findfirst then;



                while QtyPurch <= PurchLne.Quantity do begin

                    GenJournle.init;
                    GenJournle."Journal Template Name" := 'ASSETS';
                    GenJournle."Journal Batch Name" := 'ASSET';
                    GenJournle."Line No." := NextGLLineNo;
                    GenJournle."Posting Date" := Today;
                    GenJournle."Document No." := PurchLne."Document No.";
                    GenJournle."Account Type" := GenJournle."Bal. Account Type"::"G/L Account";
                    GenJournle."Account No." := GenPosSetup."Inventory Adjmt. Account";
                    //GenJournle."Depreciation Book Code" := DepGRec."Depreciation Book Code";
                    //IF GenJournle."Line No." = 10000 then
                    // GenJournle."Depreciation Book Code" := 'COMP'
                    //else
                    GenJournle."Depreciation Book Code" := '';
                    //GenJournle."FA Posting Type" := GenJournle."FA Posting Type"::"Acquisition Cost";
                    //GenJournle."Gen. Posting Type" := GenJournle."Gen. Posting Type"::Purchase;
                    GenJournle."Bal. Account Type" := GenJournle."Bal. Account Type"::"G/L Account";
                    GenJournle."Bal. Account No." := GenPosSetup."Inventory Adjmt. Account";
                    GenJournle."Bal. Gen. Posting Type" := GenJournle."Bal. Gen. Posting Type"::Purchase;
                    GenJournle.Quantity := 1;
                    GenJournle.VALIDATE(Amount, PurchLne.Amount / PurchLne.Quantity);
                    GenJournle.Insert();
                    QtyPurch += 1;
                    NextGLLineNo += 10000;
                END;
            until PurchLne.next = 0;

        CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post", GenJournle);
    END;

    var
        FieldEditable: Boolean;

        POCreateCnfm: Label 'Do You want to Create POs.';
        Text011: Label 'PO Created from %1 to %2.';
        NoLinesError: Label 'There are no Lines to Create PO.';
        CU2: Codeunit Codeunit2;
        Text0010: Label 'Do you want to Transfer the FA book Value to Accounts?';
        PurchLn: Record "Purchase Line";
        UserSetup: Record "User Setup";
}