page 50124 "Material Disp. Lines Subform"
{

    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = ListPart;
    SourceTable = "MDV Line";
    UsageCategory = tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("MDV Type"; "MDV Type")
                {
                }
                field(Type; Type)
                {
                    ApplicationArea = all;
                }
                field("Item Category Code"; "Item Category Code")
                {
                    ApplicationArea = all;

                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field("Item Variant Code"; "Item Variant Code")
                {
                    ApplicationArea = all;

                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;

                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Line Amount(LCY)"; "Line Amount(LCY)")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Available Stock"; "Available Stock")
                {
                    ApplicationArea = all;
                }
                field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                }
                field("FA Posting Type"; "FA Posting Type")
                {
                    ApplicationArea = all;
                }
                field("FA No."; "FA No.")
                {
                    ApplicationArea = all;
                }
                field("Maintenance Code"; "Maintenance Code")
                {
                    ApplicationArea = all;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Item Tracking Lines")
                {
                    Caption = 'Item Tracking Lines';
                    //Visible = false;

                    trigger OnAction();
                    begin
                        OpenItemTracking(Rec);

                    end;
                }
            }
        }

    }




    trigger OnModifyRecord(): Boolean
    var
        MatDisHdr: Record "MDV Header";
    BEGIN
        IF MatDisHdr.GET("Document No.") THEN
            MatDisHdr.TestField("Approval Status", MatDisHdr."Approval Status"::Open);
    END;



    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::" ";
    end;

    procedure OpenItemTracking(LpaScrpJlines: Record "MDV Line")
    var
        ItemTrackingForm: page "Item Tracking Lines";
        TrackingSpecification: Record "Tracking Specification";
    begin
        TrackingSpecification.InitfromMDV(LpaScrpJlines);
        ItemTrackingForm.SetSourceSpec(TrackingSpecification, WorkDate);
        ItemTrackingForm.SetInbound(false);
        ItemTrackingForm.RUNMODAL;
    end;
}

