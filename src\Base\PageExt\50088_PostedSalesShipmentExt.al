pageextension 50088 PostedSalesShipment extends "Posted Sales Shipment"
{
    layout
    {
        addafter("Order No.")
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
            }
        }


    }

    actions
    {
        addafter("F&unctions")
        {
            action("Attached Gate Entry")
            {
                Caption = 'Attached Gate Entry';
                ApplicationArea = all;
                Image = InwardEntry;
                RunObject = page "Outward Gate Entry Line List";
                RunPageLink = "Entry Type" = const(Outward), "Source Type" = const("Sales Shipment"), "Source No." = field("No.");
            }
            group(Approval)
            {
                action("Re&lease")
                {
                    AccessByPermission = TableData "Sales Shipment Header" = MR;//PKON31052021
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()

                    begin
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendPSSforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF "Approval Status" <> "Approval Status"::Released then BEGIN
                            "Approval Status" := "Approval Status"::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    AccessByPermission = TableData "Sales Shipment Header" = MR;//PKON31052021
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 110);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF "Approval Status" <> "Approval Status"::Open then BEGIN
                            "Approval Status" := "Approval Status"::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }

                action(Approve)
                {
                    ApplicationArea = All;
                    Image = Action;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin

                        IF allinoneCU.CheckPSSApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendPSSForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelPSSForApproval(Rec);
                    end;
                }
            }
        }
        modify("&Print")
        {
            trigger OnBeforeAction()
            var
                uSrSet: record "User Setup";
            begin
                //b2bpksalecorr12
                //IF "No. Printed" > 1 then begin
                uSrSet.get(UserId);
                IF NOT uSrSet."Reprint Shipmt & GatePass Docs" then
                    Error('You do not have permissions to reprint the document.');
            end;
            // end;
        }
    }
    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    var
        OpenAppEntrExistsForCurrUser: Boolean;
        WorkflowManagement: Codeunit "Workflow Management";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: codeunit Codeunit1;
        RecordRest: record "Restricted Record";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
}
