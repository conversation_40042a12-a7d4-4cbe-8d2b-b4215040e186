pageextension 50064 PurchReceiptSubformExt extends 137
{
    layout
    {
        addafter("Location Code")
        {
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = ALL;
            }

            field("WHT Applicable"; "WHT Applicable")
            {
                ApplicationArea = ALL;
            }
            field("Direct Unit Cost"; "Direct Unit Cost")
            {
                ApplicationArea = ALL;
                Visible = true;//PKONJU7.2
            }
        }
    }

    actions
    {
        addafter("&Line")
        {
            action(TaskLines)
            {
                Image = List;
                RunObject = page "Task Codes Lines for Posting";
                RunPageLink = "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                ApplicationArea = All;

            }
        }
        modify("&Undo Receipt")
        {
            trigger OnBeforeAction()
            begin
                PurchRcptHdr.Reset();
                PurchRcptHdr.SetRange("No.", "Document No.");
                if PurchRcptHdr.FindFirst() then begin
                    PurchRcptHdr.TestField("Approval Status", 2);
                end;
                if UserSetup.GET(USERID) then
                    if not UserSetup."Undo Receipt" then
                        ERROR(Text001);
            end;
        }
    }
    var
        PurchRcptHdr: Record "Purch. Rcpt. Header";
        UserSetup: Record "User Setup";
        Text001: Label 'You do not have permission for Undo Receipt Please Enable UndoRecipt';
}