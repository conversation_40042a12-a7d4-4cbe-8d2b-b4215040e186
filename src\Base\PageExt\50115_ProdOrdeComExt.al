pageextension 50115 ProdComExt extends "Prod. Order Components"
{
    //>>>>>> G2S CAS-01318-S4L7H9 7/10/24
    Editable = false;
    //<<<<<< G2S CAS-01318-S4L7H9 7/10/24
    layout
    {
        // Add changes to page layout here
        addafter("Unit of Measure Code")
        {
            field("Available Qty"; "Available Qty")
            {
                applicationarea = ALL;
            }
        }

    }

    actions
    {
        modify(SelectItemSubstitution)
        {
            Visible = False;
        }
        addafter(SelectItemSubstitution)
        {
            action(SelectItemSubstitution2)
            {
                AccessByPermission = TableData "Item Substitution" = R;
                ApplicationArea = Manufacturing;
                Caption = '&Select Item Substitution';
                Image = SelectItemSubstitution;
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Select another item that has been set up to be traded instead of the original item if it is unavailable.';

                trigger OnAction()
                begin
                    CurrPage.SaveRecord;
                    ShowItemSub2;
                    CurrPage.Update(true);
                    ReserveComp;
                end;

            }
        }
    }

    procedure ShowItemSub2()
    begin
        GetCompSubst2(Rec);
    end;


    procedure GetCompSubst2(var ProdOrderComp: Record "Prod. Order Component")
    var
        ProdOrderCompSubst: Record "Prod. Order Component";
    begin
        ProdOrderCompSubst := ProdOrderComp;

        if not PrepareSubstList2(
             ProdOrderComp."Item No.",
             ProdOrderComp."Variant Code",
             ProdOrderComp."Location Code",
             ProdOrderComp."Due Date",
             true)
        then
            ErrorMessage2(ProdOrderComp."Item No.", ProdOrderComp."Variant Code");

        TempItemSubstitution.Reset();
        TempItemSubstitution.SetRange("Variant Code", ProdOrderComp."Variant Code");
        TempItemSubstitution.SetRange("Location Filter", ProdOrderComp."Location Code");
        if TempItemSubstitution.Find('-') then;
        if PAGE.RunModal(PAGE::"Item Substitution Entries", TempItemSubstitution) = ACTION::LookupOK then
            UpdateComponent2(ProdOrderComp, TempItemSubstitution."Substitute No.", TempItemSubstitution."Substitute Variant Code");
    end;

    procedure UpdateComponent2(var ProdOrderComp: Record "Prod. Order Component"; SubstItemNo: Code[20]; SubstVariantCode: Code[10])
    var
        TempProdOrderComp: Record "Prod. Order Component" temporary;
        ProdOrderCompReserve: Codeunit "Prod. Order Comp.-Reserve";
    begin
        if (ProdOrderComp."Item No." <> SubstItemNo) or (ProdOrderComp."Variant Code" <> SubstVariantCode) then
            ProdOrderCompReserve.DeleteLine(ProdOrderComp);

        TempProdOrderComp := ProdOrderComp;

        with TempProdOrderComp do begin
            SaveQty := "Quantity per";

            "Item No." := SubstItemNo;
            "Variant Code" := SubstVariantCode;
            "Location Code" := ProdOrderComp."Location Code";
            "Quantity per" := 0;
            Validate("Item No.");
            Validate("Variant Code");

            "Original Item No." := ProdOrderComp."Item No.";
            "Original Variant Code" := ProdOrderComp."Variant Code";

            if ProdOrderComp."Qty. per Unit of Measure" <> 1 then begin
                if ItemUnitOfMeasure.Get(Item."No.", ProdOrderComp."Unit of Measure Code") and
                   (ItemUnitOfMeasure."Qty. per Unit of Measure" = ProdOrderComp."Qty. per Unit of Measure")
                then
                    Validate("Unit of Measure Code", ProdOrderComp."Unit of Measure Code")
                else
                    SaveQty :=
                      Round(ProdOrderComp."Quantity per" * ProdOrderComp."Qty. per Unit of Measure", UOMMgt.QtyRndPrecision);
            end;
            Validate("Quantity per", SaveQty);
        end;

        ProdOrderComp := TempProdOrderComp;
    end;

    procedure PrepareSubstList2(ItemNo: Code[20]; VariantCode: Code[10]; LocationCode: Code[10]; DemandDate: Date; CalcATP: Boolean): Boolean
    begin
        Item.Get(ItemNo);
        Item.SetFilter("Location Filter", LocationCode);
        Item.SetFilter("Variant Filter", VariantCode);
        Item.SetRange("Date Filter", 0D, DemandDate);

        ItemSubstitution.Reset();
        ItemSubstitution.SetRange(Type, ItemSubstitution.Type::Item);
        ItemSubstitution.SetRange("No.", ItemNo);
        ItemSubstitution.SetRange("Variant Code", VariantCode);
        ItemSubstitution.SetRange("Location Filter", LocationCode);
        ItemSubstitution.SETFILTER("Period Start", '<=%1', Today());
        ItemSubstitution.SETFILTER("Period End", '>=%1', TODAY());
        if ItemSubstitution.Find('-') then begin
            TempItemSubstitution.DeleteAll();
            CreateSubstList2(ItemNo, ItemSubstitution, 1, DemandDate, CalcATP);
            exit(true);
        end;

        exit(false);
    end;

    local procedure CreateSubstList2(OrgNo: Code[20]; var ItemSubstitution3: Record "Item Substitution"; RelationsLevel: Integer; DemandDate: Date; CalcATP: Boolean)
    var
        ItemSubstitution: Record "Item Substitution";
        ItemSubstitution2: Record "Item Substitution";
        RelationsLevel2: Integer;
        ODF: DateFormula;
    begin
        ItemSubstitution.Copy(ItemSubstitution3);
        RelationsLevel2 := RelationsLevel;

        if ItemSubstitution.Find('-') then
            repeat
                Clear(TempItemSubstitution);
                TempItemSubstitution.Type := ItemSubstitution.Type;
                TempItemSubstitution."No." := ItemSubstitution."No.";
                TempItemSubstitution."Variant Code" := ItemSubstitution."Variant Code";
                TempItemSubstitution."Substitute Type" := ItemSubstitution."Substitute Type";
                TempItemSubstitution."Substitute No." := ItemSubstitution."Substitute No.";
                TempItemSubstitution."Substitute Variant Code" := ItemSubstitution."Substitute Variant Code";
                TempItemSubstitution.Description := ItemSubstitution.Description;
                TempItemSubstitution.Interchangeable := ItemSubstitution.Interchangeable;
                TempItemSubstitution."Location Filter" := ItemSubstitution."Location Filter";
                TempItemSubstitution."Relations Level" := RelationsLevel2;
                TempItemSubstitution."Shipment Date" := DemandDate;

                if CalcATP then begin
                    Item.Get(ItemSubstitution."Substitute No.");
                    TempItemSubstitution."Quantity Avail. on Shpt. Date" :=
                      AvailToPromise.QtyAvailabletoPromise(
                        Item, GrossReq, SchedRcpt,
                        Item.GetRangeMax("Date Filter"), 2, ODF);
                    Item.CalcFields(Inventory);
                    TempItemSubstitution.Inventory := Item.Inventory;
                end;

                if IsSubstitutionInserted2(TempItemSubstitution, ItemSubstitution) then begin
                    ItemSubstitution2.SetRange(Type, ItemSubstitution.Type);
                    ItemSubstitution2.SetRange("No.", ItemSubstitution."Substitute No.");
                    ItemSubstitution2.SetFilter("Substitute No.", '<>%1&<>%2', ItemSubstitution."No.", OrgNo);
                    ItemSubstitution.CopyFilter("Variant Code", ItemSubstitution2."Variant Code");
                    ItemSubstitution.CopyFilter("Location Filter", ItemSubstitution2."Location Filter");
                    if ItemSubstitution2.FindFirst then
                        CreateSubstList2(OrgNo, ItemSubstitution2, RelationsLevel2 + 1, DemandDate, CalcATP);
                end else begin
                    TempItemSubstitution.Reset;
                    if TempItemSubstitution.Find then
                        if RelationsLevel2 < TempItemSubstitution."Relations Level" then begin
                            TempItemSubstitution."Relations Level" := RelationsLevel2;
                            TempItemSubstitution.Modify;
                        end;
                end;
            until ItemSubstitution.Next = 0;
    end;

    local procedure IsSubstitutionInserted2(var ItemSubstitutionToCheck: Record "Item Substitution"; ItemSubstitution: Record "Item Substitution"): Boolean
    begin
        if ItemSubstitution."Substitute No." <> '' then
            with ItemSubstitutionToCheck do begin
                Reset;
                SetRange("Substitute Type", ItemSubstitution."Substitute Type");
                SetRange("Substitute No.", ItemSubstitution."Substitute No.");
                SetRange("Substitute Variant Code", ItemSubstitution."Substitute Variant Code");
                if IsEmpty then
                    exit(Insert);
            end;
        exit(false);
    end;

    procedure ErrorMessage2(ItemNo: Code[20]; VariantCode: Code[10])
    var
        Text100: Label 'An Item Substitution with period within today %1 does not exist for Item No. %2';
    begin
        if VariantCode <> '' then
            Error(Text001, ItemNo);
        IF (ItemSubstitution."Period Start" <> 0D) OR (ItemSubstitution."Period End" <> 0D) THEN BEGIN
            IF (ItemSubstitution."Period Start" > TODAY()) OR (TODAY() < ItemSubstitution."Period End") THEN
                ERROR(Text100, TODAY(), ItemNo)
        END ELSE
            ERROR('There are no valid substitutes are available for the Item %1', ItemNo);
        Error(Text002, ItemNo);
    end;

    var
        TempItemSubstitution: Record "Item Substitution" temporary;
        Item: Record Item;
        ItemSubstitution: Record "Item Substitution";
        ItemUnitOfMeasure: Record "Item Unit of Measure";
        SaveQty: Decimal;
        GrossReq: Decimal;
        SchedRcpt: Decimal;
        Text001: Label 'An Item Substitution with the specified variant does not exist for Item No. ''%1''.';
        Text002: Label 'An Item Substitution does not exist for Item No. ''%1''';
        UOMMgt: Codeunit "Unit of Measure Management";
        AvailToPromise: Codeunit "Available to Promise";
}