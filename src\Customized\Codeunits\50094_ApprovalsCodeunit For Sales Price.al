codeunit 50094 "Approval Process"
{
    trigger OnRun()
    begin

    end;

    [IntegrationEvent(false, false)]
    Procedure OnSendValueBaseWFForApproval(var ValueBaseWF: Record "Sales Price")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelValueBaseWFForApproval(var ValueBaseWF: Record "Sales Price")
    begin
    end;
    //Create events for workflow
    procedure RunworkflowOnSendCheckValueforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCheckValueforApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Approval Process", 'OnSendValueBaseWFForApproval', '', true, true)]
    local procedure RunworkflowonsendValueBaseForApproval(var ValueBaseWF: Record "Sales Price")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCheckValueforApprovalCode(), ValueBaseWF);
    end;

    procedure RunworkflowOnCancelCheckValueforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelValueBaseWFForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Approval Process", 'OncancelValueBaseWFForApproval', '', true, true)]
    local procedure RunworkflowonCancelValueBaseForApproval(var ValueBaseWF: Record "Sales Price")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCheckValueforApprovalCode(), ValueBaseWF);
    end;
    //Add events to library
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCheckValueforApprovalCode(), DATABASE::"Sales Price",
          CopyStr(CheckValuesendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCheckValueforApprovalCode(), DATABASE::"Sales Price",
          CopyStr(CheckValuerequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCheckValueforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCheckValueforApprovalCode(), RunworkflowOnSendCheckValueforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendCheckValueForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendCheckValueForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendCheckValueForApprovalCode());
        end;
    end;

    procedure ISCheckValuworkflowenabled(var ValueBaseWF: Record "Sales Price"): Boolean
    begin
        if ValueBaseWF."Approval Status" <> ValueBaseWF."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(ValueBaseWF, RunworkflowOnSendCheckValueforApprovalCode()));
    end;

    Procedure CheckValueBaseApprovalsWorkflowEnabled(VAR ValueBase: Record "Sales Price"): Boolean
    begin
        IF not ISCheckValuworkflowenabled(ValueBase) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, 1535, 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        ValueBase: Record "Sales Price";
    begin
        case RecRef.Number() of
            DATABASE::"Sales Price":
                begin
                    RecRef.SetTable(ValueBase);
                    ApprovalEntryArgument."Document No." := copystr(FORMAT(ValueBase.RecordId), 1, 20);
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument(RecRef: RecordRef; var Handled: boolean)
    var
        ValueBase: Record "Sales Price";
    begin
        case RecRef.Number() of
            DATABASE::"Sales Price":
                begin
                    RecRef.SetTable(ValueBase);
                    ValueBase."Approval Status" := ValueBase."Approval Status"::Open;
                    ValueBase.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument(RecRef: RecordRef; var Handled: boolean)
    var
        ValueBase: Record "Sales Price";
    begin
        case RecRef.Number() of
            DATABASE::"Sales Price":
                begin
                    RecRef.SetTable(ValueBase);
                    ValueBase."Approval Status" := ValueBase."Approval Status"::Released;
                    ValueBase.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, 1535, 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval(RecRef: RecordRef; var IsHandled: boolean)
    var
        ValueBase: Record "Sales Price";
    begin
        case RecRef.Number() of
            DATABASE::"Sales Price":
                begin
                    RecRef.SetTable(ValueBase);
                    ValueBase."Approval Status" := ValueBase."Approval Status"::"Pending for Approval";
                    ValueBase.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCheckValueforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCheckValueforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCheckValueforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCheckValueforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(ValueBaseCategoryTxt, 1, 20), CopyStr(ValueBaseCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Sales Price", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate()
    begin
        InsertValueBaseApprovalworkflowtemplate();
    end;

    local procedure InsertValueBaseApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt, 1, 17), CopyStr(ValueBaseApprWorkflowDescTxt, 1, 100), CopyStr(ValueBaseCategoryTxt, 1, 20));
        InsertValueBaseApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertValueBaseApprovalworkflowDetails(var workflow: record Workflow);
    var
        ValueBase: Record "Sales Price";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildValueBasetypecondition(ValueBase."Approval Status"::open), RunworkflowOnSendCheckValueforApprovalCode(), BuildValueBasetypecondition(ValueBase."Approval Status"::"Pending for Approval"), RunworkflowOnCancelCheckValueforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildValueBasetypecondition(status: integer): Text
    var
        ValueBase: Record "Sales Price";
    Begin
        ValueBase.SetRange("Approval Status", status);
        exit(StrSubstNo(ValueBaseTypeCondnTxt, workflowsetup.Encode(ValueBase.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = DATABASE::"Sales Price" then
            PageID := GetConditionalcardPageid(RecordRef)
    end;

    [EventSubscriber(ObjectType::Codeunit, 7000, 'OnAfterFindSalesLineItemPrice', '', false, false)]
    procedure FilterSalesPrice(var SalesLine: Record "Sales Line"; var TempSalesPrice: Record "Sales Price"; var FoundSalesPrice: Boolean)
    begin
        TempSalesPrice.SetRange("Approval Status", TempSalesPrice."Approval Status"::Released);
    end;

    local procedure GetConditionalcardPageid(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            DATABASE::"Sales Price":
                exit(page::"Sales Prices_Copy");
        end;
    end;



    var
        WorkflowManagement: Codeunit "Workflow Management";
        WorkflowevenHandling: Codeunit "Workflow Event Handling";
        workflowsetup: codeunit "Workflow Setup";
        CheckValuesendforapprovaleventdescTxt: Label 'Approval of a Sales Price is requested';
        CheckValuerequestcanceleventdescTxt: Label 'Approval of a Sales Price document is Cancelled';
        NoworkfloweableErr: Label 'No Approval workflow for this record type is enabled.';
        ValueBaseCategoryTxt: Label 'SalesPrice';
        ValueBaseCategoryDescTxt: Label 'SalesPrice';
        ValueBaseApprWorkflowDescTxt: Label 'SalesPrice Approval Workflow';
        IncDocOCRWorkflowCodeTxt: Label 'INCDOC-SalesPrice';
        ValueBaseTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="SalesPrice">%1</DataItem></DataItems></ReportParameters>';

}