pageextension 50348 NoseriesListExt extends "No. Series List"
{
    layout
    {
        // Add changes to page layout here
    }

    actions
    {
        // Add changes to page actions here
    }

    var
        myInt: Integer;

    trigger OnDeleteRecord(): Boolean
    var
        UserSetup: Record "User Setup";
    begin
        UserSetup.Reset();
        UserSetup.SetRange("User ID", UserId);
        IF Not UserSetup.FindFirst() then
            Error('User setup not exis for the user %1', UserId);
        IF Not UserSetup."Delete No. Series" then
            error('You dont have permisssions to delete the record.')
    end;
}