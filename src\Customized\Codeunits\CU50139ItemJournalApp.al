codeunit 50139 IJLSubEvents
{
    trigger OnRun()
    begin

    end;

    var

        WorkflowevenHandling: Codeunit "Workflow Event Handling";
        WorkflowManagement: Codeunit "Workflow Management";
        ApprvMgt: Codeunit "Approvals Mgmt.";
        RecordRest: Codeunit "Record Restriction Mgt.";
        ItemJournalBatchSendForApprovalEventDescTxtLbl: Label 'An approval of an Item journal batch has been requested';
        ItemJournalBatchApprovalRequestCancelEventDescTxtLbl: Label 'An approval request for an Item journal batch is canceled';
        ItemJournalLineSendForApprovalEventDescTxtLbl: Label '  Approval of a Item journal line is requested.';
        ItemJournalLineApprovalRequestCancelEventDescTxtLbl: Label '    An approval request for a Item journal line is canceled.';
        ACreatedItemJnlEventDescTxtLbl: Label 'A item Journal line is created.';
        PendingJournalBatchApprovalExistsErrLbl: Label 'An approval request already exists.';
        NoWorkflowEnabledErrLbl: Label 'This record is not supported by related approval workflow.';
        NoApprovalsSentMsgLbl: Label 'No approval requests have been sent, either because they are already sent or because related workflows do not support the journal line.';
        PendingApprovalForSelectedLinesMsgLbl: Label 'Approval requests have been sent.';
        PendingApprovalForSomeSelectedLinesMsgLbl: Label 'Approval requests have been sent.\\Requests for some journal lines were not sent, either because they are already sent or because related workflows do not support the journal line.';
        ApprovalReqCanceledForSelectedLinesMsgLbl: Label 'The approval request for the selected record has been canceled.';
        RestrictLineUsageDetailsTxtLbl: Label 'The restriction was imposed because the line requires approval.';
        RestrictBatchUsageDetailsTxtLbl: Label 'The restriction was imposed because the journal batch requires approval.';

    Procedure HasAnyOpenItemJournalLineApprovalEntries(JournalTemplateName: Code[20]; JournalBatchName: Code[20]): Boolean
    var
        ItemJournalLine: Record "Item Journal Line";
        ApprovalEntry: Record "Approval Entry";
        ItemJournalLineRecRef: RecordRef;
        ItemJournalLineRecordID: RecordID;
    begin
        ApprovalEntry.SETRANGE("Table ID", DATABASE::"Item Journal Line");
        ApprovalEntry.SETRANGE(Status, ApprovalEntry.Status::Open);
        ApprovalEntry.SETRANGE("Related to Change", FALSE);
        IF ApprovalEntry.ISEMPTY() THEN
            EXIT(FALSE);

        ItemJournalLine.SETRANGE("Journal Template Name", JournalTemplateName);
        ItemJournalLine.SETRANGE("Journal Batch Name", JournalBatchName);
        IF ItemJournalLine.ISEMPTY() THEN
            EXIT(FALSE);

        IF ItemJournalLine.COUNT() < ApprovalEntry.COUNT() THEN BEGIN
            ItemJournalLine.FINDSET();
            REPEAT
                IF HasOpenApprovalEntries(ItemJournalLine.RECORDID()) THEN
                    EXIT(TRUE);
            UNTIL ItemJournalLine.NEXT() = 0;
        END ELSE BEGIN
            ApprovalEntry.FINDSET();
            REPEAT
                ItemJournalLineRecordID := ApprovalEntry."Record ID to Approve";
                ItemJournalLineRecRef := ItemJournalLineRecordID.GETRECORD();
                ItemJournalLineRecRef.SETTABLE(ItemJournalLine);
                IF (ItemJournalLine."Journal Template Name" = JournalTemplateName) AND
                   (ItemJournalLine."Journal Batch Name" = JournalBatchName)
                THEN
                    EXIT(TRUE);
            UNTIL ApprovalEntry.NEXT() = 0;
        END;

        EXIT(FALSE)
    end;

    Procedure TrySendItemJournalBatchApprovalRequest(VAR ItemJournalLine: Record "Item Journal Line")
    var
        ItemJournalBatch: Record "Item Journal Batch";
    Begin
        GetItemJournalBatch(ItemJournalBatch, ItemJournalLine);
        CheckItemJournalBatchApprovalsWorkflowEnabled(ItemJournalBatch);
        IF HasOpenApprovalEntries(ItemJournalBatch.RECORDID()) OR
           HasAnyOpenItemJournalLineApprovalEntries(ItemJournalBatch."Journal Template Name", ItemJournalBatch.Name)
        THEN
            ERROR(PendingJournalBatchApprovalExistsErrLbl);
        OnSendItemJournalBatchForApproval(ItemJournalBatch);
    end;

    Procedure TrySendItemJournalLineApprovalRequests(VAR ItemJournalLine: Record "Item Journal Line")
    var
        LinesSent: Integer;
    begin
        IF ItemJournalLine.COUNT() = 1 THEN
            CheckItemJournalLineApprovalsWorkflowEnabled(ItemJournalLine);

        REPEAT
            IF WorkflowManagement.CanExecuteWorkflow(ItemJournalLine,
                 RunWorkflowOnSendItemJournalLineForApprovalCode()) AND
               NOT HasOpenApprovalEntries(ItemJournalLine.RECORDID())
            THEN BEGIN
                OnSendItemJournalLineForApproval(ItemJournalLine);
                LinesSent += 1;
            END;
        UNTIL ItemJournalLine.NEXT() = 0;

        CASE LinesSent OF
            0:
                MESSAGE(NoApprovalsSentMsgLbl);
            ItemJournalLine.COUNT():
                MESSAGE(PendingApprovalForSelectedLinesMsgLbl);
            ELSE
                MESSAGE(PendingApprovalForSomeSelectedLinesMsgLbl);
        END;
    end;

    Procedure TryCancelItemJournalBatchApprovalRequest(VAR ItemJournalLine: Record "Item Journal Line")
    var
        ItemJournalBatch: Record "Item Journal Batch";
    begin
        GetItemJournalBatch(ItemJournalBatch, ItemJournalLine);
        OnCancelItemJournalBatchApprovalRequest(ItemJournalBatch);
    end;

    procedure TryCancelItemJournalLineApprovalRequests(VAR ItemJournalLine: Record "Item Journal Line")
    begin
        REPEAT
            IF HasOpenApprovalEntries(ItemJournalLine.RECORDID()) THEN
                OnCancelItemJournalLineApprovalRequest(ItemJournalLine);
        UNTIL ItemJournalLine.NEXT() = 0;
        MESSAGE(ApprovalReqCanceledForSelectedLinesMsgLbl);
    end;

    LOCAL Procedure GetItemJournalBatch(VAR ItemJournalBatch: Record "Item Journal Batch"; VAR
                                                                                     ItemJournalLine: Record "Item Journal Line")
    begin
        IF NOT ItemJournalBatch.GET(ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name") THEN
            ItemJournalBatch.GET(ItemJournalLine.GETFILTER("Journal Template Name"), ItemJournalLine.GETFILTER("Journal Batch Name"));
    end;

    procedure CheckItemJournalBatchApprovalsWorkflowEnabled(VAR ItemJournalBatch: Record "Item Journal Batch"): Boolean
    begin
        IF NOT
           WorkflowManagement.CanExecuteWorkflow(ItemJournalBatch, RunWorkflowOnSendItemJournalBatchForApprovalCode())
        THEN
            ERROR(NoWorkflowEnabledErrLbl);

        EXIT(TRUE);
    end;

    procedure CheckItemJournalLineApprovalsWorkflowEnabled(VAR ItemJournalLine: Record "Item Journal Line"): Boolean
    begin
        IF NOT
           WorkflowManagement.CanExecuteWorkflow(ItemJournalLine,
            RunWorkflowOnSendItemJournalLineForApprovalCode())
        THEN
            ERROR(NoWorkflowEnabledErrLbl);

        EXIT(TRUE);
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnMoveItemJournalLine', '', false, false)]
    Procedure PostApprovalEntriesMoveItemJournalLine(VAR Sender: Record "Item Journal Line"; ToRecordID: RecordID)
    begin
        //Need to check direct calls
        ApprvMgt.PostApprovalEntries(Sender.RECORDID(), ToRecordID, Sender."Document No.");
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnAfterDeleteEvent', '', false, false)]
    Procedure DeleteApprovalEntriesAfterDeleteItemJournalLine(VAR Rec: Record "Item Journal Line"; RunTrigger: Boolean)
    Begin
        ApprvMgt.DeleteApprovalEntries(Rec.RECORDID());
    End;

    [EventSubscriber(ObjectType::Table, 233, 'OnMoveItemJournalBatch', '', false, false)]
    Procedure PostApprovalEntriesMoveItemJournalBatch(var Sender: record "Item Journal Line"; ToRecordID: RecordId)
    var
        RecordRestrictionMgt: Codeunit "Record Restriction Mgt.";
    begin
        ApprvMgt.PostApprovalEntries(Sender.RECORDID(), ToRecordID, '');
        RecordRestrictionMgt.AllowRecordUsage(Sender.RECORDID());
        ApprvMgt.DeleteApprovalEntries(Sender.RECORDID());
    end;

    [EventSubscriber(ObjectType::Table, 233, 'OnAfterDeleteEvent', '', false, false)]
    Procedure DeleteApprovalEntriesAfterDeleteItemJournalBatch(VAR Rec: Record "Item Journal Batch"; RunTrigger: Boolean)
    begin
        ApprvMgt.DeleteApprovalEntries(Rec.RECORDID());
    end;

    [IntegrationEvent(false, false)]
    local procedure OnSendItemJournalBatchForApproval(VAR ItemJournalBatch: Record "Item Journal Batch")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnCancelItemJournalBatchApprovalRequest(VAR ItemJournalBatch: Record "Item Journal Batch")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnSendItemJournalLineForApproval(VAR ItemJournalLine: Record "Item Journal Line")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnCancelItemJournalLineApprovalRequest(var ItemJournalLine: Record "Item Journal Line")
    begin
    end;

    Procedure ShowItemJournalApprovalEntries(VAR ItemJournalLine: Record "Item Journal Line")
    var
        ApprovalEntry: Record "Approval Entry";
        ItemJournalBatch: Record "Item Journal Batch";
    Begin
        GetItemJournalBatch(ItemJournalBatch, ItemJournalLine);
        ApprovalEntry.SETFILTER("Table ID", '%1|%2', DATABASE::"Item Journal Batch", DATABASE::"Item Journal Line");
        ApprovalEntry.SETFILTER("Record ID to Approve", '%1|%2', ItemJournalBatch.RECORDID(), ItemJournalLine.RECORDID());
        ApprovalEntry.SETRANGE("Related to Change", FALSE);
        PAGE.RUN(PAGE::"Approval Entries", ApprovalEntry);
    end;

    procedure IsItemJournalBatchApprovalsWorkflowEnabled(VAR ItemJournalBatch: Record "Item Journal Batch"): Boolean
    begin
        EXIT(WorkflowManagement.CanExecuteWorkflow(ItemJournalBatch,
            RunWorkflowOnSendItemJournalBatchForApprovalCode()));
    end;

    procedure IsItemJournalLineApprovalsWorkflowEnabled(VAR ItemJournalLine: Record "Item Journal Line"): Boolean
    Begin
        EXIT(WorkflowManagement.CanExecuteWorkflow(ItemJournalLine,
            RunWorkflowOnSendItemJournalLineForApprovalCode()));
    end;

    procedure HasOpenApprovalEntries(RecordID: RecordID): Boolean
    var
        ApprovalEntry: Record "Approval Entry";
    begin
        ApprovalEntry.SETRANGE("Table ID", RecordID.TABLENO());
        ApprovalEntry.SETRANGE("Record ID to Approve", RecordID);
        ApprovalEntry.SETRANGE(Status, ApprovalEntry.Status::Open);
        ApprovalEntry.SETRANGE("Related to Change", FALSE);
        EXIT(NOT ApprovalEntry.ISEMPTY());
    end;

    Procedure RunWorkflowOnAfterInsertItemJournalLineCode(): Code[128]
    begin
        EXIT(COPYSTR(UPPERCASE('RunWorkflowOnAfterInsertItemJournalLine'), 1, 128));
    end;

    Procedure RunWorkflowOnSendItemJournalBatchForApprovalCode(): Code[128]
    begin
        EXIT(COPYSTR(UPPERCASE('RunWorkflowOnSendItemJournalBatchForApproval'), 1, 128));
    end;

    Procedure RunWorkflowOnCancelItemJournalBatchApprovalRequestCode(): Code[128]
    Begin
        EXIT(COPYSTR(UPPERCASE('RunWorkflowOnCancelItemJournalBatchApprovalRequest'), 1, 128));
    End;

    Procedure RunWorkflowOnSendItemJournalLineForApprovalCode(): Code[128]
    Begin
        EXIT(COPYSTR(UPPERCASE('RunWorkflowOnSendItemJournalLineForApproval'), 1, 128));
    End;

    Procedure RunWorkflowOnCancelItemJournalLineApprovalRequestCode(): Code[128]
    Begin
        EXIT(COPYSTR(UPPERCASE('RunWorkflowOnCancelItemJournalLineApprovalRequest'), 1, 128));
    End;

    [EventSubscriber(ObjectType::Table, 83, 'OnAfterInsertEvent', '', false, false)]
    procedure RunWorkflowOnAfterInsertItemJournalLine(VAR Rec: Record "Item Journal Line"; RunTrigger: Boolean)
    begin
        WorkflowManagement.HandleEvent(RunWorkflowOnAfterInsertItemJournalLineCode(), Rec);
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendItemJournalBatchForApproval', '', false, false)]
    procedure RunWorkflowOnSendItemJournalBatchForApproval(VAR ItemJournalBatch: Record "Item Journal Batch")
    begin
        WorkflowManagement.HandleEvent(RunWorkflowOnSendItemJournalBatchForApprovalCode(), ItemJournalBatch);
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnCancelItemJournalBatchApprovalRequest', '', false, false)]
    Procedure beginRunWorkflowOnCancelItemJournalBatchApprovalRequest(VAR ItemJournalBatch: Record "Item Journal Batch")
    begin

        WorkflowManagement.HandleEvent(RunWorkflowOnCancelItemJournalBatchApprovalRequestCode(), ItemJournalBatch);
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendItemJournalLineForApproval', '', false, false)]
    Procedure RunWorkflowOnSendItemJournalLineForApproval(VAR ItemJournalLine: Record "Item Journal Line")
    begin
        WorkflowManagement.HandleEvent(RunWorkflowOnSendItemJournalLineForApprovalCode(), ItemJournalLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnCancelItemJournalLineApprovalRequest', '', false, false)]
    Procedure RunWorkflowOnCancelItemJournalLineApprovalRequest(VAR ItemJournalLine: Record "Item Journal Line")
    begin
        WorkflowManagement.HandleEvent(RunWorkflowOnCancelItemJournalLineApprovalRequestCode(), ItemJournalLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryitemJournalLines(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.CreateApprovalRequestsCode():
                BEGIN
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CreateApprovalRequestsCode(), RunWorkflowOnSendItemJournalLineForApprovalCode());
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CreateApprovalRequestsCode(), RunWorkflowOnSendItemJournalBatchForApprovalCode());
                END;
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                BEGIN
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunWorkflowOnSendItemJournalLineForApprovalCode());
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunWorkflowOnSendItemJournalBatchForApprovalCode());
                END;
            workflowresponsehandling.OpenDocumentCode():
                BEGIN
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunWorkflowOnCancelItemJournalLineApprovalRequestCode());
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunWorkflowOnCancelItemJournalBatchApprovalRequestCode());
                END;
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                BEGIN
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(),
        RunWorkflowOnCancelItemJournalLineApprovalRequestCode());
                    workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(),
        RunWorkflowOnCancelItemJournalBatchApprovalRequestCode());
                END;
        end;
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunWorkflowOnAfterInsertItemJournalLineCode(), DATABASE::"Item Journal Line",
          CopyStr(ACreatedItemJnlEventDescTxtLbl, 1, 250), 0, FALSE);
        //>>>>> CAS-01383-Y5V4Y9 
        //>>>> Item Journal Batch Fix G2S 130225
        WorkflowevenHandling.AddEventToLibrary(RunWorkflowOnSendItemJournalBatchForApprovalCode(), DATABASE::"Item Journal Batch",
          CopyStr(ItemJournalBatchSendForApprovalEventDescTxtLbl, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunWorkflowOnCancelItemJournalBatchApprovalRequestCode(), DATABASE::"Item Journal Batch", CopyStr(ItemJournalBatchApprovalRequestCancelEventDescTxtLbl, 1, 250), 0, FALSE);
        //<<<<< CAS-01383-Y5V4Y9 
        //<<<<< Item Journal Batch Fix G2S 130225
        WorkflowevenHandling.AddEventToLibrary(RunWorkflowOnSendItemJournalLineForApprovalCode(), DATABASE::"Item Journal Line", CopyStr(ItemJournalLineSendForApprovalEventDescTxtLbl, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunWorkflowOnCancelItemJournalLineApprovalRequestCode(), DATABASE::"Item Journal Line", CopyStr(ItemJournalLineApprovalRequestCancelEventDescTxtLbl, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryQuotationComparision(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunWorkflowOnCancelItemJournalBatchApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(RunWorkflowOnCancelItemJournalBatchApprovalRequestCode(),
                  RunWorkflowOnSendItemJournalBatchForApprovalCode());
            RunWorkflowOnCancelItemJournalLineApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(RunWorkflowOnCancelItemJournalLineApprovalRequestCode(),
                  RunWorkflowOnSendItemJournalLineForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                begin
                    WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendItemJournalBatchForApprovalCode());
                    WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendItemJournalLineForApprovalCode());
                end;
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                begin
                    WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendItemJournalBatchForApprovalCode());
                    WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendItemJournalLineForApprovalCode());
                end;
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                begin
                    WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendItemJournalBatchForApprovalCode());
                    WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendItemJournalLineForApprovalCode());
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgument(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        ItemJournalBatch: Record "Item Journal Batch";
        ItemJournalLine: Record "Item Journal Line";
    begin
        case RecRef.Number() of
            DATABASE::"Item Journal Batch":
                RecRef.SETTABLE(ItemJournalBatch);
            DATABASE::"Item Journal Line":
                BEGIN
                    RecRef.SETTABLE(ItemJournalLine);
                    ApprovalEntryArgument."Document Type" := ItemJournalLine."Document Type";
                    ApprovalEntryArgument."Document No." := ItemJournalLine."Document No.";
                    ApprovalEntryArgument."Salespers./Purch. Code" := ItemJournalLine."Salespers./Purch. Code";
                    ApprovalEntryArgument.Amount := ItemJournalLine.Amount;
                    ApprovalEntryArgument."Amount (LCY)" := ItemJournalLine."Amount (ACY)";
                END;
        end;
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Batch", 'OnBeforeCheckLines', '', false, false)]
    procedure CheckItejouApprovals(var ItemJnlLine: Record "Item Journal Line")
    begin
        //ItemJnlLine.OnCheckItemJournalLinePostRestrictions()
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'OnConditionalCardPageIDNotFound', '', false, false)]
    procedure OpenPageIJL(RecordRef: RecordRef; var CardPageID: Integer)
    begin
        CASE RecordRef.NUMBER() OF
            DATABASE::"Item Journal Batch":
                CardPageID := GetItemJournalBatchPageID(RecordRef);
            DATABASE::"Item Journal Line":
                CardPageID := GetItemJournalLinePageID(RecordRef);
        end;
    end;

    LOCAL Procedure GetItemJournalBatchPageID(RecordRef: RecordRef): Integer
    var
        ItemJournalBatch: Record "Item Journal Batch";
        ItemJournalLine: Record "Item Journal Line";
    begin
        RecordRef.SETTABLE(ItemJournalBatch);
        ItemJournalLine.SETRANGE("Journal Template Name", ItemJournalBatch."Journal Template Name");
        ItemJournalLine.SETRANGE("Journal Batch Name", ItemJournalBatch.Name);
        ItemJournalLine.FINDFIRST();

        RecordRef.GETTABLE(ItemJournalLine);
        EXIT(GetItemJournalLinePageID(RecordRef));
    end;

    LOCAL Procedure GetItemJournalLinePageID(RecordRef: RecordRef): Integer
    var
        ItemJournalLine: Record "Item Journal Line";
        ItemJournalTemplate: Record "Item Journal Template";
    begin
        RecordRef.SETTABLE(ItemJournalLine);
        ItemJournalTemplate.GET(ItemJournalLine."Journal Template Name");
        IF ItemJournalTemplate.Recurring THEN
            EXIT(PAGE::"Recurring General Journal");
        CASE ItemJournalTemplate.Type OF
            ItemJournalTemplate.Type::Item:
                EXIT(PAGE::"Item Journal");
            ItemJournalTemplate.Type::"Phys. Inventory":
                EXIT(PAGE::"Phys. Inventory Journal");
            ItemJournalTemplate.Type::Revaluation:
                EXIT(PAGE::"Revaluation Journal");
            ItemJournalTemplate.Type::Consumption:
                EXIT(PAGE::"Consumption Journal");
            ItemJournalTemplate.Type::Output:
                EXIT(PAGE::"Output Journal");
            ItemJournalTemplate.Type::Capacity:
                EXIT(PAGE::"Capacity Journal");
        END;
    end;


    [EventSubscriber(ObjectType::Table, 83, 'OnCheckItemJournalLinePostRestrictions', '', false, false)]
    procedure ItemJournalBatchCheckItemJournalLinePostRestrictions(VAR Sender: Record "Item Journal Line")
    var
        ItemJournalBatch: Record "Item Journal Batch";
    begin
        IF ItemJournalBatch.GET(Sender."Journal Template Name", Sender."Journal Batch Name") THEN
            RecordRest.CheckRecordHasUsageRestrictions(ItemJournalBatch);
        //RecordRest.CheckRecordHasUsageRestrictions(ItemJournalBatch.RECORDID());
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnCheckItemJournalLinePostRestrictions', '', false, false)]
    procedure ItemJournalLineCheckItemJournalLinePostRestrictions(VAR Sender: Record "Item Journal Line")
    begin
        RecordRest.CheckRecordHasUsageRestrictions(Sender);
        //RecordRest.CheckRecordHasUsageRestrictions(Sender.RECORDID());
    end;

    Procedure AllowItemJournalBatchUsage(ItemJournalBatch: Record "Item Journal Batch")
    var
        ItemJournalLine: Record "Item Journal Line";
    begin
        RecordRest.AllowRecordUsage(ItemJournalBatch.RECORDID());

        ItemJournalLine.SETRANGE("Journal Template Name", ItemJournalBatch."Journal Template Name");
        ItemJournalLine.SETRANGE("Journal Batch Name", ItemJournalBatch.Name);
        IF ItemJournalLine.FINDSET() THEN
            REPEAT
                RecordRest.AllowRecordUsage(ItemJournalLine.RECORDID());
            UNTIL ItemJournalLine.NEXT() = 0;
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnBeforeDeleteEvent', '', false, false)]
    procedure RemoveItemJournalLineRestrictionsBeforeDelete(VAR Rec: Record "Item Journal Line"; RunTrigger: Boolean)
    begin
        RestrictItemJournalLine(Rec);
        RecordRest.AllowRecordUsage(Rec);
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnAfterInsertEvent', '', false, false)]
    procedure RestrictItemJournalLineAfterInsert(VAR Rec: Record "Item Journal Line"; RunTrigger: Boolean)
    begin
        RestrictItemJournalLine(Rec);
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnAfterModifyEvent', '', false, false)]
    procedure RestrictItemJournalLineAfterModify(VAR Rec: Record "Item Journal Line"; VAR xRec: Record "Item Journal Line"; RunTrigger: Boolean)
    begin
        IF FORMAT(Rec) = FORMAT(xRec) THEN
            EXIT;
        RestrictItemJournalLine(Rec);
    end;

    LOCAL Procedure RestrictItemJournalLine(VAR ItemJournalLine: Record "Item Journal Line")
    var
        ItemJournalBatch: Record "Item Journal Batch";
        RestrictedRecord: Record "Restricted Record";
    BegiN
        IF ItemJournalLine.ISTEMPORARY() THEN
            EXIT;

        IF IsItemJournalLineApprovalsWorkflowEnabled(ItemJournalLine) THEN BEGIN
            // message('%1', ItemJournalLine.RecordId());
            RestrictedRecord.RESET;
            RestrictedRecord.SetRange("Record ID", ItemJournalLine.RecordId());
            if not RestrictedRecord.IsEmpty() then
                Error('You can not modify/Add/Delete the record when record restriction is available.');
            RecordRest.RestrictRecordUsage(ItemJournalLine, RestrictLineUsageDetailsTxtLbl);
        END;
        /*IF IsItemJournalLineApprovalsWorkflowEnabled(ItemJournalLine) THEN
            RecordRest.RestrictRecordUsage(ItemJournalLine, RestrictLineUsageDetailsTxtLbl);
        RecordRest.RestrictRecordUsage(ItemJournalLine, RestrictLineUsageDetailsTxtLbl);*/
        
                    IF ItemJournalBatch.GET(ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name") THEN
                        IF IsItemJournalBatchApprovalsWorkflowEnabled(ItemJournalBatch) THEN
                            RecordRest.RestrictRecordUsage(ItemJournalLine, RestrictBatchUsageDetailsTxtLbl);
                    // RecordRest.RestrictRecordUsage(ItemJournalLine.RECORDID(), RestrictBatchUsageDetailsTxtLbl);
    END;
    //end;

    [EventSubscriber(ObjectType::Table, 83, 'OnAfterRenameEvent', '', false, false)]
    procedure UpdateItemJournalLineRestrictionsAfterRename(VAR Rec: Record "Item Journal Line"; VAR xRec: Record "Item Journal Line"; RunTrigger: Boolean)
    begin
        UpdateRestriction(Rec.RECORDID(), xRec.RECORDID());
    end;

    [EventSubscriber(ObjectType::Table, 233, 'OnBeforeDeleteEvent', '', false, false)]
    procedure RemoveItemJournalBatchRestrictionsBeforeDelete(VAR Rec: Record "Item Journal Batch"; RunTrigger: Boolean)
    begin
        AllowRecordUsage(Rec.RECORDID());
    end;

    [EventSubscriber(ObjectType::Table, 233, 'OnAfterRenameEvent', '', false, false)]
    procedure UpdateItemJournalBatchRestrictionsAfterRename(VAR Rec: Record "Item Journal Batch"; VAR xRec: Record "Item Journal Batch"; RunTrigger: Boolean)
    begin
        UpdateRestriction(Rec.RECORDID(), xRec.RECORDID());
    end;

    LOCAL procedure UpdateRestriction(RecID: RecordID; xRecID: RecordID)
    var
        RestrictedRecord: Record "Restricted Record";
    BEGIN
        RestrictedRecord.SETRANGE("Record ID", xRecID);
        RestrictedRecord.MODIFYALL("Record ID", RecID);
    END;

    LOCAL procedure AllowRecordUsage(RecID: RecordID)
    var
        RestrictedRecord: Record "Restricted Record";
    BEGIN
        RestrictedRecord.SETRANGE("Record ID", RecID);
        RestrictedRecord.DELETEALL(TRUE);
    END;


    //Prod BOM Approval


    [IntegrationEvent(false, false)]
    Procedure OnSendProdBOMForApproval(var ProdBOM: Record "Production BOM Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelProdBOMForApproval(var ProdBOM: Record "Production BOM Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendProdBOMforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendProdBOMforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendProdBOMForApproval', '', true, true)]
    local procedure RunworkflowonsendValueBaseForApproval(var ProdBOM: Record "Production BOM Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendProdBOMforApprovalCode(), ProdBOM);
    end;

    procedure RunworkflowOnCancelProdBOMforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelProdBOMForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelProdBOMForApproval', '', true, true)]

    local procedure RunworkflowonCancelProdBOMForApproval(var ProdBOM: Record "Production BOM Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelProdBOMforApprovalCode(), ProdBOM);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary2();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendProdBOMforApprovalCode(), DATABASE::"Production BOM Header",
          CopyStr(CheckProdBOMSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelProdBOMforApprovalCode(), DATABASE::"Production BOM Header",
          CopyStr(ProdBOMrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelProdBOMforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelProdBOMforApprovalCode(), RunworkflowOnSendProdBOMforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendProdBOMForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendProdBOMForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendProdBOMForApprovalCode());
        end;
    end;

    procedure ISProdBOMworkflowenabled(var ProdBOM: Record "Production BOM Header"): Boolean
    begin
        if ProdBOM."Approval Status" <> ProdBOM."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(ProdBOM, RunworkflowOnSendProdBOMforApprovalCode()));
    end;

    Procedure CheckProdBOMApprovalsWorkflowEnabled(VAR ProdBOM: Record "Production BOM Header"): Boolean
    begin
        IF not ISProdBOMworkflowenabled(ProdBOM) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument2(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        ProdBOM: Record "Production BOM Header";
    begin
        case RecRef.Number() of
            Database::"Production BOM Header":
                begin
                    RecRef.SetTable(ProdBOM);
                    ApprovalEntryArgument."Document No." := FORMAT(ProdBOM."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument(RecRef: RecordRef; var Handled: boolean)
    var
        ProdBOM: Record "Production BOM Header";
    begin
        case RecRef.Number() of
            Database::"Production BOM Header":
                begin
                    RecRef.SetTable(ProdBOM);
                    ProdBOM."Approval Status" := ProdBOM."Approval Status"::Open;
                    ProdBOM.VALIDATE(Status, ProdBOM.Status::"Under Development");
                    ProdBOM.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument(RecRef: RecordRef; var Handled: boolean)
    var
        ProdBOM: Record "Production BOM Header";
    begin
        case RecRef.Number() of
            Database::"Production BOM Header":
                begin
                    RecRef.SetTable(ProdBOM);
                    ProdBOM."Approval Status" := ProdBOM."Approval Status"::Released;
                    ProdBOM.Validate(Status, ProdBOM.Status::Certified);
                    ProdBOM.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval(RecRef: RecordRef; var IsHandled: boolean)
    var
        ProdBOM: Record "Production BOM Header";
    begin
        case RecRef.Number() of
            Database::"Production BOM Header":
                begin
                    RecRef.SetTable(ProdBOM);
                    ProdBOM."Approval Status" := ProdBOM."Approval Status"::"Pending for Approval";
                    ProdBOM.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendProdBOMforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendProdBOMforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelProdBOMforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelProdBOMforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(ProdBOMCategoryTxt, 1, 20), CopyStr(ProdBOMCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Production BOM Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate()
    begin
        InsertProdBOMApprovalworkflowtemplate();
    end;

    local procedure InsertProdBOMApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt2, 1, 17), CopyStr(ProdBOMApprWorkflowDescTxt, 1, 100), CopyStr(ProdBOMCategoryTxt, 1, 20));
        InsertProdBOMApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertProdBOMApprovalworkflowDetails(var workflow: record Workflow);
    var
        ProdBOM: Record "Production BOM Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildProdBOMtypecondition(ProdBOM."Approval Status"::open), RunworkflowOnSendProdBOMforApprovalCode(), BuildProdBOMtypecondition(ProdBOM."Approval Status"::"Pending for Approval"), RunworkflowOnCancelProdBOMforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildProdBOMtypecondition(status: integer): Text
    var
        ProdBOM: Record "Production BOM Header";
    Begin
        ProdBOM.SetRange("Approval Status", status);
        exit(StrSubstNo(ProdBOMTypeCondnTxt, workflowsetup.Encode(ProdBOM.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid2(RecordRef)
    end;

    local procedure GetConditionalcardPageid2(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Production BOM Header":
                exit(page::"Production BOM");
        end;
    end;

    var
        workflowsetup: codeunit "Workflow Setup";
        CheckProdBOMsendforapprovaleventdescTxt: Label 'Approval of a ProdBOM document is requested';
        ProdBOMrequestcanceleventdescTxt: Label 'Approval of a ProdBOM document is Cancelled';
        NoworkfloweableErr: Label 'No Approval workflow for this record type is enabled.';
        ProdBOMCategoryTxt: Label 'ProdBOM';
        ProdBOMCategoryDescTxt: Label 'ProdBOMDocuments';
        ProdBOMApprWorkflowDescTxt: Label 'ProdBOM Approval Workflow';
        IncDocOCRWorkflowCodeTxt: Label 'INCDOC-ProdBOM';
        ProdBOMTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="ProdBOM">%1</DataItem></DataItems></ReportParameters>';

    //Prod BOM Approval


    [IntegrationEvent(false, false)]
    Procedure OnSendProdBOMVerForApproval(var ProdBOMVer: Record "Production BOM Version")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelProdBOMVerForApproval(var ProdBOMVer: Record "Production BOM Version")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendProdBOMVerforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendProdBOMVerforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendProdBOMVerForApproval', '', true, true)]
    local procedure RunworkflowonsendProdBOMForApproval(var ProdBOMVer: Record "Production BOM Version")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendProdBOMVerforApprovalCode(), ProdBOMVer);
    end;

    procedure RunworkflowOnCancelProdBOMVerforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelProdBOMVerForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelProdBOMVerForApproval', '', true, true)]

    local procedure RunworkflowonCancelProdBOMVerForApproval(var ProdBOMVer: Record "Production BOM Version")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelProdBOMVerforApprovalCode(), ProdBOMVer);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary3();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendProdBOMVerforApprovalCode(), DATABASE::"Production BOM Version",
          CopyStr(CheckProdBOMVerSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelProdBOMVerforApprovalCode(), DATABASE::"Production BOM Version",
          CopyStr(ProdBOMVerrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary2(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelProdBOMVerforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelProdBOMVerforApprovalCode(), RunworkflowOnSendProdBOMVerforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendProdBOMVerForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendProdBOMVerForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendProdBOMVerForApprovalCode());
        end;
    end;

    procedure ISProdBOMVerworkflowenabled(var ProdBOMVer: Record "Production BOM Version"): Boolean
    begin
        if ProdBOMVer."Approval Status" <> ProdBOMVer."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(ProdBOMVer, RunworkflowOnSendProdBOMVerforApprovalCode()));
    end;

    Procedure CheckProdBOMVerApprovalsWorkflowEnabled(VAR ProdBOMVer: Record "Production BOM Version"): Boolean
    begin
        IF not ISProdBOMVerworkflowenabled(ProdBOMVer) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument3(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        ProdBOMVer: Record "Production BOM Version";
    begin
        case RecRef.Number() of
            Database::"Production BOM Version":
                begin
                    RecRef.SetTable(ProdBOMVer);
                    ApprovalEntryArgument."Document No." := FORMAT(ProdBOMVer."Production BOM No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument2(RecRef: RecordRef; var Handled: boolean)
    var
        ProdBOMVer: Record "Production BOM Version";
    begin
        case RecRef.Number() of
            Database::"Production BOM Version":
                begin
                    RecRef.SetTable(ProdBOMVer);
                    ProdBOMVer."Approval Status" := ProdBOMVer."Approval Status"::Open;
                    ProdBOMVer.VALIDATE(Status, ProdBOMVER.Status::"Under Development");
                    RecRef.SetTable(ProdBOMVer);
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument2(RecRef: RecordRef; var Handled: boolean)
    var
        ProdBOMVer: Record "Production BOM Version";
    begin
        case RecRef.Number() of
            Database::"Production BOM Version":
                begin
                    RecRef.SetTable(ProdBOMVer);
                    ProdBOMVer."Approval Status" := ProdBOMVer."Approval Status"::Released;
                    ProdBOMVer.Validate(Status, ProdBOMVer.Status::Certified);
                    ProdBOMVer.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval2(RecRef: RecordRef; var IsHandled: boolean)
    var
        ProdBOMVer: Record "Production BOM Version";
    begin
        case RecRef.Number() of
            Database::"Production BOM Version":
                begin
                    RecRef.SetTable(ProdBOMVer);
                    ProdBOMVer."Approval Status" := ProdBOMVer."Approval Status"::"Pending for Approval";
                    ProdBOMVer.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary2(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendProdBOMVerforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendProdBOMVerforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelProdBOMVerforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelProdBOMVerforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary2()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(ProdBOMVerCategoryTxt, 1, 20), CopyStr(ProdBOMVerCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations2()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Production BOM Version", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate2()
    begin
        InsertProdBOMVerApprovalworkflowtemplate();
    end;

    local procedure InsertProdBOMVerApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt3, 1, 17), CopyStr(ProdBOMVerApprWorkflowDescTxt, 1, 100), CopyStr(ProdBOMVerCategoryTxt, 1, 20));
        InsertProdBOMVerApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertProdBOMVerApprovalworkflowDetails(var workflow: record Workflow);
    var
        ProdBOMVer: Record "Production BOM Version";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildProdBOMVertypecondition(ProdBOMVer."Approval Status"::open), RunworkflowOnSendProdBOMVerforApprovalCode(), BuildProdBOMVertypecondition(ProdBOMVer."Approval Status"::"Pending for Approval"), RunworkflowOnCancelProdBOMVerforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildProdBOMVertypecondition(status: integer): Text
    var
        ProdBOMVer: Record "Production BOM Version";
    Begin
        ProdBOMVer.SetRange("Approval Status", status);
        exit(StrSubstNo(ProdBOMVerTypeCondnTxt, workflowsetup.Encode(ProdBOMVer.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid3(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid3(RecordRef)
    end;

    local procedure GetConditionalcardPageid3(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Production BOM Version":
                exit(page::"Production BOM");
        end;
    end;


    //COA Start
    [IntegrationEvent(false, false)]
    Procedure OnSendCOAForApproval(var GLAccount: Record "G/L Account")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCOAForApproval(var GLAccount: Record "G/L Account")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCOAforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCOAforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendCOAForApproval', '', true, true)]
    local procedure RunworkflowonsendCOAForApproval(var GLAccount: Record "G/L Account")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCOAforApprovalCode(), GLAccount);
    end;

    procedure RunworkflowOnCancelCOAforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCOAForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelCOAForApproval', '', true, true)]

    local procedure RunworkflowonCancelCOAForApproval(var GLAccount: Record "G/L Account")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCOAforApprovalCode(), GLAccount);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary4();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCOAforApprovalCode(), DATABASE::"G/L Account",
          CopyStr(CheckCOASendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCOAforApprovalCode(), DATABASE::"G/L Account",
          CopyStr(COArequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary3(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCOAforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCOAforApprovalCode(), RunworkflowOnSendCOAforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendCOAForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendCOAForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendCOAForApprovalCode());
        end;
    end;

    procedure ISCOAworkflowenabled(var GLAccount: Record "G/L Account"): Boolean
    begin
        if GLAccount."Approval Status" <> GLAccount."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(GLAccount, RunworkflowOnSendCOAforApprovalCode()));
    end;

    Procedure CheckCOAApprovalsWorkflowEnabled(VAR GLAccount: Record "G/L Account"): Boolean
    begin
        IF not ISCOAworkflowenabled(GLAccount) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument4(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        GLAccount: Record "G/L Account";
    begin
        case RecRef.Number() of
            Database::"G/L Account":
                begin
                    RecRef.SetTable(GLAccount);
                    ApprovalEntryArgument."Document No." := FORMAT(GLAccount."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument3(RecRef: RecordRef; var Handled: boolean)
    var
        GLAccount: Record "G/L Account";
    begin
        case RecRef.Number() of
            Database::"G/L Account":
                begin
                    RecRef.SetTable(GLAccount);
                    GLAccount."Approval Status" := GLAccount."Approval Status"::Open;
                    GLAccount.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument3(RecRef: RecordRef; var Handled: boolean)
    var
        GLAccount: Record "G/L Account";
    begin
        case RecRef.Number() of
            Database::"G/L Account":
                begin
                    RecRef.SetTable(GLAccount);
                    GLAccount."Approval Status" := GLAccount."Approval Status"::Released;
                    GLAccount.Blocked := FALSE;
                    //GLAccount.Validate(Status, GLAccount.Status::Certified); //PJ
                    GLAccount.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval3(RecRef: RecordRef; var IsHandled: boolean)
    var
        GLAccount: Record "G/L Account";
    begin
        case RecRef.Number() of
            Database::"G/L Account":
                begin
                    RecRef.SetTable(GLAccount);
                    GLAccount."Approval Status" := GLAccount."Approval Status"::"Pending for Approval";
                    GLAccount.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary3(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCOAforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCOAforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCOAforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCOAforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary3()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(COACategoryTxt, 1, 20), CopyStr(COACategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations3()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"G/L Account", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate3()
    begin
        InsertCOAApprovalworkflowtemplate();
    end;

    local procedure InsertCOAApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt4, 1, 17), CopyStr(COAApprWorkflowDescTxt, 1, 100), CopyStr(COACategoryTxt, 1, 20));
        InsertCOAApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCOAApprovalworkflowDetails(var workflow: record Workflow);
    var
        GLAccount: Record "G/L Account";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCOAtypecondition(GLAccount."Approval Status"::open), RunworkflowOnSendCOAforApprovalCode(), BuildCOAtypecondition(GLAccount."Approval Status"::"Pending for Approval"), RunworkflowOnCancelCOAforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCOAtypecondition(status: integer): Text
    var
        GLAccount: Record "G/L Account";
    Begin
        GLAccount.SetRange("Approval Status", status);
        exit(StrSubstNo(COATypeCondnTxt, workflowsetup.Encode(GLAccount.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid4(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid4(RecordRef)
    end;

    local procedure GetConditionalcardPageid4(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"G/L Account":
                exit(page::"G/L Account Card");
        end;
    end;
    //COA End

    //BankAcc Start
    [IntegrationEvent(false, false)]
    Procedure OnSendBankAccForApproval(var BankAcc: Record "Bank Account")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelBankAccForApproval(var BankAcc: Record "Bank Account")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendBankAccforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendBankAccforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendBankAccForApproval', '', true, true)]
    local procedure RunworkflowonsendBankAccForApproval(var BankAcc: Record "Bank Account")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendBankAccforApprovalCode(), BankAcc);
    end;

    procedure RunworkflowOnCancelBankAccforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelBankAccForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelBankAccForApproval', '', true, true)]

    local procedure RunworkflowonCancelBankAccForApproval(var BankAcc: Record "Bank Account")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelBankAccforApprovalCode(), BankAcc);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary5();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendBankAccforApprovalCode(), DATABASE::"Bank Account",
          CopyStr(CheckBankAccSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelBankAccforApprovalCode(), DATABASE::"Bank Account",
          CopyStr(BankAccrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary4(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelBankAccforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelBankAccforApprovalCode(), RunworkflowOnSendBankAccforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendBankAccForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendBankAccForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendBankAccForApprovalCode());
        end;
    end;

    procedure ISBankAccworkflowenabled(var BankAcc: Record "Bank Account"): Boolean
    begin
        if BankAcc."Approval Status" <> BankAcc."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(BankAcc, RunworkflowOnSendBankAccforApprovalCode()));
    end;

    Procedure CheckBankAccApprovalsWorkflowEnabled(VAR BankAcc: Record "Bank Account"): Boolean
    begin
        IF not ISBankAccworkflowenabled(BankAcc) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument5(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        BankAcc: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BankAcc);
                    ApprovalEntryArgument."Document No." := FORMAT(BankAcc."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument4(RecRef: RecordRef; var Handled: boolean)
    var
        BankAcc: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BankAcc);
                    BankAcc."Approval Status" := BankAcc."Approval Status"::Open;
                    BankAcc.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument4(RecRef: RecordRef; var Handled: boolean)
    var
        BankAcc: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BankAcc);
                    BankAcc."Approval Status" := BankAcc."Approval Status"::Released;
                    //GLAccount.Validate(Status, GLAccount.Status::Certified); //PJ
                    bankAcc.Blocked := FALSE;
                    BankAcc.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval4(RecRef: RecordRef; var IsHandled: boolean)
    var
        BankAcc: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BankAcc);
                    BankAcc."Approval Status" := BankAcc."Approval Status"::"Pending for Approval";
                    BankAcc.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary4(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendBankAccforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendBankAccforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelBankAccforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelBankAccforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary4()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(BankAccCategoryTxt, 1, 20), CopyStr(BankAccCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations4()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Bank Account", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate4()
    begin
        InsertBankAccApprovalworkflowtemplate();
    end;

    local procedure InsertBankAccApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt6, 1, 17), CopyStr(BankAccApprWorkflowDescTxt, 1, 100), CopyStr(BankAccCategoryTxt, 1, 20));
        InsertBankAccApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertBankAccApprovalworkflowDetails(var workflow: record Workflow);
    var
        BankAcc: Record "Bank Account";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildBankAcctypecondition(BankAcc."Approval Status"::open), RunworkflowOnSendBankAccforApprovalCode(), BuildBankAcctypecondition(BankAcc."Approval Status"::"Pending for Approval"), RunworkflowOnCancelBankAccforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildBankAcctypecondition(status: integer): Text
    var
        BankAcc: Record "Bank Account";
    Begin
        BankAcc.SetRange("Approval Status", status);
        exit(StrSubstNo(BankAccTypeCondnTxt, workflowsetup.Encode(BankAcc.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid5(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid5(RecordRef)
    end;

    local procedure GetConditionalcardPageid5(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Bank Account":
                exit(page::"Bank Account Card");
        end;
    end;
    //BankAcc End

    //BankAccRec Start
    [IntegrationEvent(false, false)]
    Procedure OnSendBankAccRecForApproval(var BankAccRec: Record "Bank Acc. Reconciliation")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelBankAccRecForApproval(var BankAccRec: Record "Bank Acc. Reconciliation")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendBankAccRecforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendBankAccRecforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendBankAccRecForApproval', '', true, true)]
    local procedure RunworkflowonsendBankAccRecForApproval(var BankAccRec: Record "Bank Acc. Reconciliation")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendBankAccRecforApprovalCode(), BankAccRec);
    end;

    procedure RunworkflowOnCancelBankAccRecforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelBankAccRecForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelBankAccRecForApproval', '', true, true)]

    local procedure RunworkflowonCancelBankAccRecForApproval(var BankAccRec: Record "Bank Acc. Reconciliation")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelBankAccRecforApprovalCode(), BankAccRec);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary6();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendBankAccRecforApprovalCode(), DATABASE::"Bank Acc. Reconciliation",
          CopyStr(CheckBankAccRecSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelBankAccRecforApprovalCode(), DATABASE::"Bank Acc. Reconciliation",
          CopyStr(BankAccRecrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary5(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelBankAccRecforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelBankAccRecforApprovalCode(), RunworkflowOnSendBankAccRecforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendBankAccRecForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendBankAccRecForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendBankAccRecForApprovalCode());
        end;
    end;

    procedure ISBankAccRecworkflowenabled(var BankAccRec: Record "Bank Acc. Reconciliation"): Boolean
    begin
        if BankAccRec."Approval Status" <> BankAccRec."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(BankAccRec, RunworkflowOnSendBankAccRecforApprovalCode()));
    end;

    Procedure CheckBankAccRecApprovalsWorkflowEnabled(VAR BankAccRec: Record "Bank Acc. Reconciliation"): Boolean
    begin
        IF not ISBankAccRecworkflowenabled(BankAccRec) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument6(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        BankAccRec: Record "Bank Acc. Reconciliation";
    begin
        case RecRef.Number() of
            Database::"Bank Acc. Reconciliation":
                begin
                    RecRef.SetTable(BankAccRec);
                    ApprovalEntryArgument."Document No." := FORMAT(BankAccRec."Bank Account No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument5(RecRef: RecordRef; var Handled: boolean)
    var
        BankAccRec: Record "Bank Acc. Reconciliation";
    begin
        case RecRef.Number() of
            Database::"Bank Acc. Reconciliation":
                begin
                    RecRef.SetTable(BankAccRec);
                    BankAccRec."Approval Status" := BankAccRec."Approval Status"::Open;
                    BankAccRec.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument5(RecRef: RecordRef; var Handled: boolean)
    var
        BankAccRec: Record "Bank Acc. Reconciliation";
    begin
        case RecRef.Number() of
            Database::"Bank Acc. Reconciliation":
                begin
                    RecRef.SetTable(BankAccRec);
                    BankAccRec."Approval Status" := BankAccRec."Approval Status"::Released;

                    //GLAccount.Validate(Status, GLAccount.Status::Certified); //PJ
                    BankAccRec.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval5(RecRef: RecordRef; var IsHandled: boolean)
    var
        BankAccRec: Record "Bank Acc. Reconciliation";
    begin
        case RecRef.Number() of
            Database::"Bank Acc. Reconciliation":
                begin
                    RecRef.SetTable(BankAccRec);
                    BankAccRec."Approval Status" := BankAccRec."Approval Status"::"Pending for Approval";
                    BankAccRec.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary5(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendBankAccRecforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendBankAccRecforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelBankAccRecforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelBankAccRecforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary5()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(BankAccRecCategoryTxt, 1, 20), CopyStr(BankAccRecCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations5()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Bank Acc. Reconciliation", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate5()
    begin
        InsertBankAccRecApprovalworkflowtemplate();
    end;

    local procedure InsertBankAccRecApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt7, 1, 17), CopyStr(BankAccRecApprWorkflowDescTxt, 1, 100), CopyStr(BankAccRecCategoryTxt, 1, 20));
        InsertBankAccRecApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertBankAccRecApprovalworkflowDetails(var workflow: record Workflow);
    var
        BankAccRec: Record "Bank Acc. Reconciliation";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildBankAccRectypecondition(BankAccRec."Approval Status"::open), RunworkflowOnSendBankAccRecforApprovalCode(), BuildBankAccRectypecondition(BankAccRec."Approval Status"::"Pending for Approval"), RunworkflowOnCancelBankAccRecforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildBankAccRectypecondition(status: integer): Text
    var
        BankAccRec: Record "Bank Acc. Reconciliation";
    Begin
        BankAccRec.SetRange("Approval Status", status);
        exit(StrSubstNo(BankAccRecTypeCondnTxt, workflowsetup.Encode(BankAccRec.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid6(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Bank Acc. Reconciliation" then
            PageID := GetConditionalcardPageid6(RecordRef)
    end;

    local procedure GetConditionalcardPageid6(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Bank Acc. Reconciliation":
                exit(page::"Bank Acc. Reconciliation-PK");
        end;
    end;
    //BankAccRec End


    //GL Budget Start
    [IntegrationEvent(false, false)]
    Procedure OnSendGLBudgetForApproval(var GLBudgetName: Record "G/L Budget Name")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelGLBudgetForApproval(var GLBudgetName: Record "G/L Budget Name")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendGLBudgetforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendGLBudgetforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendGLBudgetForApproval', '', true, true)]
    local procedure RunworkflowonsendGLBudgetForApproval(var GLBudgetName: Record "G/L Budget Name")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendGLBudgetforApprovalCode(), GLBudgetName);
    end;

    procedure RunworkflowOnCancelGLBudgetforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelGLBudgetForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelGLBudgetForApproval', '', true, true)]

    local procedure RunworkflowonCancelGLBudgetForApproval(var GLBudgetName: Record "G/L Budget Name")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelGLBudgetforApprovalCode(), GLBudgetName);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary7();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendGLBudgetforApprovalCode(), DATABASE::"G/L Budget Name",
          CopyStr(CheckGLBudgetSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelGLBudgetforApprovalCode(), DATABASE::"G/L Budget Name",
          CopyStr(GLBudgetrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary6(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelGLBudgetforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelGLBudgetforApprovalCode(), RunworkflowOnSendGLBudgetforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendGLBudgetForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendGLBudgetForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendGLBudgetForApprovalCode());
        end;
    end;

    procedure ISGLBudgetworkflowenabled(var GLBudgetName: Record "G/L Budget Name"): Boolean
    begin
        if GLBudgetName."Approval Status" <> GLBudgetName."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(GLBudgetName, RunworkflowOnSendGLBudgetforApprovalCode()));
    end;

    Procedure CheckGLBudgetApprovalsWorkflowEnabled(VAR GLBudgetName: Record "G/L Budget Name"): Boolean
    begin
        IF not ISGLBudgetworkflowenabled(GLBudgetName) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument7(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        GLBudgetName: Record "G/L Budget Name";
    begin
        case RecRef.Number() of
            Database::"G/L Budget Name":
                begin
                    RecRef.SetTable(GLBudgetName);
                    ApprovalEntryArgument."Document No." := FORMAT(GLBudgetName.Name);
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument6(RecRef: RecordRef; var Handled: boolean)
    var
        GLBudgetName: Record "G/L Budget Name";
    begin
        case RecRef.Number() of
            Database::"G/L Budget Name":
                begin
                    RecRef.SetTable(GLBudgetName);
                    GLBudgetName."Approval Status" := GLBudgetName."Approval Status"::Open;
                    GLBudgetName.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument6(RecRef: RecordRef; var Handled: boolean)
    var
        GLBudgetName: Record "G/L Budget Name";
    begin
        case RecRef.Number() of
            Database::"G/L Budget Name":
                begin
                    RecRef.SetTable(GLBudgetName);
                    GLBudgetName."Approval Status" := GLBudgetName."Approval Status"::Released;
                    GLBudgetName.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval6(RecRef: RecordRef; var IsHandled: boolean)
    var
        GLBudgetName: Record "G/L Budget Name";
    begin
        case RecRef.Number() of
            Database::"G/L Budget Name":
                begin
                    RecRef.SetTable(GLBudgetName);
                    GLBudgetName."Approval Status" := GLBudgetName."Approval Status"::"Pending for Approval";
                    GLBudgetName.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary6(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendGLBudgetforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendGLBudgetforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelGLBudgetforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelGLBudgetforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary6()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(GLBudgetCategoryTxt, 1, 20), CopyStr(GLBudgetCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations6()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"G/L Budget Name", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate6()
    begin
        InsertGLBudgetApprovalworkflowtemplate();
    end;

    local procedure InsertGLBudgetApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt8, 1, 17), CopyStr(GLBudgetApprWorkflowDescTxt, 1, 100), CopyStr(GLBudgetCategoryTxt, 1, 20));
        InsertGLBudgetApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertGLBudgetApprovalworkflowDetails(var workflow: record Workflow);
    var
        GLBudgetName: Record "G/L Budget Name";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildGLBudgettypecondition(GLBudgetName."Approval Status"::open), RunworkflowOnSendGLBudgetforApprovalCode(), BuildGLBudgettypecondition(GLBudgetName."Approval Status"::"Pending for Approval"), RunworkflowOnCancelGLBudgetforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildGLBudgettypecondition(status: integer): Text
    var
        GLBudgetName: Record "G/L Budget Name";
    Begin
        GLBudgetName.SetRange("Approval Status", status);
        exit(StrSubstNo(GLBudgetTypeCondnTxt, workflowsetup.Encode(GLBudgetName.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid7(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid7(RecordRef)
    end;

    local procedure GetConditionalcardPageid7(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"G/L Budget Name":
                exit(page::"G/L Budget Names");
        end;
    end;
    //GL Budget End

    //ItemSalesDiscQty Start
    [IntegrationEvent(false, false)]
    Procedure OnSendItemSalesDiscQtyForApproval(var ItemSalesDiscQty: Record "Item Sales Disc. Qty.")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelItemSalesDiscQtyForApproval(var ItemSalesDiscQty: Record "Item Sales Disc. Qty.")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendItemSalesDiscQtyforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendItemSalesDiscQtyforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendItemSalesDiscQtyForApproval', '', true, true)]
    local procedure RunworkflowonsendItemSalesDiscQtyForApproval(var ItemSalesDiscQty: Record "Item Sales Disc. Qty.")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendItemSalesDiscQtyforApprovalCode(), ItemSalesDiscQty);
    end;

    procedure RunworkflowOnCancelItemSalesDiscQtyforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelItemSalesDiscQtyForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelItemSalesDiscQtyForApproval', '', true, true)]

    local procedure RunworkflowonCancelItemSalesDiscQtyForApproval(var ItemSalesDiscQty: Record "Item Sales Disc. Qty.")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelItemSalesDiscQtyforApprovalCode(), ItemSalesDiscQty);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary8();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendItemSalesDiscQtyforApprovalCode(), DATABASE::"Item Sales Disc. Qty.",
          CopyStr(CheckItemSalesDiscQtySendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelItemSalesDiscQtyforApprovalCode(), DATABASE::"Item Sales Disc. Qty.",
          CopyStr(ItemSalesDiscQtyrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary7(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelItemSalesDiscQtyforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelItemSalesDiscQtyforApprovalCode(), RunworkflowOnSendItemSalesDiscQtyforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendItemSalesDiscQtyForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendItemSalesDiscQtyForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendItemSalesDiscQtyForApprovalCode());
        end;
    end;

    procedure ISItemSalesDiscQtyworkflowenabled(var ItemSalesDiscQty: Record "Item Sales Disc. Qty."): Boolean
    begin
        if ItemSalesDiscQty.Status <> ItemSalesDiscQty.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(ItemSalesDiscQty, RunworkflowOnSendItemSalesDiscQtyforApprovalCode()));
    end;

    Procedure CheckItemSalesDiscQtyApprovalsWorkflowEnabled(VAR ItemSalesDiscQty: Record "Item Sales Disc. Qty."): Boolean
    begin
        IF not ISItemSalesDiscQtyworkflowenabled(ItemSalesDiscQty) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument8(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        ItemSalesDiscQty: Record "Item Sales Disc. Qty.";
    begin
        case RecRef.Number() of
            Database::"Item Sales Disc. Qty.":
                begin
                    RecRef.SetTable(ItemSalesDiscQty);
                    ApprovalEntryArgument."Document No." := FORMAT(ItemSalesDiscQty."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument7(RecRef: RecordRef; var Handled: boolean)
    var
        ItemSalesDiscQty: Record "Item Sales Disc. Qty.";
    begin
        case RecRef.Number() of
            Database::"Item Sales Disc. Qty.":
                begin
                    RecRef.SetTable(ItemSalesDiscQty);
                    ItemSalesDiscQty.Status := ItemSalesDiscQty.Status::Open;
                    ItemSalesDiscQty.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument7(RecRef: RecordRef; var Handled: boolean)
    var
        ItemSalesDiscQty: Record "Item Sales Disc. Qty.";
    begin
        case RecRef.Number() of
            Database::"Item Sales Disc. Qty.":
                begin
                    RecRef.SetTable(ItemSalesDiscQty);
                    ItemSalesDiscQty.Status := ItemSalesDiscQty.Status::Released;
                    ItemSalesDiscQty.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval7(RecRef: RecordRef; var IsHandled: boolean)
    var
        ItemSalesDiscQty: Record "Item Sales Disc. Qty.";
    begin
        case RecRef.Number() of
            Database::"Item Sales Disc. Qty.":
                begin
                    RecRef.SetTable(ItemSalesDiscQty);
                    ItemSalesDiscQty.Status := ItemSalesDiscQty.Status::"Pending Approval";
                    ItemSalesDiscQty.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary7(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendItemSalesDiscQtyforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendItemSalesDiscQtyforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelItemSalesDiscQtyforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelItemSalesDiscQtyforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary7()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(ItemSalesDiscQtyCategoryTxt, 1, 20), CopyStr(ItemSalesDiscQtyCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations7()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Item Sales Disc. Qty.", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate7()
    begin
        InsertItemSalesDiscQtyApprovalworkflowtemplate();
    end;

    local procedure InsertItemSalesDiscQtyApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt9, 1, 17), CopyStr(ItemSalesDiscQtyApprWorkflowDescTxt, 1, 100), CopyStr(ItemSalesDiscQtyCategoryTxt, 1, 20));
        InsertItemSalesDiscQtyApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertItemSalesDiscQtyApprovalworkflowDetails(var workflow: record Workflow);
    var
        ItemSalesDiscQty: Record "Item Sales Disc. Qty.";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildItemSalesDiscQtytypecondition(ItemSalesDiscQty.Status::open), RunworkflowOnSendItemSalesDiscQtyforApprovalCode(), BuildItemSalesDiscQtytypecondition(ItemSalesDiscQty.Status::"Pending Approval"), RunworkflowOnCancelItemSalesDiscQtyforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildItemSalesDiscQtytypecondition(status: integer): Text
    var
        ItemSalesDiscQty: Record "Item Sales Disc. Qty.";
    Begin
        ItemSalesDiscQty.SetRange(status, status);
        exit(StrSubstNo(ItemSalesDiscQtyTypeCondnTxt, workflowsetup.Encode(ItemSalesDiscQty.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid8(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid8(RecordRef)
    end;

    local procedure GetConditionalcardPageid8(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Item Sales Disc. Qty.":
                exit(page::"Item Sales Disc. Quantity");
        end;
    end;
    //ItemSalesDiscQty End

    //FADisposal Start
    [IntegrationEvent(false, false)]
    Procedure OnSendFADisposalForApproval(var FADisposal: Record "FA Disposal")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelFADisposalForApproval(var FADisposal: Record "FA Disposal")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendFADisposalforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendFADisposalforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendFADisposalForApproval', '', true, true)]
    local procedure RunworkflowonsendFADisposalForApproval(var FADisposal: Record "FA Disposal")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendFADisposalforApprovalCode(), FADisposal);
    end;

    procedure RunworkflowOnCancelFADisposalforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelFADisposalForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelFADisposalForApproval', '', true, true)]

    local procedure RunworkflowonCancelFADisposalForApproval(var FADisposal: Record "FA Disposal")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelFADisposalforApprovalCode(), FADisposal);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary9();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendFADisposalforApprovalCode(), DATABASE::"FA Disposal",
          CopyStr(CheckFADisposalSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelFADisposalforApprovalCode(), DATABASE::"FA Disposal",
          CopyStr(FADisposalrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary8(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelFADisposalforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelFADisposalforApprovalCode(), RunworkflowOnSendFADisposalforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendFADisposalForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendFADisposalForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendFADisposalForApprovalCode());
        end;
    end;

    procedure ISFADisposalworkflowenabled(var FADisposal: Record "FA Disposal"): Boolean
    begin
        if FADisposal.Status <> FADisposal.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(FADisposal, RunworkflowOnSendFADisposalforApprovalCode()));
    end;

    Procedure CheckFADisposalApprovalsWorkflowEnabled(VAR FADisposal: Record "FA Disposal"): Boolean
    begin
        IF not ISFADisposalworkflowenabled(FADisposal) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument9(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        FADisposal: Record "FA Disposal";
    begin
        case RecRef.Number() of
            Database::"FA Disposal":
                begin
                    RecRef.SetTable(FADisposal);
                    ApprovalEntryArgument."Document No." := FORMAT(FADisposal."FA No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument8(RecRef: RecordRef; var Handled: boolean)
    var
        FADisposal: Record "FA Disposal";
    begin
        case RecRef.Number() of
            Database::"FA Disposal":
                begin
                    RecRef.SetTable(FADisposal);
                    FADisposal.Status := FADisposal.Status::Open;
                    FADisposal.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument8(RecRef: RecordRef; var Handled: boolean)
    var
        FADisposal: Record "FA Disposal";
    begin
        case RecRef.Number() of
            Database::"FA Disposal":
                begin
                    RecRef.SetTable(FADisposal);
                    FADisposal.Status := FADisposal.Status::Released;
                    FADisposal.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval8(RecRef: RecordRef; var IsHandled: boolean)
    var
        FADisposal: Record "FA Disposal";
    begin
        case RecRef.Number() of
            Database::"FA Disposal":
                begin
                    RecRef.SetTable(FADisposal);
                    FADisposal.Status := FADisposal.Status::"Pending Approval";
                    FADisposal.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary8(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendFADisposalforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendFADisposalforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelFADisposalforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelFADisposalforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary8()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(FADisposalCategoryTxt, 1, 20), CopyStr(FADisposalCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations8()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"FA Disposal", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate8()
    begin
        InsertFADisposalApprovalworkflowtemplate();
    end;

    local procedure InsertFADisposalApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt10, 1, 17), CopyStr(FADisposalApprWorkflowDescTxt, 1, 100), CopyStr(FADisposalCategoryTxt, 1, 20));
        InsertFADisposalApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertFADisposalApprovalworkflowDetails(var workflow: record Workflow);
    var
        FADisposal: Record "FA Disposal";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildFADisposaltypecondition(FADisposal.Status::open), RunworkflowOnSendFADisposalforApprovalCode(), BuildFADisposaltypecondition(FADisposal.Status::"Pending Approval"), RunworkflowOnCancelFADisposalforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildFADisposaltypecondition(status: integer): Text
    var
        FADisposal: Record "FA Disposal";
    Begin
        FADisposal.SetRange(status, status);
        exit(StrSubstNo(FADisposalTypeCondnTxt, workflowsetup.Encode(FADisposal.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid9(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid9(RecordRef)
    end;

    local procedure GetConditionalcardPageid9(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"FA Disposal":
                exit(page::"FA Disposal");
        end;
    end;
    //FADisposal End

    //FAMovReg Start
    [IntegrationEvent(false, false)]
    Procedure OnSendFAMovRegForApproval(var FAMovReg: Record "FA Movement Register")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelFAMovRegForApproval(var FAMovReg: Record "FA Movement Register")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendFAMovRegforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendFAMovRegforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendFAMovRegForApproval', '', true, true)]
    local procedure RunworkflowonsendFAMovRegForApproval(var FAMovReg: Record "FA Movement Register")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendFAMovRegforApprovalCode(), FAMovReg);
    end;

    procedure RunworkflowOnCancelFAMovRegforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelFAMovRegForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelFAMovRegForApproval', '', true, true)]

    local procedure RunworkflowonCancelFAMovRegForApproval(var FAMovReg: Record "FA Movement Register")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelFAMovRegforApprovalCode(), FAMovReg);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary10();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendFAMovRegforApprovalCode(), DATABASE::"FA Movement Register",
          CopyStr(CheckFAMovRegSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelFAMovRegforApprovalCode(), DATABASE::"FA Movement Register",
          CopyStr(FAMovRegrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary9(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelFAMovRegforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelFAMovRegforApprovalCode(), RunworkflowOnSendFAMovRegforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendFAMovRegForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendFAMovRegForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendFAMovRegForApprovalCode());
        end;
    end;

    procedure ISFAMovRegworkflowenabled(var FAMovReg: Record "FA Movement Register"): Boolean
    begin
        if FAMovReg."Approval Status" <> FAMovReg."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(FAMovReg, RunworkflowOnSendFAMovRegforApprovalCode()));
    end;

    Procedure CheckFAMovRegApprovalsWorkflowEnabled(VAR FAMovReg: Record "FA Movement Register"): Boolean
    begin
        IF not ISFAMovRegworkflowenabled(FAMovReg) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument10(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        FAMovReg: Record "FA Movement Register";
    begin
        case RecRef.Number() of
            Database::"FA Movement Register":
                begin
                    RecRef.SetTable(FAMovReg);
                    ApprovalEntryArgument."Document No." := FORMAT(FAMovReg."Document No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument9(RecRef: RecordRef; var Handled: boolean)
    var
        FAMovReg: Record "FA Movement Register";
    begin
        case RecRef.Number() of
            Database::"FA Movement Register":
                begin
                    RecRef.SetTable(FAMovReg);
                    FAMovReg."Approval Status" := FAMovReg."Approval Status"::Open;
                    FAMovReg.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument9(RecRef: RecordRef; var Handled: boolean)
    var
        FAMovReg: Record "FA Movement Register";
    begin
        case RecRef.Number() of
            Database::"FA Movement Register":
                begin
                    RecRef.SetTable(FAMovReg);
                    FAMovReg."Approval Status" := FAMovReg."Approval Status"::Released;
                    FAMovReg.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval9(RecRef: RecordRef; var IsHandled: boolean)
    var
        FAMovReg: Record "FA Movement Register";
    begin
        case RecRef.Number() of
            Database::"FA Movement Register":
                begin
                    RecRef.SetTable(FAMovReg);
                    FAMovReg."Approval Status" := FAMovReg."Approval Status"::"Pending for Approval";
                    FAMovReg.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary9(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendFAMovRegforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendFAMovRegforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelFAMovRegforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelFAMovRegforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary9()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(FAMovRegCategoryTxt, 1, 20), CopyStr(FAMovRegCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations9()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"FA Movement Register", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate9()
    begin
        InsertFAMovRegApprovalworkflowtemplate();
    end;

    local procedure InsertFAMovRegApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt11, 1, 17), CopyStr(FAMovRegApprWorkflowDescTxt, 1, 100), CopyStr(FAMovRegCategoryTxt, 1, 20));
        InsertFAMovRegApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertFAMovRegApprovalworkflowDetails(var workflow: record Workflow);
    var
        FAMovReg: Record "FA Movement Register";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildFAMovRegtypecondition(FAMovReg."Approval Status"::open), RunworkflowOnSendFAMovRegforApprovalCode(), BuildFAMovRegtypecondition(FAMovReg."Approval Status"::"Pending for Approval"), RunworkflowOnCancelFAMovRegforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildFAMovRegtypecondition(status: integer): Text
    var
        FAMovReg: Record "FA Movement Register";
    Begin
        FAMovReg.SetRange("Approval Status", status);
        exit(StrSubstNo(FAMovRegTypeCondnTxt, workflowsetup.Encode(FAMovReg.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid10(RecordRef: RecordRef; var PageID: Integer)
    begin
        /*if PageID = 0 then
            PageID := GetConditionalcardPageid9(RecordRef)*///PK
        if PageID = 0 then
            PageID := GetConditionalcardPageid10(RecordRef)
    end;

    local procedure GetConditionalcardPageid10(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"FA Movement Register":
                exit(page::"FA Movement Register");
        end;
    end;
    //FAMovReg End

    //JournalVoucher Start
    [IntegrationEvent(false, false)]
    Procedure OnSendJournalVoucherForApproval(var JournalVoucher: Record "Voucher Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelJournalVoucherForApproval(var JournalVoucher: Record "Voucher Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendJournalVoucherforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendJournalVoucherforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendJournalVoucherForApproval', '', true, true)]
    local procedure RunworkflowonsendJournalVoucherForApproval(var JournalVoucher: Record "Voucher Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendJournalVoucherforApprovalCode(), JournalVoucher);
    end;

    procedure RunworkflowOnCancelJournalVoucherforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelJournalVoucherForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelJournalVoucherForApproval', '', true, true)]

    local procedure RunworkflowonCancelJournalVoucherForApproval(var JournalVoucher: Record "Voucher Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelJournalVoucherforApprovalCode(), JournalVoucher);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary11();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendJournalVoucherforApprovalCode(), DATABASE::"Voucher Header",
          CopyStr(CheckJournalVoucherSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelJournalVoucherforApprovalCode(), DATABASE::"Voucher Header",
          CopyStr(JournalVoucherrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary10(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelJournalVoucherforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelJournalVoucherforApprovalCode(), RunworkflowOnSendJournalVoucherforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendJournalVoucherForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendJournalVoucherForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendJournalVoucherForApprovalCode());
        end;
    end;

    procedure ISJournalVoucherworkflowenabled(var JournalVoucher: Record "Voucher Header"): Boolean
    begin
        if JournalVoucher.Status <> JournalVoucher.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(JournalVoucher, RunworkflowOnSendJournalVoucherforApprovalCode()));
    end;

    Procedure CheckJournalVoucherApprovalsWorkflowEnabled(VAR JournalVoucher: Record "Voucher Header"): Boolean
    begin
        IF not ISJournalVoucherworkflowenabled(JournalVoucher) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument11(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        JournalVoucher: Record "Voucher Header";
    begin
        case RecRef.Number() of
            Database::"Voucher Header":
                begin
                    RecRef.SetTable(JournalVoucher);
                    ApprovalEntryArgument."Document No." := FORMAT(JournalVoucher."Document No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument10(RecRef: RecordRef; var Handled: boolean)
    var
        JournalVoucher: Record "Voucher Header";
    begin
        case RecRef.Number() of
            Database::"Voucher Header":
                begin
                    RecRef.SetTable(JournalVoucher);
                    JournalVoucher.Status := JournalVoucher.Status::Open;
                    JournalVoucher.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument10(RecRef: RecordRef; var Handled: boolean)
    var
        JournalVoucher: Record "Voucher Header";
    begin
        case RecRef.Number() of
            Database::"Voucher Header":
                begin
                    RecRef.SetTable(JournalVoucher);
                    JournalVoucher.Status := JournalVoucher.Status::Released;
                    JournalVoucher.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval10(RecRef: RecordRef; var IsHandled: boolean)
    var
        JournalVoucher: Record "Voucher Header";
    begin
        case RecRef.Number() of
            Database::"Voucher Header":
                begin
                    RecRef.SetTable(JournalVoucher);
                    JournalVoucher.Status := JournalVoucher.Status::"Pending for Approval";
                    JournalVoucher.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary10(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendJournalVoucherforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendJournalVoucherforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelJournalVoucherforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelJournalVoucherforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary10()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(JournalVoucherCategoryTxt, 1, 20), CopyStr(JournalVoucherCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations10()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Voucher Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate10()
    begin
        InsertJournalVoucherApprovalworkflowtemplate();
    end;

    local procedure InsertJournalVoucherApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt12, 1, 17), CopyStr(JournalVoucherApprWorkflowDescTxt, 1, 100), CopyStr(JournalVoucherCategoryTxt, 1, 20));
        InsertJournalVoucherApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertJournalVoucherApprovalworkflowDetails(var workflow: record Workflow);
    var
        JournalVoucher: Record "Voucher Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildJournalVouchertypecondition(JournalVoucher.Status::open), RunworkflowOnSendJournalVoucherforApprovalCode(), BuildJournalVouchertypecondition(JournalVoucher.Status::"Pending for Approval"), RunworkflowOnCancelJournalVoucherforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildJournalVouchertypecondition(status: integer): Text
    var
        JournalVoucher: Record "Voucher Header";
    Begin
        JournalVoucher.SetRange(status, status);
        exit(StrSubstNo(JournalVoucherTypeCondnTxt, workflowsetup.Encode(JournalVoucher.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid11(RecordRef: RecordRef; var PageID: Integer)
    begin
        //PKON805>>
        /*if PageID = 0 then
            PageID := GetConditionalcardPageid9(RecordRef)*/
        //if PageID = 0 then
        Case RecordRef.Number() of
            database::"Voucher Header":
                PageID := GetConditionalcardPageid11(RecordRef)
        end;
        //PKON805<<
    end;

    local procedure GetConditionalcardPageid11(RecordRef: RecordRef): Integer
    var
        VoucherHeader: Record "Voucher Header";
    begin
        Case RecordRef.Number() of
            database::"Voucher Header":
                begin
                    RecordRef.SetTable(VoucherHeader);
                    if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::JV) and (VoucherHeader.Status <> VoucherHeader.Status::Released)
                        and ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::" ") or ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::Bank))) then
                        exit(Page::"Bank Journal Voucher")
                    else
                        if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::BPV) and (VoucherHeader.Status <> VoucherHeader.Status::Released)
                            and (VoucherHeader."Multiple Batch" = false) then
                            exit(Page::"Bank Payment Voucher New")
                        else
                            if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::BRV) and (VoucherHeader.Status <> VoucherHeader.Status::Released)
                               and ((VoucherHeader."Bank Receipt Type" = VoucherHeader."Bank Receipt Type"::" ") or ((VoucherHeader."Bank Receipt Type" = VoucherHeader."Bank Receipt Type"::Indirect))) then
                                exit(Page::"Bank Receipt Voucher New")
                            else
                                if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::CPV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) then
                                    exit(Page::"Cash Payment Voucher New")
                                else
                                    if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::CRV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) then
                                        exit(Page::"Cash Receipt Voucher New")
                                    else
                                        if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::JV) and (VoucherHeader.Status <> VoucherHeader.Status::Released)
                                        and ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::" ") or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::General) and not VoucherHeader."Branch CPV") then
                                            exit(Page::"Journal Voucher")
                                        else
                                            if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::JV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::" ") or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::Sales)) then
                                                exit(Page::"Sales Journal Voucher")
                                            else
                                                if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::JV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::" ") or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::Purchase)) then
                                                    exit(Page::"Purchase Journal Voucher")
                                                else
                                                    if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::JV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::" ")
                                                    or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::General) or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::BRJV) or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::LBSV)) and (VoucherHeader."Branch CPV") then
                                                        exit(Page::"Branch Cash Voucher")

                    /*
                    if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::BRV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and not VoucherHeader."Multiple Batch" then
                        exit(Page::"Bank Payment Voucher New")
                    else
                        if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::JV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and ((VoucherHeader."JV Type" = VoucherHeader."JV Type"::" ") or (VoucherHeader."JV Type" = VoucherHeader."JV Type"::Bank)) then
                            exit(Page::"Bank Journal Voucher")
                        else
                            if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::BRV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and ((VoucherHeader."Bank Receipt Type" = VoucherHeader."Bank Receipt Type"::Indirect) or (VoucherHeader."Bank Receipt Type" = VoucherHeader."Bank Receipt Type"::" ")) then
                                exit(page::"Bank Receipt Voucher New")
                            else
                                if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::BPV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and not VoucherHeader."Multiple Batch" then
                                    exit(page::"Bank Receipt Voucher New")
                                else
                                    if (VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type"::BPV) and (VoucherHeader.Status <> VoucherHeader.Status::Released) and not VoucherHeader."Multiple Batch" then
                                        exit(page::"Bank Receipt Voucher New");*/
                end;


        end;
    end;
    //JournalVoucher End

    //MDT Start
    [IntegrationEvent(false, false)]
    Procedure OnSendMDTForApproval(var MDT: Record "Master Data Template")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelMDTForApproval(var MDT: Record "Master Data Template")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendMDTforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendMDTforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendMDTForApproval', '', true, true)]
    local procedure RunworkflowonsendMDTForApproval(var MDT: Record "Master Data Template")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendMDTforApprovalCode(), MDT);
    end;

    procedure RunworkflowOnCancelMDTforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelMDTForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelMDTForApproval', '', true, true)]

    local procedure RunworkflowonCancelMDTForApproval(var MDT: Record "Master Data Template")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelMDTforApprovalCode(), MDT);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary12();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendMDTforApprovalCode(), DATABASE::"Master Data Template",
          CopyStr(CheckMDTSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelMDTforApprovalCode(), DATABASE::"Master Data Template",
          CopyStr(MDTrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary11(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelMDTforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelMDTforApprovalCode(), RunworkflowOnSendMDTforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendMDTForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendMDTForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendMDTForApprovalCode());
        end;
    end;

    procedure ISMDTworkflowenabled(var MDT: Record "Master Data Template"): Boolean
    begin
        if MDT.Status <> MDT.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(MDT, RunworkflowOnSendMDTforApprovalCode()));
    end;

    Procedure CheckMDTApprovalsWorkflowEnabled(VAR MDT: Record "Master Data Template"): Boolean
    begin
        IF not ISMDTworkflowenabled(MDT) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument12(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        MDT: Record "Master Data Template";
    begin
        case RecRef.Number() of
            Database::"Master Data Template":
                begin
                    RecRef.SetTable(MDT);
                    ApprovalEntryArgument."Document No." := FORMAT(MDT."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument11(RecRef: RecordRef; var Handled: boolean)
    var
        MDT: Record "Master Data Template";
    begin
        case RecRef.Number() of
            Database::"Master Data Template":
                begin
                    RecRef.SetTable(MDT);
                    MDT.Status := MDT.Status::Open;
                    MDT.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument11(RecRef: RecordRef; var Handled: boolean)
    var
        MDT: Record "Master Data Template";
    begin
        case RecRef.Number() of
            Database::"Master Data Template":
                begin
                    RecRef.SetTable(MDT);
                    MDT.Status := MDT.Status::Released;
                    MDT.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval11(RecRef: RecordRef; var IsHandled: boolean)
    var
        MDT: Record "Master Data Template";
    begin
        case RecRef.Number() of
            Database::"Master Data Template":
                begin
                    RecRef.SetTable(MDT);
                    MDT.Status := MDT.Status::"Pending Approval";
                    MDT.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary11(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendMDTforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendMDTforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelMDTforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelMDTforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary11()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(MDTCategoryTxt, 1, 20), CopyStr(MDTCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations11()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Master Data Template", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate11()
    begin
        InsertMDTApprovalworkflowtemplate();
    end;

    local procedure InsertMDTApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt13, 1, 17), CopyStr(MDTApprWorkflowDescTxt, 1, 100), CopyStr(MDTCategoryTxt, 1, 20));
        InsertMDTApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertMDTApprovalworkflowDetails(var workflow: record Workflow);
    var
        MDT: Record "Master Data Template";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildMDTtypecondition(MDT.Status::open), RunworkflowOnSendMDTforApprovalCode(), BuildMDTtypecondition(MDT.Status::"Pending Approval"), RunworkflowOnCancelMDTforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildMDTtypecondition(status: integer): Text
    var
        MDT: Record "Master Data Template";
    Begin
        MDT.SetRange(status, status);
        exit(StrSubstNo(MDTTypeCondnTxt, workflowsetup.Encode(MDT.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid12(RecordRef: RecordRef; var PageID: Integer)
    begin
        Case RecordRef.Number() of
            database::"Master Data Template":
                PageID := GetConditionalcardPageid12(RecordRef)
        end;
    end;

    local procedure GetConditionalcardPageid12(RecordRef: RecordRef): Integer
    var
        MasterDataTemp: Record "Master Data Template";
    begin
        Case RecordRef.Number() of
            database::"Master Data Template":
                begin
                    RecordRef.SetTable(MasterDataTemp);
                    if MasterDataTemp."Master Process Type" = MasterDataTemp."Master Process Type"::Create then
                        exit(page::"Create Master Data Template")
                    else
                        if MasterDataTemp."Master Process Type" = MasterDataTemp."Master Process Type"::Modify then
                            exit(page::"Master Data Modify Template");
                end;
        end;
    end;
    //MDT End

    //FA Start
    [IntegrationEvent(false, false)]
    Procedure OnSendFAForApproval(var FixedAsset: Record "Fixed Asset")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelFAForApproval(var FixedAsset: Record "Fixed Asset")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendFAforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendFAforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OnSendFAForApproval', '', true, true)]
    local procedure RunworkflowonsendFAForApproval(var FixedAsset: Record "Fixed Asset")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendFAforApprovalCode(), FixedAsset);
    end;

    procedure RunworkflowOnCancelFAforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelFAForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::IJLSubEvents, 'OncancelFAForApproval', '', true, true)]

    local procedure RunworkflowonCancelFAForApproval(var FixedAsset: Record "Fixed Asset")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelFAforApprovalCode(), FixedAsset);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryFA();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendFAforApprovalCode(), DATABASE::"Fixed Asset",
          CopyStr(CheckFASendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelFAforApprovalCode(), DATABASE::"Fixed Asset",
          CopyStr(FArequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryFA(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelFAforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelFAforApprovalCode(), RunworkflowOnSendFAforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendFAForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendFAForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendFAForApprovalCode());
        end;
    end;

    procedure ISFAworkflowenabled(var FixedAsset: Record "Fixed Asset"): Boolean
    begin
        if FixedAsset."Approval Status" <> FixedAsset."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(FixedAsset, RunworkflowOnSendFAforApprovalCode()));
    end;

    Procedure CheckFAApprovalsWorkflowEnabled(VAR FixedAsset: Record "Fixed Asset"): Boolean
    begin
        IF not ISFAworkflowenabled(FixedAsset) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentFA(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        FixedAsset: Record "Fixed Asset";
    begin
        case RecRef.Number() of
            Database::"Fixed Asset":
                begin
                    RecRef.SetTable(FixedAsset);
                    ApprovalEntryArgument."Document No." := FORMAT(FixedAsset."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentFA(RecRef: RecordRef; var Handled: boolean)
    var
        FixedAsset: Record "Fixed Asset";
    begin
        case RecRef.Number() of
            Database::"Fixed Asset":
                begin
                    RecRef.SetTable(FixedAsset);
                    FixedAsset."Approval Status" := FixedAsset."Approval Status"::Open;
                    FixedAsset.Modify();
                    Handled := true;
                end;
        end;
    end;

    //G2S 220225 CAS-01383-Y5V4Y9 
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentItemJNLBatch(RecRef: RecordRef; var Handled: boolean)
    var
        ItemJnlBatch: Record "Item Journal Batch";
    begin
        case RecRef.Number() of
            Database::"Item Journal Batch":
                begin
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentFA(RecRef: RecordRef; var Handled: boolean)
    var
        FixedAsset: Record "Fixed Asset";
    begin
        case RecRef.Number() of
            Database::"Fixed Asset":
                begin
                    RecRef.SetTable(FixedAsset);
                    FixedAsset."Approval Status" := FixedAsset."Approval Status"::Released;
                    FixedAsset.Blocked := false;
                    FixedAsset.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalFA(RecRef: RecordRef; var IsHandled: boolean)
    var
        FixedAsset: Record "Fixed Asset";
    begin
        case RecRef.Number() of
            Database::"Fixed Asset":
                begin
                    RecRef.SetTable(FixedAsset);
                    FixedAsset."Approval Status" := FixedAsset."Approval Status"::"Pending for Approval";
                    FixedAsset.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryFA(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendFAforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendFAforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelFAforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelFAforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryFA()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(FACategoryTxt, 1, 20), CopyStr(FACategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsFA()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Fixed Asset", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateFA()
    begin
        InsertFAApprovalworkflowtemplate();
    end;

    local procedure InsertFAApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt5, 1, 17), CopyStr(FAApprWorkflowDescTxt, 1, 100), CopyStr(FACategoryTxt, 1, 20));
        InsertFAApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertFAApprovalworkflowDetails(var workflow: record Workflow);
    var
        FixedAsset: Record "Fixed Asset";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildFAtypecondition(FixedAsset."Approval Status"::open), RunworkflowOnSendFAforApprovalCode(), BuildFAtypecondition(FixedAsset."Approval Status"::"Pending for Approval"), RunworkflowOnCancelFAforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildFAtypecondition(status: integer): Text
    var
        FixedAsset: Record "Fixed Asset";
    Begin
        FixedAsset.SetRange("Approval Status", status);
        exit(StrSubstNo(FATypeCondnTxt, workflowsetup.Encode(FixedAsset.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure OnaftergetpageidFA(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidFA(RecordRef)
    end;

    local procedure GetConditionalcardPageidFA(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Fixed Asset":
                exit(page::"Fixed Asset Card");
        end;
    end;
    //FA End

    var
        CheckProdBOMVersendforapprovaleventdescTxt: Label 'Approval of a ProdBOMVer document is requested';
        ProdBOMVerrequestcanceleventdescTxt: Label 'Approval of a ProdBOMVer document is Cancelled';
        ProdBOMVerCategoryTxt: Label 'ProdBOMVer';
        ProdBOMVerCategoryDescTxt: Label 'ProdBOMVerDocuments';
        ProdBOMVerApprWorkflowDescTxt: Label 'ProdBOMVer Approval Workflow';
        IncDocOCRWorkflowCodeTxt2: Label 'INCDOC-ProdBOM';
        IncDocOCRWorkflowCodeTxt3: Label 'INCDOC-ProdBOMVer';
        ProdBOMVerTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="ProdBOMVer">%1</DataItem></DataItems></ReportParameters>';

        CheckCOASendforapprovaleventdesctxt: Label 'Approval of a COA document is requested';
        COArequestcanceleventdescTxt: Label 'Approval of a COA document is Cancelled';
        COACategoryTxt: Label 'COA';
        IncDocOCRWorkflowCodeTxt4: Label 'INCDOC-COA';
        COACategoryDescTxt: Label 'COADocuments';
        COAApprWorkflowDescTxt: Label 'COA Approval Workflow';
        COATypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="COA">%1</DataItem></DataItems></ReportParameters>';

        CheckFASendforapprovaleventdesctxt: Label 'Approval of a Fixed Asset document is requested';
        FArequestcanceleventdescTxt: Label 'Approval of a Fixed Asset document is Cancelled';
        FACategoryTxt: Label 'FA';
        IncDocOCRWorkflowCodeTxt5: Label 'INCDOC-FA';
        FACategoryDescTxt: Label 'FADocuments';
        FAApprWorkflowDescTxt: Label 'FA Approval Workflow';
        FATypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="FA">%1</DataItem></DataItems></ReportParameters>';
        CheckBankAccSendforapprovaleventdesctxt: Label 'Approval of a Bank Account document is requested';
        BankAccrequestcanceleventdescTxt: Label 'Approval of a Bank Account document is Cancelled';
        BankAccCategoryTxt: Label 'Bank Account';
        BankAccCategoryDescTxt: Label 'Bank Account Documents';
        BankAccApprWorkflowDescTxt: Label 'Bank Account Approval Workflow';
        BankAccTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="BankAcc">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt6: Label 'INCDOC-BankAcc';
        CheckBankAccRecSendforapprovaleventdesctxt: Label 'Approval of a Bank Acc. Reconciliation document is requested';
        BankAccRecrequestcanceleventdescTxt: Label 'Approval of a Bank Acc. Reconciliation document is Cancelled';
        BankAccRecCategoryTxt: Label 'Bank Acc. Reconciliation';
        BankAccRecCategoryDescTxt: Label 'Bank Acc. Reconciliation Documents';
        BankAccRecApprWorkflowDescTxt: Label 'Bank Acc. Reconciliation Approval Workflow';
        BankAccRecTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="BankAccRec">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt7: Label 'INCDOC-BankAccRec';
        CheckGLBudgetSendforapprovaleventdesctxt: Label 'Approval of a G/L Budget document is requested';
        GLBudgetrequestcanceleventdescTxt: Label 'Approval of a G/L Budget document is Cancelled';
        GLBudgetCategoryTxt: Label 'G/L Budget';
        GLBudgetCategoryDescTxt: Label 'G/L Budget Documents';
        GLBudgetApprWorkflowDescTxt: Label 'G/L Budget Approval Workflow';
        GLBudgetTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="GLBudget">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt8: Label 'INCDOC-GLBudget';
        CheckItemSalesDiscQtySendforapprovaleventdesctxt: Label 'Approval of a Item Sales Disc. Qty. document is requested';
        ItemSalesDiscQtyrequestcanceleventdescTxt: Label 'Approval of a Item Sales Disc. Qty. document is Cancelled';
        ItemSalesDiscQtyCategoryTxt: Label 'Item Sales Disc. Qty.';
        ItemSalesDiscQtyCategoryDescTxt: Label 'Item Sales Disc. Qty. Documents';
        ItemSalesDiscQtyApprWorkflowDescTxt: Label 'Item Sales Disc. Qty. Approval Workflow';
        ItemSalesDiscQtyTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="ItemSalesDiscQty">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt9: Label 'INCDOC-ItemSalesDiscQty';
        CheckFADisposalSendforapprovaleventdesctxt: Label 'Approval of a FA Disposal document is requested';
        FADisposalrequestcanceleventdescTxt: Label 'Approval of a FA Disposal document is Cancelled';
        FADisposalCategoryTxt: Label 'FA Disposal';
        FADisposalCategoryDescTxt: Label 'FA Disposal Documents';
        FADisposalApprWorkflowDescTxt: Label 'FA Disposal Approval Workflow';
        FADisposalTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="FADisposal">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt10: Label 'INCDOC-FADisposal';
        CheckFAMovRegSendforapprovaleventdesctxt: Label 'Approval of a FA Movement Register document is requested';
        FAMovRegrequestcanceleventdescTxt: Label 'Approval of a FA Movement Register document is Cancelled';
        FAMovRegCategoryTxt: Label 'FA Movement Register';
        FAMovRegCategoryDescTxt: Label 'FA Movement Register Documents';
        FAMovRegApprWorkflowDescTxt: Label 'FA Movement Register Approval Workflow';
        FAMovRegTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="FAMovReg">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt11: Label 'INCDOC-FAMovReg';
        CheckJournalVoucherSendforapprovaleventdesctxt: Label 'Approval of a Journal Voucher document is requested';
        JournalVoucherrequestcanceleventdescTxt: Label 'Approval of a Journal Voucher document is Cancelled';
        JournalVoucherCategoryTxt: Label 'Journal Voucher';
        JournalVoucherCategoryDescTxt: Label 'Journal Voucher Documents';
        JournalVoucherApprWorkflowDescTxt: Label 'Journal Voucher Approval Workflow';
        JournalVoucherTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="JournalVoucher">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt12: Label 'INCDOC-JournalVoucher';

        CheckMDTSendforapprovaleventdesctxt: Label 'Approval of a Master Data Template document is requested';
        MDTrequestcanceleventdescTxt: Label 'Approval of a Master Data Template document is Cancelled';
        MDTCategoryTxt: Label 'Master Data Template';
        MDTCategoryDescTxt: Label 'Master Data Template Documents';
        MDTApprWorkflowDescTxt: Label 'Master Data Template Approval Workflow';
        MDTTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="MDT">%1</DataItem></DataItems></ReportParameters>';
        IncDocOCRWorkflowCodeTxt13: Label 'INCDOC-MDT';

}