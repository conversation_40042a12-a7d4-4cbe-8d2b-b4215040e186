/// <summary>
/// TableExtension ItemTabExt (ID 50038) extends Record Item.
/// </summary>
tableextension 50038 ItemTabExt extends Item
{

    fields
    {
        field(50000; "Gross Weight in KGS"; Decimal)
        {

            DataClassification = CustomerContent;
        }
        field(50001; "Net Weight in KGS"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Shortcut Dimension 3 Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(3));
        }
        field(50003; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
            editable = false;
        }
        /*
        field(50004; "Status"; Option)
        {
            OptionMembers = Released,Open;
            DataClassification = CustomerContent;
        }*/
        field(50005; "SNOP Include"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Minimun Stock Days"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "SKU Unit"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Default Warehouse Location"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Location;
        }
        field(50009; "Default Prod. Line No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Machine Center"."No.";
        }
        field(50010; "SNOP LOT Qty"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "SNOP Lead Time"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "BOM Explosion Lead Time Calc."; Option)
        {
            DataClassification = CustomerContent;
            OptionMembers = "SNOP Lead Time","Last 3 GRNS";
        }
        field(50013; "Stock Division Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(5));
        }
        field(50014; "Discount Quantity Change"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Spl. Quantity"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Standard Quantity"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Variant Size"; Option)
        {
            OptionCaption = 'Small,Big';
            OptionMembers = Small,Big;
            DataClassification = CustomerContent;
        }
        field(50018; "Variant Size Multiplier"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50020; "MRS No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50021; "MRS Line No."; integer)
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Lead Time"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50023; "Purchase Policy"; Option)
        {
            OptionCaption = ',Imported Item,Local Item';
            OptionMembers = ,"Imported Item","Local Item";
            DataClassification = CustomerContent;
        }
        field(50026; ItemQRData; BLOB)
        {
            Caption = 'QR Data Picture';
            SubType = Bitmap;
            DataClassification = CustomerContent;
        }
        field(50027; "Transfered FA Cost"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50028; "Multiply Factor"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(50029; "Per Tray"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Litre Per Tray"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Active/Obsolete"; Option)
        {
            InitValue = Active;
            OptionMembers = Active,Obsolete;

            trigger OnValidate();
            begin
                //PermChk.CheckAutority(USERID,291);
                /*IF "Active/Obsolete" = 0 THEN
                BEGIN
                   "Active/Obsolete":=USERID;
                   "Active UserID":=TODAY;
                   "Active Date":=TIME;
                END;
                
                IF "Active/Obsolete" = 1 THEN
                BEGIN
                   "Active Time":=USERID;
                   "Obsolete UserID":=TODAY;
                   "Obsolete Date":=TIME;
                END;
                 */

            end;
        }
        field(50032; "Maximum Stock Days"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50037; "Conversion Unit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50073; "KO BPP Code"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50074; "Description 1"; Code[100])
        {
            DataClassification = CustomerContent;
            //Pk-N on 14.05.2021
        }
        field(50080; "Customer's Transportation Rate"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50082; "Last Movement Date"; Date)  //PKONOC5
        {
            CalcFormula = Max ("Item Ledger Entry"."Posting Date" WHERE("Item No." = FIELD("No.")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(50083; "Last Purchase Date"; Date)//Baluoncct6
        {
            CalcFormula = Max ("Item Ledger Entry"."Posting Date" WHERE("Item No." = FIELD("No."), "Entry Type" = const(Purchase)));
            Editable = false;
            FieldClass = FlowField;
        }
        //B2BBaluOn25Nov21>>
        field(50084; "SON Required or NOT"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50085; "NAFDAC Required or NOT"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50086; "Duty %"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        //B2BBaluOn25Nov21<<
        //Baluon17Jan2022>>
        field(50088; Brand; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50089; "Master Brand"; text[50])
        {
            DataClassification = CustomerContent;
        }

        //Baluon17Jan2022<<
        //Baluon11Jul2022>>
        field(50090; "Increase Stock at date"; Decimal)
        {
            //DataClassification = CustomerContent;
            FieldClass = FlowField;
            CalcFormula = Sum ("Item Ledger Entry".Quantity WHERE("Item No." = FIELD("No."), "Location Code" = FIELD("Location Filter"), "Posting Date" = FIELD("Date Filter"), "Document Type" = FILTER("Transfer Receipt" | "Purchase Receipt" | "Sales Return Receipt")));
        }
        //Baluon11Jul2022>>

        //>>>>>> G2S 7TH Sept., 2023 G2S
        field(50091; "License Plate Enabled?"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //G2S <<<<<<< 7th Sept., 23 G2S
        //RFC# 2024_17 AND 2024_18 >>
        field(50092; "CHI SKU Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "SKU Data";
            trigger OnValidate()
            var
                SKUData: Record "SKU Data";
            begin
                if SKUData.Get("CHI SKU Code") then begin
                    "CHI SKU Description" := SKUData."SKU Description";
                    "CHI SKU Product" := SKUData."SKU Product";
                    "CHI SKU Group" := SKUData."SKU Group";
                    "CHI SKU Pack Size" := SKUData."SKU Pack Size";
                    "CHI SKU Category" := SKUData."SKU Category";

                end;
            end;
        }
        field(50093; "CHI SKU Description"; Code[200])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50094; "CHI SKU Product"; Code[50])
        {
            DataClassification = CustomerContent;
            Editable = false;

        }
        field(50095; "CHI SKU Pack Size"; Code[200])
        {
            DataClassification = CustomerContent;
            Caption = 'Pack Size';
            Editable = false;

        }
        field(50096; "CHI SKU Group"; Code[100])
        {
            DataClassification = CustomerContent;
            Caption = 'SKU Group';
            Editable = false;

        }
        field(50097; "CHI SKU Category"; Enum "SKU Category")
        {
            DataClassification = CustomerContent;
            Caption = 'SKU Category';
            Editable = false;

        }
        //RFC# 2024_17 AND 2024_18 <<
        //Project Leap >>
        field(50098; "Qty on MRS Line"; Decimal)
        {

            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum (MRSLine.Quantity where("Production Batch No." = filter(<> ''), "MRS Closed" = const(false), Rejected = const(false), "Line CLosed" = const(false), "No." = FIELD("No.")));
        }
        //Project Leap <<<
        // Item_and_FAclass_field
        field(50099; "Supply Chain Category"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Supply Chain Category".Code;
        }
        field(50100; "Supply Chain Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Supply Chain Group".Code;
        }
        field(50101; "Production Category"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Production Category".Code;
        }
        // Item_and_FAclass_field
        field(50200; BarCode; text[100])
        {
            DataClassification = ToBeClassified;
        }
        field(50201; "Clearwox Item ID"; text[100])
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        modify(Description)
        {
            trigger OnAfterValidate()
            var
                IntegrationCU: Codeunit "CHI Retail Integrations";
                ItemBarcodeUOM: Record "Item Barcodes Unit of Measure";
                RetailProductRec: Record "Retail Products Log";

            begin
                if Rec."Clearwox Item ID" <> '' then begin
                    RetailProductRec.Reset();
                    RetailProductRec.SetRange("Item No.", Rec."No.");
                    RetailProductRec.SetRange(Code, Rec.BarCode);
                    if RetailProductRec.FindFirst() then begin

                        RetailProductRec.Description := Rec.Description;
                        RetailProductRec.Status := RetailProductRec.Status::Pending;
                        RetailProductRec.Modify();


                        ItemBarcodeUOM.Reset();
                        ItemBarcodeUOM.SetRange("Item No.", Rec."No.");
                        if ItemBarcodeUOM.FindSet() then begin
                            repeat
                                RetailProductRec.Reset();
                                RetailProductRec.SetRange("Item No.", Rec."No.");
                                RetailProductRec.SetRange(Code, ItemBarcodeUOM.Barcode);
                                if RetailProductRec.FindFirst() then begin

                                    RetailProductRec.Description := ItemBarcodeUOM."Unit Of Measure" + ' ' + Rec.Description;
                                    RetailProductRec.Status := RetailProductRec.Status::Pending;
                                    RetailProductRec.Modify();
                                end;

                            until ItemBarcodeUOM.Next() = 0;
                        end;

                    end;

                end;
                IntegrationCU.UpdateItemInRetail();
            end;
        }
        field(50202; "Allow Fraction"; Boolean)
        {
            Caption = 'Allow Fraction';
        }
    }


    //RFCOutreachAPIGo2solveJuly2023>>>>>>
    trigger OnAfterModify()
    var
        prodLog: Record "Product Transaction Log";
        prodLog1: Record "Product Transaction Log";
        DefaultDim: Record "Default Dimension";
        SalesLine: Record "Sales Line";
        ItemUOM: Record "Item Unit of Measure";
        VATSetup: Record "VAT Posting Setup";
    begin

        //<<<<< G2S 22nd September 2023
        If "License Plate Enabled?" then
            TestField("Expiration Calculation");
        //>>>>> G2S  G2S 22nd September 2023


        if ((Rec."No." <> '') AND (Rec.Description <> '') AND (Rec."Item Category Code" = 'FG')) then begin
            //prodLog1.Init();
            prodLog1.SetRange("No.", Rec."No.");
            if (prodLog1.FindSet()) then begin
                prodLog1.DeleteAll();
            end;
            prodLog.Init();
            prodLog.Validate("No.", Rec."No.");
            prodLog.Validate(Description, Rec.Description);
            //130623
            prodLog.Validate(prod_short_name, Rec."Description 2");
            prodLog.Validate(product_type_name, Rec.Brand);
            prodLog.Validate(product_type_code, Rec.Brand);
            DefaultDim.Reset();
            DefaultDim.SetRange("No.", Rec."No.");
            DefaultDim.SetRange("Table ID", 27);
            DefaultDim.SetRange("Dimension Code", 'DIVISION');
            if DefaultDim.FindFirst() then begin
                prodLog.prod_cat_code := DefaultDim."Dimension Code";
                prodLog.prod_cat_name := DefaultDim."Dimension Code";
                prodLog.Validate(prod_cat_code, DefaultDim."Dimension Code");
                prodLog.Validate(prod_cat_name, DefaultDim."Dimension Code");
            end else begin
                DefaultDim.SetRange("Dimension Code");
                if DefaultDim.FindFirst() then begin
                    prodLog.prod_cat_code := DefaultDim."Dimension Code";
                    prodLog.prod_cat_name := DefaultDim."Dimension Code";
                    prodLog.Validate(prod_cat_code, DefaultDim."Dimension Code");
                    prodLog.Validate(prod_cat_name, DefaultDim."Dimension Code");
                end;
            end;
            prodLog.Validate(brand_code, Rec."Master Brand");
            prodLog.Validate(brand_name, Rec."Master Brand");
            prodLog.Validate(sub_brand_code, Rec."SKU Unit");
            prodLog.Validate(sub_brand_name, Rec."SKU Unit");
            prodLog.Validate(sku_group_code, Rec."SKU Unit");
            prodLog.Validate(sku_group_name, Rec."SKU Unit");

            VATSetup.Reset();
            VATSetup.SetRange("VAT Prod. Posting Group", Rec."VAT Prod. Posting Group");
            VATSetup.SetFilter("VAT %", '<>%1', 0);
            if VATSetup.FindFirst() then begin
                prodLog.Validate(tax1perc, VATSetup."VAT %");
            end;
            ItemUOM.Reset();
            ItemUOM.SetRange("Item No.", Rec."No.");
            ItemUOM.SetRange(Code, Rec."Base Unit of Measure");
            if ItemUOM.FindFirst() then begin
                prodLog.Validate(prod_cbb_volume, ItemUOM."Qty. per Unit of Measure");
            end;
            prodLog.Validate(Type, Format(Rec.Type));
            prodLog.Validate("Item Category Code", Rec."Item Category Code");
            prodLog.Validate("Item Category Id", Format("Item Category Id"));
            prodLog.Validate(comp_code, CompanyName);
            prodLog.Insert(true);
            //RFCOutreachAPIGo2solveJuly2023<<<<<<
        end;
    end;

    trigger OnAfterInsert()

    var
        //RFCOutreachAPIGo2solveJuly2023>>>>>>
        prodLog: Record "Product Transaction Log";
        prodLog1: Record "Product Transaction Log";
        //RFCOutreachAPIGo2solveJuly2023<<<<<<
        DefaultDim: Record "Default Dimension";
        SalesLine: Record "Sales Line";
        ItemUOM: Record "Item Unit of Measure";
        VATSetup: Record "VAT Posting Setup";
    begin
        //<<<<< G2S 22nd September 2023
        If "License Plate Enabled?" then
            TestField("Expiration Calculation");
        //>>>>> G2S  G2S 22nd September 2023

        //RFCOutreachAPIGo2solveJuly2023>>>>>>
        if ((Rec."No." <> '') AND (Rec.Description <> '') AND (Rec."Item Category Code" = 'FG')) then begin
            prodLog1.Init();
            prodLog1.SetRange("No.", Rec."No.");
            if (prodLog1.FindSet()) then begin
                prodLog1.DeleteAll();
            end;

            prodLog.Init();
            prodLog.Validate("No.", Rec."No.");
            prodLog.Validate(Description, Rec.Description);
            prodLog.Validate(prod_short_name, Rec."Description 2");
            prodLog.Validate(product_type_name, Rec.Brand);
            prodLog.Validate(product_type_code, Rec.Brand);
            DefaultDim.Reset();
            DefaultDim.SetRange("No.", Rec."No.");
            DefaultDim.SetRange("Table ID", 27);
            DefaultDim.SetRange("Dimension Code", 'DIVISION');
            if DefaultDim.FindFirst() then begin
                prodLog.prod_cat_code := DefaultDim."Dimension Code";
                prodLog.prod_cat_name := DefaultDim."Dimension Code";
                prodLog.Validate(prod_cat_code, DefaultDim."Dimension Code");
                prodLog.Validate(prod_cat_name, DefaultDim."Dimension Code");
            end else begin
                DefaultDim.SetRange("Dimension Code");
                if DefaultDim.FindFirst() then begin
                    prodLog.prod_cat_code := DefaultDim."Dimension Code";
                    prodLog.prod_cat_name := DefaultDim."Dimension Code";
                    prodLog.Validate(prod_cat_code, DefaultDim."Dimension Code");
                    prodLog.Validate(prod_cat_name, DefaultDim."Dimension Code");
                end;
            end;
            prodLog.Validate(brand_code, Rec."Master Brand");
            prodLog.Validate(brand_name, Rec."Master Brand");
            prodLog.Validate(sub_brand_code, Rec."SKU Unit");
            prodLog.Validate(sub_brand_name, Rec."SKU Unit");
            prodLog.Validate(sku_group_code, Rec."SKU Unit");
            prodLog.Validate(sku_group_name, Rec."SKU Unit");
            VATSetup.Reset();
            VATSetup.SetRange("VAT Prod. Posting Group", Rec."VAT Prod. Posting Group");
            VATSetup.SetFilter("VAT %", '<>0');
            if VATSetup.FindFirst() then begin
                prodLog.Validate(tax1perc, VATSetup."VAT %");
            end;
            ItemUOM.Reset();
            ItemUOM.SetRange("Item No.", Rec."No.");
            ItemUOM.SetRange(Code, prodLog."No.");
            if ItemUOM.FindFirst() then begin
                prodLog.Validate(prod_cbb_volume, ItemUOM."Qty. per Unit of Measure");
            end;
            prodLog.Validate(Type, Format(Rec.Type));
            prodLog.Validate("Item Category Code", Rec."Item Category Code");
            prodLog.Validate("Item Category Id", Format("Item Category Id"));
            prodLog.Validate(comp_code, CompanyName);
            prodLog.Insert(true);
        end;
    end;
    //RFCOutreachAPIGo2solveJuly2023<<<<<<


    // trigger OnModify()
    // begin
    //     //if "Approval Status" <> "Approval Status"::Open then
    //     //error('you cannot modify record while in approval/release stage');//Jan282021
    // end;

    trigger OnInsert()
    begin
        // Blocked := true; //Pk-N on 14.05.2021
    end;
}