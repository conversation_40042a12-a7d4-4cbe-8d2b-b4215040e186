
pageextension 50104 GLBudgetNames extends "G/L Budget Names"
{
    layout
    {
        // Add changes to page layout here
        addafter(Description)
        {
            field("Budget Start Period"; "Budget Start Period")
            {
                ApplicationArea = ALL;
            }
            field("Budget End Period"; "Budget End Period")
            {
                ApplicationArea = ALL;
            }
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
                //Editable = false;
            }
        }
    }

    actions
    {
        addafter(EditBudget)
        {
            action(WorkFlows)
            {
                ApplicationArea = All;
                Image = Action;

                trigger OnAction()
                begin
                    Message('This actions are for workflows.');
                end;
            }
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    IF allinoneCU.CheckGLBudgetApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendGLBudgetForApproval(Rec);
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelGLBudgetForApproval(rec);
                end;
            }

            action("Release")
            {
                Image = ReleaseDoc;
                ApplicationArea = all;
                trigger OnAction()
                begin
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendGLBudgetforApprovalCode()) then
                        error('Workflow is enabled. You can not release manually.');
                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        Modify();
                    end;
                end;
            }
            action("Open")
            {
                Image = Open;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF "Approval Status" = "Approval Status"::"Pending for Approval" THEN
                        ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 95);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        Modify();
                    end;
                end;
            }


        }
    }
    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);
    END;

    var
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit IJLSubEvents;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        RecordRest: record "Restricted Record";
        WorkflowManagement: codeunit "Workflow Management";
        WorkflowEventHandling: Codeunit "Workflow Event Handling";
}

