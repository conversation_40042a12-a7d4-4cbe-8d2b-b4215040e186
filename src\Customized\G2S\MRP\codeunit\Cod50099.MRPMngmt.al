codeunit 50099 "MRP Mngmt"
{
    trigger OnRun()
    begin
    end;

    var
        componentLine: Record "Prod. Order Component";

    // Event Subscription to delete Prod. Order Components when Prod. Order is deleted
    [EventSubscriber(ObjectType::Table, Database::"Production Order", 'OnAfterDeleteEvent', '', false, false)]
    local procedure OnAfterDeleteEventProdOrder(RunTrigger: Boolean; Rec: Record "Production Order")
    begin
        componentLine.SetCurrentKey("Prod. Order No.");
        componentLine.SetFilter("Prod. Order No.", '%1', Rec."No.");
        if componentLine.FindSet() then componentLine.DeleteAll();
    end;

    procedure "calc. SafetyStock"(var Rec: Record "13Wk Header")
    var
        VersionMgt: Codeunit VersionManagement;
        PlanLine: Record "13Wk Plan Line";
        PlanHeader: Record "13Wk Header";
        Item: Record Item;
        BomHeader: Record "Production BOM Header";
        BomLine: Record "Production BOM Line";
        BomVersion: Record "Production BOM Version";
    begin
        PlanHeader.Copy(Rec);
        PlanLine.SetFilter("Document No", '=%1', PlanHeader."No.");
        if PlanLine.FindSet() then begin
            repeat
                Clear(FGAvgWeeklyUsage);

                FGAvgWeeklyUsage := PlanLine."Week 1" + PlanLine."Week 2" + PlanLine."Week 3" + PlanLine."Week 4" + PlanLine."Week 5" + PlanLine."Week 6" + PlanLine."Week 7" + PlanLine."Week 8" + PlanLine."Week 9" + PlanLine."Week 10" + PlanLine."Week 11" + PlanLine."Week 12" + PlanLine."Week 13";
                FGAvgWeeklyUsage := FGAvgWeeklyUsage / 13;

                if Item.Get(PlanLine."Item No.") then begin
                    BomHeader.SetRange("No.", Item."Production BOM No.");
                    if BomHeader.FindFirst() then begin
                        BomVersion.SetCurrentKey("Version Code", "Production BOM No.");
                        BomVersion.SetRange("Production BOM No.", BomHeader."No.");
                        BomVersion.SetRange("Version Code", VersionMgt.GetBOMVersion(BomHeader."No.", WorkDate, true));
                        if BomVersion.FindFirst() then begin
                            BomLine.SetRange("Production BOM No.", BomHeader."No.");
                            BomLine.SetRange("Version Code", BomVersion."Version Code");
                            if BomLine.FindSet() then begin
                                repeat

                                until BomLine.Next() = 0;
                            end;
                        end;
                    end;
                end;
            until PlanLine.Next() = 0;
        end;
    end;

    procedure UpdatePlanLine(var Rec: Record "13Wk Header")
    var
        PlanLine: Record "13Wk Plan Line";
        PlanHeader: Record "13Wk Header";
    begin
        PlanHeader.Copy(Rec);
        PlanLine.SetFilter("Document No", '=%1', PlanHeader."No.");
        if PlanLine.FindSet() then begin
            repeat
            // PlanLine."Safety Stock" := "calc. SafetyStock"(PlanHeader);
            // PlanLine.Modify();
            until PlanLine.Next() = 0;
        end;
    end;

    procedure GetAverageWeeklyUsage(ItemNo: Code[20]): Decimal
    begin

    end;

}
