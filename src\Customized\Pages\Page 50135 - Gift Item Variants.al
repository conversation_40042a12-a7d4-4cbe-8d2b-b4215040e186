page 50135 "Gift Item Variants"
{
    AutoSplitKey = true;
    PageType = List;
    SourceTable = "Promo. Schedule Gift Items";
    UsageCategory = lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Promotion Type"; "Promotion Type")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Promo. Schd. Doc. Type"; "Promo. Schd. Doc. Type")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Promo. Schd. Document No."; "Promo. Schd. Document No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Promo. Schd. Line No."; "Promo. Schd. Line No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Gift Item No."; "Gift Item No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field(Active; Active)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

