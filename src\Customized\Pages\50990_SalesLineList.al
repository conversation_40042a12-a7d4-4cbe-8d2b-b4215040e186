page 50990 SalesLineList
{
    caption = 'Sales Line List View';//PKONJ10.2 full object
    PageType = List;
    SourceTable = "Sales Line";
    Insertallowed = false;
    ModifyAllowed = false;
    DeleteAllowed = false;
    ApplicationArea = all;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(Control)
            {
                field(OrStatus; OrStatus)
                {
                    ApplicationArea = All;
                    Caption = 'Order Status';
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = All;
                    caption = 'Document Type';

                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;
                    Caption = 'Document No.';
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = All;
                }
                field("No."; "No.")
                {
                    ApplicationArea = All;
                    Caption = 'Item No.';
                }
                field("Description"; "Description")
                {
                    ApplicationArea = All;
                    Caption = 'Description';
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    ApplicationArea = All;
                    Caption = 'Unit of Measure';
                }
                field("Quantity"; "Quantity")
                {
                    ApplicationArea = All;
                }
                field("Quantity Shipped"; "Quantity Shipped")
                {
                    ApplicationArea = All;
                }
                field("Quantity Invoiced"; "Quantity Invoiced")
                {
                    ApplicationArea = All;
                }
                field("Outstanding Quantity"; "Outstanding Quantity")
                {
                    ApplicationArea = All;
                }
                field("Unit Price"; "Unit Price")
                {
                    ApplicationArea = All;
                    Caption = 'Unit Price';
                }
                field(Amount; Amount)
                {
                    ApplicationArea = All;
                    Caption = 'Amount';
                }
            }

        }
    }
    trigger OnAfterGetRecord()
    begin
        Orstatus := 'Open';
        if "Order Status" = "Order Status"::Cancelled then
            Orstatus := 'Cancelled'
        else
            if "Order Status" = "Order Status"::"Short Closed" then
                Orstatus := 'Short Closed';
    end;

    var
        Orstatus: text;
}