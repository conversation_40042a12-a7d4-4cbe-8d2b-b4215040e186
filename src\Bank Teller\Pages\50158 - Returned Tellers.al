page 50158 "Returned Tellers"
{
    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = List;
    SourceTable = "Request Teller Receipt";
    SourceTableView = WHERE("Teller Returned" = FILTER(true));
    ApplicationArea = all;
    UsageCategory = tasks;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Bank Deposited"; "Bank Deposited")
                {
                    ApplicationArea = all;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                }
                field("Unposted Teller No."; "Unposted Teller No.")
                {
                    ApplicationArea = all;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                }
                field("Teller Returned"; "Teller Returned")
                {
                    ApplicationArea = all;
                }
                field("Returned Teller Information"; "Returned Teller Information")
                {
                    ApplicationArea = all;
                }
                field("Returned Teller No."; "Returned Teller No.")
                {
                    ApplicationArea = all;
                }
                field("Returned Teller Date"; "Returned Teller Date")
                {
                    ApplicationArea = all;
                }
                field("Returned Teller Amount"; "Returned Teller Amount")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnOpenPage();
    begin
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);
    end;

    var
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

