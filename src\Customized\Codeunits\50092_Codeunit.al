codeunit 50042 "Allow Posting Control"
{
    TableNo = "Job Queue Entry";

    trigger OnRun();
    begin
        //SDT.GET;
        Usersetup.RESET;
        Usersetup.SETFILTER("Allow Posting To", '<>%1', 0D);
        if not Usersetup.ISEMPTY then
            Usersetup.MODIFYALL(Usersetup."Allow Posting To", Today);


        Usersetup.RESET;
        Usersetup.SETFILTER("Allow FA Posting To", '<>%1', 0D);
        if not Usersetup.ISEMPTY then
            Usersetup.MODIFYALL("Allow FA Posting To", Today);

        if GLSetup.GET then begin
            GLSetup."Allow Posting To" := Today;
            GLSetup.MODIFY;
        end;

        if FASetup.GET then begin
            FASetup."Allow FA Posting To" := Today;
            FASetup.MODIFY;
        end;
    end;

    var
        Usersetup: Record "User Setup";
        SDT: Record ServerDateTime_View;
        GLSetup: Record "General Ledger Setup";
        FASetup: Record "FA Setup";
}

