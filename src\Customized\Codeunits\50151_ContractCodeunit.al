codeunit 50151 "Contract Renewal Mail Alert"
{
    //Created by - B2BMSOn18Oct21
    trigger OnRun()
    begin
        ContractRenewal();
    end;

    procedure ContractRenewal()

    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        PurchPaySetup: Record "Purchases & Payables Setup";
        SenderMail: Text;
        RecepientMail1: Text;
        RecepientMail2: Text;
        RecepientMail3: Text;
        Recepients: List of [Text];
        Subject: text;
        ConPro: Record "Contract Procurement";
        OrderNo: Code[20];
        CreatedDate: Date;
    begin
        PurchPaySetup.get();
        SMTPMailSetup.get();
        SenderMail := SMTPMailSetup."User ID";
        if PurchPaySetup."Contract Email 1" <> '' then
            RecepientMail1 := (PurchPaySetup."Contract Email 1");
        if PurchPaySetup."Contract Email 2" <> '' then
            RecepientMail2 := (PurchPaySetup."Contract Email 2");
        if PurchPaySetup."Contract Email 3" <> '' then
            RecepientMail3 := (PurchPaySetup."Contract Email 3");

        Subject := 'Contracts Renewal Alert';

        ConPro.Reset();
        ConPro.SetRange("Contract Renewal Date", CalcDate(PurchPaySetup."Contract Duration", WorkDate));
        if ConPro.FindSet() then begin
            if RecepientMail1 <> '' then
                Recepients.Add(RecepientMail1);
            if RecepientMail2 <> '' then
                Recepients.Add(RecepientMail2);
            if RecepientMail3 <> '' then
                Recepients.Add(RecepientMail3);

            SMTPMail.CreateMessage('ERP', SenderMail, Recepients, Subject, '', TRUE);
            SMTPMail.AppendBody('Hello ,');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('You are registered to receive notifications related to CHI.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('This is a message to notify you that:');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Contracts are going to expire for the below details:');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<th>Type</th>');
            SMTPMail.AppendBody('<th>Name of Agreement</th>');
            SMTPMail.AppendBody('<th>Duration</th>');
            SMTPMail.AppendBody('<th>Expiry Date</th>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            repeat
                SMTPMail.AppendBody('<td>' + Format(ConPro."Type") + '</td>');
                SMTPMail.AppendBody('<td>' + ConPro."Name of Agreement" + '</td>');
                SMTPMail.AppendBody('<td>' + ConPro."Duration" + '</td>');
                SMTPMail.AppendBody('<td>' + Format(ConPro."Contract Expiry Date") + '</td>');
                SMTPMail.AppendBody('<tr>');
            until ConPro.next = 0;
            SMTPMail.AppendBody('</table>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Thanking You.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Regards,');
            SMTPMail.AppendBody('<BR>');
            SMTPMail.AppendBody('CHI Ltd');
            SMTPMail.Send;
        END;
    end;

    //B2BMSOn21Oct21>>
    procedure ContractForeignDataMail()

    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        PurchPaySetup: Record "Purchases & Payables Setup";
        SenderMail: Text;
        RecepientMail1: Text;
        RecepientMail2: Text;
        RecepientMail3: Text;
        Recepients: List of [Text];
        Subject: text;
        ConPro: Record "Contract Procurement";
        OrderNo: Code[20];
        Vendor: Record Vendor;
        CreatedDate: Date;
        CommentText: Text;
        CountRec: Integer;
        CountOpen: Integer;
        CountExpired: Integer;
    begin
        PurchPaySetup.get();
        SMTPMailSetup.get();
        SenderMail := SMTPMailSetup."User ID";
        if PurchPaySetup."Contract Email 1" <> '' then
            RecepientMail1 := (PurchPaySetup."Contract Email 1");
        if PurchPaySetup."Contract Email 2" <> '' then
            RecepientMail2 := (PurchPaySetup."Contract Email 2");
        if PurchPaySetup."Contract Email 3" <> '' then
            RecepientMail3 := (PurchPaySetup."Contract Email 3");

        Subject := 'Foreign Contracts Details';

        if (SenderMail <> '') and (RecepientMail1 <> '') then begin
            if RecepientMail1 <> '' then
                Recepients.Add(RecepientMail1);
            if RecepientMail2 <> '' then
                Recepients.Add(RecepientMail2);
            if RecepientMail3 <> '' then
                Recepients.Add(RecepientMail3);

            SMTPMail.CreateMessage('ERP', SenderMail, Recepients, Subject, '', TRUE);
            SMTPMail.AppendBody('Hello ,');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('You are registered to receive notifications related to CHI.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('This is a message to notify you that:');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Contract is going to expire for the below details:');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<table border="1" width="1200" cellspacing="0" cellpadding="0">');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<th width="200">Vendor No.</th>');
            SMTPMail.AppendBody('<th width="200">Vendor Name</th>');
            SMTPMail.AppendBody('<th width="200">Vendor Address</th>');
            SMTPMail.AppendBody('<th width="200">No. of Open Contracts</th>');
            SMTPMail.AppendBody('<th width="200">No. of Expired Contracts</th>');
            SMTPMail.AppendBody('<th width="200">Comment</th>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            Vendor.Reset();
            Vendor.SetRange("Vendor Type", Vendor."Vendor Type"::Import);
            if Vendor.FindSet() then begin
                repeat
                    Clear(CountOpen);
                    Clear(CountExpired);
                    ConPro.Reset();
                    ConPro.SetRange("Vendor No.", Vendor."No.");
                    ConPro.SetRange(Status, ConPro.Status::Open);
                    if ConPro.FindSet() then
                        CountOpen := ConPro.Count;

                    ConPro.Reset();
                    ConPro.SetRange("Vendor No.", Vendor."No.");
                    ConPro.SetRange(Status, ConPro.Status::Expired);
                    if ConPro.FindSet() then
                        CountExpired := ConPro.Count;

                    if (CountOpen <> 0) AND (CountExpired = 0) then
                        CommentText := 'All Contracts are in Open';

                    if (CountOpen = 0) AND (CountExpired <> 0) then
                        CommentText := 'All Contracts Expired';

                    if (CountOpen <> 0) AND (CountExpired <> 0) then
                        CommentText := 'Contracts Available';

                    if (CountOpen = 0) AND (CountExpired = 0) then
                        CommentText := 'No Contracts Available';

                    SMTPMail.AppendBody('<td>' + Vendor."No." + '</td>');
                    SMTPMail.AppendBody('<td>' + Vendor.Name + '</td>');
                    SMTPMail.AppendBody('<td>' + Vendor.Address + '</td>');
                    SMTPMail.AppendBody('<td>' + Format(CountOpen) + '</td>');
                    SMTPMail.AppendBody('<td>' + Format(CountExpired) + '</td>');
                    SMTPMail.AppendBody('<td>' + CommentText + '</td>');
                    SMTPMail.AppendBody('</tr>');
                until Vendor.next = 0;
            end;
            SMTPMail.AppendBody('</table>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Thanking You.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Regards,');
            SMTPMail.AppendBody('<BR>');
            SMTPMail.AppendBody('CHI Ltd');
            SMTPMail.Send;
        END;
    end;
    //B2BMSOn21Oct21<<

    procedure ContractLocalDataMail()

    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        PurchPaySetup: Record "Purchases & Payables Setup";
        SenderMail: Text;
        RecepientMail1: Text;
        RecepientMail2: Text;
        RecepientMail3: Text;
        Recepients: List of [Text];
        Subject: text;
        ConPro: Record "Contract Procurement";
        OrderNo: Code[20];
        Vendor: Record Vendor;
        CreatedDate: Date;
        CommentText: Text;
        CountRec: Integer;
        CountOpen: Integer;
        CountExpired: Integer;
    begin
        PurchPaySetup.get();
        SMTPMailSetup.get();
        SenderMail := SMTPMailSetup."User ID";
        if PurchPaySetup."Contract Email 1" <> '' then
            RecepientMail1 := (PurchPaySetup."Contract Email 1");
        if PurchPaySetup."Contract Email 2" <> '' then
            RecepientMail2 := (PurchPaySetup."Contract Email 2");
        if PurchPaySetup."Contract Email 3" <> '' then
            RecepientMail3 := (PurchPaySetup."Contract Email 3");

        Subject := 'Local Contracts Details';

        if ConPro.Status = ConPro.Status::Open then
            CountOpen := ConPro.Count;

        if ConPro.Status = ConPro.Status::Expired then
            CountExpired := ConPro.Count;

        if (CountOpen <> 0) AND (CountExpired = 0) then
            CommentText := 'All Contracts are in Open';

        if (CountOpen = 0) AND (CountExpired <> 0) then
            CommentText := 'All Contracts Expired';

        if (CountOpen <> 0) AND (CountExpired <> 0) then
            CommentText := 'Contracts Available';

        if (CountOpen = 0) AND (CountExpired = 0) then
            CommentText := 'No Contracts Available';


        //CommentText := 'Vendor balance is above 150 Millions';

        if (SenderMail <> '') and (RecepientMail1 <> '') then begin
            if RecepientMail1 <> '' then
                Recepients.Add(RecepientMail1);
            if RecepientMail2 <> '' then
                Recepients.Add(RecepientMail2);
            if RecepientMail3 <> '' then
                Recepients.Add(RecepientMail3);

            SMTPMail.CreateMessage('ERP', SenderMail, Recepients, Subject, '', TRUE);
            SMTPMail.AppendBody('Hello ,');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('You are registered to receive notifications related to CHI.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('This is a message to notify you that:');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Contract is going to expire for the below details:');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<table border="1" width="1000" cellspacing="0" cellpadding="0">');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<th width="200">Vendor No.</th>');
            SMTPMail.AppendBody('<th width="200">Vendor Name</th>');
            SMTPMail.AppendBody('<th width="200">Vendor Address</th>');
            SMTPMail.AppendBody('<th width="200">No. of Open Contracts</th>');
            SMTPMail.AppendBody('<th width="200">No. of Expired Contracts</th>');
            SMTPMail.AppendBody('<th width="200">Comment</th>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            Vendor.Reset();
            Vendor.SetRange("Vendor Type", Vendor."Vendor Type"::Import);
            if Vendor.FindSet() then begin
                Vendor.CalcFields("Balance (LCY)");
                if Vendor."Balance (LCY)" < 150000000 then
                    repeat
                        Clear(CountOpen);
                        Clear(CountExpired);
                        ConPro.Reset();
                        ConPro.SetRange("Vendor No.", Vendor."No.");
                        ConPro.SetRange(Status, ConPro.Status::Open);
                        if ConPro.FindSet() then
                            CountOpen := ConPro.Count;

                        ConPro.Reset();
                        ConPro.SetRange("Vendor No.", Vendor."No.");
                        ConPro.SetRange(Status, ConPro.Status::Expired);
                        if ConPro.FindSet() then
                            CountExpired := ConPro.Count;

                        if (CountOpen <> 0) AND (CountExpired = 0) then
                            CommentText := 'All Contracts are in Open';

                        if (CountOpen = 0) AND (CountExpired <> 0) then
                            CommentText := 'All Contracts Expired';

                        if (CountOpen <> 0) AND (CountExpired <> 0) then
                            CommentText := 'Contracts Available';

                        if (CountOpen = 0) AND (CountExpired = 0) then
                            CommentText := 'No Contracts Available';

                        SMTPMail.AppendBody('<td>' + Vendor."No." + '</td>');
                        SMTPMail.AppendBody('<td>' + Vendor.Name + '</td>');
                        SMTPMail.AppendBody('<td>' + Vendor.Address + '</td>');
                        SMTPMail.AppendBody('<td>' + Format(CountOpen) + '</td>');
                        SMTPMail.AppendBody('<td>' + Format(CountExpired) + '</td>');
                        SMTPMail.AppendBody('<td>' + CommentText + '</td>');
                        SMTPMail.AppendBody('</tr>');
                    until Vendor.next = 0;
            end;
            SMTPMail.AppendBody('</table>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Thanking You.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Regards,');
            SMTPMail.AppendBody('<BR>');
            SMTPMail.AppendBody('CHI Ltd');
            SMTPMail.Send;
        END;
    end;
    //B2BMSOn21Oct21<<

    var
        myInt: Integer;
}