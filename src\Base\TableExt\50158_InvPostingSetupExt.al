tableextension 50158 InvPostingSetupExt extends "Inventory Posting Setup"
{
    fields
    {
        // Add changes to table fields here
        field(50000; "Item Charge (Interim)"; Code[20])
        {
            Caption = 'Item Charge (Interim)';
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
            Description = 'PROV1.0';
        }
        field(50001; "GL Services (Interim)"; Code[20])
        {
            Caption = 'GL Services (Interim)';
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
            Description = 'PROV1.0';
        }
    }

    var
        myInt: Integer;
}