page 50979 "POS Sales Transactions"
{
    ApplicationArea = All;
    Caption = 'POS Sales Transactions';
    PageType = List;
    SourceTable = "Retail Sales Transaction Log";
    UsageCategory = Lists;
    DeleteAllowed = false;

    layout
    {
        area(content)
        {
            repeater(Group)
            {
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = All;
                }
                field("Transaction ID"; "Transaction ID")
                {
                    ApplicationArea = All;
                }
                field("Type"; "Type")
                {
                    ApplicationArea = All;
                }
                field("Receipt No"; "Receipt No")
                {
                    ApplicationArea = All;
                }
                field("Amount"; "Amount")
                {
                    ApplicationArea = All;
                }
                field("Transactor ID"; "Transactor ID")
                {
                    ApplicationArea = All;
                }
                field("Transactor"; "Transactor")
                {
                    ApplicationArea = All;
                }
                field("Staff ID"; "Staff ID")
                {
                    ApplicationArea = All;
                }
                field("Staff Name"; "Staff Name")
                {

                }
                field("Staff"; "Staff")
                {
                    ApplicationArea = All;
                }
                field("Outlet ID"; "Outlet ID")
                {
                    ApplicationArea = All;
                }
                field("Outlet"; "Outlet")
                {
                    ApplicationArea = All;
                }
                field("Remarks"; "Remarks")
                {
                    ApplicationArea = All;
                }
                field(Processed; Processed)
                {
                    ApplicationArea = All;
                    editable = true;
                }
                field("Sales Order Created"; "Sales Order Created")
                {
                    ApplicationArea = All;

                }
                field("Date"; "Date")
                {
                    ApplicationArea = All;
                }
                field("Entered On"; "Entered On")
                {
                    ApplicationArea = All;
                }
                field("Entered By"; "Entered By")
                {
                    ApplicationArea = All;
                }

            }
        }
    }

    actions
    {
        area(processing)
        {
            action(ProcessTransactions)
            {
                Caption = 'Process Transactions';
                ApplicationArea = All;


                trigger OnAction()
                var
                    chiAPI: codeunit "CHI Retail Integrations";
                begin
                    // Call the codeunit procedure to process data
                    CHIAPI.GetSalesfromAPI();
                end;
            }
        }
    }

}
