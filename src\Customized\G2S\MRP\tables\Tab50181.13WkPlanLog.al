table 50181 "13Wk Log Header"
{
    Caption = '13Wk Log Header';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            Editable = false;
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup("13Wk Header".Description WHERE("No." = FIELD("No.")));
        }
        field(3; "Start Date"; Date)
        {
            Caption = 'Start Date';
            Editable = false;
        }
        field(4; "End Date"; Date)
        {
            Caption = 'End Date';
            Editable = false;
        }
        field(5; "Cc Code"; Code[20])
        {
            Caption = 'Cc Code';
            Editable = false;
        }
        field(6; "Created By "; Code[50])
        {
            Caption = 'Created By ';
            Editable = false;
        }
        field(7; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
        }
        field(8; "Updated By"; Code[50])
        {
            Caption = 'Updated By';
            Editable = false;
        }
        field(9; "Update Date"; Date)
        {
            Caption = 'Update Date';
            Editable = false;
        }
        field(10; Version; Code[50])
        {
            Caption = 'Version';
            Editable = false;
        }
        field(11; "Approval Status"; Enum ApprovalStatus)
        {
            Caption = 'Approval Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup("13Wk Header"."Approval Status" WHERE("No." = FIELD("No.")));
        }
    }

    keys
    {
        key(PK; Version, "No.")
        {
            Clustered = true;
        }
    }
}
