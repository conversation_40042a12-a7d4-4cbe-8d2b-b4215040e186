/// <summary>
/// PageExtension ProdOrdExt (ID 50109) extends Record Released Production Order.
/// </summary>
pageextension 50109 ProdOrdExt extends "Released Production Order"
{
    layout
    {
        addafter(Blocked)
        {

            field("Prod. Sheet Ref."; "Prod. Sheet Ref.")
            { }
            field("Prod Sheet Ref No"; "Prod. Sheet Ref. Line No.")
            { }
            field("Production Batch No."; "Production Batch No.")
            {
                Editable = false;
                ApplicationArea = All;
            }
            field("External Document No."; "External Document No.")
            {
                ApplicationArea = All;
            }//PkOnEXT
             //B2BMS
            field("Created By"; "Created By")
            {
                ApplicationArea = All;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = All;
            }
            field("Modified By"; "Modified By")
            {
                ApplicationArea = All;
            }
            field("Modified date"; "Modified date")
            {
                ApplicationArea = All;
            }
            //B2BMS
        }
        // G2s >>>>>> 10 Nov 2023

        addbefore("Production Batch No.")
        {
            field("Line No."; "Line No.")
            {
                Caption = 'Machine line no';
                // Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
                Editable = (OutputExist) and (("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit));
                ApplicationArea = All;
                trigger OnValidate()

                begin
                    CheckProdOrderAgainstSpecification(); // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
                    if (Rec."Line No." = '') then Error('Line No. cannot be empty');
                    Rec."Production Batch No." := '';
                    //GetLineNoSplit(Rec."Line No.");
                    Rec."Production Batch No." := Rec."Factory Code" + FORMAT(Rec."Due Date", 0, '<Day,2><Month,2><Year>') + Format(Rec."Shift Code") + GetLineNoSplit(Rec."Line No.");
                    Rec.Modify();
                end;
            }

            field("Factory Code"; "Factory Code")
            {
                //Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
                Editable = false;
                ApplicationArea = All;
                trigger OnValidate()
                var
                    LineNoSplit: Code[10];
                begin
                    if "Factory Code" = '' then Error('Factory Code cannot be empty');
                    Rec."Production Batch No." := '';
                    CheckProdOrderAgainstSpecification(); // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
                    // GetLineNoSplit(Rec."Line No.");
                    Rec."Production Batch No." := Rec."Factory Code" + FORMAT(Rec."Due Date", 0, '<Day,2><Month,2><Year>') + Format(Rec."Shift Code") + GetLineNoSplit(Rec."Line No.");
                    Rec.Modify();
                end;
            }
            field("Shift Code"; "Shift Code")
            {
                // Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
                Editable = (OutputExist) and (("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit));
                ApplicationArea = All;
                trigger OnValidate()
                var
                    LineNoSplit: Code[10];
                begin
                    // if (("Shift Code".AsInteger() <> 0) or ("Shift Code".AsInteger() <> 1) or ("Shift Code".AsInteger() <> 2)) then Error('Shift Code cannot be empty');
                    "Production Batch No." := '';
                    CheckProdOrderAgainstSpecification(); // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
                    //GetLineNoSplit(Rec."Line No.");
                    Rec."Production Batch No." := Rec."Factory Code" + FORMAT(Rec."Due Date", 0, '<Day,2><Month,2><Year>') + Format(Rec."Shift Code") + GetLineNoSplit(Rec."Line No.");
                    Rec.Modify();
                end;
            }

        }

        addafter("Last Date Modified")
        {
            field("Barcode Printed?"; "Barcode Printed?")
            {
                Caption = 'Barcode Label Printed';
                ApplicationArea = All;
                Enabled = false;
            }
        }
        // G2S <<<<<< 10 Nov 2023
        //g2s >>>>> 03 Nov 2023
        modify("No.")
        {
            Editable = ((("Barcode Printed?" = false) and ("Barcode Details Changed?" = false) or (not LPItemEnabled)));
            // Editable = false;
        }
        modify("Source No.")
        {
            // Editable = (Rec."Barcode Printed?" = false);
            Editable = ((("Barcode Printed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

            trigger OnBeforeValidate()
            var
                myInt: Integer;
            begin
                ValidateLPItem();
            end;

            trigger OnAfterValidate()
            var
                myInt: Integer;
            begin
                //122323
                ValidateLPItem();
                CheckProdOrderAgainstSpecification(); // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
                //122323
                // if (Rec."Source No." <> xRec."Source No.") and (Rec."Barcode Printed?") then begin
                // if ((Rec."Source No." <> xRec."Source No.") and (xRec."Source No." <> '') and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = false)) then begin
                // if (((Rec."Source No." <> xRec."Source No.") and (xRec."Source No." <> '') and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or ((GlobalProdEdit) and (Rec."Barcode Printed?" = true))) then begin
                //121223

                if LPItemEnabled then begin
                    if (((Rec."Source No." <> xRec."Source No.") and (xRec."Source No." <> '') and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or (GlobalProdEdit)) then begin
                        //121223
                        Rec."Barcode Details Changed?" := true;
                        Rec."Barcode Printed?" := false;
                        Rec."Refresh Count" := 0;
                        //211123 RKD
                        Rec."Barcode Printed?" := false;
                        Rec.Modify(true);
                        // CurrPage.Update(true);
                    end;
                end;
            end;
        }
        modify("Source Type")
        {
            // Editable = (Rec."Barcode Printed?" = false);
            //Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
            Editable = ((("Barcode Printed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

        }
        modify("Due Date")
        {
            // Editable = (Rec."Barcode Printed?" = false);
            // Editable = ((("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));
            Editable = (OutputExist) and (("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit));

            trigger OnBeforeValidate()
            var
                myInt: Integer;
            begin
                Rec.TestField("Source No.");
                ValidateLPItem();
            end;

            trigger OnAfterValidate()
            var
                myInt: Integer;
            begin
                Rec."Production Batch No." := '';
                // GetLineNoSplit(Rec."Line No.");
                Rec."Production Batch No." := Rec."Factory Code" + FORMAT(Rec."Due Date", 0, '<Day,2><Month,2><Year>') + Format(Rec."Shift Code") + GetLineNoSplit(Rec."Line No.");

                // if (Rec."Due Date" <> xRec."Due Date") and (Rec."Barcode Printed?") then begin
                // if (Rec."Due Date" <> xRec."Due Date") then begin
                // if (((Rec."Due Date" <> xRec."Due Date") and (xRec."Due Date" <> 0D) and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or ((GlobalProdEdit) and (Rec."Barcode Printed?" = true))) then begin
                //121233

                if LPItemEnabled then begin
                    if (((Rec."Due Date" <> xRec."Due Date") and (xRec."Due Date" <> 0D) and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or ((GlobalProdEdit))) then begin
                        //121223
                        Rec."Barcode Details Changed?" := true;
                        Rec."Refresh Count" := 0;
                        //211123 RKD
                        Rec."Barcode Printed?" := false;
                        Rec.Modify();
                        CurrPage.Update(true);
                    end;
                end;
            end;
        }
        modify(Quantity)
        {
            // Editable = (Rec."Barcode Printed?" = false);
            // Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
            Editable = ((("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

            trigger OnBeforeValidate()
            var
                myInt: Integer;
            begin
                Rec.TestField("Source No.");
                ValidateLPItem();
            end;

            trigger OnAfterValidate()
            var
                myInt: Integer;
            begin

                // if (Rec.Quantity <> xRec.Quantity) and (Rec."Barcode Printed?") then begin
                //if (Rec.Quantity <> xRec.Quantity) then begin
                // if ((Rec.Quantity <> xRec.Quantity) and (xRec.Quantity <> 0) and (Rec."Refresh Count" <> 0)) and (Rec."Barcode Printed?" = false) then begin
                // if (((Rec.Quantity <> xRec.Quantity) and (xRec.Quantity <> 0) and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or ((Rec."Barcode Printed?" = true) and (GlobalProdEdit))) then begin
                //121223
                if LPItemEnabled then begin
                    if (((Rec.Quantity <> xRec.Quantity) and (xRec.Quantity <> 0) and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or (GlobalProdEdit)) then begin
                        //121223
                        Rec."Barcode Details Changed?" := true;
                        Rec."Barcode Printed?" := false;
                        Rec."Refresh Count" := 0;
                        //211123 RKD
                        Rec.Modify();
                        //CurrPage.Update(true);
                    end;
                end;
                CheckProdOrderAgainstSpecification(); // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
            end;
        }
        modify("Starting Date-Time")
        {
            // Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
            Editable = ((("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

            trigger OnBeforeValidate()
            var
                myInt: Integer;
            begin
                Rec.TestField("Source No.");
                ValidateLPItem();
            end;

            trigger OnAfterValidate()
            var
                myInt: Integer;
            begin
                // if (Rec."Starting Date-Time" <> xRec."Starting Date-Time") and (Rec."Barcode Printed?") then begin
                //if (Rec."Starting Date-Time" <> xRec."Starting Date-Time") then begin
                // if (Rec."Starting Date-Time" <> xRec."Starting Date-Time") and (xRec."Starting Date-Time" <> 0DT) and ("Refresh Count" <> 0) and (Rec."Barcode Printed?" = false) then begin
                // if (((Rec."Starting Date-Time" <> xRec."Starting Date-Time") and (xRec."Starting Date-Time" <> 0DT) and ("Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or ((GlobalProdEdit and "Barcode Printed?" = true))) then begin
                //121223
                if LPItemEnabled then begin

                    if (((Rec."Starting Date-Time" <> xRec."Starting Date-Time") and (xRec."Starting Date-Time" <> 0DT) and ("Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or (GlobalProdEdit)) then begin
                        //121223
                        Rec."Barcode Details Changed?" := true;
                        Rec."Barcode Printed?" := false;
                        Rec."Refresh Count" := 0;
                        //211123 RKD
                        Rec."Barcode Printed?" := false;
                        Rec.Modify();
                        //CurrPage.Update(true);
                    end;
                end;
            end;
        }
        modify("Ending Date-Time")
        {
            //Editable = ("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit);
            Editable = ((("Barcode Printed?" = false and "Barcode Details Changed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

            trigger OnBeforeValidate()
            var
                myInt: Integer;
            begin
                Rec.TestField("Source No.");
                ValidateLPItem();
            end;


            trigger OnAfterValidate()
            var
                myInt: Integer;
            begin
                //if (Rec."Ending Date-Time" <> xRec."Ending Date-Time") and (Rec."Barcode Printed?") then begin
                // if (Rec."Ending Date-Time" <> xRec."Ending Date-Time") then begin
                // if (Rec."Ending Date-Time" <> xRec."Ending Date-Time") and (xRec."Ending Date-Time" <> 0DT) and ("Refresh Count" <> 0) and (Rec."Barcode Printed?" = false) then begin
                // if (((Rec."Ending Date-Time" <> xRec."Ending Date-Time") and (xRec."Ending Date-Time" <> 0DT) and ("Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or ((GlobalProdEdit) and ("Barcode Printed?" = true))) then begin
                if LPItemEnabled then begin
                    if (((Rec."Ending Date-Time" <> xRec."Ending Date-Time") and (xRec."Ending Date-Time" <> 0DT) and ("Refresh Count" <> 0) and (Rec."Barcode Printed?" = true)) or (GlobalProdEdit)) then begin
                        Rec."Barcode Details Changed?" := true;
                        Rec."Barcode Printed?" := false;
                        Rec."Refresh Count" := 0;
                        //211123 RKD
                        Rec."Barcode Printed?" := false;
                        Rec.Modify();
                        // CurrPage.Update(true);
                    end;
                end;
            end;
        }
        modify("Location Code")
        {
            Editable = ((("Barcode Printed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));
        }
        modify("Shortcut Dimension 1 Code")
        {
            //Editable = ("Barcode Printed?" = false) or (GlobalProdEdit);
            Editable = ((("Barcode Printed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

        }
        modify("Shortcut Dimension 2 Code")
        {
            // Editable = ("Barcode Printed?" = false) or (GlobalProdEdit);
            Editable = ((("Barcode Printed?" = false) or (GlobalProdEdit)) or (not LPItemEnabled));

        }
        modify(ProdOrderLines)
        {
            Visible = false;
            //    UpdatePropagation = Both;
        }
        addafter(ProdOrderLines)
        {
            part(ProdOrderLiesCust; "Rel. Prod Order Customized")
            {
                ApplicationArea = Manufacturing;
                SubPageLink = "Prod. Order No." = FIELD("No.");
                UpdatePropagation = SubPart;
            }
        }
        //g2s <<<<< 10 Nov 2023

    }
    actions
    {
        modify(RefreshProductionOrder)
        {
            Enabled = ((GlobalProdEdit Or ("Barcode Details Changed?" = false and "Barcode Printed?" = false)) or (not LPItemEnabled));

            trigger OnBeforeAction()
            var
                myInt: Integer;
            begin
                Rec.TestField("Factory Code");
                Rec.TestField("Line No.");
                Rec.TestField("Source No.");
                //Rec.TestField("Shift Code");
                Rec.TestField("Due Date");
                Rec.TestField("Production Batch No.");
                Rec.TestField("Location Code");
                Rec.TestField("External Document No.");
                ValidateLPItem();
                Rec.Validate("Due Date");
            end;
            //Enabled = ("Barcode Printed?" = false) or GlobalProdEdit;
            //>>>>> G2S 071223
            trigger OnAfterAction()

            var
                Item, ItemCopy, ItemCopy2 : Record Item;
                TrackSpec: Record "Tracking Specification";
                ExpDateString: Code[10];
                IWXLPHeader, IWXLPHeaderCopy, IWXLPHeaderCopy2 : Record "IWX LP Header";
                IUOM: Record "Item Unit of Measure";
                i, LPNoToCreate : Integer;
                ProdOrderComp: Record "Prod. Order Component";
                ItemSub: Record "Item Substitution";
                ItemErrorList, ItemToBeSub, SubstituedItemList, ItemQtyExpList : List of [Text];
                ZeroQtyInSubItem, SubItemExist, TrackSpecNotFound : Boolean;
                ZeroQtyLabel: Label 'The following item(s) have zero/insufficient quantity(ies) in inventory: %1. Expected Qty(ies) needed to fulfil the order is/are: %2';
                ReplacedItemLable: Label 'The following item(s): %1 has(ve) been replaced due to insufficient qty(ies) in inventory. They have been replaced with: %2 ';
                itemlist, DisplayItemErrorList, ItemToBeSubMsgList, SubstituedItemMsgList, QtyExpList : Text;
                ProdOrderLine, ProdOrderLine2 : Record "Prod. Order Line";
                RefProdOrder: Report "Refresh Production Order";
                AssignItemToBeSub: Code[20];
                ExpQty, NetAvailQtyItem, NetAvailQtySub : Decimal;
                ProdOrderCompLine, ProdOrderCompLineCopy, ProdOrderCompLineCopy3 : Record "Prod. Order Component";
                ReservationEtEntry: Record "Reservation Entry";
                ProdOrderCopy: Record "Production Order";
                IUOMQty: Decimal;
                ProdOrderCompUOM: Code[20];
                //121323
                ProdOrderLineCo: Record "Prod. Order Line";
            //121323
            begin
                if LPItemEnabled then begin

                    //121323
                    ProdOrderLineCo.Reset();
                    ProdOrderLineCo.SetRange("Prod. Order No.", Rec."No.");
                    if not ProdOrderLineCo.FindFirst() then exit;

                    Rec.TestField("Location Code");
                    //check if LP is enabled and check Expiration date
                    ItemCopy.Reset();
                    ItemCopy.SetRange("No.", Rec."Source No.");
                    ItemCopy.SetRange("License Plate Enabled?", true);
                    if ItemCopy.FindFirst() then begin
                        ItemCopy.TestField("Expiration Calculation");
                        ItemCopy.TestField("Production BOM No.");
                    end else begin
                        //Error(Txt001);
                    end;
                    //030424 material availability relaxation
                    CustomSetup.Reset;
                    CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
                    if not CustomSetup.FindFirst() then exit;
                    // If CustomSetup."Validate Material Availability" then begin
                    ProdOrderComp.Reset();
                    ProdOrderComp.SetRange("Prod. Order No.", Rec."No.");
                    if ProdOrderComp.FindFirst() then begin
                        IUOMQty := 0;
                        ZeroQtyInSubItem := false;
                        ZeroQtyInSubItemMain := false;
                        SubItemExist := false;
                        repeat
                            ProdOrderComp."Expected Quantity" := ProdOrderComp."Expected Quantity" * ProdOrderComp."Qty. per Unit of Measure";
                            ProdOrderCompUOM := ProdOrderComp."Unit of Measure Code";
                            NetAvailQtyItem := 0;
                            ItemCopy2.Reset;
                            ItemCopy2.SetRange("No.", ProdOrderComp."Item No.");
                            ItemCopy2.SetRange("Location Filter", Rec."Location Code");
                            if Itemcopy2.FindFirst() then begin
                                ItemCopy2.CalcFields("Net Change", "Qty. on Component Lines", "Qty. on Prod. Order", "Reserved Qty. on Inventory");

                                //NetAvailQtyItem := ItemCopy2."Net Change" - ItemCopy2."Qty. on Component Lines" - ItemCopy2."Qty. on Prod. Order" - ItemCopy2."Reserved Qty. on Inventory";
                                // NetAvailQtyItem := ItemCopy2."Net Change" - ItemCopy2."Qty. on Component Lines" - ItemCopy2."Qty. on Prod. Order"
                                ProdOrderCompLine.Reset();
                                ProdOrderCompLine.SetFilter(Status, '%1..%2', ProdOrderCompLine.Status::Planned, ProdOrderCompLine.Status::Released);
                                ProdOrderCompLine.SetRange("Item No.", ItemCopy2."No.");

                                // ProdOrderCompLine.SetRange("Shortcut Dimension 1 Code", Rec."Shortcut Dimension 1 Code");
                                // ProdOrderCompLine.SetRange("Shortcut Dimension 2 Code", Rec."Shortcut Dimension 2 Code");
                                ProdOrderCompLine.SetRange("Location Code", Rec."Location Code");
                                ProdOrderCompLine.SetFilter("Prod. Order No.", '<>%1', Rec."No.");
                                ProdOrderCompLine.CalcSums("Remaining Qty. (Base)");

                                //ProdOrderCompLineCopy.Copy(ProdOrderCompLine);
                                ProdOrderCompLineCopy.Reset();
                                ProdOrderCompLineCopy.SetRange("Prod. Order No.", Rec."No.");
                                ProdOrderCompLineCopy.SetRange("Item No.", ItemCopy2."No.");
                                If not ProdOrderCompLineCopy.FindFirst() then begin
                                    //
                                end;
                                //calculate qty on prod order
                                // ReservationEtEntry.Reset();
                                // ReservationEtEntry.SetRange("Item No.", ItemCopy2."No.");
                                // ReservationEtEntry.SetRange("Reservation Status", ReservationEtEntry."Reservation Status"::Reservation);
                                // ReservationEtEntry.SetRange("Location Code", Rec."Location Code");
                                // ReservationEtEntry.CalcSums("Quantity (Base)");
                                NetAvailQtyItem := ItemCopy2."Net Change" - ProdOrderCompLine."Remaining Qty. (Base)" - ItemCopy2."Qty. on Prod. Order";
                                // //27.11.23
                                // NetAvailQtyItem := ItemCopy2."Net Change" - (ProdOrderCompLine."Remaining Qty. (Base)" * ProdOrderComp."Qty. per Unit of Measure") - ItemCopy2."Qty. on Prod. Order";
                                // //27.11.23
                                // NetAvailQtyItem := ItemCopy2."Net Change" - ProdOrderCompLine."Remaining Qty. (Base)" - ReservationEtEntry."Quantity (Base)";
                            end;
                            ProdOrderComp.CalcFields("Available Qty");
                            // ProdOrderComp.CalcSums("Expected Quantity");
                            //if ProdOrderComp."Available Qty" < ProdOrderComp."Expected Quantity" then begin
                            // if ProdOrderComp."Available Qty" < (ItemCopy2."Net Change" - ItemCopy2."Qty. on Component Lines" - ItemCopy2."Reserved Qty. on Inventory") then begin
                            if NetAvailQtyItem < ProdOrderComp."Expected Quantity" then begin
                                AssignItemToBeSub := '';
                                ExpQty := 0;
                                IUOMQty := 0;
                                //check substitute item
                                ItemSub.Reset();
                                ItemSub.SetRange("No.", ProdOrderComp."Item No.");
                                ItemSub.SetRange("Sub. Item Blocked", false);
                                ItemSub.SetRange("Substitute Type", ItemSub."Substitute Type"::Item);
                                ItemSub.SetRange("Variant Code", ProdOrderComp."Variant Code");
                                if ItemSub.FindFirst() then begin
                                    NetAvailQtySub := 0;
                                    Item.Reset();
                                    Item.SetRange("No.", ItemSub."Substitute No.");
                                    Item.SetRange("Location Filter", Rec."Location Code");
                                    if Item.FindFirst() then begin
                                        Item.CalcFields("Net Change", "Qty. on Component Lines", "Qty. on Prod. Order", "Reserved Qty. on Inventory");
                                        // //29.11.23 
                                        If ProdOrderComp."Unit of Measure Code" <> Item."Base Unit of Measure" then begin
                                            IUOM.Reset();
                                            IUOM.SetRange("Item No.", ProdOrderComp."Item No.");
                                            IUOM.SetRange(Code, ProdOrderComp."Unit of Measure Code");
                                            If IUOM.FindFirst() then begin
                                                //<<<<<<<<<< 14/06/24 CAS-01308-K6Y3T1
                                                ProdOrderCompUOM := Item."Base Unit of Measure";
                                                //<<<<<<<<<< 14/06/24 CAS-01308-K6Y3T1
                                                IUOMQty := IUOM."Qty. per Unit of Measure";
                                                // Message(format(IUOMQty));
                                            end;
                                        end;
                                        // //29.11.23
                                        //231023 change qty. on comp lines calc
                                        ProdOrderCompLine.Reset();
                                        ProdOrderCompLine.SetFilter(Status, '%1..%2', ProdOrderCompLine.Status::Planned, ProdOrderCompLine.Status::Released);
                                        ProdOrderCompLine.SetRange("Item No.", Item."No.");
                                        // ProdOrderCompLine.SetRange("Shortcut Dimension 1 Code", Rec."Shortcut Dimension 1 Code");
                                        // ProdOrderCompLine.SetRange("Shortcut Dimension 2 Code", Rec."Shortcut Dimension 2 Code");
                                        ProdOrderCompLine.SetRange("Location Code", Rec."Location Code");
                                        ProdOrderCompLine.SetFilter("Prod. Order No.", '<>%1', Rec."No.");
                                        ProdOrderCompLine.CalcSums("Remaining Qty. (Base)");
                                        //ProdOrderCompLineCopy.Copy(ProdOrderCompLine);
                                        // ProdOrderCompLineCopy.Reset();
                                        // ProdOrderCompLineCopy.SetRange("Prod. Order No.", Rec."No.");
                                        // ProdOrderCompLineCopy.SetRange("Item No.", ItemCopy2."No.");
                                        // If not ProdOrderCompLineCopy.FindFirst() then begin
                                        //     //
                                        // end;
                                        // // //calculate qty on prod order
                                        // ReservationEtEntry.Reset();
                                        // ReservationEtEntry.SetRange("Item No.", Item."No.");
                                        // ReservationEtEntry.SetRange("Reservation Status", ReservationEtEntry."Reservation Status"::Reservation);
                                        // ReservationEtEntry.SetRange("Location Code", Rec."Location Code");
                                        // ReservationEtEntry.CalcSums("Quantity (Base)");

                                        NetAvailQtySub := Item."Net Change" - ProdOrderCompLine."Remaining Qty. (Base)" - Item."Qty. on Prod. Order";

                                        // if IUOMQty <> 0 then begin
                                        //     NetAvailQtySub := Item."Net Change" - (ProdOrderCompLine."Remaining Qty. (Base)" * IUOMQty) - Item."Qty. on Prod. Order";
                                        // end;
                                        // //27.11.23
                                        // NetAvailQtySub := Item."Net Change" - (ProdOrderCompLine."Remaining Qty. (Base)" * ProdOrderComp."Qty. per Unit of Measure") - Item."Qty. on Prod. Order";
                                        // //27.11.23
                                        // NetAvailQtySub := Item."Net Change" - ProdOrderCompLine."Remaining Qty. (Base)" - ReservationEtEntry."Quantity (Base)";
                                        // 231023 end

                                        if NetAvailQtySub < ProdOrderComp."Expected Quantity" then begin
                                            //041524
                                            if CustomSetup."Validate Material Availability" then begin
                                                ExpQty := ProdOrderComp."Expected Quantity" - NetAvailQtyItem;
                                                ItemErrorList.Add(ProdOrderComp."Item No.");
                                                ItemQtyExpList.Add(Format(ExpQty));
                                                ZeroQtyInSubItem := true;
                                                ZeroQtyInSubItemMain := true;
                                            end;
                                            //041524 end
                                        end else begin
                                            if not (ItemSub."Sub. Item Blocked") then begin
                                                AssignItemToBeSub := ProdOrderComp."Item No.";
                                                //ProdOrderComp."Item No." := ItemSub."Substitute No.";
                                                ProdOrderComp."Item No." := ItemSub."Substitute No.";
                                                ProdOrderComp.Validate(ProdOrderComp."Item No.");
                                                // ProdOrderComp."Available Qty" := Item."Net Change" - Item."Qty. on Prod. Order" - Item."Qty. on Component Lines" - Item."Reserved Qty. on Inventory";
                                                //  ProdOrderComp."Available Qty" := Item."Net Change" - Item."Qty. on Prod. Order" - Item."Qty. on Component Lines";

                                                //ProdOrderComp."Available Qty" := Item."Net Change" - Item."Qty. on Prod. Order" - ProdOrderCompLine."Remaining Qty. (Base)";
                                                // ProdOrderComp."Unit of Measure Code" := ProdOrderCompUOM;
                                                IF IUOMQty <> 0 then begin
                                                    // ProdOrderComp."Available Qty" := Item."Net Change" - Item."Qty. on Prod. Order" - (ProdOrderCompLine."Remaining Qty. (Base)" * IUOMQty);
                                                    //ProdOrderComp."Unit of Measure Code" := ProdOrderCompUOM;
                                                    ProdOrderComp."Expected Quantity" := Rec.Quantity * IUOMQty;
                                                    //ProdOrderComp."Quantity per" := ProdOrderComp."Quantity per" * IUOMQty;
                                                    ProdOrderComp."Is Substitue Item?" := true;
                                                    ProdOrderComp."Unit of Measure Code" := ProdOrderCompUOM;
                                                    // ProdOrderComp.Quantity := ProdOrderComp."Quantity per" * IUOMQty;
                                                    ProdOrderComp.Validate(ProdOrderComp.Quantity);
                                                    ProdOrderComp.Validate("Unit of Measure Code");
                                                    //ProdOrderComp."Quantity (Base)" := ProdOrderComp."Quantity per" * IUOMQty;
                                                    // ProdOrderComp.Validate(ProdOrderComp."Quantity (Base)");
                                                    // ProdOrderComp.Validate("Expected Quantity");
                                                end;
                                                ProdOrderComp.Modify();
                                                SubstituedItemList.Add(ItemSub."Substitute No.");
                                                SubItemExist := true;
                                            end;
                                            if SubItemExist then begin
                                                ItemToBeSub.Add(AssignItemToBeSub);
                                            end;
                                        end;
                                    end
                                    else begin
                                        //041524
                                        if CustomSetup."Validate Material Availability" then begin
                                            ExpQty := ProdOrderComp."Expected Quantity" - NetAvailQtyItem;
                                            ItemErrorList.Add(ProdOrderComp."Item No.");
                                            ItemQtyExpList.Add(Format(ExpQty));
                                            ZeroQtyInSubItem := true;
                                            ZeroQtyInSubItemMain := true;
                                        end;
                                    end;
                                end else begin
                                    //041524
                                    if CustomSetup."Validate Material Availability" then begin
                                        ExpQty := ProdOrderComp."Expected Quantity" - NetAvailQtyItem;
                                        ItemErrorList.Add(ProdOrderComp."Item No.");
                                        ItemQtyExpList.Add(Format(ExpQty));
                                        ZeroQtyInSubItem := true;
                                        ZeroQtyInSubItemMain := true;
                                    end;
                                end;
                            end;

                        until ProdOrderComp.Next() = 0;
                        foreach itemlist in ItemErrorList do begin
                            if ItemErrorList.Count > 0 then
                                DisplayItemErrorList += itemlist + '|'
                            else
                                DisplayItemErrorList := itemlist;
                        end;
                        itemlist := '';
                        foreach itemlist in SubstituedItemList do begin

                            if SubstituedItemList.Count > 0 then
                                SubstituedItemMsgList += itemlist + '|'
                            else
                                SubstituedItemMsgList := itemlist;
                        end;
                        itemlist := '';
                        foreach itemlist in ItemToBeSub do begin

                            if ItemToBeSub.Count > 0 then
                                ItemToBeSubMsgList += itemlist + '|'
                            else
                                ItemToBeSubMsgList := itemlist;
                        end;

                        itemlist := '';
                        foreach itemlist in ItemQtyExpList do begin
                            if ItemQtyExpList.Count > 0 then
                                QtyExpList += itemlist + '|'
                            else
                                QtyExpList := itemlist;
                        end;



                    end;
                    //     //030424 material availability relaxation end
                    // end;

                    //if not ZeroQtyInSubItem then begin
                    if ((not ZeroQtyInSubItem) or (CustomSetup."Validate Material Availability")) then begin
                        //update expiry date  
                        Item.Reset();
                        Item.SetRange("No.", Rec."Source No.");
                        Item.SetRange("License Plate Enabled?", true);
                        if item.FindFirst() then begin
                            TrackSpec.Reset();
                            // TrackSpec.SetRange("Item No.", item."No."); //Commented to enable only one Rec in TrackingSpec. // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
                            TrackSpec.SetRange("Source ID", Rec."No.");
                            if not TrackSpec.FindFirst() then begin
                                TrackSpec.init();
                                TrackSpecNotFound := true;
                            end;
                            begin
                                //if TrackSpec."Expiration Date" = 0D then begin
                                TrackSpec."Expiration Date" := CalcDate(Item."Expiration Calculation", Rec."Due Date");
                                TrackSpec."Creation Date" := Today;
                                TrackSpec."Source ID" := Rec."No.";
                                TrackSpec."Item No." := Rec."Source No.";
                                TrackSpec."Location Code" := Rec."Location Code";
                                TrackSpec."Source Ref. No." := 10000;
                                //Message('New expiry date: ' + Format(TrackSpec."Expiration Date"));
                                //calcualte new Lot no 
                                ExpDateString := Format(TrackSpec."Expiration Date", 10, '<Day,2><Month,2><Year,2>');
                                //TrackSpec."Lot No." := Rec."Source No." + Format(Date2DMY(Rec."Due Date", 1)) + Format(Date2DMY(Rec."Due Date", 2)) + format(Date2DMY(Rec."Due Date", 3)); 
                                if Rec."Production Batch No." <> '' then
                                    TrackSpec."Lot No." := Rec."Production Batch No."
                                else
                                    Error('Prod. Batch No is blank for order: ', Rec."No.");
                                //TrackSpec."Quantity (Base)" := Rec.Quantity;
                                TrackSpec.Validate("Quantity (Base)", Rec.Quantity);
                                if TrackSpecNotFound then
                                    TrackSpec."Entry No." := CustomCode.GetLastEntryNo();
                                //Message(('New Lot no: ' + TrackSpec."Lot No."));
                                if TrackSpecNotFound then
                                    TrackSpec.Insert()
                                else
                                    TrackSpec.Modify();
                                //end;
                            end;
                        end;
                        //09 11 2023
                        //Check if any of the main fields have changed
                        // //auto-generate LP records

                        IWXLPHeaderCopy.Reset();
                        IWXLPHeaderCopy.SetRange("Production Order No.", Rec."No.");
                        //if Rec."Barcode Printed?" and Rec."Barcode Details Changed?" and (Rec."Refresh Count" = 0) then begin
                        if (Rec."Barcode Details Changed?" and (Rec."Refresh Count" = 0)) then begin
                            IWXLPHeaderCopy2.Copy(IWXLPHeaderCopy);
                            IWXLPHeaderCopy2.DeleteAll();
                        end;


                        //121223
                        if ((Rec."Barcode Details Changed?" = false) and (Rec."Refresh Count" <> 0) and (Rec."Barcode Printed?" = false) and not (GlobalProdEdit)) then begin
                            IWXLPHeaderCopy2.Copy(IWXLPHeaderCopy);
                            IWXLPHeaderCopy2.DeleteAll();
                        end;
                        //121223
                        if NOT IWXLPHeaderCopy.FindFirst() then begin
                            Item.Reset();
                            Item.SetRange("No.", Rec."Source No.");
                            Item.SetRange("License Plate Enabled?", true);
                            if Item.FindFirst() then begin
                                IUOM.Reset();
                                IUOM.SetRange("Item No.", Rec."Source No.");
                                IUOM.SetRange(Code, 'PALLETS');
                                if IUOM.FindFirst() then begin
                                    LPNoToCreate := ROUND((Rec.Quantity / IUOM."Qty. per Unit of Measure"), 1, '<');
                                    while (i < LPNoToCreate) do begin
                                        // IWXLPHeader.Reset();
                                        IWXLPHeader.Init();
                                        IWXLPHeader."Location Code" := Rec."Location Code";
                                        IWXLPHeader."Production Order No." := Rec."No.";
                                        //311023
                                        // IWXLPHeader."No." := GetLastLPHeadRecord();
                                        //311023
                                        IWXLPHeader."No." := '';
                                        IWXLPHeader."Created By" := UserId;
                                        IWXLPHeader."Created On" := Today;
                                        IWXLPHeader.Insert(true);
                                        i += 1;
                                    end;
                                end;
                            end;
                        end;
                        // end;
                        // 09 11 2023
                        CheckProdOrderLine(Rec."No.");
                        // end;
                        Rec."Refresh Count" += 1;
                        Rec."Error Exists on Refresh?" := false;
                        Rec.Modify();
                        CurrPage.Update(true);
                    end;

                    //if ZeroQtyInSubItem then begin
                    if CustomSetup."Validate Material Availability" then begin
                        if ZeroQtyInSubItem then begin
                            ProdOrderLine2.Reset();
                            ProdOrderLine2.SetRange("Prod. Order No.", Rec."No.");
                            ProdOrderLine2.DeleteAll();
                            ProdOrderCopy.Reset();
                            ProdOrderCopy.SetRange("No.", Rec."No.");
                            ProdOrderCopy.SetRange("Error Exists on Refresh?", false);
                            If ProdOrderCopy.FindFirst() then begin
                                ProdOrderCopy."Error Exists on Refresh?" := true;
                                ProdOrderCopy.Modify();
                            end;
                            //Error(ZeroQtyLabel, DisplayItemErrorList, QtyExpList);
                            Message(ZeroQtyLabel, DisplayItemErrorList, QtyExpList);
                            CurrPage.Close();
                        end;
                        if SubItemExist and not ZeroQtyInSubItem then
                            Message(ReplacedItemLable, ItemToBeSubMsgList, SubstituedItemMsgList);
                    end;
                end;

                //update production order component location
                // ProdOrderCompLineCopy3.Reset();
                // ProdOrderCompLineCopy3.SetRange("Prod. Order No.", Rec."No.");
                // if ProdOrderCompLineCopy3.FindFirst() then
                //     repeat
                //         ProdOrderCompLineCopy3."Location Code" := Rec."Location Code";
                //         ProdOrderCompLineCopy3.Modify();
                //     until ProdOrderCompLineCopy3.Next() = 0;
                ProdOrderCompLineCopy3.Reset();
                ProdOrderCompLineCopy3.SetRange("Prod. Order No.", Rec."No.");
                ProdOrderCompLineCopy3.ModifyAll("Location Code", Rec."Location Code");
            end;
            // end;
            //<<<<< G2S 071223
            //update production order component line


        }
        addafter(RefreshProductionOrder)
        {
            action("Create Transfer Ticket")
            {
                Image = CreateDocument;
                ApplicationArea = all;
                trigger OnAction()
                var
                    myInteger: Integer;
                begin
                    If Confirm('Do you want to create Transfer Ticket Document ?', True, False) then
                        CreateTT();
                end;
            }


            //>>>>>> G2S 071123
            //CREATE BARCODE
            action("Print Barcode")
            {
                Caption = 'Generate Barcode';
                Image = BarCode;
                ApplicationArea = All;
                Promoted = true;
                PromotedIsBig = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                // Enabled = ((BarcodeEnabled and ("Barcode Printed?" = false)) or GlobalProdEdit);
                //Enabled = ("Barcode Printed?" = false) or GlobalProdEdit;
                Enabled = (GlobalProdEdit or ("Barcode Printed?" = false and "Barcode Details Changed?" = false));
                Visible = LPItemEnabled;

                trigger OnAction()
                var
                    Barcode: Report "Barcode Report Customization";
                    ProdOrder: Record "Production Order";
                    Item: Record Item;
                    Txt002: Label 'Changes cannot be made to the production order after the print button is clicked. Would you like to proceed?';
                    ProdOrderLine: Record "Prod. Order Line";
                    ProdOrderRep: Report "Barcode Report Customization";
                    ProdOrderReport: Record "Production Order Report";
                begin
                    Prodorder.SetRange("No.", Rec."No.");
                    if ProdOrder.FindFirst() then begin
                        Item.Reset();
                        Item.SetRange("License Plate Enabled?", true);
                        if Item.FindFirst() then begin

                            if GlobalProdEdit then begin
                                // CreateProdOrderRec(ProdOrder);
                                Report.RunModal(50041, true, true, ProdOrder)
                            end else begin
                                if Confirm(Txt002, false) then begin
                                    //   CreateProdOrderRec(Rec);
                                    Report.RunModal(50041, true, true, ProdOrder);

                                end;
                            end;
                        end
                        else
                            Error(Txt001, Rec."No.");
                    end
                end;
                //<<<<<< G2S 110723

                // end;
            }
            //<<<<<< G2S 110723
        }
    }
    trigger OnAfterGetRecord()
    var
        myInt: Integer;
        ProdOrderLineGet: Record "Prod. Order Line";
    begin
        ValidateLPItem();
        OutputExist := NOT Rec.ItemLedgerExist();    //G2S>22/05/25 9254_CAS-01418-M5L2V9
        //030424 material availability relaxation
        CustomSetup.Reset;
        CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
        if not CustomSetup.FindFirst() then exit;
        if CustomSetup."Validate Material Availability" then begin
            if LPItemEnabled then begin
                If (Rec."Error Exists on Refresh?" = true) then begin
                    ProdOrderLineGet.Reset();
                    ProdOrderLineGet.SetRange("Prod. Order No.", Rec."No.");
                    ProdOrderLineGet.DeleteAll();
                    Commit();
                end;
            end;
        end;
        //G2S >>>> 14.02.24 PROJECT LEAP
        if Rec."Due Date" <> 0D then begin
            Rec."Production Batch No." := '';
            //GetLineNoSplit(Rec."Line No.");
            Rec."Production Batch No." := Rec."Factory Code" + FORMAT(Rec."Due Date", 0, '<Day,2><Month,2><Year>') + Format(Rec."Shift Code") + GetLineNoSplit(Rec."Line No.");
            Rec.Modify();
            Commit();
        end;
        //G2S <<<< 14.02.24 PROJECT LEAP
    end;

    trigger OnOpenPage()
    var
        myInt: Integer;
    begin
        ZeroQtyInSubItemMain := false;
        CheckProdOrderLine(Rec."No.");
        // isEditble := true;
        ValidateLPItem();
    end;

    // >>>>>>>G2S>>>>22/05/25 9254_CAS-01418-M5L2V9
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    begin
        CurrPage.Update(false);
    end;
    // >>>>>>>G2S>>>>22/05/25 9254_CAS-01418-M5L2V9

    // trigger OnAfterGetRecord()
    // // var
    // //     myInt: Integer;
    // begin
    //     //     ZeroQtyInSubItemMain := false;
    //    // ValidatePCQ(Rec);
    // end;



    trigger OnAfterGetCurrRecord()
    var
        // IWXLP: Record "IWX LP Header";
        // LPLine: Record "IWX LP Line";
        ProdOrder: Record "Production Order";
        ToBePostedBaseConsumpQty: Decimal;
        ProdOrderComp: Record "Prod. Order Component";
        ItemJnlLine: Record "Item Journal Line";
        Item: Record Item;
        ItemLedgEntry, ItemLedgEntryCopy : Record "Item Ledger Entry";
        ConsumptionCount, i : Integer;
        RelProdOrderLine, ProdOrderLine : Record "Prod. Order Line";
        // IWXLPLine: Record "IWX LP Line";
        PCQ, POQ, LPScannedQty : Decimal;

    begin
        ValidateLPItem();
        OutputExist := NOT Rec.ItemLedgerExist();  //G2S>22/05/25 9254_CAS-01418-M5L2V9
        //030424 material availability relaxation
        CustomSetup.Reset;
        CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
        if not CustomSetup.FindFirst() then exit;
        if CustomSetup."Validate Material Availability" then begin
            if LPItemEnabled then begin
                // validate insufficient components before deleting line
                If ZeroQtyInSubItemMain then begin
                    ProdOrderLine.Reset();
                    ProdOrderLine.SetRange(Status, Rec.Status);
                    ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
                    ProdOrderLine.SetRange("Item No.", Rec."Source No.");
                    ProdOrderLine.DeleteAll();

                    //delete prod order component
                    ProdOrderComp.Reset();
                    ProdOrderComp.SetRange("Prod. Order No.", Rec."No.");
                    ProdOrderComp.DeleteAll();
                    // Message('Deleting Prod. Order Line No. %1', "No.");
                end;
            end;
        end;
        //validate prod order line
        CheckProdOrderLine(Rec."No.");




        //check user setup of user
        UserSetup.Reset();
        UserSetup.SetRange("User ID", USERID);
        if UserSetup.FindFirst() then begin
            if UserSetup."Can Modify Prod. Order" then
                GlobalProdEdit := true
            else
                GlobalProdEdit := false;
        end;

        ZeroQtyInSubItemMain := false;
        ConfirmRefOrder := false;
        // CurrPage.Update(true);


        //G2S >>>> 14.02.24 PROJECT LEAP
        if Rec."Due Date" <> 0D then begin
            Rec."Production Batch No." := '';
            //GetLineNoSplit(Rec."Line No.");
            Rec."Production Batch No." := Rec."Factory Code" + FORMAT(Rec."Due Date", 0, '<Day,2><Month,2><Year>') + Format(Rec."Shift Code") + GetLineNoSplit(Rec."Line No.");
            Rec.Modify();
            Commit();
        end;
        //G2S <<<< 14.02.24 PROJECT LEAP
        Rec.modify(true);
    end;



    //G2S Aug 16 2023


    /// <summary>
    /// GetLineNoSplit
    /// </summary>
    /// <param name="LineNo">VAR Code[20].</param>
    /// <returns>Return value of type Code[10] {.</returns>
    procedure GetLineNoSplit(var LineNo: Code[20]): Code[10]
    var
        LineNoSplit: Code[10];
    begin
        if LineNo <> '' then begin
            LineNoSplit := DELCHR(Rec."Line No.", '=', 'LINE');
            LineNoSplit := DelChr(LineNoSplit, '=', ' ');
            exit(LineNoSplit);
        end;
    end;

    /// <summary>
    /// CheckProdOrderLine.
    /// </summary>
    /// <param name="ProdNo">VAR Code[20].</param>
    /// <returns>Return value of type Boolean.</returns>
    procedure CheckProdOrderLine(var ProdNo: Code[20])
    var
        ProdOrderline: Record "Prod. Order Line";
    begin
        ProdOrderline.Reset();
        ProdOrderline.SetRange("Prod. Order No.", Rec."No.");
        if ProdOrderline.FindFirst() then begin
            BarcodeEnabled := true;
        end else
            BarcodeEnabled := false;
    end;

    /// <summary>
    /// ValidatePCQ.
    /// </summary>
    /// <param name="ProdOrder">VAR Record "Production Order".</param>
    procedure ValidatePCQ(var ProdOrder: Record "Production Order")
    var
        Item: Record Item;
        ItemLedgEntry, ItemLedgEntryCopy : Record "Item Ledger Entry";
        ProdOrderComp, ProdOrderCompCopy : Record "Prod. Order Component";
        PCQ, POQ : Integer;
        RelProdOrderLine, ProdOrderLine : Record "Prod. Order Line";
    begin

        Item.Reset();
        Item.SetRange("No.", ProdOrder."Source No.");
        Item.SetRange("License Plate Enabled?", true);
        If Item.FindFirst() then begin
            ItemLedgEntry.Reset();
            ItemLedgEntry.SetRange("Document No.", ProdOrder."No.");
            ItemLedgEntry.SetRange("Source No.", ProdOrder."Source No.");
            ItemLedgEntry.SetRange("Entry Type", ItemLedgEntry."Entry Type"::Output);
            ItemLedgEntry.CalcSums(ItemLedgEntry.Quantity);
            POQ := ItemLedgEntry.Quantity;
            If POQ <> 0 then begin
                ProdOrderComp.Reset();
                ProdOrderComp.SetCurrentKey("Prod. Order No.");
                ProdOrderComp.SetCurrentKey("Item No.");
                ProdOrderComp.SetRange("Prod. Order No.", ProdOrder."No.");
                if ProdOrderComp.FindFirst() then begin
                    //071223
                    ProdOrderCompCopy.copy(ProdOrderComp);
                    ProdOrderCompCopy.SetRange("Item No.", ProdOrderComp."Item No.");
                    ProdOrderCompCopy.CalcSums("Quantity per");
                    //071223
                    ItemLedgEntryCopy.Reset();
                    ItemLedgEntry.SetCurrentKey("Document No.", "Item No.");
                    ItemLedgEntryCopy.SetRange("Document No.", ProdOrderComp."Prod. Order No.");
                    ItemLedgEntryCopy.SetRange("Source No.", ProdOrder."Source No.");
                    ItemLedgEntryCopy.SetRange("Entry Type", ItemLedgEntry."Entry Type"::Consumption);
                    ItemLedgEntryCopy.SetRange("Item No.", ProdOrderComp."Item No.");
                    ItemLedgEntryCopy.CalcSums(Quantity);
                    // PCQ := ABS(Round((ItemLedgEntryCopy.Quantity / ProdOrderComp."Quantity per"), 1, '>'));
                    //071223
                    PCQ := ABS(Round((ItemLedgEntryCopy.Quantity / ProdOrderCompCopy."Quantity per"), 1, '>'));
                    //071223
                    //if PCQ <> 0 then begin
                    RelProdOrderLine.Reset();
                    RelProdOrderLine.SetRange("Prod. Order No.", ProdOrder."No.");
                    if RelProdOrderLine.FindFirst() then begin
                        if PCQ <> RelProdOrderLine."Posted Consumption Qty." then
                            RelProdOrderLine."Posted Consumption Qty." := PCQ;

                        // //071223
                        // If RelProdOrderLine."Posted Consumption Qty." > PCQ then
                        //     ProdOrderLine."Posted Consumption Qty." := PCQ;
                        // //071223
                        // //check if fraction qty is <> 0 and FQ = Qty
                        // if ((RelProdOrderLine.Quantity = RelProdOrderLine."Finished Quantity") and (RelProdOrderLine."Fraction Quantity" <> 0)) then begin
                        //     RelProdOrderLine."Fraction Quantity" := 0;
                        // end;
                        RelProdOrderLine.Modify();
                    end;
                    //end;
                end;

                // //update IWXLP Header
                // IWXLP.Reset();
                // IWXLP.SetCurrentKey("No.");
                // IWXLP.SetRange("Production Order No.", Rec."No.");
                // if IWXLP.FindFirst() then begin
                //     LPScannedQty := 0;
                //     repeat
                //         IWXLPLine.Reset();
                //         IWXLPLine.SetRange("License Plate No.", IWXLP."No.");
                //         if IWXLPLine.FindFirst() then begin
                //             If LPScannedQty < PCQ then begin
                //                 if IWXLP."LP Posted?" <> true then begin
                //                     IWXLP."LP Posted?" := true;
                //                     IWXLP.Modify();
                //                 end;
                //             end;
                //             //LPScannedQty += 1;
                //             LPScannedQty += IWXLPLine.Quantity;
                //         end
                //     until IWXLP.Next() = 0;
                // end;
                // CurrPage.Update(true);
                //CurrPage.Activate(true);
            end;
        end;
        // CurrPage.Update(true);
    end;

    /// <summary>
    /// ValidateLPItem.
    /// </summary>
    /// <returns>Return value of type Boolean.</returns>
    procedure ValidateLPItem()
    var
        Item: Record Item;
    begin
        //Rec.TestField("Source No.");
        if Rec."Source No." <> '' then begin
            Item.Reset();
            ITem.SetRange("No.", Rec."Source No.");
            Item.SetRange("License Plate Enabled?", true);
            if Item.FindFirst() then begin
                LPItemEnabled := true;
            end
            else
                LPItemEnabled := false;
        end;
        // CurrPage.Update(true);

        //check user setup of user
        UserSetup.Reset();
        UserSetup.SetRange("User ID", USERID);
        if UserSetup.FindFirst() then begin
            if UserSetup."Can Modify Prod. Order" then
                GlobalProdEdit := true
            else
                GlobalProdEdit := false;
        end;
    end;

    //<<<<<< G2S CAS-01312-L5Q5B8 6/21/2024
    trigger OnDeleteRecord(): Boolean
    var
        UserSetup: Record "User Setup";
        ProdOrderLine: Record "Prod. Order Line";
    begin
        UserSetup.SetRange("User ID", UserId);
        if UserSetup.FindFirst() then begin
            if not UserSetup."Delete Production Order" then begin
                Error('You do not have permmission to delete this Order!');
            end else begin
                ProdOrderLine.SetRange("Prod. Order No.", "No.");
                ProdOrderLine.SetRange("Item No.", "Source No.");
                if ProdOrderLine.FindFirst() then begin
                    if ProdOrderLine."Finished Quantity" <> 0 then Error('Your can not delete a Production order with Entires!');
                end;
            end;
        end else
            Error('User Not Found');
    end;

    // >>>>G2S>>>10112_CAS-01430-R9F1K9>>>>170725
    procedure CheckProdOrderAgainstSpecification()
    var
        TrkgSpec: Record "Tracking Specification";
    BEGIN
        IF (xRec."Production Batch No." <> Rec."Production Batch No.") OR (xRec."Source No." <> Rec."Source No.") OR (xRec.Quantity <> Rec.Quantity) then BEGIN
            TrkgSpec.Reset();
            TrkgSpec.SetRange("Source ID", Rec."No.");
            IF TrkgSpec.FindFirst() THEN BEGIN
                IF (Rec."Production Batch No." <> TrkgSpec."Lot No.") OR
                (Rec."Source No." <> TrkgSpec."Item No.") OR (Rec.Quantity <> TrkgSpec."Quantity (Base)") then
                    Message('Kindly refresh production order to update the barcode label for scanning');
            END;
        END;
    END;
    //>>>>>> G2S CAS-01312-L5Q5B8 6/21/2024

    // procedure setEditable()
    // begin
    //     ItemLedgerExist();
    //     if KeyLotFieldsEdit then
    //         isEditble := false else begin
    //         if Rec."Barcode Printed?" then begin
    //             isEditble := false;
    //         end else begin
    //             isEditble := true;
    //         end;
    //     end;
    // end;

    // procedure ItemLedgerExist()
    // var
    //     ProdOrderLine: Record "Prod. Order Line";
    //     ItemLedgEntry: Record "Item Ledger Entry";
    // begin
    //     KeyLotFieldsEdit := false;
    //     ProdOrderLine.Reset();
    //     ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
    //     IF ProdOrderLine.FindSet() THEN
    //         Repeat
    //             ItemLedgEntry.Reset();
    //             ItemLedgEntry.SetRange("Document No.", ProdOrderLine."Prod. Order No.");
    //             IF NOT ItemLedgEntry.IsEmpty then
    //                 KeyLotFieldsEdit := true;
    //         Until ProdOrderLine.Next() = 0;
    // end;


    var
        Txt001: Label 'Barcode cannot be generatd for this item';
        CustomCode: Codeunit "Custom-G2S";
        ZeroQtyInSubItemMain: Boolean;
        GlobalProdEdit: Boolean;
        UserSetup: Record "User Setup";
        BarcodeEnabled: Boolean;
        TxtDispRefConfirm: Label 'Barcode labels have already been printed for order %1. Refreshing this order will discard previously generated LP records and create new LP records for this order %1. Do you want to continue?';
        ConfirmRefOrder: Boolean;
        LPItemEnabled: Boolean;
        OutputExist: Boolean;

        //G2S Aug 16 2023

        //270324
        CustomSetup: Record "Custom Setup";
    //270324

}