page 50237 TransportSettlementArchieve
{
    UsageCategory = lists;

    ApplicationArea = All;
    Caption = 'TransportSettlementArchieve';
    PageType = List;
    SourceTable = "Transport Settlement Archieve";
    Editable = false;
    //CardPageId = TransportContractTypesCard;
    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = All;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = All;
                }
                field("To-Location"; "To-Location")
                {
                    ApplicationArea = All;
                }
                field("Add Rate Per KM Above 6000KM"; "Add Rate Per KM Above 6000KM")
                {
                    ApplicationArea = All;
                }
                field("Baseline KM"; "Baseline KM")
                {
                    ApplicationArea = All;
                }
                field("Contract Type"; "Contract Type")
                {
                    ApplicationArea = All;
                }
                field("Date in Service"; "Date in Service")
                {
                    ApplicationArea = All;
                }
                field("Diesel Quantity Issued"; "Diesel Quantity Issued")
                {
                    ApplicationArea = All;
                }
                field("Ending Date"; "Ending Date")
                {
                    ApplicationArea = All;
                }
                field("FIXED COST"; "FIXED COST")
                {
                    ApplicationArea = All;
                }
                field("IN-TRANSIT TIME TO & FRO"; "IN-TRANSIT TIME TO & FRO")
                {
                    ApplicationArea = All;
                }
                field("LOADING & OFFLOAd TIME COMBINE"; "LOADING & OFFLOAd TIME COMBINE")
                {
                    ApplicationArea = All;
                }
                field("New Distance"; "New Distance")
                {
                    ApplicationArea = All;
                }
                field("Others Per KM"; "Others Per KM")
                {
                    ApplicationArea = All;
                }
                field("R&M Per KM"; "R&M Per KM")
                {
                    ApplicationArea = All;
                }
                field("R&M"; "R&M")
                {
                    ApplicationArea = All;
                }
                field("Rate Per KM 6000KM Base line"; "Rate Per KM 6000KM Base line")
                {
                    ApplicationArea = All;
                }
                field("Reimbursement for FueL Cost"; "Reimbursement for FueL Cost")
                {
                    ApplicationArea = All;
                }
                field("Starting Date"; "Starting Date")
                {
                    ApplicationArea = All;
                }
                field("TOTAL COST FIXED & VARIABLE"; "TOTAL COST FIXED & VARIABLE")
                {
                    ApplicationArea = All;
                }
                field("TOTAL TIME TAT"; "TOTAL TIME TAT")
                {
                    ApplicationArea = All;
                }
                field("TOTAL VARIABLE"; "TOTAL VARIABLE")
                {
                    ApplicationArea = All;
                }
                field("TYRE PER KM"; "TYRE PER KM")
                {
                    ApplicationArea = All;
                }
                field("Validate Period"; "Validate Period")
                {
                    ApplicationArea = All;
                }
                field("Vehicle Registration Number"; "Vehicle Registration Number")
                {
                    ApplicationArea = All;
                }
                field("Vehicle Type"; "Vehicle Type")
                {
                    ApplicationArea = All;
                }
                field(Diesel; Diesel)
                {
                    ApplicationArea = All;
                }
                field(LOT; LOT)
                {
                    ApplicationArea = All;
                }
                field(OTHERS; OTHERS)
                {
                    ApplicationArea = All;
                }
                field(Rate; Rate)
                {
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    ApplicationArea = All;
                }

                field(Total; Total)
                {
                    ApplicationArea = All;
                }
                field(TYRES; TYRES)
                {
                    ApplicationArea = All;
                }
                field("Full Diesel Price"; "Full Diesel Price")
                {
                    ApplicationArea = all;
                }
                field("Half Diesel Price"; "Half Diesel Price")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = ALL;
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = ALL;
                }
                field("Document Line No."; "Document Line No.")
                {
                    ApplicationArea = ALL;
                }
            }
        }
    }

}
