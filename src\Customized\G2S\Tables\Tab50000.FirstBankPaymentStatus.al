//G2S Providus Integration
table 50386 "Bank Payment Status"
{
    Caption = 'Bank Payment Status';
    DataClassification = ToBeClassified;
    Permissions = tabledata "Bank Payment Status" = RM;


    fields
    {
        field(1; "Entry No"; Integer)
        {
            Caption = 'Entry No';
            AutoIncrement = true;
        }
        field(2; "Payment Status"; Integer)
        {
            Caption = 'Payment Status';
        }
        field(3; "Status Description"; Code[30])
        {
            Caption = 'Status Description';
        }
        field(4; Remark; Text[50])
        {
            Caption = 'Remark';
        }
        field(5; "Bank Code"; Code[20])
        {
            Caption = 'Bank Account Code';
            TableRelation = "Bank Account"."No.";
        }
        field(6; "Successfull"; Boolean)
        {
            Caption = 'Successfull';
        }
        field(7; "Failed"; Boolean)
        {
            Caption = 'Failed';
        }
        field(8; "Payment StatusT"; Code[20])
        {
            Caption = 'Payment StatusT';
        }

    }
    keys
    {
        key(PK; "Entry No", "Bank Code")
        {
            Clustered = true;
        }
    }
}
