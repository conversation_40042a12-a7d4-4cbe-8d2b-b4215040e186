page 50975 "Retail Product Log"
{
    ApplicationArea = All;
    Caption = 'Retail Product Log';
    PageType = List;
    SourceTable = "Retail Products Log";
    UsageCategory = Lists;


    layout
    {
        area(content)
        {
            repeater(Group)
            {
                field("Code"; "Code")
                {
                    Caption = 'Bar Code';
                    ApplicationArea = All;
                }
                field("Item No."; "Item No.")
                {
                    Caption = 'Chi Item No.';
                    ApplicationArea = All;
                }

                field("Description"; "Description")
                {
                    ApplicationArea = All;
                }

                field("Generic Name"; "Generic Name")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Manufacturer ID"; "Manufacturer ID")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Category ID"; "Category ID")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Cost"; "Cost")
                {
                    ApplicationArea = All;
                }

                field("Unit"; "Unit")
                {
                    ApplicationArea = All;
                }

                field("Recurring Qty"; "Recurring Qty")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Markup"; "Markup")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Price"; "Price")
                {
                    ApplicationArea = All;
                }

                field("Taxable"; "Taxable")
                {
                    ApplicationArea = All;
                }

                field("Watched"; "Watched")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Online"; "Online")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Raw Material"; "Raw Material")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("Produce"; "Produce")
                {
                    ApplicationArea = All;
                    Visible = false;
                }
                field("Allow Fraction"; "Allow Fraction")
                {
                    ApplicationArea = All;
                }

                // Fields for Price Levels with "PL_" prefix
                field("PL Description"; "PL Description")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("PL Quantity"; "PL Quantity")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("PL Unit"; "PL Unit")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("PL Price"; "PL Price")
                {
                    ApplicationArea = All;
                    Visible = false;
                }
                field("Product ID"; "Product ID")
                {
                    ApplicationArea = All;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                }

                field("WS Description1"; "WS Description1")
                {
                    ApplicationArea = All;
                }

                field("WS Quantity1"; "WS Quantity1")
                {
                    ApplicationArea = All;
                }

                field("WS Code1"; "WS Code1")
                {
                    ApplicationArea = All;
                }

                field("WS Price1"; "WS Price1")
                {
                    ApplicationArea = All;
                }
                // Fields for Wholesales with "WS_" prefix
                field("WS Description"; "WS Description")
                {
                    ApplicationArea = All;
                }


                field("WS Quantity"; "WS Quantity")
                {
                    ApplicationArea = All;
                }

                field("WS Code"; "WS Code")
                {
                    ApplicationArea = All;
                }

                field("WS Price"; "WS Price")
                {
                    ApplicationArea = All;
                }

                // Fields for Price Profiles with "PP_" prefix
                field("PP Profile ID"; "PP Profile ID")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                field("PP Price"; "PP Price")
                {
                    ApplicationArea = All;
                    Visible = false;
                }

                // Status field
                field("Status"; "Status")
                {
                    ApplicationArea = All;
                }
                field(ResponseMessage; ResponseMessage)
                {
                    ApplicationArea = All;
                }
                field(ResponseSuccess; ResponseSuccess)
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {

        area(Navigation)
        {
            group(GroupName)
            {


                // action(TestAPI)
                // {
                //     trigger OnAction()
                //     var
                //         fetchsales: Codeunit RetailAPI2;
                //     begin
                //       //  fetchsales.Run();
                //     end;
                // }
            }
        }
    }

    trigger OnOpenPage();
    begin
        // Add logic here if needed when the page opens
    end;
}


