pageextension 50071 PostdTransferShipmnt extends "Posted Transfer Shipment"
{
    layout
    {
        addafter("Transfer-to Code")
        {
            field("Transfer Type"; "Transfer Type")
            {
                ApplicationArea = ALL;
            }
        }
    }
    actions
    {
        addafter("&Print")
        {
            action("Attached Gate Entry")
            {
                Caption = 'Attached Gate Entry';
                ApplicationArea = all;
                Image = InwardEntry;
                RunObject = page "Outward Gate Entry Line List";
                RunPageLink = "Entry Type" = const(Outward), "Source Type" = const("Transfer Shipment"), "Source No." = field("No.");
            }
        }
    }
}
