page 50161 "Teller/Cheq. Awaiting Confirm."
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO       18-Jun-13    -> Form created for Bank Teller Confirmation functionality.

    Caption = 'Bank Confirmation Register';
    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Request Teller Receipt";
    SourceTableView = WHERE("Released for Confirmation" = FILTER(true),
                            "Teller Returned" = CONST(false));
    UsageCategory = lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnLookup(var Text: Text): Boolean;
                    begin
                        /*Prasanna
                                                RespCentRec.RESET;
                                                UserRespCentRec.SETRANGE("User ID", USERID);
                                                if UserRespCentRec.FINDSET then begin
                                                    if UserRespCentRec."Resp. Center Code" <> '' then begin
                                                        repeat
                                                            if RespCentRec.GET(UserRespCentRec."Resp. Center Code") then
                                                                RespCentRec.MARK(true);
                                                        until UserRespCentRec.NEXT = 0;
                                                        RespCentRec.MARKEDONLY(true);
                                                        if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                                                            "Responsibility Center" := RespCentRec.Code;
                                                    end else begin
                                                        RespCentRec.RESET;
                                                        RespCentRec.SETCURRENTKEY(Code);
                                                        if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                                                            "Responsibility Center" := RespCentRec.Code;
                                                    end;
                                                end else begin
                                                    RespCentRec.RESET;
                                                    RespCentRec.SETCURRENTKEY(Code);
                                                    if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                                                        "Responsibility Center" := RespCentRec.Code;
                                                end;*/
                        RespCentRec.RESET;
                        RespCentRec.SETCURRENTKEY(Code);
                        if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                            "Responsibility Center" := RespCentRec.Code;

                        //IF PAGE.RUNMODAL(PAGE::"Responsibility Center List")  = ACTION::LookupOK THEN
                    end;

                    trigger OnValidate();
                    begin
                        if "Responsibility Center" = '' then begin
                            CLEAR("Customer No.");
                            CLEAR("Customer Name");
                        end;
                    end;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = true;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Paid By"; "Paid By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                }
                field("G/L Account Name"; "G/L Account Name")
                {
                    ApplicationArea = all;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank No."; "Bank No.")
                {
                    ApplicationArea = all;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    //Editable = False;
                    trigger onvalidate()
                    var
                        TellNum: integer;
                        TellNumErr: label 'Please Enter Integer Value in Teller No.';
                    BEGIN
                        /*IF NOT Evaluate(TellNum, "Teller No.") then
                            Error(TellNumErr);*/
                        //"Reconciliation teller no." := "Teller No.";
                        "Teller no. Modified By" := UserId;
                        "Teller no. Modified Date" := WorkDate();
                    END;

                }
                field("Reconciliation teller no."; "Reconciliation teller no.")
                {
                    ApplicationArea = all;//b2bpksalecorr12
                    Editable = false;

                }
                field("Teller no. Modified By"; "Teller no. Modified By")
                {
                    ApplicationArea = all;//b2bpksalecorr12
                }
                field("Teller no. Modified Date"; "Teller no. Modified Date")
                {
                    ApplicationArea = all;//b2bpksalecorr12
                }
                field("Chq. Value Date"; "Chq. Value Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = true;
                }
                field("Cheque No."; "Cheque No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Cheque Date"; "Cheque Date")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                }
                field("Teller Amount(LCY)"; "Teller Amount(LCY)")
                {
                    ApplicationArea = all;
                }
                field("Teller Is Confirmed"; "Teller Is Confirmed")
                {
                    ApplicationArea = all;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                }
                field("Confirmed By"; "Confirmed By")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Confirmation Date"; "Confirmation Date")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Confirmation Time"; "Confirmation Time")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Released By"; "Released By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released Date"; "Released Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released time"; "Released time")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Last Modified By"; "Last Modified By")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                }
                field("Last Modified Date"; "Last Modified Date")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Re&gister")
            {
                Caption = 'Re&gister';
                action(Reopen)
                {
                    Caption = 'Reopen';
                    ApplicationArea = all;
                    trigger OnAction();
                    begin
                        ReopenRelBankConf;
                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action(Post)
                {
                    Caption = 'Post';
                    ApplicationArea = all;
                    Ellipsis = true;
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    var
                        PurchaseHeader: Record "Purchase Header";
                        ApprovalMgt: Codeunit 1535;
                    begin
                        CheckMandValues;

                        //HO1.0<<
                        if UserSetup.GET(USERID) then
                            if not UserSetup."Post Bank Confirmation Reg." then
                                ERROR(Text50000);
                        //HO1.0>>

                        BankTellerConfirmationRec.RESET;
                        BankTellerConfirmationRec.SETFILTER("Teller Is Confirmed", '%1', true);
                        BankTellerConfirmationRec.SETRANGE("Confirmed By", UPPERCASE(USERID));

                        if not BankTellerConfirmationRec.FINDFIRST then
                            ERROR(Text50200, FIELDCAPTION("Teller Is Confirmed"));

                        BankTellerConfirmationRec.RESET;
                        BankTellerConfirmationRec.SETFILTER("Teller Is Confirmed", '%1', true);
                        BankTellerConfirmationRec.SETRANGE("Confirmed By", UPPERCASE(USERID));
                        if BankTellerConfirmationRec.FINDSET then
                            Window.OPEN('#1############################\\' + Text50201);

                        repeat
                            Linecount := Linecount + 1;
                            Window.UPDATE(2, Linecount);

                            BankConfirmedTellersRec.INIT;
                            if not OldBankConfirmedTellersRec.FINDLAST then begin
                                no := 1;
                            end else
                                if OldBankConfirmedTellersRec.FINDLAST then
                                    no := OldBankConfirmedTellersRec."No." + 1;

                            BankConfirmedTellersRec.TRANSFERFIELDS(BankTellerConfirmationRec);


                            BankConfirmedTellersRec."Teller No." := FORMAT(BankTellerConfirmationRec."Bank Code") +
                                                                  FORMAT(BankTellerConfirmationRec."Teller No.");
                            BankConfirmedTellersRec."Unposted Teller No." := BankTellerConfirmationRec."Unposted Teller No.";
                            BankConfirmedTellersRec."No." := no;
                            BankConfirmedTellersRec.INSERT;

                            BankConfirmedTellersRec."Posted Date" := CURRENTDATETIME;
                            BankConfirmedTellersRec."Posted By" := USERID;
                            BankConfirmedTellersRec."Return Confirmed Bank Teller" := false;

                            BankConfirmedTellersRec.MODIFY;

                            // Delete Confirmation Line after inserting to Confirmed Tellers Table
                            BankTellerConfirmationRec.DELETE;

                        until BankTellerConfirmationRec.NEXT = 0;

                        Window.CLOSE;
                    end;
                }
            }
        }
    }

    trigger OnOpenPage();
    begin
        if UserSetup.GET(USERID) then
            if not UserSetup."Teller/Cheque Awaiting Confirm" then
                ERROR(Text50205);

        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);


        /*
        RespCentCount :=0;
        
        UserIDRespCent.SETCURRENTKEY("User ID","Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
         REPEAT
          RespCentCount +=1;
          //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
          IF RespCentCount = 1 THEN
           BuildFilter :=UserIDRespCent."Resp. Center Code"
          ELSE
           BuildFilter+='|' + UserIDRespCent."Resp. Center Code";
         UNTIL UserIDRespCent.NEXT=0;
        
        IF RespCentCount > 0 THEN
         //SETRANGE("Responsibility Center",TempResp[1],TempResp[RespCentCount]);
         SETFILTER("Responsibility Center",BuildFilter);
        */

    end;

    var
        Employee: Record Employee;
        BankTellerConfirmationRec: Record "Request Teller Receipt";
        BankConfirmedTellersRec: Record "Confirmed Teller Receipt";
        Window: Dialog;
        Linecount: Integer;
        OldBankConfirmedTellersRec: Record "Confirmed Teller Receipt";
        Text50000: Label 'You do not have permission to post the Bank confirmation Register.';
        Text50200: Label 'You cannnot post while %1 is false';
        Text50201: Label 'Posting lines         #2######';
        Text50202: Label 'You do not have permission to post to Bank Confirmed Tellers';
        no: Integer;
        UserSetup: Record "User Setup";
        //UserRespCentRec: Record "UserID Resp. Cent. Lines";
        RespCentRec: Record "Responsibility Center";
        DimValRec: Record "Dimension Value";
        GenLedgSetup: Record "General Ledger Setup";
        Text50205: Label 'You do not have permission for Teller/Cheque Awaiting Confirmation.';
        UserMgt: Codeunit "User Setup Management";
        RespCentCount: Integer;
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

