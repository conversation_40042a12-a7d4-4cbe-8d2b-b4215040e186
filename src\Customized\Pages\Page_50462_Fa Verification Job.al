page 50462 "Fa Verification Job"
{
    // version TRKIT

    Editable = false;
    PageType = Document;
    SourceTable = XX_TRKIT_FA_VERIFICATION_JOB;
    UsageCategory = Administration;
    ApplicationArea = all; 
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field(VERIFICATION_TASK_NUMBER; VERIFICATION_TASK_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(VERIFICATION_TASK_DESC; VERIFICATION_TASK_DESC)
                {
                    ApplicationArea = all;
                }
                field(START_DATE; START_DATE)
                {
                    ApplicationArea = all;
                }
                field(END_DATE; END_DATE)
                {
                    ApplicationArea = all;
                }
                field(STATUS; STATUS)
                {
                    ApplicationArea = all;
                }
                field(NAVISION_SYNC_TIME; NAVISION_SYNC_TIME)
                {
                    ApplicationArea = all;
                }
            }
            part(FAverificationdata; "FA Verification Data")
            {
                ApplicationArea = all;
                SubPageLink = VERIFICATION_TASK_NUMBER = FIELD(VERIFICATION_TASK_NUMBER);
            }
        }
    }

    actions
    {
    }
}

