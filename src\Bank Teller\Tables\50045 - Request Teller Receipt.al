table 50045 "Request Teller Receipt"
{

    LookupPageID = "Request Teller Receipt List";

    fields
    {
        field(1; "Branch Code"; Code[20])
        {
            //ValuesAllowed = 
            trigger OnValidate();
            begin
                /*
                IF "Teller No." <> '' THEN
                  ERROR(Text50200);
                */


            end;
        }
        field(2; "Bank Deposited"; Option)
        {
            //OptionMembers = " ",ZIB,FBN,UBA,CITI,GTB,SKYE,FBNCITI,SCB,STR,WEMA,DMB,REBATE;
            OptionMembers = " ",ZIB,FBN,UBA,CITI,GTB,SKYE,FBNCITI,SCB,STR,WEMA,DMB,REBATE,CORONATION;//PKONDE16
            trigger OnValidate();
            begin
                if "Teller No." <> '' then
                    ERROR(Text50200);
            end;
        }
        field(3; "Teller No."; Code[20])
        {

            trigger OnValidate();
            //BaluOn17Jan2022>>
            var
                TellerRcptGRec: Record "Request Teller Receipt";
                ConTellerGRec: Record "Confirmed Teller Receipt";
            //BaluOn17Jan2022<<
            begin
                /*TellerNo := COPYSTR(FORMAT("Teller No."), 1, 1);
                if TellerNo = FORMAT(0) then
                    ERROR(Text50212, FIELDCAPTION("Teller No."));*/


                "Bank Code" := "Bank Code"::" ";

                TESTFIELD("Global Dimension 1 Code");
                TESTFIELD("Responsibility Center");
                TESTFIELD("Teller Date");

                "Unposted Teller No." := "Teller No.";
                "Reconciliation teller no." := "Teller No.";//b2bpksalecorr12


                CLEAR(BankTeller);
                BankTeller := FORMAT("Bank Code") + "Teller No.";
                //"Teller No.":=BankTeller;

                //Check for duplicate Teller No in Confirmed Teller Register
                IF "Teller Type" = "Teller Type"::"Teller Cash" THEN BEGIN
                    //BankTeller:= FORMAT("Bank Code")+"Teller No.";
                    BankConfirmedRec.RESET;
                    BankConfirmedRec.SETRANGE("Bank Code", "Bank Code");
                    BankConfirmedRec.SETRANGE(BankConfirmedRec."Teller No.", BankTeller);
                    //BankConfirmedRec.SETRANGE("Branch Name","Global Dimension 1 Code");
                    IF BankConfirmedRec.FINDSET THEN
                        REPEAT
                            IF BankConfirmedRec."Teller No." = BankTeller THEN BEGIN
                                //IF "Teller Date" = BankConfirmedRec."Teller Date" THEN
                                ERROR(Text50201, "Teller No.", "Teller Date", BankConfirmedRec."Bank Code",
                                BankConfirmedRec."Global Dimension 1 Code");
                            END;
                        UNTIL BankConfirmedRec.NEXT = 0;


                    //To test for duplication confirmation No. in unposted confirmations
                    BankConfirmationRec.RESET;
                    BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                    //BankConfirmationRec.SETRANGE("Global Dimension 1 Code","Global Dimension 1 Code");
                    BankConfirmationRec.SETRANGE("Teller No.", "Teller No.");
                    IF BankConfirmationRec.FINDFIRST THEN
                        ERROR(
                             Text50202, BankConfirmationRec."Teller No.", BankConfirmationRec."Teller Date", BankConfirmationRec."Bank Code",
                             BankConfirmationRec."Global Dimension 1 Code");

                END;
                //BaluOn17Jan2022>>
                TellerRcptGRec.Reset();
                TellerRcptGRec.SetRange("Teller No.", "Teller No.");
                if TellerRcptGRec.FindFirst() then begin
                    Error('Teller No. already exists in Teller Request Document No. %1', TellerRcptGRec."No.")
                end else begin
                    ConTellerGRec.Reset();
                    ConTellerGRec.SetRange("Teller No.", "Teller No.");
                    if ConTellerGRec.FindFirst() then
                        Error('Teller No. already exists in Confirmed Teller No. %1', ConTellerGRec."No.");
                end;
                //BaluOn17Jan2022<<

            end;
        }
        field(4; "Teller Date"; Date)
        {

            trigger OnValidate();
            begin
                if "Teller Date" > TODAY then
                    ERROR('Teller Date can not be a future Date');

                if "Teller No." <> '' then begin
                    CLEAR(BankTeller);
                    BankConfirmedRec.RESET;
                    BankConfirmationRec.RESET;
                    if "Teller Type" = "Teller Type"::"Teller Cash" then begin
                        BankTeller := FORMAT("Bank Code") + "Teller No.";
                        BankConfirmedRec.SETRANGE(BankConfirmedRec."Bank Code", "Bank Code");
                        BankConfirmedRec.SETRANGE(BankConfirmedRec."Teller No.", BankTeller);
                        //BankConfirmedRec.SETRANGE("Branch names","Branch Names");
                        if BankConfirmedRec.FINDSET then
                            repeat
                                if BankConfirmedRec."Teller No." = BankTeller then begin
                                    //IF "Teller Date" = BankConfirmedRec."Teller Date" THEN
                                    ERROR(Text50201, BankConfirmedRec."Teller No.", "Teller Date", BankConfirmedRec."Bank Code",
                                    BankConfirmedRec."Branch names");
                                end;
                            until BankConfirmedRec.NEXT = 0;

                        // Test for duplication confirmation nos in unposted confirmations
                        BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                        //BankConfirmationRec.SETRANGE("Branch Names","Branch Names");
                        BankConfirmationRec.SETRANGE("Teller No.", "Teller No.");
                        if BankConfirmationRec.FINDFIRST then
                            //REPEAT
                            //IF "Teller Date" = BankConfirmationRec."Teller Date" THEN
                            ERROR(Text50202, BankConfirmationRec."Teller No.", "Teller Date",
                            BankConfirmationRec."Bank Code", BankConfirmationRec."Branch Names");
                        //UNTIL BankConfirmationRec.NEXT = 0;
                        //End of  Test for duplication confirmation nos in unposted Bank Confirmations
                    end;
                end;
            end;
        }
        field(5; "Teller Amount"; Decimal)
        {

            trigger OnValidate();
            begin
                if "Teller Amount" <> 0 then begin
                    //TESTFIELD("Bank Code");
                    TESTFIELD("Teller No.");
                    TESTFIELD("Teller Date");
                    //CRF: 2019 - 0090 SAA  >>
                    if "Currency Code" = '' then
                        "Teller Amount(LCY)" := "Teller Amount"
                    else
                        "Teller Amount(LCY)" := ROUND(
                          CurrExchRate.ExchangeAmtFCYToLCY(
                            TODAY, "Currency Code",
                            "Teller Amount", "Currency Factor"));

                    //CRF: 2019 - 0090 SAA <<

                end;
            end;
        }
        field(6; "Cheque No."; Code[20])
        {

            trigger OnValidate();
            begin
                /*
                IF "Cheque No." <> '' THEN  BEGIN
                  TESTFIELD("Cheque Date");
                  TESTFIELD("Bank Code");
                END;
                */

            end;
        }
        field(7; "Cheque Date"; Date)
        {
        }
        field(8; "Bank No."; Code[10])
        {
            Caption = 'Bank No.';
            TableRelation = "Bank Account" WHERE(Blocked = FILTER(false),
                                                  "Account Type" = FILTER(Collection | Bank),
                                                  "Bank Acc. Posting Group" = FILTER('BANKOD|CURRENT|DOM'));

            //"Teller Bank Name" = FIELD("Bank Code"));

            trigger OnValidate();
            begin
                if BankAcctRec.GET("Bank No.") then begin
                    //BankAcctRec.TESTFIELD("Teller Bank Name");
                    "Bank Name" := BankAcctRec.Name;
                    //"Bank Code" :=BankAcctRec."Teller Bank Name";
                end;

                if "Bank No." = '' then begin
                    CLEAR("Bank Name");
                    CLEAR("Bank Code");
                end;


                CLEAR(BankTeller);
                BankTeller := FORMAT("Bank Code") + "Teller No.";

                if "Bank No." <> '' then begin
                    //Check for duplicate Teller No in Confirmed Teller Register
                    if "Teller Type" = "Teller Type"::"Teller Cash" then begin
                        //BankTeller:= FORMAT("Bank Code")+"Teller No.";
                        BankConfirmedRec.RESET;
                        BankConfirmedRec.SETRANGE("Bank Code", "Bank Code");
                        BankConfirmedRec.SETRANGE(BankConfirmedRec."Teller No.", BankTeller);
                        BankConfirmedRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmedRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                        if BankConfirmedRec.FINDSET then
                            repeat
                                if BankConfirmedRec."Teller No." = BankTeller then begin
                                    //IF "Teller Date" = BankConfirmedRec."Teller Date" THEN
                                    ERROR(Text50201, "Teller No.", BankConfirmedRec."Teller Date", BankConfirmedRec."Bank Code",
                                    BankConfirmedRec."Global Dimension 1 Code");
                                end;
                            until BankConfirmedRec.NEXT = 0;


                        //To test for duplication Teller No. in unposted confirmations
                        BankConfirmationRec.RESET;
                        BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                        BankConfirmationRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmationRec.SETRANGE("Teller No.", "Teller No.");
                        BankConfirmationRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                        if BankConfirmationRec.FINDSET then
                            repeat
                                if BankConfirmationRec."No." <> "No." then
                                    ERROR(
                                        Text50202, "Teller No.", BankConfirmationRec."Teller Date", BankConfirmationRec."Bank Code",
                                        BankConfirmationRec."Global Dimension 1 Code");
                            until BankConfirmationRec.NEXT = 0;
                    end;
                end;
            end;
        }
        field(9; "Customer Name"; Code[100])
        {
            Editable = false;
        }
        field(10; "Bank Code"; Option)
        {
            //OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,RMB,Polaris';//PKONDE16
            //OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,RMB,Polaris;//PKONDE16
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,RMB,Polaris,CORONATION,PROVIDUS';//PKONDE16
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,RMB,Polaris,CORONATION,PROVIDUS;//PKONDE16

            trigger OnValidate();
            begin
                TESTFIELD("Teller No.");
                TESTFIELD("Teller Type");//NYO...22/02/2016

                CLEAR(BankTeller);
                BankTeller := FORMAT("Bank Code") + "Teller No.";
                //"Teller No.":=BankTeller;

                //Check for duplicate Teller No in Confirmed Teller Register
                if "Teller Type" <> "Teller Type"::" " then begin   //NYO...22/02/2016
                                                                    //BankTeller:= FORMAT("Bank Code")+"Teller No.";
                    BankConfirmedRec.RESET;
                    BankConfirmedRec.SETRANGE("Bank Code", "Bank Code");
                    BankConfirmedRec.SETRANGE(BankConfirmedRec."Teller No.", BankTeller);
                    BankConfirmedRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                    BankConfirmedRec.SETRANGE("Teller Date", CALCDATE('-60D', TODAY), TODAY);//NYO 16/06/2016
                    if BankConfirmedRec.FINDSET then
                        repeat
                            if BankConfirmedRec."Teller No." = BankTeller then begin
                                //IF "Teller Date" = BankConfirmedRec."Teller Date" THEN
                                ERROR(Text50201, BankConfirmedRec."Teller No.", BankConfirmedRec."Teller Date", BankConfirmedRec."Bank Code",
                                BankConfirmedRec."Global Dimension 1 Code", BankConfirmedRec."No.");
                            end;
                        until BankConfirmedRec.NEXT = 0;

                    //To test for duplication Teller No. in unposted confirmations
                    IF "Teller No." <> 'TRANSFER' THEN BEGIN
                        BankConfirmationRec.RESET;
                        BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                        BankConfirmationRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmationRec.SETRANGE("Teller No.", "Unposted Teller No.");
                        BankConfirmationRec.SETRANGE("Teller Date", CALCDATE('-60D', TODAY), TODAY);//NYO 16/06/2016;
                        if BankConfirmationRec.FINDSET then
                            repeat
                                if BankConfirmationRec."No." <> "No." then
                                    ERROR(
                                        Text50202, "Teller No.", BankConfirmationRec."Teller Date", BankConfirmationRec."Bank Code",
                                        BankConfirmationRec."Global Dimension 1 Code", BankConfirmationRec."No.");
                            until BankConfirmationRec.NEXT = 0;
                    end;
                end;
            end;
        }
        field(11; "Confirmation No."; Code[10])
        {
            Editable = false;

            trigger OnValidate();
            begin
                /*IF UserPermissionLevelRec.GET(USERID) THEN
                  IF NOT UserPermissionLevelRec."Edit Confirmation No" THEN
                    ERROR(Text50205);
                */
                if "Teller Is Confirmed" then
                    ERROR(Text50204);

                VALIDATE("Confirmation Date", TODAY);
                VALIDATE("Confirmation Time", TIME);
                VALIDATE("Confirmed By", USERID);

            end;
        }
        field(12; "Confirmation Date"; Date)
        {
            Editable = false;
        }
        field(13; "Confirmation Time"; Time)
        {
            Editable = false;
        }
        field(14; "Confirmed By"; Code[50])
        {
            Editable = false;
        }
        field(15; "Teller Is Confirmed"; Boolean)
        {

            trigger OnValidate();
            begin
                if UserSetup.GET(USERID) then
                    if not UserSetup."Teller/Cheque Awaiting Confirm" then
                        ERROR(Text50211);


                /*
                IF "Confirmation No." = '' THEN
                  ERROR(Text50203);
                
                IF "Teller Is Confirmed" THEN
                  "Teller Is Confirmed":=TRUE
                ELSE
                  "Teller Is Confirmed":=FALSE;
                
                MODIFY;
                 */

                if "Confirmation No." = '' then begin

                    //NYO 26-09-2019 for G/L and Customer Request >>
                    if ("Teller Is Confirmed" and ("Credit Account Type" <> "Credit Account Type"::"G/L Account") and
                    ("Credit Account Type" <> "Credit Account Type"::Vendor)) then begin  //NYO 12/12/19 0100, Vendor added to the options

                        //TESTFIELD(Company);
                        TESTFIELD("Global Dimension 1 Code");
                        TESTFIELD("Global Dimension 2 Code");
                        TESTFIELD("Responsibility Center");
                        TESTFIELD("Customer No.");
                        TESTFIELD("Teller Type");
                        TESTFIELD("Teller No.");
                        TESTFIELD("Teller Date");
                        TESTFIELD("Teller No.");
                        if "Teller Type" = "Teller Type"::"Teller Cheque" then
                            TESTFIELD("Chq. Value Date");

                        TESTFIELD("Bank No.");
                        TESTFIELD("Bank Code");
                        TESTFIELD("Bank Location");
                        TESTFIELD("Teller Amount");

                        CLEAR(BankTeller);
                        BankTeller := FORMAT("Bank Code") + "Teller No.";
                        //"Teller No.":=BankTeller;

                        //Check for duplicate Teller No in Confirmed Teller Register
                        if "Teller Type" = "Teller Type"::"Teller Cash" then begin
                            //BankTeller:= FORMAT("Bank Code")+"Teller No.";
                            BankConfirmedRec.RESET;
                            BankConfirmedRec.SETRANGE("Bank Code", "Bank Code");
                            BankConfirmedRec.SETRANGE(BankConfirmedRec."Teller No.", BankTeller);
                            BankConfirmedRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                            BankConfirmedRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                            if BankConfirmedRec.FINDSET then
                                repeat
                                    if BankConfirmedRec."Teller No." = BankTeller then begin
                                        //IF "Teller Date" = BankConfirmedRec."Teller Date" THEN
                                        ERROR(Text50201, BankConfirmedRec."Teller No.", BankConfirmedRec."Teller Date", BankConfirmedRec."Bank Code",
                                        BankConfirmedRec."Global Dimension 1 Code", BankConfirmedRec."No.");
                                    end;
                                until BankConfirmedRec.NEXT = 0;

                            //To test for duplication Teller No. in unposted confirmations
                            BankConfirmationRec.RESET;
                            BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                            BankConfirmationRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                            BankConfirmationRec.SETRANGE("Teller No.", "Unposted Teller No.");
                            BankConfirmationRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                            if BankConfirmationRec.FINDSET then
                                repeat
                                    if BankConfirmationRec."No." <> "No." then
                                        ERROR(
                                            Text50202, "Teller No.", BankConfirmationRec."Teller Date", BankConfirmationRec."Bank Code",
                                            BankConfirmationRec."Global Dimension 1 Code", BankConfirmationRec."No.");
                                until BankConfirmationRec.NEXT = 0;

                        end;

                        dd := DATE2DMY(TODAY, 1);
                        mm := DATE2DMY(TODAY, 2);
                        yy := DATE2DMY(TODAY, 3);

                        RANDOMIZE();
                        ConfirmNo := FORMAT(RANDOM(5000) * dd + mm + yy);

                        //Check For Duplicate Confirmation No.
                        BankConfirmationRec.RESET;
                        BankConfirmationRec.SETRANGE("Confirmation No.", ConfirmNo);
                        if BankConfirmationRec.FINDFIRST then begin
                            Confirmed := false;
                            ERROR(
                               Text50207, ConfirmNo, BankConfirmationRec."Teller Date",
                               BankConfirmationRec."Bank Code",
                               BankConfirmationRec."Global Dimension 1 Code");
                        end;

                        "Confirmation No." := ConfirmNo;
                        "Confirmed By" := USERID;
                        "Confirmation Date" := TODAY;
                        "Confirmation Time" := TIME;

                    end;
                end;

                if ("Teller Is Confirmed" and ("Credit Account Type" = "Credit Account Type"::"G/L Account") or
                  ("Credit Account Type" = "Credit Account Type"::Vendor)) then begin  //NYO 12/12/19 0100, Vendor added to the options

                    //TESTFIELD(Company);
                    TESTFIELD("Global Dimension 1 Code");
                    TESTFIELD("Global Dimension 2 Code");
                    TESTFIELD("Responsibility Center");
                    TESTFIELD("Account No.");
                    TESTFIELD("Teller Type");
                    TESTFIELD("Teller No.");
                    TESTFIELD("Teller Date");
                    TESTFIELD("Teller No.");
                    if "Teller Type" = "Teller Type"::"Teller Cheque" then
                        TESTFIELD("Chq. Value Date");

                    TESTFIELD("Bank No.");
                    TESTFIELD("Bank Code");
                    TESTFIELD("Bank Location");
                    TESTFIELD("Teller Amount");

                    CLEAR(BankTeller);
                    BankTeller := FORMAT("Bank Code") + "Teller No.";
                    //"Teller No.":=BankTeller;

                    //Check for duplicate Teller No in Confirmed Teller Register
                    if "Teller Type" = "Teller Type"::"Teller Cash" then begin
                        //BankTeller:= FORMAT("Bank Code")+"Teller No.";
                        BankConfirmedRec.RESET;
                        BankConfirmedRec.SETRANGE("Bank Code", "Bank Code");
                        BankConfirmedRec.SETRANGE(BankConfirmedRec."Teller No.", BankTeller);
                        BankConfirmedRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmedRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                        if BankConfirmedRec.FINDSET then
                            repeat
                                if BankConfirmedRec."Teller No." = BankTeller then begin
                                    //IF "Teller Date" = BankConfirmedRec."Teller Date" THEN
                                    ERROR(Text50201, BankConfirmedRec."Teller No.", BankConfirmedRec."Teller Date", BankConfirmedRec."Bank Code",
                                    BankConfirmedRec."Global Dimension 1 Code", BankConfirmedRec."No.");
                                end;
                            until BankConfirmedRec.NEXT = 0;

                        //To test for duplication Teller No. in unposted confirmations
                        BankConfirmationRec.RESET;
                        BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                        BankConfirmationRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmationRec.SETRANGE("Teller No.", "Unposted Teller No.");
                        BankConfirmationRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                        if BankConfirmationRec.FINDSET then
                            repeat
                                if BankConfirmationRec."No." <> "No." then
                                    ERROR(
                                        Text50202, "Teller No.", BankConfirmationRec."Teller Date", BankConfirmationRec."Bank Code",
                                        BankConfirmationRec."Global Dimension 1 Code", BankConfirmationRec."No.");
                            until BankConfirmationRec.NEXT = 0;

                    end;

                    dd := DATE2DMY(TODAY, 1);
                    mm := DATE2DMY(TODAY, 2);
                    yy := DATE2DMY(TODAY, 3);

                    RANDOMIZE();
                    ConfirmNo := FORMAT(RANDOM(5000) * dd + mm + yy);

                    //Check For Duplicate Confirmation No.
                    BankConfirmationRec.RESET;
                    BankConfirmationRec.SETRANGE("Confirmation No.", ConfirmNo);
                    if BankConfirmationRec.FINDFIRST then begin
                        Confirmed := false;
                        ERROR(
                           Text50207, ConfirmNo, BankConfirmationRec."Teller Date",
                           BankConfirmationRec."Bank Code",
                           BankConfirmationRec."Global Dimension 1 Code");
                    end;

                    "Confirmation No." := ConfirmNo;
                    "Confirmed By" := USERID;
                    "Confirmation Date" := TODAY;
                    "Confirmation Time" := TIME;

                end;
                //NYO 26-09-2019 for G/L and Customer Request <<


                if not "Teller Is Confirmed" then begin
                    CLEAR("Confirmation No.");
                    CLEAR("Confirmed By");
                    "Confirmation Date" := 0D;
                    "Confirmation Time" := 000000T;
                end;

            end;
        }
        field(16; "Line No."; Integer)
        {
        }
        field(17; "Last Modified By"; Code[50])
        {
            Editable = false;
        }
        field(18; "Last Modified Date"; DateTime)
        {
            Editable = false;
        }
        field(19; "Unposted Teller No."; Code[20])
        {
        }
        field(21; "Global Dimension 1 Code"; Code[20])
        {
            Caption = 'Branch Name';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));

            trigger OnValidate();
            begin
                if "Teller No." <> '' then begin
                    BankTeller := ' ';
                    if "Teller Type" = "Teller Type"::"Teller Cash" then begin
                        //BankTeller:= FORMAT("Bank Code")+"Teller No.";
                        BankConfirmedRec.SETRANGE(BankConfirmedRec."Bank Code", "Bank Code");
                        BankConfirmedRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmedRec.SETRANGE("Teller Date", CALCDATE('-60D', "Teller Date"), "Teller Date");
                        if BankConfirmedRec.FINDFIRST then
                            repeat
                                if BankConfirmedRec."Teller No." = "Teller No." then begin
                                    if "Teller Date" = BankConfirmedRec."Teller Date" then
                                        ERROR(Text50201, BankConfirmedRec."Teller No.", "Teller Date", BankConfirmedRec."Bank Code",
                                        BankConfirmedRec."Global Dimension 1 Code");
                                end;
                            until BankConfirmedRec.NEXT = 0;

                        // To test for duplication confirmation nos in unposted confirmations
                        BankConfirmationRec.SETRANGE("Bank Code", "Bank Code");
                        BankConfirmationRec.SETRANGE("Global Dimension 1 Code", "Global Dimension 1 Code");
                        BankConfirmationRec.SETRANGE("Teller No.", "Teller No.");
                        if BankConfirmationRec.FINDFIRST then
                            repeat
                                if "Teller Date" = BankConfirmationRec."Teller Date" then
                                    ERROR(Text50202, BankConfirmationRec."Teller No.", "Teller Date",
                                    BankConfirmationRec."Bank Code", BankConfirmationRec."Global Dimension 1 Code");
                            until BankConfirmationRec.NEXT = 0;
                        // End of Test
                    end;
                end;
            end;
        }
        field(22; "Reversed By"; Code[50])
        {
        }
        field(23; Company; Option)
        {
            OptionCaption = 'CHI';
            OptionMembers = CHI;
        }
        field(24; "Chq. Value Date"; Date)
        {
            Caption = 'Cheque Value Date';

            trigger OnValidate();
            begin
                if "Teller Type" = "Teller Type"::"Teller Cheque" then begin
                    TESTFIELD("Teller Date");
                    if "Chq. Value Date" < "Teller Date" then
                        ERROR(Text50206, FIELDCAPTION("Chq. Value Date"), FIELDCAPTION("Teller Date"));
                end;

                if "Teller Type" = "Teller Type"::"Teller Cash" then
                    ERROR(Text50209, FIELDCAPTION("Chq. Value Date"), FIELDCAPTION("Teller Type"), "Teller Type"::"Teller Cash");
            end;
        }
        field(25; "Branch Names"; Option)
        {
            OptionCaption = '" ,ABA,ABUJA,AJANLA,BENIN,GOMBE,IBADAN,KADUNA,KANO,MAIDUGURI,MAKURDI,ONITSHA,PORT-HARCOURT,SOKOTO,YOLA,AJAO,IKEJA,IKOYI,S\LERE,APAPA,TGI-KAN,TGI-ABJ,ENUGU,WARRI,LAGOS"';
            OptionMembers = " ",ABA,ABUJA,AJANLA,BENIN,GOMBE,IBADAN,KADUNA,KANO,MAIDUGURI,MAKURDI,ONITSHA,"PORT-HARCOURT",SOKOTO,YOLA,AJAO,IKEJA,IKOYI,"S\LERE",APAPA,"TGI-KAN","TGI-ABJ",ENUGU,WARRI,LAGOS;
        }
        field(26; "Teller Type"; Option)
        {
            OptionCaption = '" ,Teller Cash,Teller Cheque,Post Dated Cheque,E-Payment"';
            OptionMembers = " ","Teller Cash","Teller Cheque",PDC,"E-Payment";

            trigger OnValidate();
            begin
                if "Teller Type" = "Teller Type"::"Teller Cash" then begin
                    TESTFIELD("Teller Date");
                    "Chq. Value Date" := "Teller Date";
                end;

                if "Teller Type" = "Teller Type"::"Teller Cheque" then
                    CLEAR("Chq. Value Date");

                CLEAR("Teller No.");
            end;
        }
        field(27; "Bank Location"; Code[50])
        {
        }
        field(28; "No."; Integer)
        {
            Editable = false;

        }
        field(35; "Customer No."; Code[20])
        {
            TableRelation = "Customer Resp. Cent. Lines"."customer No." WHERE("Resp. Center Code" = FIELD("Responsibility Center"));
            trigger OnValidate();
            begin
                if "Customer No." = '' then
                    CLEAR("Customer Name");

                if CustRec.GET("Customer No.") then begin
                    //CustRec.TESTFIELD(Status,CustRec.Status::Released);CHI2.0
                    "Customer Name" := CustRec.Name;
                end;
            end;
        }
        field(36; "Global Dimension 2 Code"; Code[20])
        {
            CaptionClass = '1,2,2';
            Caption = 'Global Dimension 2 Code';

            trigger OnLookup();
            begin
                if GLSetup.GET then
                    if GLSetup."Bank Teller Confirmation CC" <> '' then begin
                        DimVal.RESET;
                        DimVal.FILTERGROUP(2);
                        DimVal.SETRANGE("Global Dimension No.", 2);
                        DimVal.SETRANGE(DimVal.Code, GLSetup."Bank Teller Confirmation CC");
                        DimVal.FILTERGROUP(0);
                        if DimVal.FINDSET then
                            if PAGE.RUNMODAL(0, DimVal) = ACTION::LookupOK then
                                "Global Dimension 2 Code" := DimVal.Code;
                    end;
            end;

            trigger OnValidate();
            begin
                if GLSetup.GET then
                    if GLSetup."Bank Teller Confirmation CC" <> "Global Dimension 2 Code" then
                        ERROR('CC code must be %1', GLSetup."Bank Teller Confirmation CC");
            end;
        }
        field(37; "Responsibility Center"; Code[20])
        {
            trigger OnValidate();
            begin
                /*
           if ResponsibilityCenter.GET("Responsibility Center") then
               "Global Dimension 1 Code" := ResponsibilityCenter."Global Dimension 1 Code";
           Prasanna removed and moved to page*/
            end;
        }
        field(38; "Bank Name"; Text[100])
        {
            Editable = false;
        }
        field(39; "Created by"; Code[50])
        {
            Editable = false;
        }
        field(40; "Created Date"; Date)
        {
            Editable = false;
        }
        field(41; "Created Time"; Time)
        {
            Editable = false;
        }
        field(42; "Released for Confirmation"; Boolean)
        {
            Editable = true;
        }
        field(44; "Paid By"; Text[100])
        {
        }
        field(45; "Released By"; Code[50])
        {
        }
        field(46; "Released Date"; Date)
        {
        }
        field(47; "Released time"; Time)
        {
        }
        field(48; "Reason for Return"; Text[200])
        {
        }
        field(49; "Created BRV No."; Code[20])
        {
            Editable = false;
        }
        field(60; "Teller Returned"; Boolean)
        {
        }
        field(61; "Returned Teller Information"; Text[250])
        {
        }
        field(62; "Returned Teller No."; Code[20])
        {
        }
        field(63; "Returned Teller Date"; Date)
        {
        }
        field(64; "Returned Teller Amount"; Decimal)
        {
        }
        field(65; "Credit Account Type"; Option)
        {
            Description = '//NYO 12/12/19 0100, Vendor added to the options';
            OptionCaption = '" ,Customer,G/L Account,Vendor"';
            OptionMembers = " ",Customer,"G/L Account",Vendor;
        }
        field(66; "Account No."; Code[20])
        {
            TableRelation = IF ("Credit Account Type" = FILTER("G/L Account")) "G/L Account"."No."
            ELSE
            IF ("Credit Account Type" = FILTER(Vendor)) Vendor."No.";

            trigger OnValidate();
            begin
                //CRF: 2019 - 0090 SAA  >>
                TESTFIELD("Customer No.", '');
                if "Account No." <> '' then
                    if GLRec.GET("Account No.") then
                        VALIDATE("G/L Account Name", GLRec.Name);
                //CRF: 2019 - 0090 SAA   <<

                //NYO 12/12/19 0100, Vendor added to the options>>
                if ("Credit Account Type" = "Credit Account Type"::Vendor) and ("Account No." <> '') then
                    if VendRec.GET("Account No.") then
                        VALIDATE("G/L Account Name", VendRec.Name);
                //NYO 12/12/19 0100, Vendor added to the options<<
            end;
        }
        field(67; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency;

            trigger OnValidate();
            begin
                //TestStatusOpen; // UNL 1.0

                if "Customer No." <> '' then begin
                    if CustRec.GET("Customer No.") and (CustRec."Currency Code" <> '') then
                        CustRec.TESTFIELD("Currency Code", "Currency Code");
                end;

                if "Currency Code" <> '' then begin
                    //GetCurrency;
                    if ("Currency Code" <> xRec."Currency Code") or
                       ("Teller Date" <> xRec."Teller Date") or
                       (CurrFieldNo = FIELDNO("Currency Code")) or
                       ("Currency Factor" = 0)
                    then
                        "Currency Factor" :=
                          CurrExchRate.ExchangeRate(TODAY, "Currency Code");
                end else
                    "Currency Factor" := 0;
                VALIDATE("Currency Factor");
            end;
        }
        field(68; "Teller Amount(LCY)"; Decimal)
        {
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Teller Amount (LCY)';
            Editable = false;

            trigger OnValidate();
            begin
                //TestStatusOpen; // UNL 1.0
            end;
        }
        field(69; "Currency Factor"; Decimal)
        {
            Caption = 'Currency Factor';
            DecimalPlaces = 0 : 15;
            Editable = false;
            MinValue = 0;

            trigger OnValidate();
            begin
                if ("Currency Code" = '') and ("Currency Factor" <> 0) then
                    FIELDERROR("Currency Factor", STRSUBSTNO(Text002, FIELDCAPTION("Currency Code")));
                VALIDATE("Teller Amount");
            end;
        }
        field(70; "G/L Account Name"; Text[100])
        {
            Editable = false;
        }
        field(71; Narration; Text[100])
        {
        }
        field(73; "Reconciliation teller no."; Code[20])
        {
            //b2bpksalecorr11
            DataClassification = CustomerContent;

        }
        field(74; "Teller no. Modified By"; Code[50])
        {
            //b2bpksalecorr12
            DataClassification = CustomerContent;
            Editable = false;

        }
        field(75; "Teller no. Modified Date"; Date)
        {
            //b2bpksalecorr12
            DataClassification = CustomerContent;
            Editable = false;

        }
    }

    keys
    {
        key(Key1; "No.")
        {
        }
        key(Key2; "Branch Code")
        {
            Enabled = false;
        }
        key(Key3; "Teller Date")
        {
        }
        key(Key4; "Teller Amount")
        {
        }
    }

    fieldgroups
    {
    }

    trigger OnInsert();
    begin
        if BankConfirmationRec.FINDLAST then
            "No." := BankConfirmationRec."No." + 1
        else
            "No." := 1;

        "Last Modified By" := USERID;
        "Last Modified Date" := CURRENTDATETIME;
        "Created by" := USERID;
        "Created Date" := TODAY;
        "Created Time" := TIME;
    end;

    trigger OnModify();
    begin
        "Last Modified By" := USERID;
        "Last Modified Date" := CURRENTDATETIME;
    end;

    var
        UserSetup: Record "User Setup";
        Banks: Record "Bank Account";
        BankConfirmationRec: Record "Request Teller Receipt";
        BankConfirmedRec: Record "Confirmed Teller Receipt";
        BankTeller: Code[30];
        Text50200: Label 'The Record cannot be modified if teller no is not blank';
        Text50201: Label 'Teller No %1 already exist...! on %2, in %3, in branch %4 in the posted Bank Confirmed Tellers %5';
        Text50202: Label 'Teller No %1 already exist...! on date %2, in %3, in branch %4 in the Bank Tellers Confirmation Register %5';
        Text50203: Label 'Confirmation No. must not be blank';
        Text50204: Label '"You cannot modify the Confirmation No. when TellerIsConfirmed "';
        Text50205: Label 'You do not have permission to return the Teller/Cheque.';
        CustRec: Record Customer;
        Text50206: Label '%1 cannot be less than %2.';
        BankAcctRec: Record "Bank Account";
        dd: Integer;
        mm: Integer;
        yy: Integer;
        ConfirmNo: Code[20];
        Text50207: Label 'Confirmation No. %1 already exist...! for the teller on %2, in %3, in branch %4 in the Bank Tellers Confirmation Register, please re-confirm.';
        Confirmed: Boolean;
        Text50208: Label 'The Teller/Cheque is already confirmed.';
        Text50209: Label 'You cannot enter %1 when %2 is %3.';
        Text50210: Label 'You do not have permission to release Request for Teller Receipt.';
        Text50211: Label 'You do not have permission for Teller/Cheque confirmation.';
        TellerNo: Text[20];
        Text50212: Label '%1 must not start with a zero.';
        GLSetup: Record "General Ledger Setup";
        DimVal: Record "Dimension Value";
        ResponsibilityCenter: Record "Responsibility Center";
        Currency: Record Currency;
        CurrExchRate: Record "Currency Exchange Rate";
        FromCurrencyCode: Code[10];
        ToCurrencyCode: Code[10];
        GLRec: Record "G/L Account";
        Text002: Label 'cannot be specified without %1';
        VendRec: Record Vendor;

    procedure CheckMandValues();
    var
        Text0001: Label '%1 value %2 in the Bank Teller Confirmation Register does not match with the value %3 in data template %4.';
        Text0002: Label '%1 must not be empty, in line No. %2';
        BankTellerConfRegLRec: Record "Request Teller Receipt";
        //DataTempHeadLRec : Record "Data Template Header_CHI";//B2BSB.1.0
        //DataTempLineLRec : Record "Data Template Line_CHI";//B2BSB.1.0
        RecRefLVar: RecordRef;
        FieldRefLVar: FieldRef;
        Text0003: Label 'There is no data template created for %1.';
        Text0004: Label 'There is one or no mandatory field specify in data template for %1.';
        Text0005: Label 'There is nothing to approve.';
    begin
        /*
        BankTellerConfRegLRec.RESET;
        //BankTellerConfRegLRec.SETRANGE("Customer No.","Customer No.");
        BankTellerConfRegLRec.SETFILTER("Teller Is Confirmed",'%1',TRUE);
        
        IF BankTellerConfRegLRec.FINDSET THEN BEGIN
          REPEAT
            DataTempLineLRec.RESET;
            DataTempLineLRec.SETRANGE(TableID,50021);
            DataTempLineLRec.SETRANGE(Type,DataTempLineLRec.Type ::Field);
            IF DataTempLineLRec.FINDSET THEN BEGIN
              CLEAR(FieldRefLVar);
              CLEAR(RecRefLVar);
        
              REPEAT
                CLEAR(FieldRefLVar);
                CLEAR(RecRefLVar);
        
                RecRefLVar.GETTABLE(BankTellerConfRegLRec);
                FieldRefLVar := RecRefLVar.FIELD(DataTempLineLRec.FieldID);
        
                IF DataTempLineLRec.Mandatory THEN BEGIN
                  IF DataTempLineLRec."Default Value" <> '' THEN BEGIN
                    IF FORMAT(FieldRefLVar.VALUE) <> DataTempLineLRec."Default Value" THEN
                      ERROR(Text0001,
                             FieldRefLVar.NAME,FieldRefLVar.VALUE,DataTempLineLRec."Default Value",DataTempLineLRec."Data Template Code");
                  END ELSE BEGIN
                    IF (FORMAT(FieldRefLVar.VALUE) = '')  OR (FORMAT(FieldRefLVar.VALUE) = '0') THEN
                      ERROR(Text0002,FieldRefLVar.NAME, BankTellerConfRegLRec."No.");
                  END;
                END ELSE
                 ERROR(Text0004,BankTellerConfRegLRec.TABLENAME);
              UNTIL DataTempLineLRec.NEXT = 0;
            END ELSE
             ERROR(Text0003, BankTellerConfRegLRec.TABLENAME);
          UNTIL BankTellerConfRegLRec.NEXT =0;
        END ELSE
         ERROR(Text0005);
        */
        if ("Credit Account Type" <> "Credit Account Type"::"G/L Account") and
        //NYO 12/12/19 0100, Vendor added to the options >>
           ("Credit Account Type" <> "Credit Account Type"::Vendor) then begin
            //NYO 12/12/19 0100, Vendor added to the options <<

            BankTellerConfRegLRec.RESET;
            BankTellerConfRegLRec.SETRANGE("No.", "No.");
            BankTellerConfRegLRec.SETRANGE("Customer No.", "Customer No.");
            BankTellerConfRegLRec.SETFILTER("Teller Is Confirmed", '%1', true);
            if BankTellerConfRegLRec.FINDFIRST then begin

                BankTellerConfRegLRec.TESTFIELD("Global Dimension 1 Code");
                BankTellerConfRegLRec.TESTFIELD("Global Dimension 2 Code");
                BankTellerConfRegLRec.TESTFIELD("Responsibility Center");
                BankTellerConfRegLRec.TESTFIELD("Customer No.");
                BankTellerConfRegLRec.TESTFIELD("Paid By");
                BankTellerConfRegLRec.TESTFIELD("Teller Type");
                BankTellerConfRegLRec.TESTFIELD("Teller No.");
                BankTellerConfRegLRec.TESTFIELD("Teller Date");
                BankTellerConfRegLRec.TESTFIELD("Bank Code");

                BankTellerConfRegLRec.TESTFIELD("Bank No.");
                BankTellerConfRegLRec.TESTFIELD("Bank Location");
                BankTellerConfRegLRec.TESTFIELD("Teller Amount");

                if BankTellerConfRegLRec."Teller Type" = BankTellerConfRegLRec."Teller Type"::"Teller Cheque" then
                    BankTellerConfRegLRec.TESTFIELD("Chq. Value Date");
            end;
        end;
        if ("Credit Account Type" = "Credit Account Type"::"G/L Account") or
        //NYO 12/12/19 0100, Vendor added to the options>>
           ("Credit Account Type" = "Credit Account Type"::Vendor) then begin
            //NYO 12/12/19 0100, Vendor added to the options<<

            BankTellerConfRegLRec.RESET;
            BankTellerConfRegLRec.SETRANGE("No.", "No.");
            BankTellerConfRegLRec.SETRANGE("Account No.", "Account No.");
            BankTellerConfRegLRec.SETFILTER("Teller Is Confirmed", '%1', true);
            if BankTellerConfRegLRec.FINDFIRST then begin

                BankTellerConfRegLRec.TESTFIELD("Global Dimension 1 Code");
                BankTellerConfRegLRec.TESTFIELD("Global Dimension 2 Code");
                BankTellerConfRegLRec.TESTFIELD("Responsibility Center");
                BankTellerConfRegLRec.TESTFIELD("Account No.");
                BankTellerConfRegLRec.TESTFIELD("Paid By");
                BankTellerConfRegLRec.TESTFIELD("Teller Type");
                BankTellerConfRegLRec.TESTFIELD("Teller No.");
                BankTellerConfRegLRec.TESTFIELD("Teller Date");
                BankTellerConfRegLRec.TESTFIELD("Bank Code");

                BankTellerConfRegLRec.TESTFIELD("Bank No.");
                BankTellerConfRegLRec.TESTFIELD("Bank Location");
                BankTellerConfRegLRec.TESTFIELD("Teller Amount");

                if BankTellerConfRegLRec."Teller Type" = BankTellerConfRegLRec."Teller Type"::"Teller Cheque" then
                    BankTellerConfRegLRec.TESTFIELD("Chq. Value Date");
            end;
            //NYO 26-09-2019 for G/L and Customer Request <<
        end;

    end;

    procedure ReopenRelBankConf();
    begin
        /*IF UserSetup.GET(USERID) THEN
         IF UserSetup."Teller/Cheque Awaiting Confirm" THEN BEGIN
           TESTFIELD("Reason for Return");
           BankConfirmationRec.RESET;
           BankConfirmationRec.SETRANGE("No.", "No.");
           BankConfirmationRec.SETRANGE("Released for Confirmation",TRUE);
           //BankConfirmationRec.SETRANGE("Teller Is Confirmed",FALSE);
           IF BankConfirmationRec.FINDFIRST THEN BEGIN
             BankConfirmationRec."Released for Confirmation" :=FALSE;
             BankConfirmationRec."Released By" :='';
             BankConfirmationRec."Released Date" :=0D;
             BankConfirmationRec."Released time" :=0T;
        
             BankConfirmationRec."Teller Is Confirmed" :=FALSE;
             BankConfirmationRec."Confirmation No." :='';
             BankConfirmationRec."Confirmed By" :='';
             BankConfirmationRec."Confirmation Time" :=0T;
             BankConfirmationRec."Confirmation Date" :=0D;
        
             BankConfirmationRec."Teller No." :="Unposted Teller No.";
             BankConfirmationRec."Bank No." :='';
             BankConfirmationRec."Bank Name" :='';
             BankConfirmationRec."Bank Code" :=BankConfirmationRec."Bank Code" ::" ";
             BankConfirmationRec."Bank Location" :='';
             BankConfirmationRec.MODIFY;
           END
         END ELSE
          ERROR(Text50205);
        */

        if UserSetup.GET(USERID) then
            if UserSetup."Teller/Cheque Awaiting Confirm" then begin
                TESTFIELD("Reason for Return");
                BankConfirmationRec.RESET;
                BankConfirmationRec.SETRANGE("No.", "No.");
                BankConfirmationRec.SETRANGE("Released for Confirmation", true);
                //BankConfirmationRec.SETRANGE("Teller Is Confirmed",FALSE);
                if BankConfirmationRec.FINDFIRST then begin
                    BankConfirmationRec."Teller Returned" := true;
                    BankConfirmationRec."Returned Teller No." := BankConfirmationRec."Teller No.";
                    BankConfirmationRec."Returned Teller Date" := BankConfirmationRec."Teller Date";
                    BankConfirmationRec."Returned Teller Amount" := BankConfirmationRec."Teller Amount";
                    BankConfirmationRec."Released for Confirmation" := false;
                    //BankConfirmationRec."Released By" :='';
                    //BankConfirmationRec."Released Date" :=0D;
                    //BankConfirmationRec."Released time" :=0T;

                    BankConfirmationRec."Teller Is Confirmed" := false;
                    BankConfirmationRec."Confirmation No." := '';
                    BankConfirmationRec."Confirmed By" := '';
                    BankConfirmationRec."Confirmation Time" := 000000T;
                    BankConfirmationRec."Confirmation Date" := 0D;
                    BankConfirmationRec."Teller Amount" := 0;

                    BankConfirmationRec."Teller No." := '';//"Unposted Teller No.";
                    BankConfirmationRec."Bank No." := '';
                    BankConfirmationRec."Bank Name" := '';
                    BankConfirmationRec."Bank Code" := BankConfirmationRec."Bank Code"::" ";
                    BankConfirmationRec."Bank Location" := '';
                    BankConfirmationRec.MODIFY;
                end
            end else
                ERROR(Text50205);

    end;

    procedure PerformManualRelease();
    begin
        if UserSetup.GET(USERID) then
            if not UserSetup."Request for Teller Receipt" then
                ERROR(Text50210);

        //IF "Released for Confirmation" THEN BEGIN
        if ("Credit Account Type" <> "Credit Account Type"::"G/L Account") and
        //NYO 12/12/19 0100, Vendor added to the options>>
          ("Credit Account Type" <> "Credit Account Type"::Vendor) then begin
            //NYO 12/12/19 0100, Vendor added to the options<<

            TESTFIELD("Global Dimension 1 Code");
            TESTFIELD("Global Dimension 2 Code");
            TESTFIELD("Responsibility Center");
            TESTFIELD("Customer No.");
            TESTFIELD("Paid By");
            TESTFIELD("Teller Type");
            TESTFIELD("Teller No.");
            TESTFIELD("Teller Date");
            TESTFIELD("Teller Amount");
            TESTFIELD("Bank Code");
            TESTFIELD("Bank Location");
            //TESTFIELD(Narration);//PKON22AP27 
            if "Teller Type" = "Teller Type"::"Teller Cheque" then
                TESTFIELD("Chq. Value Date");
        end;

        if ("Credit Account Type" = "Credit Account Type"::"G/L Account") or
        //NYO 12/12/19 0100, Vendor added to the options>>
           ("Credit Account Type" = "Credit Account Type"::Vendor) then begin
            //NYO 12/12/19 0100, Vendor added to the options<<

            TESTFIELD("Global Dimension 1 Code");
            TESTFIELD("Global Dimension 2 Code");
            TESTFIELD("Responsibility Center");
            TESTFIELD("Account No.");
            TESTFIELD("Paid By");
            TESTFIELD("Teller Type");
            TESTFIELD("Teller No.");
            TESTFIELD("Teller Date");
            TESTFIELD("Teller Amount");
            TESTFIELD("Bank Code");
            TESTFIELD("Bank Location");
            TESTFIELD(Narration);//PKON22AP28 
            if "Teller Type" = "Teller Type"::"Teller Cheque" then
                TESTFIELD("Chq. Value Date");
        end;
        //NYO 26-09-2019 for G/L and Customer Request <<

        "Released for Confirmation" := true;
        "Released By" := USERID;
        "Released Date" := TODAY;
        "Released time" := TIME;
        CLEAR("Reason for Return");

        if "Created by" = '' then begin
            "Created by" := USERID;
            "Created Date" := TODAY;
            "Created Time" := TIME;
        end;

        MODIFY;
    end;
}

