pageextension 50080 SalesCreditMemo extends "Sales Credit Memo"
{
    layout
    {
        modify("Posting Description")
        {
            Visible = true;
        }
        addafter("Applies-to ID")
        {
            field("Reason Codes"; "Reason Codes")
            {

                ApplicationArea = all;
            }
            field("Ship-to Code"; "Ship-to Code")
            {
                ApplicationArea = all;
            }
            field("Pos Load Slip Reason Code"; "Pos Load Slip Reason Code")
            {
                ApplicationArea = ALL;
            }
            field(Narration; Narration)
            {

            }
        }
        addafter("Assigned User ID")
        {
            field("Rebate Period Code"; "Rebate Period Code")
            {
                ApplicationArea = all;
            }
            field("Cr. Memo Sub Doc. Type"; "Cr. Memo Sub Doc. Type")
            {
                ApplicationArea = ALL;
            }
            field("Printable Comment 1"; "Printable Comment 1")
            {
                ApplicationArea = all;
            }
            field("Cr. Memo Stock Type"; "Cr. Memo Stock Type")
            {
                ApplicationArea = ALL;
            }

            field("Cr. Memo Reason Type"; "Cr. Memo Reason Type")
            {
                ApplicationArea = all;
                trigger OnValidate()
                begin
                    if "Cr. Memo Reason Type" = "Cr. Memo Reason Type"::"Return Order" then
                        Error('You Can not Select this Option');//b2bpksalecorr15
                end;
            }
            field("GRN No."; "GRN No.")
            {
                ApplicationArea = all;
            }
            field("Branch GRN No."; "Branch GRN No.")
            {
                ApplicationArea = all;
            }
            field("Transporter Code"; "Transporter Code")
            {
                ApplicationArea = all;
            }
            field("Transporter Name"; "Transporter Name")
            {
                ApplicationArea = all;
            }
            field("Amount Including VAT"; "Amount Including VAT")
            {
                ApplicationArea = all;
            }
            field("Sales Ret. Created By"; "Sales Ret. Created By")
            {
                ApplicationArea = all;
            }
            field("Posted Loading Slip No."; "Posted Loading Slip No.")
            {
                ApplicationArea = ALL;
            }
        }
        modify("Sell-to Customer No.")
        {
            trigger OnAfterValidate()//PKONAU11.2
            var
                CustResp: record "Customer Resp. Cent. Lines";
            begin
                CustResp.Reset();
                CustResp.SetRange("Customer No.", "Sell-to Customer No.");
                IF CustResp.FindFirst() then
                    validate("Responsibility Center", CustResp."Resp. Center Code")
            end;
        }



    }
    actions
    {
        modify(Post)
        {
            trigger OnBeforeAction()
            begin
                CheckDim();//PKON22MA7
                IF "Rebate Period Code" <> '' THEN
                    ERROR('You can only post Rebate Cr. Memos from batch');
                TestField("Printable Comment 1");//PKONAU11.2
                TestField("Responsibility Center");
            end;
        }
        modify(Release)
        {
            trigger OnBeforeAction()
            begin
                CheckDim();//PKON22MA7
                TestField("Responsibility Center");
                TestField("Printable Comment 1");//PKONAU11.2
            end;
        }
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            begin
                CheckDim();//PKON22MA7
                TestField("Responsibility Center");
                TestField("Printable Comment 1");//PKONAU11.2
            end;
        }
        modify(PostAndSend)
        {
            trigger OnBeforeAction()
            begin
                CheckDim();//PKON22MA7
                TestField("Responsibility Center");
                TestField("Printable Comment 1");//PKONAU11.2
            end;

        }
        addafter(Post)
        {
            action(UpdateLines)
            {
                caption = 'Get Transporter Adj. Lines';
                trigger onaction()
                var
                    PosCreMemoLines: Record "Purch. Cr. Memo Line";
                    PosCreMemoHdr: Record "Purch. Cr. Memo Hdr.";
                    SalCreMemoLines: Record "Sales Line";
                    LineNoLVar: Integer;
                BEGIN
                    //TestField("Pos Load Slip Reason Code", 3);//PKonj08
                    TestField("Printable Comment 1");//PKONAU11.2
                    TestField("Posted Loading Slip No.");
                    SalCreMemoLines.RESET;
                    SalCreMemoLines.SetRange("Document Type", SalCreMemoLines."Document Type"::"Credit Memo");
                    SalCreMemoLines.SetRange("Document No.", "No.");
                    IF SalCreMemoLines.findfirst then
                        ERROR('Lines already exist for this document.');


                    PosCreMemoHdr.RESET;
                    PosCreMemoHdr.SetRange("Posted Loading Slip No.", "Posted Loading Slip No.");
                    PosCreMemoHdr.SetRange("Pos Load Slip Reason Code", "Pos Load Slip Reason Code");//PKonj08
                    PosCreMemoHdr.SetRange("Customer No. For Adj", "Sell-to Customer No.");//PKONJ29
                    IF PosCreMemoHdr.Findfirst() THEN BEGIN
                        PosCreMemoLines.reset;
                        PosCreMemoLines.SetRange("Document No.", PosCreMemoHdr."No.");
                        IF PosCreMemoLines.findset then
                            repeat
                                /*SalCreMemoLines.RESET;
                                SalCreMemoLines.SetRange("Document No.", "No.");
                                IF SalCreMemoLines.findfirst then BEGIN

                                     SalCreMemoLines.Validate(Type, PosCreMemoLines.Type);
                                     SalCreMemoLines.Validate("No.", PosCreMemoLines."No.");
                                     SalCreMemoLines.Validate(Quantity, PosCreMemoLines.Quantity);
                                     SalCreMemoLines.Validate("Location Code", PosCreMemoLines."Location Code");
                                     SalCreMemoLines.Modify();

                                END ELSE BEGIN
                                SalCreMemoLines.reset;
                                SalCreMemoLines.SetRange("Document No.", "No.");
                                IF SalCreMemoLines.findlast then
                                    LineNoLVar := SalCreMemoLines."Line No."
                                else
                                    LineNoLVar := 10000;*/

                                SalCreMemoLines.INIT;
                                SalCreMemoLines."Document Type" := SalCreMemoLines."Document Type"::"Credit Memo";
                                SalCreMemoLines."Document No." := "No.";
                                SalCreMemoLines."Line No." := PosCreMemoLines."Line No.";
                                SalCreMemoLines.Insert();
                                SalCreMemoLines.Validate(Type, PosCreMemoLines.Type);
                                SalCreMemoLines.Validate("No.", PosCreMemoLines."No.");
                                SalCreMemoLines.Validate(Quantity, PosCreMemoLines.Quantity);
                                SalCreMemoLines.Validate(Amount, amount);
                                SalCreMemoLines.Validate("Location Code", PosCreMemoLines."Location Code");
                                SalCreMemoLines.Validate("Shortcut Dimension 1 Code", PosCreMemoLines."Shortcut Dimension 1 Code");
                                SalCreMemoLines.Validate("Shortcut Dimension 2 Code", PosCreMemoLines."Shortcut Dimension 2 Code");
                                SalCreMemoLines.Validate("Dimension Set ID", PosCreMemoLines."Dimension Set ID");
                                SalCreMemoLines.Validate("Unit Price", PosCreMemoLines."Unit Cost");//PKONj08
                                SalCreMemoLines.Modify();
                            until PosCreMemoLines.next = 0;
                    END;
                END;
            }
        }

    }
    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        "Cr. Memo Stock Type" := "Cr. Memo Stock Type"::"Non-Items";
    end;

    procedure CheckDim()
    var
        SalesLine: Record "Sales Line";
    Begin
        TestField("Shortcut Dimension 1 Code");
        TestField("Shortcut Dimension 2 Code");
        SalesLine.RESET;
        SalesLine.SetRange("Document Type", SalesLine."Document Type"::"Credit Memo");
        SalesLine.SetRange("Document No.", "No.");
        IF SalesLine.FindSet() Then
            repeat
                SalesLine.TestField("Shortcut Dimension 1 Code");
                SalesLine.TestField("Shortcut Dimension 2 Code");
            Until SalesLine.Next = 0;
    End;
}