codeunit 50079 "Upload Customers"
{
    // version AutomationError,nibss
    trigger OnRun();
    begin
        CLEARALL;

        RecCustomer.RESET;
        RecCustomer.SETRANGE("Approval Status", RecCustomer."Approval Status"::Released);
        RecCustomer.SETFILTER(RecCustomer."Customer Posting Group", 'BRANCH|BRCUST|LOCAL|VANSALES'); //RKD CR220050 
        IF RecCustomer.FINDSET THEN
            REPEAT
                CLEAR(CustName);
                CustName := DELCHR(RecCustomer.Name, '=', QT);
                CLEAR(Loc);
                CLEAR(InsertQuery);
                CLEAR(UpdateQuery);
                custres.RESET;
                custres.SETFILTER(custres."Customer No.", RecCustomer."No.");
                custres.SETFILTER("Resp. Center Code", '<>%1', '');

                IF custres.FINDFIRST THEN
                    Loc := custres."Resp. Center Code"
                ELSE
                    Loc := '';
                //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
                SQLConnectionString := 'Server=192.168.1.106;Database=biller2;User Id=nibbss;Password=**********;';
                SqlConnection := SqlConnection.SqlConnection(SQLConnectionString);
                SQLConnection.Open();
                //Select Query
                //SelectQuery := 'SELECT * FROM [NIBSTest].[dbo].[validation] where [customercode] = ' + QT + RecCustomer."No." + QT;
                SelectQuery := 'SELECT * FROM [biller2].[dbo].[validation] where [customercode] = ' + QT + RecCustomer."No." + QT;
                SQLCommand := SQLCommand.SqlCommand(SelectQuery, SqlConnection);
                SQLCommand.CommandTimeout(0);
                SQLReader := SQLCommand.ExecuteReader();

                IF SQLReader.HasRows THEN begin
                    //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
                    SQLConnectionString := 'Server=192.168.1.106;Database=biller2;User Id=nibbss;Password=**********;';
                    SqlConnection2 := SqlConnection2.SqlConnection(SQLConnectionString);
                    SQLConnection2.Open();
                    WHILE SQLReader.Read DO BEGIN
                        //update Query
                        UpdateQuery := 'UPDATE [biller2].[dbo].[validation] SET [name] =' + QT + CustName + QT + ', ';
                        UpdateQuery += '[location] = ' + QT + Loc + QT + 'Where [customercode] =' + QT + RecCustomer."No." + QT;
                        SQLCommand := SQLCommand.SqlCommand(UpdateQuery, SqlConnection2);
                        SQLCommand.ExecuteNonQuery();
                    end;
                    SqlConnection2.Close();
                end Else begin
                    //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
                    //Con.Open('DRIVER=SQL Server;UID=nibbss;Password=**********;DATABASE=biller2;Trusted_Connection=Yes;SERVER=192.168.1.106');
                    SQLConnectionString := 'Server=192.168.1.106;Database=biller2;User Id=nibbss;Password=**********;';
                    SqlConnection3 := SqlConnection3.SqlConnection(SQLConnectionString);
                    SQLConnection3.Open();
                    //Insert Query
                    InsertQuery := 'INSERT INTO [biller2].[dbo].[validation]([customercode],[name],[location]) VALUES (';
                    InsertQuery += QT + RecCustomer."No." + QT + ',' + QT + CustName + QT + ',' + QT + Loc + QT + ')';
                    SQLCommand := SQLCommand.SqlCommand(InsertQuery, SqlConnection3);
                    SQLCommand.ExecuteNonQuery();
                    SqlConnection3.Close();
                end;
                SqlConnection.Close();
            UNTIL RecCustomer.NEXT = 0;
        UploadCustomertoInterswitch();
    end;

    procedure UploadCustomertoInterswitch()
    begin
        CLEARALL;

        RecCustomer.RESET;
        RecCustomer.SETRANGE("Approval Status", RecCustomer."Approval Status"::Released);
        RecCustomer.SETFILTER(RecCustomer."Customer Posting Group", 'BRANCH|BRCUST|LOCAL|VANSALES'); //Included Vansales CR220050 RKD 060422
        IF RecCustomer.FINDSET THEN
            REPEAT
                CLEAR(CustName);
                CustName := DELCHR(RecCustomer.Name, '=', QT);
                CLEAR(Loc);
                CLEAR(InsertQuery);
                CLEAR(UpdateQuery);
                custres.RESET;
                custres.SETFILTER(custres."Customer No.", RecCustomer."No.");
                IF custres.FINDFIRST THEN
                    Loc := custres."Resp. Center Code"
                ELSE
                    Loc := '';
                //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
                SQLConnectionString := 'Server=192.168.1.70;Database=biller;User Id=INTERSWITCH;Password=*****;';
                SqlConnection := SqlConnection.SqlConnection(SQLConnectionString);
                SQLConnection.Open();
                //Select Query
                //SelectQuery := 'SELECT * FROM [NIBSTest].[dbo].[validation] where [customercode] = ' + QT + RecCustomer."No." + QT;
                SelectQuery := 'SELECT * FROM [biller].[dbo].[validation] where [customercode] = ' + QT + RecCustomer."No." + QT;
                SQLCommand := SQLCommand.SqlCommand(SelectQuery, SqlConnection);
                SQLCommand.CommandTimeout(0);
                SQLReader := SQLCommand.ExecuteReader();

                IF SQLReader.HasRows THEN begin
                    //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
                    SQLConnectionString := 'Server=192.168.1.70;Database=biller;User Id=INTERSWITCH;Password=*****;';
                    SqlConnection2 := SqlConnection2.SqlConnection(SQLConnectionString);
                    SQLConnection2.Open();
                    WHILE SQLReader.Read DO BEGIN
                        //update Query
                        UpdateQuery := 'UPDATE [biller].[dbo].[validation] SET [Name] =' + QT + CustName + QT + ', ';
                        UpdateQuery += '[location] = ' + QT + Loc + QT + 'Where [customercode] =' + QT + RecCustomer."No." + QT;
                        SQLCommand := SQLCommand.SqlCommand(UpdateQuery, SqlConnection2);
                        SQLCommand.ExecuteNonQuery();
                    end;
                    SqlConnection2.Close();
                end Else begin
                    //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
                    //Con.Open('DRIVER=SQL Server;UID=nibbss;Password=**********;DATABASE=biller2;Trusted_Connection=Yes;SERVER=192.168.1.106');
                    SQLConnectionString := 'Server=192.168.1.70;Database=biller;User Id=INTERSWITCH;Password=*****;';
                    SqlConnection3 := SqlConnection3.SqlConnection(SQLConnectionString);
                    SQLConnection3.Open();
                    //Insert Query
                    InsertQuery := 'INSERT INTO [biller].[dbo].[validation]([customercode],[Name],[location]) VALUES (';
                    InsertQuery += QT + RecCustomer."No." + QT + ',' + QT + CustName + QT + ',' + QT + Loc + QT + ')';
                    SQLCommand := SQLCommand.SqlCommand(InsertQuery, SqlConnection3);
                    SQLCommand.ExecuteNonQuery();
                    SqlConnection3.Close();
                end;
                SqlConnection.Close();
            UNTIL RecCustomer.NEXT = 0;
    end;

    var
        SQLConnection: DotNet SqlConnection;
        SQLConnection2: DotNet SqlConnection;
        SqlConnection3: DotNet SqlConnection;
        SQLConnectionString: Text;
        SQLCommand: DotNet SqlCommand;
        SQLReader: DotNet SqlReader;
        SelectQuery: Text[1024];
        UpdateQuery: Text[1024];
        QT: Label '''';
        RecCustomer: Record Customer;
        InsertQuery: Text[1024];
        custres: Record "Customer Resp. Cent. Lines";
        Loc: Code[20];
        CustName: Text[100];
}

