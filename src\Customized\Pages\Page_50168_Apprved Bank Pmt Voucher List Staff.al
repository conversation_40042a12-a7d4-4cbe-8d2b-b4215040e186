page 50168 "Apprved Bank Pmt Vchr List Stf"
{
    // version CHI6.0

    // This page saved as from Apprved Bank Pmt Voucher List (50354, List) by B2BMSOn17Jan2022

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display voucher list.
    // 3.0      SAA      06-Apr-17      -> added "'%1'" to the setfilter in OnOpenForm.
    // B2BMS    B2BMS    17-Jan-22      -> Added Staff Voucher filter in SourceTableView Property. 

    Editable = false;
    PageType = List;
    SourceTable = "Voucher Header";
    Caption = 'Approved Bank Payment Voucher List - Staff';
    CardPageId = "Apprved Bank Pmt Vouchers Stf";
    UsageCategory = Lists;
    ApplicationArea = all;
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(BPV),
                            Status = CONST(Released),
                            "Staff Voucher" = filter(true)); //B2BMSOn17Jan2022

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Inbox Document No."; "Inbox Document No.")
                {
                    ApplicationArea = all;
                }
                field("WHT Applicable"; "WHT Applicable")
                {
                    ApplicationArea = all;

                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Posting Doc. No."; "Posting Doc. No.")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        //B2BMSOn17Jan2022>>
        UserSetupGRec.Get(UserId);
        if not UserSetupGRec."Open Staff Bank Pmt Voucher" then
            Error('You donot have permissions to access this page. Please Enable it in User Setup.');
        //B2BMSOn17Jan2022<<
    end;

    var
        UserSetupGRec: Record "User Setup"; //B2BMSOn17Jan2022
}

