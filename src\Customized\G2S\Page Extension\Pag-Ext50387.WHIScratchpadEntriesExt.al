/// <summary>
/// PageExtension WHIScratchpadEntriesExt (ID 50387) extends Record WHI Scratchpad Entries.
/// </summary>
pageextension 50387 WHIScratchpadEntriesExt extends "WHI Scratchpad Entries"
{
    //scratchpad branch 050724
    //
    Editable = false;
    layout
    {
        addafter(fldLocationCode)
        {
            field(Duplicate; Rec.Duplicate)
            {
                Editable = false;
                ApplicationArea = All;
            }
            field("Licence Plate"; Rec."Licence Plate")
            {
                ApplicationArea = All;
                Editable = false;
            }
            //>>> G2S >>>> 170524 
            field("Production Order"; Rec."Production Order")
            {
                ApplicationArea = All;
                Editable = false;
            }

            // <<<< G2S 170524
        }
    }

    actions
    {
        modify(acDecodeItems)
        {
            Visible = false;
        }
        modify(acShowDocument)
        {
            Visible = false;
        }
        modify(acProcess)
        {
            Visible = false;
        }
        modify(acConfigureDocuments)
        {
            Visible = false;
        }
        addafter(acDecodeItems)
        {

            action(Delete)
            {
                Image = DeleteExpiredComponents;
                Visible = CanDeleteProdOrder;
                trigger OnAction()
                var
                    Text001: Label 'Please confirm you wish to delete the entry(ies)';
                    iterator: Integer;
                    Text002: Label '%1 record(s) have been successfully deleted';
                    Text003: Label 'There is nothing to delete';
                    Scratchpad: Record "WHI Scratchpad Entry";

                begin
                    if Confirm(Text001, false) then begin
                        CurrPage.SetSelectionFilter(Scratchpad);
                        Scratchpad.Mark(true);
                        if Scratchpad.FindFirst() then begin
                            repeat
                                Scratchpad.SetRange(Posted, false);
                                Scratchpad.SetRange("Date Posted", 0D);
                                if Scratchpad.FindFirst() then begin
                                    if ((Scratchpad."Released Production Order") and (Scratchpad.Duplicate = true)) or (Scratchpad."Released Production Order" = false) then begin
                                        Scratchpad.MarkedOnly(false);
                                        Scratchpad.ClearMarks();
                                        Scratchpad.Delete();
                                        iterator += 1;
                                    end;
                                end;
                            until Scratchpad.Next() = 0;
                        end;
                        if iterator > 0 then
                            Message(Text002, iterator)
                        else
                            Message(Text003);
                    end;
                end;
            }
            action(Post)
            {
                Caption = 'Post';
                trigger OnAction()
                var
                    myInt: Integer;
                    CustomCodeCU: Codeunit "Custom-G2S";
                    CustomSetup: Record "Custom Setup";
                    Txt001: Label 'Please confirm you want to post the scratchpad records';
                    Txt002: Label 'Custom setup for LEAP Project does not exist. Please contact IT';
                    Item: Record Item;
                    IUOM: Record "Item Unit of Measure";
                    ProdOrder: Record "Production Order";
                    ProdOrderLine: Record "Prod. Order Line";
                    IUOMErrLbl: Label 'Pallets IUOM for %1 does not exist. Please contact IT';
                    ProdLineNtFoundErr: Label 'Production Order Line not found for order no: %1';
                    pdPreviousQuantityOut, pdNewQuantityOut : Decimal;
                    ProdOrdRoutingLine: Record "Prod. Order Routing Line";
                    RoutingLineNtFoundErr: Label 'Prod. Order Routing Line not found for %1';
                    Txt003: Label 'The records have been successfully posted';
                    RecExistToPost: Boolean;
                    Txt004: Label 'There is nothing to post';
                    RecCopy: Record "WHI Scratchpad Entry";
                begin
                    RecExistToPost := false;
                    if Confirm(Txt001, false) = true then begin
                        CustomSetup.Reset();
                        CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
                        if not CustomSetup.FindFirst() then
                            Error(Txt002);
                        CustomSetup.TestField("Scratchpad Journal Batch");
                        CustomSetup.TestField("Scratchpad Jnl. Template");
                        RecCopy.Reset();
                        RecCopy.SetCurrentKey("Production Order", "Licence Plate");
                        RecCopy.SetRange(Duplicate, false);
                        RecCopy.SetRange(Posted, false);
                        RecCopy.SetRange("Date Posted", 0D);
                        if RecCopy.FindSet() then begin
                            repeat
                                Clear(CustomCodeCU);
                                RecCopy.TestField(Posted, false);
                                RecCopy.TestField(Duplicate, false);
                                ProdOrder.Reset();
                                ProdOrder.SetRange("No.", RecCopy."Production Order");
                                //TBD
                                ProdOrder.SetRange(Status, ProdOrder.Status::Released);
                                //end;
                                If ProdOrder.FindFirst() then begin
                                    ProdOrder.TestField("Routing No.");
                                    ProdOrdRoutingLine.Reset();
                                    ProdOrdRoutingLine.SetRange("Routing No.", ProdOrder."Routing No.");
                                    if not ProdOrdRoutingLine.FindFirst() then
                                        Error(RoutingLineNtFoundErr, ProdOrder."No.");
                                    ProdOrderLine.Reset();
                                    ProdOrderLine.SetRange("Prod. Order No.", ProdOrder."No.");
                                    if not ProdOrderLine.FindFirst() then
                                        Error(ProdLineNtFoundErr, ProdOrder."No.");
                                    //end;
                                    IUOM.Reset();
                                    IUOM.SetRange("Item No.", RecCopy."Item No.");
                                    IUOM.SetRange(Code, 'PALLETS');
                                    if Not IUOM.FindFirst() then begin
                                        Error(IUOMErrLbl, RecCopy."Item No.")
                                    end;
                                    CustomCodeCU.CreateProdOutputEntry(CustomSetup."Scratchpad Jnl. Template", CustomSetup."Scratchpad Journal Batch", ProdOrderLine, ProdOrdRoutingLine."Operation No.", IUOM."Qty. per Unit of Measure", 0, 0, 0, '', RecCopy."Lot No.", '', RecCopy."Expiry Date", false, Today, false);
                                    CustomCodeCU.UpdateLPLine(RecCopy."Licence Plate", RecCopy."Item No.", '', '', RecCopy."Lot No.", '', RecCopy."Expiry Date", '', IUOM."Qty. per Unit of Measure", Item."Base Unit of Measure", '', pdPreviousQuantityOut, pdNewQuantityOut);
                                    CustomCodeCU.CreateOutputLPLineUsage(RecCopy."Licence Plate", 10000, RecCopy."Production Order", ProdOrderLine."Line No.", IUOM."Qty. per Unit of Measure");

                                    RecCopy.Posted := true;
                                    RecCopy."Posted By" := UserId;
                                    RecCopy."Date Posted" := Today();
                                    RecCopy."Posted DateTime" := CurrentDateTime();
                                    RecCopy."Date Posted" := Today();
                                    RecCopy.Modify();
                                    RecExistToPost := true;
                                    //tbd
                                end;
                            //end;
                            until RecCopy.Next() = 0;
                        end;
                        if RecExistToPost then
                            Message(Txt003)
                        else
                            Message(Txt004);
                    end;
                end;
            }
        }
    }

    trigger OnOpenPage()
    var
        myInt: Integer;
        UserSetup: Record "User Setup";
    begin

        UserSetup.Reset();
        UserSetup.SetRange("User ID", UserId);
        UserSetup.SetRange("Delete Production Order", true);
        if UserSetup.FindFirst() then begin
            CanDeleteProdOrder := true;
        end;
        Rec.SetRange(Posted, false);
        Rec.SetRange("Date Posted", 0D);
    end;


    var
        CanDeleteProdOrder: Boolean;
}
