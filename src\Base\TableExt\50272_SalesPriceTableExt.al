tableextension 50272 SalesPriceExt extends "Sales Price"
{
    fields
    {
        field(50000; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = ToBeClassified;
        }
        field(50001; "Item Description"; Text[100])
        {
            DataClassification = ToBeClassified;
        }
        //Balu Jun15>>
        modify("Unit Price")
        {
            trigger OnBeforeValidate()
            begin
                TestField("Approval Status", "Approval Status"::Open);
            end;
        }
        modify("Unit of Measure Code")
        {
            trigger OnBeforeValidate()
            begin
                TestField("Approval Status", "Approval Status"::Open);
            end;
        }
        modify("Minimum Quantity")
        {
            trigger OnBeforeValidate()
            begin
                TestField("Approval Status", "Approval Status"::Open);
            end;
        }
        modify("Starting Date")
        {
            trigger OnBeforeValidate()
            begin
                TestField("Approval Status", "Approval Status"::Open);
            end;
        }
        modify("Item No.")
        {
            trigger OnBeforeValidate()
            begin
                TestField("Approval Status", "Approval Status"::Open);
            end;

            trigger OnAfterValidate()
            Var
                Item: Record Item;
            begin
                Item.Get("Item No.");
                "Item Description" := Item.Description;
            end;
        }
        //Balu Jun15<<

    }



    trigger OnModify()
    begin
        //if "Approval Status" <> "Approval Status"::Open then
        //  error('You can not modify the record.');//Balu  On April 28
    end;
}