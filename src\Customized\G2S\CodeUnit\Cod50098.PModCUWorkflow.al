codeunit 50098 "PMOD Workflow Rqst Pg Handling"
{

    trigger OnRun()
    begin
    end;

    var
        PurchaseDocumentCodeTxt: Label 'PURCHMODDOC', Locked = true;
        PurchaseDocumentDescTxt: Label 'Purchase Document Modifier';

    procedure CreateEntitiesAndFields()
    begin
        InsertRequestPageEntities;
        InsertRequestPageFields;
        AssignEntitiesToWorkflowEvents();
    end;

    procedure AssignEntitiesToWorkflowEvents()
    begin
        AssignEntityToWorkflowEvent(DATABASE::"Purchase Doc Modifier", PurchaseDocumentCodeTxt);

        OnAfterAssignEntitiesToWorkflowEvents();
    end;

    local procedure InsertRequestPageEntities()
    begin
        InsertReqPageEntity(
          PurchaseDocumentCodeTxt, PurchaseDocumentDescTxt, DATABASE::"Purchase Doc Modifier", DATABASE::"Purchase Doc Modifier Line");

        OnAfterInsertRequestPageEntities();
    end;

    local procedure InsertReqPageEntity(Name: Code[20]; Description: Text[100]; TableId: Integer; RelatedTableId: Integer)
    begin
        if not FindReqPageEntity(Name, TableId, RelatedTableId) then
            CreateReqPageEntity(Name, Description, TableId, RelatedTableId);
    end;

    local procedure FindReqPageEntity(Name: Code[20]; TableId: Integer; RelatedTableId: Integer): Boolean
    var
        DynamicRequestPageEntity: Record "Dynamic Request Page Entity";
    begin
        DynamicRequestPageEntity.SetRange(Name, Name);
        DynamicRequestPageEntity.SetRange("Table ID", TableId);
        DynamicRequestPageEntity.SetRange("Related Table ID", RelatedTableId);
        exit(DynamicRequestPageEntity.FindFirst);
    end;

    local procedure CreateReqPageEntity(Name: Code[20]; Description: Text[100]; TableId: Integer; RelatedTableId: Integer)
    var
        DynamicRequestPageEntity: Record "Dynamic Request Page Entity";
    begin
        DynamicRequestPageEntity.Init();
        DynamicRequestPageEntity.Name := Name;
        DynamicRequestPageEntity.Description := Description;
        DynamicRequestPageEntity.Validate("Table ID", TableId);
        DynamicRequestPageEntity.Validate("Related Table ID", RelatedTableId);
        DynamicRequestPageEntity.Insert(true);
    end;

    local procedure InsertRequestPageFields()
    begin
        InsertPurchaseHeaderReqPageFields;
        InsertPurchaseLineReqPageFields;

        InsertApprovalEntryReqPageFields;

        OnAfterInsertRequestPageFields();
    end;


    local procedure InsertPurchaseHeaderReqPageFields()
    var
        PurchaseHeader: Record "Purchase Doc Modifier";
    begin
        InsertReqPageField(DATABASE::"Purchase Doc Modifier", PurchaseHeader.FieldNo("No."));
        InsertReqPageField(DATABASE::"Purchase Doc Modifier", PurchaseHeader.FieldNo(TableID));
    end;

    local procedure InsertPurchaseLineReqPageFields()
    var
        PurchaseLine: Record "Purchase Doc Modifier Line";
    begin
        InsertReqPageField(DATABASE::"Purchase Doc Modifier Line", PurchaseLine.FieldNo("Modifier Code"));
        InsertReqPageField(DATABASE::"Purchase Doc Modifier Line", PurchaseLine.FieldNo(TableID));
    end;

    local procedure InsertApprovalEntryReqPageFields()
    var
        ApprovalEntry: Record "Approval Entry";
    begin
        InsertReqPageField(DATABASE::"Approval Entry", ApprovalEntry.FieldNo("Pending Approvals"));
    end;

    local procedure InsertReqPageField(TableId: Integer; FieldId: Integer)
    var
        DynamicRequestPageField: Record "Dynamic Request Page Field";
    begin
        if not DynamicRequestPageField.Get(TableId, FieldId) then
            CreateReqPageField(TableId, FieldId);
    end;

    local procedure CreateReqPageField(TableId: Integer; FieldId: Integer)
    var
        DynamicRequestPageField: Record "Dynamic Request Page Field";
    begin
        DynamicRequestPageField.Init();
        DynamicRequestPageField.Validate("Table ID", TableId);
        DynamicRequestPageField.Validate("Field ID", FieldId);
        DynamicRequestPageField.Insert();
    end;

    local procedure AssignEntityToWorkflowEvent(TableID: Integer; DynamicReqPageEntityName: Code[20])
    var
        WorkflowEvent: Record "Workflow Event";
    begin
        WorkflowEvent.SetRange("Table ID", TableID);
        WorkflowEvent.SetFilter("Dynamic Req. Page Entity Name", '<>%1', DynamicReqPageEntityName);
        if not WorkflowEvent.IsEmpty then
            WorkflowEvent.ModifyAll("Dynamic Req. Page Entity Name", DynamicReqPageEntityName);
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterAssignEntitiesToWorkflowEvents()
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterInsertRequestPageEntities()
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterInsertRequestPageFields()
    begin
    end;

    procedure clearSalesreturnLine(isCheckCount: Boolean)
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        reservationQtyZero: Decimal;
        dateRange: Date;
        dateRange2: Date;
    begin
        recCount := 0;
        // Evaluate(dateRange, '01/06/25'); 01/09/2021
        saleSetup.Get();
        dateRange := saleSetup."Allow Document Deletion Before";
        saleSetup.TestField("Item filter");

        itemList := saleSetup."Item filter".Split('|');

        Dialog.Open('#1############\' + 'Working with record before #5############\' + 'Item: #2###########\' + 'Location: #3##############\' + 'Deleted: #4###########');
        Dialog.Update(1, 'Processing...........');

        recCount := 0;
        Dialog.Update(5, dateRange);
        foreach item in itemList do begin
            Dialog.Update(2, item);
            SalesLine.SetCurrentKey("No.");
            SalesLine.SetFilter("No.", '%1', item);
            if SalesLine.FindSet() then begin
                repeat
                    SalesHeader.SetCurrentKey("No.");
                    SalesHeader.SetRange("No.", SalesLine."Document No.");
                    if SalesHeader.FindFirst() then begin
                        if SalesHeader."Posting Date" <= dateRange then begin
                            Dialog.Update(3, SalesLine."Location Code");
                            if SalesLine.Quantity = 0 then begin
                                reservationEntry.SetCurrentKey("Source ID", "Item No.");
                                reservationEntry.SetFilter("Source ID", '%1', SalesLine."Document No.");
                                reservationEntry.SetFilter("Item No.", '%1', SalesLine."No.");
                                if not reservationEntry.FindSet() then begin
                                    if isCheckCount then begin
                                        recCount += 1;
                                        Dialog.Update(4, 'False');
                                    end else
                                        if SalesLine.Delete() then begin
                                            recCount += 1;
                                            Dialog.Update(4, 'True');
                                        end;
                                end else begin
                                    if isCheckCount then begin
                                        recCount += 1;
                                        Dialog.Update(4, 'False');
                                    end else begin
                                        trackingSpecification.SetCurrentKey("Lot No.");
                                        trackingSpecification.SetFilter("Lot No.", '%1', reservationEntry."Lot No.");
                                        if trackingSpecification.FindSet() then begin
                                            trackingSpecification.DeleteAll();
                                            reservationEntry.DeleteAll();
                                            if SalesLine.Delete() then begin
                                                recCount += 1;
                                                Dialog.Update(4, 'True');
                                            end;
                                        end else begin
                                            reservationEntry.DeleteAll();
                                            if SalesLine.Delete() then begin
                                                recCount += 1;
                                                Dialog.Update(4, 'True');
                                            end;
                                        end;
                                    end;
                                end;
                            end;
                        end;
                    end;
                until SalesLine.Next() = 0;
            end;
        end;

        Evaluate(dateRange, '01/09/2025');
        Evaluate(dateRange2, '03/09/2025');
        reservationEntry.Reset();
        reservationEntry.SetCurrentKey("Created By", "Creation Date");
        reservationEntry.SetFilter("Created By", '%1', 'AYODEJI');
        reservationEntry.SetFilter("Creation Date", '%1..%2', dateRange, dateRange2);
        reservationEntry.SetFilter(Description, '=%1', '');
        if reservationEntry.FindSet() then reservationEntry.DeleteAll();

        Dialog.Close();
        if isCheckCount then Message('Total %1 records found', recCount) else Message('%1 Records cleared successfully', recCount);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Reservation Entry", 'OnBeforeInsertEvent', '', false, false)]
    local procedure MyProcedure(var Rec: Record "Reservation Entry"; RunTrigger: Boolean)
    var
        item: Record Item;
    begin
        item.Get(Rec."Item No.");
    end;

    procedure clearTransferLine(isCheckCount: Boolean)
    var
        TransferHeader: Record "Transfer Header";
        TransferLine: Record "Transfer Line";
    begin
        recCount := 0;
        saleSetup.Get();
        // saleSetup.TestField("Item filter");

        itemList := saleSetup."Item filter".Split('|');

        // If itemList.Count = 0 then begin
        //   
        // end;
        recCount := 0;

        foreach item in itemList do begin
            TransferLine.SetCurrentKey("Item No.");
            TransferLine.SetFilter("Item No.", '%1', item);
            Dialog.Open('#6############\' + 'Item: #1###########\' + 'Order Status: #5############\' + 'Location \' + 'from #2############## to #3#############\' + 'Deleted: #4###########');
            Dialog.Update(6, 'Processing...........');
            if TransferLine.FindSet() then begin
                //MT650702
                repeat
                    Dialog.Update(1, TransferLine."Item No.");
                    if TransferHeader.Get(TransferLine."Document No.") then begin
                        Dialog.Update(2, TransferHeader."Transfer-from Code");
                        Dialog.Update(3, TransferHeader."Transfer-to Code");
                        if (TransferHeader."order status" <> TransferHeader."order status"::"Short close") then begin //and (TransferHeader.Status = TransferHeader.Status::Open)
                            if isCheckCount then begin
                                recCount += 1;
                                Dialog.Update(5, TransferHeader."order status");
                                Dialog.Update(4, 'False');
                            end else begin
                                reservationEntry.SetCurrentKey("Source ID", "Item No.");
                                reservationEntry.SetFilter("Source ID", '%1', TransferLine."Document No.");
                                reservationEntry.SetFilter("Item No.", '%1', TransferLine."Item No.");
                                if reservationEntry.FindSet() then begin

                                    trackingSpecification.SetCurrentKey("Lot No.");
                                    trackingSpecification.SetFilter("Lot No.", '%1', reservationEntry."Lot No.");
                                    if trackingSpecification.FindSet() then begin
                                        trackingSpecification.DeleteAll();
                                        reservationEntry.DeleteAll();
                                    end else
                                        reservationEntry.DeleteAll();

                                    if TransferLine."Outstanding Quantity" = TransferLine.Quantity then
                                        if TransferLine.Delete() then begin
                                            recCount += 1;
                                            Dialog.Update(4, 'True');
                                        end
                                end else
                                    if TransferLine.Delete() then begin
                                        recCount += 1;
                                        Dialog.Update(4, 'True');
                                    end
                            end;
                        end;
                    end else
                        if TransferLine.Delete() then begin
                            Dialog.Update(5, 'Short closed');
                            recCount += 1;
                            Dialog.Update(4, 'True');
                        end;
                until TransferLine.Next() = 0;
            end;
        end;

        Dialog.Close();
        if isCheckCount then Message('Total %1 records found', recCount) else Message('%1 Records cleared successfully', recCount);
    end;

    procedure clearComponentLine()
    begin
        prodComponent.SetCurrentKey("Prod. Order No.");
        prodComponent.SetFilter("Prod. Order No.", '<>%1', '');
        if prodComponent.FindSet() then
            repeat
                prodOrder.SetCurrentKey("No.");
                prodOrder.SetRange("No.", prodComponent."Prod. Order No.");
                if not prodOrder.FindFirst() then prodComponent.Delete();
            until prodComponent.Next() = 0;
        Message('Component Line Cleared Successfully');
    end;

    var
        prodOrder: Record "Production Order";
        prodComponent: Record "Prod. Order Component";
        trackingSpecification: Record "Tracking Specification";
        reservationEntry: Record "Reservation Entry";
        saleSetup: Record "Sales & Receivables Setup";
        itemList: list of [Text];
        item: Text;
        recCount: Integer;
        Dialog: Dialog;


    // EFG0069|FG0110|FG0014|FG0017|FG0026|FG0038|FG0041|FG0043|FG0145|FG0198|FG0204|FG0224|FG0292
    // |FG0299|FG0307|FG0320|FG0327|FG0024|FG0024|RM0116|RM0175|RM0041|RM0148|RM00014|RM00016|RM00018|RM00021|RM0020

    // EFG0051|EFG0066|EFG0069|EFG0091|EFG0097|EFG0098|EFG0108|EFG0110|EFG0136|EFG0137|EFG0138|GFG0014|GFG0017|GFG0020|GFG0021|
    // GFG0023|GFG0025|GFG0026|GFG0027|GFG0028|KFG0043|RFG0037|RFG0038|RFG0040|RFG0041|RFG0042|RFG0043|RFG0051|RFG0052|RFG0055|RFG0056|RFG0057|RFG0058|RFG0059|RFG0060|VFG0115|VFG0134
    // |VFG0145|VFG0150|VFG0171|VFG0172|VFG0173|VFG0198|VFG0203|VFG0204|
    // VFG0224|VFG0252|VFG0280|VFG0285|VFG0292|VFG0293|VFG0294|VFG0295|VFG0296|VFG0297|VFG0298|VFG0299|VFG0300|VFG0301|VFG0304|VFG0305|VFG0306|VFG0307|VFG0308|VFG0309|VFG0310|VFG0311
    // |VFG0312|VFG0313|VFG0314|VFG0315|VFG0316|VFG0317|VFG0318|VFG0319|
    // VFG0320|VFG0321|VFG0322|VFG0323|VFG0324|VFG0325|VFG0326|VFG0327|VFG0328|VFG0329|VFG0330|VFG0331|VFG0332|VFG0333|VFG0334|
    // YFG0024|YFG0027|YFG0029|YFG0048|YFG0049|YFG0058|YFG0069|YFG0070|YFG0071|YFG0072|YFG0073|YFG0074|YFG0076|YFG0077|YFG0078|YFG0079|YFG0080|YFG0081
    // |YFG0082|YFG0083|YFG0084|YFG0085|YFG0086|YFG0087|ZFG0016|ZFG0017|ZFG0024|ZFG0025|ZFG0026|EFG0069|FG0110|FG0014|FG0017|FG0026|FG0038|FG0041|FG0043|FG0145
    // |FG0198|FG0204|FG0224|FG0292|FG0299|FG0307|FG0320|FG0327|FG0024|FG0024|RM0116|RM0175|RM0041|RM0148|RM00014|RM00016|RM00018|RM00021|RM0020

    // EFG0069|EFG0110|GFG0014|GFG0017|GFG0026|RFG0038|RFG0041|RFG0043|VFG0145|VFG0198|VFG0204|VFG0224|VFG0292|VFG0299|VFG0307|VFG0320|VFG0327|YFG0024|ZFG0024

    procedure createSKUTemplate(var itemCode: Code[20])
    var
        SKU: Record "Stockkeeping Unit";
        itemLedgerEntry: Record "Item Ledger Entry";
    begin
        itemLedgerEntry.SetCurrentKey("Item No.");

    end;
}