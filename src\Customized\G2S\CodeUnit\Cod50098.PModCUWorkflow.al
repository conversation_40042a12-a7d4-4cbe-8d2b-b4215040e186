codeunit 50098 "PMOD Workflow Rqst Pg Handling"
{

    trigger OnRun()
    begin
    end;

    var
        PurchaseDocumentCodeTxt: Label 'PURCHMODDOC', Locked = true;
        PurchaseDocumentDescTxt: Label 'Purchase Document Modifier';

    procedure CreateEntitiesAndFields()
    begin
        InsertRequestPageEntities;
        InsertRequestPageFields;
        AssignEntitiesToWorkflowEvents();
    end;

    procedure AssignEntitiesToWorkflowEvents()
    begin
        AssignEntityToWorkflowEvent(DATABASE::"Purchase Doc Modifier", PurchaseDocumentCodeTxt);

        OnAfterAssignEntitiesToWorkflowEvents();
    end;

    local procedure InsertRequestPageEntities()
    begin
        InsertReqPageEntity(
          PurchaseDocumentCodeTxt, PurchaseDocumentDescTxt, DATABASE::"Purchase Doc Modifier", DATABASE::"Purchase Doc Modifier Line");

        OnAfterInsertRequestPageEntities();
    end;

    local procedure InsertReqPageEntity(Name: Code[20]; Description: Text[100]; TableId: Integer; RelatedTableId: Integer)
    begin
        if not FindReqPageEntity(Name, TableId, RelatedTableId) then
            CreateReqPageEntity(Name, Description, TableId, RelatedTableId);
    end;

    local procedure FindReqPageEntity(Name: Code[20]; TableId: Integer; RelatedTableId: Integer): Boolean
    var
        DynamicRequestPageEntity: Record "Dynamic Request Page Entity";
    begin
        DynamicRequestPageEntity.SetRange(Name, Name);
        DynamicRequestPageEntity.SetRange("Table ID", TableId);
        DynamicRequestPageEntity.SetRange("Related Table ID", RelatedTableId);
        exit(DynamicRequestPageEntity.FindFirst);
    end;

    local procedure CreateReqPageEntity(Name: Code[20]; Description: Text[100]; TableId: Integer; RelatedTableId: Integer)
    var
        DynamicRequestPageEntity: Record "Dynamic Request Page Entity";
    begin
        DynamicRequestPageEntity.Init();
        DynamicRequestPageEntity.Name := Name;
        DynamicRequestPageEntity.Description := Description;
        DynamicRequestPageEntity.Validate("Table ID", TableId);
        DynamicRequestPageEntity.Validate("Related Table ID", RelatedTableId);
        DynamicRequestPageEntity.Insert(true);
    end;

    local procedure InsertRequestPageFields()
    begin
        InsertPurchaseHeaderReqPageFields;
        InsertPurchaseLineReqPageFields;

        InsertApprovalEntryReqPageFields;

        OnAfterInsertRequestPageFields();
    end;


    local procedure InsertPurchaseHeaderReqPageFields()
    var
        PurchaseHeader: Record "Purchase Doc Modifier";
    begin
        InsertReqPageField(DATABASE::"Purchase Doc Modifier", PurchaseHeader.FieldNo("No."));
        InsertReqPageField(DATABASE::"Purchase Doc Modifier", PurchaseHeader.FieldNo(TableID));
    end;

    local procedure InsertPurchaseLineReqPageFields()
    var
        PurchaseLine: Record "Purchase Doc Modifier Line";
    begin
        InsertReqPageField(DATABASE::"Purchase Doc Modifier Line", PurchaseLine.FieldNo("Modifier Code"));
        InsertReqPageField(DATABASE::"Purchase Doc Modifier Line", PurchaseLine.FieldNo(TableID));
    end;

    local procedure InsertApprovalEntryReqPageFields()
    var
        ApprovalEntry: Record "Approval Entry";
    begin
        InsertReqPageField(DATABASE::"Approval Entry", ApprovalEntry.FieldNo("Pending Approvals"));
    end;

    local procedure InsertReqPageField(TableId: Integer; FieldId: Integer)
    var
        DynamicRequestPageField: Record "Dynamic Request Page Field";
    begin
        if not DynamicRequestPageField.Get(TableId, FieldId) then
            CreateReqPageField(TableId, FieldId);
    end;

    local procedure CreateReqPageField(TableId: Integer; FieldId: Integer)
    var
        DynamicRequestPageField: Record "Dynamic Request Page Field";
    begin
        DynamicRequestPageField.Init();
        DynamicRequestPageField.Validate("Table ID", TableId);
        DynamicRequestPageField.Validate("Field ID", FieldId);
        DynamicRequestPageField.Insert();
    end;

    local procedure AssignEntityToWorkflowEvent(TableID: Integer; DynamicReqPageEntityName: Code[20])
    var
        WorkflowEvent: Record "Workflow Event";
    begin
        WorkflowEvent.SetRange("Table ID", TableID);
        WorkflowEvent.SetFilter("Dynamic Req. Page Entity Name", '<>%1', DynamicReqPageEntityName);
        if not WorkflowEvent.IsEmpty then
            WorkflowEvent.ModifyAll("Dynamic Req. Page Entity Name", DynamicReqPageEntityName);
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterAssignEntitiesToWorkflowEvents()
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterInsertRequestPageEntities()
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterInsertRequestPageFields()
    begin
    end;

    // procedure clearSalesreturnLine(isCheckCount: Boolean)
    // var
    //     SalesHeader: Record "Sales Header";
    //     SalesLine: Record "Sales Line";
    //     reservationQtyZero: Decimal;
    //     dateRange: Date;
    //     dateRange2: Date;
    // begin
    //     recCount := 0;
    //     // Evaluate(dateRange, '01/06/25'); 01/09/2021
    //     saleSetup.Get();
    //     dateRange := saleSetup."Allow Document Deletion Before";
    //     saleSetup.TestField("Item filter");

    //     itemList := saleSetup."Item filter".Split('|');

    //     Dialog.Open('#1############\' + 'Working with record before #5############\' + 'Item: #2###########\' + 'Location: #3##############\' + 'Deleted: #4###########');
    //     Dialog.Update(1, 'Processing...........');

    //     recCount := 0;
    //     Dialog.Update(5, dateRange);
    //     foreach item in itemList do begin
    //         Dialog.Update(2, item);
    //         SalesLine.SetCurrentKey("No.");
    //         SalesLine.SetFilter("No.", '%1', item);
    //         if SalesLine.FindSet() then begin
    //             repeat
    //                 SalesHeader.SetCurrentKey("No.");
    //                 SalesHeader.SetRange("No.", SalesLine."Document No.");
    //                 if SalesHeader.FindFirst() then begin
    //                     if SalesHeader."Posting Date" <= dateRange then begin
    //                         Dialog.Update(3, SalesLine."Location Code");
    //                         if SalesLine.Quantity = 0 then begin
    //                             reservationEntry.SetCurrentKey("Source ID", "Item No.");
    //                             reservationEntry.SetFilter("Source ID", '%1', SalesLine."Document No.");
    //                             reservationEntry.SetFilter("Item No.", '%1', SalesLine."No.");
    //                             if not reservationEntry.FindSet() then begin
    //                                 if isCheckCount then begin
    //                                     recCount += 1;
    //                                     Dialog.Update(4, 'False');
    //                                 end else
    //                                     if SalesLine.Delete() then begin
    //                                         recCount += 1;
    //                                         Dialog.Update(4, 'True');
    //                                     end;
    //                             end else begin
    //                                 if isCheckCount then begin
    //                                     recCount += 1;
    //                                     Dialog.Update(4, 'False');
    //                                 end else begin
    //                                     trackingSpecification.SetCurrentKey("Lot No.");
    //                                     trackingSpecification.SetFilter("Lot No.", '%1', reservationEntry."Lot No.");
    //                                     if trackingSpecification.FindSet() then begin
    //                                         trackingSpecification.DeleteAll();
    //                                         reservationEntry.DeleteAll();
    //                                         if SalesLine.Delete() then begin
    //                                             recCount += 1;
    //                                             Dialog.Update(4, 'True');
    //                                         end;
    //                                     end else begin
    //                                         reservationEntry.DeleteAll();
    //                                         if SalesLine.Delete() then begin
    //                                             recCount += 1;
    //                                             Dialog.Update(4, 'True');
    //                                         end;
    //                                     end;
    //                                 end;
    //                             end;
    //                         end;
    //                     end;
    //                 end;
    //             until SalesLine.Next() = 0;
    //         end;
    //     end;

    //     Evaluate(dateRange, '01/09/2025');
    //     Evaluate(dateRange2, '03/09/2025');
    //     reservationEntry.Reset();
    //     reservationEntry.SetCurrentKey("Created By", "Creation Date");
    //     reservationEntry.SetFilter("Created By", '%1', 'AYODEJI');
    //     reservationEntry.SetFilter("Creation Date", '%1..%2', dateRange, dateRange2);
    //     reservationEntry.SetFilter(Description, '=%1', '');
    //     if reservationEntry.FindSet() then reservationEntry.DeleteAll();

    //     Dialog.Close();
    //     if isCheckCount then Message('Total %1 records found', recCount) else Message('%1 Records cleared successfully', recCount);
    // end;


    // procedure clearTransferLine(isCheckCount: Boolean)
    // var
    //     TransferHeader: Record "Transfer Header";
    //     TransferLine: Record "Transfer Line";
    // begin
    //     recCount := 0;
    //     saleSetup.Get();
    //     // saleSetup.TestField("Item filter");

    //     itemList := saleSetup."Item filter".Split('|');

    //     // If itemList.Count = 0 then begin
    //     //   
    //     // end;
    //     recCount := 0;

    //     foreach item in itemList do begin
    //         TransferLine.SetCurrentKey("Item No.");
    //         TransferLine.SetFilter("Item No.", '%1', item);
    //         Dialog.Open('#6############\' + 'Item: #1###########\' + 'Order Status: #5############\' + 'Location \' + 'from #2############## to #3#############\' + 'Deleted: #4###########');
    //         Dialog.Update(6, 'Processing...........');
    //         if TransferLine.FindSet() then begin
    //             //MT650702
    //             repeat
    //                 Dialog.Update(1, TransferLine."Item No.");
    //                 if TransferHeader.Get(TransferLine."Document No.") then begin
    //                     Dialog.Update(2, TransferHeader."Transfer-from Code");
    //                     Dialog.Update(3, TransferHeader."Transfer-to Code");
    //                     if (TransferHeader."order status" <> TransferHeader."order status"::"Short close") then begin //and (TransferHeader.Status = TransferHeader.Status::Open)
    //                         if isCheckCount then begin
    //                             recCount += 1;
    //                             Dialog.Update(5, TransferHeader."order status");
    //                             Dialog.Update(4, 'False');
    //                         end else begin
    //                             reservationEntry.SetCurrentKey("Source ID", "Item No.");
    //                             reservationEntry.SetFilter("Source ID", '%1', TransferLine."Document No.");
    //                             reservationEntry.SetFilter("Item No.", '%1', TransferLine."Item No.");
    //                             if reservationEntry.FindSet() then begin

    //                                 trackingSpecification.SetCurrentKey("Lot No.");
    //                                 trackingSpecification.SetFilter("Lot No.", '%1', reservationEntry."Lot No.");
    //                                 if trackingSpecification.FindSet() then begin
    //                                     trackingSpecification.DeleteAll();
    //                                     reservationEntry.DeleteAll();
    //                                 end else
    //                                     reservationEntry.DeleteAll();

    //                                 if TransferLine."Outstanding Quantity" = TransferLine.Quantity then
    //                                     if TransferLine.Delete() then begin
    //                                         recCount += 1;
    //                                         Dialog.Update(4, 'True');
    //                                     end
    //                             end else
    //                                 if TransferLine.Delete() then begin
    //                                     recCount += 1;
    //                                     Dialog.Update(4, 'True');
    //                                 end
    //                         end;
    //                     end;
    //                 end else
    //                     if TransferLine.Delete() then begin
    //                         Dialog.Update(5, 'Short closed');
    //                         recCount += 1;
    //                         Dialog.Update(4, 'True');
    //                     end;
    //             until TransferLine.Next() = 0;
    //         end;
    //     end;

    //     Dialog.Close();
    //     if isCheckCount then Message('Total %1 records found', recCount) else Message('%1 Records cleared successfully', recCount);
    // end;

    procedure clearComponentLine()
    begin
        prodComponent.SetCurrentKey("Prod. Order No.");
        prodComponent.SetFilter("Prod. Order No.", '<>%1', '');
        if prodComponent.FindSet() then
            repeat
                prodOrder.SetCurrentKey("No.");
                prodOrder.SetRange("No.", prodComponent."Prod. Order No.");
                if not prodOrder.FindFirst() then prodComponent.Delete();
            until prodComponent.Next() = 0;
        Message('Component Line Cleared Successfully');
    end;

    [EventSubscriber(ObjectType::Table, Database::"Reservation Entry", 'OnBeforeInsertEvent', '', false, false)]
    local procedure MyProcedure(var Rec: Record "Reservation Entry"; RunTrigger: Boolean)
    var
        item: Record Item;
    begin
        item.Get(Rec."Item No.");
    end;

    var
        prodOrder: Record "Production Order";
        prodComponent: Record "Prod. Order Component";
        trackingSpecification: Record "Tracking Specification";
        reservationEntry: Record "Reservation Entry";
        saleSetup: Record "Sales & Receivables Setup";
        itemList: list of [Text];
        item: Text;
        recCount: Integer;
        Dialog: Dialog;


    // EFG0069|EFG0110|GFG0014|GFG0017|GFG0026|RFG0038|RFG0041|RFG0043|VFG0145|VFG0198|VFG0204|VFG0224|VFG0292|VFG0299|VFG0307|VFG0320|VFG0327|YFG0024|ZFG0024

    /// <summary>
    /// Creates Stockkeeping Unit (SKU) cards for an item based on inventory balance from Item Ledger Entries.
    /// Only creates SKUs for locations where the item has non-zero inventory balance.
    /// Copies all planning parameters from the Item card to each SKU card created.
    /// Displays a message with inventory balance details and creation results.
    /// </summary>
    /// <param name="itemCode">The item number for which to create SKU cards</param>
    procedure createSKUTemplate(var itemCode: Code[20]): Text
    var
        VersionMgt: Codeunit VersionManagement;
        BomVersion: Record "Production BOM Version";
        itemLedgerEntry2: Record "Item Ledger Entry";
        items: Record Item;
        SKU: Record "Stockkeeping Unit";
        itemLedgerEntry: Record "Item Ledger Entry";
        location: Record Location;
        BOM: Record "Production BOM Header";
        BOMLine: Record "Production BOM Line";
        inventoryBalance: Decimal;
        locationCode: Code[20];
        LocationList: List of [Code[20]];
        locationCount: Integer;
        messageText: Text;
        skuCreatedCount: Integer;
        totalInventory: Decimal;
        baseSafetyStock: Decimal;
        ReaminingSafetyStock: Decimal;
        SKUSafetyStock: Decimal;
    begin
        Clear(SKU);
        Clear(BOM);
        Clear(items);
        Clear(Dialog);
        Clear(BOMLine);
        Clear(location);
        Clear(BomVersion);
        Clear(messageText);
        Clear(locationCode);
        Clear(LocationList);
        Clear(locationCount);
        Clear(totalInventory);
        Clear(baseSafetyStock);
        Clear(itemLedgerEntry);
        Clear(skuCreatedCount);
        Clear(itemLedgerEntry2);
        Clear(inventoryBalance);

        // Validate that the item exists
        if not items.Get(itemCode) then begin
            Message('Item %1 does not exist.', itemCode);
            exit;
        end;

        baseSafetyStock := items."Safety Stock Quantity";

        Dialog.Open('#1############\' + 'Item available at #2### Locations\' + 'SKU Created/Updated for #3###########' + '#4###########\');
        Dialog.Update(1, 'Processing...........');

        //Get all active location for item
        itemLedgerEntry2.SetCurrentKey("Item No.", "Location Code");
        itemLedgerEntry2.SetRange("Item No.", itemCode);
        if itemLedgerEntry2.FindSet() then begin
            repeat
                if not LocationList.Contains(itemLedgerEntry2."Location Code") then
                    if location.Get(itemLedgerEntry2."Location Code") then
                        if not location.Blocked then
                            LocationList.Add(itemLedgerEntry2."Location Code");
            until itemLedgerEntry2.Next() = 0;
            Dialog.Update(2, locationList.Count());
        end;

        skuCreatedCount := 0;
        totalInventory := 0;
        locationCount := LocationList.Count();

        while locationCount > 1 do begin
            locationCode := LocationList.Get(locationCount);
            // Get inventory balance for this location
            itemLedgerEntry.SetCurrentKey("Item No.", "Location Code");
            itemLedgerEntry.SetRange("Item No.", itemCode);
            itemLedgerEntry.SetRange("Location Code", locationCode);
            itemLedgerEntry.CalcSums(Quantity);
            inventoryBalance := itemLedgerEntry.Quantity;

            //Calculate Safety Stock Distribution for SKU cards
            if items."Item Category Code" = 'RM' then begin
                if inventoryBalance < items."Safety Stock Quantity" then begin
                    if locationCount = LocationList.Count() then begin
                        if inventoryBalance < 1000 then begin
                            SKUSafetyStock := baseSafetyStock * 0.2;
                            ReaminingSafetyStock := baseSafetyStock - SKUSafetyStock;
                        end else begin
                            SKUSafetyStock := baseSafetyStock * 0.01;
                            ReaminingSafetyStock := baseSafetyStock - SKUSafetyStock;
                        end;
                    end else begin
                        if locationCount = 1 then begin
                            SKUSafetyStock := ReaminingSafetyStock;
                            ReaminingSafetyStock := 0;
                        end else begin
                            if ReaminingSafetyStock > 0 then begin
                                if inventoryBalance < 1000 then begin
                                    SKUSafetyStock := baseSafetyStock * 0.1;
                                    ReaminingSafetyStock := ReaminingSafetyStock - SKUSafetyStock;
                                end else begin
                                    SKUSafetyStock := baseSafetyStock * 0.2;
                                    ReaminingSafetyStock := ReaminingSafetyStock - SKUSafetyStock;
                                end;
                            end else
                                SKUSafetyStock := 0;
                        end;
                    end;
                end;
            end;

            // Only process locations with inventory balance
            if inventoryBalance <> 0 then begin
                SKU.SetCurrentKey("Item No.", "Location Code");
                SKU.SetRange("Item No.", itemCode);
                SKU.SetRange("Location Code", locationCode);
                if not SKU.FindFirst() then begin
                    // Create new SKU
                    SKU.Init();
                    SKU."Item No." := itemCode;
                    SKU."Location Code" := locationCode;

                    // Copy planning parameters from Item to SKU
                    if items."Item Category Code" = 'RM' then
                        CopyPlanningParametersFromItem(SKU, items, SKUSafetyStock)
                    else
                        CopyPlanningParametersFromItem(SKU, items, items."Safety Stock Quantity");

                    // CopyPlanningParametersFromItem(SKU, items);

                    if SKU.Insert(true) then begin
                        skuCreatedCount += 1;
                        Dialog.Update(3, locationCode);
                    end;
                end else begin
                    // Update existing SKU with planning parameters
                    if items."Item Category Code" = 'RM' then
                        CopyPlanningParametersFromItem(SKU, items, SKUSafetyStock)
                    else
                        CopyPlanningParametersFromItem(SKU, items, items."Safety Stock Quantity");
                    // CopyPlanningParametersFromItem(SKU, items);
                    SKU.Modify(true);
                    Dialog.Update(3, locationCode);
                end;
            end;
            locationCount -= 1;
            totalInventory += inventoryBalance;
        end;

        //Create SKU for BOM items if any
        BOM.Reset();
        Bom.SetCurrentKey("No.");
        Bom.SetRange("No.", itemCode);
        if Bom.FindFirst() then begin
            Dialog.Close();
            BomVersion.SetRange("Version Code", VersionMgt.GetBOMVersion(BOM."No.", WorkDate, true));
            if BomVersion.FindFirst() then begin
                BOMLine.SetCurrentKey("Production BOM No.", "Version Code");
                BOMLine.SetRange("Production BOM No.", itemCode);
                BOMLine.SetRange("Version Code", BOM."Version Nos.");
                if BOMLine.FindSet() then
                    repeat
                        createSKUTemplate(BOMLine."No.");
                    until BOMLine.Next() = 0;
            end;
        end;

        messageText += '\Total Inventory: ' + Format(totalInventory);
        messageText += '\Total SKU Cards Created/Updated: ' + Format(skuCreatedCount);
        exit(messageText);
    end;

    local procedure CopyPlanningParametersFromItem(var SKU: Record "Stockkeeping Unit"; Item: Record Item; SKUSafetyStock: Decimal)
    begin
        // Copy planning parameters from Item to SKU
        SKU."Reorder Point" := Item."Reorder Point";
        SKU."Reorder Quantity" := Item."Reorder Quantity";
        SKU."Maximum Inventory" := Item."Maximum Inventory";
        // SKU."Safety Stock Quantity" := Item."Safety Stock Quantity";
        SKU."Safety Stock Quantity" := SKUSafetyStock;
        SKU."Minimum Order Quantity" := Item."Minimum Order Quantity";
        SKU."Maximum Order Quantity" := Item."Maximum Order Quantity";
        SKU."Order Multiple" := Item."Order Multiple";
        SKU."Safety Lead Time" := Item."Safety Lead Time";
        SKU."Lead Time Calculation" := Item."Lead Time Calculation";
        SKU."Replenishment System" := Item."Replenishment System";
        SKU."Lead Time Calculation" := Item."Lead Time Calculation";
        SKU."Reordering Policy" := Item."Reordering Policy";
        SKU."Include Inventory" := Item."Include Inventory";
        SKU."Manufacturing Policy" := Item."Manufacturing Policy";
        SKU."Rescheduling Period" := Item."Rescheduling Period";
        SKU."Lot Accumulation Period" := Item."Lot Accumulation Period";
        SKU."Dampener Period" := Item."Dampener Period";
        SKU."Dampener Quantity" := Item."Dampener Quantity";
        SKU."Overflow Level" := Item."Overflow Level";
        SKU."Time Bucket" := Item."Time Bucket";
    end;
}