pageextension 50068 WareHouseShipmntLines extends "Whse. Shipment Subform"
{
    layout
    {
        /*modify("Bin Code")//PKONDE16
        {
            //Editable = false;
        }*/
        addafter("Item No.")
        {
            field("Posted Loading Slip No."; "Posted Loading Slip No.")
            {
                ApplicationArea = ALL;
                trigger OnValidate()
                var
                    PostdLoadSlpGRec: record "Posted Loading Slip Line";
                BEGIN
                    /*
                    CurrPage.Update();
                    IF "Posted Loading Slip No." <> '' then BEGIN
                        PostdLoadSlpGRec.reset;
                        PostdLoadSlpGRec.SetRange("No.", "No.");
                        PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                        PostdLoadSlpGRec.SetRange("Document No.", "Posted Loading Slip No.");
                        IF PostdLoadSlpGRec.findfirst then BEGIN
                            "Posted Loading Slip Line No." := PostdLoadSlpGRec."Line No.";
                            VALIDATE("Qty. to Ship", PostdLoadSlpGRec."Qty. Loading");
                            message('%1...%2', "Qty. to Ship", PostdLoadSlpGRec."Qty. Loading")
                        end;
                    end else begin
                        Clear("Posted Loading Slip Line No.");
                        Validate("Qty. to Ship", 0);
                    end;
                    message('%1...Base %2', "Qty. to Ship", "Qty. to Ship (Base)")*/ //Prasanna om 23.12.2020
                end;
            }
            field("Posted Loading Slip Line No."; "Posted Loading Slip Line No.")
            {
                ApplicationArea = all;
            }
            //Fix 24Feb2021>>
            field("Ship to Address"; "Ship to Address")
            {

            }
            field("Ship to Address 2"; "Ship to Address 2")
            {

            }
            //Fix 24Feb2021<<

        }
        modify("Qty. to Ship")
        {
            Editable = QtyVisibleGVar;
            trigger OnBeforeValidate()
            BEGIN

                case "Source Document" of
                    "Source Document"::"Sales Order":
                        BEGIN
                            SalHdrGRec.reset;
                            SalHdrGRec.SetRange("No.", "Source No.");
                            IF SalHdrGRec.findfirst then begin
                                IF (SalHdrGRec."Loading Slip Required") THEN
                                    error('You cannot enter Qty to Ship manually Sale order is Loading Slip required.');
                            end
                        End;
                    "Source Document"::"Outbound Transfer":
                        BEGIN
                            TraHdrGRec.reset;
                            TraHdrGRec.SetRange("No.", "Source No.");
                            IF TraHdrGRec.findfirst then begin
                                IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then
                                    message('You cannot enter Qty to Ship Manually Transfer type is Branch Request.');
                            end;
                        END;
                end;

            END;

        }


    }
    actions
    {
        // Add changes to page actions here
        addafter(ItemTrackingLines)
        {

            action("Bin Reclassfication Jnl")
            {
                Caption = 'Bin Reclassfication Jnl';
                ApplicationArea = all;
                Image = Journal;
                trigger OnAction()
                var
                    Usersetup: Record "User Setup";
                begin
                    TestField("No.");
                    TestField(Quantity);
                    Usersetup.get(UserId);
                    IF Usersetup."Bin Reclassification" then
                        BinReclassJnlLine()
                    else
                        Error('You Dont have permissions to Open and Process Bin Reclassification.');
                end;
            }

            action("Bin Reclassfication")
            {
                Caption = 'Bin Reclassfication';
                ApplicationArea = all;
                Image = InwardEntry;
                trigger OnAction()
                var
                    Usersetup: Record "User Setup";
                begin
                    Usersetup.get(UserId);
                    IF Usersetup."Bin Reclassification" then
                        BinReclassfication
                    else
                        Error('You Dont have permissions to Open and Process Bin Reclassification.');
                    SendBinRecMai();
                end;



            }

        }
        //B2BMSOn09Sep21>>
        modify(ItemTrackingLines)
        {
            Visible = false;
        }
        addbefore(ItemTrackingLines)
        {
            action(ItemTrackingLinesCopy)
            {
                ApplicationArea = ItemTracking;
                Caption = 'Item &Tracking Lines';
                Image = ItemTrackingLines;
                ShortCutKey = 'Shift+Ctrl+I';
                ToolTip = 'View or edit serial numbers and lot numbers that are assigned to the item on the document or journal line.';

                trigger OnAction()
                begin
                    //OpenItemTrackingLines();
                    OpenItemTrackingLinesCopy();
                end;
            }
        }
        //B2BMSOn09Sep21


    }

    trigger OnAfterGetRecord()
    BEGIN
        VisibleQty;
    END;

    trigger OnAfterGetCurrRecord()

    BEGIN
        VisibleQty;
    END;

    trigger OnModifyRecord(): Boolean
    var
    BEGIN
        VisibleQty;
    END;

    Procedure VisibleQty()
    BEGIN
        QtyVisibleGVar := TRUE;
        case "Source Document" of
            "Source Document"::"Sales Order":
                BEGIN

                    SalHdrGRec.reset;
                    SalHdrGRec.SetRange("No.", "Source No.");
                    IF SalHdrGRec.findfirst then begin
                        IF (SalHdrGRec."Loading Slip Required") THEN
                            QtyVisibleGVar := false;
                    end
                End;
            "Source Document"::"Outbound Transfer":
                BEGIN
                    TraHdrGRec.reset;
                    TraHdrGRec.SetRange("No.", "Source No.");
                    IF TraHdrGRec.findfirst then begin
                        IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then
                            QtyVisibleGVar := FALSE;
                    end;
                END;
        end;
    END;

    procedure BinReclassfication()
    var
        WareHouseEntry: Record "Warehouse Entry";
        WareHouseEntry2: record "Warehouse Entry";
        ItemJnlLine2: Record "Item Journal Line";
        ItemJnlBatch: Record "Item Journal Batch";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        ILELotRec: Record "Item Ledger Entry";



        PrevBincode: Code[20];

        AvaliableQty: Decimal;
        LineQtyBase: Decimal;
        ReqQtyBase: Decimal;
        TotQtyBase: Decimal;
        BinQty: Decimal;

        ItemJnlBatchErr: Label 'you must specify No. series in journal template name % Journal bacth name %2';
        ItemJnlReclassMsg: Label 'There is nothing to process';
        ItemJnlMsg: Label 'Bin Reclassfication successfully';
        ItemJnlConfirm: Label 'Do you want to process reclassfication?';
        ReservEntry: Record "Reservation Entry";
        ItemJoulineLrec: Record "Item Journal Line";
    begin
        //B2BPKON260521>>
        BinReclassJnl.Reset;
        //BinReclassJnl.Setrange("Document Type", BinReclassJnl."Document Type"::Whsship);
        BinReclassJnl.Setrange("Document No.", Rec."No.");
        BinReclassJnl.Setrange("Document Line No.", Rec."Line No.");
        BinReclassJnl.Setrange("Bin Code", Rec."Bin Code");
        BinReclassJnl.SetFilter(Quantity, '>%1', 0);
        BinReclassJnl.Setfilter(Reclassfication, '%1', false);
        if BinReclassJnl.FindFIRST then
            Error('From Bin and To Bin Should not be same');
        //B2BPKON260521<<

        if Not Confirm(ItemJnlConfirm, False) then
            Exit;


        Clear(ItemJnlPostBatch);
        Clear(LineQtyBase);
        Clear(ReqQtyBase);
        Clear(TotQtyBase);
        clear(NextLineNo);
        CLEAR(NoSeriesMgt);
        clear(InsertedLine);
        clear(LotFilterText);


        ReservEntry.RESET;
        ReservEntry.SETCURRENTKEY(
        "Source ID", "Source Ref. No.", "Source Type", "Source Subtype", "Source Batch Name", "Source Prod. Order Line");
        ReservEntry.SETRANGE("Source ID", Rec."Source No.");
        ReservEntry.SETRANGE("Source Ref. No.", Rec."Source Line No.");
        Case "Source Document" of
            Rec."Source Document"::"Sales Order":
                begin
                    ReservEntry.SETRANGE("Source Type", DATABASE::"Sales Line");
                    ReservEntry.SETRANGE("Source Subtype", 1);
                end;
            Rec."Source Document"::"Outbound Transfer":
                begin
                    ReservEntry.SETRANGE("Source Type", DATABASE::"Transfer Line");
                    ReservEntry.SETRANGE("Source Subtype", 0);

                end;
        end;

        IF ReservEntry.FINDSET THEN
            ReservEntry.DELETEALL(TRUE);
        LineQtyBase := "Qty. to Ship (Base)";

        Case Rec."Source Document" of
            Rec."Source Document"::"Sales Order":
                begin
                    SalesHeader.Get(SalesHeader."Document Type"::Order, rec."Source No.");
                    Customer.Get(SalesHeader."Sell-to Customer No.");
                end;
            Rec."Source Document"::"Outbound Transfer":
                begin
                    TransferHeader.get(rec."Source No.");
                    Location.get(TransferHeader."Transfer-from Code");
                end;
        end;

        ItemJnlLine2.Reset;
        ItemJnlLine2.SETRANGE("Journal Template Name", 'RECLASS');
        ItemJnlLine2.SETRANGE("Journal Batch Name", 'DEFAULT');
        if ItemJnlLine2.FindFirst() then
            ItemJnlLine2.DeleteAll();

        ItemJnlBatch.GET('RECLASS', 'DEFAULT');
        IF ItemJnlBatch."No. Series" = '' THEN
            Error(ItemJnlBatchErr);
        NextDocNo := NoSeriesMgt.GetNextNo(ItemJnlBatch."No. Series", workdate, FALSE);

        ILELotRec.reset;
        ILELotRec.SetCurrentKey("Item No.", "Location Code", "Expiration Date");
        case "Source Document" of
            Rec."Source Document"::"Sales Order":
                begin
                    if Customer."Batch Assign" = Customer."Batch Assign"::LEFO then
                        ILELotRec.SetAscending("Expiration Date", false);
                end;
            Rec."Source Document"::"Outbound Transfer":
                begin
                    if Location."Batch Assign" = Location."Batch Assign"::LEFO then
                        ILELotRec.SetAscending("Expiration Date", False);
                end;

        end;
        ILELotRec.SETRANGE("Location Code", "Location Code");
        ILELotRec.SETRANGE("Item No.", "Item No.");
        ILELotRec.SETRANGE("Variant Code", "Variant Code");
        ILELotRec.SETfilter(Open, '%1', true);
        ilelotrec.SetFilter("Lot No.", '<>%1', '');
        IF ILELotRec.Findset THEN begin
            repeat
                clear(AvaliableQty);
                AvaliableQty := ILELotRec."Remaining Quantity";



                if LineQtyBase <= AvaliableQty then begin
                    ReqQtyBase := LineQtyBase;
                    LineQtyBase := 0;

                    InsertReclassJnl(ILELotRec, ReqQtyBase);
                end else begin
                    ReqQtyBase := AvaliableQty;
                    LineQtyBase -= AvaliableQty;
                    InsertReclassJnl(ILELotRec, ReqQtyBase);
                end;
                if LotFilterText <> '' then
                    LotFilterText += '|' + ILELotRec."Lot No."
                else
                    LotFilterText := ILELotRec."Lot No.";


            until (ILELotRec.Next = 0) or (LineQtyBase = 0);
            if Insertedline then begin
                CLEAR(ItemJoulineLrec);
                ItemJoulineLrec.RESET;
                ItemJoulineLrec.SETRANGE("Journal Template Name", 'RECLASS');
                ItemJoulineLrec.SETRANGE("Journal Batch Name", 'DEFAULT');
                if ItemJoulineLrec.FindSET() then
                    ItemJnlPostBatch.RUN(ItemJoulineLrec);//PKONAU25
                //ItemJnlPostBatch.RUN(ItemJnlLine);//PKONAU25
                //AssignItemTracking();

            end else
                Message(ItemJnlReclassMsg);

        end;

    end;

    procedure InsertReclassJnl(var ItemLedgerEntryParRec: Record "Item Ledger Entry"; ReqQtyBasePar: decimal)
    var

        ItemUOM: Record "Item Unit of Measure";
        ReclassQtyBase: Decimal;
    begin
        Clear(ReclassQtyBase);
        BinReclassJnl.Reset;
        BinReclassJnl.Setrange("Document Type", BinReclassJnl."Document Type"::Whsship);
        BinReclassJnl.Setrange("Document No.", Rec."No.");
        BinReclassJnl.Setrange("Document Line No.", Rec."Line No.");
        BinReclassJnl.SetFilter(Quantity, '>%1', 0);
        BinReclassJnl.Setfilter(Reclassfication, '%1', false);
        if BinReclassJnl.FindSet then begin
            repeat


                Clear(ReclassQtyBase);
                if ReqQtyBasePar < (BinReclassJnl."Qty.(Base)" - BinReclassJnl."Qty.Reclassfied Base") then begin
                    ReclassQtyBase := ReqQtyBasePar;
                    ReqQtyBasePar := 0;
                    BinReclassJnl."Qty.Reclassfied Base" += ReclassQtyBase;
                    BinReclassJnl.Modify;

                end else begin
                    ReclassQtyBase := (BinReclassJnl."Qty.(Base)" - BinReclassJnl."Qty.Reclassfied Base");
                    ReqQtyBasePar -= ReclassQtyBase;
                    BinReclassJnl."Qty.Reclassfied Base" += ReclassQtyBase;
                    BinReclassJnl.Reclassfication := true;
                    BinReclassJnl.Modify;

                end;
                NextLineNo := NextLineNo + 10000;
                ItemUOM.Get(Rec."Item No.", Rec."Unit of Measure Code");
                ItemJnlLine.INIT();
                ItemJnlLine."Journal Template Name" := 'RECLASS';
                ItemJnlLine."Journal Batch Name" := 'DEFAULT';
                ItemJnlLine."Posting Date" := WORKDATE();
                ItemJnlLine."Document Date" := WORKDATE();
                ItemJnlLine."Document No." := NextDocNo;
                ItemJnlLine."Line No." := NextLineNo;
                ItemJnlLine."Entry Type" := ItemJnlLine."Entry Type"::Transfer;
                ItemJnlLine.VALIDATE("Item No.", ItemLedgerEntryParRec."Item No.");

                ItemJnlLine.VALIDATE("Variant Code", ItemLedgerEntryParRec."Variant Code");
                ItemJnlLine.VALIDATE("Location Code", ItemLedgerEntryParRec."Location Code");
                ItemJnlLine.VALIDATE("Unit of Measure Code", REC."Unit of Measure Code");
                ItemJnlLine.validate(Quantity, ROUND(ReclassQtyBase / ItemUOM."Qty. per Unit of Measure", 0.00001));
                ItemJnlLine.validate("Quantity (Base)", ReclassQtyBase);


                ItemJnlLine."Bin Code" := BinReclassJnl."Bin Code";
                ItemJnlLine."New Bin Code" := Rec."Bin Code";
                ItemJnlLine."Warranty Date" := ItemLedgerEntryParRec."Warranty Date";
                ItemJnlLine."Expiration Date" := ItemLedgerEntryParRec."Expiration Date";
                ItemJnlLine.INSERT();
                CreateDime(ItemJnlLine, ItemLedgerEntryParRec);//PKONAU12
                ItemJnlLine.Modify();//PKONAU12
                UpdateResEntry(ItemJnlLine, ItemLedgerEntryParRec."Lot No.");


                Insertedline := true;

            Until (BinReclassJnl.Next = 0) or (ReqQtyBasePar = 0);
        end;


    END;

    procedure CreateDime(Var IJLDim: Record "Item Journal Line"; ILEQt: Record "Item Ledger Entry")
    var
        TrackingSpecLv: Record "Tracking Specification";
        ReservationEntry: Record "Reservation Entry";
    begin
        IJLDim.Validate("Dimension Set ID", ILEQt."Dimension Set ID");
        IJLDim."Shortcut Dimension 1 Code" := ILEQt."Global Dimension 1 Code";
        IJLDim."Shortcut Dimension 2 Code" := ILEQt."Global Dimension 2 Code";
        IJLDim.Validate("New Dimension Set ID", ILEQt."Dimension Set ID");
        IJLDim."New Shortcut Dimension 1 Code" := ILEQt."Global Dimension 1 Code";
        IJLDim."New Shortcut Dimension 2 Code" := ILEQt."Global Dimension 2 Code";
    end;



    procedure AssignItemTracking()
    var


        ItemLedgerEntry: Record "Item Ledger Entry";
        ReservationEntry: record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";

        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;

        WhseTrackErr: Label 'Sufficient quantity not exist in Bin code %1 .';
        LotNumErr: Label 'Item Ledger entries not exist in item lot combination';

        LineInserted: Boolean;
        TrackingAssigned: Label 'Tacking Assigned successfully.';
        TransferLine: Record "Transfer Line";
    Begin

        Testfield("Qty. to Ship (Base)");

        LineQty := "Qty. to Ship (Base)";



        if LotFilterText = '' then
            Error(LotNumErr);
        ReservationEntry2.RESET;
        IF ReservationEntry2.FINDLAST THEN
            EntryNum := ReservationEntry2."Entry No." + 1
        ELSE
            EntryNum := 1;
        ItemLedgerEntry.RESET;
        ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
        Case Rec."Source Document" of
            Rec."Source Document"::"Sales Order":
                begin
                    if Customer."Batch Assign" = Customer."Batch Assign"::LEFO then
                        ItemLedgerEntry.SetAscending("Expiration Date", false);
                end;

            Rec."Source Document"::"OutBound Transfer":
                begin
                    if Location."Batch Assign" = Location."Batch Assign"::LEFO then
                        ItemLedgerEntry.SetAscending("Expiration Date", False);

                end;

        end;


        ItemLedgerEntry.SETRANGE("Item No.", Rec."Item No.");
        ItemLedgerEntry.SETRANGE("Location Code", Rec."Location Code");
        ItemLedgerEntry.SETRANGE("Variant Code", Rec."Variant Code");
        ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
        ItemLedgerEntry.SETFILTER("Lot No.", LotFilterText);
        IF ItemLedgerEntry.FINDSET THEN BEGIN

            REPEAT

                IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                    AssignQty := LineQty;
                    LineQty := 0;
                END ELSE BEGIN
                    AssignQty := (ItemLedgerEntry."Remaining Quantity");
                    LineQty -= AssignQty;
                END;


                ReservationEntry.INIT;
                ReservationEntry."Entry No." := EntryNum;
                ReservationEntry.VALIDATE(Positive, FALSE);
                ReservationEntry.VALIDATE("Item No.", Rec."Item No.");
                ReservationEntry.VALIDATE("Location Code", Rec."Location Code");
                ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / Rec."Qty. per Unit of Measure"));
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                Case Rec."Source Document" of
                    rec."Source Document"::"Sales Order":
                        begin
                            ReservationEntry.VALIDATE("Source Type", DATABASE::"Sales line");
                            ReservationEntry.VALIDATE("Source Subtype", 1);
                        end;
                    rec."Source Document"::"OutBound Transfer":
                        begin
                            ReservationEntry.VALIDATE("Source Type", DATABASE::"Transfer line");
                            ReservationEntry.VALIDATE("Source Subtype", 0);
                        end;
                end;

                ReservationEntry.VALIDATE("Source ID", rec."Source no.");

                ReservationEntry.VALIDATE("Source Ref. No.", Rec."source Line No.");
                ReservationEntry.VALIDATE("Shipment Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                ReservationEntry."Created By" := USERID;
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.INSERT;
                EntryNum += 1;
                LineInserted := true;

                if "Source Document" = "Source Document"::"Outbound Transfer" then begin
                    TransferLine.Get("Source No.", "Source Line No.");
                    ReservationEntry.INIT;
                    ReservationEntry."Entry No." := EntryNum;
                    ReservationEntry.VALIDATE(Positive, true);
                    ReservationEntry.VALIDATE("Item No.", Rec."Item No.");
                    ReservationEntry.VALIDATE("Location Code", TransferLine."Transfer-to Code");
                    ReservationEntry.VALIDATE("Quantity (Base)", AssignQty);
                    ReservationEntry.VALIDATE(Quantity, ROUND(AssignQty / Rec."Qty. per Unit of Measure"));
                    ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                    ReservationEntry.VALIDATE("Creation Date", WorkDate());
                    ReservationEntry.VALIDATE("Source Type", DATABASE::"Transfer line");
                    ReservationEntry.VALIDATE("Source Subtype", 1);
                    ReservationEntry.VALIDATE("Source ID", TransferLine."Document No.");

                    ReservationEntry.VALIDATE("Source Ref. No.", TransferLine."Line No.");
                    ReservationEntry.VALIDATE("Expected Receipt Date", WorkDate());
                    ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                    ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                    ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                    ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                    ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                    ReservationEntry."Created By" := USERID;
                    ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                    ReservationEntry.VALIDATE(Correction, FALSE);
                    ReservationEntry.INSERT;
                    EntryNum += 1;
                end;

            UNTIL (ItemLedgerEntry.NEXT = 0) OR (LineQty = 0);

        END;

        if LineInserted then
            Message(TrackingAssigned)
    END;

    procedure UpdateResEntry(VAR ItemJournalLineParRec2: Record "Item Journal Line"; LotCodePar: Code[20]);
    Var
        ReservationEntry: Record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";
        EntryNum: Integer;

    begin
        IF ReservationEntry2.FINDlast() THEN
            EntryNum := ReservationEntry2."Entry No."
        ELSE
            EntryNum := 0;
        ReservationEntry.INIT();
        ReservationEntry."Entry No." := EntryNum + 1;
        ReservationEntry.VALIDATE(Positive, FALSE);
        ReservationEntry.VALIDATE("Item No.", ItemJournalLineParRec2."Item No.");
        ReservationEntry.VALIDATE("Location Code", ItemJournalLineParRec2."Location Code");
        ReservationEntry.VALIDATE("variant Code", ItemJournalLineParRec2."Variant Code");

        ReservationEntry.VALIDATE("Quantity (Base)", -ItemJournalLineParRec2."Quantity (Base)");
        ReservationEntry.VALIDATE(Quantity, -ItemJournalLineParRec2.Quantity);
        ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Prospect);
        ReservationEntry.VALIDATE("Creation Date", ItemJournalLineParRec2."Posting Date");
        ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
        ReservationEntry.VALIDATE("Source Subtype", 4);
        ReservationEntry.VALIDATE("Source ID", ItemJournalLineParRec2."Journal Template Name");
        ReservationEntry.VALIDATE("Source Batch Name", ItemJournalLineParRec2."Journal Batch Name");
        ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLineParRec2."Line No.");
        ReservationEntry.VALIDATE("Shipment Date", ItemJournalLineParRec2."Posting Date");

        ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
        ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);

        ReservationEntry.VALIDATE("Lot No.", LotCodePar);
        ReservationEntry.VALIDATE("New Lot No.", LotCodePar);
        ReservationEntry.VALIDATE("Expiration Date", ItemJournalLineParRec2."Expiration Date");
        //PKONJ25.2
        IF ItemJournalLineParRec2."Expiration Date" <> 0D THEN
            ReservationEntry.VALIDATE("New Expiration Date", ItemJournalLineParRec2."Expiration Date")
        else begin
            ReservationEntry.VALIDATE("Expiration Date", WorkDate());
            ReservationEntry.VALIDATE("New Expiration Date", CalcDate('6M', Today));
        end;
        //PKONJ25.2
        ReservationEntry.VALIDATE(Correction, FALSE);
        ReservationEntry.INSERT();



    end;

    procedure BinReclassJnlLine()
    var

        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        BinReclassJnlLines: Page "Bin Reclassfication Jnl";

        TransferLine: Record "Transfer Line";
        SalesLine: Record "Sales Line";
    begin
        Clear(BinReclassJnlLines);
        BinReclassJnl2.SetRange("Document Type", BinReclassJnl2."Document Type"::WhsShip);
        BinReclassJnl2.SetRange("Document No.", "No.");
        BinReclassJnl2.SetRange("Document Line No.", "Line No.");

        BinReclassJnlLines.SetSourceValues(BinReclassJnl2."Document Type"::WhsShip, SalesLine, TransferLine, Rec);
        BinReclassJnlLines.SetTableView(BinReclassJnl2);
        BinReclassJnlLines.RunModal;
    end;

    procedure SendBinRecMai()
    var
        InvSetup: record "Inventory Setup";
        ItemLedgerEntry: record "Item Ledger Entry";
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
    begin
        IF InvSetup.get() And (InvSetup."Bin Reclass Mail Alert ID" <> '') then begin
            SMTPMailSetup.get();
            SenderAddr := SMTPMailSetup."User ID";
            BinReclassJnl2.RESET();
            BinReclassJnl2.SetRange("Document No.", "No.");
            BinReclassJnl2.SetRange("Document Line No.", "Line No.");
            IF BinReclassJnl2.FINDFIRST THEN begin
                RecepientAddr.Add(InvSetup."Bin Reclass Mail Alert ID");
                if InvSetup."Bin Reclass Mail Alert ID2" <> '' then
                    RecepientAddr.Add(InvSetup."Bin Reclass Mail Alert ID2");
                SubjectTxt := 'Bin Reclassification Posted For  -' + FORMAT(BinReclassJnl2."Document No.") + '-' + FORMAT(BinReclassJnl2."Line No.") + ' On ' + FORMAT(WorkDate());
                SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
                SMTPMail.AppendBody(CreateEmailBodyVend(BinReclassJnl2));
                SMTPMAil.send;
            end;
        end;
    end;

    Local procedure CreateEmailBodyVend(BinReclassJnl2Lpa: Record "Bin Reclassfication Jnl") EmailBodyText: Text
    var
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        Doc: Text;
        Lot: Text;
    begin
        BinReclassJnl2.Reset();
        BinReclassJnl2.SetRange("Document No.", BinReclassJnl2Lpa."Document No.");
        BinReclassJnl2.SetRange("Line No.", BinReclassJnl2Lpa."Line No.");
        IF BinReclassJnl2.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Document No.-' + BinReclassJnl2."Document No.");
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Line No.-' + FORMAT(BinReclassJnl2Lpa."Line No."));
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Date-' + FORMAT(WorkDate()));
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'UserID-' + USERID);
            EmailBodyText += '<tr>';
            EmailBodyText += '</tr>';
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            //EmailBodyText += StrSubstNo('<td>%1</td>', 'Variant Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Location Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'From Bin Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'To Bin Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Order No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Lot Details');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Item No.");
                //EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Variant Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Location Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Bin Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', "Bin Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2.Quantity);
                GetLotdetails(BinReclassJnl2, Doc, Lot);
                EmailBodyText += StrSubstNo('<td>%1</td>', Doc);
                EmailBodyText += StrSubstNo('<td>%1</td>', Lot);
                EmailBodyText += '</tr>';
            until BinReclassJnl2.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    procedure GetLotdetails(BinReclassJnl2LP: Record "Bin Reclassfication Jnl"; var Docno: text; var LotNoGVar: text)
    var
        WarehouseLine: Record "Warehouse Shipment Line";
        SalesLine: Record "Sales Line";
        ReservAtionGRec: Record "Reservation Entry";
        TransferLine: Record "Transfer Line";
    begin
        Clear(Docno);
        Clear(LotNoGVar);
        IF BinReclassJnl2LP."Document Type" = BinReclassJnl2LP."Document Type"::Sale then begin
            SalesLine.Reset();
            SalesLine.SetRange("Document Type", SalesLine."Document Type"::Order);
            SalesLine.SetRange("Document No.", BinReclassJnl2LP."Document No.");
            SalesLine.SetRange("Line No.", BinReclassJnl2LP."Document Line No.");
            if SalesLine.FindFirst() then;

            WarehouseLine.Reset();
            WarehouseLine.SetRange("No.", BinReclassJnl2LP."Document No.");
            WarehouseLine.SetRange("Line No.", BinReclassJnl2LP."Document Line No.");
            if WarehouseLine.FindFirst() then
                Docno := WarehouseLine."Source No.";

            ReservAtionGRec.Reset();
            ReservAtionGRec.SetRange("Source ID", SalesLine."Document No.");
            ReservAtionGRec.SetRange("Source Ref. No.", SalesLine."Line No.");
            if ReservAtionGRec.Findset then
                repeat
                    LotNoGVar := LotNoGVar + ' ' + ReservAtionGRec."Lot No.";
                until ReservAtionGRec.Next = 0;

        end else
            if BinReclassJnl2LP."Document Type" = BinReclassJnl2LP."Document Type"::Transfer then begin
                TransferLine.Reset();
                TransferLine.SetRange("Document No.", BinReclassJnl2LP."Document No.");
                TransferLine.SetRange("Line No.", BinReclassJnl2LP."Document Line No.");
                if TransferLine.FindFirst() then;

                ReservAtionGRec.Reset();
                ReservAtionGRec.SetRange("Source ID", TransferLine."Document No.");
                //ReservAtionGRec.SetRange("Source Ref. No.", TransferLine."Line No.");
                ReservAtionGRec.SetRange("Item No.", TransferLine."Item No.");
                ReservAtionGRec.SetRange("Location Code", TransferLine."Transfer-from Code");
                if ReservAtionGRec.Findset then
                    repeat
                        LotNoGVar := LotNoGVar + ' ' + ReservAtionGRec."Lot No.";
                    until ReservAtionGRec.Next = 0;
                Docno := TransferLine."Document No.";
            end else
                if BinReclassJnl2LP."Document Type" = BinReclassJnl2LP."Document Type"::WhsShip then begin
                    WarehouseLine.Reset();
                    WarehouseLine.SetRange("No.", BinReclassJnl2LP."Document No.");
                    WarehouseLine.SetRange("Line No.", BinReclassJnl2LP."Document Line No.");
                    if WarehouseLine.FindFirst() then
                        Docno := WarehouseLine."Source No.";
                    ReservAtionGRec.Reset();
                    ReservAtionGRec.SetRange("Source ID", WarehouseLine."Source No.");
                    //ReservAtionGRec.SetRange("Source Ref. No.", TransferLine."Line No.");
                    ReservAtionGRec.SetRange("Item No.", WarehouseLine."Item No.");
                    ReservAtionGRec.SetRange("Location Code", WarehouseLine."Location Code");
                    if ReservAtionGRec.Findset then
                        repeat
                            LotNoGVar := LotNoGVar + ' ' + ReservAtionGRec."Lot No.";
                        until ReservAtionGRec.Next = 0;
                end;
    end;










    Var
        QtyVisibleGVar: Boolean;
        SalHdrGRec: Record "Sales Header";
        TraHdrGRec: Record "Transfer Header";
        BinReclassJnl: Record "Bin Reclassfication Jnl";

        InsertedLine: Boolean;
        NextLineNo: Integer;
        NextDocNo: code[20];
        ItemJnlLine: Record "Item Journal Line";
        SalesHeader: Record "Sales Header";
        Customer: Record Customer;

        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
        LotFilterText: Text;
        TransferHeader: Record "Transfer Header";
        Location: Record Location;

}