page 50753 "Cust Cr Limit Schedule list"
{
    ApplicationArea = All;
    Caption = 'Cust Cr Limit Schedule list';
    PageType = List;
    SourceTable = "Cust. Cr. Limit Schedule";
    CardPageId = "Cust. Cr. Limit Schedule";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                }
                field("Customer name"; "Customer name")
                {
                    ApplicationArea = all;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Customer Type"; "Customer Type")
                {
                    ApplicationArea = all;
                }
                field("Schedule Expiry Period"; "Schedule Expiry Period")
                {
                    ApplicationArea = all;
                }
                field("Schedule Limit Expiry Date"; "Schedule Limit Expiry Date")
                {
                    ApplicationArea = all;
                }
                field("Credit Limit"; "Credit Limit")
                {
                    ApplicationArea = all;
                }
                field("Credit Limit(LCY)"; "Credit Limit(LCY)")
                {
                    ApplicationArea = all;
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Customer Credit Type"; "Customer Credit Type")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
    }
}
