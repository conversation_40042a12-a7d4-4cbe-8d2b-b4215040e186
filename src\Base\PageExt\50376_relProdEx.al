/// <summary>
/// PageExtension RpExt (ID 50376) extends Record Released Production Orders //PKONJU8.
/// </summary>
pageextension 50376 RpExt extends "Released Production Orders" //PKONJU8
{
    layout
    {
        addafter("No.")
        {
            field("External Document No."; "External Document No.")
            {
                ApplicationArea = all;
            }
        }
        addafter(Quantity)
        {
            field("Production Batch No."; "Production Batch No.")
            {
                ApplicationArea = All;
            }
            field("Finished Quantity"; "Finished Quantity")
            {
                ApplicationArea = All;

            }
            field("Remaining Quantity"; "Remaining Quantity")
            {
                ApplicationArea = All;

            }
            // >>>>>> G2S 04/12/2024  CAS-01377-X2T5S4
            field("Transfered Quantity"; "Transfered Quantity")
            {
                ApplicationArea = All;
            }
        }
        addafter("Search Description")
        {
            field("Line No."; "Line No.")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        // Add changes to page actions here
        addafter("Production Order Statistics")
        {
            action(GenerateBarcodeLabel)
            {
                Caption = 'Barcode label reprint';
                Image = "Report";
                Promoted = true;
                PromotedCategory = "Report";
                RunObject = Report "Barcode Report Custom-LP";
            }
        }
    }

    var
        myInt: Integer;
        CustomSetup: Record "Custom Setup";

    //g2s
    trigger OnAfterGetRecord()
    var
        myInt: Integer;
        ProdOrderLineGet: Record "Prod. Order Line";
    begin
        //030424 material availability relaxation
        CustomSetup.Reset;
        CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
        if not CustomSetup.FindFirst() then exit;
        if CustomSetup."Validate Material Availability" then begin
            If (Rec."Error Exists on Refresh?" = true) then begin
                ProdOrderLineGet.Reset();
                ProdOrderLineGet.SetRange("Prod. Order No.", Rec."No.");
                //G2S 110324 start 
                ProdOrderLineGet.SetRange("Finished Quantity", 0);
                //G2S 110324 end
                ProdOrderLineGet.DeleteAll();
            end;
        end;
    end;
    //g2s

    //<<<<<< G2S CAS-01312-L5Q5B8 6/21/2024
    trigger OnDeleteRecord(): Boolean
    var
        UserSetup: Record "User Setup";
        ProdOrderLine: Record "Prod. Order Line";
    begin
        UserSetup.SetRange("User ID", UserId);
        if UserSetup.FindFirst() then begin
            if not UserSetup."Delete Production Order" then begin
                Error('You do not have permmission to delete this Order!');
            end else begin
                ProdOrderLine.SetRange("Prod. Order No.", "No.");
                ProdOrderLine.SetRange("Item No.", "Source No.");
                if ProdOrderLine.FindFirst() then begin
                    if ProdOrderLine."Finished Quantity" <> 0 then Error('Your can not delete a Production order with Entires!');
                end;
            end;
        end else
            Error('User Not Found');
    end;
    //>>>>>> G2S CAS-01312-L5Q5B8 6/21/2024
}