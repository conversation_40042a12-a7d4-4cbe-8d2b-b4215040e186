pageextension 50004 LocExt5703 extends "Location Card"
{
    ////B2B.P.K.T whole page format changed.
    layout
    {
        addafter("Bin Policies")
        {
            group("Numbering")
            {
                field("Posted Invoice Nos."; "Pur. Posted Invoice Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Credit Memo Nos."; "Pur. Posted Credit Memo Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Receipt Nos."; "Pur. Posted Receipt Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Return Shpt. Nos."; "Pur.Posted Return Shpt. Nos.")
                {
                    ApplicationArea = all;
                }
                field("Whse. Ship Nos."; "Whse. Ship Nos.")
                {
                    ApplicationArea = all;

                }
                field("Posted Whse. Ship Nos."; "Posted Whse. Ship Nos.")
                {
                    ApplicationArea = all;
                }
                field("Whse. Rcpt Nos."; "Whse. Rcpt Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Whse. Rcpt Nos."; "Posted Whse. Rcpt Nos.")
                {
                    ApplicationArea = all;
                }
                field("Sal. Posted Invoice Nos."; "Sal. Posted Invoice Nos.")
                {
                    ApplicationArea = all;
                }
                field("Sal. Posted Credit Memo Nos."; "Sal. Posted Credit Memo Nos.")
                {
                    ApplicationArea = all;
                }
                field("Sal. Posted Shipment Nos."; "Sal. Posted Shipment Nos.")
                {
                    ApplicationArea = all;
                }
                field("Sal.Posted Prepmt.Cr.Memo Nos."; "Sal.Posted Return Receipt Nos.")
                {
                    ApplicationArea = all;
                }
                field("Return Journal Nos."; "Return Journal Nos.")
                {
                    ApplicationArea = all;
                }
                field("Branch Request No Series"; "Branch Request No Series")
                {
                    ApplicationArea = all;
                }
                field("SCD Approved Email 1"; "SCD Approved Email 1")
                {
                    ApplicationArea = all;
                }
                field("SCD Approved Email 2"; "SCD Approved Email 2")
                {
                    ApplicationArea = all;
                }
                field("SCD Approved Email 3"; "SCD Approved Email 3")
                {
                    ApplicationArea = all;
                }
            }
        }
        addafter("Use As In-Transit")
        {
            field("Exclude for SNOP"; "Exclude for SNOP")
            {
                ApplicationArea = all;
            }
            field(Active; Active)
            {
                ApplicationArea = all;
            }
            field("Bad Location"; "Bad Location")
            {
                Caption = 'Use as Bad Location';
                ApplicationArea = all;
            }
            field("Production Location"; "Production Location")
            {
                Caption = 'Use as Production Location';
                ApplicationArea = all;
            }
            field("Accounting location"; "Accounting location")
            {
                ApplicationArea = all;
            }
            field("Invoicing location"; "Invoicing location")
            {
                ApplicationArea = all;
            }
            field("Location Classification"; "Location Classification")
            {
                ApplicationArea = all;
            }
            field("Batch Assign"; "Batch Assign")
            {
                ApplicationArea = All;
            }
            field("Ho ware house"; "Ho ware house")
            {
                ApplicationArea = all;
            }
            field("SNOP Location Capacity"; "SNOP Location Capacity")
            {
                ApplicationArea = all;
            }
            field("Transport Location"; "Transport Location")
            {
                ApplicationArea = ALL;
            }
            field("Rejection Location"; "Rejection Location")
            {
                ApplicationArea = ALL;
            }
            field("Shortage Location"; "Shortage Location")
            {
                ApplicationArea = ALL;
            }

            field("Subcontractor No."; "Subcontractor No.")
            {
                ApplicationArea = ALL;
            }
            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = all;
            }
            field(Shopfloor; Shopfloor)
            {
                ApplicationArea = all;
            }
            field(Blocked; Blocked)
            {
                ApplicationArea = all;
            }
            field("Direct SO Location"; "Direct SO Location")
            {
                ApplicationArea = all;
                //b2bpksalecorr11
            }
        }
        addafter("E-Mail")
        {
            field("E-Mail2"; "E-Mail2")
            {
                ApplicationArea = all;
            }
            field("E-Mail3"; "E-Mail3")
            {
                ApplicationArea = all;
            }
            //Baluon11Jul2022>>
            field("Upcountry Warehouse"; "Upcountry Warehouse")
            {
                ApplicationArea = all;
            }
            //Baluon11Jul2022<<
            //B2BESGOn26NOv2022>>
            field("M Trade"; "M Trade")
            {
                ApplicationArea = All;
            }
            //B2BESGOn26NOv2022<<
            field("Retail Shop Location"; "Retail Shop Location")
            {
                ApplicationArea = All;
            }
        }
    }

}