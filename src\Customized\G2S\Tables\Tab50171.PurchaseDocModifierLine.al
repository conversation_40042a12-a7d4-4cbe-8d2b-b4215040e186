//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
table 50171 "Purchase Doc Modifier Line"
{
    Caption = 'Purchase Doc Modifier Line';

    fields
    {
        field(1; "Modifier Code"; Code[20])
        {
            Caption = 'Modifier Code';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AutoIncrement = true;
        }
        field(3; "Purchase Line No."; Integer)
        {
            Caption = 'Purchase Line No.';
            FieldClass = Normal;

            trigger OnLookup()
            var
                ModifierHeader: Record "Purchase Doc Modifier";
                PurchaseLine: Record "Purchase Line";
                PurchaseLineLookUp: Page "Purchase Lines";
            begin
                if Rec."Modifier Code" = '' then
                    exit;

                if xRec."Purchase Line No." <> Rec."Purchase Line No." then InitializeLineData();

                ModifierHeader.SetRange("No.", Rec."Modifier Code");
                if ModifierHeader.FindFirst() then begin
                    ModifierHeader.TestField("Document No.");
                    PurchaseLine.SetRange("Document No.", ModifierHeader."Document No.");
                    PurchaseLine.SetFilter("Document Type", '=%1', ModifierHeader."Document type");
                    PurchaseLineLookUp.LookupMode := true;
                    PurchaseLineLookUp.SetTableView(PurchaseLine);
                    if PurchaseLineLookUp.RunModal() = Action::LookupOK then begin
                        PurchaseLineLookUp.GetRecord(PurchaseLine);

                        if PurchaseLine.Quantity <> PurchaseLine."Quantity Invoiced" then begin
                            Rec.Validate("Purchase Line No.", PurchaseLine."Line No.");
                            Rec."Purchase Line No." := PurchaseLine."Line No.";
                            Rec."Document type" := PurchaseLine."Document Type";
                            Rec."Header No." := PurchaseLine."Document No.";
                            Rec."Item No." := PurchaseLine."No.";
                            Rec.Type := PurchaseLine.Type;
                        end else
                            Error('You cannot select a completely Invoiced line');
                    end;
                end;
                Commit();
            end;

            trigger OnValidate()
            begin
                if Rec."Purchase Line No." <> xRec."Purchase Line No." then begin
                    Rec."Field Name" := '';
                    Rec.FieldID := 0;
                    Rec."Default Value" := '';
                    Rec."New Value" := '';
                    Rec."Currency Code" := '';
                    Rec."Currency Factor" := 0;
                    Rec."Date Field" := 0D;
                    Rec."Boolean Field" := false;
                end;
            end;
        }
        field(4; "FieldID"; Integer)
        {
            DataClassification = CustomerContent;
            Caption = 'FieldID';
            TableRelation = Field."No." WHERE(TableNo = FIELD(TableID));

            trigger OnValidate()
            var
                i: Integer;
                IsValid: Boolean;
                InvalidFieldErrMsg: Label 'You can not modify field "%1" on %2 Table, Contact Administrator.';
                TableName: Text;
            begin
                ConfirmFieldDuplicate(Rec.FieldID, Rec."Field Name");
                GetValidField(TableID);
                case "Record Type" of
                    "Record Type"::"Purchase Header":
                        begin
                            TableName := 'Purchase Header';
                            if not (PurchaseHeaderValidFeilds.Count = 0) then begin
                                for i := 1 to PurchaseHeaderValidFeilds.Count do begin
                                    if Rec.FieldID = PurchaseHeaderValidFeilds.Get(i) then IsValid := true else IsValid := false;
                                    if IsValid then break;
                                end;
                            end else
                                Error('No Avaliable Valid Purchase Header field.');
                        end;

                    "Record Type"::"Purchase Line":
                        begin
                            TableName := 'Purchase Line';
                            if not (PurchaseLineValidFeilds.Count = 0) then begin
                                for i := 1 to PurchaseLineValidFeilds.Count do begin
                                    if Rec.FieldID = PurchaseLineValidFeilds.Get(i) then IsValid := true else IsValid := false;
                                    if IsValid then break;
                                end;
                            end else
                                Error('No Avaliable Valid Purchase Line field.');
                        end;
                end;
                if not IsValid then Error(InvalidFieldErrMsg, Rec."Field Name", TableName);
            end;
        }
        field(5; "Field Name"; Text[50])
        {
            DataClassification = CustomerContent;
            Caption = 'Field Name';
            FieldClass = Normal;

            trigger OnLookup()
            var
                ModifierHeader: Record "Purchase Doc Modifier";
                PurchaseLineRec: Record "Purchase Line";
                PurchaseHeaderRec: Record "Purchase Header";
                LineNo: Integer;
                IsPurchaseHeader: Boolean;
                FactorValue: Decimal;
            begin
                if "Modifier Code" = '' then
                    exit;

                if Rec.TableID = 39 then TestField("Purchase Line No.");

                if xRec."Field Name" <> Rec."Field Name" then InitializeLineData();

                ModifierHeader.SetRange("No.", Rec."Modifier Code");
                if ModifierHeader.FindFirst() then begin
                    ModifierHeader.TestField("Document No.");
                    if ModifierHeader."Record Type" = ModifierHeader."Record Type"::"Purchase Header" then
                        IsPurchaseHeader := true;

                    if ModifierHeader.TableID = 0 then
                        exit;

                    if Rec.TableID <> ModifierHeader.TableID then Rec.TableID := ModifierHeader.TableID;
                end;

                CLEAR(FieldList);
                FieldRec.SETRANGE(FieldRec.TableNo, Rec.TableID);
                FieldList.SETTABLEVIEW(FieldRec);
                FieldList.LOOKUPMODE := true;
                if FieldList.RUNMODAL = ACTION::LookupOK then begin
                    FieldList.GETRECORD(FieldRec);
                    Rec.VALIDATE("Field Name", FieldRec.FieldName);
                    Rec.VALIDATE(FieldID, FieldRec."No.");

                    case IsPurchaseHeader of
                        true:
                            begin
                                ModifierHeader.TestField("Document No.");
                                PurchaseHeaderRec.SetRange("No.", ModifierHeader."Document No.");
                                if PurchaseHeaderRec.FindFirst() then
                                    PurchaseRecRef.GetTable(PurchaseHeaderRec);

                                Rec."Default Value" := Format(PurchaseRecRef.Field(FieldRec."No."));

                                if Rec."Field Name".Contains('Currency') then begin
                                    Rec."Currency Code" := Format(PurchaseRecRef.Field(FieldRec."No."));
                                    Evaluate(FactorValue, Format(PurchaseRecRef.Field(33)));
                                    Rec."Currency Factor" := FactorValue;
                                end;
                            end;

                        false:
                            begin
                                PurchaseLineRec.SetRange("Document No.", ModifierHeader."Document No.");
                                PurchaseLineRec.SetRange("Line No.", Rec."Purchase Line No.");
                                if PurchaseLineRec.FindFirst() then begin
                                    PurchaseRecRef.GetTable(PurchaseLineRec);
                                    Rec."Default Value" := Format(PurchaseRecRef.Field(FieldRec."No."));
                                end;
                            end;
                    end;
                end;
                Commit();
            end;

            trigger OnValidate()
            begin
                if Rec."Field Name" = '' then begin
                    Rec.FieldID := 0;
                    Rec."Default Value" := '';
                    Rec."New Value" := '';
                    Rec."Currency Code" := '';
                    Rec."Currency Factor" := 0;
                    Rec."Date Field" := 0D;
                    Rec."Boolean Field" := false;
                end
            end;
        }
        field(6; "Document type"; Enum "Purchase Document Type")
        {
            DataClassification = CustomerContent;
        }
        field(7; TableID; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(8; Reason; Text[250])
        {
            DataClassification = CustomerContent;
        }
        field(9; "Default Value"; Text[100])
        {
            DataClassification = CustomerContent;
            Caption = 'Old Value';
            Editable = false;

            trigger OnValidate()
            begin
                if "Field Name".Contains('Currency') then "Currency Code" := "Default Value";
            end;
        }
        field(10; "New Value"; Text[100])
        {
            Caption = 'New Value';
            TableRelation = if (TableID = const(38), FieldID = CONST(32)) Currency.Code else
            if (TableID = const(38), FieldID = CONST(116)) "VAT Business Posting Group" else
            if (TableID = const(38), FieldID = CONST(28)) Location.Code WHERE("Use As In-Transit" = CONST(false)) else
            if (TableID = const(38), FieldID = CONST(23)) "Payment Terms" else //
            if (TableID = const(38), FieldID = CONST(29)) "Dimension Value".Code WHERE("Global Dimension No." = CONST(1),
                                                          Blocked = CONST(false)) else
            if (TableID = const(38), FieldID = CONST(30)) "Dimension Value".Code WHERE("Global Dimension No." = CONST(1),
                                                          Blocked = CONST(false)) else
            if (TableID = const(38), FieldID = CONST(31)) "Vendor Posting Group" else
            if (TableID = const(38), FieldID = CONST(72)) Customer else
            if (TableID = const(38), FieldID = CONST(73)) "Reason Code" else
            if (TableID = const(38), FieldID = CONST(74)) "Gen. Business Posting Group" else
            if (TableID = const(38), FieldID = CONST(76)) "Transaction Type" else
            if (TableID = const(38), FieldID = CONST(77)) "Transport Method" else
            if (TableID = const(38), FieldID = CONST(78)) "Country/Region" else
            if (TableID = const(38), FieldID = CONST(79)) Vendor.Name else
            if (TableID = const(38), FieldID = CONST(104)) "Payment Method" else
            if (TableID = const(38), FieldID = CONST(27)) "Shipment Method" else
            if (TableID = const(38), FieldID = CONST(20)) Date else
            //Purchase Line
            if (TableID = const(39), FieldID = CONST(7)) Location.Code WHERE("Use As In-Transit" = CONST(false)) else
            if (TableID = const(39), FieldID = CONST(74)) "Gen. Business Posting Group" else
            if (TableID = const(39), FieldID = CONST(75)) "Gen. Product Posting Group" else
            if (TableID = const(39), FieldID = CONST(89)) "VAT Business Posting Group" else
            if (TableID = const(39), FieldID = CONST(90)) "VAT Product Posting Group" else
            if (TableID = const(39), FieldID = CONST(6), "Field Name" = const('Item')) Item else
            if (TableID = const(39), FieldID = CONST(6), "Field Name" = const('Resource')) Resource else
            if (TableID = const(39), FieldID = CONST(6), "Field Name" = const('Fixed Asset')) "Fixed Asset" else
            if (TableID = const(39), FieldID = CONST(6), "Field Name" = const('G/L Account')) "G/L Account" else
            if (TableID = const(39), FieldID = CONST(6), "Field Name" = const('Charge (Item)')) "Item Charge" else
            If (TableID = const(39), FieldID = CONST(8), Type = CONST(Item)) "Inventory Posting Group" else
            If (TableID = const(39), FieldID = CONST(8), Type = CONST("Fixed Asset")) "FA Posting Group" else
            If (TableID = const(39), FieldID = CONST(78)) "Transaction Type" else
            If (TableID = const(39), FieldID = CONST(79)) "Transport Method" else
            If (TableID = const(39), FieldID = CONST(5700)) "Responsibility Center" else
            If (TableID = const(39), FieldID = CONST(91)) Currency else
            If (TableID = const(39), FieldID = CONST(13), Type = CONST(Item)) "Item Unit of Measure".Code where("Item No." = field("Item No.")) else
            If (TableID = const(39), FieldID = CONST(5407), Type = CONST(Item)) "Item Unit of Measure".Code where("Item No." = field("Item No.")) else
            If (TableID = const(39), FieldID = CONST(13), Type = CONST(Resource)) "Resource Unit of Measure".Code where("Resource No." = field("Item No.")) else
            If (TableID = const(39), FieldID = CONST(5407), Type = CONST(Resource)) "Resource Unit of Measure".Code where("Resource No." = field("Item No.")) else
            if (TableID = const(39), FieldID = CONST(5709)) "Item Category" else
            if (FieldID = CONST(5600)) Date;

            trigger OnValidate()
            var
                OldValueVar: Decimal;
                NewValueVar: Decimal;
                totalReceivedPercent: Decimal;
                PurchaseOrderLine: Record "Purchase Line";
                NewPrepaymentBase: Decimal;
            begin
                //>>>>>>>> G2S 8570-CAS-01404-M1X1L3 3/3/2025
                // Control on prepayment % value
                // 1. Prepayment % value cannot be less than 0 
                // 2. Prepayment % value cannot be greater than 100
                // 3. Prepayment % value cannot be lower than the received quantity percentage
                Clear(PurchaseOrderLine);
                if Rec."Field Name" = 'Prepayment %' then begin
                    Evaluate(OldValueVar, Rec."Default Value");
                    Evaluate(NewValueVar, Rec."New Value");
                    if NewValueVar < 0 then Error('Prepayment % value cannot be less than 0');

                    if NewValueVar < OldValueVar then begin
                        // Check Prepayment % invoiced 
                        if CheckIsprepaymentInvoice() then begin // if prepayment % invoiced, get new prepayment balance and use that as the new base value
                            PurchaseOrderLine.SETRANGE("Document Type", Rec."Document type");
                            PurchaseOrderLine.SETRANGE("Document No.", Rec."Header No.");
                            PurchaseOrderLine.SETRANGE("Line No.", Rec."Purchase Line No.");
                            if PurchaseOrderLine.FINDFIRST() then begin
                                NewPrepaymentBase := PurchaseOrderLine."Prepayment %";

                                if NewPrepaymentBase > 0 then
                                    exit;

                                if PurchaseOrderLine."Quantity Received" > 0 then begin
                                    totalReceivedPercent := (PurchaseOrderLine."Quantity Received" / PurchaseOrderLine.Quantity) * 100;
                                    if NewValueVar < totalReceivedPercent then Error('Prepayment % cannot be lower than the received quantity percentage: %1%', Round(totalReceivedPercent, 1));
                                end;
                            end;
                        end;
                    end;
                    if NewValueVar > 100 then Error('Prepayment % value cannot be greater than 100');
                end;
                //<<<<<< G2S 8570-CAS-01404-M1X1L3 3/3/2025

                // if Rec."Field Name" = 'Prepayment %' then begin
                //     Evaluate(OldValueVar, Rec."Default Value");
                //     Evaluate(NewValueVar, Rec."New Value");
                //     totalPercentValue := OldValueVar + NewValueVar;
                //     if totalPercentValue > 100 then Error('Total Prepayment % value cannot be greater than 100');
                // end;
                ValidateFieldValue(Rec);
                ModifyRecordField(Rec);
            end;
        }
        field(11; "Header No."; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        field(12; "Currency Code"; Code[10])
        {
            DataClassification = ToBeClassified;
            TableRelation = Currency;
            trigger OnValidate()
            var
                StandardCodesMgt: Codeunit "Standard Codes Mgt.";
            begin
                Rec.TestField("Field Name", 'Currency Code');
                if (CurrFieldNo <> FieldNo("Currency Code")) and ("Currency Code" = xRec."Currency Code") then
                    UpdateCurrencyFactor
                else
                    if "Currency Code" <> xRec."Currency Code" then
                        UpdateCurrencyFactor
                    else
                        if "Currency Code" <> '' then begin
                            UpdateCurrencyFactor();
                            if "Currency Factor" <> xRec."Currency Factor" then
                                ConfirmUpdateCurrencyFactor();
                        end;
                Commit();
            end;
        }
        field(13; "Currency Factor"; Decimal)
        {
            Caption = 'Currency Factor';
            DecimalPlaces = 0 : 15;
            MinValue = 0;

            trigger OnValidate()
            begin

            end;
        }
        field(14; "Decimal Field"; Decimal)
        {
            DataClassification = ToBeClassified;
            DecimalPlaces = 0 : 5;
        }
        field(15; "Record Type"; Option)
        {
            DataClassification = ToBeClassified;
            OptionMembers = " ","Purchase Header","Purchase Line";
        }
        field(17; "Item No."; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        field(18; "Line New Value"; Text[100])
        {
            DataClassification = ToBeClassified;
        }
        field(19; "Date Field"; Date)
        {
            DataClassification = ToBeClassified;
        }
        field(20; "Boolean Field"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(21; Type; Enum "Purchase Line Type")
        {
            DataClassification = ToBeClassified;

            trigger OnValidate()
            begin
                if Rec."Field Name" = 'Type' then "New Value" := Format(Type);
                if TableID = 38 then
                    if Format(Type) <> Format(Type::" ") then type := Type::" ";
            end;
        }
        field(22; "Cc Code"; Code[20])
        {
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(2));
        }
    }
    keys
    {
        key(PK; "Modifier Code", "Document type", TableID, "Record Type", "Line No.")
        {
        }
    }

    trigger OnInsert()
    var
        ModifierHeader: Record "Purchase Doc Modifier";
    begin
        ModifierHeader.SetRange("No.", "Modifier Code");
        if ModifierHeader.FindFirst() then begin
            if ModifierHeader."Cc Code" = '' then Error(CcCodeErrMsg, ModifierHeader."No.");
            Rec.Validate("Cc Code", ModifierHeader."Cc Code");
        end;
    end;

    procedure UpdateCurrencyFactor()
    var
        UpdateCurrencyExchangeRates: Codeunit "Update Currency Exchange Rates";
        ConfirmManagement: Codeunit "Confirm Management";
        PurchaseHeader: Record "Purchase Header";
        PurchaseHeaderModifier: Record "Purchase Doc Modifier";
        Updated: Boolean;
    begin
        PurchaseHeaderModifier.SetRange("No.", Rec."Modifier Code");
        if PurchaseHeaderModifier.FindFirst() then begin
            PurchaseHeaderModifier.TestField("Document No.");

            PurchaseHeader.SetRange("No.", PurchaseHeaderModifier."Document No.");
            if PurchaseHeader.FindFirst() then begin
                if PurchaseHeader."Posting Date" <> 0D then
                    CurrencyDate := PurchaseHeader."Posting Date"
                else
                    CurrencyDate := WorkDate;

                if UpdateCurrencyExchangeRates.ExchangeRatesForCurrencyExist(CurrencyDate, "Currency Code") then begin
                    "Currency Factor" := CurrExchRate.ExchangeRate(CurrencyDate, "Currency Code");
                end else begin
                    if ConfirmManagement.GetResponseOrDefault(
                         StrSubstNo(MissingExchangeRatesQst, "Currency Code", CurrencyDate), true)
                    then begin
                        UpdateCurrencyExchangeRates.OpenExchangeRatesPage("Currency Code");
                        UpdateCurrencyFactor();
                    end;
                end;
            end;
        end;
        Commit();
    end;

    local procedure ConfirmUpdateCurrencyFactor(): Boolean
    begin
        Validate("Currency Factor");
    end;

    local procedure ValidateFieldValue(MRec: Record "Purchase Doc Modifier Line"): Boolean
    var
        IsHandled: Boolean;
        ValidationError: Text;
        PurchaseRecRef: RecordRef;
        PurchaseFieldRef: FieldRef;
        PurchaseHeaderRec: Record "Purchase Header";
        PurchaseLineRec: Record "Purchase Line";
        ModifierHeader: Record "Purchase Doc Modifier";
    begin
        ModifierHeader.Get(Rec."Modifier Code");
        case Rec."Record Type" of
            Rec."Record Type"::"Purchase Header":
                begin
                    PurchaseHeaderRec.SetCurrentKey("No.");
                    PurchaseHeaderRec.SetRange("No.", ModifierHeader."Document No.");
                    if PurchaseHeaderRec.FindFirst() then
                        PurchaseRecRef.GetTable(PurchaseHeaderRec);

                    PurchaseFieldRef := PurchaseRecRef.Field(Rec.FieldID);

                    ValidationError := ConfigValidateMgt.EvaluateValue(PurchaseFieldRef, MRec."New Value", false);
                    if ValidationError <> '' then Error(ValidationError);
                    IsHandled := true;
                end;

            Rec."Record Type"::"Purchase Line":
                begin
                    MRec.TestField("Purchase Line No.");
                    PurchaseLineRec.SetRange("Document Type", ModifierHeader."Document type");
                    PurchaseLineRec.SetRange("Document No.", ModifierHeader."Document No.");
                    PurchaseLineRec.SetRange("Line No.", MRec."Purchase Line No.");
                    if PurchaseLineRec.FindFirst() then
                        PurchaseRecRef.GetTable(PurchaseLineRec);
                    PurchaseFieldRef := PurchaseRecRef.Field(Rec.FieldID);

                    ValidationError := ConfigValidateMgt.EvaluateValue(PurchaseFieldRef, MRec."New Value", false);
                    if ValidationError <> '' then Error(ValidationError);
                    IsHandled := true;
                end;
        end;
        exit(IsHandled);
        Commit();
    end;

    local procedure ConfirmFieldDuplicate(recFieldID: Integer; recFieldName: Text)
    var
        i: Integer;
        Found: Integer;
        SelectedFieldIds: list of [Integer];
        ModifierLine: Record "Purchase Doc Modifier Line";
    begin
        Found := 1;
        if (recFieldID = 0) or (recFieldName = '') then
            exit;

        case Rec."Record Type" of
            Rec."Record Type"::"Purchase Header":
                begin
                    ModifierLIne.SetFilter("Modifier Code", Rec."Modifier Code");
                    if ModifierLine.FindSet() then
                        repeat
                            if ModifierLine.FieldID <> 0 then
                                SelectedFieldIds.Add(ModifierLine.FieldID);
                        until ModifierLine.Next() = 0;
                end;

            Rec."Record Type"::"Purchase Line":
                begin
                    begin
                        ModifierLIne.SetFilter("Modifier Code", Rec."Modifier Code");
                        ModifierLIne.SetFilter("Purchase Line No.", '=%1', Rec."Purchase Line No.");
                        if ModifierLine.FindSet() then
                            repeat
                                if ModifierLine.FieldID <> 0 then
                                    SelectedFieldIds.Add(ModifierLine.FieldID);
                            until ModifierLine.Next() = 0;
                    end;
                end;
        end;

        for i := 1 to SelectedFieldIds.Count do begin
            if SelectedFieldIds.Get(i) = recFieldID then Found += 1;
        end;

        if Found > 1 then Error('You can''t modify %1 field more than once within the document', recFieldName);
    end;

    procedure ModifyRecordField(MRec: Record "Purchase Doc Modifier Line")
    var
        i: Integer;
        OptionArray: list of [Text];
        booleanOpt: Array[4] of Text;
        nooffieldsinmaster: Integer;
        MigrationMgt: Codeunit "Config. Package Management";
        newvaluecheck: Decimal;
        booleanValue: Boolean;
        DateValue: Date;
        DateTimeVar: DateTime;
        DateFormulaVar: DateFormula;
        TimeValue: Time;
        IsOptionValid: Boolean;
        MasterRecRef: RecordRef;
        PurchaseFieldRef: FieldRef;
        MasterName: Text[100];
        ModifierLine: Record "Purchase Doc Modifier Line";
    begin
        case Rec."Record Type" of
            Rec."Record Type"::"Purchase Header":
                begin
                    MasterRecRef.GetTable(PurchaseHeader);
                    nooffieldsinmaster := MasterRecRef.FieldCount;
                    PurchaseFieldRef := MasterRecRef.Field(Rec.FieldID);

                    if (Format(PurchaseFieldRef.Type) = 'Decimal') or (Format(PurchaseFieldRef.Type) = 'Integer') then begin
                        if IsNumeric(MRec."New Value") then begin
                            Evaluate(newvaluecheck, MRec."New Value");
                            Rec."Decimal Field" := newvaluecheck;
                        end else
                            Error('Invalid Decimal or Integer Value');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Option') then begin
                        OptionArray := PurchaseFieldRef.OPTIONCAPTION.Split(',');
                        for i := 1 to OptionArray.Count do begin
                            if MRec."New Value" <> OptionArray.Get(i) then IsOptionValid := false else IsOptionValid := true;
                            if IsOptionValid then break;
                        end;
                        if not IsOptionValid then Error('Invalid Option Value');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Time') then begin
                        if not Evaluate(TimeValue, MRec."New Value") then Error('Invalid Time');
                        Message('Time Field');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Date') then begin
                        if not Evaluate(DateValue, MRec."New Value") then Error('Invalid Date');
                        Rec."Date Field" := DateValue;
                        Message('Date Field');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Boolean') then begin
                        if not (MRec."New Value" in ['Yes', 'No']) then Error('Invalid Boolean Value, Kindly use "YES" or "NO" as the Value');
                        if MRec."New Value".Contains('Yes') then
                            Rec."Boolean Field" := true;
                    end;

                    if FORMAT(PurchaseFieldRef.Type) = 'DateTime' then begin
                        if not Evaluate(DateTimeVar, ModifierLine."New Value") then Error('Invalid Datetime');
                    end;

                    if FORMAT(PurchaseFieldRef.Type) = 'DateFormula' then begin
                        if not Evaluate(DateFormulaVar, ModifierLine."New Value") then Error('Invalid DateFormular');
                    end;
                end;

            Rec."Record Type"::"Purchase Line":
                begin
                    MasterRecRef.GetTable(PurchaseLine);
                    nooffieldsinmaster := MasterRecRef.FieldCount;

                    PurchaseFieldRef := MasterRecRef.Field(Rec.FieldID);
                    if (Format(PurchaseFieldRef.Type) = 'Decimal') or (Format(PurchaseFieldRef.Type) = 'Integer') then begin
                        if IsNumeric(MRec."New Value") then begin
                            Evaluate(newvaluecheck, MRec."New Value");
                            Rec."Decimal Field" := newvaluecheck;
                        end else
                            Error('Invalid Decimal or Integer Value');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Option') then begin
                        OptionArray := PurchaseFieldRef.OPTIONCAPTION.Split(',');
                        for i := 1 to OptionArray.Count do begin
                            if MRec."New Value" <> OptionArray.Get(i) then IsOptionValid := false else IsOptionValid := true;
                            if IsOptionValid then break;
                        end;
                        if not IsOptionValid then Error('Invalid Option Value');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Time') then begin
                        if not Evaluate(TimeValue, MRec."New Value") then Error('Invalid Time');
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Date') then begin
                        if not Evaluate(DateValue, MRec."New Value") then Error('Invalid Date');
                        Rec."Date Field" := DateValue;
                    end;

                    if (Format(PurchaseFieldRef.Type) = 'Boolean') then begin
                        if not (MRec."New Value" in ['Yes', 'No']) then Error('Invalid Boolean Value, Kindly use "YES" or "NO" as the Value');
                        if MRec."New Value".Contains('Yes') then
                            Rec."Boolean Field" := true;
                    end;

                    if FORMAT(PurchaseFieldRef.Type) = 'DateTime' then begin
                        if not Evaluate(DateTimeVar, ModifierLine."New Value") then Error('Invalid Datetime');
                    end;

                    if FORMAT(PurchaseFieldRef.Type) = 'DateFormula' then begin
                        if not Evaluate(DateFormulaVar, ModifierLine."New Value") then Error('Invalid DateFormular');
                    end;
                end;
        end;
        Commit();
    end;

    procedure initializeLine()
    begin

    end;

    internal procedure GetValidField(var TableID: Integer)
    var
        MFT: Record "Modifier Field Template";
    begin
        if TableID = 0 then
            exit;

        MFT.SetCurrentKey(TableID);
        MFT.SetFilter(TableID, '=%1', TableID);
        MFT.SetRange("Allow Modify", true);
        if MFT.FindSet() then
            repeat
                if TableID = 38 then
                    PurchaseHeaderValidFeilds.Add(MFT.FieldID)
                else
                    if TableID = 39 then PurchaseLineValidFeilds.Add(MFT.FieldID);
            until MFT.Next() = 0;
        Commit();
    end;

    local procedure IsNumeric(Value: Text): Boolean
    var
        intVar: Integer;
        decimalVar: Decimal;
    begin
        if Evaluate(intVar, Value) or Evaluate(decimalVar, Value) then exit(true) else exit(false);
    end;

    local procedure CheckIsprepaymentInvoice(): Boolean
    var
        PurchaseInvoiceHeader: Record "Purch. Inv. Header";
    begin
        PurchaseInvoiceHeader.SetCurrentKey("Prepayment Order No.");
        PurchaseInvoiceHeader.SetRange("Prepayment Order No.", Rec."Header No.");
        if PurchaseInvoiceHeader.FindFirst() then
            exit(true);
    end;

    procedure InitializeLineData()
    begin
        Rec."Field Name" := '';
        Rec."Currency Code" := '';
        Rec."New Value" := '';
        Rec."Currency Factor" := 0;
        Rec."Date Field" := 0D;
        Rec."Boolean Field" := false;
        Rec.Reason := '';
        Rec."Default Value" := '';
        Rec.FieldID := 0;
    end;

    var
        isPendingnPrepayment: Boolean;
        ModifierHeader: Record "Purchase Doc Modifier";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        PurchaseHeaderValidFeilds: list of [Integer];
        PurchaseLineValidFeilds: list of [Integer];
        CcCodeErrMsg: TextConst ENU = 'Cc Code must have a value in Purchase Doc Modifier Header: No.=%1. It cannot be zero or empty.';
        DeleteDocErrorMsg: Label 'You cannot delete a Modifer Doc that has already been used to modify a Purchase Document';
        MissingExchangeRatesQst: Label 'There are no exchange rates for currency %1 and date %2. Do you want to add them now? Otherwise, the last change you made will be reverted.', Comment = '%1 - currency code, %2 - posting date';
        CurrencyDate: Date;
        FieldList: Page "Fields Lookup";
        CurrExchRate: Record "Currency Exchange Rate";
        FieldRec: Record "Field";
        PurchaseRecRef: RecordRef;
        ConfigValidateMgt: Codeunit "Config. Validate Management";

    trigger OnDelete()
    begin
        if Rec."Modifier Code" <> '' then
            ModifierHeader.Get(Rec."Modifier Code");
        ModifierHeader.TestField(ModifierHeader.Status, ModifierHeader.Status::Open);
        if ModifierHeader."Document No." <> '' then begin
            PurchaseHeader.SetCurrentKey("No.");
            PurchaseHeader.SetRange("Document Type", ModifierHeader."Document type");
            PurchaseHeader.SetRange("No.", ModifierHeader."Document No.");
            if PurchaseHeader.FindFirst() then
                if PurchaseHeader.isModifed and ModifierHeader."Document Modified" then Error(DeleteDocErrorMsg);
        end;
    end;
}
// >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
