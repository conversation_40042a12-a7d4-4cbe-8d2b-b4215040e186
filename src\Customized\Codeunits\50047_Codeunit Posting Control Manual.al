codeunit 50047 "Allow Posting Control Manual"
{

    trigger OnRun();
    begin
        SDT.GET;
        Usersetup.RESET;
        Usersetup.SETFILTER("Allow Posting To",'<>%1',0D);
        if not Usersetup.ISEMPTY then
          Usersetup.MODIFYALL(Usersetup."Allow Posting To",DT2DATE(SDT.ServerDateTime));


        Usersetup.RESET;
        Usersetup.SETFILTER("Allow FA Posting To",'<>%1',0D);
        if not Usersetup.ISEMPTY then
          Usersetup.MODIFYALL("Allow FA Posting To",DT2DATE(SDT.ServerDateTime));

        if GLSetup.GET then begin
          GLSetup."Allow Posting To" := DT2DATE(SDT.ServerDateTime);
          GLSetup.MODIFY;
        end;

        if FASetup.GET then begin
          FASetup."Allow FA Posting To" := DT2DATE(SDT.ServerDateTime);
          FASetup.MODIFY;
        end;
    end;

    var
        Usersetup : Record "User Setup";
        SDT : Record ServerDateTime_View;
        GLSetup : Record "General Ledger Setup";
        FASetup : Record "FA Setup";
}

