page 50235 "Transfer Settlement"
{
    PageType = Worksheet;
    SourceTable = "Transfer Settlement";
    UsageCategory = Tasks;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            group(General)
            {
                field(StartDateGVar; StartDateGVar)
                {
                    Caption = 'Start Date';
                }
                field(EndDateGVar; EndDateGVar)
                {
                    Caption = 'End Date';
                }
            }
            repeater(TransferSettlement)
            {
                field("Entry No"; "Entry No")
                {

                }
                field("Transport Ledger Entry No."; "Transport Ledger Entry No.")
                {

                }
                field("Trip Id"; "Trip Id")
                {

                }
                field("Trip Date"; "Trip Date")
                {

                }
                field(Distance; Distance)
                {

                }
                field("From Location"; "From Location")
                {

                }
                field("Vehicle By"; "Vehicle By")
                {

                }
                field("Vehicle No"; "Vehicle No")
                {

                }
                field("Vehicle Type"; "Vehicle Type")
                {

                }
                field("Max Dist. Location"; "Max Dist. Location")
                {

                }
                field("Contract Type"; "Contract Type")
                {

                }
                field("PLS Approved"; "PLS Approved")
                {

                }
                field("PLS Approved By"; "PLS Approved By")
                {

                }
                field("PLS From-Location"; "PLS From-Location")
                {

                }
                field("PLS To-Location"; "PLS To-Location")
                {

                }
                field("CCD No."; "CCD No.")
                {
                    ApplicationArea = ALL;
                }
                field("CCD Acknowledge Date"; "CCD Acknowledge Date")
                {
                    ApplicationArea = ALL;
                }
                field("CCD Acknowledge By"; "CCD Acknowledge By")
                {
                    ApplicationArea = ALL;
                }
                field("SSD No."; "SSD No.")
                {
                    ApplicationArea = ALL;
                }
                field("SSD Acknowledge Date"; "SSD Acknowledge Date")
                {
                    ApplicationArea = ALL;
                }
                field("SSD Acknowledge By"; "SSD Acknowledge By")
                {
                    ApplicationArea = ALL;
                }
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = ALL;
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = ALL;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = ALL;
                }
                field("Order Created"; "Order Created")
                {
                    ApplicationArea = ALL;
                }

            }

        }
    }

    actions
    {
        area(processing)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Image = "Action";
                action(GetTransportLedgerEntries)
                {
                    Caption = 'Get Transport Ledger Entries';
                    trigger OnAction();
                    begin
                        UpdateFromTraspLedEntry();
                    end;
                }
                action(CreatePurchaseOrder)
                {
                    Caption = 'Create Purchase Order';
                    trigger OnAction()
                    Var
                        TransContractType: Record "Transport Contract Types";
                        TransferSettlement: Record "Transfer Settlement";
                        TransferSettlement1: Record "Transfer Settlement";
                        VendGVar: Code[20];
                        TotAmt: Decimal;
                        TotAmtTrip: Decimal;
                        VendTripGVar: code[20];
                        MultipVendTrip: Boolean;
                        MultipVendBHN: Boolean;
                        MultipVendAgn: Boolean;
                        PrevVendTrip: Code[20];
                        PrevVendBHN: Code[20];
                        PrevVendAgn: Code[20];
                        TotAmtAgn: Decimal;
                        VendAgnGVar: Code[20];
                        TotDistAgn: Decimal;
                        VehicleMaster: record "Transporter Vehicle";
                        AddAgnGVar: Decimal;
                        PrevVehicleNo: code[20];
                        PrevVehicleAgn: code[20];
                        OrderNoGVar: code[20];
                        LinGVar: integer;
                    BEGIN
                        IF Confirm('Do you want to Create PO?', true, false) then BEGIN
                            clear(PrevVendTrip);
                            clear(PrevVendBHN);
                            clear(PrevVendAgn);
                            Clear(PrevVehicleNo);
                            Clear(TotAmt);
                            clear(VendGVar);
                            TransferSettlement.reset;
                            TransferSettlement.SetCurrentKey("Vendor No.");
                            TransferSettlement.SetRange("Contract Type", TransferSettlement."Contract Type"::BHN);
                            TransferSettlement.SetRange("Order Created", false);
                            IF TransferSettlement.findset then
                                repeat
                                    IF (VendGVar <> TransferSettlement."Vendor No.") then BEGIN
                                        TransferSettlement1.reset;
                                        TransferSettlement1.SetCurrentKey("Vendor No.");
                                        TransferSettlement1.SetRange("Vendor No.", TransferSettlement."Vendor No.");
                                        TransferSettlement1.SetRange("Contract Type", TransferSettlement1."Contract Type"::BHN);
                                        IF TransferSettlement1.FindSet() then
                                            repeat
                                                TransContractType.RESET;
                                                TransContractType.SetRange("Vendor No.", TransferSettlement1."Vendor No.");
                                                TransContractType.SetRange("From Location", TransferSettlement1."From Location");
                                                TransContractType.SetRange("To-Location", TransferSettlement1."Max Dist. Location");
                                                TransContractType.SetRange("Contract Type", "Contract Type"::BHN);
                                                TransContractType.SetCurrentKey("Vendor No.");
                                                IF TransContractType.findfirst then
                                                    TotAmt += TransContractType.Total;
                                            until TransferSettlement1.next = 0;
                                        ///create PO >>

                                        OrderNoGVar := CreatePO(TransferSettlement1,
                                        TotAmt, TransContractType);
                                        LinGVar := 10000;

                                        UpdateTransportSettlementArchieve(TransferSettlement,
                                        TransContractType,
                                        OrderNoGVar, LinGVar);


                                        TransferSettlement1.reset;
                                        TransferSettlement1.SetCurrentKey("Vendor No.");
                                        TransferSettlement1.SetRange("Vendor No.", TransferSettlement."Vendor No.");
                                        TransferSettlement1.SetRange("Contract Type", TransferSettlement1."Contract Type"::BHN);
                                        IF TransferSettlement1.FindSet() then
                                            repeat
                                                TransferSettlement1."Order Created" := true;
                                                Transfersettlement1."Document No." := OrderNoGVar;
                                                TransferSettlement1."Document Type" := 1;
                                                TransferSettlement1.modify;
                                            until TransferSettlement1.next = 0;
                                    END;
                                    //Create PO <<
                                    clear(TotAmt);
                                    clear(orderNoGVar);
                                    VendGVar := TransferSettlement."Vendor No.";
                                until TransferSettlement.next = 0;

                            /*//B2B.P.K.T
                                                        Clear(TotAmtTrip);
                                                        clear(VendTripGVar);
                                                        TransferSettlement.reset;
                                                        TransferSettlement.SetCurrentKey("Vendor No.");
                                                        TransferSettlement.SetRange("Contract Type", TransferSettlement."Contract Type"::TripWise);
                                                        TransferSettlement.SetRange("Order Created", false);
                                                        IF TransferSettlement.findset then
                                                            repeat
                                                                IF (VendTripGVar <> TransferSettlement."Vendor No.") then begin

                                                                    TransferSettlement1.reset;
                                                                    TransferSettlement1.SetCurrentKey("Vendor No.");
                                                                    TransferSettlement1.SetRange("Vendor No.", TransferSettlement."Vendor No.");
                                                                    TransferSettlement1.SetRange("Contract Type", TransferSettlement1."Contract Type"::TripWise);
                                                                    IF TransferSettlement1.FindSet() then
                                                                        repeat
                                                                            TransContractType.RESET;
                                                                            TransContractType.SetCurrentKey("Vendor No.");
                                                                            TransContractType.SetRange("Vendor No.", TransferSettlement1."Vendor No.");
                                                                            TransContractType.SetRange("From Location", TransferSettlement1."From Location");
                                                                            TransContractType.SetRange("To-Location", TransferSettlement1."Max Dist. Location");
                                                                            TransContractType.SetRange("Contract Type", "Contract Type"::TripWise);
                                                                            TransContractType.Setfilter("Starting Date", '<=%1', TransferSettlement1."Trip Date");
                                                                            TransContractType.Setfilter("Ending Date", '>=%1', TransferSettlement1."Trip Date");
                                                                            IF TransContractType.findfirst then
                                                                                TotAmtTrip += TransContractType.Rate;
                                                                        until TransferSettlement1.next = 0;

                                                                    OrderNoGVar := CreatePO(TransferSettlement1,
                                                                 TotAmtTrip, TransContractType);

                                                                    LinGVar := 10000;

                                                                    UpdateTransportSettlementArchieve(TransferSettlement,
                                                                    TransContractType,
                                                                    OrderNoGVar, LinGVar);

                                                                    TransferSettlement1.reset;
                                                                    TransferSettlement1.SetCurrentKey("Vendor No.");
                                                                    TransferSettlement1.SetRange("Vendor No.", TransferSettlement."Vendor No.");
                                                                    TransferSettlement1.SetRange("Contract Type", TransferSettlement1."Contract Type"::TripWise);
                                                                    IF TransferSettlement1.FindSet() then
                                                                        repeat
                                                                            TransferSettlement1."Order Created" := true;
                                                                            Transfersettlement1."Document No." := OrderNoGVar;
                                                                            TransferSettlement1."Document Type" := 1;
                                                                            TransferSettlement1.modify;
                                                                        until TransferSettlement1.next = 0;
                                                                end;
                                                                clear(TotAmtTrip);
                                                                clear(orderNoGVar);
                                                                VendTripGVar := TransferSettlement."Vendor No.";
                                                            until TransferSettlement.next = 0;


                                                        Clear(TotDistAgn);
                                                        clear(VendAgnGVar);
                                                        clear(AddAgnGVar);
                                                        Clear(PrevVehicleAgn);
                                                        TransferSettlement.reset;
                                                        TransferSettlement.SetCurrentKey("Vehicle No", "Vendor No.");
                                                        TransferSettlement.SetRange("Contract Type", TransferSettlement."Contract Type"::Aglevinthis);
                                                        TransferSettlement.SetRange("Order Created", false);
                                                        IF TransferSettlement.findset then
                                                            repeat
                                                                IF ((VendAgnGVar <> TransferSettlement."Vendor No.") OR (TransferSettlement."Vehicle No" <> PrevVehicleAgn)) then begin

                                                                    TransferSettlement1.reset;
                                                                    TransferSettlement1.SetCurrentKey("Vehicle No", "Vendor No.");
                                                                    TransferSettlement1.SetRange("Vendor No.", TransferSettlement."Vendor No.");
                                                                    TransferSettlement1.SetRange("Vehicle No", TransferSettlement."Vehicle No");
                                                                    TransferSettlement1.SetRange("Contract Type", TransferSettlement1."Contract Type"::Aglevinthis);
                                                                    IF TransferSettlement1.FindSet() then
                                                                        repeat
                                                                            TotDistAgn += TransferSettlement1.Distance;

                                                                        until TransferSettlement1.next = 0;

                                                                    TransContractType.RESET;
                                                                    TransContractType.SetCurrentKey("Vehicle Registration Number", "Vendor No.");
                                                                    TransContractType.SetRange("Vendor No.", TransferSettlement1."Vendor No.");
                                                                    TransContractType.SetRange("From Location", TransferSettlement1."From Location");
                                                                    TransContractType.SetRange("To-Location", TransferSettlement1."Max Dist. Location");
                                                                    TransContractType.SetRange("Contract Type", "Contract Type"::Aglevinthis);

                                                                    IF TransContractType.findfirst then;

                                                                    VehicleMaster.reset;
                                                                    VehicleMaster.SetRange("Vehicle Reg No.", TransferSettlement."Vehicle No");
                                                                    VehicleMaster.SetRange("Vendor No.", TransferSettlement1."Vendor No.");
                                                                    IF VehicleMaster.FindFirst() then BEGIN
                                                                        VehicleMaster.TestField(Availability);
                                                                        IF VehicleMaster.Availability < 90 then
                                                                            TotAmtAgn := ((TotDistAgn * TransContractType."Rate Per KM 6000KM Base line") + (TotDistAgn * TransContractType."Reimbursement for FueL Cost"))
                                                                        else
                                                                            TotAmtAgn := ((TransContractType."Baseline KM" * TransContractType."Rate Per KM 6000KM Base line") + (TotDistAgn * TransContractType."Reimbursement for FueL Cost"));
                                                                        IF TotDistAgn > TransContractType."Baseline KM" THEN
                                                                            AddAgnGVar := Abs(((TotDistAgn - TransContractType."Baseline KM") * TransContractType."Add Rate Per KM Above 6000KM"));
                                                                    END;

                                                                    IF AddAgnGVar <> 0 then
                                                                        TotAmtAgn := (TotAmtAgn + AddAgnGVar);

                                                                    OrderNoGVar := CreatePO(TransferSettlement1, TotAmtAgn, TransContractType);

                                                                    LinGVar := 10000;

                                                                    UpdateTransportSettlementArchieve(TransferSettlement,
                                                                    TransContractType,
                                                                    OrderNoGVar, LinGVar);


                                                                    TransferSettlement1.reset;
                                                                    TransferSettlement1.SetCurrentKey("Vendor No.");
                                                                    TransferSettlement1.SetRange("Vendor No.", TransferSettlement."Vendor No.");
                                                                    TransferSettlement1.SetRange("Vehicle No", TransferSettlement."Vehicle No");
                                                                    TransferSettlement1.SetRange("Contract Type", TransferSettlement1."Contract Type"::Aglevinthis);
                                                                    IF TransferSettlement1.FindSet() then
                                                                        repeat
                                                                            TransferSettlement1."Order Created" := true;
                                                                            Transfersettlement1."Document No." := OrderNoGVar;
                                                                            TransferSettlement1."Document Type" := 1;
                                                                            TransferSettlement1.modify;
                                                                        until TransferSettlement1.next = 0;

                                                                end;
                                                                clear(TotAmtAgn);
                                                                clear(OrderNoGVar);
                                                                clear(AddAgnGVar);
                                                                clear(TotDistAgn);
                                                                VendAgnGVar := TransferSettlement."Vendor No.";
                                                            until TransferSettlement.next = 0;
                                                            *///B2B.P.K.T
                        END;
                    end;
                }
            }
        }
    }

    trigger OnOpenPage();
    begin
        Clear(StartDateGVar);
        Clear(EndDateGVar);
    end;

    procedure CreatePO(var TransferSettl: record "Transfer Settlement"; var TotAmount: Decimal; Var TransContract: Record "Transport Contract Types"): Code[20]
    var
        PurchaseHeaderOrder: Record "Purchase Header";
        PPSetup: Record "Purchases & Payables Setup";
        VendorL: Record Vendor;
        NoSeriesMgt: Codeunit NoSeriesManagement;
        PurchaseOrder: Record "Purchase Header";
        PurchaseLineOrder: Record "Purchase Line";
        OrderNo: code[20];
        PurchaseLine: Record "Purchase Line";
        LineNo: Integer;
        LineNoGvar: integer;
        TransSettlmentLedgers: Record "Transport Settlement Archieve";
        TransSettlmentLRec: Record "Transfer Settlement";
    BEGIN
        PurchaseHeaderOrder.Init();
        PurchaseHeaderOrder."Document Type" := PurchaseHeaderOrder."Document Type"::Order;
        PPSetup.GET();
        VendorL.GET(TransferSettl."Vendor No.");
        //VendorL.TestField("Vendor Type");
        IF VendorL."Vendor Type" = VendorL."Vendor Type"::" " then
            error('Vendor Type Must Have a value in %1', VendorL."No.");
        IF VendorL."Vendor Type" = VendorL."Vendor Type"::Local then begin
            PPSetup.TestField("Local Purchase Order");
            PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Local Purchase Order", WORKDATE(), TRUE);
        END else
            IF VendorL."Vendor Type" = VendorL."Vendor Type"::Import then BEGIN
                PPSetup.TestField("Import Purchase Order");
                PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Import Purchase Order", WORKDATE(), TRUE);
            END;
        PurchaseHeaderOrder.INSERT(true);

        /*IF PurchaseOrder.GET(PurchaseOrder."Document Type"::Order, PurchaseHeaderOrder."No.") THEN
            ERROR('Record already Existed.');*/
        PurchaseHeaderOrder."Posting Date" := WORKDATE();
        PurchaseHeaderOrder."Document Date" := WORKDATE();
        PurchaseHeaderOrder.VALIDATE("Buy-from Vendor No.", VendorL."No.");
        PurchaseHeaderOrder."Expected Receipt Date" := WORKDATE();
        PurchaseHeaderOrder."Purchase Type" := VendorL."Vendor Type";
        PurchaseHeaderOrder.Modify();

        PurchaseLine.reset;
        PurchaseLine.setrange("Document Type", PurchaseLine."Document Type"::Order);
        PurchaseLine.setrange("Document No.", PurchaseHeaderOrder."No.");
        IF PurchaseLine.findlast then
            LineNo := PurchaseLine."Line No." + 10000
        else
            LineNo := 10000;

        PurchaseLineOrder.INIT();
        PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
        PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
        PurchaseLineOrder."Line No." := LineNo;
        PurchaseLineOrder.INSERT();

        PurchaseLineOrder."Buy-from Vendor No." := PurchaseHeaderOrder."Buy-from Vendor No.";
        PurchaseLineOrder.VALIDATE("Buy-from Vendor No.");
        PurchaseLineOrder.Type := PurchaseLineOrder.Type::"G/L Account";
        IF PurchaseHeaderOrder."Purchase Type" = PurchaseHeaderOrder."Purchase Type"::Import then
            PurchaseLineOrder.VALIDATE("No.", '782130')
        else
            IF PurchaseHeaderOrder."Purchase Type" = PurchaseHeaderOrder."Purchase Type"::Local THEN
                PurchaseLineOrder.VALIDATE("No.", '782120');

        PurchaseLineOrder.VALIDATE(Quantity, 1);
        PurchaseLineOrder."Direct Unit Cost" := TotAmount;
        PurchaseLineOrder.VALIDATE("Direct Unit Cost");
        PurchaseLineOrder."Location Code" := TransferSettl."Max Dist. Location";
        PurchaseLineOrder.modify;
        OrderNo := PurchaseLineOrder."Document No.";
        message('Created PO is %1', OrderNo);

        /*TransSettlmentLedgers.reset;
        IF TransSettlmentLedgers.Findlast then
            LineNoGVar := TransSettlmentLedgers."Line No." + 10000
        else
            LineNoGvar := 10000;


        TransSettlmentLRec.reset;
        TransSettlmentLRec.SetRange("Vendor No.", TransferSettl."Vendor No.");
        TransSettlmentLRec.SetRange("Contract Type", TransferSettl."Contract Type");
        TransSettlmentLRec.SetRange("Vehicle No", TransferSettl."Vehicle No");
        if TransSettlmentLRec.findset then
            repeat
                TransSettlmentLedgers.Init();
                TransSettlmentLedgers."Document Type" := PurchaseHeaderOrder."Document Type";
                TransSettlmentLedgers."Document No." := PurchaseHeaderOrder."No.";
                TransSettlmentLedgers."Document Line No." := PurchaseLineOrder."Line No.";
                TransSettlmentLedgers."Line No." := LineNoGvar;
                TransSettlmentLedgers.insert(true);
                TransSettlmentLedgers.TransferFields(TransContract);
                TransSettlmentLedgers."Transport Settlement No." := TransferSettl."Entry No";
                TransSettlmentLedgers."Trip Date" := TransferSettl."Trip Date";
                TransSettlmentLedgers."Trip Id" := TransferSettl."Trip Id";
                TransSettlmentLedgers."Max Dist. Location" := TransferSettl."Max Dist. Location";
                TransSettlmentLedgers.Modify();
                LineNoGvar += 10000;
            until TransSettlmentLRec.next = 0;*/

        exit(PurchaseHeaderOrder."No.");
    END;

    Procedure UpdateTransportSettlementArchieve(var TransferSettl: Record "Transfer Settlement"; Var TransContract: Record "Transport Contract Types"; Var DocNoLRec: COde[20]; Var DocLineNo: Integer)
    var
        TransSettlmentLRec: Record "Transfer Settlement";
        LineNoGvar: integer;
        TransSettlmentLedgers: record "Transport Settlement Archieve";


    BEGIN

        TransSettlmentLedgers.reset;
        IF TransSettlmentLedgers.Findlast then
            LineNoGVar := TransSettlmentLedgers."Line No." + 10000
        else
            LineNoGvar := 10000;

        TransSettlmentLRec.reset;
        TransSettlmentLRec.SetRange("Vendor No.", TransferSettl."Vendor No.");
        TransSettlmentLRec.SetRange("Contract Type", TransferSettl."Contract Type");
        TransSettlmentLRec.SetRange("Vehicle No", TransferSettl."Vehicle No");
        if TransSettlmentLRec.findset then
            repeat
                TransSettlmentLedgers.Init();
                TransSettlmentLedgers."Document Type" := TransSettlmentLedgers."Document Type"::Order;
                TransSettlmentLedgers."Document No." := DocNoLRec;
                TransSettlmentLedgers."Document Line No." := DocLineNo;
                TransSettlmentLedgers."Line No." := LineNoGvar;
                TransSettlmentLedgers.insert(true);
                TransSettlmentLedgers.TransferFields(TransContract);
                TransSettlmentLedgers."Transport Settlement No." := TransferSettl."Entry No";
                TransSettlmentLedgers."Trip Date" := TransferSettl."Trip Date";
                TransSettlmentLedgers."Trip Id" := TransferSettl."Trip Id";
                TransSettlmentLedgers."Max Dist. Location" := TransferSettl."Max Dist. Location";
                TransSettlmentLedgers.Modify();
                LineNoGvar += 10000;
            until TransSettlmentLRec.next = 0;

    END;

    procedure UpdateFromTraspLedEntry();
    Var
        TraspLedEntry: Record "Transport Ledger Entries";
        DetaildTraLedEnt: record "Transfer Settlement";
        EntryNo: integer;
        PostdLoaSlipLne: Record "Posted Loading Slip Line";
        DetTransLedgerEntry: Record "Detailed Tran Ledger Entry";
        MaxDistanceValue: Decimal;
        PostLadSlpHdr: Record "Posted Loading SLip Header";
        VehicleTransporter: Record "Transporter Vehicle";
    begin
        if (StartDateGVar = 0D) OR (EndDateGVar = 0D) then
            ERROR('Please select the Start and End dates');
        if CONFIRM('Do you want to Create DetailTransPostLedEntry ?', false) then begin
            DetaildTraLedEnt.reset;
            IF DetaildTraLedEnt.findlast then
                EntryNo := DetaildTraLedEnt."Entry No" + 1
            else
                EntryNo := 1;


            TraspLedEntry.RESET();
            TraspLedEntry.SETRANGE("Trip Date", StartDateGVar, EndDateGVar);
            TraspLedEntry.SETRANGE("Vehicle By", TraspLedEntry."Vehicle By"::Transporter);
            TraspLedEntry.SETRANGE("Contract Type", TraspLedEntry."Contract Type"::Aglevinthis);///B2B.P.K.T
            if TraspLedEntry.FINDSET() then
                repeat
                    TraspLedEntry.CalcFields("Distance Travelled");
                    DetaildTraLedEnt.INIT();
                    DetaildTraLedEnt."Entry No" := EntryNo;
                    DetaildTraLedEnt."Transport Ledger Entry No." := TraspLedEntry."Entry No";
                    DetaildTraLedEnt."Trip Id" := TraspLedEntry."Trip Id";
                    DetaildTraLedEnt."Trip Date" := TraspLedEntry."Trip Date";
                    DetaildTraLedEnt."Vehicle By" := TraspLedEntry."Vehicle By";
                    DetaildTraLedEnt."Vehicle No" := TraspLedEntry."Vehicle No";
                    DetaildTraLedEnt."Vehicle Type" := TraspLedEntry."Vehicle Type";
                    DetaildTraLedEnt."Contract Type" := TraspLedEntry."Contract Type";
                    DetaildTraLedEnt.Distance := TraspLedEntry."Distance Travelled";

                    /*if VehicleTransporter.Get(TraspLedEntry."Vehicle No") then
                        DetaildTraLedEnt."Vendor No." := VehicleTransporter."Vendor No.";*/
                    VehicleTransporter.Reset();
                    VehicleTransporter.SetRange("Vehicle Reg No.", TraspLedEntry."Vehicle No");
                    if VehicleTransporter.FindFirst() then begin
                        DetaildTraLedEnt."Vendor No." := VehicleTransporter."Vendor No.";
                    end;
                    DetTransLedgerEntry.reset;
                    DetTransLedgerEntry.SetRange("Transprt Ledger Entrires", TraspLedEntry."Entry No");
                    DetTransLedgerEntry.SetCurrentKey("Distance Travelled");
                    IF DetTransLedgerEntry.findset then
                        repeat
                            IF (DetTransLedgerEntry."Distance Travelled" >= MaxDistanceValue) THEN
                                MaxDistanceValue := DetTransLedgerEntry."Distance Travelled";

                            DetaildTraLedEnt."From Location" := DetTransLedgerEntry."From Location";
                        //DetaildTraLedEnt."Max Dist. Location" := DetTransLedgerEntry."To Location";
                        until DetTransLedgerEntry.next = 0;

                    PostdLoaSlipLne.reset;
                    PostdLoaSlipLne.SetRange("Document No.", TraspLedEntry."Posted Slip Number");
                    IF PostdLoaSlipLne.findfirst then begin
                        DetaildTraLedEnt."PLS From-Location" := PostdLoaSlipLne."From Location";
                        DetaildTraLedEnt."PLS To-Location" := PostdLoaSlipLne."To Location";
                        DetaildTraLedEnt.Acknowledge := PostdLoaSlipLne.Acknowledge;
                        DetaildTraLedEnt."Acknowledge By" := PostdLoaSlipLne."Acknowledge By";
                        DetaildTraLedEnt."Acknowledge Date" := PostdLoaSlipLne."Acknowledge Date";
                    end;
                    PostLadSlpHdr.reset;
                    PostLadSlpHdr.SetRange("No.", TraspLedEntry."Posted Slip Number");
                    IF PostLadSlpHdr.findfirst then begin
                        DetaildTraLedEnt."CCD No." := PostLadSlpHdr."CCD No.";
                        DetaildTraLedEnt."CCD Acknowledge Date" := PostLadSlpHdr."CCD Acknowledge Date";
                        DetaildTraLedEnt."CCD Acknowledge By" := PostLadSlpHdr."CCD Acknowledge By";
                        DetaildTraLedEnt."SSD No." := PostLadSlpHdr."SSD No.";
                        DetaildTraLedEnt."SSD Acknowledge By" := PostLadSlpHdr."SSD Acknowledge By";
                        DetaildTraLedEnt."SSD Acknowledge Date" := PostLadSlpHdr."SSD Acknowledge Date";
                    end;

                    EntryNo += 1;
                    DetaildTraLedEnt.INSERT();
                until TraspLedEntry.NEXT() = 0;
            CurrPage.Update();
        end;
    end;

    var
        StartDateGVar: Date;
        EndDateGVar: Date;
        TransContracVehicle: Record "Transport Contract Types";
}