page 50133 "Promo. Group Subform"
{
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = ListPart;
    SourceTable = "Promo Schedule Line";
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field(Type; Type)
                {
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field("Minimum Qty."; "Minimum Qty.")
                {
                    ApplicationArea = all;
                }
                field("Retail Promo"; "Retail Promo")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field(Active; Active)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50132. Unsupported part was commented. Please check it.
                        /*CurrPage.PromoSchdGrpLines.FORM.*/
                        _ShowDimensions;

                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::"Promo Group";
    end;

    var
        ChangeExchangeRate: Page "Change Exchange Rate";

    procedure ShowOfferLines();
    begin
        ShowPromoOfferLines;
    end;

    procedure _ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;
}

