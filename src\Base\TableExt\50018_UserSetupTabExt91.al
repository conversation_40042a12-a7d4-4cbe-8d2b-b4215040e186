/// <summary>
/// TableExtension UserSetupTabExt91 (ID 50018) extends Record User Setup.
/// </summary>
tableextension 50018 UserSetupTabExt91 extends "User Setup"
{
    fields
    {
        field(50000; "Gen. Jouranl Line Resp. Centre"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center";
        }
        field(50001; "Edit Max Fuel Availed"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Approve Transpoter Vehicle"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Request for Teller Receipt"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "Teller/Cheque Awaiting Confirm"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Teller/Cheque Awaiting BRV"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Post Bank Confirmation Reg."; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Reprint Dispatch"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Edit Loading Advice Qty."; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50009; "Name"; Text[250])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "Post Multiple Rebate Cr.Memos"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Upload Productivity Days App."; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "Process Redistribution App."; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "Reprint Branch Report"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Suppl Chain Approval Rights"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "BrCrMemoSend App"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Create Rebate Cred. Memos"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Revise Capex Budget"; boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50018; "Customer Attachment Delete"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //>>B2BSB.1.0

        field(50130; "QC Checked (BAD Location)"; Boolean)
        {
            DataClassification = CustomerContent;
        }

        field(50132; "Voucher Resp. Ctr. Filter"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50133; "FA Req. Amt. Appr. Limit"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50134; "Unlimited FA Req. Approval"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50135; "MRS-Sample Resp. Ctr. Filter"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50136; "FA Req. Resp. Ctr. Filter"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50137; "Service Request Resp. Ctr. Ftr"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50138; "MDV Resp. Ctr. Filter"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50139; "WrkOrdReq Resp. Ctr. Filter"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50140; "Prj. Req. Resp. Ctr. Filter"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center".Code;
        }
        field(50141; "Release Prod. Planning"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50142; "Release Prod. Capture"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50143; "Create Prod. order"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50144; "Release Stock Capture"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50145; "Store Type"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Category";
        }
        field(50146; "Get Prod. MRS Notification"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50147; "SCD Checked"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50148; "CCD Checked"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50149; "Short Close Checked"; Boolean)
        {
            DataClassification = CustomerContent;
        }

        field(50153; "Enter Excess PCR Count"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50154; "Approve Fa Mvt Reg"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50155; "FA Req. To BUH Approval"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50156; "Validate Vendor Classification"; Boolean)
        {
            DataClassification = CustomerContent;

        }
        field(50157; "Cashier Permission"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'HO1.0';
        }
        field(50158; "Edit Chaeque/Teller No."; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'HO1.0';
        }
        field(50159; "View Indirect Bank Receipts"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
        }
        field(50160; "View Main Cash Vouchers"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
        }
        field(50161; "View Direct Bank Receipts"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
        }
        field(50162; "Reprint Payment Documents"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50163; "Reprint Receipt Documents"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50165; "AllowPostingDateModify"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50170; "CCD Mail Alert"; Boolean)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            Begin
                TestField("E-Mail");
            End;
        }
        field(50171; "Resend Branch Request"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50172; "Undo Shipment"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50173; "Undo Receipt"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50174; "Ack. Residence Diesel"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50175; "Create Item from template"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50176; "Edit Master Data Template"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50177; "Create Master Data Approval"; Boolean)
        {
            Description = 'SAA_04122017_MDM';
            DataClassification = CustomerContent;
        }
        field(50178; "Modify Master Data Approval"; Boolean)
        {
            Description = 'SAA_04122017_MDM';
            DataClassification = CustomerContent;
        }
        field(50198; "Release Service & Rate"; Boolean)
        {
            Description = 'Service';//Service08Jul2021
            DataClassification = CustomerContent;
        }
        field(50209; "Edit PMS Entries"; Boolean)
        {
            Description = 'Edit PMS Entries';//PMS Editable
            DataClassification = CustomerContent;
        }
        field(50202; "Reverse BRS"; Boolean)
        {
            Description = 'Reverse BRS';
            DataClassification = CustomerContent;
        }
        field(60259; "Mark-Scrap App"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(60260; "Direct Posting"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(60261; "Ammend Br Send"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(60263; "Allow Inv. Posting From"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(60264; "Allow Inv. Posting To"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(60265; "View Petty Cash Vouchers"; Boolean)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(60270; "Sales Order Modify"; Boolean)
        {
            DataClassification = customerContent;
        }
        field(60272; "Show Item Proft & Othr Details"; Boolean)
        {
            DataClassification = customerContent;
            //b2bpksalecorr10 end
            Caption = 'Show Sales Profit & Othr Details';
        }
        field(60274; "Reprint Shipmt & GatePass Docs"; Boolean)
        {
            DataClassification = customerContent;
            //b2bpksalecorr12 end
        }
        field(60275; "Reprint Invoiced & Credm Docs"; Boolean)
        {
            DataClassification = customerContent;
            //b2bpksalecorr12 end
        }
        field(60230; "Send Customer History Mail"; Boolean)
        {
            DataClassification = customerContent;
            Caption = 'Send Customer Statement Mail';
            //b2bpksalecorr13 end
        }
        field(60231; "Process KD"; Boolean)
        {
            DataClassification = customerContent;
            //b2bpksalecorr13 end
        }
        field(60232; "Delete RBS Right side data"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(60233; "Reopen Receipt Page"; Boolean)
        {
            DataClassification = CustomerContent;
        }//Fix22Feb2021
        field(60235; "Create BRS Out. Dupl Entrie"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(60236; "Prod. MRS Mail Alert"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(60237; "Capt. Prod Qty. Mail Alert"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(60238; Password; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(60239; "Retail User"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65000; Location; Code[20])
        {
            Description = 'GJ_CHI_RKD_091013';
            TableRelation = Location;
        }
        field(65001; "MIS Approval"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //Balu 05132021>>
        field(50179; "Reprint Res. Gpass"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50180; "Material Loading Reprint"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //Balu 05132021<<
        field(50181; "Delete No. Series"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50182; "Open Sales Invoice Page"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50183; "ReOpen App Sales order"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50184; "Short Close Trans. Order"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50188; "Bin Reclassification"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50189; "Open Sales Order Page"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50190; "open Sales Quote Page"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50191; "Open Purchase Order Page"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50192; "Open Purchase Invoice Page"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50193; "Open Purchase Quote page"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50194; "Employee ID"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Employee."No.";
        }
        field(50195; "Employee Acc. Loc"; Code[20])
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup (Employee."Global Dimension 1 Code" WHERE("No." = FIELD("Employee ID")));

        }
        field(50196; "Employee CC. Code"; Code[20])
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup (Employee."Global Dimension 2 Code" WHERE("No." = FIELD("Employee ID")));
        }
        field(50197; Signature; BLOB)
        {
            Caption = 'Signature';
            SubType = Bitmap;
        }
        field(50199; "Send E-mail to Cust Statement"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50200; State; Option)
        {
            OptionMembers = Enable,Disable;
            FieldClass = FlowField;
            CalcFormula = lookup (User.State where("User Name" = field("User ID")));
        }
        field(50201; "Reverse G/L Entry"; Boolean) //PKONJU27
        {
            DataClassification = CustomerContent;
        }
        field(50210; "Delete Approval Entries"; Boolean) //PKONAU11
        {
            DataClassification = CustomerContent;
        }
        field(50204; "Open Released Import PO"; Boolean) //PKONAU20
        {
            DataClassification = CustomerContent;
        }
        field(50205; "Open Released Local PO"; Boolean) //PKONAU20
        {
            DataClassification = CustomerContent;
        }
        field(50206; "Open Released PMS Voucher"; Boolean) //PKONAU20
        {
            DataClassification = CustomerContent;
        }

        field(50207; "FG Bin Assign"; Text[100])//PKONDE16
        {
            DataClassification = CustomerContent;
            //TableRelation = "Bin Content"."Bin Code" where("Location Code" = const('FGDESPATCH'));
        }
        //B2BMSOn17Jan2022>>
        field(50208; "Open Staff Bank Pmt Voucher"; Boolean)
        {
            Caption = 'Open Staff Bank Payment Voucher';
            DataClassification = CustomerContent;
        }
        //B2BMSOn17Jan2022<<
        //Baluon Apr 18 2022>>
        field(50052; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50053; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50054; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50058; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //Baluon Apr 18 2022<<
        field(50059; "HOD Product Complaint"; Boolean)//PKON22J2-CR220070
        {
            DataClassification = CustomerContent;

        }
        //Go2solve March 28 2023 >>>>>
        field(50060; SVAlert; Boolean)
        {
            Caption = 'Sales Value Alert';
            DataClassification = ToBeClassified;
        }
        //Go2solve March 28 2023 <<<<<

        //Go2solve March 28 2023 <<
        field(50061; "Can rec. Outreach Job Notif?"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        //>>>>>> G2S Aug 1 2023
        //<<<<< G2S October 31 2023
        field(50062; "Can Modify Prod. Order"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        //<<<<< G2S October 31 2023
        //Project Leap >>>>>
        field(50063; "Can Close Production MRS"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(50064; "Can Reopen Closed Prod. MRS"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(50065; CHIERP_AllowValidPeriodChange; Boolean)
        {
            DataClassification = CustomerContent;
            Caption = 'Allow Valid Period Change';
        }
        //Project Leap <<<<<
        //<<<<<< G2S CAS-01312-L5Q5B8 6/21/2024
        field(50066; "Delete Production Order"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        //>>>>>> G2S CAS-01312-L5Q5B8 6/21/2024
        // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024 
        field(50067; "Can Access Local Sales Doc"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(50068; "Can Access Direct Sales Doc"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(50069; "Can Access Export Sales Doc"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024
        //<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
        field(50070; "Modify Purchase Document"; Boolean)
        {
            Caption = 'Modify Purchase Document';
            DataClassification = ToBeClassified;
        }
        //>>>>>> G2S CAS-01334-J2M7C2 8/30/2024
        field(50500; CHI_ERP_ViewCommercialDetails; Boolean)
        {
            DataClassification = CustomerContent;
            Caption = 'View Commercial Details';
        }
        field(50501; "Can Use Transfer Ticket"; Boolean)
        {
            Caption = 'Can Use Transfer Ticket Type';
            DataClassification = ToBeClassified;
        }
        // >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9
        field(50502; "CHIERP_Complimentary Sales"; Boolean)
        {
            DataClassification = CustomerContent;
            Caption = 'CHIERP_Complimentary Sales Order';
        }
        // >>>>>>G2S 14/01/25 CAS-01388-C9P4S5
        field(50504; "Delete Sales Order"; Boolean)
        {
            Caption = 'Delete Sales Order';
            DataClassification = ToBeClassified;
        }
        field(50606; "Staff ID in StoreApp"; Text[20])
        {
            Caption = 'Staff Id in StoreApp';
            DataClassification = ToBeClassified;
        }
        // >>>>>>G2S 14/01/25 CAS-01388-C9P4S5
        // >>>>>> G2S 13/12/2024  CAS-01381-Y2Z6B9
        field(50505; "User Created Date"; DateTime)
        {
            Caption = 'User Created Date';
            Editable = false;
            DataClassification = ToBeClassified;
        }
        field(50506; "User Last Date Modified"; DateTime)
        {
            Caption = 'User Last Date Modified';
            Editable = false;
            DataClassification = ToBeClassified;
        }
        // >>>>>> G2S 13/12/2024  CAS-01381-Y2Z6B9
    }
    procedure FilterResponsibilityCenter(): Text[500]
    var
        UserIDRespCent: Record "UserID Resp. Cent. Lines";
        BuildFilter: Text[500];
        RespCentCount: Integer;
    begin

        RespCentCount := 0;
        BuildFilter := '';

        UserIDRespCent.SETCURRENTKEY("User ID", "Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
            REPEAT
                RespCentCount += 1;
                IF RespCentCount = 1 THEN
                    BuildFilter := UserIDRespCent."Resp. Center Code"
                ELSE
                    BuildFilter += '|' + UserIDRespCent."Resp. Center Code";
            UNTIL UserIDRespCent.NEXT = 0;

        IF RespCentCount > 0 THEN
            EXIT(BuildFilter) ELSE
            EXIT('');
    end;
}