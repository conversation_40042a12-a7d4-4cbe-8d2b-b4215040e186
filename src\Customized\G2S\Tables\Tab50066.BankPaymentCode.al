///G2SProvidus Integration 7th Aug 2024 
table 50385 "Bank Payment Code"
{
    Caption = 'Bank Payment Code';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Entry No"; Integer)
        {
            Caption = 'Entry No';
            AutoIncrement = true;
        }
        field(2; "Providus Bank Code"; Code[20])
        {
            Caption = 'Providus Bank Code';
        }
        field(3; "Bank Name"; Code[50])
        {
            Caption = 'Bank Name';
        }
        field(4; "Bank Routing Code"; Code[50])
        {
            Caption = 'Bank Routing Code';
            TableRelation = "Bank Routing Code";
            trigger OnValidate()
            var
                BankRtngCode: Record "Bank Routing Code";
            begin
                BankRtngCode.SetRange("Routing Code", Rec."Bank Routing Code");
                if BankRtngCode.FindFirst() then
                    Rec."Bank Name" := BankRtngCode."Bank Name";
            end;
        }
        field(5; "First Bank Code"; Code[20])
        {
            Caption = 'First Bank Code';
        }
        field(6; "Zenith Bank Code"; Code[20])
        {
            Caption = 'Zenith Bank Code';
        }

    }
    keys
    {
        key(PK; "Entry No")
        {
            Clustered = true;
        }
    }
}
///Providus Integration 7th Aug 2024 
