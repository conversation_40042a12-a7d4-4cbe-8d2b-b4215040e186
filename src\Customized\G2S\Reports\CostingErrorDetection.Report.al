Report 50460 "Costing Error Detection"
{
    DefaultLayout = RDLC;
    RDLCLayout = './src/Customized/G2S/Reports/Layouts/Costing Error Detection.rdlc';
    Caption = 'Costing Error Detection';
    PreviewMode = PrintLayout;
    Permissions = tabledata "Value Entry" = RM;
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    dataset
    {
        dataitem(ErrorGroup; "Integer")
        {
            DataItemTableView = sorting(Number);
            PrintOnlyIfDetail = true;
            column(ReportForNavId_5414; 5414)
            {
            }
            column(CompanyName; COMPANYNAME)
            {
            }
            column(Today; Format(Today, 0, 4))
            {
            }
            column(UserID; UserId)
            {
            }
            column(ShowCaption; ShowCaption)
            {
            }
            column(Number_ErrorGroup; ErrorGroup.Number)
            {
            }
            dataitem(Item; Item)
            {
                PrintOnlyIfDetail = true;
                RequestFilterFields = "No.";
                column(ReportForNavId_8129; 8129)
                {
                }
                column(No_Item; Item."No.")
                {
                }
                column(Description_Item; Item.Description)
                {
                    //DecimalPlaces = 0:0;
                    IncludeCaption = true;
                }
                dataitem(ItemCheck; Item)
                {
                    DataItemLink = "No." = field("No.");
                    DataItemTableView = sorting("No.");
                    column(ReportForNavId_9273; 9273)
                    {
                    }
                    dataitem(ItemErrors; "Integer")
                    {
                        DataItemTableView = sorting(Number);
                        column(ReportForNavId_2059; 2059)
                        {
                        }
                        column(ErrorText_ItemErrors; ErrorText[ItemErrors.Number])
                        {
                        }
                        column(Number_ItemErrors; ItemErrors.Number)
                        {
                        }

                        trigger OnPreDataItem()
                        begin
                            ItemErrors.SetRange(ItemErrors.Number, 1, CompressArray(ErrorText));
                        end;
                    }

                    trigger OnAfterGetRecord()
                    begin
                        ClearErrorText;

                        case ErrorGroupIndex of
                            0:
                                CheckItem(ItemCheck."No.");
                        end;

                        if CompressArray(ErrorText) = 0 then
                            CurrReport.Skip;
                    end;
                }
                dataitem("Item Ledger Entry"; "Item Ledger Entry")
                {
                    DataItemLink = "Item No." = field("No.");
                    DataItemTableView = sorting("Item No.");
                    column(ReportForNavId_7209; 7209)
                    {
                    }
                    column(EntryNo_ItemLedgerEntry; "Item Ledger Entry"."Entry No.")
                    {
                        IncludeCaption = true;
                    }
                    column(EntryType_ItemLedgerEntry; "Item Ledger Entry"."Entry Type")
                    {
                        IncludeCaption = true;
                    }
                    column(EntryTypeFormat_ItemLedgerEntry; Format("Item Ledger Entry"."Entry Type"))
                    {
                    }
                    column(ItemNo_ItemLedgerEntry; "Item Ledger Entry"."Item No.")
                    {
                        IncludeCaption = true;
                    }
                    column(Quantity_ItemLedgerEntry; "Item Ledger Entry".Quantity)
                    {
                        IncludeCaption = true;
                    }
                    column(RemainingQuantity_ItemLedgerEntry; "Item Ledger Entry"."Remaining Quantity")
                    {
                        IncludeCaption = true;
                    }
                    column(Positive_ItemLedgerEntry; "Item Ledger Entry".Positive)
                    {
                        IncludeCaption = true;
                    }
                    column(Open_ItemLedgerEntry; "Item Ledger Entry".Open)
                    {
                        IncludeCaption = true;
                    }
                    column(PostingDate_ItemLedgerEntry; "Item Ledger Entry"."Posting Date")
                    {
                        IncludeCaption = true;
                    }
                    dataitem(Errors; "Integer")
                    {
                        DataItemTableView = sorting(Number);
                        column(ReportForNavId_6545; 6545)
                        {
                        }
                        column(ErrorText_Errors; ErrorText[Errors.Number])
                        {
                        }
                        column(Number_Errors; Errors.Number)
                        {
                        }

                        trigger OnPreDataItem()
                        begin
                            Errors.SetRange(Errors.Number, 1, CompressArray(ErrorText))
                        end;
                    }

                    trigger OnAfterGetRecord()
                    begin
                        Window.Update(3, "Item Ledger Entry"."Entry No.");
                        ClearErrorText;
                        case ErrorGroupIndex of
                            0:
                                CheckBasicData;
                            1:
                                CheckValuationDate;
                        end;

                        if CompressArray(ErrorText) = 0 then
                            CurrReport.Skip;
                    end;

                    trigger OnPreDataItem()
                    begin
                        case ErrorGroupIndex of
                            0:
                                ;//Basic Data Test
                            1://Check Valuation Date
                                begin
                                    "Item Ledger Entry".SetRange("Item Ledger Entry".Positive, true);
                                    ItemApplEntry.Reset;
                                    ItemApplEntry.SetCurrentkey("Inbound Item Entry No.");
                                end;

                        end;
                    end;
                }
                dataitem("Value Entry"; "Value Entry")
                {
                    DataItemLink = "Item No." = field("No.");
                    DataItemTableView = sorting("Item No.", "Posting Date", "Item Ledger Entry Type", "Entry Type", "Variance Type", "Item Charge No.", "Location Code", "Variant Code");
                    column(ReportForNavId_8894; 8894)
                    {
                    }
                    column(EntryNo_ValueEntry; "Value Entry"."Entry No.")
                    {
                    }
                    column(ItemNo_ValueEntry; "Value Entry"."Item No.")
                    {
                    }
                    column(ItemLedgerEntryType_ValueEntry; "Value Entry"."Item Ledger Entry Type")
                    {
                    }
                    column(PostingDate_ValueEntry; "Value Entry"."Posting Date")
                    {
                    }
                    column(ItemLedgerEntryQuantity_ValueEntry; "Value Entry"."Item Ledger Entry Quantity")
                    {
                    }

                    trigger OnAfterGetRecord()
                    var
                        ItemLedgEntry: Record "Item Ledger Entry";
                    begin
                        if ItemLedgEntry.Get("Value Entry"."Item Ledger Entry No.") then
                            CurrReport.Skip;

                        if Item2.Get("Value Entry"."Item No.") then begin
                            ItemTemp := Item2;
                            if ItemTemp.Insert then;
                        end;
                    end;

                    trigger OnPreDataItem()
                    begin
                        if (ErrorGroupIndex <> 8) or (Item."No." = '') then
                            CurrReport.Break;

                        if not CheckItemLedgEntryExists then
                            CurrReport.Break;
                    end;
                }

                trigger OnAfterGetRecord()
                begin
                    if Item.Type = Item.Type::Service then
                        CurrReport.Skip;

                    Window.Update(2, Item."No.");
                end;

                trigger OnPreDataItem()
                begin
                    if ErrorGroupIndex in [3, 4] then begin
                        if CostingMethodFiltered then
                            CurrReport.Break
                        else
                            Item.SetRange(Item."Costing Method", Item."costing method"::Average);
                    end else
                        Item.SetRange(Item."Costing Method");
                end;
            }

            trigger OnAfterGetRecord()
            begin
                ErrorGroupIndex := ErrorGroup.Number;
                case ErrorGroupIndex of
                    0:
                        Window.Update(1, Text023);
                    1:
                        Window.Update(1, Text005);
                end;
            end;

            trigger OnPreDataItem()
            begin
                ErrorGroup.SetRange(ErrorGroup.Number, 0, 1);
            end;
        }
        dataitem(Summary; "Integer")
        {
            DataItemTableView = sorting(Number);
            column(ReportForNavId_5444; 5444)
            {
            }
            column(ItemSummaryCaption; Text047)
            {
            }
            column(No_Summary; ItemTemp."No.")
            {
            }
            column(Description_Summary; ItemTemp.Description)
            {
            }
            column(Number_Summary; Summary.Number)
            {
            }

            trigger OnAfterGetRecord()
            begin
                if Summary.Number = 1 then
                    ItemTemp.FindFirst
                else
                    ItemTemp.Next;
            end;

            trigger OnPreDataItem()
            begin
                Summary.SetRange(Summary.Number, 1, ItemTemp.Count);
            end;
        }
    }

    requestpage
    {
        SaveValues = true;
    }

    labels
    {
        ReportName = 'Costing Error Detection - Outbound Valuation';
        PageNoCaption = 'Page';
    }

    trigger OnPostReport()
    begin
        Window.Close;
    end;

    trigger OnPreReport()
    var
        TempItem: Record Item temporary;
    begin
        if not "Item Ledger Entry".Find('-') then
            Error(Text001);

        Window.Open(Text020 + Text021 + Text022);

        TempItem.SetRange("Costing Method", TempItem."costing method"::Average);
        if Item.GetFilter("Costing Method") <> '' then
            if Item.GetFilter("Costing Method") <> TempItem.GetFilter("Costing Method") then
                CostingMethodFiltered := true;
    end;

    var
        ItemApplEntry: Record "Item Application Entry";
        Item2: Record Item;
        ItemTemp: Record Item temporary;
        ErrorGroupIndex: Integer;
        ErrorIndex: Integer;
        BasicDataTest: Boolean;
        QtyCheckItemLedgEntry: Boolean;
        ApplicationQtyCheck: Boolean;
        ValuedByAverageCheck: Boolean;
        ValuationDateCheck: Boolean;
        Text000: label 'You must select one or more of the data checks.';
        Text001: label 'There are no Item Ledger Entries to check.';
        Text002: label 'Item Ledger Entry - Item Appl. Entry Qty. Check';
        Text003: label 'Application Quantity Check';
        Text005: label 'Check Valuation Date';
        Text006: label 'Check Valued By Average Cost';
        CostingMethodFiltered: Boolean;
        ErrorText: array[500] of Text[250];
        Text007: label 'An %1 exists even though the %2 and %3 are the same in this negative Item Ledger Entry.';
        Text008: label 'There is more than one %1 with the same combination of %2, %3 and %4.';
        Text009: label 'The %1 is greater than the %1.';
        Text010: label 'The summed %1 of the linked Item Application Entries is not the same as the difference between %2 and %3.';
        Text011: label 'The summed %1 of the linked Item Application Entries is not the same as the %2.';
        Text012: label 'The sign of the %1 in one or more of the linked Item Application Entries must be the opposite (positive must be negative or negative must be positive).';
        Text013: label 'The summed %1 of the linked Item Application Entries is different than the %2 of the %3.';
        Text014: label 'There are no Value Entries for this %1.';
        Text015: label 'The summed %1 of the Item Application Entries for this transfer do not add up to zero as they should.';
        Text016: label 'The Value Entries linked to the Item Application Entries do not all have the same %1 or %2.';
        Text017: label 'The %1 in Value Entries applied to this %2 is earlier than the %1 in the %3 for this %2. %4, %5, %6';
        Text018: label 'The value of the %1 field in the corresponding Value Entries is not correct.';
        Text019: label 'The value of the %1 field in the corresponding Item Application Entries is not correct.';
        Window: Dialog;
        Text020: label 'Function          #1##########################\';
        Text021: label 'Item              #2##########################\';
        Text022: label 'Item Ledger Entry #3##########################';
        Text023: label 'Basic Data Test';
        Text024: label 'The %1 must not be 0.';
        Text025: label '%1 must be %2.';
        Text026: label '%1 must be %2.';
        Text027: label 'The linked Value Entries do not all have the same value of %1.';
        Text028: label 'A linked %1 should not have the %2 %3 for a positive entry with %4 Consumption.';
        Text029: label '%1 of Value Entries must not be Yes for any %2 other than Average.';
        Text030: label 'There are no Item Application Entries for this %1.';
        RemExpectedOnClosedEntry: Boolean;
        Text031: label 'Check Expected Cost on a closed entry';
        Text032: label 'Expected Cost Amount was not 0 on an invoiced entry';
        OutputCompletelyInvd: Boolean;
        Text033: label 'Check Output Completely Invoiced Date';
        Text034: label 'At least one linked Item Application Entry has an unspecified %1.';
        Text035: label 'A blank date in %1 was found on an Item Ledger Entry which has been invoiced';
        CheckItemLedgEntryExists: Boolean;
        Text036: label 'Value Entries with missing Item Ledger Entry';
        Text037: label 'Check Exptd. Cost on Completely Invoiced entries';
        Text038: label 'Check Completely Invoiced date';
        Text039: label '%1 was different to %2 on a linked Item Application Entry.';
        Text040: label '%1 was specified on a linked Item Application Entry but the Item Ledger Entry is not Completely Invoiced.';
        Text041: label '%1 was not equal to %2 on a linked Item Application Entry.';
        Text042: label 'The summed %1 of the linked Value Entries is not the same as the %2.';
        Text043: label '%1 or %2 was not 0 on a linked Value Entry. However this could be because report 1002 has not been run yet.';
        Text044: label '%1 must be 0 when %2 is true.';
        Text045: label '%1 or %2 is not 0 on a Completely Invoiced Item Ledger Entry';
        Text046: label '%1 must equal %2 when %3 is true.';
        Text047: label 'Item Summary';
        Text048: label '%1 must be 0 on %2 %3 when %4 is true on a %5';
        Text049: label '%1 must be false when %2 > 0 and %3 is %4 on %5 %6';
        Text050: label '%1 on a %2 is not equal to %3 on %4 %5';
        Text051: label '%1 on a %2 must not be smaller than or equal to 0.';
        Text052: label '%1 must not be blank on a %2.';


    procedure ShowCaption(): Text[50]
    begin
        //ShowCaption
        case ErrorGroupIndex of
            0:
                exit(Text023);
            1:
                exit(Text005);

        end;
    end;


    procedure ClearErrorText()
    begin
        //ClearErrorText
        if CompressArray(ErrorText) <> 0 then
            for ErrorIndex := 1 to CompressArray(ErrorText) do
                ErrorText[ErrorIndex] := '';
        ErrorIndex := 1;
    end;


    procedure CheckBasicData()
    begin
        //CheckBasicData
        BasicCheckItemLedgEntry;
        BasicCheckValueEntry;
    end;


    procedure BasicCheckItemLedgEntry()
    var
        ValueEntry: Record "Value Entry";
    begin
        //BasicCheckItemLedgEntry
        begin
            if "Item Ledger Entry"."Entry No." <= 0 then
                AddError(StrSubstNo(Text051, "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Entry No."), "Item Ledger Entry".TableCaption), "Item Ledger Entry"."Item No.");
            if "Item Ledger Entry".Quantity = 0 then begin
                AddError(StrSubstNo(Text024, "Item Ledger Entry".FieldCaption("Item Ledger Entry".Quantity)), "Item Ledger Entry"."Item No.");
            end else begin
                if ("Item Ledger Entry".Quantity * "Item Ledger Entry"."Remaining Quantity") < 0 then begin
                    AddError(StrSubstNo(Text009, "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Remaining Quantity"), "Item Ledger Entry".FieldCaption("Item Ledger Entry".Quantity)), "Item Ledger Entry"."Item No.");
                end else
                    if Abs("Item Ledger Entry"."Remaining Quantity") > Abs("Item Ledger Entry".Quantity) then begin
                        AddError(StrSubstNo(Text009, "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Remaining Quantity"), "Item Ledger Entry".FieldCaption("Item Ledger Entry".Quantity)), "Item Ledger Entry"."Item No.");
                    end;
                if ("Item Ledger Entry".Quantity > 0) <> "Item Ledger Entry".Positive then begin
                    AddError(StrSubstNo(Text025, "Item Ledger Entry".FieldCaption("Item Ledger Entry".Positive), not "Item Ledger Entry".Positive), "Item Ledger Entry"."Item No.");
                end;
            end;
            if ("Item Ledger Entry"."Remaining Quantity" = 0) = "Item Ledger Entry".Open then begin
                AddError(StrSubstNo(Text026, "Item Ledger Entry".FieldCaption("Item Ledger Entry".Open), not "Item Ledger Entry".Open), "Item Ledger Entry"."Item No.");
                ;
            end;

            if "Item Ledger Entry"."Completely Invoiced" then begin
                if "Item Ledger Entry"."Invoiced Quantity" <> "Item Ledger Entry".Quantity then begin
                    AddError(
                      StrSubstNo(Text046, "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Invoiced Quantity"), "Item Ledger Entry".FieldCaption("Item Ledger Entry".Quantity), "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Completely Invoiced")),
                      "Item Ledger Entry"."Item No.");
                end;

                ValueEntry.SetCurrentkey("Item Ledger Entry No.");
                ValueEntry.SetRange("Item Ledger Entry No.", "Item Ledger Entry"."Entry No.");
                ValueEntry.CalcSums("Invoiced Quantity");
                if "Item Ledger Entry"."Invoiced Quantity" <> ValueEntry."Invoiced Quantity" then begin
                    AddError(
                      StrSubstNo(Text042, ValueEntry.FieldCaption("Invoiced Quantity"), "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Invoiced Quantity")),
                      "Item Ledger Entry"."Item No.");
                end;
            end;
        end;
    end;


    procedure BasicCheckValueEntry()
    var
        ValueEntry: Record "Value Entry";
        ValuationDate: Date;
        ConsumptionDate: Date;
        ValuedByAverageCost: Boolean;
        Continue: Boolean;
        Compare: Boolean;
    begin
        //BasicCheckValueEntry
        begin
            ValueEntry.SetCurrentkey("Item Ledger Entry No.");
            ValueEntry.SetRange("Item Ledger Entry No.", "Item Ledger Entry"."Entry No.");
            ValueEntry.SetRange(Inventoriable, true);
            if not ValueEntry.Find('-') then begin
                AddError(StrSubstNo(Text014, "Item Ledger Entry".TableCaption), "Item Ledger Entry"."Item No.");
            end else begin
                ValuedByAverageCost := ValueEntry."Valued By Average Cost";
                repeat
                    if ValueEntry.Adjustment then begin
                        if ValueEntry."Invoiced Quantity" <> 0 then begin
                            AddError(StrSubstNo(Text044, ValueEntry.FieldCaption("Invoiced Quantity"), ValueEntry.FieldCaption(Adjustment)),
                            "Item Ledger Entry"."Item No.");
                            Continue := true;
                        end;
                        if ValueEntry."Item Ledger Entry Quantity" <> 0 then begin
                            AddError(
                              StrSubstNo(Text048, "Item Ledger Entry".FieldCaption(Quantity),
                                "Item Ledger Entry".TableCaption, "Item Ledger Entry"."Entry No.",
                                ValueEntry.FieldCaption(Adjustment), ValueEntry.TableCaption),
                                "Item Ledger Entry"."Item No.");
                        end;
                    end;
                    if ("Item Ledger Entry"."Entry Type" = "Item Ledger Entry"."entry type"::Consumption) and "Item Ledger Entry".Positive and
                       (ValueEntry."Valuation Date" = Dmy2date(31, 12, 9999))
                    then begin
                        ConsumptionDate := Dmy2date(31, 12, 9999);
                        AddError(
                          StrSubstNo(Text028, ValueEntry.TableCaption, ValueEntry.FieldCaption("Valuation Date"), ConsumptionDate,
                          "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Entry Type")),
                          "Item Ledger Entry"."Item No.");
                        Continue := true;
                    end else begin
                        if (not Compare) and (ValueEntry."Valuation Date" <> 0D) and
                           not (ValueEntry."Entry Type" in [ValueEntry."entry type"::Rounding, ValueEntry."entry type"::Revaluation])
                        then begin
                            ValuationDate := ValueEntry."Valuation Date";
                            Compare := true;
                        end;
                        if Compare then
                            if (ValueEntry."Valuation Date" <> ValuationDate) and (ValueEntry."Valuation Date" <> 0D) and
                               not (ValueEntry."Entry Type" in [ValueEntry."entry type"::Rounding, ValueEntry."entry type"::Revaluation])
                            then begin
                                AddError(StrSubstNo(Text027, ValueEntry.FieldCaption("Valuation Date")), "Item Ledger Entry"."Item No.");
                                Continue := true;
                            end;
                    end;
                    if (ValueEntry."Valued By Average Cost") and
                       (Item."Costing Method" <> Item."costing method"::Average)
                    then begin
                        AddError(StrSubstNo(Text029, ValueEntry.FieldCaption("Valued By Average Cost"), Item.FieldCaption("Costing Method")),
                        "Item Ledger Entry"."Item No.");
                        Continue := true;
                    end else
                        if ValueEntry."Valued By Average Cost" <> ValuedByAverageCost then begin
                            AddError(StrSubstNo(Text027, ValueEntry.FieldCaption("Valued By Average Cost")), "Item Ledger Entry"."Item No.");
                            Continue := true;
                        end;

                    if (ValueEntry."Valued By Average Cost") and
                       (Item."Costing Method" = Item."costing method"::Average) and
                       (not "Item Ledger Entry".Correction)
                     then
                        if ValueEntry."Valued Quantity" > 0 then begin
                            AddError(StrSubstNo(Text049,
                              ValueEntry.FieldCaption("Valued By Average Cost"),
                              ValueEntry.FieldCaption("Valued Quantity"),
                              Item.FieldCaption("Costing Method"),
                              Format(Item."Costing Method"),
                              ValueEntry.TableCaption,
                              ValueEntry."Entry No."),
                              "Item Ledger Entry"."Item No.");
                        end;

                    if ValueEntry."Item Charge No." = '' then
                        if ValueEntry."Item Ledger Entry Type" <> "Item Ledger Entry"."Entry Type" then begin
                            AddError(StrSubstNo(Text050,
                              ValueEntry.FieldCaption("Item Ledger Entry Type"),
                              ValueEntry.TableCaption,
                              "Item Ledger Entry".FieldCaption("Item Ledger Entry"."Entry Type"),
                              "Item Ledger Entry".TableCaption,
                              "Item Ledger Entry"."Entry No."),
                              "Item Ledger Entry"."Item No.");
                        end;
                until (ValueEntry.Next = 0) or Continue;
            end;
        end;
    end;

    procedure CheckValuationDate()
    var
        ValueEntry: Record "Value Entry";
        ValuationDate: Date;
        Continue: Boolean;
    begin
        //CheckValuationDate
        begin
            ValueEntry.SetCurrentkey("Item Ledger Entry No.");
            ValueEntry.SetRange("Item Ledger Entry No.", "Item Ledger Entry"."Entry No.");
            ValueEntry.SetRange(Inventoriable, true);
            ValueEntry.SetRange("Partial Revaluation", false);
            if ValueEntry.Find('-') then begin
                ValuationDate := ValueEntry."Valuation Date";
                ItemApplEntry.SetRange("Inbound Item Entry No.", "Item Ledger Entry"."Entry No.");
                if ItemApplEntry.Find('-') then
                    repeat
                        if ItemApplEntry.Quantity < 0 then begin
                            ValueEntry.SetRange("Item Ledger Entry No.", ItemApplEntry."Item Ledger Entry No.");
                            if ValueEntry.Find('-') then
                                repeat
                                    if ValueEntry."Valuation Date" < ValuationDate then begin
                                        AddError(StrSubstNo(Text017, ValueEntry.FieldCaption("Valuation Date"), "Item Ledger Entry".TableCaption, ValueEntry.TableCaption
                                        , ValueEntry."Entry No.", ValueEntry."Valuation Date", ValuationDate),
                                          "Item Ledger Entry"."Item No.");
                                        //Continue := true;
                                        ValueEntry."Valuation Date" := ValuationDate;
                                        ValueEntry.Modify();
                                    end;
                                until (ValueEntry.Next = 0) or Continue;
                        end;
                    until (ItemApplEntry.Next = 0) or Continue;
            end;
        end;
    end;

    procedure CheckItem(ItemNo: Code[20])
    begin
        if ItemNo = '' then
            AddError(StrSubstNo(Text052, Item.FieldCaption("No."), Item.TableCaption), ItemNo);
    end;


    procedure AddError(ErrorMessage: Text[250]; ItemNo: Code[20])
    begin
        ErrorText[ErrorIndex] := ErrorMessage;
        ErrorIndex := ErrorIndex + 1;

        if Item2.Get(ItemNo) then begin
            ItemTemp := Item2;
            if ItemTemp.Insert then;
        end;
    end;
}
