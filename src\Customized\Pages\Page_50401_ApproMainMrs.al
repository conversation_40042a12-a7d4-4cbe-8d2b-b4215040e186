page 50401 "Approved Maintenance MRS."
{

    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    PageType = Card;
    SourceTable = MRSHeader;
    SourceTableView = SORTING("MRS No.")
                      ORDER(Ascending)
                      WHERE(Status = CONST(Released),
    "Maintenance Job Card No." = FILTER(<> ''));
    UsageCategory = tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MRS No."; "MRS No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Manual MRS No."; "Manual MRS No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                  field("Maintenance Job Card No.";"Maintenance Job Card No.")
                {
                    ApplicationArea = all;
                }
                 field("Equipment ID";"Equipment ID")
                {
                    ApplicationArea = all;
                }
                field("Additional MRS"; "Additional MRS")
                {
                    ApplicationArea = all;
                }
                field("Issue Dept."; "Issue Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Issue Bus. Unit"; "Issue Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Production Batch No."; "Production Batch No.")
                {
                    ApplicationArea = all;
                }
                field("Materials Issued To"; "Materials Issued To")
                {
                    ApplicationArea = all;
                }
                field("Transfer Orders Created"; "Transfer Orders Created")
                {
                    ApplicationArea = all;

                    trigger OnDrillDown();
                    begin
                        TransferHDR.SETRANGE("MRS Ref. No.", "MRS No.");
                        PAGE.RUNMODAL(5742, TransferHDR);
                    end;
                }
                field("Transfer Shipment Posted"; "Transfer Shipment Posted")
                {
                    ApplicationArea = all;
                }
                field("Transfer Receipt Posted"; "Transfer Receipt Posted")
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Date of MRS"; "Date of MRS")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Issued Date"; "Issued Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
            }
            part(PostedMatReqLineSubform; "Posted Prdn. Mat. Req. Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Document No." = FIELD("MRS No.");
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Requisition")
            {
                Caption = '&Requisition';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';
                    Image = Dimensions;
                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1102152037)
                {
                }

            }
            group("&Line")
            {
                Caption = '&Line';
                action("Substitute MRS Lines")
                {
                    ApplicationArea = all;
                    Caption = 'Substitute MRS Lines';
                    Visible = false;
                    Image = ItemSubstitution;
                    trigger OnAction();
                    begin
                        MRSLineRec.FILTERGROUP(2);
                        MRSLineRec.SETRANGE(MRSLineRec."Document No.", "MRS No.");
                        MRSLineRec.FILTERGROUP(0);
                        PAGE.RUN(50890, MRSLineRec);
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Visible = true;
                action("Make Transfer Order")
                {
                    ApplicationArea = all;
                    Caption = 'Make Transfer Order';
                    image = Create;
                    trigger OnAction();
                    begin
                        //MakeTransferOrder;
                        MakeTransferOrders;
                    end;
                }
                separator("-")
                {
                    Caption = '-';
                }
                action("Create Return Jnl. Batch")
                {
                    ApplicationArea = all;
                    Caption = 'Create Return Jnl. Batch';
                    Visible = false;
                    image = Create;
                    trigger OnAction();
                    begin
                        CreateReturnItemJnlLine;
                    end;
                }
            }
        }
    }

    trigger OnOpenPage();
    begin
        /*if UserMg.GetMRSFilter <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetMRSFilter);
            FILTERGROUP(0);
        end;B2B.1.0.CD */
    end;

    var
        ApprovalEntries: Page 658;
        MRSLineRec: Record MRSLine;
        Text50200: Label 'Purchase Requisition not required when Stock available is greater than MRS Quantity.';
        Text50201: Label 'Qty to Issue must not exceed %1.';
        UserMg: Codeunit "User Setup Management";
        TransferHDR: Record "Transfer Header";
        TransferShipmentHDR: Record "Transfer Shipment Header";
        TransferRcptHDR: Record "Transfer Receipt Header";
        MRSHeader: Record MRSHeader;


}

