page 50472 "Posted Mat. Req. Lines Subform"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Ltd
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL       29-Dec-11    -> Form created for Material Requisitions.

    AutoSplitKey = true;
    DelayedInsert = true;
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = true;
    PageType = ListPart;
    SourceTable = MRSLine;

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                /*
                field("MRS Type";"MRS Type")
                {
                    Editable = false;
                }*/
                field(Type; Type)
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                //FIX03Jun2021>>
                field("No. 2"; "No. 2")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                //FIX03Jun2021<<
                field(Description; Description)
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Variant Code"; "Variant Code")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    Visible = false;
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    Visible = false;
                }
                field(Quantity; Quantity)
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Available Stock"; "Available Stock")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Qty. on comp. line"; "Qty. on Comp. Line")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Quantity Issued"; "Quantity Issued")
                {
                    ApplicationArea = all;
                }

                field("Acknowledged Issued Quantity"; "Acknowledged Issued Quantity")
                {
                    ApplicationArea = all;
                }
                field("Scrap Return Qty."; "Scrap Return Qty.")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Acknowledged Scrap Quantity"; "Acknowledged Scrap Quantity")
                {
                    ApplicationArea = all;

                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Purch. Req. Ref. No."; "Purch. Req. Ref. No.")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                /*
                field("Issued Acknowledged By";"Issued Acknowledged By")
                {
                    Visible = false;
                }*/
                field(Comment; Comment)
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Qty. on transfer Receipt"; "Qty. on transfer Receipt")
                {
                    Editable = false;
                }
                field("Line CLosed"; "Line CLosed")
                {
                    Editable = false;
                }

            }
        }
    }

    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50148. Unsupported part was commented. Please check it.
                        /*CurrPage.PostedMatReqLineSubform.FORM.*/
                        _ShowDimensions;

                    end;
                }
            }
        }
    }

    procedure _ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;

    procedure ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;
}

