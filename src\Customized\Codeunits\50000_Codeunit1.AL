codeunit 50000 Codeunit1
{
    Permissions = TableData "Sales Shipment Header" = MR, TableData "Purch. Rcpt. Header" = MR,
    TableData "Default Dimension" = IMR, TableData "Dimension Value" = IMR;//PKONJ18.3 //PKON22FE11-CR220021
    trigger OnRun()
    begin

    end;
    //PhaniFeb222021>>
    /*[EventSubscriber(ObjectType::Codeunit, Codeunit::"FA Check Consistency", 'OnSetFAPostingDateOnBeforeFADeprBookModify', '', false, false)]
    Local Procedure OnSetFAPostingDateOnBeforeFADeprBookModify1(var FADepreciationBook: Record "FA Depreciation Book"; var FALedgerEntry: Record "FA Ledger Entry"; MaxDate: Date; MinDate: Date; GLDate: Date)
    var
        FASSGRec: Record "fixed asset";
    BEGIN
        IF FADepreciationBook."Disposal Date" <> 0D THEN BEGIN
            IF FASSGRec.GET(FADepreciationBook."FA No.") THEN BEGIN
                FASSGRec.Blocked := true;
                FASSGRec."Approval Status" := FASSGRec."Approval Status"::Open;
                FASSGRec.Inactive := true;
                FASSGRec.Modify();
            end;
            //message('%1', FADepreciationBook."Disposal Date");
        END;
    END;*/
    //PhaniFeb222021<<
    //PKON22MA7>>
    [EventSubscriber(ObjectType::Codeunit, 22, 'OnBeforeRunWithCheck', '', false, false)]
    local procedure OnBeforeRunWithCheck(var ItemJournalLine: Record "Item Journal Line"; CalledFromAdjustment: Boolean; CalledFromInvtPutawayPick: Boolean; CalledFromApplicationWorksheet: Boolean; PostponeReservationHandling: Boolean; var IsHandled: Boolean)
    var
        GenJouLine: Record "Gen. Journal Line";
    begin
        // IF (ItemJournalLine."Shortcut Dimension 1 Code" = '') or (ItemJournalLine."Shortcut Dimension 2 Code" = '') then
        //     Error('Accounting Location and CC Code must have a value in Document No. %1 and line no %2', ItemJournalLine."Document No.", ItemJournalLine."Line No.");
    end;
    //PKON22MA7<<


    [EventSubscriber(ObjectType::Codeunit, 11, 'OnBeforeRunCheck', '', false, false)]
    local procedure OnBeforeRunCheck1(var GenJournalLine: Record "Gen. Journal Line")
    var
        GenJouLine: Record "Gen. Journal Line";
    begin
        GenJouLine.RESET;
        GenJouLine.SetRange("Journal Template Name", 'RECURRING');
        GenJouLine.SetRange("Journal Batch Name", GenJournalLine."Journal Batch Name");
        GenJouLine.SetRange("Document No.", GenJournalLine."Document No.");
        IF GenJouLine.findfirst then begin
            GenJournalLine."FA Posting Type" := GenJouLine."FA Posting Type";
            GenJournalLine."Depreciation Book Code" := GenJouLine."Depreciation Book Code";
        end;
    end;


    [EventSubscriber(ObjectType::Table, 81, 'OnAfterSetJournalLineFieldsFromApplication', '', false, false)]
    local procedure OnAfterSetJnlLineFieldsFromAppProc(var GenJournalLine: Record "Gen. Journal Line"; AccType: Option "G/L Account",Customer,Vendor,"Bank Account","Fixed Asset","IC Partner",Employee; AccNo: Code[20]; xGenJournalLine: Record "Gen. Journal Line")
    begin
        GenJournalLine."External Document No." := GenJournalLine."Applies-to Ext. Doc. No.";
    end;

    //Vendor Approval
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalForVend(RecRef: RecordRef; var IsHandled: boolean)
    var
        Vendor: Record Vendor;
    begin
        case RecRef.Number() of
            Database::Vendor:
                begin
                    RecRef.SetTable(Vendor);
                    Vendor."Approval Status" := Vendor."Approval Status"::"Pending for Approval";
                    Vendor.Blocked := Vendor.Blocked::All;
                    Vendor.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentVend(RecRef: RecordRef; var Handled: boolean)
    var
        Vendor: Record Vendor;
    begin
        case RecRef.Number() of
            Database::Vendor:
                begin
                    RecRef.SetTable(Vendor);
                    Vendor."Approval Status" := Vendor."Approval Status"::Released;
                    vendor.Blocked := Vendor.Blocked::" ";
                    Vendor.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Table, 5744, 'OnAfterCopyFromTransferHeader', '', false, false)]
    Local Procedure OnAfterCopyFromTransferHeadShpmt(var TransferShipmentHeader: Record "Transfer Shipment Header"; TransferHeader: Record "Transfer Header")
    BEGIN
        TransferShipmentHeader."Transfer Type" := TransferHeader."Transfer Type";
        TransferShipmentHeader."Production Batch No." := TransferHeader."Production Batch No.";
        TransferShipmentHeader."Production Order No." := TransferHeader."Production Order No.";
        TransferShipmentHeader."Manual MRS No." := TransferHeader."Manual MRS No.";
        TransferShipmentHeader."Branch Request No" := TransferHeader."Branch Request No";
        //project leap
        TransferShipmentHeader."MRS Category Code" := TransferHeader."MRS Category Code";
        //project leap
    END;

    [EventSubscriber(ObjectType::Table, 5746, 'OnAfterCopyFromTransferHeader', '', false, false)]
    Local Procedure OnAfterCopyFromTransferHeadRecpt(var TransferReceiptHeader: Record "Transfer Receipt Header"; TransferHeader: Record "Transfer Header")
    BEGIN
        TransferReceiptHeader."Transfer Type" := TransferHeader."Transfer Type";
        TransferReceiptHeader."Production Batch No." := TransferHeader."Production Batch No.";
        TransferReceiptHeader."Production Order No." := TransferHeader."Production Order No.";
        TransferReceiptHeader."Manual MRS No." := TransferHeader."Manual MRS No.";
        TransferReceiptHeader."Branch Request No" := TransferHeader."Branch Request No";
        //project leap
        TransferReceiptHeader."MRS Category Code" := TransferHeader."MRS Category Code";
        //project leap
    END;

    //project leap
    [EventSubscriber(ObjectType::Table, 5747, 'OnAfterCopyFromTransferLine', '', false, false)]
    Local Procedure copyfromtransferline(var TransferReceiptLine: Record "Transfer Receipt Line"; TransferLine: Record "Transfer Line")
    var
        mrslinerec: Record MRSLine;
    BEGIN
        //TransferReceiptHeader."Transfer Type" := TransferHeader."Transfer Type";
        if TransferLine."Production Batch No." <> '' then begin
            TransferReceiptLine."Production Batch No." := TransferLine."Production Batch No.";

            mrslinerec.SetRange("Production Batch No.", TransferLine."Production Batch No.");
            mrslinerec.SetRange("No.", transferline."Item No.");
            mrslinerec.SetRange("line closed", false);
            if mrslinerec.findset then
                //repeat
                begin
                mrslinerec.calcfields("qty. on transfer receipt");
                mrslinerec."Outstanding Quantity" := mrslinerec."Outstanding Quantity" - TransferReceiptLine.quantity;
                if (mrslinerec."qty. on Transfer receipt" >= mrslinerec.quantity)
                or (mrslinerec."Outstanding Quantity" = 0) then begin
                    mrslinerec."line closed" := true;
                end;
                mrslinerec.Modify();
            end;
            // until mrslinerec.next = 0;
            //project leap

        end;
        //project leap

    end;
    //project leap

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"TransferOrder-Post Shipment", 'OnAfterCreateItemJnlLine', '', false, false)]
    local procedure OnAfterCreateItemJnlLine(var ItemJournalLine: Record "Item Journal Line"; TransferLine: Record "Transfer Line"; TransferShipmentHeader: Record "Transfer Shipment Header"; TransferShipmentLine: Record "Transfer Shipment Line")
    begin
        ItemJournalLine."External Document No." := TransferShipmentHeader."Manual MRS No.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"TransferOrder-Post Receipt", 'OnBeforePostItemJournalLine', '', false, false)]
    local procedure OnBeforePostItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; TransferLine: Record "Transfer Line"; TransferReceiptHeader: Record "Transfer Receipt Header"; TransferReceiptLine: Record "Transfer Receipt Line"; CommitIsSuppressed: Boolean; TransLine: Record "Transfer Line")
    begin
        ItemJournalLine."External Document No." := TransferReceiptHeader."Manual MRS No.";
        //Project Leap
        TransferReceiptLine."Production Batch No." := TransferReceiptHeader."Production Batch No.";
        //Project Leap

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentVend(RecRef: RecordRef; var Handled: boolean)
    var
        Vendor: Record Vendor;
    begin
        case RecRef.Number() of
            Database::Vendor:
                begin
                    RecRef.SetTable(Vendor);
                    Vendor."Approval Status" := Vendor."Approval Status"::Open;
                    Vendor.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnCancelVendorApprovalRequest', '', false, false)]
    local procedure OnCanceldocumentVend(Vendor: Record Vendor)
    var
    begin
        Vendor."Approval Status" := Vendor."Approval Status"::Open;
        Vendor.Modify();
    end;
    //Customer Approval
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalForCust(RecRef: RecordRef; var IsHandled: boolean)
    var
        Customer: Record Customer;
    begin
        case RecRef.Number() of
            Database::Customer:
                begin
                    RecRef.SetTable(Customer);
                    Customer."Approval Status" := Customer."Approval Status"::"Pending for Approval";
                    Customer.Blocked := Customer.Blocked::All;
                    Customer.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentCust(RecRef: RecordRef; var Handled: boolean)
    var
        Customer: Record Customer;
    begin
        case RecRef.Number() of
            Database::Customer:
                begin
                    RecRef.SetTable(Customer);
                    Customer."Approval Status" := Customer."Approval Status"::Released;
                    Customer.Blocked := Customer.Blocked::" ";
                    Customer.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentCust(RecRef: RecordRef; var Handled: boolean)
    var
        Customer: Record Customer;
    begin
        case RecRef.Number() of
            Database::Customer:
                begin
                    RecRef.SetTable(Customer);
                    Customer."Approval Status" := Customer."Approval Status"::Open;
                    Customer.Modify();
                    Handled := true;
                end;
        end;
    end;

    /*
        [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterPostCustomerEntry', '', false, false)]
        local procedure OnAfterPostCustomerEnt(var GenJnlLine: Record "Gen. Journal Line"; var SalesHeader: Record "Sales Header"; var TotalSalesLine: Record "Sales Line"; var TotalSalesLineLCY: Record "Sales Line"; CommitIsSuppressed: Boolean; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
        var
            GenJouLine: Record "Gen. Journal Line";
            CustLRec: Record Customer;
            LoanLRec: Record Loan_B2B;
            LineGVar: Integer;
            SalesLne: Record "Sales Line";
            LoanPostGrp: Record "Loan Posting Groups";
            LoanDetails: Record "Loan Details B2B";
            LoanDea: Decimal;
            GenJnLne: Codeunit "Gen. Jnl.-Post Line";
            PosSalCreMemo: record "Sales Cr.Memo Header";
        begin
            //IF (SalesHeader."Rebate Period Code" <> '') THEN BEGIN
            GenJouLine.RESET;
            GenJouLine.Setrange("Journal Template Name", 'JV');
            GenJouLine.SETRANGE("Journal Batch Name", 'JOURNAL');
            IF GenJouLine.findLAST THEN
                LineGVar := GenJouLine."Line No." + 10000
            else
                LineGVar := 10000;

            PosSalCreMemo.reset;
            PosSalCreMemo.setrange("Pre-Assigned No.", SalesHeader."No.");
            IF PossalCreMemo.findfirst then;
            // message('%1', PossalCreMemo."No.");

            SalesLne.RESET;
            SalesLne.setrange("Document No.", SalesHeader."No.");
            SalesLne.SetFilter("Line Amount", '<>%1', 0);
            IF SalesLne.findset then
                REPEAT
                    IF CustLRec.GET(SalesHeader."Sell-to Customer No.") THEN BEGIN
                        LoanLRec.RESET;
                        LoanLRec.SetRange("Customer No.", CustLRec."No.");
                        LoanLRec.SetRange(Closed, FALSE);
                        LoanLRec.CalcFields("Total Loan Payable");
                        LoanLRec.SetFilter("Total Loan Payable", '<>%1', 0);
                        IF LoanLRec.FindFirst() then begin
                            //message('%1', LoanLRec."Total Loan Payable");
                            LoanPostGrp.RESET;
                            LoanPostGrp.SetRange("Posting Group", LoanLRec."Loan Posting Group");
                            IF LoanPostGrp.findfirst then;
                            clear(LoanDea);
                            LoanDetails.RESET;
                            LoanDetails.SetRange("Loan Id", LoanLRec.ID);
                            LoanDetails.Setfilter("EMI Deducted", '<>%1', 0);
                            IF LoanDetails.findset then
                                repeat
                                    LoanDea += LoanDetails."EMI Deducted";
                                until LoanDetails.next = 0;

                            IF ((LoanLRec."Total Loan Payable" - LoanDea) <> 0) THEN BEGIN
                                GenJouLine.INIT;
                                GenJouLine."Journal Template Name" := 'JV';
                                GenJouLine."Journal Batch Name" := 'JOURNAL';
                                GenJouLine."Line No." := LineGVar;
                                GenJouLine.INSERT();
                                GenJouLine."Posting Date" := Today;
                                GenJouLine."Document Date" := Today;
                                GenJouLine.Description := SalesHeader."Sell-to Customer Name";
                                GenJouLine."Account Type" := GenJouLine."Account Type"::Customer;
                                GenJouLine."Document No." := SalesHeader."No.";
                                GenJouLine."Loan ID" := LoanLRec.Id;
                                GenJouLine."External Document No." := SalesHeader."External Document No.";
                                GenJouLine."System-Created Entry" := TRUE;
                                IF ((LoanLRec."Total Loan Payable" - LoanDea) > SalesLne.Amount) THEN
                                    GenJouLine.VALIDATE(Amount, SalesLne.Amount)
                                ELSE
                                    GenJouLine.VALIDATE(Amount, (LoanLRec."Total Loan Payable" - LoanDea));
                                GenJouLine."Account Type" := GenJouLine."Account Type"::Customer;
                                GenJouLine."Account No." := SalesHeader."Sell-to Customer No.";
                                GenJouLine."Bal. Account Type" := GenJouLine."Bal. Account Type"::"G/L Account";
                                GenJouLine."Bal. Account No." := LoanPostGrp."Loan Refundable Acc.";
                                GenJouLine."Applies-to ID" := PossalCreMemo."No.";
                                GenJouLine.Modify();
                                //Error('%1', GenJouLine."Document No.");
                                //message('%1', GenJouLine.Amount);
                            END;
                        END;
                        LineGVar += 10000;

                        LoanCalculation(GenJouLine);
                        GenJnLne.RunWithCheck(GenJouLine);
                    end;
                Until SalesLne.next = 0;


            //end;
            //Error('STOP');
        end;

    */
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnCancelCustomerApprovalRequest', '', false, false)]
    local procedure OnCanceldocumentCust(Customer: Record Customer)
    var
    begin
        Customer."Approval Status" := Customer."Approval Status"::Open;
        Customer.Modify();
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Purchase Document", 'OnBeforeReleasePurchaseDoc', '', false, false)]
    local procedure OnBeforeReleasePurchaseDoc(var PurchaseHeader: Record "Purchase Header"; PreviewMode: Boolean)
    var
        UserSetup: Record "User Setup";
        PurchLn: Record "Purchase Line";
        Cape: Record "Budget Line";
        CapBudValue: Decimal;
        PrevCap: code[20];
        PurValue: Decimal;
        PurLne: Record "Purchase Line";
        PHdr: Record "Purchase Header";
    begin
        //PurchaseHeader.TestField("Shortcut Dimension 1 Code");
        //PurchaseHeader.TestField("Shortcut Dimension 2 Code");
        IF (PurchaseHeader."Purchase Type" <> PurchaseHeader."Purchase Type"::PMS)
        then begin
            PurchLn.reset;
            PurchLn.SetRange("Document Type", PurchaseHeader."Document Type");
            PurchLn.SetRange("Document No.", PurchaseHeader."No.");
            PurchLn.SetFilter(Type, '<>%1', PurchLn.type::"G/L Account");
            PurchLn.SetFilter("No.", '<>%1', '');
            //PurchLn.SetRange(Type, PurchLn.Type::Item);
            if PurchLn.findset then
                repeat
                    PurchaseHeader.TestField("Location Code");
                    PurchLn.TestField("Location Code");
                until PurchLn.next = 0;
            //PK-Balu-            PurchaseHeader.TestField("Location Code");
        end;
        /*
        IF (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Order) THEN BEGIN
            IF UserSetup.GET(UserId) THEN BEGIN
                IF (PurchaseHeader."Posting Date" <> WorkDate()) AND (NOT UserSetup.AllowPostingDateModify) THEN BEGIN
                    ERROR('Posting date must be equal to Workdate.');
                END;
            END;
        end;*///Pk On 27.04.2021 

        //PhaniFeb122021>>
        //Prasanna modified
        PHdr.Reset();
        PHdr.SetRange("Document Type", PurchaseHeader."Document Type");
        PHdr.SetRange("RFQ No.", PurchaseHeader."RFQ No.");
        PHdr.SetRange(Status, PHdr.Status::Released);
        IF not PHdr.FindFirst() then begin
            PurchLn.RESET;
            PurchLn.SetRange("Document No.", PurchaseHeader."No.");
            //PurchLn.Setfilter("Qty. to Invoice", '<>%1', 0);
            PurchLn.Setfilter("Capex No.", '<>%1', '');
            PurchLn.Setfilter("Capex Line No.", '<>%1', 0);
            PurchLn.SetCurrentKey("Capex No.", "Capex Line No.");
            IF PurchLn.findset then
                repeat
                    clear(CapBudValue);
                    clear(PurValue);
                    Cape.RESET;
                    Cape.SetRange("Document No.", PurchLn."Capex No.");
                    Cape.SetRange("Line No.", PurchLn."Capex Line No.");
                    Cape.SetRange("Shortcut Dimension 1 Code", PurchLn."Shortcut Dimension 1 Code");
                    Cape.SetRange("Shortcut Dimension 2 Code", PurchLn."Shortcut Dimension 2 Code");
                    cape.CalcFields("Budget Utilized");
                    IF Cape.findfirst then
                        CapBudValue += (Cape.Amount - cape."Budget Utilized");
                    PurLne.RESET;
                    PurLne.SetRange("Capex No.", PurchLn."Capex No.");
                    PurLne.SetRange("Capex Line No.", PurchLn."Capex Line No.");
                    PurLne.SetRange("Document Type", PurchaseHeader."Document Type");
                    PurLne.SetRange("Document No.", PurchaseHeader."No.");
                    IF PurLne.findset then
                        repeat
                            PurValue += PurLne.Amount;
                        until PurLne.next = 0;
                    Message('%1..%2', CapBudValue, PurValue);
                    IF CapBudValue < PurValue then
                        Error('Insufficient Budget.');
                until PurchLn.next = 0;
        end;
        //PhaniFeb122021<<
    end;

    /* [EventSubscriber(ObjectType::Codeunit, Codeunit::"Posting Preview Event Handler", 'OnGetEntries', '', false, false)]
     procedure OnGetEntries2(TableNo: Integer; var RecRef: RecordRef)
     var
         GlEntry: Record "G/L Entry";
     begin
         case TableNO of
             Database::"WHT Buffer":
                 BEGIN
                     RecRef.GETTABLE(TempWhtLedgerEntry);
                 END;
         End;

     end;

     [EventSubscriber(ObjectType::Codeunit, Codeunit::"Posting Preview Event Handler", 'OnAfterShowEntries', '', false, false)]
     procedure OnAfterShowEntries2(TableNo: Integer)
     Var
         WHTLedENt: Page WHTLedgerEntryPreview;
         TempWhtLedgerEntr: Record "WHT Buffer" temporary;
     begin
         Case TableNo of
             Database::"WHT Buffer":
                 begin
                     WHTLedENt.Set(TempWhtLedgerEntr);
                     WHTLedENt.Run;
                     Clear(WHTLedENt);
                     //PAGE.Run(PAGE::WHTLedgerEntries, TempWhtLedgerEntry);
                 end;
         ///
         End;
     end;

     [EventSubscriber(ObjectType::Codeunit, Codeunit::"Posting Preview Event Handler", 'OnAfterFillDocumentEntry', '', false, false)]
     procedure OnAfterFillDocumentEntry2(var DocumentEntry: Record "Document Entry")
     begin

         cu.InsertDocumentEntry(TempWhtLedgerEntry, DocumentEntry);
     end;*/
    //Post Preview code for WHT Process.

    [EventSubscriber(ObjectType::Table, 37, 'OnAfterInitOutstanding', '', false, false)] //PKONOC27 ////PKONOC29 uncommented
    procedure InitOutstandingQuty(var SalesLine: Record "Sales Line")
    var
        SalHdrLREc: Record "Sales Header";
        SalLneLRec: Record "Sales Line";
        QtyGVar: Decimal;
        QtyShiped: Decimal;
        QtyInvoiced: Decimal;
        QtyShipNotInv: Decimal;
        PosalInvLne: Record "Sales Invoice Line";
        PosShiLne: Record "Sales Shipment Line";

    BEGIN
        Clear(QtyGVar);
        Clear(QtyShiped);
        Clear(QtyInvoiced);
        clear(QtyShipNotInv);
        if not (SalesLine."Document Type" = SalesLine."Document Type"::Order) then
            exit;
        IF SalHdrLREc.GET(SalesLine."Document Type", SalesLine."Document No.") THEN BEGIN
            SalLneLRec.reset;
            SalLneLRec.SetRange("Document No.", SalesLine."Document No.");
            SalLneLRec.SetFilter(Type, '<>%1', SalLneLRec.type::"Charge (Item)");
            IF SalLneLRec.FindSet() then
                repeat
                    QtyGVar += SalLneLRec.Quantity;
                    //QtyShiped += (SalLneLRec."Qty. to Ship" + SalLneLRec."Quantity Shipped");
                    QtyShipNotInv += (SalLneLRec."Qty. Shipped Not Invoiced");
                    PosalInvLne.RESET;
                    PosalInvLne.SetRange("Order No.", SalLneLRec."Document No.");
                    PosalInvLne.SetRange("Order Line No.", SalLneLRec."Line No.");
                    IF PosalInvLne.findset THEN
                        repeat
                            QtyInvoiced += PosalInvLne.Quantity;
                        until PosalInvLne.next = 0;

                    PosShiLne.RESET;
                    PosShiLne.SetRange("Order No.", SalLneLRec."Document No.");
                    PosShiLne.SetRange("Order Line No.", SalLneLRec."Line No.");
                    IF PosShiLne.findset THEN
                        repeat
                            QtyShiped += PosShiLne.Quantity;
                        until PosShiLne.next = 0;


                /*IF SalHdrLREc."Order Tracking" = SalHdrLREc."Order Tracking"::"Completely Shipped" THEN
                    QtyInvoiced += (SalLneLRec."Quantity Invoiced" + SalLneLRec."Qty. to Invoice");*/
                until SalLneLRec.next = 0;

            //message('%1', QtyInvoiced);
            //message('%1..%2', SalLneLRec."Document No.", SalLneLRec."Document Type");
            IF ((QtyGVar <> 0) AND (QtyShiped <> 0)) THEN BEGIN
                /* IF ((QtyInvoiced <> 0)) THEN BEGIN
                     IF (QtyGVar = QtyInvoiced) then BEGIN
                         SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Completely Invoiced";
                         SalHdrLREc.Modify;
                     END ELSE BEGIN
                         SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Partially Invoiced";
                         SalHdrLREc.Modify;
                     END;
                 END ELSE BEGIN
                     IF (QtyShiped = QtyGVar) then BEGIN
                         SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Completely Shipped";
                         SalHdrLREc.Modify;
                     end
                     ELSE BEGIN
                         SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Partially Shipped";
                         SalHdrLREc.Modify;
                     END;
                 END;*/


                IF ((QtyInvoiced <> 0)) THEN BEGIN
                    IF (QtyGVar = QtyInvoiced) then BEGIN
                        SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Completely Invoiced";
                        SalHdrLREc.Modify;
                    END ELSE BEGIN
                        SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Partially Invoiced";
                        SalHdrLREc.Modify;
                    END;
                END ELSE BEGIN
                    IF (QtyShiped = QtyGVar) then BEGIN
                        SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Completely Shipped";
                        SalHdrLREc.Modify;
                    end
                    ELSE BEGIN
                        SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Partially Shipped";
                        SalHdrLREc.Modify;
                    END;
                END;
            END ELSE BEGIN
                SalHdrLREc."Order Tracking" := SalHdrLREc."Order Tracking"::"Not Yet Shipped";
                SalHdrLREc.Modify;
            END;
        END;
    END;

    [EventSubscriber(ObjectType::Table, 39, 'OnAfterInitOutstandingQty', '', false, false)]
    procedure InitOutstandingQuty1(var PurchaseLine: Record "Purchase Line")
    var
        PurHdrLREc: Record "Purchase Header";
        PurLneLRec: Record "Purchase Line";
        QtyGVar: Decimal;
        QtyReceived: Decimal;
        QtyInvoiced: Decimal;
        QtyReciveNotInv: Decimal;
        PosPurRecLne: Record "Purch. Inv. Line";
        PosPurReceiptLne: Record "Purch. Rcpt. Line";

    BEGIN
        Clear(QtyGVar);
        Clear(QtyReceived);
        Clear(QtyInvoiced);
        clear(QtyReciveNotInv);
        IF PurHdrLREc.GET(PurHdrLREc."Document Type"::Order, PurchaseLine."Document No.") THEN BEGIN
            PurLneLRec.reset;
            PurLneLRec.SetRange("Document No.", PurchaseLine."Document No.");
            PurLneLRec.SetFilter(Type, '<>%1', PurLneLRec.Type::"Charge (Item)");
            IF PurLneLRec.FindSet() then
                repeat
                    QtyGVar += PurLneLRec.Quantity;
                    //QtyReceived += (PurLneLRec."Qty. to Receive" + PurLneLRec."Quantity Received");
                    QtyReciveNotInv += (PurLneLRec."Qty. Rcd. Not Invoiced");

                    PosPurRecLne.RESET;
                    PosPurRecLne.SetRange("Order No.", PurLneLRec."Document No.");
                    PosPurRecLne.SetRange("Order Line No.", PurLneLRec."Line No.");
                    IF PosPurRecLne.findset THEN
                        repeat
                            QtyInvoiced += PosPurRecLne.Quantity;
                        until PosPurRecLne.next = 0;

                    PosPurReceiptLne.RESET;
                    PosPurReceiptLne.SetRange("Order No.", PurLneLRec."Document No.");
                    PosPurReceiptLne.SetRange("Order Line No.", PurLneLRec."Line No.");
                    IF PosPurReceiptLne.findset THEN
                        repeat
                            QtyReceived += PosPurReceiptLne.Quantity;
                        until PosPurReceiptLne.next = 0;
                until PurLneLRec.next = 0;


            IF (QtyGVar <> 0) AND (QtyReceived <> 0) THEN BEGIN
                IF (QtyInvoiced <> 0) THEN BEGIN
                    IF (QtyGVar = QtyInvoiced) then BEGIN
                        PurHdrLREc."Purchase Order Tracking" := PurHdrLREc."Purchase Order Tracking"::"Completely Invoiced";
                        PurHdrLREc.Modify;
                    END ELSE BEGIN
                        PurHdrLREc."Purchase Order Tracking" := PurHdrLREc."Purchase Order Tracking"::"Partially Invoiced";
                        PurHdrLREc.Modify;
                    END;
                END ELSE BEGIN
                    IF (QtyReceived = QtyGVar) then BEGIN
                        PurHdrLREc."Purchase Order Tracking" := PurHdrLREc."Purchase Order Tracking"::"Completely Received";
                        PurHdrLREc.Modify;
                    end
                    ELSE BEGIN
                        PurHdrLREc."Purchase Order Tracking" := PurHdrLREc."Purchase Order Tracking"::"Partially Received";
                        PurHdrLREc.Modify;
                    END;
                END;
            END else BEGIN
                PurHdrLREc."Purchase Order Tracking" := PurHdrLREc."Purchase Order Tracking"::"Not Yet Received";
                PurHdrLREc.Modify;
            END;
        END;
    end;

    [EventSubscriber(ObjectType::Table, 37, 'OnAfterInitHeaderDefaults', '', false, false)]
    local procedure CheckCrMemoReasonType(var SalesLine: Record "Sales Line"; SalesHeader: Record "Sales Header")
    begin
        IF SalesLine."Document Type" = SalesLine."Document Type"::"Credit Memo" THEN
            SalesHeader.TESTFIELD("Cr. Memo Reason Type");
    end;

    [EventSubscriber(ObjectType::Table, 37, 'OnAfterCopyFromItem', '', false, false)]
    local procedure OnAfterCopyFromItem(var SalesLine: Record "Sales Line"; Item: Record Item; CurrentFieldNo: Integer)
    var
        InvPostGroupGRec: Record "Inventory Posting Group";
    begin
        IF InvPostGroupGRec.Get(Item."Inventory Posting Group") then
            If InvPostGroupGRec."Prod. Discount Code" <> '' then
                SalesLine."Cust. Discount Code" := InvPostGroupGRec."Prod. Discount Code";
    end;



    [EventSubscriber(ObjectType::Codeunit, Codeunit::"TransferOrder-Post Receipt", 'OnBeforeReleaseDocument', '', false, false)]
    local procedure ChangeGlobalDimCode(var TransferHeader: Record "Transfer Header")
    var
        LOC: Record Location;
        "Responsibility centre": Record "Responsibility Center";
    begin
        // 1.0  Sasidhar To change the Global Dimension Code - by sasidhar.
        IF LOC.GET(TransferHeader."Transfer-to Code") THEN BEGIN
            IF "Responsibility centre".GET(LOC."Responsibility Center") THEN BEGIN
                TransferHeader."Shortcut Dimension 1 Code" := "Responsibility centre"."Global Dimension 1 Code";
                TransferHeader."Shortcut Dimension 2 Code" := "Responsibility centre"."Global Dimension 2 Code";
                TransferHeader.VALIDATE("Shortcut Dimension 1 Code");
                TransferHeader.MODIFY;
                COMMIT;
            END;
        END;
        //1.0  By sasi
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnAfterInitItemLedgEntry', '', false, false)]
    local procedure OnafterInitItemLedentr(var NewItemLedgEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line")
    var
        LocRec: Record Location;
        TranShip: Record "Transfer Shipment Header";
    begin
        NewItemLedgEntry."MRS No." := ItemJournalLine."MRS No.";
        NewItemLedgEntry."Manual MRS No." := ItemJournalLine."Manual MRS No.";
        NewItemLedgEntry."Production Batch No." := ItemJournalLine."Production Batch No.";
        NewItemLedgEntry."Br. Cust. Discount Code" := ItemJournalLine."Br. Cust. Discount Code";
        //GJ_RG_221216 
        LocRec.RESET;
        LocRec.SETRANGE(Active, TRUE);
        LocRec.SETRANGE(Code, ItemJournalLine."Location Code");
        LocRec.SETRANGE("Use As In-Transit", TRUE);
        IF LocRec.FINDFIRST THEN
            NewItemLedgEntry."In-Transit" := TRUE;
        IF ((ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer) AND (ItemJournalLine."Document Type" = ItemJournalLine."Document Type"::"Transfer Shipment")) THEN BEGIN
            TranShip.RESET;
            TranShip.SETRANGE("No.", ItemJournalLine."Document No.");
            IF TranShip.FINDFIRST THEN BEGIN
                NewItemLedgEntry."To Location_SNOP" := TranShip."Transfer-to Code";
            END;
        END;
        //GJ_RG_221216 
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnAfterInitValueEntry', '', false, false)]
    local procedure OnafterInitValeEntr(var ValueEntry: Record "Value Entry"; ItemJournalLine: Record "Item Journal Line")
    var
    begin
        ValueEntry."MRS No." := ItemJournalLine."MRS No.";
        ValueEntry."Manual MRS No." := ItemJournalLine."Manual MRS No.";
        ValueEntry."Production Batch No." := ItemJournalLine."Production Batch No.";
        ValueEntry."Prod. Discount Code" := ItemJournalLine."Cust. Discount Code";
        ///message('PK in to ... %1 %2..Value Entry Insertion', ValueEntry."Branch Prod. Discount Code", ItemJournalLine."Br. Cust. Discount Code");
    end;

    local procedure OnafterInitCapEntr(var CapLedgEntry: Record "Capacity Ledger Entry"; ItemJournalLine: Record "Item Journal Line")
    var
    begin
        CapLedgEntry."Production Batch No." := ItemJournalLine."Production Batch No.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnInitValueEntryOnAfterAssignFields', '', false, false)]
    local procedure OnInitValueEntryOnAfterAssignFields(var ValueEntry: Record "Value Entry"; ItemLedgEntry: Record "Item Ledger Entry")
    var
        RebatesHead: Record "Item Sales Disc. Qty.";
    begin
        ValueEntry."Prod. Discount Code" := ItemLedgEntry."Cust. Discount Code";

        if ItemLedgEntry."Br. Cust. Discount Code" <> '' then
            ValueEntry."Branch Prod. Discount Code" := ItemLedgEntry."Br. Cust. Discount Code";

        IF ItemLedgEntry."Cust. Discount Code" <> '' then begin
            RebatesHead.Reset();
            IF RebatesHead.GET(ItemLedgEntry."Cust. Discount Code") then
                If RebatesHead.Status = RebatesHead.Status::Released then
                    ValueEntry."Prod. Discount Code" := ItemLedgEntry."Cust. Discount Code";
        end;

    end;

    //CreditLimit sTART

    [IntegrationEvent(false, false)]
    Procedure OnSendCreditLimitForApproval(var CreditLimit: Record "Cust. Cr. Limit Schedule")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCreditLimitForApproval(var CreditLimit: Record "Cust. Cr. Limit Schedule")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCreditLimitforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCreditLimitforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCreditLimitForApproval', '', true, true)]
    local procedure RunworkflowonsendCreditLimitForApproval(var CreditLimit: Record "Cust. Cr. Limit Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCreditLimitforApprovalCode(), CreditLimit);
    end;

    procedure RunworkflowOnCancelCreditLimitforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCreditLimitForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCreditLimitForApproval', '', true, true)]

    local procedure RunworkflowonCancelCreditLimitForApproval(var CreditLimit: Record "Cust. Cr. Limit Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCreditLimitforApprovalCode(), CreditLimit);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCreditLimit();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCreditLimitforApprovalCode(), DATABASE::"Cust. Cr. Limit Schedule",
          CopyStr(CreditLimitsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCreditLimitforApprovalCode(), DATABASE::"Cust. Cr. Limit Schedule",
          CopyStr(CreditLimitrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryCreditLimit(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCreditLimitforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCreditLimitforApprovalCode(), RunworkflowOnSendCreditLimitforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCreditLimitforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCreditLimitforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCreditLimitforApprovalCode());
        end;
    end;

    procedure ISCreditLimitworkflowenabled(var CreditLimit: Record "Cust. Cr. Limit Schedule"): Boolean
    begin
        if CreditLimit.Status <> CreditLimit.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CreditLimit, RunworkflowOnSendCreditLimitforApprovalCode()));
    end;

    Procedure CheckCreditLimitApprovalsWorkflowEnabled(var CreditLimit: Record "Cust. Cr. Limit Schedule"): Boolean
    begin
        IF not ISCreditLimitworkflowenabled(CreditLimit) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentCreditLimit(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CreditLimit: Record "Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CreditLimit);
                    ApprovalEntryArgument."Document No." := FORMAT(CreditLimit."Line No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentCreditLimit(RecRef: RecordRef; var Handled: boolean)
    var
        CreditLimit: Record "Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CreditLimit);
                    CreditLimit.Status := CreditLimit.Status::open;
                    CreditLimit.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentCreditLimit(RecRef: RecordRef; var Handled: boolean)
    var
        CreditLimit: Record "Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CreditLimit);
                    CreditLimit.Status := CreditLimit.Status::Released;
                    CreditLimit.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalCreditLimit(RecRef: RecordRef; var IsHandled: boolean)
    var
        CreditLimit: Record "Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CreditLimit);
                    CreditLimit.Status := CreditLimit.Status::"Pending Approval";
                    CreditLimit.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCustCreLimit(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCreditLimitforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCreditLimitforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCreditLimitforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCreditLimitforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryCreditLimit()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CreditLimitCategoryTxt, 1, 20), CopyStr(CreditLimitCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsCreditLimit()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Cust. Cr. Limit Schedule", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateCreditLimit()
    begin
        InsertCreditLimitApprovalworkflowtemplate();
    end;

    local procedure InsertCreditLimitApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CreditLimitDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CreditLimitApprWorkflowDescTxt, 1, 100), CopyStr(CreditLimitCategoryTxt, 1, 20));
        InsertCreditLimitApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCreditLimitApprovalworkflowDetails(var workflow: record Workflow);
    var
        CustCrLimit: Record "Cust. Cr. Limit Schedule";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCreditLimittypecondition(CustCrLimit.Status::open), RunworkflowOnSendCreditLimitforApprovalCode(), BuildCreditLimittypecondition(CustCrLimit.Status::"Pending Approval"), RunworkflowOnCancelCreditLimitforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCreditLimittypecondition(status: integer): Text
    var
        CustCrLimt: Record "Cust. Cr. Limit Schedule";
    Begin
        CustCrLimt.SetRange(status, status);
        exit(StrSubstNo(CreditLimitTypeCondnTxt, workflowsetup.Encode(CustCrLimt.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure OnaftergetpageidCreditLimit(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Cust. Cr. Limit Schedule" then
            PageID := GetConditionalcardPageidCreditLimit(RecordRef)
    end;

    local procedure GetConditionalcardPageidCreditLimit(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Cust. Cr. Limit Schedule":
                exit(page::"Cust. Cr. Limit Schedule");
        end;
    end;
    //CreditLimit eND



    //Add Credit Limit Approval Start >>


    [IntegrationEvent(false, false)]
    Procedure OnSendAddCreditLimitForApproval(var AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelAddCreditLimitForApproval(var AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendAddCreditLimitforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendAddCreditLimitforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendAddCreditLimitForApproval', '', true, true)]
    local procedure RunworkflowonsendAddCreditLimitForApproval(var AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendAddCreditLimitforApprovalCode(), AddCreditLimit);
    end;

    procedure RunworkflowOnCancelAddCreditLimitforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelAddCreditLimitForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelAddCreditLimitForApproval', '', true, true)]

    local procedure RunworkflowonCancelAddCreditLimitForApproval(var AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelAddCreditLimitforApprovalCode(), AddCreditLimit);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryAddCreditLimit();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendAddCreditLimitforApprovalCode(), DATABASE::"Add. Cust. Cr. Limit Schedule",
          CopyStr(AddCreditLimitsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelAddCreditLimitforApprovalCode(), DATABASE::"Add. Cust. Cr. Limit Schedule",
          CopyStr(AddCreditLimitrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryAddCreditLimit(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelAddCreditLimitforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelAddCreditLimitforApprovalCode(), RunworkflowOnSendAddCreditLimitforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendAddCreditLimitforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendAddCreditLimitforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendAddCreditLimitforApprovalCode());
        end;
    end;

    procedure ISAddCreditLimitworkflowenabled(var AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule"): Boolean
    begin
        if AddCreditLimit.Status <> AddCreditLimit.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(AddCreditLimit, RunworkflowOnSendAddCreditLimitforApprovalCode()));
    end;

    Procedure CheckAddCreditLimitApprovalsWorkflowEnabled(var AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule"): Boolean
    begin
        IF not ISAddCreditLimitworkflowenabled(AddCreditLimit) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentAddCreditLimit(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Add. Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(AddCreditLimit);
                    ApprovalEntryArgument."Document No." := FORMAT(AddCreditLimit."Line No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentAddCreditLimit(RecRef: RecordRef; var Handled: boolean)
    var
        AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Add. Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(AddCreditLimit);
                    AddCreditLimit.Status := AddCreditLimit.Status::open;
                    AddCreditLimit.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentAddCreditLimit(RecRef: RecordRef; var Handled: boolean)
    var
        AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Add. Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(AddCreditLimit);
                    AddCreditLimit.Status := AddCreditLimit.Status::Released;
                    AddCreditLimit.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalAddCreditLimit(RecRef: RecordRef; var IsHandled: boolean)
    var
        AddCreditLimit: Record "Add. Cust. Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Add. Cust. Cr. Limit Schedule":
                begin
                    RecRef.SetTable(AddCreditLimit);
                    AddCreditLimit.Status := AddCreditLimit.Status::"Pending Approval";
                    AddCreditLimit.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryAddCreditLimit(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendAddCreditLimitforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendAddCreditLimitforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelAddCreditLimitforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelAddCreditLimitforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryAddCreditLimit()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(AddCreditLimitCategoryTxt, 1, 20), CopyStr(AddCreditLimitCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsAddCreditLimit()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Add. Cust. Cr. Limit Schedule", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateAddCreditLimit()
    begin
        InsertAddCreditLimitApprovalworkflowtemplate();
    end;
    //PMS Process
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterPurchInvLineInsert', '', false, false)]
    Local procedure OnAfterPurchInvLineInsert(var PurchInvLine: Record "Purch. Inv. Line"; PurchInvHeader: Record "Purch. Inv. Header"; PurchLine: Record "Purchase Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean)
    var
        PMSCardLedger: Record PMSCardLedger;
        PMSCardLedger1: Record PMSCardLedger;
        PurchaseHeader: Record "Purchase Header";
        PMSCard: Record PMSManagement;
    begin
        PurchaseHeader.get(PurchLine."Document Type", PurchLine."Document No.");
        IF (PurchaseHeader."Purchase Type" = PurchaseHeader."Purchase Type"::PMS) and (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Invoice) then begin
            PMSCardLedger.INIT;
            IF PMSCardLedger1.FINDLAST THEN
                PMSCardLedger."Entry No." := PMSCardLedger1."Entry No." + 1
            ELSE
                PMSCardLedger."Entry No." := 1;
            PMSCardLedger."FA No." := PurchInvLine."No.";
            PMSCardLedger."PMS Card No." := PurchInvLine."PMS card No.";
            PMSCardLedger."Pms No." := PurchInvLine."PMS No.";
            PMSCardLedger.Description := PurchInvLine.Description;
            PMSCardLedger."Posting Date" := PurchInvLine."Posting Date";
            PMSCardLedger."Documen type" := 1;
            PMSCardLedger."Receipt No." := PurchInvLine."Old_PMS Card No.";
            PMSCardLedger."Last Km Reading" := PurchInvLine."Last Meter Reading";
            PMSCardLedger."Current KM Reading" := PurchInvLine."Current Meter Reading";
            PMSCardLedger."Document No." := PurchInvLine."Document No.";
            PMSCardLedger.Amount := -PurchInvLine."Amount Including VAT";
            PMSCardLedger."Date PMS Availed" := PurchInvLine."Date PMS Availed";//PKPMS
            PMSCardLedger."USER ID" := UPPERCASE(USERID);
            PMSCardLedger.INSERT;
            if PMSCard.Get(PurchInvLine."PMS No.") then begin
                PMSCard."Current Meter Reading" := PurchInvLine."Current Meter Reading";
                PMSCard."Last Meter Reading" := PurchInvLine."Last Meter Reading";
                PMSCard.Modify();
            end;
        END;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnAfterInitCustLedgEntry', '', false, false)]
    local procedure OnAfterInitCustLedgEntry(var CustLedgerEntry: Record "Cust. Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        CustLedgerEntry."Sales Rebate Period" := GenJournalLine."Sales Rebate Period";
        CustLedgerEntry."Responsibility Center" := GenJournalLine."Responsibility Center";
        CustLedgerEntry."Description 2" := GenJournalLine."Description 2";
        CustLedgerEntry."Teller / Cheque No." := GenJournalLine."Cheque No.";
        CustLedgerEntry."Qty per branch" := GenJournalLine."Qty per branch";
        //BaluOnJu7>>
        CustLedgerEntry."Teller Bank Name" := GenJournalLine."Teller Bank Name";
        CustLedgerEntry."Teller / Cheque Date" := GenJournalLine."Teller / Cheque Date";
        CustLedgerEntry."Bank Name" := GenJournalLine."Bank Name";
        //BaluOnJu7<<
        if genjournalline."Document Type" = genjournalline."Document Type"::"Credit Memo" then begin //RFC#2024_16
            CustLedgerEntry."Description 2" := GenJournalLine."Printable Comment 1";//RFC#2024_16     
            CustLedgerEntry."Narration1" := GenJournalLine."Printable Comment 2";//RFC#2024_16
        end;//RFC#16
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnAfterInitGLEntry', '', false, false)]
    local procedure OnAfterInitGLEntryProc(var GLEntry: Record "G/L Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        GLEntry."Branch GRN No." := GenJournalLine."Branch GRN No.";
        GLEntry."Qty per branch" := GenJournalLine."Qty per branch";
        GLEntry."Reversal For Prov Entry No." := GenJournalLine."Reversal For Prov Entry No.";
        //RFC #22
        GLEntry."Description 2" := GenJournalLine."Description 2";
        GLEntry.Narration := GenJournalLine.Narration;
        Glentry.Narration1 := GenJournalLine.Narration1;
        //RFC #22
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnAfterVendLedgEntryInsert', '', false, false)]
    local procedure OnAfterVendLedgEntryInsert(var VendorLedgerEntry: Record "Vendor Ledger Entry"; GenJournalLine: Record "Gen. Journal Line"; DtldLedgEntryInserted: Boolean)
    var
        DefaultDimension: record "Default Dimension";
        PMSCardLedger: Record PMSCardLedger;
        PMSCardLedger1: Record PMSCardLedger;
        Vend: record Vendor;
        FAREC: Record "Fixed Asset";
        PMSCard: Record PMSManagement;
        DimMgt: codeunit DimensionManagement;
        ShortcutDimCode: array[8] of Code[20];
    begin
        DimMgt.GetShortcutDimensions(GenJournalLine."Dimension Set ID", ShortcutDimCode);
        if Vend.get(GenJournalLine."Account No.") then;
        IF (Vend."Vendor Type" = Vend."Vendor Type"::PMS) AND (ShortcutDimCode[4] <> '') THEN BEGIN
            DefaultDimension.RESET;
            DefaultDimension.SETRANGE(DefaultDimension."Table ID", 5600);
            DefaultDimension.SETRANGE(DefaultDimension."Dimension Code", 'EXPENSE');
            DefaultDimension.SETRANGE(DefaultDimension."Dimension Value Code", ShortcutDimCode[4]);
            IF DefaultDimension.FINDFIRST THEN BEGIN
                PMSCardLedger.INIT;
                IF PMSCardLedger1.FINDLAST THEN
                    PMSCardLedger."Entry No." := PMSCardLedger1."Entry No." + 1
                ELSE
                    PMSCardLedger."Entry No." := 1;
                PMSCardLedger."FA No." := DefaultDimension."No.";
                IF FAREC.GET(DefaultDimension."No.") THEN;
                FAREC.TestField("PMS Card No.");
                PMSCardLedger."PMS Card No." := FAREC."PMS Card No.";
                PMSCardLedger."Pms No." := FAREC."PMS No.";
                PMSCardLedger.Description := FAREC.Description;
                PMSCardLedger."Posting Date" := GenJournalLine."Posting Date";
                PMSCardLedger."Documen type" := 2;
                PMSCardLedger."Document No." := VendorLedgerEntry."Document No.";
                PMSCardLedger.Amount := GenJournalLine.Amount;
                PMSCardLedger."USER ID" := UPPERCASE(USERID);
                PMSCardLedger."Receipt No." := GenJournalLine."External Document No.";
                PMSCardLedger."Date PMS Availed" := GenJournalLine."Date PMS Availed";//PK
                PMSCardLedger.INSERT;

            END;
        END;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line2", 'OnAfterVendLedgEntryInsert', '', false, false)]
    local procedure OnAfterVendLedgEntryInsert1(var VendorLedgerEntry: Record "Vendor Ledger Entry"; GenJournalLine: Record "Gen. Journal Line"; DtldLedgEntryInserted: Boolean)
    var
        DefaultDimension: record "Default Dimension";
        PMSCardLedger: Record PMSCardLedger;
        PMSCardLedger1: Record PMSCardLedger;
        Vend: record Vendor;
        FAREC: Record "Fixed Asset";
        PMSCard: Record PMSManagement;
        DimMgt: codeunit DimensionManagement;
        ShortcutDimCode: array[8] of Code[20];
    begin
        DimMgt.GetShortcutDimensions(GenJournalLine."Dimension Set ID", ShortcutDimCode);
        if Vend.get(GenJournalLine."Account No.") then;
        IF (Vend."Vendor Type" = Vend."Vendor Type"::PMS) AND (ShortcutDimCode[4] <> '') THEN BEGIN
            DefaultDimension.RESET;
            DefaultDimension.SETRANGE(DefaultDimension."Table ID", 5600);
            DefaultDimension.SETRANGE(DefaultDimension."Dimension Code", 'EXPENSE');
            DefaultDimension.SETRANGE(DefaultDimension."Dimension Value Code", ShortcutDimCode[4]);
            IF DefaultDimension.FINDFIRST THEN BEGIN
                PMSCardLedger.INIT;
                IF PMSCardLedger1.FINDLAST THEN
                    PMSCardLedger."Entry No." := PMSCardLedger1."Entry No." + 1
                ELSE
                    PMSCardLedger."Entry No." := 1;
                PMSCardLedger."FA No." := DefaultDimension."No.";
                IF FAREC.GET(DefaultDimension."No.") THEN;
                FAREC.TestField("PMS Card No.");
                PMSCardLedger."PMS Card No." := FAREC."PMS Card No.";
                PMSCardLedger."Pms No." := FAREC."PMS No.";
                PMSCardLedger.Description := FAREC.Description;
                PMSCardLedger."Posting Date" := GenJournalLine."Posting Date";
                PMSCardLedger."Documen type" := 2;
                PMSCardLedger."Document No." := VendorLedgerEntry."Document No.";
                PMSCardLedger.Amount := GenJournalLine.Amount;
                PMSCardLedger."USER ID" := UPPERCASE(USERID);
                PMSCardLedger."Receipt No." := GenJournalLine."External Document No.";
                PMSCardLedger.INSERT;
            END;
        END;
    end;
    //PK Process
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Make Maintenance Ledger Entry", 'OnAfterCopyFromGenJnlLine', '', false, false)]
    local procedure OnAfterCopyFromGenJnlLineToMainLedEntr(var MaintenanceLedgerEntry: Record "Maintenance Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    var
        ValueEntry: Record "Value Entry";
        PurchaseInvLines: Record "Purch. Inv. Line";
    begin
        MaintenanceLedgerEntry."PMS Card No." := GenJournalLine."PMS Card No.";
        MaintenanceLedgerEntry."Old_PMS Card No." := GenJournalLine."Old_PMS Card No.";
        MaintenanceLedgerEntry."Last Meter Reading" := GenJournalLine."Last Meter Reading";
        MaintenanceLedgerEntry."Current Meter Reading" := GenJournalLine."Current Meter Reading";
        //FIX23Jun2021>>
        if GenJournalLine."Last Km Reading" <> 0 then
            MaintenanceLedgerEntry."Last Meter Reading" := GenJournalLine."Last Km Reading";
        if GenJournalLine."Current Km Reading" <> 0 then
            MaintenanceLedgerEntry."Current Meter Reading" := GenJournalLine."Current Km Reading";
        //FIX23Jun2021<<
        MaintenanceLedgerEntry."Date PMS Availed_" := GenJournalLine."Date PMS Availed";
        MaintenanceLedgerEntry.Quantity := GenJournalLine.Quantity;
        MaintenanceLedgerEntry."Description 2" := GenJournalLine."Description 2";
        //Fix02Sep2021>>
        ValueEntry.Reset();
        ValueEntry.SetRange("Document No.", MaintenanceLedgerEntry."Document No.");
        ValueEntry.SetRange("FA No.", MaintenanceLedgerEntry."FA No.");
        if ValueEntry.FindLast() then
            MaintenanceLedgerEntry.Quantity := Abs(ValueEntry."Item Ledger Entry Quantity");
        //Fix02Sep2021<<
        if MaintenanceLedgerEntry."Document Type" = MaintenanceLedgerEntry."Document Type"::Invoice then begin
            PurchaseInvLines.Reset();
            PurchaseInvLines.SetRange("Document No.", MaintenanceLedgerEntry."Document No.");
            PurchaseInvLines.SetRange("No.", MaintenanceLedgerEntry."FA No.");
            if PurchaseInvLines.FindLast() then
                MaintenanceLedgerEntry.Quantity := Abs(PurchaseInvLines.Quantity);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnBeforePostInvPostBuffer', '', false, false)]
    local procedure InsertJurnalPostLines(var GenJnlLine: Record "Gen. Journal Line"; var InvoicePostBuffer: Record "Invoice Post. Buffer"; var PurchHeader: Record "Purchase Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PreviewMode: Boolean; CommitIsSupressed: Boolean)
    begin
        GenJnlLine."PMS Card No." := InvoicePostBuffer."PMS Card No.";
        GenJnlLine."Old_PMS Card No." := InvoicePostBuffer."Old_PMS Card No.";
        GenJnlLine."Last Meter Reading" := InvoicePostBuffer."Last Meter Reading";
        GenJnlLine."Current Meter Reading" := InvoicePostBuffer."Current Meter Reading";
        GenJnlLine."CWIP No." := InvoicePostBuffer."CWIP No.";
        GenJnlLine."Capex No." := InvoicePostBuffer."Capex No.";//FIX02June2021
        GenJnlLine."Capex Line No." := InvoicePostBuffer."Capex Line No.";//FIX02June2021
        GenJnlLine."Description 2" := InvoicePostBuffer."Description 2";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterFillInvoicePostBuffer', '', false, false)]
    local procedure InsertTempLines(var InvoicePostBuffer: Record "Invoice Post. Buffer"; PurchLine: Record "Purchase Line"; var TempInvoicePostBuffer: Record "Invoice Post. Buffer" temporary; CommitIsSupressed: Boolean)
    begin
        InvoicePostBuffer."PMS Card No." := PurchLine."PMS Card No.";
        InvoicePostBuffer."Old_PMS Card No." := PurchLine."PMS Receipt No.";
        InvoicePostBuffer."Current Meter Reading" := PurchLine."Current Meter Reading";
        InvoicePostBuffer."Last Meter Reading" := PurchLine."Last Meter Reading";
        InvoicePostBuffer."CWIP No." := PurchLine."CWIP No.";
        InvoicePostBuffer."Capex No." := PurchLine."Capex No.";//FIX02June2021
        InvoicePostBuffer."Capex Line No." := PurchLine."Capex Line No.";//FIX02June2021
        InvoicePostBuffer."Description 2" := PurchLine."Description 2";

    end;

    local procedure InsertAddCreditLimitApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(AddCreditLimitDocOCRWorkflowCodeTxt, 1, 17), CopyStr(AddCreditLimitApprWorkflowDescTxt, 1, 100), CopyStr(AddCreditLimitCategoryTxt, 1, 20));
        InsertAddCreditLimitApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertAddCreditLimitApprovalworkflowDetails(var workflow: record Workflow);
    var
        AddCustCrLimit: Record "Add. Cust. Cr. Limit Schedule";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildAddCreditLimittypecondition(AddCustCrLimit.Status::open), RunworkflowOnSendAddCreditLimitforApprovalCode(), BuildAddCreditLimittypecondition(AddCustCrLimit.Status::"Pending Approval"), RunworkflowOnCancelAddCreditLimitforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildAddCreditLimittypecondition(status: integer): Text
    var
        AddCustCrLmt: Record "Add. Cust. Cr. Limit Schedule";
    Begin
        AddCustCrLmt.SetRange(status, status);
        exit(StrSubstNo(AddCreditLimitTypeCondnTxt, workflowsetup.Encode(AddCustCrLmt.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidAddCreditLimit(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Add. Cust. Cr. Limit Schedule" then
            PageID := GetConditionalcardPageidAddCreditLimit(RecordRef)
    end;

    local procedure GetConditionalcardPageidAddCreditLimit(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Add. Cust. Cr. Limit Schedule":
                exit(page::"Add. Cust. Cr. Limit Schedule");
        end;
    end;

    //Add Credit Limit Approval End  <<


    //Item Approval For Transfer FA START
    [IntegrationEvent(false, false)]
    Procedure OnSendITMFAForApproval(var ITMFA: Record "Item Approval For Transfer FA")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelITMFAForApproval(var ITMFA: Record "Item Approval For Transfer FA")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendITMFAforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendITMFAforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendITMFAForApproval', '', true, true)]
    local procedure RunworkflowonsendITMFAForApproval(var ITMFA: Record "Item Approval For Transfer FA")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendITMFAforApprovalCode(), ITMFA);
    end;

    procedure RunworkflowOnCancelITMFAforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelITMFAForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelITMFAForApproval', '', true, true)]

    local procedure RunworkflowonCancelITMFAForApproval(var ITMFA: Record "Item Approval For Transfer FA")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelITMFAforApprovalCode(), ITMFA);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryITMFA();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendITMFAforApprovalCode(), DATABASE::"Item Approval For Transfer FA",
          CopyStr(ITMFAsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelITMFAforApprovalCode(), DATABASE::"Item Approval For Transfer FA",
          CopyStr(ITMFArequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryITMFA(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelITMFAforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelITMFAforApprovalCode(), RunworkflowOnSendITMFAforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendITMFAforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendITMFAforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendITMFAforApprovalCode());
        end;
    end;

    procedure ISITMFAworkflowenabled(var ITMFA: Record "Item Approval For Transfer FA"): Boolean
    begin
        if ITMFA.Status <> ITMFA.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(ITMFA, RunworkflowOnSendITMFAforApprovalCode()));
    end;

    Procedure CheckITMFAApprovalsWorkflowEnabled(var ITMFA: Record "Item Approval For Transfer FA"): Boolean
    begin
        IF not ISITMFAworkflowenabled(ITMFA) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentITMFA(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        ITMFA: Record "Item Approval For Transfer FA";
    begin
        case RecRef.Number() of
            Database::"Item Approval For Transfer FA":
                begin
                    RecRef.SetTable(ITMFA);
                    ApprovalEntryArgument."Document No." := FORMAT(ITMFA."Item No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentITMFA(RecRef: RecordRef; var Handled: boolean)
    var
        ITMFA: Record "Item Approval For Transfer FA";
    begin
        case RecRef.Number() of
            Database::"Item Approval For Transfer FA":
                begin
                    RecRef.SetTable(ITMFA);
                    ITMFA.Status := ITMFA.Status::open;
                    ITMFA.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentITMFA(RecRef: RecordRef; var Handled: boolean)
    var
        ITMFA: Record "Item Approval For Transfer FA";
    begin
        case RecRef.Number() of
            Database::"Item Approval For Transfer FA":
                begin
                    RecRef.SetTable(ITMFA);
                    ITMFA.Status := ITMFA.Status::Released;
                    ITMFA.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalITMFA(RecRef: RecordRef; var IsHandled: boolean)
    var
        ITMFA: Record "Item Approval For Transfer FA";
    begin
        case RecRef.Number() of
            Database::"Item Approval For Transfer FA":
                begin
                    RecRef.SetTable(ITMFA);
                    ITMFA.Status := ITMFA.Status::"Pending for Approval";
                    ITMFA.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryITMFA(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendITMFAforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendITMFAforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelITMFAforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelITMFAforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryITMFA()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(ITMFACategoryTxt, 1, 20), CopyStr(ITMFACategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsITMFA()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Item Approval For Transfer FA", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateITMFA()
    begin
        InsertITMFAApprovalworkflowtemplate();
    end;

    local procedure InsertITMFAApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(ITMFADocOCRWorkflowCodeTxt, 1, 17), CopyStr(ITMFAApprWorkflowDescTxt, 1, 100), CopyStr(ITMFACategoryTxt, 1, 20));
        InsertITMFAApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertITMFAApprovalworkflowDetails(var workflow: record Workflow);
    var
        ITMFA: Record "Item Approval For Transfer FA";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildITMFAtypecondition(ITMFA.Status::open), RunworkflowOnSendITMFAforApprovalCode(), BuildITMFAtypecondition(ITMFA.Status::"Pending for Approval"), RunworkflowOnCancelITMFAforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildITMFAtypecondition(status: integer): Text
    var
        ITMFA: Record "Item Approval For Transfer FA";
    Begin
        ITMFA.SetRange(status, status);
        exit(StrSubstNo(ITMFATypeCondnTxt, workflowsetup.Encode(ITMFA.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure OnaftergetpageidITMFA(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidITMFA(RecordRef)
    end;

    local procedure GetConditionalcardPageidITMFA(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Item Approval For Transfer FA":
                exit(page::"ItemTransfertoFA");
        end;
    end;
    //Item Approval For Transfer FA END


    //Approval for Loading Slip  start >>
    [IntegrationEvent(false, false)]
    Procedure OnSendLSPForApproval(var LSP: Record "Loading Slip Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelLSPForApproval(var LSP: Record "Loading Slip Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendLSPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendLSPforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendLSPForApproval', '', false, false)]
    local procedure RunworkflowonsendLSPForApproval(var LSP: Record "Loading Slip Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendLSPforApprovalCode(), LSP);
    end;

    procedure RunworkflowOnCancelLSPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelLSPForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelLSPForApproval', '', false, false)]

    local procedure RunworkflowonCancelLSPForApproval(var LSP: Record "Loading Slip Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelLSPforApprovalCode(), LSP);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryLSP();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendLSPforApprovalCode(), DATABASE::"Loading Slip Header",
          CopyStr(LSPsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelLSPforApprovalCode(), DATABASE::"Loading Slip Header",
          CopyStr(LSPrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryLSP(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelLSPforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelLSPforApprovalCode(), RunworkflowOnSendLSPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendLSPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendLSPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendLSPforApprovalCode());
        end;
    end;

    procedure ISLSPWorkflowenabled(var LSP: Record "Loading Slip Header"): Boolean
    begin
        if LSP."Approval Status" <> LSP."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(LSP, RunworkflowOnSendLSPforApprovalCode()));
    end;

    Procedure CheckLSPApprovalsWorkflowEnabled(var LSP: Record "Loading Slip Header"): Boolean
    begin
        IF not ISLSPworkflowenabled(LSP) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentLSP(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        LSP: Record "Loading Slip Header";
    begin
        case RecRef.Number() of
            Database::"Loading Slip Header":
                begin
                    RecRef.SetTable(LSP);
                    ApprovalEntryArgument."Document No." := FORMAT(LSP."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentLSP(RecRef: RecordRef; var Handled: boolean)
    var
        LSP: Record "Loading Slip Header";
    begin
        case RecRef.Number() of
            Database::"Loading Slip Header":
                begin
                    RecRef.SetTable(LSP);
                    LSP."Approval Status" := LSP."Approval Status"::open;
                    LSP.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentLSP(RecRef: RecordRef; var Handled: boolean)
    var
        LSP: Record "Loading Slip Header";
    begin
        case RecRef.Number() of
            Database::"Loading Slip Header":
                begin
                    RecRef.SetTable(LSP);
                    LSP."Approval Status" := LSP."Approval Status"::Released;
                    LSP.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalLSP(RecRef: RecordRef; var IsHandled: boolean)
    var
        LSP: Record "Loading Slip Header";
    begin
        case RecRef.Number() of
            Database::"Loading Slip Header":
                begin
                    RecRef.SetTable(LSP);
                    LSP."Approval Status" := LSP."Approval Status"::"Pending for Approval";
                    LSP.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryLSP(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendLSPforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendLSPforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelLSPforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelLSPforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryLSP()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(LSPCategoryTxt, 1, 20), CopyStr(LSPCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsLSP()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Loading Slip Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateLSP()
    begin
        InsertLSPApprovalworkflowtemplate();
    end;


    local procedure InsertLSPApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(LSPDocOCRWorkflowCodeTxt, 1, 17), CopyStr(LSPApprWorkflowDescTxt, 1, 100), CopyStr(LSPCategoryTxt, 1, 20));
        InsertLSPApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertLSPApprovalworkflowDetails(var workflow: record Workflow);
    var
        LSP: Record "Loading Slip Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildLSPtypecondition(LSP."Approval Status"::open), RunworkflowOnSendCPXforApprovalCode(), BuildLSPtypecondition(LSP."Approval Status"::"Pending for Approval"), RunworkflowOnCancelLSPforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildLSPtypecondition(status: integer): Text
    var
        LSP: Record "Loading Slip Header";
    Begin
        LSP.SetRange("Approval Status", Status);
        exit(StrSubstNo(LSPTypeCondnTxt, workflowsetup.Encode(LSP.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidLSP(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidLSP(RecordRef)
    end;

    local procedure GetConditionalcardPageidLSP(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Loading Slip Header":
                exit(page::"Loading Slip Card"
                );
        end;
    end;
    //Approval for Loading Slip  End <<
    //Approval for Capex Process Start >>

    [IntegrationEvent(false, false)]
    Procedure OnSendCPXForApproval(var CPX: Record "Budget Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCPXForApproval(var CPX: Record "Budget Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCPXforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCPXforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCPXForApproval', '', false, false)]
    local procedure RunworkflowonsendCPXForApproval(var CPX: Record "Budget Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCPXforApprovalCode(), CPX);
    end;

    procedure RunworkflowOnCancelCPXforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCPXForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCPXForApproval', '', false, false)]

    local procedure RunworkflowonCancelCPXForApproval(var CPX: Record "Budget Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCPXforApprovalCode(), CPX);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCPX();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCPXforApprovalCode(), DATABASE::"Budget Header",
          CopyStr(CPXsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCPXforApprovalCode(), DATABASE::"Budget Header",
          CopyStr(CPXrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryCPX(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCPXforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCPXforApprovalCode(), RunworkflowOnSendCPXforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCPXforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCPXforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCPXforApprovalCode());
        end;
    end;

    procedure ISCPXWorkflowenabled(var CPX: Record "Budget Header"): Boolean
    begin
        if CPX.Status <> CPX.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CPX, RunworkflowOnSendCPXforApprovalCode()));
    end;

    Procedure CheckCPXApprovalsWorkflowEnabled(var CPX: Record "Budget header"): Boolean
    begin
        IF not ISCPXworkflowenabled(CPX) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentCPX(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CPX: Record "Budget Header";
    begin
        case RecRef.Number() of
            Database::"Budget Header":
                begin
                    RecRef.SetTable(CPX);
                    ApprovalEntryArgument."Document No." := FORMAT(CPX."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentCPX(RecRef: RecordRef; var Handled: boolean)
    var
        CPX: Record "Budget Header";
    begin
        case RecRef.Number() of
            Database::"Budget Header":
                begin
                    RecRef.SetTable(CPX);
                    CPX.Status := CPX.Status::open;
                    CPX.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentCPX(RecRef: RecordRef; var Handled: boolean)
    var
        CPX: Record "Budget Header";
    begin
        case RecRef.Number() of
            Database::"Budget Header":
                begin
                    RecRef.SetTable(CPX);
                    CPX.Status := CPX.Status::Released;
                    CPX.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalCPX(RecRef: RecordRef; var IsHandled: boolean)
    var
        CPX: Record "Budget Header";
    begin
        case RecRef.Number() of
            Database::"Budget Header":
                begin
                    RecRef.SetTable(CPX);
                    CPX
                    .Status := CPX.Status::"Pending for Approval";
                    CPX.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCPX(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCPXforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCPXforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCPXforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCPXforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryCPX()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CPXCategoryTxt, 1, 20), CopyStr(CPXCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsCPX()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Budget Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateCPX()
    begin
        InsertCPXApprovalworkflowtemplate();
    end;


    local procedure InsertCPXApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CPXDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CPXApprWorkflowDescTxt, 1, 100), CopyStr(CPXCategoryTxt, 1, 20));
        InsertCPXApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCPXApprovalworkflowDetails(var workflow: record Workflow);
    var
        CPX: Record "Budget Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCPXtypecondition(CPX.Status::open), RunworkflowOnSendCPXforApprovalCode(), BuildCPXtypecondition(CPX.Status::"Pending for Approval"), RunworkflowOnCancelCPXforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCPXtypecondition(status: integer): Text
    var
        CPX: Record "Budget Header";
    Begin
        CPX.SetRange(status, Status);
        exit(StrSubstNo(CPXTypeCondnTxt, workflowsetup.Encode(CPX.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidCPX(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() =
            database::"Budget Header" then
            PageID := GetConditionalcardPageidCPX(RecordRef)
    end;

    local procedure GetConditionalcardPageidCPX(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Budget Header":
                exit(page::"Capex Budget"
                );
        end;
    end;

    //Approval for Capex Process End <<





    //Approval for CompanyCreditLimit start >>
    [IntegrationEvent(false, false)]
    Procedure OnSendSCLForApproval(var SCL: Record "Special Credit Limit")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelSCLForApproval(var SCL: Record "Special Credit Limit")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendSCLforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendSCLforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendSCLForApproval', '', false, false)]
    local procedure RunworkflowonsendSCLForApproval(var SCL: Record "Special Credit Limit")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendSCLforApprovalCode(), SCL);
    end;

    procedure RunworkflowOnCancelSCLforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelSCLForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelSCLForApproval', '', false, false)]

    local procedure RunworkflowonCancelSCLForApproval(var SCL: Record "Special Credit Limit")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelSCLforApprovalCode(), SCL);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrarySCL();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendSCLforApprovalCode(), DATABASE::"Special Credit Limit",
          CopyStr(SCLsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelSCLforApprovalCode(), DATABASE::"Special Credit Limit",
          CopyStr(SCLrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibrarySCL(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelSCLforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelSCLforApprovalCode(), RunworkflowOnSendSCLforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendSCLforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendSCLforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendSCLforApprovalCode());
        end;
    end;

    procedure ISSCLWorkflowenabled(var SCL: Record "Special Credit Limit"): Boolean
    begin
        if SCL."Approval Status" <> SCL."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(SCL, RunworkflowOnSendSCLforApprovalCode()));
    end;

    Procedure CheckSCLApprovalsWorkflowEnabled(var SCL: Record "Special Credit Limit"): Boolean
    begin
        IF not ISSCLworkflowenabled(SCL) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentSCL(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        SCL: Record "Special Credit Limit";
    begin
        case RecRef.Number() of
            Database::"Special Credit Limit":
                begin
                    RecRef.SetTable(SCL);
                    ApprovalEntryArgument."Document No." := FORMAT(SCL."Entry No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentSCL(RecRef: RecordRef; var Handled: boolean)
    var
        SCL: Record "Special Credit Limit";
    begin
        case RecRef.Number() of
            Database::"Special Credit Limit":
                begin
                    RecRef.SetTable(SCL);
                    SCL."Approval Status" := SCL."Approval Status"::open;
                    SCL.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentSCL(RecRef: RecordRef; var Handled: boolean)
    var
        SCL: Record "Special Credit Limit";
    begin
        case RecRef.Number() of
            Database::"Special Credit Limit":
                begin
                    RecRef.SetTable(SCL);
                    SCL."Approval Status" := SCL."Approval Status"::Released;
                    SCL.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalSCL(RecRef: RecordRef; var IsHandled: boolean)
    var
        SCL: Record "Special Credit Limit";
    begin
        case RecRef.Number() of
            Database::"Special Credit Limit":
                begin
                    RecRef.SetTable(SCL);
                    SCL."Approval Status" := SCL."Approval Status"::"Pending for Approval";
                    SCL.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibrarySCL(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendSCLforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendSCLforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelSCLforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelSCLforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibrarySCL()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(SCLCategoryTxt, 1, 20), CopyStr(SCLCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsSCL()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Special Credit Limit", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateSCL()
    begin
        InsertSCLApprovalworkflowtemplate();
    end;


    local procedure InsertSCLApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(SCLDocOCRWorkflowCodeTxt, 1, 17), CopyStr(SCLApprWorkflowDescTxt, 1, 100), CopyStr(SCLCategoryTxt, 1, 20));
        InsertSCLApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertSCLApprovalworkflowDetails(var workflow: record Workflow);
    var
        SCL: Record "Special Credit Limit";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildSCLtypecondition(SCL."Approval Status"::open), RunworkflowOnSendSCLforApprovalCode(), BuildSCLtypecondition(SCL."Approval Status"::"Pending for Approval"), RunworkflowOnCancelSCLforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildSCLtypecondition(status: integer): Text
    var
        SCL: Record "Special Credit Limit";
    Begin
        SCL.SetRange("Approval Status", Status);
        exit(StrSubstNo(SCLTypeCondnTxt, workflowsetup.Encode(SCL.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidSCL(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Special Credit Limit" then
            PageID := GetConditionalcardPageidSCL(RecordRef)
    end;

    local procedure GetConditionalcardPageidSCL(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Special Credit Limit":
                exit(page::"Special Credit Limit"
                );
        end;
    end;
    //Approval for ComapnyCreditLimit End <<


    //Approval for GateEntry Process >>

    [IntegrationEvent(false, false)]
    Procedure OnSendGATEForApproval(var GATE: Record "Gate Entry Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelGATEForApproval(var GATE: Record "Gate Entry Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendGATEforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendGATEforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendGATEForApproval', '', false, false)]
    local procedure RunworkflowonsendGATEForApproval(var GATE: Record "Gate Entry Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendGATEforApprovalCode(), GATE);
    end;

    procedure RunworkflowOnCancelGATEforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelGATEForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelGATEForApproval', '', false, false)]

    local procedure RunworkflowonCancelGATEForApproval(var GATE: Record "Gate Entry Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelGATEforApprovalCode(), GATE);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryGATE();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendGATEforApprovalCode(), DATABASE::"Gate Entry Header",
          CopyStr(GATEsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelGATEforApprovalCode(), DATABASE::"Gate Entry Header",
          CopyStr(GATErequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryGATE(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelGATEforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelGATEforApprovalCode(), RunworkflowOnSendGATEforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendGATEforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendGATEforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendGATEforApprovalCode());
        end;
    end;

    procedure ISGATEWorkflowenabled(var GATE: Record "Gate Entry Header"): Boolean
    begin
        if GATE."Approval Status" <> GATE."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(GATE, RunworkflowOnSendGATEforApprovalCode()));
    end;

    Procedure CheckGATEApprovalsWorkflowEnabled(var GATE: Record "Gate Entry Header"): Boolean
    begin
        IF not ISGATEworkflowenabled(GATE) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentGATE(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        GATE: Record "Gate Entry Header";
    begin
        case RecRef.Number() of
            Database::"Gate Entry Header":
                begin
                    RecRef.SetTable(GATE);
                    ApprovalEntryArgument."Document No." := FORMAT(GATE."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentGATE(RecRef: RecordRef; var Handled: boolean)
    var
        GATE: Record "Gate Entry Header";
    begin
        case RecRef.Number() of
            Database::"Gate Entry Header":
                begin
                    RecRef.SetTable(GATE);
                    GATE."Approval Status" := GATE."Approval Status"::open;
                    GATE.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentGATE(RecRef: RecordRef; var Handled: boolean)
    var
        GATE: Record "Gate Entry Header";
    begin
        case RecRef.Number() of
            Database::"Gate Entry Header":
                begin
                    RecRef.SetTable(GATE);
                    GATE."Approval Status" := GATE."Approval Status"::Released;
                    GATE.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalGATE(RecRef: RecordRef; var IsHandled: boolean)
    var
        GATE: Record "Gate Entry Header";
    begin
        case RecRef.Number() of
            Database::"Gate Entry Header":
                begin
                    RecRef.SetTable(GATE);
                    GATE."Approval Status" := GATE."Approval Status"::"Pending for Approval";
                    GATE.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryGATE(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendGATEforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendGATEforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelGATEforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelGATEforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryGATE()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(GATECategoryTxt, 1, 20), CopyStr(GATECategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsGATE()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Gate Entry Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateGATE()
    begin
        InsertGATEApprovalworkflowtemplate();
    end;


    local procedure InsertGATEApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(GATEDocOCRWorkflowCodeTxt, 1, 17), CopyStr(GATEApprWorkflowDescTxt, 1, 100), CopyStr(GATECategoryTxt, 1, 20));
        InsertGATEApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertGATEApprovalworkflowDetails(var workflow: record Workflow);
    var
        GATE: Record "Gate Entry Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildSCLtypecondition(GATE."Approval Status"::open), RunworkflowOnSendGATEforApprovalCode(), BuildGATEtypecondition(GATE."Approval Status"::"Pending for Approval"), RunworkflowOnCancelGATEforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildGATEtypecondition(status: integer): Text
    var
        GATE: Record "Gate Entry Header";
    Begin
        GATE.SetRange("Approval Status", Status);
        exit(StrSubstNo(GATETypeCondnTxt, workflowsetup.Encode(GATE.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidGATE(RecordRef: RecordRef; var PageID: Integer)
    begin
        If RecordRef.Number() = database::"Gate Entry Header" then
            PageID := GetConditionalcardPageidGATE(RecordRef)
    end;

    local procedure GetConditionalcardPageidGATE(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Gate Entry Header":
                exit(page::"Inward Gate Entry-NRGP"
                );
        end;
    end;

    //Approval for GateEntry Process <<
    //Approval For Bank AccountCheck Start >>
    [IntegrationEvent(false, false)]
    Procedure OnSendBCDForApproval(var BCD: Record "Bank Cheque Details")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelBCDForApproval(var BCD: Record "Bank Cheque Details")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendBCDforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendBCDforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendBCDForApproval', '', false, false)]
    local procedure RunworkflowonsendBCDForApproval(var BCD: Record "Bank Cheque Details")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendBCDforApprovalCode(), BCD);
    end;

    procedure RunworkflowOnCancelBCDforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelBCDForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelBCDForApproval', '', false, false)]

    local procedure RunworkflowonCancelBCDForApproval(var BCD: Record "Bank Cheque Details")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelBCDforApprovalCode(), BCD);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryBCD();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendBCDforApprovalCode(), DATABASE::"Bank Cheque Details",
          CopyStr(BCDsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelBCDforApprovalCode(), DATABASE::"Bank Cheque Details",
          CopyStr(BCDrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryBCD(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelBCDforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelBCDforApprovalCode(), RunworkflowOnSendBCDforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendBCDforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendBCDforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendBCDforApprovalCode());
        end;
    end;

    procedure ISBCDWorkflowenabled(var BCD: Record "Bank Cheque Details"): Boolean
    begin
        if BCD."Approval Status" <> BCD."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(BCD, RunworkflowOnSendBCDforApprovalCode()));
    end;

    Procedure CheckBCDApprovalsWorkflowEnabled(var BCD: Record "Bank Cheque Details"): Boolean
    begin
        IF not ISBCDworkflowenabled(BCD) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentBCD(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        BCD: Record "Bank Cheque Details";
    begin
        case RecRef.Number() of
            Database::"Bank Cheque Details":
                begin
                    RecRef.SetTable(BCD);
                    ApprovalEntryArgument."Document No." := FORMAT(BCD."Bank No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentBCD(RecRef: RecordRef; var Handled: boolean)
    var
        BCD: Record "Bank Cheque Details";
    begin
        case RecRef.Number() of
            Database::"Bank Cheque Details":
                begin
                    RecRef.SetTable(BCD);
                    BCD."Approval Status" := BCD."Approval Status"::open;
                    BCD.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentBCD(RecRef: RecordRef; var Handled: boolean)
    var
        BCD: Record "Bank Cheque Details";
    begin
        case RecRef.Number() of
            Database::"Bank Cheque Details":
                begin
                    RecRef.SetTable(BCD);
                    BCD."Approval Status" := BCD."Approval Status"::Released;
                    BCD.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalBCD(RecRef: RecordRef; var IsHandled: boolean)
    var
        BCD: Record "Bank Cheque Details";
    begin
        case RecRef.Number() of
            Database::"Bank Cheque Details":
                begin
                    RecRef.SetTable(BCD);
                    BCD."Approval Status" := BCD."Approval Status"::"Pending for Approval";
                    BCD.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryBCD(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendBCDforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendBCDforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelBCDforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelBCDforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryBCD()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(BCDCategoryTxt, 1, 20), CopyStr(BCDCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsBCD()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Bank Cheque Details", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateBCD()
    begin
        InsertBCDApprovalworkflowtemplate();
    end;


    local procedure InsertBCDApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(BCDDocOCRWorkflowCodeTxt, 1, 17), CopyStr(BCDApprWorkflowDescTxt, 1, 100), CopyStr(BCDCategoryTxt, 1, 20));
        InsertBCDApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertBCDApprovalworkflowDetails(var workflow: record Workflow);
    var
        BCD: Record "Bank Cheque Details";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildBCDtypecondition(BCD."Approval Status"::open), RunworkflowOnSendLSPforApprovalCode(), BuildLSPtypecondition(BCD."Approval Status"::"Pending for Approval"), RunworkflowOnCancelBCDforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildBCDtypecondition(status: integer): Text
    var
        BCD: Record "Bank Cheque Details";
    Begin
        BCD.SetRange("Approval Status", Status);
        exit(StrSubstNo(BCDTypeCondnTxt, workflowsetup.Encode(BCD.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidBCD(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Bank Cheque Details" then
            PageID := GetConditionalcardPageidBCD(RecordRef)
    end;

    local procedure GetConditionalcardPageidBCD(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Bank Cheque Details":
                exit(page::"Bank Cheque Details Card"
                );
        end;
    end;
    //Approval For Bank AccountCheck End<<

    /* //PJ Prasanna Commented -- Start
    //Bank Account Card Start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendBACForApproval(var BAC: Record "Bank Account")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelBACForApproval(var BAC: Record "Bank Account")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendBACforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendBACforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendBACForApproval', '', false, false)]
    local procedure RunworkflowonsendBACForApproval(var BAC: Record "Bank Account")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendBACforApprovalCode(), BAC);
    end;

    procedure RunworkflowOnCancelBACforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelBACForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelBACForApproval', '', false, false)]

    local procedure RunworkflowonCancelBACForApproval(var BAC: Record "Bank Account")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelBACforApprovalCode(), BAC);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryBAC();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendBACforApprovalCode(), DATABASE::"Bank Account",
          CopyStr(BACsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelBACforApprovalCode(), DATABASE::"Bank Account",
          CopyStr(BACrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryBAC(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelBACforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelBACforApprovalCode(), RunworkflowOnSendBACforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendBACforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendBACforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendBACforApprovalCode());
        end;
    end;

    procedure ISBACWorkflowenabled(var BAC: Record "Bank Account"): Boolean
    begin
        if BAC."Approval Status" <> BAC."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(BAC, RunworkflowOnSendBACforApprovalCode()));
    end;

    Procedure CheckBACApprovalsWorkflowEnabled(var BAC: Record "Bank Account"): Boolean
    begin
        IF not ISBACworkflowenabled(BAC) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentBAC(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        BAC: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BAC);
                    ApprovalEntryArgument."Document No." := FORMAT(BAC."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentBAC(RecRef: RecordRef; var Handled: boolean)
    var
        BAC: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BAC);
                    BAC."Approval Status" := BAC."Approval Status"::open;
                    BAC.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentBAC(RecRef: RecordRef; var Handled: boolean)
    var
        BAC: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BAC);
                    BAC."Approval Status" := BAC."Approval Status"::Released;
                    BAC.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalBAC(RecRef: RecordRef; var IsHandled: boolean)
    var
        BAC: Record "Bank Account";
    begin
        case RecRef.Number() of
            Database::"Bank Account":
                begin
                    RecRef.SetTable(BAC);
                    BAC."Approval Status" := BAC."Approval Status"::"Pending for Approval";
                    BAC.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryBAC(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendBACforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendBACforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelBACforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelBACforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryBAC()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(BACCategoryTxt, 1, 20), CopyStr(BACCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsBAC()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Bank Account", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateBAC()
    begin
        InsertBACApprovalworkflowtemplate();
    end;


    local procedure InsertBACApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(BACDocOCRWorkflowCodeTxt, 1, 17), CopyStr(BCDApprWorkflowDescTxt, 1, 100), CopyStr(BCDCategoryTxt, 1, 20));
        InsertBACApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertBACApprovalworkflowDetails(var workflow: record Workflow);
    var
        BAC: Record "Bank Account";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildBCDtypecondition(BAC."Approval Status"::open), RunworkflowOnSendLSPforApprovalCode(), BuildLSPtypecondition(BAC."Approval Status"::"Pending for Approval"), RunworkflowOnCancelBACforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildBACtypecondition(status: integer): Text
    var
        BAC: Record "Bank Account";
    Begin
        BAC.SetRange("Approval Status", Status);
        exit(StrSubstNo(BACTypeCondnTxt, workflowsetup.Encode(BAC.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidBAC(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidBAC(RecordRef)
    end;

    local procedure GetConditionalcardPageidBAC(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Bank Cheque Details":
                exit(page::"Bank Cheque Details Card"
                );
        end;
    end;
    //Bank Account Card End<<
    */
    //PJ Prasanna Commented -- End

    //Promo Schedule Start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendPRSForApproval(var PRS: Record "Promo Schedule")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPRSForApproval(var PRS: Record "Promo Schedule")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendPRSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPRSforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendPRSForApproval', '', false, false)]
    local procedure RunworkflowonsendPRSForApproval(var PRS: Record "Promo Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPRSforApprovalCode(), PRS);
    end;

    procedure RunworkflowOnCancelPRSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPRSForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelPRSForApproval', '', false, false)]

    local procedure RunworkflowonCancelPRSForApproval(var PRS: Record "Promo Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPRSforApprovalCode(), PRS);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPRS();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPRSforApprovalCode(), DATABASE::"Promo Schedule",
          CopyStr(PRSsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPRSforApprovalCode(), DATABASE::"Promo Schedule",
          CopyStr(PRSrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPRS(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPRSforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPRSforApprovalCode(), RunworkflowOnSendPRSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPRSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPRSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPRSforApprovalCode());
        end;
    end;

    procedure ISPRSWorkflowenabled(var PRS: Record "Promo Schedule"): Boolean
    begin
        if PRS.Status <> PRS.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(PRS, RunworkflowOnSendPRSforApprovalCode()));
    end;

    Procedure CheckPRSApprovalsWorkflowEnabled(var PRS: Record "Promo Schedule"): Boolean
    begin
        IF not ISPRSworkflowenabled(PRS) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPRS(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PRS: Record "Promo Schedule";
    begin
        case RecRef.Number() of
            Database::"Promo Schedule":
                begin
                    RecRef.SetTable(PRS);
                    ApprovalEntryArgument."Document No." := FORMAT(PRS."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPRS(RecRef: RecordRef; var Handled: boolean)
    var
        PRS: Record "Promo Schedule";
    begin
        case RecRef.Number() of
            Database::"Promo Schedule":
                begin
                    RecRef.SetTable(PRS);
                    PRS.Status := PRS.Status::open;
                    PRS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPRS(RecRef: RecordRef; var Handled: boolean)
    var
        PRS: Record "Promo Schedule";
    begin
        case RecRef.Number() of
            Database::"Promo Schedule":
                begin
                    RecRef.SetTable(PRS);
                    PRS.Status := PRS.Status::Released;
                    PRS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalPRS(RecRef: RecordRef; var IsHandled: boolean)
    var
        PRS: Record "Promo Schedule";
    begin
        case RecRef.Number() of
            Database::"Promo Schedule":
                begin
                    RecRef.SetTable(PRS);
                    PRS.Status := PRS.Status::"Pending Approval";
                    PRS.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPRS(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPRSforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPRSforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPRSforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPRSforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPRS()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PRSCategoryTxt, 1, 20), CopyStr(PRSCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPRS()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Promo Schedule", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePRS()
    begin
        InsertPRSApprovalworkflowtemplate();
    end;


    local procedure InsertPRSApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(PRSDocOCRWorkflowCodeTxt, 1, 17), CopyStr(PRSApprWorkflowDescTxt, 1, 100), CopyStr(PRSCategoryTxt, 1, 20));
        InsertPRSApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPRSApprovalworkflowDetails(var workflow: record Workflow);
    var
        PRS: Record "Promo Schedule";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPRStypecondition(PRS.Status::open), RunworkflowOnSendPRSforApprovalCode(), BuildPRStypecondition(PRS.Status::"Pending Approval"), RunworkflowOnCancelPRSforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPRStypecondition(status: integer): Text
    var
        PRS: Record "Promo Schedule";
    Begin
        PRS.SetRange(status, Status);
        exit(StrSubstNo(PRSTypeCondnTxt, workflowsetup.Encode(PRS.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPRS(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Promo Schedule" then
            PageID := GetConditionalcardPageidPRS(RecordRef)
    end;

    local procedure GetConditionalcardPageidPRS(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Promo Schedule":
                exit(page::"Promo. Schedule"
                );
        end;
    end;
    //Promo Schedule End<<
    //Transfer Order Approvals Start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendTROForApproval(var TRO: Record "Transfer Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelTROForApproval(var TRO: Record "Transfer Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendTROforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendTROforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendTROForApproval', '', false, false)]
    local procedure RunworkflowonsendTROForApproval(var TRO: Record "Transfer Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendTROforApprovalCode(), TRO);
    end;

    procedure RunworkflowOnCancelTROforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelTROForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelTROForApproval', '', false, false)]

    local procedure RunworkflowonCancelTROForApproval(var TRO: Record "Transfer Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelTROforApprovalCode(), TRO);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryTRO();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendTROforApprovalCode(), DATABASE::"Transfer Header",
          CopyStr(TROsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelTROforApprovalCode(), DATABASE::"Transfer Header",
          CopyStr(TROrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryTRO(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelTROforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelTROforApprovalCode(), RunworkflowOnSendTROforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendTROforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendTROforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendTROforApprovalCode());
        end;
    end;

    procedure ISTROWorkflowenabled(var TRO: Record "Transfer Header"): Boolean
    begin
        if TRO."Approval Status" <> TRO."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(TRO, RunworkflowOnSendTROforApprovalCode()));
    end;

    Procedure CheckTROApprovalsWorkflowEnabled(var TRO: Record "Transfer Header"): Boolean
    begin
        IF not ISTROworkflowenabled(TRO) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentTRO(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        TRO: Record "Transfer Header";
    begin
        case RecRef.Number() of
            Database::"Transfer Header":
                begin
                    RecRef.SetTable(TRO);
                    ApprovalEntryArgument."Document No." := FORMAT(TRO."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentTRO(RecRef: RecordRef; var Handled: boolean)
    var
        TRO: Record "Transfer Header";
        WhseTransferRelease: codeunit "Release Transfer Document";
    begin
        case RecRef.Number() of
            Database::"Transfer Header":
                begin
                    RecRef.SetTable(TRO);
                    TRO."Approval Status" := TRO."Approval Status"::open;
                    WhseTransferRelease.Reopen(TRO);
                    TRO.Validate(Status, TRO.Status::Open);
                    TRO.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentTRO(RecRef: RecordRef; var Handled: boolean)
    var
        TRO: Record "Transfer Header";
    begin
        case RecRef.Number() of
            Database::"Transfer Header":
                begin
                    RecRef.SetTable(TRO);
                    TRO."Approval Status" := TRO."Approval Status"::Released;
                    //TRO.Validate(Status, TRO.Status::Released);
                    CODEUNIT.RUN(Codeunit::"Release Transfer Document", TRO);
                    TRO.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalTRO(RecRef: RecordRef; var IsHandled: boolean)
    var
        TRO: Record "Transfer Header";
    begin
        case RecRef.Number() of
            Database::"Transfer Header":
                begin
                    RecRef.SetTable(TRO);
                    TRO."Approval Status" := TRO."Approval Status"::"Pending for Approval";
                    TRO.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryTRO(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendTROforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendTROforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelTROforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelTROforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryTRO()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(TROCategoryTxt, 1, 20), CopyStr(TROCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsTRO()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Transfer Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateTRO()
    begin
        InsertTROApprovalworkflowtemplate();
    end;


    local procedure InsertTROApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(TRODocOCRWorkflowCodeTxt, 1, 17), CopyStr(TROApprWorkflowDescTxt, 1, 100), CopyStr(TROCategoryTxt, 1, 20));
        InsertTROApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertTROApprovalworkflowDetails(var workflow: record Workflow);
    var
        TRO: Record "Transfer Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildTROtypecondition(TRO."Approval Status"::open), RunworkflowOnSendLSPforApprovalCode(), BuildLSPtypecondition(TRO."Approval Status"::"Pending for Approval"), RunworkflowOnCancelTROforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildTROtypecondition(status: integer): Text
    var
        TRO: Record "Transfer Header";
    Begin
        TRO.SetRange("Approval Status", Status);
        exit(StrSubstNo(TROTypeCondnTxt, workflowsetup.Encode(TRO.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidTRO(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidTRO(RecordRef)
    end;

    local procedure GetConditionalcardPageidTRO(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Transfer Header":
                exit(page::"Transfer Order"
                );
        end;
    end;

    //Transfer Order Approval End<<

    //Item Approval Start
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalForItem(RecRef: RecordRef; var IsHandled: boolean)
    var
        Item: Record Item;
    begin
        case RecRef.Number() of
            Database::Item:
                begin
                    RecRef.SetTable(Item);
                    Item."Approval Status" := Item."Approval Status"::"Pending for Approval";
                    Item.Blocked := true;
                    Item.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentItem(RecRef: RecordRef; var Handled: boolean)
    var
        Item: Record Item;
    begin
        case RecRef.Number() of
            Database::Item:
                begin
                    RecRef.SetTable(Item);
                    Item."Approval Status" := Item."Approval Status"::Released;
                    Item.Blocked := false;
                    Item.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentItem(RecRef: RecordRef; var Handled: boolean)
    var
        Item: Record Item;
    begin
        case RecRef.Number() of
            Database::Item:
                begin
                    RecRef.SetTable(Item);
                    Item."Approval Status" := Item."Approval Status"::Open;
                    Item.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnCancelItemApprovalRequest', '', false, false)]
    local procedure OnCanceldocumentItem(Item: Record Item)
    var
    begin
        Item."Approval Status" := Item."Approval Status"::Open;
        item.Modify();
    end;
    //Item Approval end

    //MDV Approvals Start
    [IntegrationEvent(false, false)]
    Procedure OnSendMDVHeaderForApproval(var "MDV Header": Record "MDV Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelMDVHeaderForApproval(var "MDV Header": Record "MDV Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendMDVHeaderforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendMaterialDisposalVoucherforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendMDVHeaderForApproval', '', true, true)]
    local procedure RunworkflowonsendMDForApproval(var "MDV Header": Record "MDV Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendMDVHeaderforApprovalCode(), "MDV Header");
    end;

    procedure RunworkflowOnCancelMDVHeaderforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelMaterialDisposalVoucherForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnCancelMDVHeaderForApproval', '', true, true)]

    local procedure RunworkflowonCancelMDVHeaderForApproval(var "MDV Header": Record "MDV Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelMDVHeaderforApprovalCode(), "MDV Header");
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendMDVHeaderforApprovalCode(), DATABASE::"MDV Header",
          CopyStr(MDVsendforapprovaleventdescTxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelMDVHeaderforApprovalCode(), DATABASE::"MDV Header",
          CopyStr(MDVrequestcanceleventdescTxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelMDVHeaderforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelMDVHeaderforApprovalCode(), RunworkflowOnSendMDVHeaderforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendMDVHeaderForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendMDVHeaderForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendMDVHeaderForApprovalCode());
        end;
    end;

    procedure ISMDVHeaderworkflowenabled(var "MDV Header": Record "MDV Header"): Boolean
    begin
        if "MDV Header"."Approval Status" <> "MDV Header"."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow("MDV Header", RunworkflowOnSendMDVHeaderforApprovalCode()));
    end;

    Procedure MDVHeaderApprovalsWorkflowEnabled(var "MDV Header": Record "MDV Header"): Boolean
    begin
        IF not ISMDVHeaderworkflowenabled("MDV Header") then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        "MDV Header": Record "MDV Header";
    begin
        case RecRef.Number() of
            Database::"MDV Header":
                begin
                    RecRef.SetTable("MDV Header");
                    ApprovalEntryArgument."Document No." := FORMAT("MDV Header"."MDV No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument(RecRef: RecordRef; var Handled: boolean)
    var
        "MDV Header": Record "MDV Header";
    begin
        case RecRef.Number() of
            Database::"MDV Header":
                begin
                    RecRef.SetTable("MDV Header");
                    "MDV Header"."Approval Status" := "MDV Header"."Approval Status"::Open;
                    "MDV Header".Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument(RecRef: RecordRef; var Handled: boolean)
    var
        "MDV Header": Record "MDV Header";
    begin
        case RecRef.Number() of
            Database::"MDV Header":
                begin
                    RecRef.SetTable("MDV Header");
                    "MDV Header"."Approval Status" := "MDV Header"."Approval Status"::Released;
                    "MDV Header".Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval(RecRef: RecordRef; var IsHandled: boolean)
    var
        "MDV Header": Record "MDV Header";
    begin
        case RecRef.Number() of
            Database::"MDV Header":
                begin
                    RecRef.SetTable("MDV Header");
                    "MDV Header"."Approval Status" := "MDV Header"."Approval Status"::"Pending for Approval";
                    "MDV Header".Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendMDVHeaderforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendMDVHeaderforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelMDVHeaderforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelMDVHeaderforApprovalCode());
        end;
    end;

    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(MDVCategoryTxt, 1, 20), CopyStr(MDVCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"MDV Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate()
    begin
        MDVHeaderApprovalworkflowtemplate();
    end;

    local procedure MDVHeaderApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt, 1, 17), CopyStr(MDVApprWorkflowDescTxt, 1, 100), CopyStr(MDVCategoryTxt, 1, 20));
        InsertMDVheaderApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertMDVheaderApprovalworkflowDetails(var workflow: record Workflow);
    var
        "MDV Header": Record "MDV Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildMDVcondition("MDV Header"."Approval Status"::open), RunworkflowOnSendMDVHeaderforApprovalCode(), BuildMDVcondition("MDV Header"."Approval Status"::"Pending for Approval"), RunworkflowOnCancelMDVHeaderforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildMDVcondition(status: integer): Text
    var
        ValueBase: Record "MDV Header";
    Begin
        ValueBase.SetRange("Approval Status", status);
        exit(StrSubstNo(MDVTypeCondnTxt, workflowsetup.Encode(ValueBase.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageid(RecordRef)
    end;

    local procedure GetConditionalcardPageid(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"MDV Header":
                exit(page::"Material Disposals");
        end;
    end;
    //MDV Approvals End



    //Company Credit Limit Schedule  START

    [IntegrationEvent(false, false)]
    Procedure OnSendCompCreditLimitScheduleForApproval(var CompCreditLimitSchedule: Record "Company Cr. Limit Schedule")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCompCreditLimitScheduleForApproval(var CompCreditLimitSchedule: Record "Company Cr. Limit Schedule")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCompCreditLimitScheduleforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCompCreditLimitScheduleforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCompCreditLimitScheduleForApproval', '', true, true)]
    local procedure RunworkflowonsendCompCreditLimitScheduleForApproval(var CompCreditLimitSchedule: Record "Company Cr. Limit Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCompCreditLimitScheduleforApprovalCode(), CompCreditLimitSchedule);
    end;

    procedure RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCompCreditLimitScheduleForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCompCreditLimitScheduleForApproval', '', true, true)]

    local procedure RunworkflowonCancelCompCreditLimitScheduleForApproval(var CompCreditLimitSchedule: Record "Company Cr. Limit Schedule")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCompCreditLimitScheduleforApprovalCode(), CompCreditLimitSchedule);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCompCreditLimitSchedule();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCompCreditLimitScheduleforApprovalCode(), DATABASE::"Company Cr. Limit Schedule",
          CopyStr(CompCreditLimitSchedulesendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode(), DATABASE::"Company Cr. Limit Schedule",
          CopyStr(CompCreditLimitSchedulerequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryCompCreditLimitSchedule(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode(), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode());
        end;
    end;

    procedure ISCompCreditLimitScheduleworkflowenabled(var CompCreditLimitSchedule: Record "Company Cr. Limit Schedule"): Boolean
    begin
        if CompCreditLimitSchedule.Status <> CompCreditLimitSchedule.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CompCreditLimitSchedule, RunworkflowOnSendCompCreditLimitScheduleforApprovalCode()));
    end;

    Procedure CheckCompCreditLimitScheduleApprovalsWorkflowEnabled(var CompCreditLimitSchedule: Record "Company Cr. Limit Schedule"): Boolean
    begin
        IF not ISCompCreditLimitScheduleworkflowenabled(CompCreditLimitSchedule) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentCompCreditLimitSchedule(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CompCreditLimitSchedule: Record "Company Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CompCreditLimitSchedule);
                    ApprovalEntryArgument."Document No." := FORMAT(CompCreditLimitSchedule."Line No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentCompCreditLimitSchedule(RecRef: RecordRef; var Handled: boolean)
    var
        CompCreditLimitSchedule: Record "Company Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CompCreditLimitSchedule);
                    CompCreditLimitSchedule.Status := CompCreditLimitSchedule.Status::open;
                    CompCreditLimitSchedule.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentCompCreditLimitSchedule(RecRef: RecordRef; var Handled: boolean)
    var
        CompCreditLimitSchedule: Record "Company Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CompCreditLimitSchedule);
                    CompCreditLimitSchedule.Status := CompCreditLimitSchedule.Status::Released;
                    CompCreditLimitSchedule.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalCompCreditLimitSchedule(RecRef: RecordRef; var IsHandled: boolean)
    var
        CompCreditLimitSchedule: Record "Company Cr. Limit Schedule";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Schedule":
                begin
                    RecRef.SetTable(CompCreditLimitSchedule);
                    CompCreditLimitSchedule.Status := CompCreditLimitSchedule.Status::"Pending Approval";
                    CompCreditLimitSchedule.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCompCustCreLimitSchedule(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryCompCreditLimitSchedule()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CompCreditLimitScheduleCategoryTxt, 1, 20), CopyStr(CompCreditLimitScheduleCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsCompCreditLimitSchedule()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Company Cr. Limit Schedule", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateCompCreditLimitSchedule()
    begin
        InsertCompCreditLimitScheduleApprovalworkflowtemplate();
    end;

    local procedure InsertCompCreditLimitScheduleApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CompCreditLimitScheduleDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CompCreditLimitScheduleApprWorkflowDescTxt, 1, 100), CopyStr(CompCreditLimitScheduleCategoryTxt, 1, 20));
        InsertCompCreditLimitScheduleApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCompCreditLimitScheduleApprovalworkflowDetails(var workflow: record Workflow);
    var
        CustCrLimit: Record "Company Cr. Limit Schedule";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCompCreditLimitScheduletypecondition(CustCrLimit.Status::open), RunworkflowOnSendCompCreditLimitScheduleforApprovalCode(), BuildCompCreditLimitScheduletypecondition(CustCrLimit.Status::"Pending Approval"), RunworkflowOnCancelCompCreditLimitScheduleforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCompCreditLimitScheduletypecondition(status: integer): Text
    var
        CustCrLimt: Record "Company Cr. Limit Schedule";
    Begin
        CustCrLimt.SetRange(status, status);
        exit(StrSubstNo(CompCreditLimitScheduleTypeCondnTxt, workflowsetup.Encode(CustCrLimt.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure OnaftergetpageidCompCreditLimitSchedule(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidCompCreditLimitSchedule(RecordRef)
    end;

    local procedure GetConditionalcardPageidCompCreditLimitSchedule(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Company Cr. Limit Schedule":
                exit(page::"Company Cr. Limit Schedule");
        end;
    end;
    //Company Credit Limit Schedule END



    //Company Credit Limit Budget  START

    [IntegrationEvent(false, false)]
    Procedure OnSendCompCreditLimitBudgetForApproval(var CompCreditLimitBudget: Record "Company Cr. Limit Budget")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCompCreditLimitBudgetForApproval(var CompCreditLimitBudget: Record "Company Cr. Limit Budget")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCompCreditLimitBudgetforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCompCreditLimitBudgetforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCompCreditLimitBudgetForApproval', '', true, true)]
    local procedure RunworkflowonsendCompCreditLimitBudgetForApproval(var CompCreditLimitBudget: Record "Company Cr. Limit Budget")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCompCreditLimitBudgetforApprovalCode(), CompCreditLimitBudget);
    end;

    procedure RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCompCreditLimitBudgetForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCompCreditLimitBudgetForApproval', '', true, true)]

    local procedure RunworkflowonCancelCompCreditLimitBudgetForApproval(var CompCreditLimitBudget: Record "Company Cr. Limit Budget")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCompCreditLimitBudgetforApprovalCode(), CompCreditLimitBudget);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCompCreditLimitBudget();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCompCreditLimitBudgetforApprovalCode(), DATABASE::"Company Cr. Limit Budget",
          CopyStr(CompCreditLimitBudgetsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode(), DATABASE::"Company Cr. Limit Budget",
          CopyStr(CompCreditLimitBudgetrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryCompCreditLimitBudget(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode(), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode());
        end;
    end;

    procedure ISCompCreditLimitBudgetworkflowenabled(var CompCreditLimitBudget: Record "Company Cr. Limit Budget"): Boolean
    begin
        if CompCreditLimitBudget.Status <> CompCreditLimitBudget.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CompCreditLimitBudget, RunworkflowOnSendCompCreditLimitBudgetforApprovalCode()));
    end;

    Procedure CheckCompCreditLimitBudgetApprovalsWorkflowEnabled(var CompCreditLimitBudget: Record "Company Cr. Limit Budget"): Boolean
    begin
        IF not ISCompCreditLimitBudgetworkflowenabled(CompCreditLimitBudget) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentCompCreditLimitBudget(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CompCreditLimitBudget: Record "Company Cr. Limit Budget";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Budget":
                begin
                    RecRef.SetTable(CompCreditLimitBudget);
                    ApprovalEntryArgument."Document No." := FORMAT(CompCreditLimitBudget."Line No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentCompCreditLimitBudget(RecRef: RecordRef; var Handled: boolean)
    var
        CompCreditLimitBudget: Record "Company Cr. Limit Budget";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Budget":
                begin
                    RecRef.SetTable(CompCreditLimitBudget);
                    CompCreditLimitBudget.Status := CompCreditLimitBudget.Status::open;
                    CompCreditLimitBudget.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentCompCreditLimitBudget(RecRef: RecordRef; var Handled: boolean)
    var
        CompCreditLimitBudget: Record "Company Cr. Limit Budget";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Budget":
                begin
                    RecRef.SetTable(CompCreditLimitBudget);
                    CompCreditLimitBudget.Status := CompCreditLimitBudget.Status::Released;
                    CompCreditLimitBudget.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalCompCreditLimitBudget(RecRef: RecordRef; var IsHandled: boolean)
    var
        CompCreditLimitBudget: Record "Company Cr. Limit Budget";
    begin
        case RecRef.Number() of
            Database::"Company Cr. Limit Budget":
                begin
                    RecRef.SetTable(CompCreditLimitBudget);
                    CompCreditLimitBudget.Status := CompCreditLimitBudget.Status::"Pending Approval";
                    CompCreditLimitBudget.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCompCustCreLimitBudget(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryCompCreditLimitBudget()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CompCreditLimitBudgetCategoryTxt, 1, 20), CopyStr(CompCreditLimitBudgetCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsCompCreditLimitBudget()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Company Cr. Limit Budget", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateCompCreditLimitBudget()
    begin
        InsertCompCreditLimitBudgetApprovalworkflowtemplate();
    end;

    local procedure InsertCompCreditLimitBudgetApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CompCreditLimitBudgetDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CompCreditLimitBudgetApprWorkflowDescTxt, 1, 100), CopyStr(CompCreditLimitBudgetCategoryTxt, 1, 20));
        InsertCompCreditLimitBudgetApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCompCreditLimitBudgetApprovalworkflowDetails(var workflow: record Workflow);
    var
        CustCrLimit: Record "Company Cr. Limit Budget";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCompCreditLimitBudgettypecondition(CustCrLimit.Status::open), RunworkflowOnSendCompCreditLimitBudgetforApprovalCode(), BuildCompCreditLimitBudgettypecondition(CustCrLimit.Status::"Pending Approval"), RunworkflowOnCancelCompCreditLimitBudgetforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCompCreditLimitBudgettypecondition(status: integer): Text
    var
        CustCrLimt: Record "Company Cr. Limit Budget";
    Begin
        CustCrLimt.SetRange(status, status);
        exit(StrSubstNo(CompCreditLimitBudgetTypeCondnTxt, workflowsetup.Encode(CustCrLimt.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure OnaftergetpageidCompCreditLimitBudget(RecordRef: RecordRef; var PageID: Integer)
    begin
        if RecordRef.Number() = database::"Company Cr. Limit Budget" then
            PageID := GetConditionalcardPageidCompCreditLimitBudget(RecordRef)
    end;

    local procedure GetConditionalcardPageidCompCreditLimitBudget(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Company Cr. Limit Budget":
                exit(page::"Company Cr. Limt Budget");
        end;
    end;
    //Company Credit Limit Budget END
    //Prod Order Sheet Start
    [IntegrationEvent(false, false)]
    Procedure OnSendPOSForApproval(var POS: Record "Production Order Sheet Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPOSForApproval(var POS: Record "Production Order Sheet Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendPOSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPOSforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendPOSForApproval', '', false, false)]
    local procedure RunworkflowonsendPOSForApproval(var POS: Record "Production Order Sheet Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPOSforApprovalCode(), POS);
    end;

    procedure RunworkflowOnCancelPOSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPOSForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelPOSForApproval', '', false, false)]

    local procedure RunworkflowonCancelPOSForApproval(var POS: Record "Production Order Sheet Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPOSforApprovalCode(), POS);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPOS();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPOSforApprovalCode(), DATABASE::"Production Order Sheet Header",
          CopyStr(POSsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPOSforApprovalCode(), DATABASE::"Production Order Sheet Header",
          CopyStr(POSrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPOS(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPOSforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPOSforApprovalCode(), RunworkflowOnSendPOSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPOSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPOSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPOSforApprovalCode());
        end;
    end;

    procedure ISPOSWorkflowenabled(var POS: Record "Production Order Sheet Header"): Boolean
    begin
        if POS."Approval Status" <> POS."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(POS, RunworkflowOnSendPOSforApprovalCode()));
    end;

    Procedure CheckPOSApprovalsWorkflowEnabled(var POS: Record "Production Order Sheet Header"): Boolean
    begin
        IF not ISPOSworkflowenabled(POS) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPOS(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        POS: Record "Production Order Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Order Sheet Header":
                begin
                    RecRef.SetTable(POS);
                    ApprovalEntryArgument."Document No." := FORMAT(POS."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPOS(RecRef: RecordRef; var Handled: boolean)
    var
        POS: Record "Production Order Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Order Sheet Header":
                begin
                    RecRef.SetTable(POS);
                    POS."Approval Status" := POS."Approval Status"::open;
                    POS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPOS(RecRef: RecordRef; var Handled: boolean)
    var
        POS: Record "Production Order Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Order Sheet Header":
                begin
                    RecRef.SetTable(POS);
                    POS."Approval Status" := POS."Approval Status"::Released;
                    POS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalPOS(RecRef: RecordRef; var IsHandled: boolean)
    var
        POS: Record "Production Order Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Order Sheet Header":
                begin
                    RecRef.SetTable(POS);
                    POS."Approval Status" := POS."Approval Status"::"Pending for Approval";
                    POS.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPOS(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPOSforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPOSforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPOSforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPOSforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPOS()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(POSCategoryTxt, 1, 20), CopyStr(POSCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPOS()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Production Order Sheet Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePOS()
    begin
        InsertPOSApprovalworkflowtemplate();
    end;


    local procedure InsertPOSApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(POSDocOCRWorkflowCodeTxt, 1, 17), CopyStr(POSApprWorkflowDescTxt, 1, 100), CopyStr(POSCategoryTxt, 1, 20));
        InsertPOSApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPOSApprovalworkflowDetails(var workflow: record Workflow);
    var
        POS: Record "Production Order Sheet Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPOStypecondition(POS."Approval Status"::open), RunworkflowOnSendCPXforApprovalCode(), BuildPOStypecondition(POS."Approval Status"::"Pending for Approval"), RunworkflowOnCancelPOSforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPOStypecondition(status: integer): Text
    var
        POS: Record "Production Order Sheet Header";
    Begin
        POS.SetRange("Approval Status", Status);
        exit(StrSubstNo(POSTypeCondnTxt, workflowsetup.Encode(POS.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPOS(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidPOS(RecordRef)
    end;

    local procedure GetConditionalcardPageidPOS(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Production Order Sheet Header":
                exit(page::"Production Order Sheet");
        end;
    end;
    //Prod Order Sheet End<<
    //PMS Management Start>>

    [IntegrationEvent(false, false)]
    Procedure OnSendPMSForApproval(var PMS: Record PMSManagement)
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPMSForApproval(var PMS: Record PMSManagement)
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendPMSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPMSforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendPMSForApproval', '', false, false)]
    local procedure RunworkflowonsendPMSForApproval(var PMS: Record PMSManagement)
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPMSforApprovalCode(), PMS);
    end;

    procedure RunworkflowOnCancelPMSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPMSForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelPMSForApproval', '', false, false)]

    local procedure RunworkflowonCancelPMSForApproval(var PMS: Record PMSManagement)
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPMSforApprovalCode(), PMS);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPMS();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPMSforApprovalCode(), DATABASE::PMSManagement,
          CopyStr(PMSsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPMSforApprovalCode(), DATABASE::PMSManagement,
          CopyStr(PMSrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPMS(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPMSforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPMSforApprovalCode(), RunworkflowOnSendPMSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPMSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPMSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPMSforApprovalCode());
        end;
    end;

    procedure ISPMSWorkflowenabled(var PMS: Record PMSManagement): Boolean
    begin
        if PMS."Approval Status" <> PMS."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(PMS, RunworkflowOnSendPMSforApprovalCode()));
    end;

    Procedure CheckPMSApprovalsWorkflowEnabled(var PMS: Record PMSManagement): Boolean
    begin
        IF not ISPMSworkflowenabled(PMS) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPMS(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PMS: Record PMSManagement;
    begin
        case RecRef.Number() of
            Database::PMSManagement:
                begin
                    RecRef.SetTable(PMS);
                    ApprovalEntryArgument."Document No." := FORMAT(PMS."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPMS(RecRef: RecordRef; var Handled: boolean)
    var
        PMS: Record PMSManagement;
    begin
        case RecRef.Number() of
            Database::PMSManagement:
                begin
                    RecRef.SetTable(PMS);
                    PMS."Approval Status" := PMS."Approval Status"::open;
                    PMS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPMS(RecRef: RecordRef; var Handled: boolean)
    var
        PMS: Record PMSManagement;
    begin
        case RecRef.Number() of
            Database::PMSManagement:
                begin
                    RecRef.SetTable(PMS);
                    PMS."Approval Status" := PMS."Approval Status"::Released;
                    PMS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalPMS(RecRef: RecordRef; var IsHandled: boolean)
    var
        PMS: Record PMSManagement;
    begin
        case RecRef.Number() of
            Database::PMSManagement:
                begin
                    RecRef.SetTable(PMS);
                    PMS."Approval Status" := PMS."Approval Status"::"Pending for Approval";
                    PMS.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPMS(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPMSforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPMSforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPMSforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPMSforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPMS()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PMSCategoryTxt, 1, 20), CopyStr(PMSCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPMS()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::PMSManagement, 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePMS()
    begin
        InsertPMSApprovalworkflowtemplate();
    end;


    local procedure InsertPMSApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(PMSDocOCRWorkflowCodeTxt, 1, 17), CopyStr(PMSApprWorkflowDescTxt, 1, 100), CopyStr(PMSCategoryTxt, 1, 20));
        InsertPMSApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPMSApprovalworkflowDetails(var workflow: record Workflow);
    var
        PMS: Record PMSManagement;
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPMStypecondition(PMS."Approval Status"::open), RunworkflowOnSendCPXforApprovalCode(), BuildPMStypecondition(PMS."Approval Status"::"Pending for Approval"), RunworkflowOnCancelPMSforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPMStypecondition(status: integer): Text
    var
        PMS: Record PMSManagement;
    Begin
        PMS.SetRange("Approval Status", Status);
        exit(StrSubstNo(PMSTypeCondnTxt, workflowsetup.Encode(PMS.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPMS(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidPMS(RecordRef)
    end;

    local procedure GetConditionalcardPageidPMS(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"PMSManagement":
                exit(page::"PMS Management Car");
        end;
    end;
    //Approval for PMS Management  End <<

    ///code for Passing Fa Depreciation date to FA

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Batch", 'OnAfterPostGenJnlLine', '', false, false)]
    local Procedure OnAfterPostGenJnlLine1(var GenJournalLine: Record "Gen. Journal Line"; CommitIsSuppressed: Boolean; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        FADpeB: Record "FA Depreciation Book";
        FAPostingGr: Record "FA Posting Group";
    BEGIN
        FADpeB.RESET;
        FADpeB.SetRange("FA No.", GenJournalLine."Account No.");
        IF FADpeB.FindFirst() then BEGIN
            IF (FADpeB."Depreciation Starting Date" = 0D) THEN
                FADpeB."Depreciation Starting Date" := GenJournalLine."Posting Date";
            IF FAPostingGr.GET(FADpeB."FA Posting Group") then begin
                IF (FAPostingGr."No. of Depreciation Years" <> 0) AND (FADpeB."No. of Depreciation Years" = 0) then
                    FADpeB.Validate("No. of Depreciation Years", FAPostingGr."No. of Depreciation Years");
            end;
            FADpeB.Modify();
        END;
    END;


    /// 
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterPurchRcptLineInsert', '', false, false)]
    local procedure MakeGlForGrn(PurchaseLine: Record "Purchase Line"; var PurchRcptLine: Record "Purch. Rcpt. Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean; PurchInvHeader: Record "Purch. Inv. Header"; var TempTrackingSpecification: Record "Tracking Specification" temporary)
    var
        GenJnlLine: Record "Gen. Journal Line";
        PurchHeader: Record "Purchase Header";
        FAPostingGr: Record "FA Posting Group";
        InvPostingBuffer: Array[2] of Record "Invoice Post. Buffer" Temporary;
        PurchHdr: Record "Purchase Header";
        FADpeB: Record "FA Depreciation Book";
    begin
        WITH PurchaseLine DO BEGIN
            if PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset" THEN BEGIN
                FADpeB.RESET;
                FADpeB.SetRange("FA No.", PurchaseLine."No.");
                IF FADpeB.FINDSET THEN
                    repeat
                        IF FADpeB."Depreciation Starting Date" = 0D then
                            FADpeB."Depreciation Starting Date" := PurchRcptLine."Posting Date";
                        IF FAPostingGr.GET(FADpeB."FA Posting Group") then begin
                            IF (FAPostingGr."No. of Depreciation Years" <> 0) AND (FADpeB."No. of Depreciation Years" = 0) then
                                FADpeB.Validate("No. of Depreciation Years", FAPostingGr."No. of Depreciation Years");
                        end;
                        FADpeB.Modify();
                        Message('Depreciation Start date update for FA %1 and date %2', FADpeB."FA No.", FADpeB."Depreciation Starting Date")
                    until FADpeB.NEXT = 0;
                /*
                                PurchHdr.RESET;
                                PurchHdr.SetRange("Document Type", "Document Type");
                                PurchHdr.SetRange("No.", "Document No.");
                                IF PurchHdr.findfirst then;
                                GenJnlLine.INIT;
                                //PurchaseLine.CALCFIELDS("Posting Date");//03/10/2019  NYO Bugfix//error
                                GenJnlLine."Posting Date" := PurchHdr."Posting Date";
                                GenJnlLine."Document Date" := PurchHdr."Posting Date";
                                GenJnlLine.Description := Description;
                                GenJnlLine."Account Type" := GenJnlLine."Account Type"::"Fixed Asset";
                                //GenJnlLine."Reason Code" := "Reason Code";
                                //GenJnlLine."Document Type" := '';
                                GenJnlLine."Document No." := PurchRcptLine."Document No.";
                                //GenJnlLine."Purchase Contra Flag" := TRUE;  //RKD;
                                GenJnlLine."PDS No." := "No.";  //RKD
                                GenJnlLine."External Document No." := 'FAGRN';
                                GenJnlLine."System-Created Entry" := TRUE;
                                GenJnlLine.Quantity := "Qty. to Receive";
                                GenJnlLine."VAT %" := "VAT %";
                                GenJnlLine.VALIDATE(Amount, ("Direct Unit Cost" * "Qty. to Receive"));
                                ;
                                GenJnlLine."Source Currency Code" := "Currency Code";
                                GenJnlLine."Source Currency Amount" := Amount;
                                GenJnlLine."Shortcut Dimension 1 Code" := "Shortcut Dimension 1 Code";
                                GenJnlLine."Shortcut Dimension 2 Code" := "Shortcut Dimension 2 Code";
                                //CHI >>
                                // GenJnlLine."Shortcut Dimension 3 Code" := "Shortcut Dimension 3 Code";
                                // GenJnlLine."Shortcut Dimension 4 Code" := "Shortcut Dimension 4 Code";
                                //GenJnlLine."Shortcut Dimension 5 Code" := "Shortcut Dimension 5 Code";
                                //GenJnlLine."Shortcut Dimension 6 Code" := "Shortcut Dimension 6 Code";
                                //GenJnlLine."Shortcut Dimension 7 Code" := "Shortcut Dimension 7 Code";
                                //GenJnlLine."Shortcut Dimension 8 Code" := "Shortcut Dimension 8 Code";
                                //CHI <<
                                //HO1.0<<
                                GenJnlLine."Description 2" := "Description 2";
                                GenJnlLine."Capex No." := "Capex No.";
                                GenJnlLine."Capex Line No." := "Capex Line No.";
                                //HO1.0>>
                                //GenJnlLine."Source Code" := SrcCode;//Balu
                                GenJnlLine."Source Code" := 'PURCHASES';//PK-GRN
                                GenJnlLine."FA No." := "No.";
                                GenJnlLine."Sell-to/Buy-from No." := "Buy-from Vendor No.";
                                GenJnlLine."Bill-to/Pay-to No." := "Pay-to Vendor No.";
                                GenJnlLine."Posting No. Series" := PurchHeader."Posting No. Series";
                                GenJnlLine."IC Partner Code" := PurchHeader."Pay-to IC Partner Code";
                                GenJnlLine."Ship-to/Order Address Code" := PurchHeader."Order Address Code";
                                //to post import file no. to GL
                                //GenJnlLine."Import File No." := "Import File No.";//Balu
                                //SAA3.0 
                                //IF InvPostingBuffer[1].Type = Type::"Fixed Asset" THEN BEGIN
                                GenJnlLine."Account Type" := GenJnlLine."Account Type"::"Fixed Asset";
                                GenJnlLine."Account No." := "No.";
                                GenJnlLine."FA Posting Group" := "FA Posting Group";
                                IF FAPostingGr.GET("FA Posting Group") THEN BEGIN
                                    FAPostingGr.TESTFIELD("GRN Adjustment Acct");
                                    GenJnlLine."Bal. Account Type" := GenJnlLine."Bal. Account Type"::"G/L Account";
                                    GenJnlLine."Bal. Account No." := FAPostingGr."GRN Adjustment Acct";
                                END;
                                GenJnlLine."FA Posting Date" := "FA Posting Date";
                                GenJnlLine."Depreciation Book Code" := "Depreciation Book Code";
                                GenJnlLine."Salvage Value" := "Salvage Value";
                                GenJnlLine."Depr. until FA Posting Date" := "Depr. until FA Posting Date";
                                GenJnlLine."Depr. Acquisition Cost" := "Depr. Acquisition Cost";
                                GenJnlLine."Maintenance Code" := "Maintenance Code";
                                GenJnlLine."Insurance No." := "Insurance No.";
                                GenJnlLine."Budgeted FA No." := "Budgeted FA No.";
                                GenJnlLine."Duplicate in Depreciation Book" := "Duplicate in Depreciation Book";
                                GenJnlLine."Use Duplication List" := "Use Duplication List";
                                //GenJnlLine."FA Posting Type" := GenJnlLine."FA Posting Type"::"Acquisition Cost";//pk-GRN
                                //GenJnlLine.Validate("Dimension Set ID", InvPostingBuffer[1]."Dimension Set ID");pk
                                GenJnlLine.Validate("Dimension Set ID", "Dimension Set ID");//pk
                                                                                            //RunGenJnlPostLine(GenJnlLine, InvPostingBuffer[1]."Dimension Entry No.");
                                GenJnlLine."FA Posting Type" := GenJnlLine."FA Posting Type"::GRN;//PK-GRN
                                RunGenJnlPostLine(GenJnlLine, InvPostingBuffer[1]."Dimension Set ID");//pk-GRN
                                //END;*/
            end;
        END;
    end;


    Local Procedure RunGenJnlPostLine(VAR GenJnlLine: Record "Gen. Journal Line"; DimEntryNo: Integer)
    var
        TempDimBuf: record "Dimension Buffer" temporary;
        GenJnlPostLineLVar: Codeunit "Gen. Jnl.-Post Line";

    begin
        /*
        TempDimBuf.DELETEALL;
        TempJnlLineDim.DELETEALL;
        DimBufMgt.GetDimensions(DimEntryNo, TempDimBuf);
        DimMgt.CopyDimBufToJnlLineDim(
          TempDimBuf, TempJnlLineDim, GenJnlLine."Journal Template Name",
          GenJnlLine."Journal Batch Name", GenJnlLine."Line No.");*/
        //Message('GenJnl Line after assign %1', GenJnlLine."FA Posting Type");
        GenJnlPostLineLVar.RunWithCheck(GenJnlLine);
    end;
    /*
        [EventSubscriber(ObjectType::Codeunit, Codeunit::"FA jnl.-Check Line", 'OnAfterSetGLIntegration', '', false, false)]
        procedure UpdateDepGRN(FAPostingType: Enum "FA Journal Line FA Posting Type"; var GLIntegration: Boolean; var GnlJnlPosting: Boolean)
        var
            DeprBook: Record "Depreciation Book";
        begin
            //::GRN
            case FAPostingType of
                FAPostingType::GRN:
                    GLIntegration := true;
            end;
            GLIntegration := true;
            Message('%1..%2', FAPostingType, GLIntegration);
        end;*/

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", 'OnAfterPostedWhseShptHeaderInsert', '', false, false)]
    local procedure OnAfterPostedWhseShptHeaderInsert1(PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; LastShptNo: Code[20])
    var
        WareHseHdrLRec: Record "Warehouse Shipment Header";
        WareHseShipLRec: Record "Warehouse Shipment Line";
        Usersetup: Record "User Setup";
        LocRec: Record Location;
    begin
        /* SMTPSetup.GET;
        //SMTPSetup.TESTFIELD("Trans. Rcpt. Report Path");
        Usersetup.GET(USERID);
        IF LocRec.GET(PostedWhseShipmentHeader."Location Code") THEN;
        LocRec.testfield("E-Mail");
        SmtpMail.CreateMessage(USERID, SMTPSetup."User ID", LocRec."E-Mail", 'Warehouse Shipment Notification', '', TRUE);
        SmtpMail.AppendBody('Dear All');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Find below details of the WareHouse Shipment Items:');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');

        WareHseHdrLRec.Reset();
        WareHseHdrLRec.SetRange("No.", PostedWhseShipmentHeader."Whse. Shipment No.");
        if WareHseHdrLRec.FindFirst() then begin
            //Mail Table Header and header values
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Transfer from: ' + FORMAT(WareHseHdrLRec."Location Code"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Posted Whr.Shpmnt No: ' + FORMAT(PostedWhseShipmentHeader."No."));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Document No.: ' + FORMAT(WareHseHdrLRec."No."));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Document Date: ' + FORMAT(CURRENTDATETIME));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<table border="1", width="100%">');
            SmtpMail.AppendBody('<tr>');
            //SmtpMail.AppendBody('<th>Quantity Dispatched</th>');

            WareHseShipLRec.Reset();
            WareHseShipLRec.SetRange("No.", WareHseHdrLRec."No.");
            if WareHseShipLRec.FindSet() then begin
                //LineTable Header
                SmtpMail.AppendBody('<th>Item No.</th>');
                SmtpMail.AppendBody('<th>Item Description</th>');
                SmtpMail.AppendBody('<th>Total Quantity</th>');
                SmtpMail.AppendBody('<th>Quantity Shipped</th>');
                SmtpMail.AppendBody('</tr>');
                repeat
                    //LineTable Values
                    SmtpMail.AppendBody('<tr>');
                    SmtpMail.AppendBody('<td>' + FORMAT(WareHseShipLRec."Item No.") + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(WareHseShipLRec.Description) + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(WareHseShipLRec.Quantity) + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(WareHseShipLRec."Qty. to Ship") + '</td>');
                    SmtpMail.AppendBody('</tr>');
                until WareHseShipLRec.Next() = 0;
                SmtpMail.AppendBody('</table>');
                SmtpMail.AppendBody('</body>');
                SmtpMail.AppendBody('</html>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Confirmation of the warehouse Shipment. ');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Regards');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody(FORMAT(Usersetup.Name));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
                SmtpMail.Send;
                Message('Shipment Mail Sent');
            end;
        end */// commented to aviod locking issue 06/10/23
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", 'OnAfterPostedWhseRcptHeaderInsert', '', false, false)]
    local procedure OnAfterPostedWhseRcptHeaderInsert1(var PostedWhseReceiptHeader: Record "Posted Whse. Receipt Header"; WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        WareRcptHdrLRec: Record "Warehouse Receipt Header";
        WareHseRcptLRec: Record "Warehouse Receipt Line";
        Usersetup: Record "User Setup";
        LocRec: Record Location;
    begin
        /*  SMTPSetup.GET;
         //SMTPSetup.TESTFIELD("Trans. Rcpt. Report Path");
         Usersetup.GET(USERID);
         IF LocRec.GET(PostedWhseReceiptHeader."Location Code") THEN;
         LocRec.testfield("E-Mail");
         SmtpMail.CreateMessage(USERID, SMTPSetup."User ID", LocRec."E-Mail", 'Warehouse Receipt Notification', '', TRUE);
         SmtpMail.AppendBody('Dear All');
         SmtpMail.AppendBody('<br>');
         SmtpMail.AppendBody('<br>');
         SmtpMail.AppendBody('Find below details of the WareHouse Receipt Items:');
         SmtpMail.AppendBody('<br>');
         SmtpMail.AppendBody('<br>');
         WareRcptHdrLRec.Reset();
         WareRcptHdrLRec.SetRange("No.", PostedWhseReceiptHeader."Whse. Receipt No.");
         if WareRcptHdrLRec.FindFirst() then begin
             //Mail Table Header and header values
             SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('Received from: ' + FORMAT(WareRcptHdrLRec."Location Code"));
             SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('Posted Whr.Rcpt No: ' + FORMAT(PostedWhseReceiptHeader."No."));
             SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('Document No.: ' + FORMAT(WareRcptHdrLRec."No."));
             SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('Document Date: ' + FORMAT(CURRENTDATETIME));
             SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('<table border="1", width="100%">');
             SmtpMail.AppendBody('<tr>');
             //SmtpMail.AppendBody('<th>Quantity Dispatched</th>');
             WareHseRcptLRec.Reset();
             WareHseRcptLRec.SetRange("No.", WareRcptHdrLRec."No.");
             if WareHseRcptLRec.FindSet() then begin
                 //LineTable Header
                 SmtpMail.AppendBody('<th>Item No.</th>');
                 SmtpMail.AppendBody('<th>Item Description</th>');
                 SmtpMail.AppendBody('<th>Total Quantity</th>');
                 SmtpMail.AppendBody('<th>Quantity Received</th>');
                 SmtpMail.AppendBody('</tr>');
                 repeat
                     //LineTable Values
                     SmtpMail.AppendBody('<tr>');
                     SmtpMail.AppendBody('<td>' + FORMAT(WareHseRcptLRec."Item No.") + '</td>');
                     SmtpMail.AppendBody('<td>' + FORMAT(WareHseRcptLRec.Description) + '</td>');
                     SmtpMail.AppendBody('<td>' + FORMAT(WareHseRcptLRec.Quantity) + '</td>');
                     SmtpMail.AppendBody('<td>' + FORMAT(WareHseRcptLRec."Qty. to Receive") + '</td>');
                     SmtpMail.AppendBody('</tr>');
                 until WareHseRcptLRec.Next() = 0;
                 SmtpMail.AppendBody('</table>');
                 SmtpMail.AppendBody('</body>');
                 SmtpMail.AppendBody('</html>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('Confirmation of the warehouse receipt.');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('Regards');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody(FORMAT(Usersetup.Name));
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('<br>');
                 SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
                 SmtpMail.Send;
                 Message('Receipt Mail Sent');
             end;
         end */ // commented to aviod locking issue 06/10/23


    end;
    //Add CWIP Approval Start >>


    [IntegrationEvent(false, false)]
    Procedure OnSendAddCWIPForApproval(var CWIP: Record "CWIP Masters")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelAddCWIPForApproval(var CWIP: Record "CWIP Masters")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendAddCWIPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendAddCWIPforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendAddCWIPForApproval', '', true, true)]
    local procedure RunworkflowonsendAddCWIPForApproval(var CWIP: Record "CWIP Masters")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendAddCWIPforApprovalCode(), CWIP);
    end;

    procedure RunworkflowOnCancelAddCWIPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelAddCWIPForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelAddCWIPForApproval', '', true, true)]

    local procedure RunworkflowonCancelAddCWIPForApproval(var CWIP: Record "CWIP Masters")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelAddCWIPforApprovalCode(), CWIP);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCWIPLimit();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendAddCWIPforApprovalCode(), DATABASE::"CWIP Masters",
          CopyStr(AddCWIPsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelAddCWIPforApprovalCode(), DATABASE::"CWIP Masters",
          CopyStr(AddCWIPrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibraryAddCWIP(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelAddCWIPforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelAddCreditLimitforApprovalCode(), RunworkflowOnSendAddCWIPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendAddCWIPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendAddCWIPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendAddCWIPforApprovalCode());
        end;
    end;

    procedure ISAddCWIPworkflowenabled(var CWIP: Record "CWIP Masters"): Boolean
    begin
        if CWIP.Status <> CWIP.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CWIP, RunworkflowOnSendAddCWIPforApprovalCode()));
    end;

    Procedure CheckAddCWIPApprovalsWorkflowEnabled(var CWIP: Record "CWIP Masters"): Boolean
    begin
        IF not ISAddCWIPworkflowenabled(CWIP) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgumentAddCWIP(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CWIP: Record "CWIP Masters";
    begin
        case RecRef.Number() of
            Database::"CWIP Masters":
                begin
                    RecRef.SetTable(CWIP);
                    ApprovalEntryArgument."Document No." := CWIP."CWIP No.";
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure OnopendocumentAddCWIP(RecRef: RecordRef; var Handled: boolean)
    var
        CWIP: Record "CWIP Masters";
    begin
        case RecRef.Number() of
            Database::"CWIP Masters":
                begin
                    RecRef.SetTable(CWIP);
                    CWIP.Status := CWIP.Status::open;
                    CWIP.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocumentAddCreditCWIP(RecRef: RecordRef; var Handled: boolean)
    var
        CWIP: Record "CWIP Masters";
    begin
        case RecRef.Number() of
            Database::"CWIP Masters":
                begin
                    RecRef.SetTable(CWIP);
                    CWIP.Status := CWIP.Status::Release;
                    CWIP.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApprovalAddCWIP(RecRef: RecordRef; var IsHandled: boolean)
    var
        CWIP: Record "CWIP Masters";
    begin
        case RecRef.Number() of
            Database::"CWIP Masters":
                begin
                    RecRef.SetTable(CWIP);
                    CWIP.Status := CWIP.Status::PendingForApproval;
                    CWIP.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure OnaddworkflowresponseprodecessorstolibraryAddCWIP(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendAddCWIPforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendAddCWIPforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelAddCWIPforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelAddCWIPforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibraryAddCWIP()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(AddCWIPCategoryTxt, 1, 20), CopyStr(AddCWIPCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelationsAddCWIP()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"CWIP Masters", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplateAddCWIP()
    begin
        InsertAddCWIPApprovalworkflowtemplate();
    end;

    local procedure InsertAddCWIPApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(AddCWIPDocOCRWorkflowCodeTxt, 1, 17), CopyStr(AddCWIPApprWorkflowDescTxt, 1, 100), CopyStr(AddCWIPCategoryTxt, 1, 20));
        InsertAddCWIPApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertAddCWIPApprovalworkflowDetails(var workflow: record Workflow);
    var
        CWIP: Record "CWIP Masters";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildAddCWIPtypecondition(CWIP.Status::open), RunworkflowOnSendAddCWIPforApprovalCode(), BuildAddCreditLimittypecondition(CWIP.Status::PendingForApproval), RunworkflowOnCancelAddCreditLimitforApprovalCode(), workflowstepargument, true);
    end;

    local procedure BuildAddCWIPtypecondition(status: integer): Text
    var
        CWIP: Record "CWIP Masters";
    Begin
        CWIP.SetRange(status, status);
        exit(StrSubstNo(AddCWIPTypeCondnTxt, workflowsetup.Encode(CWIP.GetView(false))));
    End;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure OnaftergetpageidCWIP(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidCWIP(RecordRef)
    end;

    local procedure GetConditionalcardPageidCWIP(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"CWIP Masters":
                exit(page::"CWIP Card");
        end;
    end;

    procedure LoanCalculation(VAR GenJnlLine: Record "Gen. Journal Line")
    var
        LoanDetails: Record "Loan Details B2B";
        GenJourLineLrec: Record "Gen. Journal Line";
        GLAmt: decimal;
        LumSumAmt: Decimal;
        Lumsum: Boolean;
        LumSumMonth: integer;
        LumSumYear: integer;
        PaidMonth: integer;
        PaidYear: Integer;
    begin
        clear(GLAmt);
        clear(LumSumAmt);
        clear(LumSumMonth);
        clear(LumSumYear);
        clear(PaidMonth);
        clear(PaidYear);
        clear(Lumsum);

        //Error('%1..%2', GenJnlLine."Loan ID", GenJnlLine.Amount);
        GenJourLineLrec.reset;
        GenJourLineLrec.SetRange("Journal Template Name", 'JV');
        GenJourLineLrec.SetRange("Journal Batch Name", 'JOURNAL');
        GenJourLineLrec.setfilter(Amount, '<>%1', 0);
        GenJourLineLrec.SetRange("Loan ID", GenJnlLine."Loan ID");
        if GenJourLineLrec.FindFirst() then begin
            GLAmt := GenJourLineLrec.Amount;
            LoanDetails.RESET;
            LoanDetails.SetRange("Loan Id", GenJourLineLrec."Loan ID");
            LoanDetails.SETFilter("Pay Date", '<=%1', WorkDate);//PKON22M6-CR220060
            IF LoanDetails.findset then
                repeat
                    //message('%1..%2', GenJourLineLrec.Amount, GenJourLineLrec."Loan ID");
                    IF (LoanDetails."EMI Amount" <> LoanDetails."EMI Deducted") AND (GLAmt > 0) THEN BEGIN
                        IF (LoanDetails."EMI Amount" >= (GLAmt + LoanDetails."EMI Deducted")) THEN BEGIN
                            LoanDetails.VALIDATE("EMI Deducted", (LoanDetails."EMI Deducted" + (GLAmt)));
                            LoanDetails."Paid Month" := Date2DMY(GenJourLineLrec."Posting Date", 2);
                            LoanDetails."Paid Year" := Date2DMY(GenJourLineLrec."Posting Date", 3);
                            LoanDetails."Posted Document No." := GenJourLineLrec."Document No.";//PKON22M6-CR220060

                            IF (LoanDetails."EMI Amount" < GLAmt) AND (Lumsum = false) THEN BEGIN
                                LumSumAmt := GLAmt;
                                LoanDetails."Repayment Month" := Date2DMY(GenJourLineLrec."Posting Date", 2);
                                LoanDetails."Repayment Year" := Date2DMY(GenJourLineLrec."Posting Date", 3);
                                LoanDetails."Lump Sum Payment" := LumSumAmt;
                            end;
                            LoanDetails.Modify();
                            clear(GLAmt);
                            //message('%1..%2...1', GLAmt, LoanDetails."EMI Deducted");
                        END else BEGIN
                            IF (LoanDetails."EMI Amount" < GLAmt) AND (Lumsum = false) THEN BEGIN
                                LumSumAmt := GLAmt;
                                LoanDetails."Repayment Month" := Date2DMY(GenJourLineLrec."Posting Date", 2);
                                LoanDetails."Repayment Year" := Date2DMY(GenJourLineLrec."Posting Date", 3);
                                LoanDetails."Lump Sum Payment" := LumSumAmt;
                            end;

                            GLAmt := (GLAmt - (LoanDetails."EMI Amount" - LoanDetails."EMI Deducted"));
                            //message('%1...%2...%3..2', GLAmt, LoanDetails."EMI Deducted", LoanDetails."EMI Amount");
                            LoanDetails.VALIDATE("EMI Deducted", (LoanDetails."EMI Amount"));
                            LoanDetails."Paid Month" := Date2DMY(GenJourLineLrec."Posting Date", 2);
                            LoanDetails."Paid Year" := Date2DMY(GenJourLineLrec."Posting Date", 3);
                            LoanDetails."Posted Document No." := GenJourLineLrec."Document No.";//PKON22M6-CR220060

                            LoanDetails.Modify();
                        end;
                        Lumsum := TRUE;
                        //LoanDetails.Modify();
                    end;
                //message('%1', LoanDetails."EMI Deducted");
                until LoanDetails.next = 0;
        end;
    end;
    //Approvals For Posted Sales Shipment
    [IntegrationEvent(false, false)]
    Procedure OnSendPSSForApproval(var PSS: Record "Sales Shipment Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPSSForApproval(var PSS: Record "Sales Shipment Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendPSSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPSSforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendPSSForApproval', '', false, false)]
    local procedure RunworkflowonsendPSSForApproval(var PSS: Record "Sales Shipment Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPSSforApprovalCode(), PSS);
    end;

    procedure RunworkflowOnCancelPSSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPSSForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelPSSForApproval', '', false, false)]

    local procedure RunworkflowonCancelPSSForApproval(var PSS: Record "Sales Shipment Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPSSforApprovalCode(), PSS);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPSS();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPSSforApprovalCode(), DATABASE::"Sales Shipment Header",
          CopyStr(PSSsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPSSforApprovalCode(), DATABASE::"Sales Shipment Header",
          CopyStr(PSSrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPSS(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPSSforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPSSforApprovalCode(), RunworkflowOnSendPSSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPSSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPSSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPSSforApprovalCode());
        end;
    end;

    procedure ISPSSWorkflowenabled(var PSS: Record "Sales Shipment Header"): Boolean
    begin
        if PSS."Approval Status" <> PSS."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(PSS, RunworkflowOnSendPSSforApprovalCode()));
    end;

    Procedure CheckPSSApprovalsWorkflowEnabled(var PSS: Record "Sales Shipment Header"): Boolean
    begin
        IF not ISPSSworkflowenabled(PSS) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPSS(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PSS: Record "Sales Shipment Header";
    begin
        case RecRef.Number() of
            Database::"Sales Shipment Header":
                begin
                    RecRef.SetTable(PSS);
                    ApprovalEntryArgument."Document No." := FORMAT(PSS."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPSS(RecRef: RecordRef; var Handled: boolean)
    var
        PSS: Record "Sales Shipment Header";
    begin
        case RecRef.Number() of
            Database::"Sales Shipment Header":
                begin
                    RecRef.SetTable(PSS);
                    PSS."Approval Status" := PSS."Approval Status"::open;
                    PSS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPSS(RecRef: RecordRef; var Handled: boolean)
    var
        PSS: Record "Sales Shipment Header";
    begin
        case RecRef.Number() of
            Database::"Sales Shipment Header":
                begin
                    RecRef.SetTable(PSS);
                    PSS."Approval Status" := PSS."Approval Status"::Released;
                    PSS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalPSS(RecRef: RecordRef; var IsHandled: boolean)
    var
        PSS: Record "Sales Shipment Header";
    begin
        case RecRef.Number() of
            Database::"Sales Shipment Header":
                begin
                    RecRef.SetTable(PSS);
                    PSS."Approval Status" := PSS."Approval Status"::"Pending for Approval";
                    PSS.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPSS(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPSSforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPSSforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPSSforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPSSforApprovalCode());
        end;
    end;
    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPSS()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PSSCategoryTxt, 1, 20), CopyStr(PSSCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPSS()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Sales Shipment Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePSS()
    begin
        InsertPSSApprovalworkflowtemplate();
    end;


    local procedure InsertPSSApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(PSSDocOCRWorkflowCodeTxt, 1, 17), CopyStr(PSSApprWorkflowDescTxt, 1, 100), CopyStr(PSSCategoryTxt, 1, 20));
        InsertPSSApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPSSApprovalworkflowDetails(var workflow: record Workflow);
    var
        PSS: Record "Sales Shipment Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildLSPtypecondition(PSS."Approval Status"::open), RunworkflowOnSendCPXforApprovalCode(), BuildPSStypecondition(PSS."Approval Status"::"Pending for Approval"), RunworkflowOnCancelPSSforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPSStypecondition(status: integer): Text
    var
        PSS: Record "Sales Shipment Header";
    Begin
        PSS.SetRange("Approval Status", Status);
        exit(StrSubstNo(PSSTypeCondnTxt, workflowsetup.Encode(PSS.GetView(false))));
    End;
    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPSS(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidPSS(RecordRef)
    end;

    local procedure GetConditionalcardPageidPSS(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Sales Shipment Header":
                exit(page::"Posted Sales Shipment"
                );
        end;
    end;

    ///Approvals End For Posted Sales Shipment
    /// 
    /// Approvals Start For Posted Purchase Receipt
    [IntegrationEvent(false, false)]
    Procedure OnSendPRHForApproval(var PRH: Record "Purch. Rcpt. Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPRHForApproval(var PRH: Record "Purch. Rcpt. Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendPRHforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPRHforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendPRHForApproval', '', false, false)]
    local procedure RunworkflowonsendPRHForApproval(var PRH: Record "Purch. Rcpt. Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPRHforApprovalCode(), PRH);
    end;

    procedure RunworkflowOnCancelPRHforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPRHForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelPRHForApproval', '', false, false)]

    local procedure RunworkflowonCancelPRHForApproval(var PRH: Record "Purch. Rcpt. Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPRHforApprovalCode(), PRH);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPRH();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPRHforApprovalCode(), DATABASE::"Purch. Rcpt. Header",
          CopyStr(PRHsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPRHforApprovalCode(), DATABASE::"Purch. Rcpt. Header",
          CopyStr(PRHrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPRH(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPRHforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPRHforApprovalCode(), RunworkflowOnSendPRHforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPRHforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPRHforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPRHforApprovalCode());
        end;
    end;

    procedure ISPRHWorkflowenabled(var PRH: Record "Purch. Rcpt. Header"): Boolean
    begin
        if PRH."Approval Status" <> PRH."Approval Status"::open then
            exit(false);
        //exit(WorkflowManagement.CanExecuteWorkflow(PRH,  RunworkflowOnSendLSPforApprovalCode()));//PKONJ18.3
        exit(WorkflowManagement.CanExecuteWorkflow(PRH, RunworkflowOnSendPRHforApprovalCode()));//PKONJ18.3
    end;

    Procedure CheckPRHApprovalsWorkflowEnabled(var PRH: Record "Purch. Rcpt. Header"): Boolean
    begin
        IF not ISPRHworkflowenabled(PRH) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPRH(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PRH: Record "Purch. Rcpt. Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Rcpt. Header":
                begin
                    RecRef.SetTable(PRH);
                    ApprovalEntryArgument."Document No." := FORMAT(PRH."No.");
                end;
        end;
    end;
    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPRH(RecRef: RecordRef; var Handled: boolean)
    var
        PRH: Record "Purch. Rcpt. Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Rcpt. Header":
                begin
                    RecRef.SetTable(PRH);
                    PRH."Approval Status" := PRH."Approval Status"::open;
                    PRH.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPRH(RecRef: RecordRef; var Handled: boolean)
    var
        PRH: Record "Purch. Rcpt. Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Rcpt. Header":
                begin
                    RecRef.SetTable(PRH);
                    PRH."Approval Status" := PRH."Approval Status"::Released;
                    PRH.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalPRH(RecRef: RecordRef; var IsHandled: boolean)
    var
        PRH: Record "Purch. Rcpt. Header";
    begin
        case RecRef.Number() of
            Database::"Purch. Rcpt. Header":
                begin
                    RecRef.SetTable(PRH);
                    PRH."Approval Status" := PRH."Approval Status"::"Pending for Approval";
                    PRH.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPRH(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPRHforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPRHforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPRHforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPRHforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPRH()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PRHCategoryTxt, 1, 20), CopyStr(PRHCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPRH()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Purch. Rcpt. Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePRH()
    begin
        InsertPRHApprovalworkflowtemplate();
    end;


    local procedure InsertPRHApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(PRHDocOCRWorkflowCodeTxt, 1, 17), CopyStr(PRHApprWorkflowDescTxt, 1, 100), CopyStr(PRHCategoryTxt, 1, 20));
        InsertPRHApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPRHApprovalworkflowDetails(var workflow: record Workflow);
    var
        PRH: Record "Purch. Rcpt. Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPRHtypecondition(PRH."Approval Status"::open), RunworkflowOnSendCPXforApprovalCode(), BuildPRHtypecondition(PRH."Approval Status"::"Pending for Approval"), RunworkflowOnCancelPRHforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPRHtypecondition(status: integer): Text
    var
        PRH: Record "Purch. Rcpt. Header";
    Begin
        PRH.SetRange("Approval Status", Status);
        exit(StrSubstNo(PRHTypeCondnTxt, workflowsetup.Encode(PRH.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPRH(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidPRH(RecordRef)
    end;

    local procedure GetConditionalcardPageidPRH(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Purch. Rcpt. Header":
                exit(page::"Posted Purchase Receipt"
                );
        end;
    end;
    //Notifications Fix 25Feb2021>>
    [EventSubscriber(ObjectType::Report, report::"Notification Email", 'OnBeforeGetDocumentTypeAndNumber', '', false, false)]
    local procedure BeforeGetDocumentTypeAndNumber(var NotificationEntry: Record "Notification Entry"; var RecRef: RecordRef; var DocumentType: Text; var DocumentNo: Text; var IsHandled: Boolean)
    var
        FieldRef: FieldRef;
    begin
        case RecRef.Number of
            database::"Production BOM Header":
                begin
                    FieldRef := RecRef.Field(1);
                    DocumentNo := Format(FieldRef.Value);
                    IsHandled := true;
                end;
            database::"Production BOM Version":
                begin
                    FieldRef := RecRef.Field(1);
                    DocumentNo := Format(FieldRef.Value);
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Report, report::"Notification Email", 'OnSetReportFieldPlaceholders', '', false, false)]
    local procedure etReportFieldPlaceholders(RecRef: RecordRef; var Field1Label: Text; var Field1Value: Text; var Field2Label: Text; var Field2Value: Text; var Field3Label: Text; var Field3Value: Text)
    var
        FieldRef: FieldRef;
        ProductionBOM: Record "Production BOM Header";
        ProductionBOMVer: Record "Production BOM Version";
    begin
        case RecRef.Number of
            database::"Production BOM Header":
                begin
                    Field1Label := ProductionBOM.FieldCaption("No.");
                    FieldRef := RecRef.Field(ProductionBOM.FieldNo("No."));
                    Field1Value := Format(FieldRef.Value);
                    Field2Label := ProductionBOM.FieldCaption(Description);
                    FieldRef := RecRef.Field(ProductionBOM.FieldNo(Description));
                    Field2Value := Format(FieldRef.Value);
                    Field3Label := ProductionBOM.FieldCaption("Description 2");
                    FieldRef := RecRef.Field(ProductionBOM.FieldNo("Description 2"));
                    Field3Value := Format(FieldRef.Value);
                end;
            database::"Production BOM Version":
                begin
                    Field1Label := ProductionBOMVer.FieldCaption("Production BOM No.");
                    FieldRef := RecRef.Field(ProductionBOMVer.FieldNo("Production BOM No."));
                    Field1Value := Format(FieldRef.Value);
                    Field2Label := ProductionBOMVer.FieldCaption("Version Code");
                    FieldRef := RecRef.Field(ProductionBOMVer.FieldNo("Version Code"));
                    Field2Value := Format(FieldRef.Value);
                    Field3Label := ProductionBOMVer.FieldCaption(Description);
                    FieldRef := RecRef.Field(ProductionBOMVer.FieldNo(Description));
                    Field3Value := Format(FieldRef.Value);

                end;
        end;
    end;
    //Notifications Fix 25Feb2021<<
    //Approval for Production Work Sheet  Start >>
    [IntegrationEvent(false, false)]
    Procedure OnSendPWSForApproval(var PWS: Record "Production Work Sheet Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPWSForApproval(var PWS: Record "Production Work Sheet Header")
    begin
    end;
    //Create events for workflow
    procedure RunworkflowOnSendPWSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPWSforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendPWSForApproval', '', false, false)]
    local procedure RunworkflowonsendPWSForApproval(var PWS: Record "Production Work Sheet Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPWSforApprovalCode(), PWS);
    end;

    procedure RunworkflowOnCancelPWSforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPWSForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelPWSForApproval', '', false, false)]

    local procedure RunworkflowonCancelPWSForApproval(var PWS: Record "Production Work Sheet Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPWSforApprovalCode(), PWS);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryPWS();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPWSforApprovalCode(), DATABASE::"Production Work Sheet Header",
          CopyStr(PWSsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPWSforApprovalCode(), DATABASE::"Production Work Sheet Header",
          CopyStr(PWSrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryPWS(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPWSforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPWSforApprovalCode(), RunworkflowOnSendPWSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendPWSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendPWSforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendPWSforApprovalCode());
        end;
    end;

    procedure ISPWSWorkflowenabled(var PWS: Record "Production Work Sheet Header"): Boolean
    begin
        if PWS."Approval Status" <> PWS."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(PWS, RunworkflowOnSendPWSforApprovalCode()));
    end;

    Procedure CheckPWSApprovalsWorkflowEnabled(var PWS: Record "Production Work Sheet Header"): Boolean
    begin
        IF not ISPWSworkflowenabled(PWS) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentPWS(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PWS: Record "Production Work Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Work Sheet Header":
                begin
                    RecRef.SetTable(PWS);
                    ApprovalEntryArgument."Document No." := FORMAT(PWS."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentPWS(RecRef: RecordRef; var Handled: boolean)
    var
        PWS: Record "Production Work Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Work Sheet Header":
                begin
                    RecRef.SetTable(PWS);
                    PWS."Approval Status" := PWS."Approval Status"::open;
                    PWS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentPWS(RecRef: RecordRef; var Handled: boolean)
    var
        PWS: Record "Production Work Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Work Sheet Header":
                begin
                    RecRef.SetTable(PWS);
                    PWS."Approval Status" := PWS."Approval Status"::Released;
                    PWS.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalPWS(RecRef: RecordRef; var IsHandled: boolean)
    var
        PWS: Record "Production Work Sheet Header";
    begin
        case RecRef.Number() of
            Database::"Production Work Sheet Header":
                begin
                    RecRef.SetTable(PWS);
                    PWS."Approval Status" := PWS."Approval Status"::"Pending for Approval";
                    PWS.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryPWS(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPWSforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPWSforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPWSforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPWSforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryPWS()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PWSCategoryTxt, 1, 20), CopyStr(PWSCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsPWS()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Production Work Sheet Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplatePWS()
    begin
        InsertPWSApprovalworkflowtemplate();
    end;


    local procedure InsertPWSApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(PWSDocOCRWorkflowCodeTxt, 1, 17), CopyStr(PWSApprWorkflowDescTxt, 1, 100), CopyStr(PWSCategoryTxt, 1, 20));
        InsertPWSApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPWSApprovalworkflowDetails(var workflow: record Workflow);
    var
        PWS: Record "Production Work Sheet Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPWStypecondition(PWS."Approval Status"::open), RunworkflowOnSendPWSforApprovalCode(), BuildPWStypecondition(PWS."Approval Status"::"Pending for Approval"), RunworkflowOnCancelPWSforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildPWStypecondition(status: integer): Text
    var
        PWS: Record "Production Work Sheet Header";
    Begin
        PWS.SetRange("Approval Status", Status);
        exit(StrSubstNo(PWSTypeCondnTxt, workflowsetup.Encode(PWS.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidPWS(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidPWS(RecordRef)
    end;

    local procedure GetConditionalcardPageidPWS(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Production Work Sheet Header":
                exit(page::"Production Work Sheet"
                );
        end;
    end;
    //Approval for Production Work Sheet  End <<

    //Approvals For Import File Start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendIFCForApproval(var IFC: Record "Vendor 2")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelIFCForApproval(var IFC: Record "Vendor 2")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendIFCforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendIFCforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendIFCForApproval', '', false, false)]
    local procedure RunworkflowonsendIFCForApproval(var IFC: Record "Vendor 2")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendIFCforApprovalCode(), IFC);
    end;

    procedure RunworkflowOnCancelIFCforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelIFCForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelIFCForApproval', '', false, false)]

    local procedure RunworkflowonCancelIFCForApproval(var IFC: Record "Vendor 2")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelIFCforApprovalCode(), IFC);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryIFC();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendIFCforApprovalCode(), DATABASE::"Vendor 2",
          CopyStr(IFCsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelIFCforApprovalCode(), DATABASE::"Vendor 2",
          CopyStr(IFCrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryIFC(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelIFCforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelIFCforApprovalCode(), RunworkflowOnSendIFCforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendIFCforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendIFCforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendIFCforApprovalCode());
        end;
    end;

    procedure ISIFCWorkflowenabled(var IFC: Record "Vendor 2"): Boolean
    begin
        if IFC."Approval Status" <> IFC."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(IFC, RunworkflowOnSendIFCforApprovalCode()));
    end;

    Procedure CheckIFCApprovalsWorkflowEnabled(var IFC: Record "Vendor 2"): Boolean
    begin
        IF not ISIFCworkflowenabled(IFC) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentIFC(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        IFC: Record "Vendor 2";
    begin
        case RecRef.Number() of
            Database::"Vendor 2":
                begin
                    RecRef.SetTable(IFC);
                    ApprovalEntryArgument."Document No." := FORMAT(IFC."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentIFC(RecRef: RecordRef; var Handled: boolean)
    var
        IFC: Record "Vendor 2";
    begin
        case RecRef.Number() of
            Database::"Vendor 2":
                begin
                    RecRef.SetTable(IFC);
                    IFC.Status := IFC.Status::open;
                    IFC.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentIFC(RecRef: RecordRef; var Handled: boolean)
    var
        IFC: Record "Vendor 2";
    begin
        case RecRef.Number() of
            Database::"Vendor 2":
                begin
                    RecRef.SetTable(IFC);
                    IFC.Status := IFC.Status::Released;
                    IFC.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalIFC(RecRef: RecordRef; var IsHandled: boolean)
    var
        IFC: Record "Vendor 2";
    begin
        case RecRef.Number() of
            Database::"Vendor 2":
                begin
                    RecRef.SetTable(IFC);
                    IFC.Status := IFC.Status::"Pending Approval";
                    IFC.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryIFC(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendIFCforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendIFCforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelIFCforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelIFCforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryIFC()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(IFCCategoryTxt, 1, 20), CopyStr(IFCCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsIFC()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Vendor 2", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateIFC()
    begin
        InsertIFCApprovalworkflowtemplate();
    end;


    local procedure InsertIFCApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IFCDocOCRWorkflowCodeTxt, 1, 17), CopyStr(IFCApprWorkflowDescTxt, 1, 100), CopyStr(IFCCategoryTxt, 1, 20));
        InsertIFCApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertIFCApprovalworkflowDetails(var workflow: record Workflow);
    var
        IFC: Record "Vendor 2";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildIFCtypecondition(IFC."Approval Status"::open), RunworkflowOnSendIFCforApprovalCode(), BuildIFCtypecondition(IFC."Approval Status"::"Pending for Approval"), RunworkflowOnCancelIFCforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildIFCtypecondition(status: integer): Text
    var
        IFC: Record "Vendor 2";
    Begin
        IFC.SetRange("Approval Status", Status);
        exit(StrSubstNo(IFCTypeCondnTxt, workflowsetup.Encode(IFC.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidIFC(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidIFC(RecordRef)
    end;

    local procedure GetConditionalcardPageidIFC(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Vendor 2":
                exit(page::"Import File Card"
                );
        end;
    end;
    //Approvals For Import File End<<
    //Approvals For Clearing Start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendCLHForApproval(var CLH: Record "Clearing Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCLHForApproval(var CLH: Record "Clearing Header")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCLHforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCLHforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCLHForApproval', '', false, false)]
    local procedure RunworkflowonsendCLHForApproval(var CLH: Record "Clearing Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCLHforApprovalCode(), CLH);
    end;

    procedure RunworkflowOnCancelCLHforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCLHForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCLHForApproval', '', false, false)]

    local procedure RunworkflowonCancelCLHForApproval(var CLH: Record "Clearing Header")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCLHforApprovalCode(), CLH);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCLH();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCLHforApprovalCode(), DATABASE::"Clearing Header",
          CopyStr(CLHsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCLHforApprovalCode(), DATABASE::"Clearing Header",
          CopyStr(CLHrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryCLH(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCLHforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCLHforApprovalCode(), RunworkflowOnSendCLHforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCLHforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCLHforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCLHforApprovalCode());
        end;
    end;

    procedure ISCLHWorkflowenabled(var CLH: Record "Clearing Header"): Boolean
    begin
        if CLH."Approval Status" <> CLH."Approval Status"::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CLH, RunworkflowOnSendCLHforApprovalCode()));
    end;

    Procedure CheckCLHApprovalsWorkflowEnabled(var CLH: Record "Clearing Header"): Boolean
    begin
        IF not ISCLHworkflowenabled(CLH) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentCLH(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CLH: Record "Clearing Header";
    begin
        case RecRef.Number() of
            Database::"Clearing Header":
                begin
                    RecRef.SetTable(CLH);
                    ApprovalEntryArgument."Document No." := FORMAT(CLH."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentCLH(RecRef: RecordRef; var Handled: boolean)
    var
        CLH: Record "Clearing Header";
    begin
        case RecRef.Number() of
            Database::"Clearing Header":
                begin
                    RecRef.SetTable(CLH);
                    CLH."Approval Status" := CLH."Approval Status"::open;
                    CLH.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentCLH(RecRef: RecordRef; var Handled: boolean)
    var
        CLH: Record "Clearing Header";
    begin
        case RecRef.Number() of
            Database::"Clearing Header":
                begin
                    RecRef.SetTable(CLH);
                    CLH."Approval Status" := CLH."Approval Status"::Released;
                    CLH.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalCLH(RecRef: RecordRef; var IsHandled: boolean)
    var
        CLH: Record "Clearing Header";
    begin
        case RecRef.Number() of
            Database::"Clearing Header":
                begin
                    RecRef.SetTable(CLH);
                    CLH."Approval Status" := CLH."Approval Status"::"Pending for Approval";
                    CLH.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCLH(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCLHforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCLHforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCLHforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCLHforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryCLH()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CLHCategoryTxt, 1, 20), CopyStr(CLHCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsCLH()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Clearing Header", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateCLH()
    begin
        InsertCLHApprovalworkflowtemplate();
    end;


    local procedure InsertCLHApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CLHDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CLHApprWorkflowDescTxt, 1, 100), CopyStr(CLHCategoryTxt, 1, 20));
        InsertCLHApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCLHApprovalworkflowDetails(var workflow: record Workflow);
    var
        CLH: Record "Clearing Header";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCLHtypecondition(CLH."Approval Status"::open), RunworkflowOnSendCLHforApprovalCode(), BuildCLHtypecondition(CLH."Approval Status"::"Pending for Approval"), RunworkflowOnCancelCLHforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCLHtypecondition(status: integer): Text
    var
        CLH: Record "Clearing Header";
    Begin
        CLH.SetRange("Approval Status", Status);
        exit(StrSubstNo(CLHTypeCondnTxt, workflowsetup.Encode(CLH.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidCLH(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidCLH(RecordRef)
    end;

    local procedure GetConditionalcardPageidCLH(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Clearing Header":
                exit(page::Clearing
                );
        end;
    end;
    //Approvals For Clearing End<<
    //Approvals For Credit Limit Approvals Page>>
    [IntegrationEvent(false, false)]
    Procedure OnSendCLAPForApproval(var CLAP: Record "Customer Credit Approvals")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCLAPForApproval(var CLAP: Record "Customer Credit Approvals")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCLAPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCLAPforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCLAPForApproval', '', false, false)]
    local procedure RunworkflowonsendCLAPForApproval(var CLAP: Record "Customer Credit Approvals")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCLAPforApprovalCode(), CLAP);
    end;

    procedure RunworkflowOnCancelCLAPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCLAPForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCLAPForApproval', '', false, false)]

    local procedure RunworkflowonCancelCLAPForApproval(var CLAP: Record "Customer Credit Approvals")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCLAPforApprovalCode(), CLAP);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCLAP();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCLAPforApprovalCode(), DATABASE::"Customer Credit Approvals",
          CopyStr(CLAPsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCLAPforApprovalCode(), DATABASE::"Customer Credit Approvals",
          CopyStr(CLAPrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryCLAP(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCLAPforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCLAPforApprovalCode(), RunworkflowOnSendCLAPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCLAPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCLAPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCLAPforApprovalCode());
        end;
    end;

    procedure ISCLAPWorkflowenabled(var CLAP: Record "Customer Credit Approvals"): Boolean
    begin
        if CLAP.Status <> CLAP.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CLAP, RunworkflowOnSendCLAPforApprovalCode()));
    end;

    Procedure CheckCLAPApprovalsWorkflowEnabled(var CLAP: Record "Customer Credit Approvals"): Boolean
    begin
        IF not ISCLAPworkflowenabled(CLAP) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentCLAP(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CLAP: Record "Customer Credit Approvals";
    begin
        case RecRef.Number() of
            Database::"Customer Credit Approvals":
                begin
                    RecRef.SetTable(CLAP);
                    ApprovalEntryArgument."Document No." := FORMAT(CLAP."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentCLAP(RecRef: RecordRef; var Handled: boolean)
    var
        CLAP: Record "Customer Credit Approvals";
    begin
        case RecRef.Number() of
            Database::"Customer Credit Approvals":
                begin
                    RecRef.SetTable(CLAP);
                    CLAP.Status := CLAP.Status::open;
                    CLAP.Modify();
                    credilimitsc.RESET;
                    credilimitsc.SetFilter("Approv Doc No.", CLAP."No.");
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl(Status, credilimitsc.Status::Open);
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentCLAP(RecRef: RecordRef; var Handled: boolean)
    var
        CLAP: Record "Customer Credit Approvals";
    begin
        case RecRef.Number() of
            Database::"Customer Credit Approvals":
                begin
                    RecRef.SetTable(CLAP);
                    CLAP.Status := CLAP.Status::Released;
                    CLAP.Modify();
                    credilimitsc.RESET;
                    credilimitsc.SetFilter("Approv Doc No.", CLAP."No.");
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl(Status, credilimitsc.Status::Released);
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalCLAP(RecRef: RecordRef; var IsHandled: boolean)
    var
        CLAP: Record "Customer Credit Approvals";
    begin
        case RecRef.Number() of
            Database::"Customer Credit Approvals":
                begin
                    RecRef.SetTable(CLAP);
                    CLAP.Status := CLAP.Status::"Pending for Approval";
                    CLAP.Modify();
                    credilimitsc.RESET;
                    credilimitsc.SetFilter("Approv Doc No.", CLAP."No.");
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl(Status, credilimitsc.Status::"Pending Approval");
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCLAP(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCLAPforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCLAPforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCLAPforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCLAPforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryCLAP()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CLAPCategoryTxt, 1, 20), CopyStr(CLAPCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsCLAP()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Customer Credit Approvals", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateCLAP()
    begin
        InsertCLAPApprovalworkflowtemplate();
    end;


    local procedure InsertCLAPApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CLAPDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CLAPApprWorkflowDescTxt, 1, 100), CopyStr(CLAPCategoryTxt, 1, 20));
        InsertCLAPApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCLAPApprovalworkflowDetails(var workflow: record Workflow);
    var
        CLAP: Record "Customer Credit Approvals";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCLAPtypecondition(CLAP.Status::open), RunworkflowOnSendCLAPforApprovalCode(), BuildCLAPtypecondition(CLAP.Status::"Pending for Approval"), RunworkflowOnCancelCLAPforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCLAPtypecondition(status: integer): Text
    var
        CLAP: Record "Customer Credit Approvals";
    Begin
        CLAP.SetRange(status, Status);
        exit(StrSubstNo(CLAPTypeCondnTxt, workflowsetup.Encode(CLAP.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidCLAP(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidCLAP(RecordRef)
    end;

    local procedure GetConditionalcardPageidCLAP(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Customer Credit Approvals":
                exit(page::"Customer Credit Limit Approval"
                );
        end;
    end;
    //Approvals For Credit Limit Approvals Page End<<
    //Approvals For Customer Price Discount Schedule start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendCSPDForApproval(var CSPD: Record "Customer Sales Price/Discount")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelCSPDForApproval(var CSPD: Record "Customer Sales Price/Discount")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendCSPDforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendCSPDforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendCSPDForApproval', '', false, false)]
    local procedure RunworkflowonsendCSPDForApproval(var CSPD: Record "Customer Sales Price/Discount")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendCSPDforApprovalCode(), CSPD);
    end;

    procedure RunworkflowOnCancelCSPDforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelCSPDForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelCSPDForApproval', '', false, false)]

    local procedure RunworkflowonCancelCSPDForApproval(var CSPD: Record "Customer Sales Price/Discount")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelCSPDforApprovalCode(), CSPD);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryCSPD();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendCSPDforApprovalCode(), DATABASE::"Customer Sales Price/Discount",
          CopyStr(CSPDsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelCSPDforApprovalCode(), DATABASE::"Customer Sales Price/Discount",
          CopyStr(CSPDrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryCSPD(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelCSPDforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelCSPDforApprovalCode(), RunworkflowOnSendCSPDforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendCSPDforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendCSPDforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendCSPDforApprovalCode());
        end;
    end;

    procedure ISCSPDWorkflowenabled(var CSPD: Record "Customer Sales Price/Discount"): Boolean
    begin
        if CSPD.Status <> CSPD.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(CSPD, RunworkflowOnSendCSPDforApprovalCode()));
    end;

    Procedure CheckCSPDApprovalsWorkflowEnabled(var CSPD: Record "Customer Sales Price/Discount"): Boolean
    begin
        IF not ISCSPDworkflowenabled(CSPD) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentCSPD(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        CSPD: Record "Customer Sales Price/Discount";
    begin
        case RecRef.Number() of
            Database::"Customer Sales Price/Discount":
                begin
                    RecRef.SetTable(CSPD);
                    ApprovalEntryArgument."Document No." := FORMAT(CSPD."Customer No.");//Balu 
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentCSPD(RecRef: RecordRef; var Handled: boolean)
    var
        CSPD: Record "Customer Sales Price/Discount";
    begin
        case RecRef.Number() of
            Database::"Customer Sales Price/Discount":
                begin
                    RecRef.SetTable(CSPD);
                    CSPD.Status := CSPD.Status::open;
                    CSPD.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentCSPD(RecRef: RecordRef; var Handled: boolean)
    var
        CSPD: Record "Customer Sales Price/Discount";
    begin
        case RecRef.Number() of
            Database::"Customer Sales Price/Discount":
                begin
                    RecRef.SetTable(CSPD);
                    CSPD.Status := CSPD.Status::Released;
                    CSPD.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalCSPD(RecRef: RecordRef; var IsHandled: boolean)
    var
        CSPD: Record "Customer Sales Price/Discount";
    begin
        case RecRef.Number() of
            Database::"Customer Sales Price/Discount":
                begin
                    RecRef.SetTable(CSPD);
                    CSPD.Status := CSPD.Status::"Pending Approval";
                    CSPD.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryCSPD(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendCSPDforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendCSPDforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelCSPDforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelCSPDforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryCSPD()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(CSPDCategoryTxt, 1, 20), CopyStr(CSPDCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsCSPD()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Customer Sales Price/Discount", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateCSPD()
    begin
        InsertCSPDApprovalworkflowtemplate();
    end;


    local procedure InsertCSPDApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(CSPDDocOCRWorkflowCodeTxt, 1, 17), CopyStr(CSPDApprWorkflowDescTxt, 1, 100), CopyStr(CSPDCategoryTxt, 1, 20));
        InsertCSPDApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertCSPDApprovalworkflowDetails(var workflow: record Workflow);
    var
        CSPD: Record "Customer Sales Price/Discount";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildCSPDtypecondition(CSPD.Status::open), RunworkflowOnSendCSPDforApprovalCode(), BuildCSPDtypecondition(CSPD.Status::"Pending Approval"), RunworkflowOnCancelCSPDforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildCSPDtypecondition(status: integer): Text
    var
        CSPD: Record "Customer Sales Price/Discount";
    Begin
        CSPD.SetRange(status, Status);
        exit(StrSubstNo(CSPDTypeCondnTxt, workflowsetup.Encode(CSPD.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidCSPD(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidCSPD(RecordRef)
    end;

    local procedure GetConditionalcardPageidCSPD(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Customer Sales Price/Discount":
                exit(page::"Customer Price/Discount Schd"
                );
        end;
    end;

    //Approvals For Customer Price Discount Schedule End<<
    //Approvals For Transport Contract Type Start>>
    [IntegrationEvent(false, false)]
    Procedure OnSendTCTForApproval(var TCT: Record "Transport Contract Types")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelTCTForApproval(var TCT: Record "Transport Contract Types")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendTCTforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendTCTforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendTCTForApproval', '', false, false)]
    local procedure RunworkflowonsendTCTForApproval(var TCT: Record "Transport Contract Types")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendTCTforApprovalCode(), TCT);
    end;

    procedure RunworkflowOnCancelTCTforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelTCTForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelTCTForApproval', '', false, false)]

    local procedure RunworkflowonCancelTCTForApproval(var TCT: Record "Transport Contract Types")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelTCTforApprovalCode(), TCT);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryTCT();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendTCTforApprovalCode(), DATABASE::"Transport Contract Types",
          CopyStr(TCTsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelTCTforApprovalCode(), DATABASE::"Transport Contract Types",
          CopyStr(TCTrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryTCT(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelTCTforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelTCTforApprovalCode(), RunworkflowOnSendTCTforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendTCTforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendTCTforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendTCTforApprovalCode());
        end;
    end;

    procedure ISTCTWorkflowenabled(var TCT: Record "Transport Contract Types"): Boolean
    begin
        if TCT.Status <> TCT.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(TCT, RunworkflowOnSendTCTforApprovalCode()));
    end;

    Procedure CheckTCTApprovalsWorkflowEnabled(var TCT: Record "Transport Contract Types"): Boolean
    begin
        IF not ISTCTworkflowenabled(TCT) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentTCT(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        TCT: Record "Transport Contract Types";
    begin
        case RecRef.Number() of
            Database::"Transport Contract Types":
                begin
                    RecRef.SetTable(TCT);
                    ApprovalEntryArgument."Document No." := copystr(FORMAT(TCT.RecordId), 1, 20);
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentTCT(RecRef: RecordRef; var Handled: boolean)
    var
        TCT: Record "Transport Contract Types";
    begin
        case RecRef.Number() of
            Database::"Transport Contract Types":
                begin
                    RecRef.SetTable(TCT);
                    TCT.Status := TCT.Status::open;
                    TCT.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentTCT(RecRef: RecordRef; var Handled: boolean)
    var
        TCT: Record "Transport Contract Types";
    begin
        case RecRef.Number() of
            Database::"Transport Contract Types":
                begin
                    RecRef.SetTable(TCT);
                    TCT.Status := TCT.Status::Release;
                    TCT.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalTCT(RecRef: RecordRef; var IsHandled: boolean)
    var
        TCT: Record "Transport Contract Types";
    begin
        case RecRef.Number() of
            Database::"Transport Contract Types":
                begin
                    RecRef.SetTable(TCT);
                    TCT.Status := TCT.Status::"Pending for Approval";
                    TCT.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryTCT(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendTCTforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendTCTforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelTCTforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelTCTforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryTCT()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(TCTCategoryTxt, 1, 20), CopyStr(TCTCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsTCT()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Transport Contract Types", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateTCT()
    begin
        InsertTCTApprovalworkflowtemplate();
    end;


    local procedure InsertTCTApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(TCTDocOCRWorkflowCodeTxt, 1, 17), CopyStr(TCTApprWorkflowDescTxt, 1, 100), CopyStr(TCTCategoryTxt, 1, 20));
        InsertTCTApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertTCTApprovalworkflowDetails(var workflow: record Workflow);
    var
        TCT: Record "Transport Contract Types";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildTCTtypecondition(TCT.Status::open), RunworkflowOnSendTCTforApprovalCode(), BuildTCTtypecondition(TCT.Status::"Pending for Approval"), RunworkflowOnCancelTCTforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildTCTtypecondition(status: integer): Text
    var
        TCT: Record "Transport Contract Types";
    Begin
        TCT.SetRange(status, Status);
        exit(StrSubstNo(TCTTypeCondnTxt, workflowsetup.Encode(TCT.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidTCT(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidTCT(RecordRef)
    end;

    local procedure GetConditionalcardPageidTCT(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Transport Contract Types":
                exit(page::"TransportContractTypesCard"
                );
        end;
    end;
    //Approvals For Transport Contract Type<<
    //CR220005-6-PKON22JA7>>
    //Approvals For Transport Contract Type<<
    //Approvals for Transport Setup Approvals Start>>//BaluonFeb 03 2022
    [IntegrationEvent(false, false)]
    Procedure OnSendTSAPForApproval(var TSAP: Record "Transport Setup Approvals")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelTSAPForApproval(var TSAP: Record "Transport Setup Approvals")
    begin
    end;

    //Create events for workflow

    procedure RunworkflowOnSendTSAPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendTSAPforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OnSendTSAPForApproval', '', false, false)]
    local procedure RunworkflowonsendTSAPForApproval(var TSAP: Record "Transport Setup Approvals")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendTSAPforApprovalCode(), TSAP);
    end;

    procedure RunworkflowOnCancelTSAPforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelTSAPForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::Codeunit1, 'OncancelTSAPForApproval', '', false, false)]

    local procedure RunworkflowonCancelTSAPForApproval(var TSAP: Record "Transport Setup Approvals")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelTSAPforApprovalCode(), TSAP);
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibraryTSAP();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendTSAPforApprovalCode(), DATABASE::"Transport Setup Approvals",
          CopyStr(TSAPsendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelTSAPforApprovalCode(), DATABASE::"Transport Setup Approvals",
          CopyStr(TSAPrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', false, false)]
    local procedure OnAddworkfloweventprodecessorstolibraryTSAP(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelTSAPforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelTSAPforApprovalCode(), RunworkflowOnSendTSAPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunworkflowOnSendTSAPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunworkflowOnSendTSAPforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunworkflowOnSendTSAPforApprovalCode());
        end;
    end;

    procedure ISTSAPWorkflowenabled(var TSAP: Record "Transport Setup Approvals"): Boolean
    begin
        if TSAP.Status <> TSAP.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(TSAP, RunworkflowOnSendTSAPforApprovalCode()));
    end;

    Procedure CheckTSAPApprovalsWorkflowEnabled(var TSAP: Record "Transport Setup Approvals"): Boolean
    begin
        IF not ISTSAPworkflowenabled(TSAP) then
            Error((NoworkfloweableErr));
        exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', false, false)]
    local procedure OnpopulateApprovalEntriesArgumentTSAP(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        TSAP: Record "Transport Setup Approvals";
    begin
        case RecRef.Number() of
            Database::"Transport Setup Approvals":
                begin
                    RecRef.SetTable(TSAP);
                    ApprovalEntryArgument."Document No." := FORMAT(TSAP."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', false, false)]
    local procedure OnopendocumentTSAP(RecRef: RecordRef; var Handled: boolean)
    var
        TSAP: Record "Transport Setup Approvals";
    begin
        case RecRef.Number() of
            Database::"Transport Setup Approvals":
                begin
                    RecRef.SetTable(TSAP);
                    TSAP.Status := TSAP.Status::open;
                    TSAP.Modify();
                    TransCtrType.RESET;
                    TransCtrType.SetFilter("Approv Doc No.", TSAP."No.");
                    IF TransCtrType.FindSet() THEN
                        TransCtrType.ModifyALl(Status, TransCtrType.Status::Open);
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentTSAP(RecRef: RecordRef; var Handled: boolean)
    var
        TSAP: Record "Transport Setup Approvals";
    begin
        case RecRef.Number() of
            Database::"Transport Setup Approvals":
                begin
                    RecRef.SetTable(TSAP);
                    TSAP.Status := TSAP.Status::Released;
                    TSAP.Modify();
                    TransCtrType.RESET;
                    TransCtrType.SetFilter("Approv Doc No.", TSAP."No.");
                    IF TransCtrType.FindSet() THEN
                        TransCtrType.ModifyALl(Status, TransCtrType.Status::Release);
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalTSAP(RecRef: RecordRef; var IsHandled: boolean)
    var
        TSAP: Record "Transport Setup Approvals";
    begin
        case RecRef.Number() of
            Database::"Transport Setup Approvals":
                begin
                    RecRef.SetTable(TSAP);
                    TSAP.Status := TSAP.Status::"Pending for Approval";
                    TSAP.Modify();
                    TransCtrType.RESET;
                    TransCtrType.SetFilter("Approv Doc No.", TSAP."No.");
                    IF TransCtrType.FindSet() THEN
                        TransCtrType.ModifyALl(Status, TransCtrType.Status::"Pending For Approval");
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', false, false)]
    local procedure OnaddworkflowresponseprodecessorstolibraryTSAP(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendTSAPforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendTSAPforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelTSAPforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelTSAPforApprovalCode());
        end;
    end;

    //Setup workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', false, false)]
    local procedure OnaddworkflowCategoryTolibraryTSAP()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(TSAPCategoryTxt, 1, 20), CopyStr(TSAPCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', false, false)]
    local procedure OnInsertApprovaltablerelationsTSAP()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Customer Credit Approvals", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', false, false)]
    local procedure OnInsertworkflowtemplateTSAP()
    begin
        InsertTSAPApprovalworkflowtemplate();
    end;


    local procedure InsertTSAPApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(TSAPDocOCRWorkflowCodeTxt, 1, 17), CopyStr(TSAPApprWorkflowDescTxt, 1, 100), CopyStr(TSAPCategoryTxt, 1, 20));
        InsertTSAPApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertTSAPApprovalworkflowDetails(var workflow: record Workflow);
    var
        TSAP: Record "Transport Setup Approvals";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildTSAPtypecondition(TSAP.Status::open), RunworkflowOnSendTSAPforApprovalCode(), BuildTSAPtypecondition(TSAP.Status::"Pending for Approval"), RunworkflowOnCancelTSAPforApprovalCode(), workflowstepargument, true);
    end;


    local procedure BuildTSAPtypecondition(status: integer): Text
    var
        TSAP: Record "Transport Setup Approvals";
    Begin
        TSAP.SetRange(status, Status);
        exit(StrSubstNo(TSAPTypeCondnTxt, workflowsetup.Encode(TSAP.GetView(false))));
    End;

    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', false, false)]
    local procedure OnaftergetpageidTSAP(RecordRef: RecordRef; var PageID: Integer)
    begin
        if PageID = 0 then
            PageID := GetConditionalcardPageidTSAP(RecordRef)
    end;

    local procedure GetConditionalcardPageidTSAP(RecordRef: RecordRef): Integer
    begin
        Case RecordRef.Number() of
            database::"Transport Setup Approvals":
                exit(page::"Transport Setup Approval"
                );
        end;
    end;
    //Approvals For Transport Setup Approvals End<<

    //PKON22FE11-CR220021>>
    [EventSubscriber(ObjectType::Table, 5200, 'OnAfterInsertEvent', '', false, false)]
    local procedure EmploDimCreation(var Rec: Record Employee)
    var
        Dimdef: Record "Default Dimension";
        DimVal: Record "Dimension Value";
        GLSetupRec: Record "General Ledger Setup";
        Dimcodecaption: Text;
    begin
        GLSetupRec.RESET;
        IF GLSetupRec.FINDFIRST THEN
            IF GLSetupRec."Shortcut Dimension 7 Code" <> '' THEN
                Dimcodecaption := GLSetupRec."Shortcut Dimension 7 Code";
        dimval.INIT;
        dimval.VALIDATE(Code, Rec."No.");
        dimval.VALIDATE("Dimension Code", Dimcodecaption);
        dimval.VALIDATE("Global Dimension No.", 7);
        dimval.VALIDATE(Blocked, FALSE);
        dimval.VALIDATE(Active, TRUE);
        dimval.VALIDATE(Name, Rec."Search Name");
        dimval.INSERT;

        dimdef.SETCURRENTKEY("Dimension Code");
        dimdef.RESET;
        dimdef.SETFILTER("Table ID", '%1', 5200);
        dimdef.SETFILTER("No.", Rec."No.");
        dimdef.SETFILTER("Dimension Code", '%1', Dimcodecaption);
        dimdef.SETFILTER("Dimension Value Code", '%1', Rec."No.");
        dimdef.SETFILTER("Value Posting", '%1', dimdef."Value Posting"::"Same Code");
        IF NOT dimdef.FINDFIRST THEN BEGIN
            dimdef.INIT;
            dimdef.VALIDATE("Table ID", 5200);
            dimdef.VALIDATE("No.", Rec."No.");
            dimdef.VALIDATE("Dimension Code", Dimcodecaption);
            dimdef.VALIDATE("Dimension Value Code", Rec."No.");
            dimdef.VALIDATE("Value Posting", dimdef."Value Posting"::"Same Code");
            dimdef.INSERT;
        END;
    end;

    procedure UpdateStaffName(var Rec: Record Employee)
    var
        Dimdef: Record "Default Dimension";
        DimVal: Record "Dimension Value";
        GLSetupRec: Record "General Ledger Setup";
        Dimcodecaption: Text;
    begin
        GLSetupRec.RESET;
        IF GLSetupRec.FINDFIRST THEN
            IF GLSetupRec."Shortcut Dimension 7 Code" <> '' THEN
                Dimcodecaption := GLSetupRec."Shortcut Dimension 7 Code";
        dimval.INIT;
        dimval.SETRANGE("Dimension Code", Dimcodecaption);
        dimval.SETRANGE(Code, Rec."No.");
        dimval.SETRANGE("Global Dimension No.", 7);
        IF dimval.FindFirst() then begin
            dimval.VALIDATE(Name, Rec."Search Name");
            DimVal.Modify();
        end;

    end;
    //PKON22FE11-CR220021<<
    //CR220117_B2BESGON27Dec2022>>
    [EventSubscriber(ObjectType::Codeunit, 5750, 'OnBeforeCreateShptLineFromSalesLine', '', false, false)]
    local procedure OnBeforeCreateShptLineFromSalesLine(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; WarehouseShipmentHeader: Record "Warehouse Shipment Header"; SalesLine: Record "Sales Line"; SalesHeader: Record "Sales Header")
    begin
        WarehouseShipmentLine."Ship to Address" := SalesHeader."Ship-to Address";
        WarehouseShipmentLine."Ship to Address 2" := SalesHeader."Ship-to Address 2";
    end;

    [EventSubscriber(ObjectType::Codeunit, 5750, 'OnBeforeCreateShptLineFromTransLine', '', false, false)]
    local procedure OnBeforeCreateShptLineFromTransLine(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; WarehouseShipmentHeader: Record "Warehouse Shipment Header"; TransferLine: Record "Transfer Line"; TransferHeader: Record "Transfer Header")
    var
        LocationLRec: Record Location;
    begin
        if LocationLRec.Get(TransferHeader."Transfer-to Code") then begin
            WarehouseShipmentLine."Ship to Address" := LocationLRec.Address;
            WarehouseShipmentLine."Ship to Address 2" := LocationLRec."Address 2";
        end;
    end;
    //CR220117_B2BESGON27Dec2022<<
    //BaluPrksON13Oct2022>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', false, false)]
    local procedure OnSetstatusToPendingApprovalForFAGenjnl(RecRef: RecordRef; var IsHandled: boolean)
    var
        Genjnlln: Record "Gen. Journal Line";
    begin
        case RecRef.Number() of
            Database::"Gen. Journal Line":
                begin
                    RecRef.SetTable(Genjnlln);
                    Genjnlln."Approval Status" := Genjnlln."Approval Status"::"Pending for Approval";
                    Genjnlln.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnSetstatusToReleaseForFAGenjnl(RecRef: RecordRef; var Handled: boolean)
    var
        Genjnlln: Record "Gen. Journal Line";
    begin
        case RecRef.Number() of
            Database::"Gen. Journal Line":
                begin
                    RecRef.SetTable(Genjnlln);
                    Genjnlln."Approval Status" := Genjnlln."Approval Status"::Released;
                    Genjnlln.Modify();
                    Handled := true;
                end;
        end;
    end;
    //BaluPrksON13Oct2022<<
    // RFC 22
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterFillInvoicePostBuffer', '', false, false)]
    local procedure InsertSalesTempLines(var InvoicePostBuffer: Record "Invoice Post. Buffer"; SalesLine: Record "Sales Line"; var TempInvoicePostBuffer: Record "Invoice Post. Buffer" temporary) //; CommitIsSupressed: Boolean)
    begin
        InvoicePostBuffer."Description 2" := SalesLine."Description 2";
        Invoicepostbuffer."Return Reason Code" := Salesline."Return Reason Code";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnBeforePostInvPostBuffer', '', false, false)]
    local procedure InsertSalesJurnalPostLines(var GenJnlLine: Record "Gen. Journal Line"; var InvoicePostBuffer: Record "Invoice Post. Buffer"; var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line");//; PreviewMode: Boolean) //; CommitIsSupressed: Boolean)
    begin

        GenJnlLine.Narration := InvoicePostBuffer."Return Reason Code";
        GenJnlLine."Description 2" := InvoicePostBuffer."Description 2";
    end;
    //RFC 22
    var
        credilimitsc: Record "Cust. Cr. Limit Schedule";
        SmtpMail: Codeunit "SMTP Mail";
        SMTPSetup: record "SMTP Mail Setup";
        cu: Codeunit 20;
        DimMgt: Codeunit DimensionManagement;
        DimBufMgt: Codeunit "Dimension Buffer Management";
        WorkflowManagement: Codeunit "Workflow Management";

        WorkflowevenHandling: Codeunit "Workflow Event Handling";
        workflowsetup: codeunit "Workflow Setup";
        CreditLimitDocOCRWorkflowCodeTxt: Label 'INCDOC-Credit Limit';
        ITMFADocOCRWorkflowCodeTxt: Label 'INCDOC-ITMFA';
        CreditLimitsendforapprovaleventdescTxt: Label 'Approval of a CreditLimit document is requested';
        ITMFAsendforapprovaleventdescTxt: Label 'Approval of a ITMFA document is requested';
        CreditLimitrequestcanceleventdescTxt: Label 'Approval of a CreditLimit document is Cancelled';
        CWIPrequestcanceleventdescTxt: Label 'Approval of a CWIIP document is Cancelled';
        ITMFArequestcanceleventdescTxt: Label 'Approval of a ITMFA document is Cancelled';
        NoworkfloweableErr: Label 'No Approval workflow for this record type is enabled.';
        CreditLimitCategoryTxt: Label 'CreditLimit';
        CWIPCategoryTxt: Label 'CWIP';
        ITMFACategoryTxt: Label 'ITMFA';
        CreditLimitCategoryDescTxt: Label 'CreditLimitDocuments';
        CWIPCategoryDescTxt: Label 'CWIPDocuments';

        ITMFACategoryDescTxt: Label 'ITMFADocuments';
        CreditLimitApprWorkflowDescTxt: Label 'CreditLimit Approval Workflow';
        CWIPApprWorkflowDescTxt: Label 'CWIP Approval Workflow';
        ITMFAApprWorkflowDescTxt: Label 'ITMFA Approval Workflow';
        IncDocOCRWorkflowCodeTxt: Label 'INCDOC-CreditLimit';
        IncDocOITMFAWorkflowCodeTxt: Label 'INCDOC-ITMFA';
        CreditLimitTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CreditLimit">%1</DataItem></DataItems></ReportParameters>';

        ITMFATypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="ITMFA">%1</DataItem></DataItems></ReportParameters>';

        CompCreditLimitScheduleDocOCRWorkflowCodeTxt: Label 'INCDOC-Company Credit Limit Schedule';
        CompCreditLimitSchedulesendforapprovaleventdesctxt: Label 'Approval of a Company CreditLimit Schedule document is requested';
        CompCreditLimitSchedulerequestcanceleventdesctxt: Label 'Approval of a Company CreditLimit Schedule document is Cancelled';
        CompCreditLimitScheduleCategoryTxt: Label 'Company CreditLimit Schedule';
        CompCreditLimitScheduleCategoryDescTxt: Label 'CompanyCreditLimitScheduleDocuments';
        CompCreditLimitScheduleApprWorkflowDescTxt: Label 'Company CreditLimit Schedule Approval Workflow';
        CompCreditLimitScheduleTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CreditLimit">%1</DataItem></DataItems></ReportParameters>';


        CompCreditLimitBudgetDocOCRWorkflowCodeTxt: Label 'INCDOC-Company Credit Limit Budget';
        CompCreditLimitBudgetsendforapprovaleventdesctxt: Label 'Approval of a Company CreditLimit Budget document is requested';
        CompCreditLimitBudgetrequestcanceleventdesctxt: Label 'Approval of a Company CreditLimit Budget document is Cancelled';
        CompCreditLimitBudgetCategoryTxt: Label 'Company CreditLimit Budget';
        CompCreditLimitBudgetCategoryDescTxt: Label 'CompanyCreditLimitBudgetDocuments';
        CompCreditLimitBudgetApprWorkflowDescTxt: Label 'Company CreditLimit Budget Approval Workflow';
        CompCreditLimitBudgetTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CreditLimit">%1</DataItem></DataItems></ReportParameters>';

        AddCreditLimitsendforapprovaleventdescTxt: Label 'Approval of a Additional CreditLimit document is requested';
        AddCreditLimitCategoryDescTxt: Label 'AddCreditLimitDocuments';
        AddCreditLimitTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="AddCreditLimit">%1</DataItem></DataItems></ReportParameters>';

        AddCreditLimitrequestcanceleventdescTxt: Label 'Approval of a Add CreditLimit document is Cancelled';
        AddCreditLimitCategoryTxt: Label 'AddCreditLimit';
        AddCreditLimitDocOCRWorkflowCodeTxt: Label 'Add Credit Limit';
        AddCreditLimitApprWorkflowDescTxt: Label 'AddCreditLimit Approval Workflow';
        //Transport Contract Type>>
        TCTrequestcanceleventdescTxt: Label 'Approval of a Transport Contract document is Cancelled';
        TCTsendforapprovaleventdescTxt: Label 'Approval of a Transport Contract Type document is requested';
        TCTCategoryDescTxt: Label 'TCTDocuments';
        TCTCategoryTxt: Label 'TCT';
        TCTApprWorkflowDescTxt: Label 'TCT Approval Workflow';
        TCTDocOCRWorkflowCodeTxt: Label 'Transport Contract Types';
        TCTTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="TCT">%1</DataItem></DataItems></ReportParameters>';
        //Transport Contract Type<<

        //CWIP>>
        AddCWIPsendforapprovaleventdescTxt: Label 'Approval of a CWIP document is requested';
        AddCWIPCategoryDescTxt: Label 'AddCWIPDocuments';
        AddCWIPTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="AddCWIP">%1</DataItem></DataItems></ReportParameters>';

        AddCWIPrequestcanceleventdescTxt: Label 'Approval of a Add CWIP document is Cancelled';
        AddCWIPCategoryTxt: Label 'AddCWIP';
        AddCWIPDocOCRWorkflowCodeTxt: Label 'Add CWIP';
        AddCWIPApprWorkflowDescTxt: Label 'Add CWIP Approval Workflow';

        //CWIP<<

        //Customer sales Price Discount Schedule>>
        CSPDsendforapprovaleventdescTxt: Label 'Approval of a Customer Price Discount Schedule document is requested';
        CSPDrequestcanceleventdescTxt: Label 'Approval of a Customer Price Discount Schedule document is Cancelled';
        CSPDCategoryTxt: Label 'CSPD';
        CSPDCategoryDescTxt: Label 'CSPDDocuments';
        CSPDDocOCRWorkflowCodeTxt: Label 'Customer Price Discount Schedule';
        CSPDApprWorkflowDescTxt: Label 'Customer Prices Discount Schedule Approval Workflow';
        CSPDTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CSPD">%1</DataItem></DataItems></ReportParameters>';
        //Customer sales Price Discount Schedule<<

        LSPsendforapprovaleventdescTxt: Label 'Approval of a Loading SLip document is requested';
        CLAPsendforapprovaleventdescTxt: Label 'Approval of a Customer Credit Limit Approval Document is requested';
        IFCsendforapprovaleventdescTxt: Label 'Approval of a Import File document is requested';
        CLHsendforapprovaleventdescTxt: Label 'Approval of a Clearing document is requested';
        PWSsendforapprovaleventdescTxt: Label 'Approval of a Production Work Sheet is requested';
        PRHsendforapprovaleventdescTxt: Label 'Approval of a Posted Purchase Receipt document is requested';
        PSSsendforapprovaleventdescTxt: Label 'Approval of a Posted Sales Shipment is requested';
        PMSsendforapprovaleventdescTxt: Label 'Approval of a PMS Management document is requested';
        POSsendforapprovaleventdesctxt: Label 'Approval of a Production Order document is requested';
        CPXsendforapprovaleventdescTxt: Label 'Approval of a Capex document is requested';
        TROsendforapprovaleventdescTxt: Label 'Approval of a Transfer Order document is requested';
        PRSsendforapprovaleventdescTxt: Label 'Approval of a Promo Schedule document is requested';
        BCDsendforapprovaleventdesctxt: Label 'Approval of a Bank Check Document is Requested';
        BACsendforapprovaleventdesctxt: Label 'Approval of a Bank Account Document is Requested';

        SCLsendforapprovaleventdesctxt: Label 'Approval of a Special Credit Limit document is requested';
        GATEsendforapprovaleventdesctxt: Label 'Approval of a Gate entry document is requested';
        LSPCategoryDescTxt: Label 'LSPDocuments';
        CLAPCategoryDescTxt: Label 'CLAPDocuments';
        CLHCategoryDescTxt: Label 'CLHDocuments';
        IFCCategoryDescTxt: Label 'IFCDocuments';
        PWSCategoryDescTxt: Label 'PWSDocuments';
        PRHCategoryDescTxt: Label 'PRHDocuments';
        PSSCategoryDescTxt: Label 'PSSDocuments';
        PMSCategoryDescTxt: Label 'PMSDocuments';
        POSCategoryDescTxt: Label 'POSDocuments';
        CPXCategoryDescTxt: Label 'CPXDocuments';
        TROCategoryDescTxt: Label 'TRODocuments';
        PRSCategoryDescTxt: Label 'PRSDocuments';
        BCDCategoryDescTxt: Label 'BCDDocuments';
        BACCategoryDescTxt: Label 'BACDocuments';
        SCLCategoryDescTxt: Label 'SCLDocuments';
        GATECategoryDescTxt: Label 'GATEDocuments';
        LSPTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="LSP">%1</DataItem></DataItems></ReportParameters>';
        CLAPTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CLAP">%1</DataItem></DataItems></ReportParameters>';
        CLHTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CLH">%1</DataItem></DataItems></ReportParameters>';
        IFCTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="IFC">%1</DataItem></DataItems></ReportParameters>';
        PWSTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="PWS">%1</DataItem></DataItems></ReportParameters>';
        PRHTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="PRH">%1</DataItem></DataItems></ReportParameters>';
        PSSTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="PSS">%1</DataItem></DataItems></ReportParameters>';
        PMSTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="PMS">%1</DataItem></DataItems></ReportParameters>';
        POSTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="POS">%1</DataItem></DataItems></ReportParameters>';

        CPXTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="CPX">%1</DataItem></DataItems></ReportParameters>';

        TROTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="TRO">%1</DataItem></DataItems></ReportParameters>';
        PRSTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="PRS">%1</DataItem></DataItems></ReportParameters>';
        BCDTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="BCD">%1</DataItem></DataItems></ReportParameters>';
        BACTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="BAC">%1</DataItem></DataItems></ReportParameters>';
        SCLTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="SCL">%1</DataItem></DataItems></ReportParameters>';

        GATETypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="GATE">%1</DataItem></DataItems></ReportParameters>';
        LSPrequestcanceleventdescTxt: Label 'Approval of a Loading Slip document is Cancelled';
        CLAPrequestcanceleventdescTxt: Label 'Approval of a Customer CreditLimitApproval document is Cancelled';
        IFCrequestcanceleventdescTxt: Label 'Approval of a Import File document is Cancelled';
        CLHrequestcanceleventdescTxt: Label 'Approval of a Clearing document is Cancelled';
        PWSrequestcanceleventdescTxt: Label 'Approval of a Production Work Sheet is Cancelled';
        PRHrequestcanceleventdescTxt: Label 'Approval of a Posted Purchase Receipt document is Cancelled';
        PSSrequestcanceleventdescTxt: Label 'Approval of a Posted Sales Shipment document is Cancelled';
        PMSrequestcanceleventdescTxt: Label 'Approval of a PMS document is Cancelled';
        POSrequestcanceleventdesctxt: Label 'Approval of a Production Order document is Cancelled';
        CPXrequestcanceleventdescTxt: Label 'Approval of a Capex document is Cancelled';
        TROrequestcanceleventdescTxt: Label 'Approval of a Transfer Order document is Cancelled';
        PRSrequestcanceleventdescTxt: Label 'Approval of a Promo SChedule document is Cancelled';
        BCDrequestcanceleventdesctxt: Label 'Approval of a Bank Check Document is Cancelled';
        BACrequestcanceleventdesctxt: Label 'Approval of a Bank Account Document is Cancelled';
        SCLrequestcanceleventdescTxt: Label 'Approval of a Special Credit Limit document is Cancelled';
        GATErequestcanceleventdescTxt: Label 'Approval of a Gate Entry document is Cancelled';
        LSPCategoryTxt: Label 'LSP';
        CLAPCategoryTxt: Label 'CLAP';
        CLHCategoryTxt: Label 'CLH';
        IFCCategoryTxt: Label 'IFC';
        PWSCategoryTxt: Label 'PWS';
        PRHCategoryTxt: Label 'PRH';
        PSSCategoryTxt: Label 'PSS';
        PMSCategoryTxt: Label 'PMS';
        POSCategoryTxt: Label 'POS';
        CPXCategoryTxt: Label 'CPX';
        TROCategoryTxt: Label 'TRO';
        PRSCategoryTxt: Label 'PRS';
        BCDCategoryTxt: Label 'BCD';
        BACCategoryTxt: Label 'BAC';
        SCLCategoryTxt: Label 'Special Credit Limit';

        GATECategoryTxt: Label 'Gate Entry';
        LSPDocOCRWorkflowCodeTxt: Label 'Loading Slip';
        CLAPDocOCRWorkflowCodeTxt: Label 'Customer Credit Limit';
        CLHDocOCRWorkflowCodeTxt: Label 'Clearing';
        IFCDocOCRWorkflowCodeTxt: Label 'Import File';
        PWSDocOCRWorkflowCodeTxt: Label 'Production Work Sheet';
        PRHDocOCRWorkflowCodeTxt: Label 'Posted Purchase Receipt';
        PSSDocOCRWorkflowCodeTxt: Label 'Posted Sales Shipment';
        PMSDocOCRWorkflowCodeTxt: Label 'PMS Management';
        POSDocOCRWorkflowCodeTxt: Label 'Production Order';
        CPXDocOCRWorkflowCodeTxt: Label 'Capex';
        TRODocOCRWorkflowCodeTxt: Label 'Loading Slip';
        PRSDocOCRWorkflowCodeTxt: Label 'Promo Schedule';
        BCDDocOCRWorkflowCodeTxt: Label 'Bank Check Details';
        BACDocOCRWorkflowCodeTxt: Label 'Bank Account Details';
        SCLDocOCRWorkflowCodeTxt: Label 'Special Credit Limit';
        GATEDocOCRWorkflowCodeTxt: Label 'Gate Entry';
        LSPApprWorkflowDescTxt: Label 'LSP Approval Workflow';
        CLAPApprWorkflowDescTxt: Label 'CLAP Approval Workflow';
        CLHApprWorkflowDescTxt: Label 'CLH Approval Workflow';
        IFCApprWorkflowDescTxt: Label 'IFC Approval Workflow';
        PWSApprWorkflowDescTxt: Label 'PWS Approval Workflow';
        PRHApprWorkflowDescTxt: Label 'PRH Approval Workflow';
        PSSApprWorkflowDescTxt: Label 'PSS Approval Workflow';
        PMSApprWorkflowDescTxt: Label 'PMS Approval Workflow';
        POSApprWorkflowDescTxt: Label 'POS Approval Workflow';
        CPXApprWorkflowDescTxt: Label 'CPX Approval Workflow';
        TROApprWorkflowDescTxt: Label 'TRO Approval Workflow';
        PRSApprWorkflowDescTxt: Label 'PRS Approval Workflow';
        BCDApprWorkflowDescTxt: Label 'BCD Approval Workflow';
        SCLApprWorkflowDescTxt: Label 'Special Credit Limit';
        GATEApprWorkflowDescTxt: Label 'Gate Entry';
        MDVsendforapprovaleventdescTxt: Label 'Approval of a Material Disposal Voucher document is requested';
        MDVrequestcanceleventdescTxt: Label 'Approval of a Material Disposal Voucher document is Cancelled';
        MDVCategoryTxt: Label 'MaterialDisposalVoucher';
        MDVCategoryDescTxt: Label 'MaterialDisposalVoucherDocuments';
        MDVApprWorkflowDescTxt: Label 'MaterialDisposalVoucher Approval Workflow';
        TSAPTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="TSAP">%1</DataItem></DataItems></ReportParameters>';
        //Transport Setup Variable>>
        TSAPCategoryTxt: Label 'TSAP';
        TSAPCategoryDescTxt: Label 'CLAPDocuments';
        TSAPsendforapprovaleventdescTxt: Label 'Approval of a Transport Setup Approval Document is requested';
        TSAPrequestcanceleventdescTxt: Label 'Approval of a Transport Setup Approval document is Cancelled';
        TSAPDocOCRWorkflowCodeTxt: Label 'Transport Setup Approvals';
        TSAPApprWorkflowDescTxt: Label 'TSAP Approval Workflow';
        TransCtrType: Record "Transport Contract Types";
        MDVTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="MaterialDisposalVoucher">%1</DataItem></DataItems></ReportParameters>';



}