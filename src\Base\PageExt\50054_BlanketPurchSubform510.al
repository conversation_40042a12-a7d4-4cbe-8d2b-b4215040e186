pageextension 50054 BlanketPurchSubform extends "Blanket Purchase Order Subform"
{
    layout
    {
        addafter(Quantity)
        {

            field("Min Qty"; "Min Qty")
            {
                ApplicationArea = all;
            }
            field("Contract Start Date"; "Contract Start Date")
            {
                ApplicationArea = all;
                trigger OnValidate()
                var
                    PurchHeaderLrec: Record "Purchase Header";
                begin
                    PurchHeaderLrec.Reset();
                    PurchHeaderLrec.SetRange("Document Type", "Document Type");
                    PurchHeaderLrec.SetRange("No.", "Document No.");
                    if PurchHeaderLrec.FindFirst() then begin
                        if ("Contract Start Date" < PurchHeaderLrec."Contract Start Date") OR ("Contract Start Date" > PurchHeaderLrec."End Date") then
                            Error('Contract start date shoud be the range of Header ');

                    end;
                end;
            }
            field("End Date"; "End Date")
            {
                ApplicationArea = all;
                trigger OnValidate()
                var
                    PurchHeaderLrec: Record "Purchase Header";
                begin
                    PurchHeaderLrec.Reset();
                    PurchHeaderLrec.SetRange("Document Type", "Document Type");
                    PurchHeaderLrec.SetRange("No.", "Document No.");
                    if PurchHeaderLrec.FindFirst() then begin
                        if ("End Date" > PurchHeaderLrec."End Date") OR ("End Date" < PurchHeaderLrec."Contract Start Date") then
                            Error('End  date cannot be less than Header date ');
                    end;
                end;
            }
        }
        modify("Qty. to Receive")
        {
            trigger OnBeforeValidate()
            var
                PurchHeadLrec: Record "Purchase Header";
            begin
                if "Qty. to Receive" <= "Min Qty" then
                    Error('Quantity should be Min Quantity');
                PurchHeadLrec.Reset();
                PurchHeadLrec.SetRange("Document Type", "Document Type");
                PurchHeadLrec.SetRange("No.", "Document No.");
                if PurchHeadLrec.FindFirst() then
                    if ("End Date" < PurchHeadLrec."Order Date") OR ("Contract Start Date" > PurchHeadLrec."Order Date") then
                        Error('You cannot give Qty to Receive %1 Document date is not with in range.', "Qty. to Receive");
            end;
        }

    }

    actions
    {
        addafter("E&xplode BOM")
        {
            action("Task Lines")
            {
                Image = List;
                RunObject = page "Task Codes Lines";
                RunPageLink = "Document Type" = field("Document Type"), "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                ApplicationArea = All;
            }
        }

    }

}