/// <summary>
/// Page Product Transaction Log List (ID 50100).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
page 50203 "Product Transaction Log List"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    InsertAllowed = false;
    ModifyAllowed = false;
    SourceTable = "Product Transaction Log";

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(ID; Rec.ID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the ID field.';
                    Visible = false;
                }
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Description field.';
                }
                field("Type"; Rec."Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Type field.';
                }
                field("Item Category Code"; Rec."Item Category Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Item Category Code field.';
                }
                field("Item Category Id"; Rec."Item Category Id")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Item Category Id field.';
                }
                field(sentStatus; Rec.sentStatus)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the sentStatus field.';
                }
                field(Remark; Rec.Remark)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Remark field.';
                }
                field(brand_code; Rec.brand_code)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the brand_code field.';
                    Visible = false;
                }
                field(brand_name; Rec.brand_name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the brand_name field.';
                    Visible = false;
                }
                field(prod_cbb_volume; Rec.prod_cbb_volume)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the prod_cbb_volume field.';
                }
                field(prod_custom_code; Rec.prod_custom_code)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the prod_custom_code field.';
                    Visible = false;
                }
                field(prod_hsn_code; Rec.prod_hsn_code)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the prod_hsn_code field.';
                    Visible = false;
                }
                field(prod_pack_volume; Rec.prod_pack_volume)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the prod_pack_volume field.';
                    Visible = false;
                }
                field(prod_short_name; Rec.prod_short_name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the prod_short_name field.';
                    Visible = false;
                }
                field(product_type_name; Rec.product_type_name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the product_type_name field.';
                    Visible = false;
                }
                field(sku_group_code; Rec.sku_group_code)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the sku_group_code field.';
                    Visible = false;
                }
                field(sku_group_name; Rec.sku_group_name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the sku_group_name field.';
                    Visible = false;
                }
                field(sub_brand_code; Rec.sub_brand_code)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the sub_brand_code field.';
                    Visible = false;
                }
                field(sub_brand_name; Rec.sub_brand_name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the sub_brand_name field.';
                    Visible = false;
                }
                field(tax1perc; Rec.tax1perc)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the tax1perc field.';
                    Visible = false;
                }
                field(tax2perc; Rec.tax2perc)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the tax2perc field.';
                    Visible = false;
                }
                field(tax3perc; Rec.tax3perc)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the tax3perc field.';
                    Visible = false;
                }
                field("Temp. Omit Rec"; "Temp. Omit Rec")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Date Sent to Outreach"; "Date Sent to Outreach")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Test)
            {
                ApplicationArea = All;
                trigger OnAction()
                var
                    jsonIntegration: Codeunit "Json Integration-Product Tran";
                begin
                    jsonIntegration.sendBatchProductJSON(Rec);
                end;
            }
        }
    }
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<