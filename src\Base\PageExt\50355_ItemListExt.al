pageextension 50355 Itemlist extends "Item List"
{

    layout
    {
        modify("Last Direct Cost")
        {
            Visible = true;
        }
        addafter("No.")
        {
            field("No. 2"; "No. 2")
            {
                ApplicationArea = all;
            }
            field("Last Movement Date"; "Last Movement Date") //PKONOC5
            {
                ApplicationArea = all;
            }
            field("Last Purchase Date"; "Last Purchase Date")
            {
                ApplicationArea = all;
            }


        }
    }
    actions
    {
        addafter("Item Reclassification Journal")
        {
            action("Show Item Projections")
            {
                ApplicationArea = all;
                Image = ProjectExpense;
                PromotedIsBig = true;
                Promoted = true;
                trigger OnAction()
                var
                    myInt: Integer;
                begin
                    Clear(itemproj);
                    itemproj.getloc(GetFilter("Location Filter"));
                    itemproj.RunModal();
                end;
            }
        }
    }
    var
        itemproj: page "Item Projection List";
}