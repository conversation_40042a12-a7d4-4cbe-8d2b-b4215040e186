tableextension 50060 SalesInvHeaderExt extends "Sales Invoice Header"
{
    fields
    {
        field(50020; "Old_Delivery Effected By"; Option)
        {
            OptionMembers = ,Customer,Marketing,"Supply Chain Department";
            DataClassification = CustomerContent;
        }
        field(50021; "Customer Reference No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Authorisers ID"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            //B2B.P.K.T
        }
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;

        }
        field(50006; "POS Window"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //Fix06Jul2021>>
        field(50064; "Sales Area"; Code[20])
        {
            DataClassification = CustomerContent;
            Caption = 'Sales Office';//PKONJU7
            Editable = false;
        }
        //Fix06Jul2021<<
        field(50065; "Total Qty Sold"; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = sum("Sales Invoice Line".Quantity where("Document No." = field("No.")));
            Editable = false;
        }
        field(65000; Total; Decimal)
        {
            Description = 'GJ_CHIPOS_RKD_141013';
        }

        field(65004; "POS Transaction Type"; Option)
        {
            Description = 'GJ_CHIPOS_RKD_181113';
            OptionCaption = ',Cash,Card,Both';//,Fund Transfer';
            OptionMembers = ,Cash,Card,Both;//,"Fund Transfer";
        }
        field(65005; "POS Transaction No."; Code[50])
        {
            Description = 'GJ_CHIPOS_RKD_181113';
        }
        field(65006; "POS User ID"; Code[50])
        {
            Description = 'GJ_CHIPOS_RKD_181113,CHIUPG';
        }
        field(65007; "POS Card Amount"; Decimal)
        {
        }
        field(65008; "POS Cash Amount"; Decimal)
        {
        }
        field(65009; "Pos Bank Name"; Option)
        {
            //OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB"';//PKONDE16
            //OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB;//PKONDE16
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB",CORONATION,Zenith Fund Transfer,Access Fund Transfer,PROVIDUS,GTB Fund Transfer';//PKONDE16;//PKONDE16 //RFC#40
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB,CORONATION,"Zenith Fund Transfer","Access Fund Transfer",PROVIDUS,"GTB Fund Transfer";//PKONDE16; //RFC#40


        }
        field(65011; "POS Account No."; Code[10])
        {

        }
        //B2BMS
        field(65012; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(65013; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(65014; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(65015; "Modified date"; DateTime)
        {
            Editable = false;
        }
        field(65019; "Applied By"; code[50]) //PKON22AP28.2
        {
            Editable = false;
            DataClassification = CustomerContent;

        }
        //270324
        field(65020; "Rebate Discount"; Decimal)
        {
            //  FieldClass = FlowField;
            // CalcFormula = Sum("Sales Header"."Rebate Discount" WHERE("No." = FIELD("Order No.")));
            caption = 'Variable Discount';
        }
        field(65021; "Fixed Rebate Amount"; Decimal)
        {
            Editable = false;
            DataClassification = CustomerContent;
            Caption = 'Fixed Discount Amount';

        }
        //270324
        field(65022; "AmtAfterRebate"; Decimal)
        {
            FieldClass = FlowField;
            Caption = 'Amount After Discounts';
            CalcFormula = sum("Sales Invoice Line".AmtafterRebate where("Document No." = field("No."), Quantity = filter(<> 0)));
            Editable = false;
        }
        field(65023; "AmtAfterRebateIncVAT"; Decimal)
        {
            FieldClass = FlowField;
            Caption = 'Amount After Discounts';
            CalcFormula = sum("Sales Invoice Line".AmtafterRebateIncVAT where("Document No." = field("No."), Quantity = filter(<> 0)));
            Editable = false;
        }
        field(50011; "Printable Comment 1"; Text[50])
        {
            DataClassification = CustomerContent;
            Description = 'Additional information to print on the Sales Order';
            Editable = false;
        }
        field(50031; "Processed by API"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'Processed by API';
            Editable = false;
        }
        field(50032; "Processed by API On"; DateTime)
        {
            DataClassification = CustomerContent;
            Description = 'Processed by API On';
            Editable = false;
        }
        field(65100; "POS Bank Names"; Text[200])
        {
            DataClassification = ToBeClassified;
            Editable = false;

        }


        //rebate issue

        //B2BMS
        modify("Sell-to Customer No.")
        {
            trigger OnAfterValidate()
            var

            BEGIN
                IF (CustGRec.get("Sell-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
            END;

        }

        modify("Bill-to Customer No.")
        {
            trigger OnAfterValidate()
            var

            BEGIN
                IF (CustGRec.get("Bill-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
            END;

        }



    }

    var
        myInt: Integer;
        CustGRec: Record Customer;
}