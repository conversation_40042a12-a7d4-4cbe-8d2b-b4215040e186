pageextension 50007 InvSetPage extends "Inventory Setup"
{
    layout
    {
        addafter("Item Nos.")
        {
            field("MRS Nos."; "MRS Nos.")
            {
                ApplicationArea = all;
            }
            field("Purch. Requisition No."; "Purch. Requisition No.")
            {
                ApplicationArea = all;
            }
            field("Create Purch Req On"; "Create Purch Req On")
            {
                ApplicationArea = all;
            }
            field("RFQ Nos."; "RFQ Nos.")
            {
                ApplicationArea = all;
            }
            field("Trip ID NoSeries"; "Trip ID NoSeries")
            {
                ApplicationArea = all;
            }
            field("Inward Gate Entry Nos. - RGP"; "Inward Gate Entry Nos. - RGP")
            {
                ApplicationArea = ALL;
            }
            field("Outward Gate Entry Nos.-RGP"; "Outward Gate Entry Nos.-RGP")
            {
                ApplicationArea = ALL;
            }
            field("Scrap Journal Batch Name"; "Scrap Journal Batch Name")
            {
                ApplicationArea = ALL;
            }
            field("Scrap Journal Template Name"; "Scrap Journal Template Name")
            {
                ApplicationArea = ALL;
            }
            field("Inward Gate Entry Nos._NRGP"; "Inward Gate Entry Nos._NRGP")
            {
                ApplicationArea = all;
            }
            field("Outward Gate Entry Nos.-NRGP"; "Outward Gate Entry Nos.-NRGP")
            {
                ApplicationArea = all;
            }
            field("SNOP FG Item Category"; "SNOP FG Item Category")
            {
                ApplicationArea = all;
            }
            field("MDV Nos."; "MDV Nos.")
            {
                ApplicationArea = all;
            }
            field("TT InTransit Location"; "TT InTransit Location")
            {
                ApplicationArea = all;
            }
            field("CCD Approval No. series"; "CCD Approval No. series")
            {
                ApplicationArea = all;
            }
            field("SCD Approval No. series"; "SCD Approval No. series")
            {
                ApplicationArea = all;
            }
            field("Inward RGP No. Series"; "Inward RGP No. Series")
            {
                ApplicationArea = all;
            }
            field("Inward NRGP No. Series"; "Inward NRGP No. Series")
            {
                ApplicationArea = all;
            }
            field("Outward RGP No. Series"; "Outward RGP No. Series")
            {
                ApplicationArea = all;
            }
            field("Outward NRGP No. Series"; "Outward NRGP No. Series")
            {
                ApplicationArea = all;
            }
            field("Mail ID If Loadslp Nt Ack"; "Mail ID If Loadslp Nt Ack")
            {
                ApplicationArea = all;
            }
            field("Requisition Nos."; "Requisition Nos.")
            {
                ApplicationArea = all;
            }
            field("Scrap Disposal Customer ID"; "Scrap Disposal Customer ID")
            {
                ApplicationArea = all;
            }
            field("Scrap Disposal GL Account No."; "Scrap Disposal GL Account No.")
            {
                ApplicationArea = all;
            }
            field("Bin Reclass Mail Alert ID"; "Bin Reclass Mail Alert ID")
            {
                ApplicationArea = all;
            }
            field("Bin Reclass Mail Alert ID2"; "Bin Reclass Mail Alert ID2")
            {
                ApplicationArea = all;
                Caption = 'Bin Reclass Mail Alert ID 2';
            }
            field("Shortg & Damg Mail Alert ID"; "Shortg & Damg Mail Alert ID")
            {
                ApplicationArea = all;
            }
            field("Update Exp Date"; "Update Exp Date")
            {
                ApplicationArea = all;
            }
            field("Retail Acct Location"; "Retail Acct Location")
            {
                ApplicationArea = all;
            }
            field("POS Central Location"; "POS Central Location")
            {
                ApplicationArea = all;
            }
            field("Retail Price Group"; "Retail Price Group")
            {
                ApplicationArea = all;
            }
            field("Retail Stock OutletID"; "Retail Stock OutletID")
            {
                ApplicationArea = all;
            }
            field("Retail Stock SupplierID"; "Retail Stock SupplierID")
            {
                ApplicationArea = all;
            }

        }
        addbefore("Automatic Cost Posting")
        {
            field("No Backdating Allowed"; "No Backdating Allowed")
            {
                ApplicationArea = all;
            }
            field("Allowed Date for Inv. BackDat"; "Allowed Date for Inv. BackDate")
            {
                ApplicationArea = all;
            }
            field("Allowed Date for Backdate To"; "Allowed Date for Backdate To")
            {
                ApplicationArea = all;
            }
            field("Check Item Tracking in lSP"; "Check Item Tracking in lSP")
            {
                ApplicationArea = all;
                Caption = 'Check Item Tracking in loading ship';
            }
        }
    }
}