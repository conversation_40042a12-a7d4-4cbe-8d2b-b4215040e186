page 50406 "UserID Resp. Cent. Lines"
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "UserID Resp. Cent. Lines";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("User ID"; "User ID")
                {
                    ApplicationArea = all;
                }
                field("User Name"; "User Name")
                {
                    ApplicationArea = all;
                    visible = false;
                }
                field("Resp. Center Code"; "Resp. Center Code")
                {
                    ApplicationArea = all;
                }
                field("Resp. Center Description"; "Resp. Center Description")
                {
                    ApplicationArea = all;
                }
                field("Expiration Date"; "Expiration Date")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

}