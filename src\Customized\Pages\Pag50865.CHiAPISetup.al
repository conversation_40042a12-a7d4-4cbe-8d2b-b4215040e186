page 50865 "CHI API Setup"
{
    ApplicationArea = all;
    Caption = 'CHI API Setup';
    PageType = List;
    SourceTable = "CHI API Setup";
    SourceTableView = where(apitype = filter(api));
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the No. field.', Comment = '%';
                }
                field("Tenant ID"; Tenant)
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the Tenant ID field.', Comment = '%';
                }
                field(Authorization; Authorize)
                {
                    ApplicationArea = All;
                    ToolTip = 'Enter or view the Authorization value. This field supports large data.';
                    MultiLine = true;

                }
                field("API Endpoint1"; Rec."API Endpoint1")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint1 field.', Comment = '%';
                }
                field("API Endpoint2"; Rec."API Endpoint2")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint2 field.', Comment = '%';
                }
                field("API Endpoint3"; Rec."API Endpoint3")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint3 field.', Comment = '%';
                }
                field("API Endpoint4"; Rec."API Endpoint4")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint4 field.', Comment = '%';
                }
                field("API Endpoint5"; Rec."API Endpoint5")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint5 field.', Comment = '%';
                }
                field("API Endpoint6"; Rec."API Endpoint6")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint6 field.', Comment = '%';
                }
                field("API Endpoint7"; Rec."API Endpoint7")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint7 field.', Comment = '%';
                }
                field("API Endpoint8"; Rec."API Endpoint8")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint8 field.', Comment = '%';
                }
                field("API Endpoint9"; Rec."API Endpoint9")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint9 field.', Comment = '%';
                }
                field("API Endpoint10"; Rec."API Endpoint10")
                {
                    ApplicationArea = All;

                    ToolTip = 'Specifies the value of the API Endpoint10 field.', Comment = '%';
                }

            }
        }
    }
    actions
    {
        area(Processing)
        {

        }

        area(Navigation)
        {
            action(SetCredentials)
            {
                Image = New;
                Caption = 'Set Credentials';
                trigger OnAction()
                var
                    CHIOutStream: OutStream;
                begin
                    UpdatedDetails := false;

                    if Rec."Access Details Provided" then
                        UpdatedDetails := true;
                    if UpdatedDetails then
                        Txt001 := 'Credentails has already been provided. Do you want to update the record?' else
                        Txt001 := 'Please confirm you want to create the access credentials';

                    If Confirm(Txt001, false) then begin

                        if Authorize <> '' then begin
                            Clear(CHIOutStream);
                            Rec.Authorization.CreateOutStream(CHIOutStream);
                            CHIOutStream.WriteText(Authorize);
                            Rec.Validate(Authorization);
                            Rec.Modify();
                        end;
                        if Tenant <> '' then begin
                            Clear(CHIOutStream);
                            Rec."Tenant ID".CreateOutStream(CHIOutStream);
                            CHIOutStream.WriteText(Tenant);
                            Rec.Validate("Tenant ID");
                            Rec.Modify();
                        end;

                        Message('Credentials created successfully');
                    end;
                    clear(Authorize);
                    Clear(Tenant);

                end;


            }

        }
    }

    trigger OnOpenPage()
    var

    begin
        Clear(Authorize);
        clear(Tenant);
    end;





    var
        IsEditable, IsAuthEditable, UpdatedDetails : Boolean;
        InputStream: InStream;
        OutputStream: OutStream;
        Authorize, Tenant : text;
        txt001: Text;
}
