codeunit 50018 ItemQR
{
    Permissions = TableData 27 = rimd;
    TableNo = 27;

    trigger OnRun()
    begin
        UpdateQRCode(Rec);
    end;

    Procedure UpdateQRCode(ItemLpa: Record item)
    var
        item: Record Item;
        ReturnDoc: BigText;
        Myfile: File;
        QRCodeFileName: Text;
        StreamIn: InStream;
        Streamout: OutStream;
        QRCodeInput: Text;
        MemoryStream: DotNet Memory;
        Convert: DotNet Convert;
    begin
        item.get(ItemLpa."No.");
        QRCodeInput := Item.Description;
        IF QRCodeInput = '' THEN
            EXIT;
        // Save a QR code image into a file in a temporary folder
        QRCodeFileName := GetQRCode(QRCodeInput);
        Myfile.Open(QRCodeFileName, TextEncoding::Windows);
        Myfile.CreateInStream(StreamIn);
        MemoryStream := MemoryStream.MemoryStream();
        COPYSTREAM(MemoryStream, StreamIn);
        ReturnDoc.ADDTEXT(Convert.ToBase64String(MemoryStream.ToArray()));
        Item.ItemQRData.CreateOutStream(Streamout, TextEncoding::Windows);
        MemoryStream.WriteTo(Streamout);
        Item.MODIFY(true);
    end;

    LOCAL Procedure GetQRCode(QRCodeInput: Text) QRCodeFileName: Text
    var
        IBarCodeProvider: DotNet BarcodeProvidersIBarcodeProvider;
    BEGIN
        GetBarCodeProvider(IBarCodeProvider);
        QRCodeFileName := IBarCodeProvider.GetBarcode(QRCodeInput);
    END;

    LOCAL Procedure GetBarCodeProvider(VAR IBarCodeProvider: DotNet BarcodeProvidersIBarcodeProvider)
    var
        QRCodeProvider: DotNet BarcodeProvidersQRCodeProvide;
    begin
        QRCodeProvider := QRCodeProvider.QRCodeProvider();
        IBarCodeProvider := QRCodeProvider;
    end;

}