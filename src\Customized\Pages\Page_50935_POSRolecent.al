page 50935 "POS Users Role Centre"
{
    Caption = 'POS Users Role Centre';
    PageType = RoleCenter;
    actions
    {
        area(Sections)
        {
            group("Group")
            {
                Caption = 'POS Access Doc & Reports';
                action("POS Login Window")
                {
                    ApplicationArea = Manufacturing;
                    Caption = 'POS Login Window';
                    RunObject = page "POS Login Window";

                }
                action("Retail Shop Sales Posting")
                {
                    ApplicationArea = Manufacturing;
                    Caption = 'Retail Shop Sales Posting';
                    RunObject = page "Retail Shop Sales Posting";
                }
                action("Sales Invoice Nos_Copy")
                {
                    ApplicationArea = Manufacturing;
                    Caption = 'Sales Invoice Nos_Copy';
                    RunObject = report "Sales Invoice Nos_Copy";
                }
            }
        }
    }
}
