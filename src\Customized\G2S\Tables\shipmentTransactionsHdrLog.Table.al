/// <summary>
/// Table MyTable (ID 50121).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
table 50064 "Shipment Transactions Hdr Log"
{
    DataClassification = ToBeClassified;

    fields
    {
        field(1; ID; Integer)
        {
            DataClassification = CustomerContent;
            AutoIncrement = true;
        }
        field(2; "Sell-to Customer No."; Code[20])
        {
            Caption = 'Sell-to Customer No.';
            DataClassification = CustomerContent;
        }
        field(3; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = CustomerContent;
        }
        field(4; CompanyName; Text[150])
        {
            DataClassification = CustomerContent;
        }

        field(5; "Veh. No."; Text[200])
        {
            DataClassification = CustomerContent;
        }

        field(6; "Sent Status"; Boolean)
        {
            DataClassification = CustomerContent;
        }

        field(7; Remark; Text[300])
        {
            DataClassification = CustomerContent;
        }

        field(21; "Shipment Date"; Date)
        {
            Caption = 'Invoice Date';//Invoice Date
            DataClassification = CustomerContent;
        }

        field(22; "Temp. Omit Rec"; Boolean)
        {
            Caption = 'Temporarily omit record from batch';
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                myInt: Integer;
            begin
                if "Sent Status" = true then
                    Error('Record has already been successfully pushed to Outreach. You can''t check this field');
            end;
        }
        field(23; "Date Sent to Outreach"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(24; "DateTime Sent to Outreach"; DateTime)
        {
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; ID)
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    begin
    end;

    trigger OnModify()
    begin

    end;

    trigger OnDelete()
    begin

    end;

    trigger OnRename()
    begin

    end;
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<