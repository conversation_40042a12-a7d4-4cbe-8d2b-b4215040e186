page 50144 "Posted Mat. Dis. Lines Subform"
{

    AutoSplitKey = true;
    DelayedInsert = true;
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = true;
    PageType = ListPart;
    SourceTable = "MDV Line";
    UsageCategory = tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("MDV Type"; "MDV Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Type; Type)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Item Variant Code"; "Item Variant Code")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("FA Posting Type"; "FA Posting Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("FA No."; "FA No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Maintenance Code"; "Maintenance Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
                field("Available Stock"; "Available Stock")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Quantity Disposed"; "Quantity Disposed")
                {
                    ApplicationArea = all;
                }
                field("Qty. Batched Not Disposed"; "Qty. Batched Not Disposed")
                {
                    ApplicationArea = all;
                }
                field("Total Item Qty Batched"; "Total Item Qty Batched")
                {
                    ApplicationArea = all;
                }
                field("Acknowledged Disposed Quantity"; "Acknowledged Disposed Quantity")
                {
                    ApplicationArea = all;
                    Caption = '<Acknowledged Disposed Quantity>';
                }
               /* field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }*/
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposed Acknowledged By"; "Disposed Acknowledged By")
                {
                    ApplicationArea = all;
                    Caption = '<Disposed Acknowledged By>';
                    Visible = false;
                }
                field("Disposed Acknowledged DateTime"; "Disposed Acknowledged DateTime")
                {
                    ApplicationArea = all;
                    Caption = '<Disposed Acknowledged DateTime>';
                    Visible = false;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
    }
}

