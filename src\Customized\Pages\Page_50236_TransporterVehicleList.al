page 50236 "Transporter Vehicle List"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Transporter Vehicle";
    Editable = FALSE;
    CardPageId = "Transporter Vehicle";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Vehicle Reg No."; "Vehicle Reg No.")
                {
                    ApplicationArea = All;
                }
                field("GIT No."; "GIT No.")
                {
                    ApplicationArea = all;
                }
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Vendor Name"; "Vendor Name")
                {
                    ApplicationArea = All;
                    Editable = false;
                }

                field("Vehicle Description"; "Vehicle Description")
                {
                    ApplicationArea = All;
                }

                field("Vehicle Tonage"; "Vehicle Tonage")
                {
                    ApplicationArea = All;
                }
                field("Approved By"; "Approved By")
                {
                    ApplicationArea = All;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = All;
                }
                field("Date Approved"; "Date Approved")
                {
                    ApplicationArea = All;
                }
                field("Date Created"; "Date Created")
                {
                    ApplicationArea = All;
                }
                field("Date Modified"; "Date Modified")
                {
                    ApplicationArea = All;
                }
                field("Expiration Date"; "Expiration Date")
                {
                    ApplicationArea = All;
                }
                field("Modify By"; "Modify By")
                {
                    ApplicationArea = All;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;
                }
                field("Validity Period"; "Validity Period")
                {
                    ApplicationArea = All;
                }
                field(Blocked; Blocked)
                {
                    ApplicationArea = all;
                }
                field("Vehicle Type"; "Vehicle Type")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = ALL;
                    Editable = FALSE;
                }
                field("Vehicle License No."; "Vehicle License No.")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    var
        usersetup: record "User Setup";
}