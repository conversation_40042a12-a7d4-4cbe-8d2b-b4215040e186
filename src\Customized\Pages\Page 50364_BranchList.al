page 50364 BranchList
{
    Caption = 'Branch Request List';
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = BranchRequest;
    CardPageId = BranchRequestCard;
    Editable = false;
    SourceTableView = where("Transfer Order No." = filter(''));//PKON1605

    layout
    {
        area(Content)
        {
            repeater(Control1102152000)
            {
                field(No; No)
                {
                    ApplicationArea = all;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = All;

                }
                field("To Location"; "To Location")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Branch Req Mail Send"; "Branch Req Mail Send")
                {
                    ApplicationArea = all;
                }
                field("Transfer Order No."; "Transfer Order No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("No. Transfer Shipments"; "No. Transfer Shipments")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("No. Transfer Receipts"; "No. Transfer Receipts")
                {
                    ApplicationArea = all;
                    Visible = false;
                }


            }
        }
    }


    trigger OnModifyRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not modify branch request. Already transfer order is created.');
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not deleted branch request. Already transfer order is created.');
    end;
}