page 50859 "Retail Customers"
{
    ApplicationArea = All;
    Caption = 'Retail Customers';
    PageType = List;
    SourceTable = Customer;
    UsageCategory = Lists;
    SourceTableView = where("Customer Posting Group" = filter('RETAILSHOP'));

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of the customer. The field is either filled automatically from a defined number series, or you enter the number manually because you have enabled manual number entry in the number-series setup.';
                }
                field(Name; Rec.Name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer''s name.';
                }
            }
        }
    }
}
