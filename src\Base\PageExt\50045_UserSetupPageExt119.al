/// <summary>
/// PageExtension UserSetupPageExt (ID 50045) extends Record User Setup.
/// </summary>
pageextension 50045 UserSetupPageExt extends "User Setup"
{
    layout
    {
        addafter("Purchase Resp. Ctr. Filter")
        {
            field("Gen. Jouranl Line Resp. Centre"; "Gen. Jouranl Line Resp. Centre")
            {
                ApplicationArea = All;
            }
            field(AllowPostingDateModify; AllowPostingDateModify)
            {
                ApplicationArea = ALL;
            }
            field("Request for Teller Receipt"; "Request for Teller Receipt")
            {
                ApplicationArea = All;
            }
            field("Edit Max Fuel Availed"; "Edit Max Fuel Availed")
            {
                ApplicationArea = all;
            }
            field("Approve Transpoter Vehicle"; "Approve Transpoter Vehicle")
            {
                ApplicationArea = all;
            }
            field("Post Multiple Rebate Cr.Memos"; "Post Multiple Rebate Cr.Memos")
            {
                ApplicationArea = all;
            }
            field("Upload Productivity Days App."; "Upload Productivity Days App.")
            {
                ApplicationArea = all;
            }
            field("Process Redistribution App."; "Process Redistribution App.")
            {
                ApplicationArea = all;
            }
            field("Reprint Dispatch"; "Reprint Dispatch")
            {
                ApplicationArea = all;
            }
            field("Reprint Branch Report"; "Reprint Branch Report")
            {
                ApplicationArea = all;
            }
            field("Teller/Cheque Awaiting Confirm"; "Teller/Cheque Awaiting Confirm")
            {
                ApplicationArea = all;
            }
            field("Teller/Cheque Awaiting BRV"; "Teller/Cheque Awaiting BRV")
            {
                ApplicationArea = all;
            }
            field("Post Bank Confirmation Reg."; "Post Bank Confirmation Reg.")
            {
                ApplicationArea = all;
            }
            field("Create Item from template"; "Create Item from template")
            {
                ApplicationArea = all;
            }
            field("Edit Master Data Template"; "Edit Master Data Template")
            {
                ApplicationArea = all;
            }
            field("Create Master Data Approval"; "Create Master Data Approval")
            {
                ApplicationArea = all;
            }
            field("Modify Master Data Approval"; "Modify Master Data Approval")
            {
                ApplicationArea = all;
            }
            field("Release Service & Rate"; "Release Service & Rate")
            {
                Description = 'Service';//Service08Jul2021
                ApplicationArea = all;
            }
            field("Reverse BRS"; "Reverse BRS")
            {
                ApplicationArea = ALL;
            }
            field("Reverse G/L Entry"; "Reverse G/L Entry")
            {
                ApplicationArea = ALL;
            }
            field("Sales Order Modify"; "Sales Order Modify")
            {
                ApplicationArea = ALL;
            }
            field("Delete Sales Order"; "Delete Sales Order")
            {
                // >>>>>>G2S 14/01/25 CAS-01388-C9P4S5
                ApplicationArea = ALL;
            }
            field("Show Item Proft & Othr Details"; "Show Item Proft & Othr Details")
            {
                ApplicationArea = ALL;
                //b2bpksalecorr10                
            }
            field("Short Close Trans. Order"; "Short Close Trans. Order")
            {
                ApplicationArea = ALL;
            }
            field("Edit PMS Entries"; "Edit PMS Entries")
            {
                ApplicationArea = ALL;
            }
            field(CHIERP_AllowValidPeriodChange; CHIERP_AllowValidPeriodChange)
            {
                ApplicationArea = ALL;
                ToolTip = 'Specifies access change to Master Valid period';
            }

            field(CHI_ERP_ViewCommercialDetails; Rec.CHI_ERP_ViewCommercialDetails)
            {
                ApplicationArea = All;
                ToolTip = 'Will view commercial details on PO Line';
            }
        }
        addafter("User ID")
        {
            field(State; State)
            {
                ApplicationArea = all;
            }
            field("Employee ID"; "Employee ID")
            {
                ApplicationArea = all;
            }
            field("Employee Acc. Loc"; "Employee Acc. Loc")
            {
                ApplicationArea = all;
            }
            field("Employee CC. Code"; "Employee CC. Code")
            {
                ApplicationArea = all;
            }

            field("Validate Vendor Classification"; "Validate Vendor Classification")
            {
                ApplicationArea = all;
            }
            field("Suppl Chain Approval Rights"; "Suppl Chain Approval Rights")
            {
                ApplicationArea = all;
            }
            field("Revise Capex Budget"; "Revise Capex Budget")
            {
                ApplicationArea = ALL;
            }
            field("QC Checked (BAD Location)"; "QC Checked (BAD Location)")
            {
                ApplicationArea = all;
            }
            field("SCD Checked"; "SCD Checked")
            {
                ApplicationArea = all;
            }
            field("CCD Checked"; "CCD Checked")
            {
                ApplicationArea = all;
            }
            field("Short Close Checked"; "Short Close Checked")
            {
                ApplicationArea = all;
            }

            field("Voucher Resp. Ctr. Filter"; "Voucher Resp. Ctr. Filter")
            {
                ApplicationArea = all;
            }
            field("FA Req. Amt. Appr. Limit"; "FA Req. Amt. Appr. Limit")
            {
                ApplicationArea = all;
            }
            field("Unlimited FA Req. Approval"; "Unlimited FA Req. Approval")
            {
                ApplicationArea = all;
            }
            field("MRS-Sample Resp. Ctr. Filter"; "MRS-Sample Resp. Ctr. Filter")
            {
                ApplicationArea = all;
            }
            field("FA Req. Resp. Ctr. Filter"; "FA Req. Resp. Ctr. Filter")
            {
                ApplicationArea = all;
            }
            field("Service Request Resp. Ctr. Ftr"; "Service Request Resp. Ctr. Ftr")
            {
                ApplicationArea = all;
            }
            field("MDV Resp. Ctr. Filter"; "MDV Resp. Ctr. Filter")
            {
                ApplicationArea = all;
            }
            field("WrkOrdReq Resp. Ctr. Filter"; "WrkOrdReq Resp. Ctr. Filter")
            {
                ApplicationArea = all;
            }
            field("Prj. Req. Resp. Ctr. Filter"; "Prj. Req. Resp. Ctr. Filter")
            {
                ApplicationArea = all;
            }
        }
        addbefore("Allow Posting From")
        {
            field("Create Rebate Cred. Memos"; "Create Rebate Cred. Memos")
            {
                ApplicationArea = all;
            }
            field("Release Prod. Planning"; "Release Prod. Planning")
            {
                ApplicationArea = all;
            }
            field("Release Prod. Capture"; "Release Prod. Capture")
            {
                ApplicationArea = all;
            }
            field("Create Prod. order"; "Create Prod. order")
            {
                ApplicationArea = all;
            }
            field("Release Stock Capture"; "Release Stock Capture")

            {
                ApplicationArea = all;
            }
            //Project Leap >>>>>
            field("Close Production MRS"; "Can Close Production MRS")
            {
                ApplicationArea = all;
            }
            field("Reopen Closed Prod. MRS"; "Can Reopen Closed Prod. MRS")
            {
                ApplicationArea = all;
            }
            //Project Leap <<<<<
            field("Store Type"; "Store Type")
            {
                ApplicationArea = all;
            }
            field("Get Prod. MRS Notification"; "Get Prod. MRS Notification")
            {
                ApplicationArea = all;
            }
            field("Enter Excess PCR Count"; "Enter Excess PCR Count")
            {
                ApplicationArea = all;
            }
            field("Approve Fa Mvt Reg"; "Approve Fa Mvt Reg")
            {
                ApplicationArea = all;
            }
            field("FA Req. To BUH Approval"; "FA Req. To BUH Approval")
            {
                ApplicationArea = all;
            }
            field("Cashier Permission"; "Cashier Permission")
            {
                ApplicationArea = all;
            }
            field("Edit Chaeque/Teller No."; "Edit Chaeque/Teller No.")
            {
                ApplicationArea = all;
            }
            field("View Main Cash Vouchers"; "View Main Cash Vouchers")
            {
                ApplicationArea = all;
            }
            field("View Petty Cash Vouchers"; "View Petty Cash Vouchers")
            {
                ApplicationArea = all;
            }
            field("View Direct Bank Receipts"; "View Direct Bank Receipts")
            {
                ApplicationArea = all;
            }
            field("View Indirect Bank Receipts"; "View Indirect Bank Receipts")
            {
                ApplicationArea = all;
            }
            field("Reprint Payment Documents"; "Reprint Payment Documents")
            {
                ApplicationArea = all;
            }
            field("Reprint Receipt Documents"; "Reprint Receipt Documents")
            {
                ApplicationArea = all;
            }
            field("CCD Mail Alert"; "CCD Mail Alert")
            {
                ApplicationArea = all;
            }
            field("Resend Branch Request"; "Resend Branch Request")
            {
                ApplicationArea = all;
            }
            field("Undo Shipment"; "Undo Shipment")
            {
                ApplicationArea = all;
            }
            field("Undo Receipt"; "Undo Receipt")
            {
                ApplicationArea = all;
            }
            field("Ack. Residence Diesel"; "Ack. Residence Diesel")
            {
                ApplicationArea = all;
            }
            field("Mark-Scrap App"; "Mark-Scrap App")
            {
                ApplicationArea = all;
            }
            field("Direct Posting"; "Direct Posting")
            {
                ApplicationArea = all;
            }
            field("Ammend Br Send"; "Ammend Br Send")
            {
                ApplicationArea = all;
            }
            field("Allow Inv. Posting From"; "Allow Inv. Posting From")
            {
                ApplicationArea = all;
            }
            field("Allow Inv. Posting To"; "Allow Inv. Posting To")
            {
                ApplicationArea = all;
            }
            field("Reprint Invoiced & Credm Docs"; "Reprint Invoiced & Credm Docs")
            {
                ApplicationArea = all;
            }
            field("Reprint Shipmt & GatePass Docs"; "Reprint Shipmt & GatePass Docs")
            {
                ApplicationArea = all;
            }
            field("Send Customer History Mail"; "Send Customer History Mail")
            {
                ApplicationArea = all;
            }
            field("Process KD"; "Process KD")
            {
                ApplicationArea = all;
            }
            field("Delete RBS Right side data"; "Delete RBS Right side data")
            {
                ApplicationArea = ALL;
            }
            field("Reopen Receipt Page"; "Reopen Receipt Page")
            {
                ApplicationArea = all;
            }
            field("Create BRS Out. Dupl Entrie"; "Create BRS Out. Dupl Entrie")
            {
                ApplicationArea = all;
            }
            field("Prod. MRS Mail Alert"; "Prod. MRS Mail Alert")
            {
                ApplicationArea = all;
            }
            field("Capt. Prod Qty. Mail Alert"; "Capt. Prod Qty. Mail Alert")
            {
                ApplicationArea = all;
            }
            field(Password; Password)
            {
                ApplicationArea = all;
            }
            field("Retail User"; "Retail User")
            {
                ApplicationArea = all;
            }
            field(Location; Location)
            {
                ApplicationArea = all;
            }
            field("MIS Approval"; "MIS Approval")
            {
                ApplicationArea = all;
            }
            //Balu 05132021>>
            field("Reprint Res. Gpass"; "Reprint Res. Gpass")
            {
                ApplicationArea = all;
            }
            field("Material Loading Reprint"; "Material Loading Reprint")
            {
                ApplicationArea = all;
            }
            //Balu 05132021<<
            field("Delete No. Series"; "Delete No. Series")
            {
                ApplicationArea = all;
            }
            field("Open Sales Invoice Page"; "Open Sales Invoice Page")
            {
                ApplicationArea = all;
            }
            field("ReOpen App Sales order"; "ReOpen App Sales order")
            {
                ApplicationArea = all;
            }
            field("Open Sales Order Page"; "Open Sales Order Page")
            {
                ApplicationArea = all;
            }
            field("open Sales Quote Page"; "open Sales Quote Page")
            {
                ApplicationArea = all;
            }
            field("Open Purchase Order Page"; "Open Purchase Order Page")
            {
                ApplicationArea = all;
            }
            field("Open Purchase Invoice Page"; "Open Purchase Invoice Page")
            {
                ApplicationArea = all;
            }
            field("Open Purchase Quote page"; "Open Purchase Quote page")
            {
                ApplicationArea = all;
            }
            field("Send E-mail to Cust Statement"; "Send E-mail to Cust Statement")
            {
                ApplicationArea = all;
            }
            // >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9
            field("CHIERP_Complimentary Sales"; "CHIERP_Complimentary Sales")
            {
                ApplicationArea = all;
            }
            //Baluon Apr 18 2022>>
            field("Created By"; "Created By")
            {
                ApplicationArea = All;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = All;
            }
            field("Modified By"; "Modified By")
            {
                ApplicationArea = All;
            }
            field("Modified date"; "Modified date")
            {
                ApplicationArea = All;
            }
            //Baluon Apr 18 2022<<
            //Go2solve March 28 2023 >>
            field(SVAlert; SVAlert)
            {
                ApplicationArea = All;
            }
            //Go2solve March 28 2023 <<
            field("Staff ID in StoreApp"; "Staff ID in StoreApp")
            {
                ApplicationArea = All;
            }

        }
        addafter(Email)
        {
            //<<<<<< G2S Aug 01 2023
            field("Can rec. Outreach Job Notif?"; "Can rec. Outreach Job Notif?")
            {
                ApplicationArea = All;
            }
            field("Can Modify Prod. Order"; "Can Modify Prod. Order")
            {
                ApplicationArea = All;
            }
            //>>>>>> G2S Aug 01 2023
            //<<<<<< G2S CAS-01312-L5Q5B8 6/21/2024
            field("Delete Production Order"; Rec."Delete Production Order")
            {
                Caption = 'Delete Production/Scratchpad';
                ApplicationArea = All;
            }
            //>>>>>> G2S CAS-01312-L5Q5B8 6/21/2024
            // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024
            field("Can Access Local Sales Doc"; "Can Access Local Sales Doc")
            {
                Caption = 'Can Not Access Local Sales Doc';
                ApplicationArea = All;
            }
            field("Can Access Direct Sales Doc"; "Can Access Direct Sales Doc")
            {
                Caption = 'Can Not Access Direct Sales Doc';
                ApplicationArea = All;
            }
            field("Can Access Export Sales Doc"; "Can Access Export Sales Doc")
            {
                Caption = 'Can Not Access Export Sales Doc';
                ApplicationArea = All;
            }
            // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024
            //<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
            field("Modify Purchase Document"; "Modify Purchase Document")
            {
                ApplicationArea = all;
            }
            //>>>>>> G2S CAS-01334-J2M7C2 8/30/2024
            // <<<<<< G2S 11/7/2024
            field("Can Use Transfer Ticket"; "Can Use Transfer Ticket")
            {
                ApplicationArea = all;
            }
            // >>>>>> G2S 11/7/2024
            // >>>>>> G2S 13/12/2024  CAS-01381-Y2Z6B9
            field("User Created Date"; "User Created Date")
            {
                ApplicationArea = all;
            }
            field("User Last Date Modified"; "User Last Date Modified")
            {
                ApplicationArea = all;
            }
            // >>>>>> G2S 13/12/2024  CAS-01381-Y2Z6B9
        }
    }
}