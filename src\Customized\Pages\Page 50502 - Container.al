page 50502 Container
{
    // version CHI6.0

    // LS = changes made by LS Retail
    // 
    // PROJECT : CHI 6.0
    // ************************************************************************************************************************************
    // SIGN
    // ************************************************************************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // ************************************************************************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // ************************************************************************************************************************************
    // 1.0       HO       29-02-12     -> Form created for Container details for Import management.

    DelayedInsert = true;
    PageType = ListPart;
    SourceTable = Container;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Container No."; "Container No.")
                {
                }
                field("Container Type"; "Container Type")
                {
                }
                field("Container Seal Open Date"; "Container Seal Open Date")
                {
                }
                field("Container Seal No."; "Container Seal No.")
                {
                }
                field("Location Code"; "Location Code")
                {
                }
                field("Container Receipt Date at WH"; "Container Receipt Date at WH")
                {
                }
                field("Container Return Date"; "Container Return Date")
                {
                }
                field(Status; Status)
                {
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group(Functions)
            {
                Caption = 'Functions';
                action("Close Container")
                {
                    Caption = 'Close Container';

                    trigger OnAction();
                    begin
                        if Status = Status::Open then
                            VALIDATE(Status, Status::Closed);
                    end;
                }
                action("Reopen Container")
                {
                    Caption = 'Reopen Container';

                    trigger OnAction();
                    begin
                        if Status = Status::Closed then
                            Status := Status::Open;
                    end;
                }
            }
        }
    }

    trigger OnModifyRecord(): Boolean;
    begin
        TESTFIELD(Status, Status::Open);
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        TESTFIELD(Status, Status::Open);
    end;
}

