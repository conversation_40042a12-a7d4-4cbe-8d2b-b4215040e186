pageextension 50013 PaymentJournal296 extends "Payment Journal"
{
    layout
    {
        addbefore("Bal. Account No.")
        {

            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = All;
            }
            field("WHT Group"; "WHT Group")
            {
                ApplicationArea = all;
                trigger OnValidate()
                begin
                    CalculateWHT();
                end;

            }
            field("WHT %"; "WHT %")
            {
                ApplicationArea = all;
            }
            field("WHT Amount"; "WHT Amount")
            {
                ApplicationArea = all;
            }
            field("WHT Amount(LCY)"; "WHT Amount(LCY)")
            {
                ApplicationArea = all;
            }
            field("WHT Account"; "WHT Account")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Cheque No."; "Cheque No.")
            {
                ApplicationArea = all;
            }
            field("Cheque Date"; "Cheque Date")
            {
                ApplicationArea = all;
            }
            field(Narration; Narration)
            {
                ApplicationArea = all;
            }
            field(Narration1; Narration1)
            {
                ApplicationArea = all;
            }
        }
        moveafter("Bal. Account Type"; "Bal. Account No.")

    }

    actions
    {
        modify(Post)
        {
            Visible = false;
        }
        modify(Preview)
        {
            Visible = false;
        }
        modify("Post and &Print")
        {
            Visible = false;
        }
        addafter(Post)
        {
            action(Post2)
            {
                ApplicationArea = all;
                Caption = 'Post';
                Image = Post;
                trigger OnAction()
                begin
                    CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post2", Rec);
                    CurrPage.UPDATE(FALSE);
                end;
            }
            action("Preview Posting")
            {
                ApplicationArea = all;
                Caption = 'Preview Posting';
                Image = ViewPostedOrder;
                trigger OnAction()
                var
                    GenJnlPost: Codeunit "Gen. Jnl.-Post2";
                begin
                    GenJnlPost.Preview(Rec);
                end;
            }

        }
        addafter("F&unctions")
        {
            action("Calculate WHT")
            {
                ApplicationArea = all;
                Caption = 'Calculate WHT';
                Image = Calculate;
                trigger OnAction()
                begin
                    CalculateWHT();
                end;
            }
        }

    }
    /*procedure CalculateWHT()
    var
        whtgrp: record WHTSetUp;
    begin
        TestField("Document Type", "Document Type"::Payment);
        TestField("Account Type", "Account Type"::Vendor);
        if not ("Bal. Account Type" = "Bal. Account Type"::"Bank Account") AND not ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") then
            Error('Balnace Acoount Type should be Either GLAccount or BankAccount ');
        TestField("Bal. Account No.");
        TestField(Amount);
        TestField("WHT Group");
        TestField("Account No.");
        IF whtgrp.get("WHT Group") THEN begin
            whtgrp.TestField(Percentage);
            "WHT %" := whtgrp.Percentage;
            IF "Account Type" = "Account Type"::Customer then BEGIN
                whtgrp.TestField("Receivable Account No.");
                "WHT Account" := whtgrp."Receivable Account No."
            END else
                IF "Account Type" = "Account Type"::Vendor then BEGIN
                    whtgrp.TestField("Payable Account No");
                    "WHT Account" := whtgrp."Payable Account No";
                END;
            "WHT Amount" := Round((Amount * ("WHT %" / 100)), 1);
            if "Currency Factor" <> 0 then
                "WHT Amount(LCY)" := Round(("Currency Factor" * (Amount * ("WHT %" / 100))), 1)
            else
                "WHT Amount(LCY)" := Round((Amount * ("WHT %" / 100)), 1);
        end else Begin
            CLEAR("WHT %");
            clear("WHT Account");
            CLEAR("WHT Amount");
            clear("WHT Amount(LCY)");

        end;

    end;*/

    var
        myInt: Integer;

}