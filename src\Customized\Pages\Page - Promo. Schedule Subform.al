page 50120 "Promo. Schedule Subform"
{
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = ListPart;
    SourceTable = "Promo Schedule Line";
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field(Type; Type)
                {
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field(Active; Active)
                {
                    ApplicationArea = all;
                }
                field("Retail Promo"; "Retail Promo")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Promotion Offers")
                {
                    ApplicationArea = all;
                    Caption = 'Promotion Offers';

                    trigger OnAction();
                    begin
                        ShowOfferLines;

                    end;
                }

                action("Promotion Offer Variants")
                {
                    ApplicationArea = all;
                    Caption = 'Promotion Offer Variants';
                    trigger OnAction();
                    begin
                        ShowOfferVariantLines;
                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::Promo;
    end;

    trigger OnModifyRecord(): Boolean
    VAR
        PromSched: Record "Promo Schedule";
    BEGIN
        PromSched.RESET;
        PromSched.SetRange("No.", "Document No.");
        IF PromSched.findfirst then
            PromSched.TestField(Status, PromSched.Status::Open);
    END;

    var
        ChangeExchangeRate: Page "Change Exchange Rate";

    procedure ShowOfferLines();
    begin
        ShowPromoOfferLines;
    end;

    procedure _ShowDimensions();
    begin
        ShowDimensions;
    end;

    procedure ShowOfferVariantLines();
    begin
        ShowPromoOfferVariants;
    end;
}

