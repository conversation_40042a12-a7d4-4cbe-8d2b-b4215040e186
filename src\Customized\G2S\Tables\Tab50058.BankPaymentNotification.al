//CR: RFC#39  
//Signed: 31st Oct., 2023
//Name: Go2Solve Nig. Ltd
//Published: 10th Nov., 2023

/// <summary>
/// Table Bank Payment Notification (ID 50055).
/// </summary>
//RFCBankAPIGo2solveJuly2023>>>>>>
table 50058 "Bank Payment Notification"
{
    //Permissions = tabledata "Bank Payment Notification" = RI;
    DataClassification = ToBeClassified;

    fields
    {
        //field(1; "ID"; Code[20])
        field(1; "ID"; Integer)
        {
            DataClassification = ToBeClassified;
            AutoIncrement = true;
        }
        field(2; "Session ID"; Code[100])
        {
            DataClassification = ToBeClassified;
        }
        field(3; "SourceBankCode"; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        field(4; "Customer ID"; Code[30])
        {
            DataClassification = ToBeClassified;
        }
        field(5; "Customer Name"; Code[100])
        {
            DataClassification = ToBeClassified;
        }
        field(6; "Payment Reference"; Code[150])
        {
            DataClassification = ToBeClassified;
        }

        field(7; "Narration"; Code[100])
        {
            DataClassification = ToBeClassified;
        }

        field(8; "Date Entered"; DateTime)
        {
            DataClassification = ToBeClassified;
        }
        field(9; "Date Posted"; DateTime)
        {
            DataClassification = ToBeClassified;
        }
        field(10; Remarks; Code[100])
        {
            DataClassification = ToBeClassified;
        }
        field(11; Processed; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(12; "No. Series"; Code[10])
        {
            DataClassification = ToBeClassified;
        }
        field(13; Amount; Decimal)
        {
            DataClassification = ToBeClassified;
        }
        field(14; "Date Created"; Date)
        {
            DataClassification = ToBeClassified;
        }
        field(15; "DateTime Created"; DateTime)
        {
            DataClassification = ToBeClassified;
        }
        field(16; "Document No."; Code[50])
        {
            DataClassification = ToBeClassified;
        }
        field(17; "Temp. Omit record from Batch"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(18; "Duplicate Session ID?"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
    }
    keys
    {
        key(PK; "ID")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        myInt: Integer;
        GLSetup: Record "General Ledger Setup";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        BankPymentRec: Record "Bank Payment Notification";

    begin
        //BankPymentRec.Copy(Rec);
        // IF "ID" = '' THEN BEGIN
        //     GLSetup.GET;
        //     GLSetup.TESTFIELD("Notification Nos.");
        //     NoSeriesMgt.InitSeries(GLSetup."Notification Nos.", xRec."No. Series", 0D, "ID", "No. Series");
        // END;

        "Date Created" := today;
        "DateTime Created" := CurrentDateTime;

        // BankPayment.Reset();
        // BankPayment.SetRange("Session ID", Rec."Session ID");
        // BankPayment.SetRange(SourceBankCode, Rec.SourceBankCode);
        // BankPayment.SetFilter(ID, '<>%1', Rec.ID);
        // if BankPayment.FindFirst() then begin
        //     BankPayment."Duplicate Session ID?" := true;
        //     BankPayment."Temp. Omit record from Batch" := true;
        //     BankPayment.Modify();
        // end;
    end;

    var
        BankPayment: Record "Bank Payment Notification";
}
//RFCBankAPIGo2solveJuly2023>>>>>>

