page 50768 "Approved Purchase Modifiers"
{
    ApplicationArea = All;
    Caption = 'Approved Purchase Modifiers';
    SourceTable = "Purchase Doc Modifier";
    CardPageId = "Purchase Doc Modifier";
    SourceTableView = where(Status = filter(Released));
    PageType = List;
    UsageCategory = Lists;
    Editable = false;
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the No. field.', Comment = '%';
                }
                field("Record Type"; Rec."Record Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Record Type field.', Comment = '%';
                }
                field("Document No."; Rec."Document No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Document No. field.', Comment = '%';
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Vendor No. field.', Comment = '%';
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Status field.', Comment = '%';
                }
                field("Date Modified"; Rec."Date Modified")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Modified field.', Comment = '%';
                }
            }
        }
    }

    // trigger OnOpenPage()
    // begin
    //     // GetLastMonday();
    //     GetReportFilter();
    // end;

    // procedure GetReportFilter()
    // var
    //     TdTxt: Text;
    //     i, TDInt, TDInt2, Day : Integer;
    //     FirstWeekDays: List of [Integer];
    //     DateVar: Date;
    //     SaleType: option Volume,Revenue;
    //     isWithinFirstWeek: Boolean;
    // begin
    //     // Check today if it's within the first week of the current accounting period;
    //     Evaluate(TDInt, Format(Today).Split('/').Get(1));
    //     DateVar := GetCurrentAccoutingPeriod();
    //     Evaluate(Day, Format(DateVar).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);
    //     Evaluate(Day, Format(DateVar + 1).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);
    //     Evaluate(Day, Format(DateVar + 2).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);
    //     Evaluate(Day, Format(DateVar + 3).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);
    //     Evaluate(Day, Format(DateVar + 4).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);
    //     Evaluate(Day, Format(DateVar + 5).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);
    //     Evaluate(Day, Format(DateVar + 6).Split('/').Get(1));
    //     FirstWeekDays.Add(Day);

    //     for i := 1 to FirstWeekDays.Count do begin
    //         if TDInt = FirstWeekDays.Get(i) then isWithinFirstWeek := true;
    //         if isWithinFirstWeek then break;
    //     end;

    //     EndDate := Today - 1;

    //     if not isWithinFirstWeek then
    //         BeginDate := GetCurrentAccoutingPeriod()
    //     else
    //         BeginDate := GetLastMonday();

    //     "Sales Volume Transaction".SetParameters(BeginDate, EndDate, SaleType::Volume);
    //     Report.Run(Report::"Sales Volume Transaction_8", false, false, integerRec);
    // end;

    // procedure GetCurrentAccoutingPeriod(): Date
    // var
    //     MonthInt, AcctPrdMonthInt : Integer;
    // begin
    //     // Return First date of the current account period;
    //     TxtDateVar := '1/1/' + Format(Today).Split('/').Get(3);
    //     Evaluate(BeginYearDateVar, TxtDateVar);
    //     TxtDateVar2 := Format(Today).Split('/').Get(2);
    //     Evaluate(MonthInt, TxtDateVar2);
    //     AccountPeriod.SetRange("Starting Date", BeginYearDateVar, Today);
    //     if AccountPeriod.FindSet() then
    //         repeat
    //             Evaluate(AcctPrdMonthInt, Format(AccountPeriod."Starting Date").Split('/').Get(2));
    //             // only January = 1 else Month - 1 
    //             if MonthInt <> 1 then
    //                 if AcctPrdMonthInt = (MonthInt - 1) then
    //                     exit(AccountPeriod."Starting Date") else
    //                     if MonthInt = 1 then
    //                         if AcctPrdMonthInt = MonthInt then
    //                             exit(AccountPeriod."Starting Date");

    //         until AccountPeriod.Next() = 0;
    // end;

    // procedure GetLastMonday(): Date
    // var
    //     DateOne, CurrAcctPeriod, PrevAcctPeriod : Date;
    //     DateOneTxt: Text;
    //     MonthInt: Integer;
    //     prevDateGot: Boolean;
    // begin
    //     // Return last monday date of previous accounting period
    //     CurrAcctPeriod := GetCurrentAccoutingPeriod();

    //     TxtDateVar := '1/1/' + Format(Today).Split('/').Get(3);
    //     Evaluate(BeginYearDateVar, TxtDateVar);
    //     TxtDateVar2 := Format(Today).Split('/').Get(2);
    //     Evaluate(MonthInt, TxtDateVar2);

    //     AccountPeriod.SetRange("Starting Date", BeginYearDateVar, Today);
    //     if AccountPeriod.FindSet() then
    //         repeat
    //             if AccountPeriod."Starting Date" = CurrAcctPeriod then begin
    //                 if AccountPeriod.Next(-1) <> 0 then PrevAcctPeriod := AccountPeriod."Starting Date";
    //                 prevDateGot := true;
    //                 // Message('Prev Date :%1 , Curr Date :%2', PrevAcctPeriod, CurrAcctPeriod);
    //             end;
    //         until (AccountPeriod.Next() = 0) or prevDateGot;

    //     // Filter for all Mondays within the last month and get the last monday
    //     dateWeek.SetCurrentKey("Period Start");
    //     dateWeek.SetRange("Period Start", PrevAcctPeriod, CurrAcctPeriod);
    //     if dateWeek.FindSet() then begin
    //         dateWeek.SetFilter("Period Name", '=%1', 'Monday');
    //         if dateWeek.FindLast() then begin
    //             exit(dateWeek."Period Start");
    //             // Message('Last Monday Date: %1', dateWeek."Period Start");
    //         end;
    //     end;
    // end;

    // var
    //     integerRec: Record Integer;
    //     dateWeek: Record **********;
    //     DateVar, BeginYearDateVar, BeginMonthDateVar, BeginDate, EndDate : Date;
    //     TxtDateVar, TxtDateVar2 : Text;
    //     "Sales Volume Transaction": Report "Sales Volume Transaction_8";
    //     AccountPeriod: Record "Accounting Period";
}
