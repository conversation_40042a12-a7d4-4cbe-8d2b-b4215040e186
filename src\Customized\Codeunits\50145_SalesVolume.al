codeunit 50145 "Sales Volume"
{
    trigger OnRun()
    var
        R1: Report 50497;
        R2: Report 50499;
    BEGIN
        //Excel1;
        /*CreateAndFillExcelBuffer();
        Commit();
        Message('Generated R1');
        CreateAndFillExcelBuffer1();
        commit;
        Message('Generated R2');
        //Excel2;
        //commit;

        // ConfigPackageTable.SetRange("Package Code", Code);
        //if ConfirmManagement.GetResponseOrDefault(StrSubstNo(Text004, Code), true) then
        //ConfigExcelExchange.ExportExcelFromTables(ConfigPackageTable);
        Report.Run(50497, false, false, Integer1);
        Report.Run(50499, false, false, Integer2);*/
        R1.Run();
        R2.Run();
    END;

    Procedure Excel1()
    BEGIN
        //ExcelBuffer.deleteall;
        ExcelBuffer.NewRow();
        ExcelBuffer.AddColumn('Source Product Code', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Source Product Desc', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('ISSCOM BPP Code', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Promotional Indicator', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('UPC Code (GTIN)', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 1', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);

        ExcelBuffer.AddColumn('Attribute 2', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);

        ExcelBuffer.AddColumn('Attribute 3', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 4', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 5', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 6', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 7', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 8', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 9', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 10', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 11', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 12', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 13', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 14', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('Attribute 15', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.AddColumn('MP Indicator', false, '', true, false, false, '', ExcelBuffer."Cell Type"::Text);
        ExcelBuffer.NewRow();

        ItemGRec.RESET;
        ItemGRec.SetFilter("Item Category Code", '=%1', 'FG');
        ItemGRec.SetRange("Approval Status", ItemGRec."Approval Status"::Released);
        ItemGRec.SetFilter("No.", '<>%1&<>%2', 'HFG*', 'SCRP*');
        ItemGRec.SetFilter("KO BPP Code", '<>%1', '');
        If ItemGRec.findset then
            repeat
                ExcelBuffer.NewRow();
                ExcelBuffer.AddColumn(ItemGRec."No.", false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn(ItemGRec.Description, false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn(ItemGRec."KO BPP Code", false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);

                ExcelBuffer.AddColumn('N', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn(ItemGRec."Inventory Posting Group", false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);

                ExcelBuffer.AddColumn(ItemGRec."Gen. Prod. Posting Group", false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
                ExcelBuffer.AddColumn('N', false, '', false, false, false, '', ExcelBuffer."Cell Type"::Text);
            until ItemGRec.next = 0;
        //ExcelBuffer.CreateBookAndOpenExcel('', 'Item', 'Report', COMPANYNAME, USERID);
        // ExcelBuffer.OnlyCreateBook('Item', '', COMPANYNAME, USERID, FALSE);
        //Commit();
    end;

    procedure Excel2()
    BEGIN
        ExcelBuffer10.deleteall;
        ExcelBuffer10.NewRow();
        ExcelBuffer10.AddColumn('Day Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Source Ship From Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Source Ship To Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Source Product Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Source Channel Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Source Sales Type Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);

        ExcelBuffer10.AddColumn('Vending Machine Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);

        ExcelBuffer10.AddColumn('Vending Machine Location Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Record Type', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Operation Route Type', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Invoice Number', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        ExcelBuffer10.AddColumn('Transaction Type', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);

        IF SaleType = SaleType::Volume THEN BEGIN
            ExcelBuffer10.AddColumn('Sales Unit Type Flag', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Quantity', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Unit Cases', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Send Datetimestamp', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Coverage Count Flag', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        end else BEGIN
            ExcelBuffer10.AddColumn('Currency Code', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Bottler Gross Revenue', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Bottler Net Sales Revenue', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Bottler Wholesale Price', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Send Datetimestamp', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
            ExcelBuffer10.AddColumn('Coverage Count Flag', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
        end;

        ExcelBuffer10.NewRow();

        ItemLdeEntrt.RESET;
        ItemLdeEntrt.SetRange("Entry Type", ItemLdeEntrt."Entry Type"::Sale);
        If ItemLdeEntrt.findset then
            repeat
                //Data
                IF (SelectOption = SelectOption::salesReport) AND (Itemrec.GET(ItemLdeEntrt."Item No.")) AND (Itemrec."KO BPP Code" <> '') THEN BEGIN
                    shiptocode := '-2';

                    CLEAR(Qty);
                    CLEAR(Quantity2);
                    CLEAR(SalesGross);
                    CLEAR(SalesGrossval);
                    CLEAR(SalesNet);
                    CLEAR(Wholesaleprice);

                    //Gross
                    IF SaleType = SaleType::Revenue THEN BEGIN
                        ILE.RESET;
                        ILE.SETCURRENTKEY("Posting Date", "Entry Type", "Document Type", "Source No.", "Item No.");
                        ILE.SETRANGE("Posting Date", ItemLdeEntrt."Posting Date");
                        ILE.SETRANGE("Entry Type", ILE."Entry Type"::Sale);
                        ILE.SETRANGE("Document Type", ILE."Document Type"::"Sales Shipment");
                        ILE.SETRANGE("Source No.", ItemLdeEntrt."Source No.");
                        ILE.SETRANGE("Item No.", ItemLdeEntrt."Item No.");
                        IF ILE.FINDSET THEN
                            REPEAT
                                ILE.CALCFIELDS("Sales Amount (Actual)");
                                SalesGrossval += ILE."Sales Amount (Actual)";
                            UNTIL ILE.NEXT = 0;
                    END;

                    Quantity2 := ItemLdeEntrt.Quantity * -1;
                    Qty := FORMAT(ItemLdeEntrt.Quantity * -1, 0, 1);
                    IF SaleType = SaleType::Revenue THEN BEGIN
                        SalesNet := FORMAT(ItemLdeEntrt."Sales Amount (Actual)", 0, 1);
                        SalesGross := FORMAT(SalesGrossval, 0, 1);

                        IF Itemrec2.GET(ItemLdeEntrt."Item No.") THEN
                            Wholesaleprice := FORMAT(ROUND((Itemrec2."Unit Price" * 1.05), 0.01, '>'), 0, 1)
                        ELSE
                            Wholesaleprice := '';
                    END;

                    IF STRPOS(ItemLdeEntrt."Source No.", ch) > 0 THEN
                        Custno := COPYSTR(ItemLdeEntrt."Source No.", 1, (STRPOS(ItemLdeEntrt."Source No.", ch) - 1))
                    ELSE
                        Custno := ItemLdeEntrt."Source No.";

                    IF STRPOS(ItemLdeEntrt."Document No.", ch) > 0 THEN
                        Invno := COPYSTR(ItemLdeEntrt."Document No.", 1, (STRPOS(ItemLdeEntrt."Document No.", ch) - 1))
                    ELSE
                        Invno := ItemLdeEntrt."Document No.";


                    //Data
                    ExcelBuffer10.NewRow();
                    ExcelBuffer10.AddColumn(FORMAT(ItemLdeEntrt."Posting Date", 0, '<Year4><Month,2><Day,2>'), false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn(ItemLdeEntrt."Global Dimension 1 Code", false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn(Custno, false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);

                    ExcelBuffer10.AddColumn(ItemLdeEntrt."Item No.", false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn('-2', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);

                    ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    ExcelBuffer10.AddColumn('1', false, '', true, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    IF SaleType = SaleType::Volume THEN BEGIN
                        ExcelBuffer10.AddColumn('1', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn(Qty, false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    end else BEGIN
                        ExcelBuffer10.AddColumn('NG', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn(SalesGross, false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn(SalesNet, false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn(Wholesaleprice, false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                        ExcelBuffer10.AddColumn('', false, '', false, false, false, '', ExcelBuffer10."Cell Type"::Text);
                    end;
                end;
            until ItemLdeEntrt.next = 0;
        //ExcelBuffer10.CreateBookAndOpenExcel('', 'ItemLedgerEntry', 'Report', COMPANYNAME, USERID);
        //ExcelBuffer10.CreateNewBook('ItemLedgerEntry');
        //ExcelBuffer10.OnlyCreateBook('ILE', '', COMPANYNAME, USERID, TRUE);
    END;


    local procedure CreateAndFillExcelBuffer()
    begin
        ExcelBuffer.CreateNewBook('Summary');
        Excel1;
        ExcelBuffer.WriteSheet('report', CompanyName(), UserId());
        ExcelBuffer.CloseBook;
        ExcelBuffer.OpenExcel;
        //ExcelBuffer.OpenBook('item', 'Summary');
    end;


    local procedure CreateAndFillExcelBuffer1()
    begin
        ExcelBuffer10.CreateNewBook('Summary1');
        Excel2;
        ExcelBuffer10.WriteSheet('report1', CompanyName(), UserId());
        ExcelBuffer10.CloseBook;
        ExcelBuffer10.OpenExcel;
        //ExcelBuffer.OpenBook('item', 'Summary');
    end;








    var


        ConfigPackageTable: Record "Config. Package Table";
        ConfigExcelExchange: Codeunit "Config. Excel Exchange";
        ConfirmManagement: Codeunit "Confirm Management";

        //ExcelBuffer: Record "Excel Buffer" temporary;
        ExcelBuffer1: Record "Excel Buffer" temporary;
        ExcelBuffer2: Record "Excel Buffer" temporary;
        ExcelBuffer3: Record "Excel Buffer" temporary;
        ExcelBuffer4: Record "Excel Buffer" temporary;
        ExcelBuffer5: Record "Excel Buffer" temporary;
        ExcelBuffer6: Record "Excel Buffer" temporary;
        RowNo: Integer;
        ItemGRec: record item;
        CustGRec: Record customer;
        ItemLdeEntrt: Record "Item Ledger Entry";
        SelectOption: Option customerReport,channelReport,shiptoReport,shipfromReport,salestypeReport,salesReport,productReport;
        SaleType: option Volume,Revenue;
        Invno: code[20];
        Quantity2: Decimal;
        //Qty: Decimal;
        Wholesaleprice: text[100];
        ILE: record "Item Ledger Entry";
        Itemrec: Record item;
        Itemrec2: record item;
        Custno: code[20];
        SalesNet: text[100];
        SalesGross: text[100];

        shiptocode: Code[20];
        SalesGrossval: Decimal;
        ch: Label '';
        Qty: TEXT[100];
        DimensionValue: Record "Dimension Value";
        CustomerGRec: Record Customer;
        Country2: code[10];
        cty: Text[80];
        CustName: Code[100];
        Add1: Text[250];
        add2: Text[250];
        Q: Label '''';
        comma: Label ',';
        QT: Label '""""';
        CustomGRec: Record customer;
        ExcelBuffer: Record "Excel Buffer" temporary;
        ExcelBuffer10: Record "Excel Buffer" temporary;
        Integer1: Record integer;
        Integer2: Record integer;
}