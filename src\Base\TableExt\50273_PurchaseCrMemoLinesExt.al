tableextension 50273 "Purchase CrMemo Lines" extends "Purch. Cr. Memo Line"
{
    fields
    {
        field(50802; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50803; "Clearing No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50804; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50026; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
            //TableRelation = "Budget Header"."No." WHERE(Status = CONST(Released), "Document Type" = CONST(Capex));
            Editable = false;

        }
        field(50027; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
            //TableRelation = "Budget Line"."Line No." WHERE("Document No." = FIELD("Capex No."));
            Editable = false;

        }
    }

    var
        myInt: Integer;
}