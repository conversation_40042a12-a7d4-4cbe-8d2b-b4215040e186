page 50217 "Released Capex Budget List"
{
    //Editable = false;
    PageType = List;
    SourceTable = "Budget Header";
    SourceTableView = SORTING("Document Type", "No.")
                      ORDER(Ascending)
                      WHERE("Document Type" = CONST(Capex), Status = FILTER(Released));
    UsageCategory = Lists;
    CardPageId = "Release Capex Budget";
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("No."; "No.")
                {
                }
                field(Description; Description)
                {
                }
                field(Status; Status)
                {
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                }
                field("Document Date"; "Document Date")
                {
                }
                field("Department Code"; "Department Code")
                {
                }
                field("Budget Name"; "Budget Name")
                {
                }
                field(Amount; Amount)
                {
                }
                field("Amount(LCY)"; "Amount(LCY)")
                {
                }
                //Go2solve052023>>
                field("Amount(FCY)"; "Amount(FCY)")
                {
                }
                //Go2solve052023<<
                field("Division Code"; "Division Code")
                {
                }
            }
        }
        //>>>>>> G2S 122023
        area(factboxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50100),
                                "No." = FIELD("No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }

            systempart(Notes; Notes)
            {
                ApplicationArea = Notes;

            }
            //<<<<<< G2S 122023
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("&Card")
                {
                    Caption = '&Card';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        case "Document Type" of
                            "Document Type"::Capex:
                                PAGE.RUN(PAGE::"Capex Budget", Rec);
                        end;
                    end;
                }
            }
        }
    }
}

