page 50239 "Purchase Requisition Lines"  //Baluon Apr 04 2022 Whole Object
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Administration;
    Caption = 'Purchase Requi Lines View_50239';
    SourceTable = "Purch. Requisition Lines";
    Editable = false;
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;

                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;

                }
                field(Type; Type)
                {
                    ApplicationArea = All;

                }
                field("No."; "No.")
                {
                    ApplicationArea = All;

                }
                //FIX03Jun2021>>
                field("No.2"; "No. 2")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                //FIX03Jun2021<<
                field(Description; Description)
                {
                    ApplicationArea = all;
                    Editable = false;

                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Unit Price"; "Unit Price")
                {
                    ApplicationArea = all;
                    Editable = false;
                }

                field("Quantity(Base)"; "Quantity(Base)")
                {
                    ApplicationArea = All;

                }
                field("MRS No."; "MRS No.")
                {
                    ApplicationArea = All;

                }
                field("MRS Line No."; "MRS Line No.")
                {
                    ApplicationArea = All;

                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }


            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}