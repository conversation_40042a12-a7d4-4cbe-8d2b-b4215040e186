report 50290 "CPV Slip"
{
    DefaultLayout = RDLC;
    RDLCLayout = './CHIReports\Reports\Layout\CPV.rdl';
    Caption = 'CPV Slip_5020';
    ApplicationArea = All;
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem("Voucher Header"; "Voucher Header")
        {
            DataItemTableView = SORTING("Voucher Type", "Document No.");
            RequestFilterFields = "Voucher Type", "Document No.";
            column(FORMAT_TODAY_0_4_; FORMAT(TODAY, 0, 4))
            {
            }
            column(CurrReport_PAGENO; CurrReport.PAGENO)
            {
            }
            column(USERID; USERID)
            {
            }
            column(CPVDescription; CPVDescription)
            {
            }
            column(CASH_PAYMENT_SLIPCaption; CASH_PAYMENT_SLIPCaptionLbl)
            {
            }
            column(PageCaption; PageCaptionLbl)
            {
            }
            column(Voucher_Header_Voucher_Type; "Voucher Type")
            {
            }
            column(Voucher_Header_Document_No_; "Document No.")
            {
            }
            dataitem("Copy Loop"; Integer)
            {
                DataItemTableView = SORTING(Number);
                dataitem("Page Loop"; Integer)
                {
                    DataItemTableView = SORTING(Number)
                                        WHERE(Number = CONST(1));
                    column(Voucher_Header__ToBeCollectedBy; "Voucher Header".ToBeCollectedBy)
                    {
                    }
                    column(PayTo; PayTo)
                    {
                    }
                    column(PayName; PayName)
                    {
                    }
                    column(Paycode; Paycode)
                    {
                    }
                    column(StaffDept_; StaffDept)
                    {
                    }
                    column(Paydetail; Paydetail)
                    {
                    }
                    column(CompanyAddr_1_; CompanyAddr[1])
                    {
                    }
                    column(CompanyAddr_2_; CompanyAddr[2])
                    {
                    }
                    column(CompanyAddr_3_; CompanyAddr[3])
                    {
                    }
                    column(CompanyAddr_4_; CompanyAddr[4])
                    {
                    }
                    column(Voucher_Header__ToBeCollectedByCaption; Voucher_Header__ToBeCollectedByCaptionLbl)
                    {
                    }
                    column(PayToCaption; PayToCaptionLbl)
                    {
                    }
                    column(Page_Loop_Number; Number)
                    {
                    }
                    dataitem("Gen. Journal Line"; "Gen. Journal Line 2")
                    {
                        DataItemLink = "Document No." = FIELD("Document No.");
                        DataItemLinkReference = "Voucher Header";
                        column(Gen__Journal_Line__Document_No__; "Document No.")
                        {
                        }
                        column(TODAY; TODAY)
                        {
                        }
                        column(Discr; Discr)
                        {
                        }
                        column(AmountInword; AmountInword)
                        {
                        }
                        column(Voucher_Header___Cash_Paid_By_; "Voucher Header"."Cash Paid By")
                        {
                        }
                        column(amt; amt)
                        {
                        }
                        column(Gen__Journal_Line__Document_No__Caption; Gen__Journal_Line__Document_No__CaptionLbl)
                        {
                        }
                        column(TODAYCaption; TODAYCaptionLbl)
                        {
                        }
                        column(DiscrCaption; DiscrCaptionLbl)
                        {
                        }
                        column(Amount_in_words_Caption; Amount_in_words_CaptionLbl)
                        {
                        }
                        column(Voucher_Header___Cash_Paid_By_Caption; Voucher_Header___Cash_Paid_By_CaptionLbl)
                        {
                        }
                        column(Cashier_SignatureCaption; Cashier_SignatureCaptionLbl)
                        {
                        }
                        column(Payee_SigntureCaption; Payee_SigntureCaptionLbl)
                        {
                        }
                        column(AmountCaption; AmountCaptionLbl)
                        {
                        }
                        column(Gen__Journal_Line_Journal_Template_Name; "Journal Template Name")
                        {
                        }
                        column(Gen__Journal_Line_Journal_Batch_Name; "Journal Batch Name")
                        {
                        }
                        column(Gen__Journal_Line_Line_No_; "Line No.")
                        {
                        }

                        trigger OnAfterGetRecord();
                        begin
                            //total1 := total1 + GenJouLine."Amount (LCY)";
                            IF Amount < 0 THEN
                                CurrReport.SKIP;

                            IF Amount > 0 THEN
                                //CurrReport.CREATETOTALS(Amount);
                                amt := amt + ABS(Amount);
                            AmountInword := AmountConv.figure(amt, FORMAT(CurrencyCode), CurrencyUnit);
                            AmountInword += ' ONLY';
                        end;

                        trigger OnPreDataItem();
                        begin
                            MoreLines := FIND('+');
                            WHILE MoreLines AND (Description = '') AND ("Account No." = '') AND (Quantity = 0) AND (Amount = 0) DO
                                MoreLines := NEXT(-1) <> 0;
                            IF NOT MoreLines THEN
                                CurrReport.BREAK;
                        end;
                    }
                    dataitem(Total; Integer)
                    {
                        DataItemLink = Number = FIELD(Number);
                        DataItemTableView = SORTING(Number)
                                            WHERE(Number = CONST(1));
                    }
                }

                trigger OnAfterGetRecord();
                begin
                    IF Number > 1 THEN
                        CopyText := 'COPY';
                    CurrReport.PAGENO := 1;
                end;

                trigger OnPreDataItem();
                begin
                    NoOfLoops := ABS(NoOfCopies) + Cust."Invoice Copies" + 1;
                    IF NoOfLoops <= 0 THEN
                        NoOfLoops := 1;
                    CopyText := '';
                    SETRANGE(Number, 1, NoOfLoops);
                end;
            }

            trigger OnAfterGetRecord();
            begin
                //IF NOT CurrReport.PREVIEW THEN BEGIN
                // "No. of CPV Slip Printed"+=1;
                //MODIFY;
                //END;

                ChequeNo := "Voucher Header"."Teller / Cheque No.";
                Discr := "Voucher Header".Narration;

                IF "Voucher Header"."Bal. Account Type" = 3 THEN
                    DOCTYPE := 1 ELSE
                    DOCTYPE := 0;
                GenJouLine.SETRANGE(GenJouLine."Journal Template Name", "Voucher Header"."Journal Template Code");
                GenJouLine.SETRANGE(GenJouLine."Journal Batch Name", "Voucher Header"."Journal Batch Name");
                IF GenJouLine.FIND('-') THEN
                    DocNo := "Gen. Journal Line"."Document No.";

                Paycode := "Voucher Header"."Payable Code";
                PayName := "Voucher Header"."Payable Name";
                PayTo := "Voucher Header"."Payable To";

                IF "Voucher Header"."Payable To" = "Voucher Header"."Payable To"::Staff THEN BEGIN
                    IF EmpRec.GET(Paycode) THEN
                        Paydetail := EmpRec."Job Title";
                    StaffDept := EmpRec."Global Dimension 2 Code";
                END;

                IF "Voucher Header"."Payable To" = "Voucher Header"."Payable To"::Vendor THEN BEGIN
                    IF VendRec.GET(Paycode) THEN
                        Paydetail := VendRec.Address;
                    PayDetail2 := VendRec."Address 2";
                END;

                IF "Voucher Header"."Payable To" = "Voucher Header"."Payable To"::Customer THEN BEGIN
                    IF CustRec.GET(Paycode) THEN
                        Paydetail := CustRec.Address;
                    PayDetail2 := CustRec."Address 2";
                END;

                IF "No. of CPV Slip Printed" > 1 THEN
                    CPVDescription := text04;
            end;

            trigger OnPreDataItem();
            begin
                CompanyInfo.GET;
                FormatAddr.Company(CompanyAddr, CompanyInfo);
            end;
        }
    }

    requestpage
    {

        layout
        {
        }

        actions
        {
        }
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        IF GenJourRec.GET("Gen. Journal Line"."Journal Template Name", "Gen. Journal Line"."Journal Batch Name") THEN

        //IF "Gen. Journal Batch".GET(
        BEGIN
            //IF GenJourRec."Journal Type" = 1 THEN DOCTYPE := 1
            //ELSE DOCTYPE := 0;
            //CustName := GenJourRec."Payer/Collector Name";
        END;

        AmountInword := '';
    end;

    var
        GLSetup: Record "General Ledger Setup";
        GenJouLine: Record "Gen. Journal Line";
        PurchSetup: Record "Purchases & Payables Setup";
        UserSetup: Record "User Setup";
        UserIdRec: Record User;
        CompanyInfo: Record "Company Information";
        Cust: Record Customer;
        //SalesInvCountPrinted: Codeunit "Sales Inv.-Printed";//TEST
        FormatAddr: Codeunit "Format Address";
        CustAddr: array[8] of Text[50];
        ShipToAddr: array[8] of Text[50];
        CompanyAddr: array[8] of Text[50];
        OrderNoText: Text[30];
        SalesPersonText: Text[30];
        VATNoText: Text[30];
        ReferenceText: Text[30];
        TotalText: Text[50];
        TotalInclVATText: Text[50];
        MoreLines: Boolean;
        NoOfCopies: Integer;
        NoOfLoops: Integer;
        CopyText: Text[30];
        ShowShippingAddr: Boolean;
        i: Integer;
        Naira: Decimal;
        AmountInword: Text[250];
        AmountTotal: Decimal;
        fileNo: Code[10];
        accno: Code[10];
        GLaccRec: Record "G/L Account";
        AccName: Text[50];
        srno: Integer;
        value1: Integer;
        value2: Integer;
        VALUE3: Integer;
        value4: Integer;
        rouval: Decimal;
        ReceivName: Integer;
        CustCode: Code[10];
        CustName: Text[30];
        PayType: Option Cash,Cheque;
        Discr: Text[250];
        CurrencyCode: Option NAIRA,DOLLAR,YEN,GUILDER,"POUND ";
        CurrencyUnit: Code[10];
        AmountConv: Codeunit NumbertoText;
        DOCTYPE: Option "CASH PAYMENT VOUCHER","BANK PAYMENT VOUCHER";
        ChequeNo: Code[20];
        BankName: Text[50];
        vend: Record Vendor;
        FixedRec: Record "Fixed Asset";
        GenJourRec: Record "Gen. Journal Batch";
        JounFilter1: Code[20];
        JournFilter2: Code[20];
        BankRec: Record "Bank Account";
        ShowRetire: Boolean;
        DocNo: Code[10];
        OpeName: Text[30];
        AudName: Text[40];
        ConName: Text[40];
        FinName: Text[40];
        hod: Text[30];
        Chequetext: Option " ","Cheque No. :-";
        CompanyBu: Text[30];
        "Business UnitRec": Record "Business Unit";
        total1: Decimal;
        VendorBal: Decimal;
        VendLedRec: Record "Vendor Ledger Entry";
        //JLDim : Record "356";
        ExpenseDim: Code[20];
        ProductDim: Code[20];
        Caption: Text[100];
        PayName: Text[100];
        Paycode: Code[20];
        Paydetail: Text[100];
        VendRec: Record Vendor;
        CustRec: Record Customer;
        EmpRec: Record "Employee";
        StaffDept: Code[20];
        PayDetail2: Text[100];
        PayTo: Option Staff,Vendor,Customer,Bank,Others;
        amt: Decimal;
        text01: Label 'BATCH REPORT FOR CASH PAYMENT VOUCHER REQUEST';
        text02: Label 'BATCH REPORT FOR ADVANCE PAYMENT VOUCHER REQUEST';
        text03: Label 'BATCH REPORT FOR SETTLEMENT PAYMENT VOUCHER REQUEST';
        CPVDescription: Text[30];
        text04: Label 'REPRINT';
        CASH_PAYMENT_SLIPCaptionLbl: Label 'CASH PAYMENT SLIP';
        PageCaptionLbl: Label 'Page:';
        Voucher_Header__ToBeCollectedByCaptionLbl: Label 'To be Collected By';
        PayToCaptionLbl: Label 'Payable to';
        Gen__Journal_Line__Document_No__CaptionLbl: Label 'Document No.:';
        TODAYCaptionLbl: Label 'Date';
        DiscrCaptionLbl: Label 'In payment of :-';
        Amount_in_words_CaptionLbl: Label 'Amount in words :-';
        Voucher_Header___Cash_Paid_By_CaptionLbl: Label 'Paid By';
        Cashier_SignatureCaptionLbl: Label 'Cashier Signature';
        Payee_SigntureCaptionLbl: Label 'Payee Signture';
        AmountCaptionLbl: Label 'Amount';
}

