page 50132 "Promotion Group"
{
    DelayedInsert = false;
    PageType = Document;
    SourceTable = "Promo Schedule";
    SourceTableView = SORTING("Document Type", "No.")
                      ORDER(Ascending)
                      WHERE("Document Type" = CONST("Promo Group"));
    UsageCategory = Documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                }
                field("Promo Type"; "Promo Type")
                {
                    ApplicationArea = all;
                }
                field("Group UOM"; "Group UOM")
                {
                    ApplicationArea = all;
                }
                field("Group Quantity"; "Group Quantity")
                {
                    ApplicationArea = all;
                    BlankZero = true;
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
            }
            part(PromoSchdGrpLines; "Promo. Group Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Document Type" = FIELD("Document Type"),
                              "Document No." = FIELD("No.");
            }
            group(Retail)
            {
                Caption = 'Retail';
                field("Retail Promo"; "Retail Promo")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("P&romo. Group")
            {
                Caption = 'P&romo. Group';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        //ShowDocDim;//B2B
                    end;
                }
                action("&Approvals")
                {
                    ApplicationArea = all;
                    Caption = '&Approvals';
                    Visible = false;

                    trigger OnAction();
                    var
                        ApprovalEntries: Page 658;
                    begin
                        /*
                        ApprovalEntries.Setfilters(DATABASE::Table60201,10,"No.");
                        ApprovalEntries.RUN;*///CHI WF
                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Document Type" := "Document Type"::"Promo Group";
        "Dim. Document Type" := "Dim. Document Type"::"Promo Group";
    end;

    var
        ApprovalMgt: Codeunit 1535;
}

