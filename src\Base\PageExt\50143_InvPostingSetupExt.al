pageextension 50143 InvPostingSetupExt extends "Inventory Posting Setup"
{
    layout
    {
        // Add changes to page layout here
        addafter("Mfg. Overhead Variance Account")
        {
            field("Item Charge (Interim)"; "Item Charge (Interim)")
            {
                ApplicationArea = All;
            }
            field("GL Services (Interim)"; "GL Services (Interim)")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        // Add changes to page actions here
    }

    var
        myInt: Integer;
}