page 50863 POSSalesLine
{
    ApplicationArea = All;
    Caption = 'POSSalesLine';
    PageType = List;
    SourceTable = POSSalesLines;
    UsageCategory = Lists;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document Type"; Rec."Document Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Document Type field.', Comment = '%';
                }
                field("Transaction ID"; Rec."Transaction ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Transaction ID field.', Comment = '%';
                }
                field("Line No."; Rec."Line No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Line No. field.', Comment = '%';
                }
                field("Item ID"; Rec."Item ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Item ID field.', Comment = '%';
                }
                field("Item Description"; "Item Description")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the item Description field', Comment = '%';
                }
                field("Wholesales ID"; Rec.WholesalesID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Wholesale ID field.', Comment = '%';
                }

                field(Quantity; Rec.Quantity)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Quantity field.', Comment = '%';
                }
                field(Price; Rec.Price)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Price field.', Comment = '%';
                }
                field(Discount; Rec.Discount)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Discount field.', Comment = '%';
                }
                field(Tax; Rec.Tax)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Tax field.', Comment = '%';
                }
                field(Cost; Rec.Cost)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Cost field.', Comment = '%';
                }
                field("Receipt No"; "Receipt No")
                {
                    Caption = 'Receipt No.';
                    ApplicationArea = All;
                }
                field(Date; Date)
                {
                    ApplicationArea = All;

                }
            }
        }
    }
}
