report 50218 "KD History Report"
{
    // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425
    ApplicationArea = All;
    Caption = 'KD History Report_50218';
    UsageCategory = ReportsAndAnalysis;
    RDLCLayout = './src/Customized/G2S/Reports/Layouts/KD History Report.rdlc';

    dataset
    {
        dataitem("Key Distributor Targets"; "Key Distributor Targets")
        {
            RequestFilterFields = "Customer No.";

            column(Shrinkage_Base_Amount; "Shrinkage Base Amount")
            { }
            column(MidEvapAch; MidEvapAch)
            { }
            column(ExclJsndAch; ExclJsndAch)
            { }
            column(ExclEvapAch; ExclEvapAch)
            { }
            column(MidEvapTarget; MidEvapTarget)
            { }
            column(ExclJsndTarget; ExclJsndTarget)
            { }
            column(ExclEvapTarget; ExclEvapTarget)
            { }
            column(MidEvapDisc; MidEvapDisc)
            { }
            column(ExclJsndDisc; ExclJsndDisc)
            { }
            column(ExclEvapDisc; ExclEvapDisc)
            { }
            column(Discount_group; ParTargetPeriod)
            {

            }
            column(Customer_No_; "Customer No.")
            {

            }
            column(Customer_Name; "Customer Name")
            {

            }

            //NewColumns Added for report G2S.....................250324
            column(DMS_Usage_Disc; "DMS_Usage Disc")
            {

            }

            column(StockNorm_Disc; "StockNorm Disc")
            {

            }

            column(Working_Cap_Disc; "Working Cap Disc")
            {

            }

            column(Van_Move_disc; "Van Move disc")
            {

            }
            //..................................................250324
            column(Global_Dimension_1_Code; "Global Dimension 1 Code")
            {

            }
            column(Monthly_Target_Value; "Monthly Target Value")
            {

            }
            column(Monthly_Total_Ach__Value; "Monthly Total Ach. Value")
            {

            }
            column(Flat_Discount; FD)
            {

            }
            column(Target_For_Category_A; "Target For Category A")
            {

            }
            column(Achieve_On_Category_A; "Achieve On Category A")
            {

            }
            column(Disc_On_Category_A; DISCA)
            {

            }
            column(Target_For_Category_B; "Target For Category B")
            {

            }
            column(Achieve_On_Category_B; "Achieve On Category B")
            {

            }
            column(Disc_On_Category_B; DISCB)
            {

            }
            column(Mixed_Focus_Brand_Target_Capr_; "Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" + "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml") //G2S Added "EvapJSND Hollandia Target-190g"
            {

            }
            column(Achieve_On_Mixed_Capr; MixdAch)
            {

            }
            column(Mixed_Focus_Brand_Disc_Capr_; MixdD)
            {

            }
            column(MixedPer; MixedPer)
            { }
            column(Evap_Excl__60G_Target; "Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml")
            {

            }
            column(Achieve_On_Evap_120G; EvapAch)
            {

            }
            column(EvaPer; EvaPer)
            { }
            column(Evap_Excl__120G_Disc; EVAPD)
            {

            }
            //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
            column(JNSD_Capr_Target; "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-120gslim" + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" + "ExclEVAP Holla Target-UHT MILK")
            {

            }
            //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
            column(Achieve_On_JSND_Capr; JSDAch)
            {

            }
            column(JNSD_Capr_Disc; JSNDDic)
            {

            }
            column(JSNPer; JSNPer)
            {

            }
            column(Quarterly_Target_Value; "Quarterly Target Value")
            {

            }
            column(Quarterly_Achievement; "Qtrly Achievement")
            {

            }
            column(Quarterly_Rebate; QR)
            {

            }
            column(TargetDedu; '20')
            {

            }
            column(AchDeds; "DMS Usage Days")
            {

            }
            column(Deduction_On_Qtr__Disc; DD)
            {

            }
            column(Total_Rebate; TR)
            {

            }
            column(CompInf; CompInf.Name)
            {

            }
            column(CompInfPic; CompInf.Picture)
            {

            }
            column(QtrPer; QtrPer)
            {

            }
            column(Shrinakge_Variance; "Shrinakge Variance")
            {

            }
            column(Shrinkage02; Shrinkage02)
            {

            }
            column(MonthlySales45; MonthlySales45)
            {

            }
            column(TotalMonthlySales45; TotalMonthlySales45)
            {

            }



            trigger OnPreDataItem()
            begin
                CalPeriod();//PKONSE21
                setrange("Target Period", ParTargetPeriod)
            end;

            trigger OnAfterGetRecord()
            var
                kdCustFocus: Record "KD Cust. Focus Brands ";
                myInt: Integer;
            begin


                Clear(MonthlySales45);
                Clear(Shrinkage02);

                FD := "Flat Discount";
                DIscA := "Disc On Category A";
                DiscB := "Disc On Category B";

                EvapD := "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" +
                "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g"
                + "EvapJSND Holla Disc-UHT" +  //G2S Added "EvapJSND Hollandia Disc-190g"
                "EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml"; //G2S>>>>>110625 CAS-01425-M8J6P2


                MixdD := "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" +
                "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                JSNDDic := "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" +
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                "ExclEVAP Holla Disc-UHT MILK";
                TR := "Total Rebate";
                DD := "Deduction On Qtr. Disc";
                QR := "Quarterly Rebate";


                //EVAP & JSND
                CalcFields("Evap & JSND Juice Ach-1lit", "Evap & JSND Juice Ach-cans", "Evap & JSND Juice Ach caprisun");
                CalcFields("Evap & JSND Yoghurt Ache-1 Lit", "Evap & JSND Yoghurt Ache-90 ML", "Evap&JSND Hollandia Ache-120g", "EvapJSND Hollandia Achiv-190g"); //G2S Added "EvapJSND Hollandia Achiv-190g"
                CalcFields("Evap&JSND Hollandia Ache-120g", "EvapJSND Hollandia Achiv-190g", "EvapJSND Holla Achiv-UHT"); //G2S Added "EvapJSND Hollandia Achiv-190g"
                CalcFields("EvapJSND Juice Achiv-315ml", "EvapJSND Juice Achiv-Pet", "EvapJSND Yoghurt Achiv-315ml"); //G2S>>>>>110625 CAS-01425-M8J6P2
                Clear(MixedPer);
                IF ("Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" +
                "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + //G2S Added "EvapJSND Hollandia Target-190g"
                "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml" <> 0) //G2S>>>>>110625 CAS-01425-M8J6P2
                and
                ("Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "Evap & JSND Juice Ach caprisun" +
                "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML" + "Evap&JSND Hollandia Ache-120g" +
                "Evap&JSND Hollandia Ache-120g" + "EvapJSND Hollandia Achiv-190g" + "EvapJSND Holla Achiv-UHT" +
                "EvapJSND Juice Achiv-315ml" + "EvapJSND Juice Achiv-Pet" + "EvapJSND Yoghurt Achiv-315ml" <> 0) //G2S Added "EvapJSND Hollandia Achiv-190g" added Juice Achiv-Pet Yoghurt Achiv-315ml Juice Achiv-315ml 110625
                then
                    MixedPer := (("Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "Evap & JSND Juice Ach caprisun" +
                "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML" + "Evap&JSND Hollandia Ache-120g" + "EvapJSND Hollandia Achiv-190g" + //G2S Added "EvapJSND Hollandia Achiv-190g"
                "Evap&JSND Hollandia Ache-120g" + "EvapJSND Holla Achiv-UHT" + "EvapJSND Juice Achiv-315ml" + "EvapJSND Juice Achiv-Pet" + "EvapJSND Yoghurt Achiv-315ml") / ("Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" +
                "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml"));  //G2S Added "EvapJSND Hollandia Target-190g"

                //Excl JSND
                Clear(EvaPer);

                CalcFields("ExclJSND Juice Achiv-1lit", "ExclJSNDJuice Achiv-cans", "Excl JSND Juice Achie-caprisun");
                CalcFields("Excl JSND Yoghurt Achiv-1lit", "Excl JSND Yoghurt Achiv-90ml");
                IF ("ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml" <> 0)
                AND ("Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml" <> 0) then
                    EvaPer := (("ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml") / ("Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml"));

                //EXCL EVAP
                clear(JSNPer);   //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                CalcFields("ExclEVAP Holl Achv -120gFc", "ExclEVAP Holl Achv -190gFc", "ExclEVAP Holla Achiv-120gslim", "ExclEVAP Holla Achiv-50Fc", "ExclEVAP Holla Achiv-50gslim",
                "ExclEVAP Holl Achv -UHT MILK");

                IF (("ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120gslim" + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" <> 0) AND ("ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holl Achv -120gFc"
                + "Excl EVAP Hollan Disc-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" + "ExclEVAP Holl Achv -UHT MILK" <> 0)) then
                    JSNPer := (("ExclEVAP Holl Achv -120gFc" + "ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holla Achiv-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" + "ExclEVAP Holl Achv -UHT MILK") / ("ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120gslim"
                     + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" + "ExclEVAP Holla Target-UHT MILK"));
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224


                /* Clear(QtrPer);
                IF ("Qtrly Achievement" <> 0) AND ("Quarterly Target Value" <> 0) then
                    QtrPer := "Qtrly Achievement" / "Quarterly Target Value";
 */
                Clear(JSDAch);
                JSDAch := "Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "Evap & JSND Juice Ach caprisun" +
                "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML" + "Evap&JSND Hollandia Ache-120g" +
                "Evap&JSND Hollandia Ache-120g" + "EvapJSND Hollandia Achiv-190g"; //G2S Added "EvapJSND Hollandia Achiv-190g" 

                Clear(EvapAch);
                EvapAch := "ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                Clear(MixdAch);
                MixdAch := "ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holl Achv -120gFc" + "ExclEVAP Holla Achiv-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" +
                "ExclEVAP Holl Achv -UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                //G2s>>>>>>>Added EvapJSND 190g into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                MidEvapAch := "Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "EvapJSND Hollandia Achiv-190g" + "Evap & JSND Juice Ach caprisun" + "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML"
                + "Evap&JSND Hollandia Ache-120g" + "Evap&JSND Hollandia Ache-120g" + "EvapJSND Holla Achiv-UHT" + "EvapJSND Juice Achiv-315ml" + "EvapJSND Juice Achiv-Pet" + "EvapJSND Yoghurt Achiv-315ml"; //G2S added Juice Achiv-Pet Yoghurt Achiv-315ml Juice Achiv-315ml 110625
                //G2s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<020224

                ExclJsndAch := "ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                ExclEvapAch := "ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holl Achv -120gFc" + "ExclEVAP Holla Achiv-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" + "ExclEVAP Holl Achv -UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                MidEvapTarget := "Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" +
                "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml"; //G2S Added "EvapJSND Hollandia Target-190g" Aadded Juice Target-Pet Yoghurt Target-315ml Juice Target-315ml 110625

                ExclJsndTarget := "Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                ExclEvapTarget := "ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120gslim" + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" + "ExclEVAP Holla Target-UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                MidEvapDisc := "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" + "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g" + "EvapJSND Holla Disc-UHT" + "EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml";  //Added G2S "EvapJSND Hollandia Disc-190g" Added Juice Disc-Pet Yoghurt Disc-315ml Juice Disc-315ml 110625

                ExclJsndDisc := "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" + "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                ExclEvapDisc := "Excl EVAP Hollan Disc-120gFc" + "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" + "ExclEVAP Holla Disc-UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                //4.5% on monthly Sales achieved
                MonthlySales45 := "Monthly Total Ach. Value" * 0.045;
                TotalMonthlySales45 += MonthlySales45;

                //Populate 0.2% on monthly Sales achieved (Shrinkage allowance)
                Shrinkage02 := "Shrinkage Base Amount" * 0.002;
                TotalShrinkage02 += Shrinkage02;

            end;

        }
    }

    trigger OnInitReport()
    begin
        CompInf.GET();
        CompInf.CalcFields(Picture);
    end;




    var
        CompInf: Record "Company Information";
        kdCustFocus: Record "KD Cust. Focus Brands ";
        ParTargetPeriod: Text[20];
        MixedPer: Decimal;
        EvaPer: Decimal;
        JSNPer: Decimal;
        QtrPer: Decimal;
        JSDAch: Decimal;
        EvapAch: Decimal;
        MixdAch: Decimal;
        FD: Decimal;
        DiscA: Decimal;
        DiscB: Decimal;
        MixdD: Decimal;
        JSNDDic: Decimal;
        EvapD: Decimal;
        QR: Decimal;
        DD: Decimal;
        TR: Decimal;
        MonthlySales45, TotalMonthlySales45 : Decimal;
        Shrinkage02, TotalShrinkage02 : Decimal;
        QKeyDistributorTarget: Record "Key Distributor Targets";
        QRebateperiod2: Record "Rebate Period Codes";//PKONSE21
        QAPeriodFilter: Text[30];//PKONSE21
        PKKDRebateData: Record KDRebateData;
        PrevMon: Integer;
        PrevYear: Integer;
        TotalMonths: Integer;
        Customerrec2: Record customer;
        KDPeriod: Record "Rebate Period Codes";
        MidEvapAch: Decimal;
        ExclJsndAch: Decimal;
        ExclEvapAch: Decimal;
        MidEvapTarget: Decimal;
        ExclJsndTarget: Decimal;
        ExclEvapTarget: Decimal;
        MidEvapDisc: Decimal;
        ExclJsndDisc: Decimal;
        ExclEvapDisc: Decimal;
        DMS_UsageDisc: Decimal;
        StockNRMDisc: Decimal;
        WorkingCAPDisc: Decimal;
        VanMOVEDisc: Decimal;

    procedure GetTarget(Lpartarget: Code[20])
    Begin
        ParTargetPeriod := Lpartarget;
    End;

    procedure CalPeriod()//PKONSE21
    begin
        CLEAR(QAPeriodFilter);
        if COPYSTR(parTargetPeriod, 6) in ['03', '06', '09', '12'] then begin
            QRebateperiod2.GET(parTargetPeriod);
            QAPeriodFilter := QRebateperiod2.Code;
            QRebateperiod2.FIND('<');
            QAPeriodFilter += '|' + QRebateperiod2.Code;
            QRebateperiod2.FIND('<');
            QAPeriodFilter += '|' + QRebateperiod2.Code;
        end;
    end;
}
