page 50453 "Import File subform"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Import Files.
    // 3.0      SAA      04-Apr-13      -> Added codes Form-OnAfterGetRecord functions to sum charges from
    //                                     Value Entry History table.

    Editable = false;
    PageType = ListPart;
    SourceTable = "Purch. Rcpt. Line";
    SourceTableView = SORTING("Document No.", "Import File No.", Type)
                      WHERE(Type = CONST(Item));

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                }
                field(Description; Description)
                {
                }
                field(Quantity; Quantity)
                {

                }
                field("Unit Cost (LCY)"; "Unit Cost (LCY)")
                {

                }
            }
        }
    }

    actions
    {
    }

    var
        ItemInvCost: Decimal;
        ValueEntry: Record "Value Entry";
        ItemChargeCost: Decimal;
        ItemChargeNo: Code[20];
        ItemTotalCharges: Decimal;
        TotalItemInvCost: Decimal;
        //ValueEntryHistory : Record "Value Entry History";
        ItemInvCostHistory: Decimal;
        ItemChargeCostHistory: Decimal;
        ItemTotalChargesHistory: Decimal;
        ActualTotalCharges: Decimal;
        ActualItemInvCost: Decimal;
        NewItemChargeCost: Decimal;

    procedure UpdateMatrix();
    begin
    end;

    procedure DrillDownCharge();
    begin
    end;

    procedure DrillDownInvoiceCost();
    begin
    end;

    procedure DrillDownChargeTotal();
    begin
    end;
}

