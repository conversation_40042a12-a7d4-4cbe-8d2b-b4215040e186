page 50635 "Bank Acc. Reconciliati LinesPK"
{
    AutoSplitKey = true;
    Caption = 'Lines';
    DelayedInsert = true;
    LinksAllowed = false;
    PageType = ListPart;
    SourceTable = "Bank Acc. Reconciliation Line";
    SourceTableView = WHERE("Statement Type" = CONST("Bank Reconciliation"));

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                ShowCaption = false;
                field(Select; Select)
                {
                    ApplicationArea = all;
                }
                field("Teller No."; "Teller No.") //PKON22AP28
                {
                    ApplicationArea = all;
                }
                field("Transaction Date"; "Transaction Date")
                {
                    ApplicationArea = Basic, Suite;
                    StyleExpr = StyleTxt;
                    ToolTip = 'Specifies the posting date of the bank account or check ledger entry on the reconciliation line when the Suggest Lines function is used.';
                }
                field("Matching Status"; "Matching Status")
                {
                    ApplicationArea = all;
                    Style = Strong;
                    Editable = false;
                }

                field("Value Date"; "Value Date")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the value date of the transaction on the bank reconciliation line.';
                    Visible = false;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = Basic, Suite;
                    Style = Strong;
                    ToolTip = 'Specifies a number of your choice that will appear on the reconciliation line.';
                    //Visible = false;
                }
                field("matchingLine No."; "MatchLine No")
                {
                    applicationarea = ALL;
                    Style = Strong;
                }
                field("Check No."; "Check No.")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the check number for the transaction on the reconciliation line.';
                    Visible = false;
                }
                field(Type; Type)
                {
                    ApplicationArea = Basic, Suite;
                    Style = Strong;
                    ToolTip = 'Specifies the type of ledger entry, or a difference to be reconciled on this line.';

                    trigger OnValidate()
                    begin
                        SetUserInteractions;
                    end;
                }
                field(Description; Description)
                {
                    ApplicationArea = Basic, Suite;
                    StyleExpr = StyleTxt;
                    ToolTip = 'Specifies a description for the transaction on the reconciliation line.';
                }
                field(Description2; Description2)
                {
                    ApplicationArea = Basic, Suite;
                    StyleExpr = StyleTxt;
                }
                field("Statement Amount"; "Statement Amount")
                {
                    ApplicationArea = Basic, Suite;
                    StyleExpr = StyleTxt;
                    ToolTip = 'Specifies the amount of the transaction on the bank''s statement shown on this reconciliation line.';
                }
                field("Paid To / Received By"; "Paid To / Received By")
                {
                    ApplicationArea = all;
                    Style = Strong;
                }
                field("Applied Amount"; "Applied Amount")
                {
                    ApplicationArea = Basic, Suite;
                    Style = Strong;
                    ToolTip = 'Specifies the amount of the transaction on the reconciliation line that has been applied to a bank account or check ledger entry.';

                    trigger OnDrillDown()
                    begin
                        DisplayApplication;
                    end;
                }
                field(Difference; Difference)
                {
                    ApplicationArea = Basic, Suite;
                    Style = Strong;
                    ToolTip = 'Specifies the difference between the amount in the Statement Amount field and the amount in the Applied Amount field.';
                }
                field("Applied Entries"; "Applied Entries")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies whether the transaction on the bank''s statement has been applied to one or more bank account or check ledger entries.';
                    Visible = false;
                }
                field("Related-Party Name"; "Related-Party Name")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the name of the customer or vendor who made the payment that is represented by the journal line.';
                    Visible = false;
                }
                field("Additional Transaction Info"; "Additional Transaction Info")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies additional information on the bank statement line for the payment.';
                    Visible = false;
                }
            }
            group(Control16)
            {
                //Visible = false;
                ShowCaption = false;
                label(Control13)
                {
                    ApplicationArea = Basic, Suite;
                    ShowCaption = false;
                    Caption = '';
                }
                field(Balance; Balance + "Statement Amount")
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Balance';
                    Editable = false;
                    Enabled = BalanceEnable;
                    ToolTip = 'Specifies a balance, consisting of the Balance Last Statement field, plus the balance that has accumulated in the Statement Amount field.';
                }
                field(Amount; SeletedEntriesAmount)
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Amount';
                    Editable = false;
                    Enabled = BalanceEnable;
                    ToolTip = 'Selected Entries total amount';
                }
                field(TotalBalance; TotalBalance + "Statement Amount")
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Total Balance';
                    Editable = false;
                    Enabled = TotalBalanceEnable;
                    ToolTip = 'Specifies the accumulated balance of the bank reconciliation, which consists of the Balance Last Statement field, plus the balance in the Statement Amount field.';
                }
                field(TotalDiff; TotalDiff + Difference)
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Total Difference';
                    Editable = false;
                    Enabled = TotalDiffEnable;
                    ToolTip = 'Specifies the total amount of the Difference field for all the lines on the bank reconciliation.';
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action(ShowStatementLineDetails)
            {
                ApplicationArea = Basic, Suite;
                Caption = 'Details';
                RunObject = Page "Bank Statement Line Details";
                RunPageLink = "Data Exch. No." = FIELD("Data Exch. Entry No."),
                              "Line No." = FIELD("Data Exch. Line No.");
                ToolTip = 'View additional information about the document on the selected line and link to the related card.';
            }
            action(ApplyEntries)
            {
                ApplicationArea = Basic, Suite;
                Caption = '&Apply Entries...';
                Enabled = ApplyEntriesAllowed;
                Image = ApplyEntries;
                ToolTip = 'Select one or more ledger entries that you want to apply this record to so that the related posted documents are closed as paid or refunded.';

                trigger OnAction()
                begin
                    ApplyBankReconEntries;
                end;
            }
            action("Select Entry")
            {
                Caption = 'Select Entries';
                Promoted = true;
                PromotedCategory = Process;
                applicationarea = ALL;

                trigger OnAction();
                var
                    BankAccReconciliationLine: Record "Bank Acc. Reconciliation Line";
                begin
                    //CurrPage.
                    BankAccReconciliationLine.CopyFilters(Rec);
                    if BankAccReconciliationLine.FindSet() then
                        BankAccReconciliationLine.ModifyAll(Select, true);
                end;
            }
            action("UnSelect Entry")
            {
                Caption = 'UnSelect Entries';
                Promoted = true;
                PromotedCategory = Process;
                applicationarea = ALL;

                trigger OnAction();
                var
                    BankAccReconciliationLine: Record "Bank Acc. Reconciliation Line";
                begin
                    //CurrPage.
                    BankAccReconciliationLine.CopyFilters(Rec);
                    if BankAccReconciliationLine.FindSet() then
                        BankAccReconciliationLine.ModifyAll(Select, false);
                end;
            }
        }
    }

    trigger OnAfterGetCurrRecord()
    begin
        if "Statement Line No." <> 0 then
            CalcBalance("Statement Line No.");
        SetUserInteractions;
    end;

    trigger OnAfterGetRecord()
    begin
        SetUserInteractions;
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        if ("Matching Status" = "Matching Status"::Exact) or ("Matching Status" = "Matching Status"::Probable) then
            Error('');
        SetUserInteractions;
    end;

    trigger OnInit()
    begin
        BalanceEnable := true;
        TotalBalanceEnable := true;
        TotalDiffEnable := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        if BelowxRec then
            CalcBalance(xRec."Statement Line No.")
        else
            CalcBalance(xRec."Statement Line No." - 1);
    end;

    var
        BankAccRecon: Record "Bank Acc. Reconciliation";
        StyleTxt: Text;
        TotalDiff: Decimal;
        Balance: Decimal;
        SeletedEntriesAmount: Decimal;
        TotalBalance: Decimal;
        [InDataSet]
        TotalDiffEnable: Boolean;
        [InDataSet]
        TotalBalanceEnable: Boolean;
        [InDataSet]
        BalanceEnable: Boolean;
        ApplyEntriesAllowed: Boolean;
        StmtApplyEntries: Codeunit "Bank Acc. Recon. Apply Entries";
        /*TotalDiff: Decimal;
        Balance: Decimal;
        TotalBalance: Decimal;*/
        OgnlBnkStatement: Record "Original Bank Statement";


    /*Procedure CalcBalance(BankAccReconLineNo: Integer)
    var
        TempBankAccReconLine: Record "Bank Acc. Reconciliation Line";
    begin
        IF BankAccRecon.GET("Bank Account No.", "Statement No.") THEN;

        TempBankAccReconLine.COPY(Rec);

        TotalDiff := -Difference;
        IF TempBankAccReconLine.CALCSUMS(Difference) THEN BEGIN
            TotalDiff := TotalDiff + TempBankAccReconLine.Difference;
            CurrPage.TotalDiff.ENABLED := TRUE
        END
            CurrPage.TotalDiff.ENABLED := FALSE;

        TotalBalance := BankAccRecon."Balance Last Statement" - "Statement Amount";
        IF TempBankAccReconLine.CALCSUMS("Statement Amount") THEN BEGIN
            TotalBalance := TotalBalance + TempBankAccReconLine."Statement Amount";
            CurrPage.TotalBalance.ENABLED := TRUE
        END else
            CurrPage.TotalBalance.ENABLED := FALSE;

        Balance := BankAccRecon."Balance Last Statement" - "Statement Amount";
        TempBankAccReconLine.SETRANGE("Statement Line No.", 0, BankAccReconLineNo);
        IF TempBankAccReconLine.CALCSUMS("Statement Amount") THEN BEGIN
            Balance := Balance + TempBankAccReconLine."Statement Amount";
            CurrPage.Balance.ENABLED := TRUE
        END
        CurrPage.Balance.ENABLED := FALSE;
    end; //PK*/

    Procedure ApplyEntries()
    BEGIn
        "Ready for Application" := TRUE;
        CurrPage.SAVERECORD;
        COMMIT;
        StmtApplyEntries.ApplyEntries(Rec);
    end;

    Procedure SelectedEntry(VAR SelectedEntryLine: Integer)
    begin
        SelectedEntryLine := "Statement Line No.";
    end;

    Procedure selectedEntries(VAR BankReconLines: Record "Bank Acc. Reconciliation Line")
    begin
        BankReconLines := Rec;
        CurrPage.SETSELECTIONFILTER(BankReconLines);
    end;

    procedure ToggleMatchedFilterforBankRec()
    begin
        SetFilter("Applied Entries", '<>%1', 0);
        CurrPage.Update;
    end;

    Procedure GetMatchFil(Lpar: option Exact,Probable,"Not Matching",Cancel,"Manually Matched",All)// G2S 7708-CAS-01421-Z6M7V9
    begin
        IF Lpar = lpar::Exact then
            setrange("Matching Status", "Matching Status"::Exact)
        else
            IF Lpar = lpar::Probable then
                setrange("Matching Status", "Matching Status"::Probable)
            Else
                IF Lpar = lpar::"Not Matching" then
                    setrange("Matching Status", "Matching Status"::"Not Matching")
                else
                    IF Lpar = lpar::Cancel then
                        setrange("Matching Status", "Matching Status"::Cancel)
                    else
                        IF Lpar = lpar::"Manually Matched" then // G2S 7708-CAS-01421-Z6M7V9
                            setrange("Matching Status", "Matching Status"::"Manually Matched")
                        else
                            IF Lpar = lpar::ALL then
                                ReSET;
        currpage.Update();
    end;


    local procedure CalcBalance(BankAccReconLineNo: Integer)
    var
        TempBankAccReconLine: Record "Bank Acc. Reconciliation Line";
    begin
        if BankAccRecon.Get("Statement Type", "Bank Account No.", "Statement No.") then;

        TempBankAccReconLine.Copy(Rec);

        TotalDiff := -Difference;
        if TempBankAccReconLine.CalcSums(Difference) then begin
            TotalDiff := TotalDiff + TempBankAccReconLine.Difference;
            TotalDiffEnable := true;
        end else
            TotalDiffEnable := false;

        TotalBalance := BankAccRecon."Balance Last Statement" - "Statement Amount";
        if TempBankAccReconLine.CalcSums("Statement Amount") then begin
            TotalBalance := TotalBalance + TempBankAccReconLine."Statement Amount";
            TotalBalanceEnable := true;
        end else
            TotalBalanceEnable := false;

        Balance := BankAccRecon."Balance Last Statement" - "Statement Amount";
        TempBankAccReconLine.SetRange("Statement Line No.", 0, BankAccReconLineNo);
        if TempBankAccReconLine.CalcSums("Statement Amount") then begin
            Balance := Balance + TempBankAccReconLine."Statement Amount";
            BalanceEnable := true;
        end else
            BalanceEnable := false;
        Clear(SeletedEntriesAmount);
        TempBankAccReconLine.reset;
        //CurrPage.SetSelectionFilter(TempBankAccReconLine);
        TempBankAccReconLine.SetRange(Select, true);
        TempBankAccReconLine.SetRange("Bank Account No.", "Bank Account No.");
        TempBankAccReconLine.SetRange("Statement No.", "Statement No.");
        TempBankAccReconLine.SETRANGE(TempBankAccReconLine."Matching Status", TempBankAccReconLine."Matching Status"::"Not Matching");
        IF TempBankAccReconLine.FINDFIRST THEN
            REPEAT
                SeletedEntriesAmount += TempBankAccReconLine."Statement Amount";
            UNTIL TempBankAccReconLine.NEXT = 0;
    end;

    local procedure ApplyBankReconEntries()
    var
        BankAccReconApplyEntries: Codeunit "Bank Acc. Recon. Apply Entries";
    begin
        "Ready for Application" := true;
        CurrPage.SaveRecord;
        Commit();
        BankAccReconApplyEntries.ApplyEntries(Rec);
    end;

    procedure GetSelectedRecords(var TempBankAccReconciliationLine: Record "Bank Acc. Reconciliation Line" temporary)
    var
        BankAccReconciliationLine: Record "Bank Acc. Reconciliation Line";
    begin
        CurrPage.SetSelectionFilter(BankAccReconciliationLine);
        if BankAccReconciliationLine.FindSet then
            repeat
                TempBankAccReconciliationLine := BankAccReconciliationLine;
                TempBankAccReconciliationLine.Insert();
            until BankAccReconciliationLine.Next = 0;
    end;

    local procedure SetUserInteractions()
    begin
        StyleTxt := GetStyle2;
        ApplyEntriesAllowed := Type = Type::"Check Ledger Entry";
    end;

    procedure GetStyle2(): Text
    begin
        if ("Matching Status" = 0) or ("Matching Status" = 1) then
            exit('Favorable');

        exit('Strong');
    end;

    procedure ToggleMatchedFilter(SetFilterOn: Boolean)
    begin
        if SetFilterOn then
            SetFilter(Difference, '<>%1', 0)
        else
            Reset;
        CurrPage.Update;
    end;
}
