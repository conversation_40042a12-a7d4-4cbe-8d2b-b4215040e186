page 50523 TempMrsLines
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = TempMrsLine;

    layout
    {
        area(Content)
        {
            repeater(Control10000000002)
            {

                field("Document No."; "Document No.")
                {
                    Caption = 'MRS No.';
                    ApplicationArea = all;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = all;
                }
                field("Item Category Code"; "Item Category Code")
                {
                    ApplicationArea = all;

                }
                field(Type; Type)
                {
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;

                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = all;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                }
                field("Qty. to Purchase"; "Qty. to Purchase")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Line Amount(LCY)"; "Line Amount(LCY)")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Bin Code"; "Bin Code")
                {
                    ApplicationArea = all;

                }
                field("Issue Dept."; "Issue Dept.")
                {
                    ApplicationArea = all;
                }
                field("Issue Bus. Unit"; "Issue Bus. Unit")
                {

                    ApplicationArea = all;
                }
                field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
                field("Variant Code"; "Variant Code")
                {
                    Caption = 'Variant Code';

                }
                field("Purch. Req. Ref. No."; "Purch. Req. Ref. No.")
                {
                    ApplicationArea = all;

                }
                field("Qty. to Issue"; "Qty. to Issue")
                {
                    ApplicationArea = all;
                }
                field("Qty. to Return"; "Qty. to Return")
                {
                    ApplicationArea = all;
                }
                field("Acknowledged Issued Quantity"; "Acknowledged Issued Quantity")
                {
                    Caption = 'Acknowledged Issued Quantity';
                    ApplicationArea = all;
                }
                field("Issued Acknowledged By"; "Issued Acknowledged By")
                {
                    ApplicationArea = all;
                }
                field("Issued Acknowledged DateTime"; "Issued Acknowledged DateTime")
                {
                    ApplicationArea = all;
                }
                field("Return Quantity"; "Return Quantity")
                {
                    ApplicationArea = all;
                }
                field("Qty. to Request"; "Qty. to Request")
                {
                    ApplicationArea = all;
                }
                field("Purch. Req. Ref. Line No."; "Purch. Req. Ref. Line No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Existing FA No."; "Existing FA No.")
                {
                    ApplicationArea = all;


                }
                field("Capex No."; "Capex No.")
                {
                    ApplicationArea = all;

                }
                field("Capex Line No."; "Capex Line No.")
                {
                    ApplicationArea = all;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    ApplicationArea = all;
                }
                field("Required Quantity"; "Required Quantity")
                {
                    ApplicationArea = all;
                }
                field("Create Transfer Order"; "Create Transfer Order")
                {
                    ApplicationArea = all;
                }
                field("Prod. Order No."; "Prod. Order No.")
                {
                    ApplicationArea = all;

                }
                field("Prod. Order Line No."; "Prod. Order Line No.")
                {
                    ApplicationArea = all;
                }
                field("Production Batch No."; "Production Batch No.")
                {
                    ApplicationArea = all;

                }
                field("Qty. Per BOM"; "Qty. Per BOM")
                {
                    ApplicationArea = all;

                }
                field("Transfer Order Ref. No."; "Transfer Order Ref. No.")
                {
                    Caption = 'Transfer Order Ref. No.';
                    ApplicationArea = all;

                }
                field("From-Location Code"; "From-Location Code")
                {

                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    TableRelation = "Responsibility Center";
                    ApplicationArea = all;
                }
                field("No. of FA Created"; "No. of FA Created")
                {
                    ApplicationArea = all;

                }
                field("FA Created"; "FA Created")
                {
                    ApplicationArea = all;
                    Editable = FALSE;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                }
                field("Insert To Pur. Req No."; "Insert To Pur. Req No.")
                {
                    ApplicationArea = all;

                }

                field("FA Item Template - MRS"; "FA Item Template - MRS")
                {
                    ApplicationArea = all;

                }
                field("FA Card Template - MRS"; "FA Card Template - MRS")
                {
                    ApplicationArea = all;

                }
                field("Maintenance Job Card No."; "Maintenance Job Card No.")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Equipment ID"; "Equipment ID")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Scrap Return Qty."; "Scrap Return Qty.")
                {
                    Description = 'SAA3.0';
                    ApplicationArea = all;
                }
                field("Acknowledged Scrap Quantity"; "Acknowledged Scrap Quantity")
                {
                    Description = 'SAA3.0';
                    ApplicationArea = all;
                }
                field("FA Res. Maint. Register No."; "FA Res. Maint. Register No.")
                {
                    ApplicationArea = all;
                }
                Field("Loading Advice Created"; "Loading Advice Created")
                {
                    ApplicationArea = all;
                }
                field("CWIPNo."; "CWIPNo.")
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                }
                field("Date of MRS"; "Date of MRS")
                {
                    ApplicationArea = all;
                }
                field("Location Codes"; "Location Codes")
                {
                    ApplicationArea = all;
                }
                field("Issue Depts"; "Issue Depts")
                {
                    ApplicationArea = all;
                }
                field("Indent Depts"; "Indent Depts")
                {

                    ApplicationArea = all;
                }
                field("Indent Bus. Units"; "Indent Bus. Units")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Codes"; "Shortcut Dimension 1 Codes")
                {
                    Caption = 'Accounting Location';
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Codes"; "Shortcut Dimension 2 Codes")
                {
                    ApplicationArea = all;
                    Caption = 'Cost Center';

                }
                field("Expected Delivery Dates"; "Expected Delivery Dates")
                {
                    ApplicationArea = all;
                }
                field("Issued Date"; "Issued Date")
                {
                    ApplicationArea = all;
                }
                field(Comments; Comments)
                {
                    ApplicationArea = all;

                }
                field("No. Series"; "No. Series")
                {

                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Last Modified By"; "Last Modified By")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Last Modified Date"; "Last Modified Date")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    Caption = 'Approval Status';
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Materials Issued To"; "Materials Issued To")
                {
                    ApplicationArea = all;
                }
                field("MRS Type"; "MRS Type")
                {

                    ApplicationArea = all;
                }
                field("FA Createds"; "FA Createds")
                {
                    ApplicationArea = all;
                    Editable = FALSE;
                }
                field("Purchase Types"; "Purchase Types")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Centers"; "Responsibility Centers")
                {
                    Caption = 'Responsibility Center';
                    ApplicationArea = all;
                }
                field("Additional MRS"; "Additional MRS")
                {
                    Editable = false;
                    ApplicationArea = all;
                }

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}