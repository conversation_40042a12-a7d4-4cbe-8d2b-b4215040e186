pageextension 50151 PurchRolcenExt extends "Purchasing Manager Role Center"
{
    layout
    {

    }

    actions
    {
        //BaluonMar8 2022>>
        addafter(Group)
        {
            group("Approve Pages")
            {
                action("Request to Approve")
                {
                    ApplicationArea = all;
                    Caption = 'Request to Approves';
                    RunObject = page "Requests to Approve";
                }
                action("Approval Entries View")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries View';
                    RunObject = page "Approval Entries View Page";
                }
                action("Approval Entries")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries';
                    RunObject = page "Approval Entries";
                }
            }
        }//BaluonMar8 2022<<


        addlast("Group8")
        {
            action("Warehouse Shipments")
            {
                ApplicationArea = Planning;
                RunObject = page "Warehouse Shipment List";
            }
            action("Warehouse Receipts")
            {
                ApplicationArea = Planning;
                RunObject = page "Warehouse Receipts";
            }
        }
        addafter("Group8")
        {
            group("Customized Pages")
            {
                action("Local Purchase Quotes")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Local Purchase Quotes";
                }
                action("Import Purchase Quotes")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Import Purchase Quotes";
                }
                action("Local Purchase Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Local Purchase Orders";
                }
                action("Import Purchase Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Import Purchase Orders";
                }

                group("Material Requisition")
                {
                    action("Material Requisitions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions.";
                    }
                    action("Material Requisitions-Released")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions-Released";
                    }
                    action("Production MRS List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production MRS List";
                    }
                    action("Approved Production MRS")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Approved Production MRS List";

                    }
                    action("Material Req Ack")
                    {
                        Caption = 'Material Requisation Release Ack';
                        ApplicationArea = all;
                        RunObject = page "Material Requ-Rel Ack";
                    }
                }
                group("Purchase Requisition")
                {
                    action("Purchase Requisitions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Purchase Requisitions";
                    }
                    action("Purchase Requisitions-Released")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Purchase Requisitions-Released";
                    }
                    action("Purchase Requisitions-Closed")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Purchase Requisitions-Closed";
                    }


                }
                group("Quotation Comparision")
                {
                    action("Quotation Comparisions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Quotation Comparisions";
                    }
                }
                group("Petrol Management System")
                {
                    action("PMS Cards")
                    {
                        ApplicationArea = Planning;
                        RunObject = page PMSManagement;
                    }
                    action("PMS Vouchers")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "PMS Voucher List";
                    }
                }
            }
            group("Customized Reports")
            {
                action("Purchase Order")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Purchase Order";
                }
                action("Purchase Invoice")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Purchase INVOICE";
                }
                action("Good Receipt Note")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Good Receipt Note";
                }
                action("Pending Invoice for GRN")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Pending Invoice for GRN";
                }
                action("Purchase Register")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Purchase Register";
                }
                action("Orders Received Not Invoiced")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Orders Received Not Invoiced";
                }
                action("Transporters pmt req Br.")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Transporters pmt req Br.";
                }
                action("Material Request Done Not issued.")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Material Req.";
                }
                action("PRN Done and PO not created.")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Purchase Order Not Created";
                }
                action("Time Delay between MRN-PRN-PO")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Time Delay";
                }
            }
            group(PMS)
            {
                action("PMS Voucher")
                {
                    Caption = 'PMS Voucher';
                    ApplicationArea = all;
                    RunObject = page "PMS Voucher List";
                }
            }

        }
    }
    var
        myInt: Integer;
}