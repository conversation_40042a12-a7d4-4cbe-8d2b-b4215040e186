pageextension 50150 ProdJour extends "Production Journal"
{
    layout
    {
        modify("External Document No.")
        {
            Visible = true;
            ApplicationArea = all;
        }
        addafter(Quantity)
        {
            field("Production Batch No."; "Production Batch No.")
            {
                ApplicationArea = all;
            }
            field("Last Direct Cost"; "Last Direct Cost")
            {
                ApplicationArea = all;

            }
            field("Unit Cost"; "Unit Cost")
            {
                ApplicationArea = all;

            }
            field(Amount; Amount)
            {
                ApplicationArea = all;

            }
            field("Available Qty"; "Available Qty")
            {
                ApplicationArea = all;

            }
        }
    }

    actions
    {
        addafter(ItemTrackingLines)
        {
            action("Assign Item Tracking")
            {
                ApplicationArea = all;
                trigger OnAction()
                var
                    myInt: Integer;
                begin
                    AssignItemTracking();
                end;
            }
        }
        modify("&Print")
        {
            Visible = false;//PKONJ21
        }
        //BaluonMar7 2022>>
        modify("Post and &Print")
        {
            Visible = false;//PKONJ21
            trigger OnBeforeAction()
            var
                ItemJournalLrec: Record "Item Journal Line";
            begin
                //TestField("External Document No.");
                ItemJournalLrec.Reset();
                ItemJournalLrec.SetRange("Document No.", "Document No.");
                ItemJournalLrec.SetRange("Journal Template Name", "Journal Template Name");
                ItemJournalLrec.SetRange("Journal Batch Name", "Journal Batch Name");
                if ItemJournalLrec.FindSet() then
                    repeat
                        if ItemJournalLrec."External Document No." = '' then
                            Error('Please Enter External Document Number');
                    //ItemJournalLrec.TestField("External Document No.");
                    until ItemJournalLrec.Next() = 0;
            end;
        }
        modify(Post)
        {
            trigger OnBeforeAction()
            var
                ItemJournalLrec: Record "Item Journal Line";
            begin
                //TestField("External Document No.");
                ItemJournalLrec.Reset();
                ItemJournalLrec.SetRange("Document No.", "Document No.");
                ItemJournalLrec.SetRange("Journal Template Name", "Journal Template Name");
                ItemJournalLrec.SetRange("Journal Batch Name", "Journal Batch Name");
                if ItemJournalLrec.FindSet() then
                    repeat
                        if ItemJournalLrec."External Document No." = '' then
                            Error('Please Enter External Document No.');
                    //ItemJournalLrec.TestField("External Document No.");
                    until ItemJournalLrec.Next() = 0;
            end;
        }
        //BaluonMar7 2022<<
    }
    procedure AssignItemTracking()
    var
        ItemJournalLines: Record "Item Journal Line";
        Item: Record Item;
        ReservationEntry: Record "Reservation Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;
        ReservationEntry2: Record "Reservation Entry";
    begin
        ItemJournalLines.Reset();
        ItemJournalLines.SetRange("Journal Template Name", "Journal Template Name");
        ItemJournalLines.SetRange("Journal Batch Name", "Journal Batch Name");
        ItemJournalLines.SetRange("Document No.", "Document No.");
        ItemJournalLines.SetRange("Entry Type", ItemJournalLines."Entry Type"::Consumption);
        if ItemJournalLines.FindSet() then
            repeat
                if Item.Get(ItemJournalLines."Item No.") and (Item."Item Tracking Code" <> '') then begin

                    LineQty := ItemJournalLines."Quantity (Base)";

                    ReservationEntry2.RESET;
                    IF ReservationEntry2.FINDLAST THEN
                        EntryNum := ReservationEntry2."Entry No." + 1
                    ELSE
                        EntryNum := 1;
                    ReservationEntry2.Reset();
                    ReservationEntry2.SetRange("Source ID", ItemJournalLines."Journal Template Name");
                    ReservationEntry2.SetRange("Source Subtype", 5);
                    ReservationEntry2.SetRange("Source Type", DATABASE::"Item Journal Line");
                    ReservationEntry2.SetRange("Source Ref. No.", ItemJournalLines."Line No.");
                    ReservationEntry2.SetRange("Source Batch Name", ItemJournalLines."Journal Batch Name");
                    if ReservationEntry2.FindSet() then
                        ReservationEntry2.DeleteAll();
                    ItemLedgerEntry.RESET;
                    ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
                    ItemLedgerEntry.SETRANGE("Item No.", ItemJournalLines."Item No.");
                    ItemLedgerEntry.SETRANGE("Location Code", ItemJournalLines."Location Code");
                    ItemLedgerEntry.SETRANGE("Variant Code", ItemJournalLines."Variant Code");
                    ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
                    IF ItemLedgerEntry.FINDSET THEN
                        REPEAT

                            IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                                AssignQty := LineQty;
                                LineQty := 0;
                            END ELSE BEGIN
                                AssignQty := (ItemLedgerEntry."Remaining Quantity");
                                LineQty -= AssignQty;
                            END;


                            ReservationEntry.INIT;
                            ReservationEntry."Entry No." := EntryNum;
                            ReservationEntry.VALIDATE(Positive, FALSE);
                            ReservationEntry.VALIDATE("Item No.", ItemJournalLines."Item No.");
                            ReservationEntry.VALIDATE("Location Code", ItemJournalLines."Location Code");
                            ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                            ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / ItemJournalLines."Qty. per Unit of Measure"));
                            ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                            ReservationEntry.VALIDATE("Creation Date", WorkDate());
                            ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
                            ReservationEntry.VALIDATE("Source Subtype", 5);
                            ReservationEntry.VALIDATE("Source ID", ItemJournalLines."Journal Template Name");

                            ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLines."Line No.");
                            ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                            ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                            ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                            ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                            ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                            ReservationEntry.Validate("Source Batch Name", ItemJournalLines."Journal Batch Name");
                            ReservationEntry."Created By" := USERID;
                            ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                            ReservationEntry.VALIDATE(Correction, FALSE);
                            ReservationEntry.INSERT;
                            EntryNum += 1;
                        until (ItemLedgerEntry.Next() = 0) OR (LineQty = 0);
                end;
            until ItemJournalLines.Next() = 0;

    end;

    var
        myInt: Integer;
}