pageextension 50079 BankAccountLedger extends "Bank Account Ledger Entries"
{
    layout
    {
        addafter("Bank Account No.")
        {
            field(Narration; Narration)
            {
                ApplicationArea = all;
            }
            field(Narration1; Narration1)
            {
                ApplicationArea = all;
            }
            //PhaniFeb122021>>
            field("Description 2"; "Description 2")
            {
                ApplicationArea = ALL;
            }
            //PhaniFeb122021<<
            //B2B Balu On April 23>>
            field("Teller / Cheque No."; "Teller / Cheque No.")
            {
                ApplicationArea = all;
            }
            field("Teller / Cheque Date"; "Teller / Cheque Date")
            {
                ApplicationArea = all;
            }
            field("Old_Deposit Slip No."; "Old_Deposit Slip No.")
            {
                ApplicationArea = all;
            }
            field("Cheque No."; "Cheque No.")
            {
                ApplicationArea = all;
            }
            field("Document Date"; "Document Date")
            {
                ApplicationArea = all;
            }
            field("Statement No."; "Statement No.")
            {
                ApplicationArea = all;
            }
            field("Bank Acc. Posting Group"; "Bank Acc. Posting Group")
            {
                ApplicationArea = all;
            }
            field("External Document No."; "External Document No.")
            {
                ApplicationArea = all;
            }
            field("Statement Status"; "Statement Status")
            {
                ApplicationArea = all;
            }
            field("Old_Description 2"; "Old_Description 2")
            {
                ApplicationArea = all;
            }

            //B2B Balu On April 23<<
        }

    }
}