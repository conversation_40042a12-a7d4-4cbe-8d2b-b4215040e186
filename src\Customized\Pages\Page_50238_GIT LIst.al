page 50238 "GIT List"
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "GIT Insurance";
    Editable = false;
    CardPageId = "GIT Insurance";

    layout
    {
        area(Content)
        {
            repeater(GIT)
            {
                field("GIT No."; "GIT No.")
                {
                    ApplicationArea = ALL;
                }
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = All;
                }
                field("Vendor Name"; "Vendor Name")
                {
                    ApplicationArea = All;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = All;
                }
                field("Date of Creation"; "Date of Creation")
                {
                    ApplicationArea = All;
                }
                field("Date of GIT"; "Date of GIT")
                {
                    ApplicationArea = All;
                }
                field("Expiration Date"; "Expiration Date")
                {
                    ApplicationArea = All;
                }
                field("Last Modified By"; "Last Modified By")
                {
                    ApplicationArea = All;
                }
                field("Last Modified Date"; "Last Modified Date")
                {
                    ApplicationArea = All;
                }

            }

        }
    }
}
