/// <summary>
/// TableExtension Scratch Pad Entry Ext (ID 50283) extends Record WHI Scratchpad Entry.
/// </summary>
tableextension 50284 "Scratch Pad Entry Ext" extends "WHI Scratchpad Entry"
{
    fields
    {
        //280624
        field(50000; "Licence Plate"; Code[20])
        {
            Caption = 'License Plate';
            DataClassification = ToBeClassified;
            TableRelation = "IWX LP Header";
        }
        // >>>>> G2S 170524 
        field(50001; "Production Order"; Code[20])
        {
            TableRelation = "Production Order";
        }
        //scratch pad entries posting
        field(50002; Duplicate; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(50003; Posted; Boolean)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(50004; "Posted By"; Code[30])
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(50005; "Date Posted"; Date)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(50006; "Posted DateTime"; DateTime)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(50007; "Expiry Date"; Date)
        {
            Editable = false;
            DataClassification = CustomerContent;
        }
        //scratchpad 050724
        //scratch pad entries posting
        //110724
        field(50008; "Released Production Order"; Boolean)
        {
            Editable = false;
            DataClassification = CustomerContent;
        }
    }
}
