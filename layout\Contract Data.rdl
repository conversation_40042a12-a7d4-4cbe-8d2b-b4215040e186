﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>1deadda0-c213-47f6-a295-47d3b86cb8a7</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.15062cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.05021cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.99953cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>4.76199cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>6.66689cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.15604cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.23542cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>5.44967cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!SNoCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VedorTypeCapLbl">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VedorTypeCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VedorTypeCapLbl</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VendorNoCapLbl">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VendorNoCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VendorNoCapLbl</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VendorNameCapLbl">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VendorNameCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VendorNameCapLbl</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VendorAddCapLbl">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VendorAddCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VendorAddCapLbl</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox20">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NoofOpenContractsCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NoofExpContractsCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CommentsCapLbl.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SNo">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!SNo.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>SNo</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Vendor_Type">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Vendor_Type.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Vendor_Type</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="No_">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!No_.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>No_</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Vendor_Name">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Vendor_Name.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Vendor_Name</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Vendor_Address">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Vendor_Address.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Vendor_Address</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CountOpen">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CountOpen.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CountOpen</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CountExpired">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CountExpired.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CountExpired</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CommentText">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CommentText.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CommentText</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember>
                  <Group Name="No_">
                    <GroupExpressions>
                      <GroupExpression>=Fields!No_.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!No_.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details" />
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.10266cm</Top>
            <Left>0cm</Left>
            <Height>1.2cm</Height>
            <Width>27.47037cm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>1.48167cm</Height>
        <Style />
      </Body>
      <Width>27.47037cm</Width>
      <Page>
        <PageHeader>
          <Height>3.00982cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Image Name="Image1">
              <Source>Database</Source>
              <Value>=First(Fields!CompanyPicture.Value, "DataSet_Result")</Value>
              <MIMEType>image/bmp</MIMEType>
              <Sizing>FitProportional</Sizing>
              <Top>0.09028in</Top>
              <Left>0.155in</Left>
              <Height>0.94792in</Height>
              <Width>1.10517in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
            <Textbox Name="Textbox1">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!TitleCaptionLbl.Value</Value>
                      <Style>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>2.25107cm</Top>
              <Left>11.02819cm</Left>
              <Height>0.6cm</Height>
              <Width>5.86021cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyName.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>0.22931cm</Top>
              <Left>10.24503cm</Left>
              <Height>0.67938cm</Height>
              <Width>7.44771cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox4">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!Company_Address.Value, "DataSet_Result")</Value>
                      <Style />
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>1.01441cm</Top>
              <Left>11.02819cm</Left>
              <Height>0.6cm</Height>
              <Width>5.86021cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>21cm</PageHeight>
        <PageWidth>29.7cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0.5cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="No_">
          <DataField>No_</DataField>
        </Field>
        <Field Name="CompanyPicture">
          <DataField>CompanyPicture</DataField>
        </Field>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
        </Field>
        <Field Name="Company_Address">
          <DataField>Company_Address</DataField>
        </Field>
        <Field Name="Company_Address2">
          <DataField>Company_Address2</DataField>
        </Field>
        <Field Name="TitleCaptionLbl">
          <DataField>TitleCaptionLbl</DataField>
        </Field>
        <Field Name="SNoCapLbl">
          <DataField>SNoCapLbl</DataField>
        </Field>
        <Field Name="VendorNoCapLbl">
          <DataField>VendorNoCapLbl</DataField>
        </Field>
        <Field Name="VedorTypeCapLbl">
          <DataField>VedorTypeCapLbl</DataField>
        </Field>
        <Field Name="VendorNameCapLbl">
          <DataField>VendorNameCapLbl</DataField>
        </Field>
        <Field Name="VendorAddCapLbl">
          <DataField>VendorAddCapLbl</DataField>
        </Field>
        <Field Name="CommentsCapLbl">
          <DataField>CommentsCapLbl</DataField>
        </Field>
        <Field Name="NoofOpenContractsCapLbl">
          <DataField>NoofOpenContractsCapLbl</DataField>
        </Field>
        <Field Name="NoofExpContractsCapLbl">
          <DataField>NoofExpContractsCapLbl</DataField>
        </Field>
        <Field Name="SNo">
          <DataField>SNo</DataField>
        </Field>
        <Field Name="Vendor_Name">
          <DataField>Vendor_Name</DataField>
        </Field>
        <Field Name="Vendor_Type">
          <DataField>Vendor_Type</DataField>
        </Field>
        <Field Name="Vendor_Address">
          <DataField>Vendor_Address</DataField>
        </Field>
        <Field Name="CountOpen">
          <DataField>CountOpen</DataField>
        </Field>
        <Field Name="CountExpired">
          <DataField>CountExpired</DataField>
        </Field>
        <Field Name="CommentText">
          <DataField>CommentText</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>