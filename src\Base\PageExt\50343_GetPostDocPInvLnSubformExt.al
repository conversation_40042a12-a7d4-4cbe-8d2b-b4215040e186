pageextension 50343 "Get Post.DocP.InvLnSubform" extends "Get Post.Doc - P.InvLn Subform"
{
    layout
    {
        addafter("Buy-from Vendor No.")
        {
            field("Import File No."; "Import File No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Clearing File No."; "Clearing File No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
        }
    }

    actions
    {
        // Add changes to page actions here
    }

    var
        myInt: Integer;
}