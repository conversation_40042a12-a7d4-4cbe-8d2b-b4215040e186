tableextension 50006 LocTabExt12 extends Location
{
    fields
    {
        field(50000; "Pur. Posted Invoice Nos."; Code[20])
        {
            Caption = 'Pur. Posted Invoice Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50001; "Pur. Posted Credit Memo Nos."; Code[20])
        {
            Caption = 'Pur. Posted Credit Memo Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50004; "Pur. Posted Receipt Nos."; Code[20])
        {
            AccessByPermission = TableData "Purch. Rcpt. Header" = R;
            Caption = 'Pur. Posted Receipt Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50005; "Pur.Posted Return Shpt. Nos."; Code[20])
        {
            AccessByPermission = TableData "Return Shipment Header" = R;
            Caption = 'Pur.Posted Return Shpt. Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50006; "Sal. Posted Invoice Nos."; Code[20])
        {
            Caption = 'Sal. Posted Invoice Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50007; "Sal. Posted Credit Memo Nos."; Code[20])
        {
            Caption = 'Sal. Posted Credit Memo Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50008; "Sal. Posted Shipment Nos."; Code[20])
        {
            AccessByPermission = TableData "Sales Shipment Header" = R;
            Caption = 'Sal. Posted Shipment Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50009; "Sal.Posted Return Receipt Nos."; Code[20])
        {
            Caption = 'Sal. Posted Return Receipt Nos.';
            TableRelation = "No. Series";
            DataClassification = CustomerContent;
        }
        field(50010; "DMS Customer"; Boolean)
        {
            Caption = 'DMS Customer';
            DataClassification = CustomerContent;

        }
        field(50011; "Blocked"; Boolean)
        {
            DataClassification = CustomerContent;
            Caption = 'Blocked';
        }

        field(50012; "Return Journal Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50013; "Accounting location"; code[20])
        {
            CalcFormula = Lookup("Responsibility Center"."Global Dimension 1 Code" WHERE(Code = FIELD("Responsibility Center")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(50014; Active; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Invoicing location"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Location Classification"; enum "Location Classification")
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Responsibility Center"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center";
        }
        field(50018; "Exclude for SNOP"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50020; "Transport Location"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Destination;
        }

        field(50023; "Rejection Location"; code[20])
        {
            DataClassification = ToBeClassified;
            Caption = 'Rejection Location';
            TableRelation = Location.Code;
            //Balu 05122021>>
            trigger OnValidate()
            var
                LocationLRec: Record Location;
            begin
                IF LocationLRec.GET("Rejection Location") THEN
                    LocationLRec.TestField(Blocked, false);
            end;
            //Balu 05122021<<
        }
        field(50024; "Shortage Location"; code[20])
        {
            DataClassification = ToBeClassified;
            Caption = 'Shortage Location';
            TableRelation = Location.Code;
            //Balu 0512021>>
            trigger OnValidate()
            var
                LocationLRec: Record Location;
            begin
                IF LocationLRec.GET("Shortage Location") THEN
                    LocationLRec.TestField(Blocked, false);
            end;

            //Balu 0512021<<
        }

        field(50050; "Subcontractor No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor;
            Description = 'SUBCON1.0';
        }
        field(50129; "Whse. Ship Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50130; "Posted Whse. Ship Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50131; "Whse. Rcpt Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50132; "Posted Whse. Rcpt Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50134; "Batch Assign"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = 'FEFO,LEFO';
            OptionMembers = FEFO,LEFO;
        }
        field(50135; "Ho ware house"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50136; "SNOP Location Capacity"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50137; Shopfloor; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50138; "Branch Request No Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50140; "E-Mail2"; Text[80])
        {
            Caption = 'Email2';
            ExtendedDatatype = EMail;

            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
            begin
                MailManagement.ValidateEmailAddressField("E-Mail2");
            end;
        }
        field(50141; "E-Mail3"; Text[80])
        {
            Caption = 'Email3';
            ExtendedDatatype = EMail;
            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
            begin
                MailManagement.ValidateEmailAddressField("E-Mail3");
            end;
        }
        field(50150; "Direct SO Location"; Boolean)
        {
            DataClassification = CustomerContent;
            //b2bpksalecorr11
        }
        field(50143; "SCD Approved Email 1"; Text[80])
        {
            ExtendedDatatype = EMail;
            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
            begin
                MailManagement.ValidateEmailAddressField("SCD Approved Email 1");
            end;
        }
        field(50144; "SCD Approved Email 2"; Text[80])
        {
            ExtendedDatatype = EMail;
            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
            begin
                MailManagement.ValidateEmailAddressField("SCD Approved Email 2");
            end;
        }
        field(50145; "SCD Approved Email 3"; Text[80])
        {
            ExtendedDatatype = EMail;
            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
            begin
                MailManagement.ValidateEmailAddressField("SCD Approved Email 2");
            end;
        }
        //Baluon11Jul2022>>
        field(50146; "Upcountry Warehouse"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //Baluon11Jul2022<<


        //B2BESGOn26NOv2022>>
        field(50147; "M Trade"; Code[20])
        {
            DataClassification = ToBeClassified;
            TableRelation = "Dimension Value".Code WHERE("Dimension Code" = Filter('ACCLOC'));
            ValidateTableRelation = false;
        }
        //B2BESGOn26NOv2022<<
        field(50148; "Retail Shop Location"; Boolean)
        {
            DataClassification = CustomerContent;

        }//B2BSPON22OCT25
        field(50149; "Bad Location"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(50151; "Production Location"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
    }
}