pageextension 50011 CustLedgEntry25 extends "Customer Ledger Entries"
{
    layout
    {
        addafter("Document Type")
        {
            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = All;
            }
            field(Narration; Narration)
            {
                ApplicationArea = all;
            }
            field(Narration1; Narration1)
            {
                ApplicationArea = all;
            }
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
            field("Teller / Cheque No."; "Teller / Cheque No.")
            {
                ApplicationArea = all;
            }
            field("Paid By"; "Paid By")
            {
                ApplicationArea = all;
            }
            field("Product Group Code"; "Product Group Code")
            {
                ApplicationArea = all;
            }
            field("business unit code"; "business unit code")
            {
                ApplicationArea = all;
            }
            field("Sales Rebate Period"; "Sales Rebate Period")//PKONAU9
            {
                ApplicationArea = all;
            }
        }
        modify("Reason Code")//PKONAU9
        {
            Visible = true;
        }
    }
}