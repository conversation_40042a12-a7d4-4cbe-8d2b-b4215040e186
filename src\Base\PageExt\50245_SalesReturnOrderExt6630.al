pageextension 50245 SalesReturnOrder extends "Sales Return Order"
{
    layout
    {
        // Add changes to page layout here
        addafter("Assigned User ID")
        {
            field("Loading Slip Required"; "Loading Slip Required")
            {
                ApplicationArea = All;
            }
            field(Narration; Narration)
            {
                ApplicationArea = All; // G2S CAS-01433-X1Y9V1-10092
            }

        }
        modify("Bill-to Name")//PKONDE16.2
        {
            Editable = false;
        }
        modify("Bill-to Contact")//BaluonMar7 2022
        {
            Editable = false;
        }
    }

    actions
    {
        // Add changes to page actions here

        //RFC#2024-08 >>>>
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField(Narration, ''); // G2S CAS-01433-X1Y9V1-10092
                CheckMandValues;
            end;
        }
        //RFC#2024-08 <<<<
    }

    var
        myInt: Integer;
}