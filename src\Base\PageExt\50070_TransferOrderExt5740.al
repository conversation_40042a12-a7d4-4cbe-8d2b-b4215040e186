pageextension 50070 TransferOrder extends "Transfer Order"
{
    layout
    {
        modify(Status)
        {
            Visible = false;
        }
        addafter("Transfer-to Code")
        {
            field("Transfer Type"; "Transfer Type")
            {
                ApplicationArea = ALL;

                trigger OnValidate()
                begin
                    if ("Production Batch No." <> '') or ("Branch Request No" <> '') then
                        Error('You can not modify this while order created from request.');
                end;
                //Editable = EditTransferType;
            }
            field("Approval Status"; "Approval Status")
            {
                Editable = false;
                ApplicationArea = all;
            }
            field("Loading Advice Printed"; "Loading Advice Printed")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Production Order No."; "Production Order No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Production Batch No."; "Production Batch No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Branch Request No"; "Branch Request No")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Maintenance Job Card No"; "Maintenance Job Card No")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Manual MRS No."; "Manual MRS No.")
            {
                ApplicationArea = all;
            }
            field("order status"; "order status")
            {
                ApplicationArea = all;
            }
            //B2BMS
            field("Created By"; "Created By")
            {
                ApplicationArea = All;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = All;
            }
            field("Modified By"; "Modified By")
            {
                ApplicationArea = All;
            }
            field("Modified date"; "Modified date")
            {
                ApplicationArea = All;
            }
            //B2BMS
            field("Released By"; "Released By")
            {
                ApplicationArea = all;
            }//b2bspon22oct18
        }


    }
    actions
    {
        modify("Re&lease")
        {
            Visible = false;
        }
        modify("Reo&pen")
        {
            Visible = false;
        }
        modify(Post)
        {
            trigger OnBeforeAction()
            begin
                Testfield("Shortcut Dimension 1 Code");//PKONJ28
                Testfield("Shortcut Dimension 2 Code");//PKONJ28
                CheckLineDimensions();
                Testfield("Approval Status", "Approval Status"::Released);
                PostingBool := TRUE;
                //B2B-Pk on 13.05.2021
                IF "Transfer Type" = "Transfer Type"::"Transfer Ticket" then
                    TestField("Manual MRS No.");
                //B2B-Pk on 13.05.2021
            end;
        }
        addafter(Dimensions)
        {
            action("Get Gate Entry Lines")
            {
                Caption = 'Get Gate Entry Lines';
                ApplicationArea = all;
                trigger OnAction()
                begin
                    GetGateEntryLines;
                end;
            }
            action("Attached Gate Entry")
            {
                Caption = 'Attached Gate Entry';
                ApplicationArea = all;
                Image = InwardEntry;
                RunObject = page "Gate Entry Attachment List";
                RunPageLink = "Source Type" = const("Transfer Receipt"), "Entry Type" = const(Inward), "Source No." = field("No.");
            }
        }
        addafter(GetReceiptLines)
        {
            action("Create Branch Loading Advice")
            {
                Image = Report;
                ApplicationArea = all;
                trigger OnAction()
                begin

                    TestField("Transfer Type", "Transfer Type"::"Branch Request");
                    usersetup.Get(UserId);
                    if "Loading Advice Printed" AND NOT usersetup."Reprint Branch Report" then
                        Error('Loading Slip Report is Already generated');
                    TransLine.RESET;
                    TransLine.SETRANGE("Document No.", "No.");
                    REPORT.RUN(50021, TRUE, TRUE, TransLine);
                END;
                /*
                IF "Loading Advice Printed" THEN BEGIN
                    IF usersetup.GET(USERID) THEN BEGIN
                        IF usersetup."Reprint Dispatch" THEN BEGIN
                            TransLine.RESET;
                            TransLine.SETRANGE("Document No.", "No.");
                            REPORT.RUN(50021, TRUE, TRUE, TransLine);
                        END;
                    END;
                END;
                */

            }
            action("Send Br. Req. Notification")
            {
                Visible = false;
                Image = SendMail;
                ApplicationArea = all;
                trigger OnAction()
                begin
                    TestField("Transfer Type", "Transfer Type"::"Branch Request");
                    sendmail.SendAppBrStkRequestAlertFromTF(Rec, Email);
                    Message('Sent Successfully');
                end;
            }
        }
        addafter("P&osting")
        {
            action("Releases")
            {
                ApplicationArea = all;
                //Caption = 'Re&lease';
                ShortCutKey = 'Ctrl+F11';
                Image = ReleaseDoc;
                trigger OnAction()
                var
                    TransferLine: Record "Transfer Line";
                    Validation: Codeunit Validations;
                begin
                    "Released By" := UserId;//b2bspon22oct18
                    Testfield("Shortcut Dimension 1 Code");//PKONJ28
                    Testfield("Shortcut Dimension 2 Code");//PKONJ28
                    CheckLineDimensions();
                    //PKON052421
                    TransferLine.Reset();
                    TransferLine.SetRange("Document No.", "No.");
                    if TransferLine.FindSet() then
                        repeat
                            TransferLine.TestField("Shortcut Dimension 1 Code");//PKONDE22
                            TransferLine.TestField("Shortcut Dimension 2 Code");//PKONDE22
                            Validation.CheckQtyValidations(TransferLine);
                        until TransferLine.Next() = 0;
                    //PKON052421
                    //B2B-Pk on 13.05.2021
                    IF "Transfer Type" = "Transfer Type"::"Transfer Ticket" then
                        TestField("Manual MRS No.");
                    TESTFIELD("Approval Status", "Approval Status"::Open); //PKON22AU19
                    //B2B-Pk on 13.05.2021
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendTROforApprovalCode()) then
                        error('Workflow is enabled. You can not release manually.');

                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        //Validate(Status, Status::Released);PK on 12.28.2020
                        CODEUNIT.RUN(Codeunit::"Release Transfer Document", rec);
                        Modify();
                        Message('Document has been Released.');
                    end;
                end;
            }
            action("Re&open")
            {
                ApplicationArea = all;
                //Caption = 'Re&open';
                Image = ReOpen;
                trigger OnAction();
                var
                    WhseTransferRelease: codeunit "Release Transfer Document";
                begin
                    if "order status" = "order status"::"Short close" then
                        Error('Order Already shortclosed');
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 5740);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        //Validate(Status, Status::Open);PK on 12.28.2020
                        WhseTransferRelease.Reopen(rec);
                        Validate(Status, Status::Open);
                        //RunModal(Codeunit::"Release Transfer Document", rec);
                        Modify();
                        Message('Document has been Reopened.');
                    end;
                end;
            }

            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                //Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;//PKONSE22
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                Var
                    TransferLine: Record "Transfer Line";
                    Validation: Codeunit Validations;
                begin
                    Testfield("Shortcut Dimension 1 Code");//PKONJ28
                    Testfield("Shortcut Dimension 2 Code");//PKONJ28
                    CheckLineDimensions();
                    if "order status" = "order status"::"Short close" then
                        Error('Order Already shortclosed');
                    //PKON052421
                    TransferLine.Reset();
                    TransferLine.SetRange("Document No.", "No.");
                    if TransferLine.FindSet() then
                        repeat
                            TransferLine.TestField("Shortcut Dimension 1 Code");//PKONDE22
                            TransferLine.TestField("Shortcut Dimension 2 Code");//PKONDE22
                            Validation.CheckQtyValidations(TransferLine);
                        until TransferLine.Next() = 0;
                    //PKON052421
                    //B2B-Pk on 13.05.2021
                    IF "Transfer Type" = "Transfer Type"::"Transfer Ticket" then
                        TestField("Manual MRS No.");
                    //B2B-Pk on 13.05.2021
                    IF allinoneCU.CheckTROApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendTROForApproval(Rec);
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelTROForApproval(Rec);
                end;
            }
            action("Generate Bin Reclasification Report")
            {
                ApplicationArea = All;
                Image = Report;
                trigger OnAction()
                var
                    Binrecjnl: Record "Bin Reclassfication Jnl";
                begin
                    //50476
                    Binrecjnl.Reset();
                    Binrecjnl.SetRange("Document Type", Binrecjnl."Document Type"::Transfer);
                    Binrecjnl.SetRange("Document No.", "No.");
                    IF Binrecjnl.FindSet() then
                        Report.RunModal(50476, true, false, Binrecjnl);

                end;
            }
            // Balu 05252021>>
            action("Short Close")
            {
                Image = Close;
                ApplicationArea = all;
                Caption = 'Short Close';
                trigger OnAction()
                var
                    TransferLine: Record "Transfer Line";
                    WareHouseshpntLn: Record "Warehouse Shipment Line";
                    warehousespnthdr: Record "Warehouse Shipment Header";
                    userset: Record "User Setup";
                begin
                    IF Not userset.GET(UserId) then
                        Error('User Setup not defined.');
                    IF Not userset."Short Close Trans. Order" then
                        Error('You dont have any permisssions to short close the order.');
                    TransferLine.Reset();
                    TransferLine.SetRange("Document No.", "No.");
                    if TransferLine.FindSet() then
                        repeat
                            if TransferLine."Quantity Shipped" <> TransferLine."Quantity Received" then
                                Error('Shipped Quantity must be received for item No. %1', TransferLine."Item No.");
                        until TransferLine.Next() = 0;
                    IF Not Confirm('Do you want to short Close', True, False) then
                        exit;
                    TransferLine.Reset();
                    TransferLine.SetRange("Document No.", "No.");
                    if TransferLine.FindSet() then
                        repeat
                            WareHouseshpntLn.Reset();
                            WareHouseshpntLn.SetRange("Source No.", TransferLine."Document No.");
                            WareHouseshpntLn.SetRange("Source Line No.", TransferLine."Line No.");
                            if WareHouseshpntLn.FindFirst() then begin
                                if warehousespnthdr.Get(WareHouseshpntLn."No.") then
                                    warehousespnthdr.Delete();
                                WareHouseshpntLn.Delete();
                            end;
                            TransferLine.Validate(Quantity, TransferLine."Quantity Shipped");
                            TransferLine.Modify();
                        until TransferLine.Next() = 0;
                    "order status" := "order status"::"Short close";
                    Modify();
                end;
            }
            // Balu 05252021<<

        }
        modify("Create Whse. S&hipment")
        {
            trigger OnBeforeAction()
            var
                Loca: Record Location;
                Ware: Record "Warehouse Setup";
                TransferLine: Record "Transfer Line";
                Validation: Codeunit Validations;
            begin
                if "order status" = "order status"::"Short close" then
                    Error('Order Already shortclosed');
                //PKON052421
                TransferLine.Reset();
                TransferLine.SetRange("Document No.", "No.");
                if TransferLine.FindSet() then
                    repeat
                        Validation.CheckQtyValidations(TransferLine);
                    until TransferLine.Next() = 0;
                //PKON052421
                //B2B-Pk on 13.05.2021
                IF "Transfer Type" = "Transfer Type"::"Transfer Ticket" then
                    TestField("Manual MRS No.");
                //B2B-Pk on 13.05.2021
                TestField("Approval Status", "Approval Status"::Released);
                Loca.GET("Transfer-from Code");
                Loca.TestField("Whse. Ship Nos.");
                Loca.TestField("Posted Whse. Ship Nos.");

                Ware.get();
                Ware."Whse. Ship Nos." := Loca."Whse. Ship Nos.";
                Ware."Posted Whse. Shipment Nos." := Loca."Posted Whse. Ship Nos.";
                Ware.Modify();
                Commit();
            end;
        }
        modify("Create &Whse. Receipt")
        {
            trigger OnBeforeAction()
            var
                Loca: Record Location;
                Ware: Record "Warehouse Setup";
            begin
                if "order status" = "order status"::"Short close" then
                    Error('Order Already shortclosed');
                //B2B-Pk on 13.05.2021
                IF "Transfer Type" = "Transfer Type"::"Transfer Ticket" then
                    TestField("Manual MRS No.");
                //B2B-Pk on 13.05.2021
                TestField("Approval Status", "Approval Status"::Released);
                Loca.GET("Transfer-To Code");
                Loca.TestField("Whse. Rcpt Nos.");
                Loca.TestField("Posted Whse. Rcpt Nos.");

                Ware.get();
                Ware."Whse. Receipt Nos." := Loca."Whse. Rcpt Nos.";
                Ware."Posted Whse. Receipt Nos." := Loca."Posted Whse. Rcpt Nos.";
                Ware.Modify();
                Commit();
                if "Transfer Type" = "Transfer Type"::"Transfer Ticket" then
                    TestField("Manual MRS No.");
            end;
        }
    }
    trigger OnAfterGetRecord()

    BEGIN
        //G2S 11/06/24
        if Rec."No." <> '' then begin
            if Rec.FromBranchReqst then isEditable := false else isEditable := true;
        end else
            isEditable := true;

        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
        //if "Transfer Type" <> "Transfer Type"::" " then
        if ("Production Batch No." <> '') or ("Branch Request No" <> '') then
            EditTransferType := false
        else
            EditTransferType := true;
    END;

    trigger OnModifyRecord(): Boolean
    BEGIN
        IF PostingBool = FALSE THEN
            TestField("Approval Status", "Approval Status"::Open);
        clear(PostingBool);
    END;

    var
        isEditable: Boolean;
        EditTransferType: Boolean;
        TransLine: Record "Transfer Line";
        RecordRest: record "Restricted Record";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        usersetup: Record "User Setup";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        sendmail: Codeunit "Send Mail";
        Email: Text[100];
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit Codeunit1;
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        PostingBool: Boolean;
    //B2BBalu0nJuly6
    procedure CheckLineDimensions()
    var
        TransferLine: Record "Transfer Line";
    begin
        TransferLine.Reset();
        TransferLine.SetRange("Document No.", "No.");
        if TransferLine.FindSet() then
            repeat
                TestField("Shortcut Dimension 1 Code");
                TestField("Shortcut Dimension 2 Code");
            until TransferLine.Next() = 0;
    end;
    //B2BBalu0nJuly6
}
