tableextension 50086 GLBudgetEntryTabExt extends "G/L Budget Entry"
{
    fields
    {
        // Add changes to table fields here
    }

    trigger OnModify()
    BEGIN
        TestStatusOpen;
    END;

    trigger OnDelete()
    BEGIN
        TestStatusOpen;
    END;

    trigger OnInsert()
    BEGIN
        TestStatusOpen;
    END;

    local procedure TestStatusOpen()
    var
        GLBudgetNameLVar: Record "G/L Budget Name";
    begin
        GLBudgetNameLVar.Get("Budget Name");
        GLBudgetNameLVar.TestField("Approval Status", GLBudgetNameLVar."Approval Status"::Open);
    end;
}