//>>>>>> 16/8/24 CAS-01340-V7R3W7 Posted Journal Voucher
report 50201 "Posted Batch Voucher"
{

    DefaultLayout = RDLC;
    RDLCLayout = 'src\Customized\G2S\Reports\Layouts\Posted Batch Voucher.rdl';
    ApplicationArea = all;
    UsageCategory = ReportsAndAnalysis;
    Caption = 'Posted_Batched_Voucher_50201';
    dataset
    {
        dataitem("Posted Voucher Header"; "Posted Voucher Header")
        {
            RequestFilterFields = "Document No.";
            DataItemTableView = ORDER(descending);
            //WHERE("Debit Amount" = FILTER(<> 0));
            //"Voucher Type" = FILTER(JV)
            column(DocumentNoCaption; "Document No.")
            {

            }

            column(AccountNameCaption; "Account Name")
            {

            }
            column(Narration; Narration)
            {

            }
            column(ReportCaption; ReportCaption)
            {

            }

            /*column(CreditAmountCaption; "Credit Amount")
            {

            }*/
            column(Posted_By; "Posted By")
            {

            }
            column(Created_By; "Created By")
            {

            }
            column(ApporvedBy; ApporvedBy)
            {

            }
            dataitem("G/L Entry"; "G/L Entry")
            {
                DataItemLink = "Document No." = FIELD("Document No.");
                column(SourceDesc_____Voucher_; SourceDesc)
                {
                }
                column(CreditAmountCaption; "Credit Amount")
                {

                }

                column(G_L_Entry__Document_No__; "Document No.")
                {
                }
                column(Date______FORMAT__Posting_Date__; 'Date: ' + FORMAT("Posting Date"))
                {
                }
                column(CompanyInformation_Address_____CompanyInformation__Address_2___________CompanyInformation_City; CompanyInformation.Address + ' ' + CompanyInformation."Address 2" + '  ' + CompanyInformation.City)
                {
                }
                column(CompanyInformation_Name; CompanyInformation.Name)
                {
                }
                column(CompanyInformation_Picture; CompanyInformation.Picture)
                {

                }
                column(Posting_Date; "Posting Date")
                {

                }
                column(GLAccName; GLAccName)
                {
                }
                column(CrText; CrText)
                {
                }
                column(DebitAmountTotal; DebitAmountTotal)
                {
                }
                column(CreditAmountTotal; CreditAmountTotal)
                {
                }
                column(G_L_Entry__Debit_Amount_; "Debit Amount")
                {
                }
                column(PostedNarration1_Narration; Narration)
                {
                }
                column(Posted_Voucher_Header__Credit_Amount_; "Credit Amount")
                {
                }
                column(Posted_Voucher_Header__Debit_Amount_; "Debit Amount")
                {
                }
                column(Rs____NumberText_1_______NumberText_2_; AmountInword)
                {
                }
                column(PassBy; PassBy)
                {
                }
                column(AuthorisedBy; AuthorisedBy)
                {
                }
                column(Voucher_No___Caption; Voucher_No___CaptionLbl)
                {
                }
                column(Credit_AmountCaption; Credit_AmountCaptionLbl)
                {
                }
                column(Debit_AmountCaption; Debit_AmountCaptionLbl)
                {
                }
                column(ParticularsCaption; ParticularsCaptionLbl)
                {
                }
                column(Narration__Caption; Narration__CaptionLbl)
                {
                }
                column(Amount__in_words__Caption; Amount__in_words__CaptionLbl)
                {
                }
                column(Prepared_by_Caption; Prepared_by_CaptionLbl)
                {
                }
                column(Checked_by_Caption; Checked_by_CaptionLbl)
                {
                }
                column(Approved_by_Caption; Approved_by_CaptionLbl)
                {
                }
                column(Posted_Voucher_Header_Voucher_Type; '')
                {
                }

                column(G_L_Entry__Credit_Amount_; "Debit Amount")
                {
                }
                column(G_L_Entry_Description; Description)
                {
                }
                column(DebitAmountTotal_Control1000000028; DebitAmountTotal)
                {
                }
                column(CreditAmountTotal_Control1000000029; CreditAmountTotal)
                {
                }
                column(DrText; DrText)
                {
                }
                column(Continued______Caption; Continued______CaptionLbl)
                {
                }
                column(G_L_Entry_Entry_No_; "Entry No.")
                {
                }
                column(G_L_Entry_Document_No_; "Document No.")
                {
                }
                column(ChequeNo; ChequeNo)
                {

                }
                column(G_L_Account_No_; "G/L Account No.")
                {

                }
                column(G_L_Account_Name; "G/L Account Name")
                {

                }
                column(GLAccNo; GLAccNo)
                {

                }
                dataitem(Integer; Integer)
                {
                    DataItemTableView = SORTING(Number);
                    column(IntegerOccurcesCaption; IntegerOccurcesCaptionLbl)
                    {
                    }
                    column(Integer_Number; Number)
                    {
                    }

                    trigger OnPreDataItem();
                    begin

                        GenJrnlLine.SETCURRENTKEY("Entry No.");
                        GenJrnlLine.ASCENDING(FALSE);
                        GenJrnlLine.SETRANGE("Posting Date", "G/L Entry"."Posting Date");
                        GenJrnlLine.SETRANGE("Document No.", "G/L Entry"."Document No.");
                        GenJrnlLine.FINDLAST;
                        IF GenJrnlLine."Entry No." <> "G/L Entry"."Entry No." THEN
                            CurrReport.BREAK;

                        SETRANGE(Number, 1, PageLoop)
                    end;
                }

                trigger OnAfterGetRecord()
                var
                    GLAccount: Record "G/L Account";
                    Customer: Record Customer;
                    Vendor: Record Vendor;
                    Check: Report Check;
                    FixedAsset: Record "Fixed Asset";
                    BankAccount: Record "Bank Account";
                begin
                    Clear(GLAccName);
                    SlNo += 1;
                    DrText := 'Dr';
                    CrText := 'To';
                    SourceDesc := '';
                    NUMLines := 13;
                    PageLoop := NUMLines;
                    LinesPrinted := 0;

                    PageLoop := PageLoop - 1;
                    LinesPrinted := LinesPrinted + 1;

                    IF "G/L Entry"."Source Code" <> '' THEN BEGIN
                        SourceCode.GET("G/L Entry"."Source Code");
                        SourceDesc := SourceCode.Description;
                    END;
                    //FIX22Jun2021>>
                    if "Source Type" = "Source Type"::" " then
                        if GLAccount.Get("G/L Entry"."G/L Account No.") then begin
                            GLAccName := GLAccount.Name;
                            GLAccNo := "G/L Account No.";
                        end;
                    if "G/L Entry"."Source Type" = "G/L Entry"."Source Type"::Vendor then
                        if Vendor.Get("G/L Entry"."Source No.") then begin
                            GLAccName := Vendor.Name;
                            GLAccNo := "Source No.";
                        end;

                    if "G/L Entry"."Source Type" = "G/L Entry"."Source Type"::Customer then
                        if Customer.Get("G/L Entry"."Source No.") then begin
                            GLAccName := Customer.Name;
                            GLAccNo := "Source No.";
                        end;
                    if "G/L Entry"."Source Type" = "G/L Entry"."Source Type"::"Fixed Asset" then
                        if FixedAsset.Get("G/L Entry"."Source No.") then begin
                            GLAccName := FixedAsset.Description;
                            GLAccNo := "Source No.";
                        end;
                    if "G/L Entry"."Source Type" = "G/L Entry"."Source Type"::"Bank Account" then
                        if BankAccount.Get("G/L Entry"."Source No.") then begin
                            GLAccName := BankAccount.Name;
                            GLAccNo := "Source No.";
                        end;
                    //FIX22Jun2021<<
                    CreditAmountTotal := 0;
                    DebitAmountTotal := 0;
                    PostedDate1 := "G/L Entry"."Posting Date";
                    if PostedDate1 <> PostedDate2 then begin
                        Clear(AmountInword);
                        clear(TotAmt);
                        GLENtry.RESET;
                        GLENtry.SetCurrentKey("Posting Date");
                        GLENtry.SetRange("Posting Date", "G/L Entry"."Posting Date");
                        GLENtry.SetRange("Document No.", "G/L Entry"."Document No.");
                        IF GLENtry.findset then begin
                            repeat
                                TotAmt += ABS(GLENtry."Debit Amount");
                                DebitAmountTotal += GLENtry."Debit Amount";
                                CreditAmountTotal += GLENtry."Credit Amount";
                            until GLENtry.next = 0;
                            AmountInword := numconv.figure(TotAmt, 'NAIRA', 'KOBO');
                            AmountInword := AmountInword + ' ONLY';
                            PostedDate2 := GLENtry."Posting Date";
                        end;
                    end;

                    /*CLEAR(Check);
                    Check.InitTextVariable;
                    Check.FormatNoText(AmountInword, ROUND(DebitAmountTotal, 0.01, '='), '');*/
                end;

                trigger OnPreDataItem()
                begin
                    //"G/L Entry".SetCurrentKey("Entry No.");
                end;

            }
            trigger OnAfterGetRecord()
            var
                ApprovalEntry: Record "Approval Entry";
                PostedAppEntry: Record "Posted Approval Entry";
            BEGIN
                AppEnt.RESET;
                AppEnt.SETRANGE("Document No.", "Document No.");
                IF AppEnt.FINDLAST THEN BEGIN
                    PassBy := AppEnt."Sender ID";
                    AuthorisedBy := AppEnt."Approver ID";

                END;
                Clear(ReportCaption);
                if "JV Type" = "JV Type"::Bank then
                    ReportCaption := 'Bank Journal';
                if "JV Type" = "JV Type"::Sales then
                    ReportCaption := 'Sales Journal';
                if "JV Type" = "JV Type"::Purchase then
                    ReportCaption := 'Purchase Journal';
                if "JV Type" = "JV Type"::General then
                    ReportCaption := 'General Journal';
                // clear(TotAmt);
                // GLENtry.RESET;
                // GLENtry.SetRange("Document No.", "Document No.");
                // IF GLENtry.findset then
                //     repeat
                //         TotAmt += ABS(GLENtry."Debit Amount");
                //     until GLENtry.next = 0;
                // Message(Format(TotAmt));
                // AmountInword := numconv.figure(TotAmt, 'NAIRA', 'KOBO');
                // AmountInword := AmountInword + ' ONLY';


                ApprovalEntry.Reset();
                ApprovalEntry.SetRange("Document No.", "Voucher No.");
                ApprovalEntry.SetRange(Status, ApprovalEntry.Status::Approved);
                if not ApprovalEntry.FindLast() then begin
                    PostedAppEntry.Reset();
                    PostedAppEntry.SetRange("Document No.", "Document No.");
                    PostedAppEntry.SetRange(Status, PostedAppEntry.Status::Approved);
                    if PostedAppEntry.FindLast() then
                        ApporvedBy := PostedAppEntry."Approver ID";
                end else
                    ApporvedBy := ApprovalEntry."Approver ID";
            END;
        }
    }

    requestpage
    {

        layout
        {
        }

        actions
        {
        }
    }

    labels
    {
    }

    var
        CompanyInformation: Record 79;
        SourceCode: Record 230;
        GenJrnlLine: Record 17;
        PostedVoucherHeader: Record "Posted Voucher Header";
        GLAccName: Text[50];
        GLENtry: Record "G/L Entry";
        TotAmt: Decimal;
        PostedDate1, PostedDate2 : Date;
        SlNo: Integer;
        SourceDesc: Text[50];
        NoText: array[20] of Text;
        CrText: Text[2];
        DrText: Text[2];
        NumberText: array[2] of Text[80];
        PageLoop: Integer;
        LinesPrinted: Integer;
        NUMLines: Integer;
        ChequeNo: Code[50];
        ChequeDate: Date;
        OnesText: array[20] of Text[30];
        TensText: array[10] of Text[30];
        ExponentText: array[5] of Text[30];
        PrintLineNarration: Boolean;
        PostingDate: Date;
        TotalDebitAmt: Decimal;
        DocumentNo: Code[20];
        DebitAmountTotal: Decimal;
        CreditAmountTotal: Decimal;
        PrePostingDate: Date;
        PreDocumentNo: Code[50];
        ToWords: Codeunit 50001;
        PassBy: Code[10];
        AuthorisedBy: Code[10];
        AppEnt: Record 456;
        AmountInword: Text[250];
        Voucher_No___CaptionLbl: Label 'Voucher No. :';
        Credit_AmountCaptionLbl: Label 'Credit Amount';
        Debit_AmountCaptionLbl: Label 'Debit Amount';
        ParticularsCaptionLbl: Label 'Particulars';
        Narration__CaptionLbl: Label 'Narration :';
        Amount__in_words__CaptionLbl: Label 'Amount (in words):';
        Prepared_by_CaptionLbl: Label 'Prepared by:';
        Checked_by_CaptionLbl: Label 'Checked by:';
        Approved_by_CaptionLbl: Label 'Approved by:';
        Continued______CaptionLbl: Label 'Continued......';
        IntegerOccurcesCaptionLbl: Label 'IntegerOccurces';
        numconv: Codeunit NumbertoText;
        ReportCaption: Text;
        ApporvedBy: Text[50];
        GLAccNo: Code[20];

    trigger onprereport();
    begin
        CompanyInformation.GET;
        CompanyInformation.CalcFields(Picture);
        DebitAmountTotal := 0;
        CreditAmountTotal := 0;
        PostedDate1 := 0D;
        PostedDate2 := 0D;
    end;
}
//>>>>>> 16/8/24 CAS-01340-V7R3W7 Posted Journal Voucher