page 50634 "Bank Acc. Reconciliation-PK"
{
    // version XMLport,NAVW16.00.01, BNKRECON

    // Flag Values 2: "Not Matching", 1:Probable, 0:Exact

    Caption = 'Bank Acc. Reconciliation';
    PageType = Document;
    Permissions = TableData "Bank Account" = rm;
    SaveValues = false;
    SourceTable = "Bank Acc. Reconciliation";
    RefreshOnActivate = true;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Bank Account No."; "Bank Account No.")
                {

                }
                field("Statement No."; "Statement No.")
                {

                }
                field("Statement Date"; "Statement Date")
                {

                }
                field("Balance Last Statement"; "Balance Last Statement")
                {

                }
                group(Outstanding)
                {
                    Caption = 'Outstanding';
                    field("Statement Credit"; "Statement Credit")
                    {
                        Caption = 'Statement Credit';

                    }
                }
                field("Statement Ending Balance"; "Statement Ending Balance")
                {

                }/*
                field("Global Dimension 9 Code"; "Global Dimension 9 Code")
                {

                }*///PK
                field("Out. Debit Balance Navision"; "Out. Debit Balance Navision")
                {

                    Caption = 'Navision Debit';
                }
                field("Out. Credit Balance Navision"; "Out. Credit Balance Navision")
                {

                    Caption = '"Navision Credit "';
                }
                field("Statement Debit"; "Statement Debit")
                {

                    Caption = '"Statement Debit "';
                }/*
                field("Bank Statement Debit Total"; "Bank Statement Debit Total")
                {

                }
                field("Bank Statement Credit Total"; "Bank Statement Credit Total")
                {

                }*///PK
                field("Outstanding Entry Downloaded"; "Outstanding Entry Downloaded")
                {

                    Editable = false;
                }
                field("Opening Balance BANK"; "Opening Balance BANK")
                {

                }
                field("Debit Balance Statement"; "Out. Debit Balance Statement")
                {
                    ApplicationArea = ALL;
                    Caption = 'Bank Statement Debit';
                }

                field("Credit Balance Statement"; "Out. Credit Balance Statement")
                {
                    ApplicationArea = ALL;
                    Caption = 'Bank Statement Credit';
                }
                field("Closing Balance BANK"; "Closing Balance BANK")
                {

                    Editable = false;
                }


                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = all;
                }
            }
            group(Control8)
            {
                ShowCaption = false;
                part(StmtLine; "Bank Acc. Reconciliati LinesPK")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Bank Statement Lines';
                    SubPageLink = "Bank Account No." = FIELD("Bank Account No."),
                                  "Statement No." = FIELD("Statement No.");
                }
                part(BankStatementLine; "Bank Upload Sheet")
                {
                    SubPageLink = Code = FIELD("Bank Account No."), "Statement No." = FIELD("Statement No.");
                    SubPageView = SORTING(Code) ORDER(Ascending);

                }

            }
            /*field(Tot; LeftSideTotal + RightSideTotal)
            {
                Visible = true;
                Editable = false;
                ApplicationArea = all;
            }*/
        }

    }

    actions
    {
        area(navigation)
        {
            group("&Recon.")
            {
                Caption = '&Recon.';
                action("&Card")
                {

                    Caption = '&Card';
                    RunObject = Page "Bank Account Card";
                    RunPageLink = "No." = FIELD("Bank Account No.");
                    ShortCutKey = 'Shift+F5';
                }
                separator(Separator1000000027)
                {
                }
                action("Outstanding Data Navision")
                {

                    Caption = 'Outstanding Data Navision';
                    RunObject = Page "BRS Outstanding Data";
                    RunPageLink = Type = FILTER(Navision), "Bank No" = FIELD("Bank Account No.");
                }
                action("Outstanding Data stalemant")
                {

                    Caption = 'Outstanding Data stalemant';
                    RunObject = Page "BRS Outstanding Data";
                    RunPageLink = Type = FILTER(Statement), "Bank No" = FIELD("Bank Account No.");
                }
                action("Refresh")
                {
                    Promoted = true;
                    PromotedIsBig = true;
                    Image = Refresh;
                    PromotedCategory = Process;
                    trigger OnAction()
                    begin
                        CurrPage.Update();
                    end;
                }
            }
            group("&Matches")
            {
                Caption = '&Matches';
                action(UploadBankStatement)
                {
                    trigger OnAction()
                    var
                        Bankstatement: XmlPort "Bank Statement Import";
                    BEGIN
                        CLEAR(Bankstatement); // added sanjoy paul ********
                        Bankstatement.GetBankNo("Bank Account No.", "Statement No.");
                        Bankstatement.Run();
                    END;
                }
                action("&Exact Matched Entries")
                {

                    Caption = '&Exact Matched Entries';
                    ShortCutKey = 'Ctrl+d';

                    trigger OnAction();
                    begin
                        /*
                        ReconLineFilter.RESET;
                        ReconLineFilter.SETRANGE(ReconLineFilter."Matching Status", 0);
                        CurrPage.StmtLine.Page.SETTABLEVIEW(ReconLineFilter);
                        //OrgBnkStatementFilter.SETCURRENTKEY("Matched Line No.");
                        OrgBnkStatementFilter.SETRANGE(OrgBnkStatementFilter."Matching Status", 0);
                        CurrPage.BankStatementLine.Page.SETTABLEVIEW(OrgBnkStatementFilter);*/
                        CurrPage.StmtLine.Page.GetMatchFil(0);
                        CurrPage.BankStatementLine.Page.GetMatchFil(0);
                    end;
                }
                action("&Probable Matched Entries")
                {
                    Caption = '&Probable Matched Entries';

                    trigger OnAction();
                    begin
                        /*
                        ReconLineFilter.RESET;
                        ReconLineFilter.SETRANGE(ReconLineFilter."Matching Status", 1);
                        CurrPage.StmtLine.Page.SETTABLEVIEW(ReconLineFilter);
                        OrgBnkStatementFilter.RESET;
                        OrgBnkStatementFilter.SETRANGE(OrgBnkStatementFilter."Matching Status", 1);
                        CurrPage.BankStatementLine.Page.SETTABLEVIEW(OrgBnkStatementFilter);*/
                        CurrPage.StmtLine.Page.GetMatchFil(1);
                        CurrPage.BankStatementLine.Page.GetMatchFil(1);
                    end;
                }
                action("&Not Matched Entries")
                {
                    Caption = '&Not Matched Entries';

                    trigger OnAction();
                    begin
                        /*
                        ReconLineFilter.RESET;
                        ReconLineFilter.SETRANGE(ReconLineFilter."Matching Status", 2);
                        IF ReconLineFilter.findset then
                            CurrPage.StmtLine.Page.SETTABLEVIEW(ReconLineFilter);
                        OrgBnkStatementFilter.RESET;
                        OrgBnkStatementFilter.SETRANGE(OrgBnkStatementFilter."Matching Status", 2);
                        IF OrgBnkStatementFilter.FINDSET THEN
                            CurrPage.BankStatementLine.Page.SETTABLEVIEW(OrgBnkStatementFilter);
                        Currpage.Update();
                        Message('Updated.')*/
                        CurrPage.StmtLine.Page.GetMatchFil(2);
                        CurrPage.BankStatementLine.Page.GetMatchFil(2);
                    end;
                }
                action("&Manually Matched Entries") // G2S-2023-10-16 7708-CAS-01421-Z6M7V9
                {
                    Caption = '&Manually Matched Entries';

                    trigger OnAction();
                    begin
                        CurrPage.StmtLine.Page.GetMatchFil(4);
                        CurrPage.BankStatementLine.Page.GetMatchFil(4);
                    end;
                }
                action("Cancel Entries")
                {
                    Caption = 'Cancel Entries';

                    trigger OnAction();
                    begin
                        /*ReconLineFilter.RESET;
                        ReconLineFilter.SETRANGE(ReconLineFilter."Matching Status", ReconLineFilter."Matching Status"::Cancel);
                        CurrPage.StmtLine.Page.SETTABLEVIEW(ReconLineFilter);
                        OrgBnkStatementFilter.SETRANGE(OrgBnkStatementFilter."Matching Status", OrgBnkStatementFilter."Matching Status"::Cancel);
                        CurrPage.BankStatementLine.Page.SETTABLEVIEW(OrgBnkStatementFilter);
                        Currpage.Update();*/
                        CurrPage.StmtLine.Page.GetMatchFil(3);
                        CurrPage.BankStatementLine.Page.GetMatchFil(3);
                    end;
                }
                action("Unselect Entries")
                {
                    Caption = 'Unselect Entries';

                    trigger OnAction();
                    var
                        BankReconLine: Record "Bank Acc. Reconciliation Line";
                        OriginalBankData: Record "Original Bank Statement";
                    begin
                        BankReconLine.Reset();
                        BankReconLine.SetRange("Statement No.", "Statement No.");
                        BankReconLine.SetRange("Bank Account No.", "Bank Account No.");
                        BankReconLine.SetRange(Select, true);
                        if BankReconLine.FindSet() then
                            BankReconLine.ModifyAll(Select, false);
                        OriginalBankData.Reset();
                        OriginalBankData.SetRange("Statement No.", "Statement No.");
                        OriginalBankData.SetRange(Code, "Bank Account No.");
                        OriginalBankData.SetRange(Select, true);
                        if OriginalBankData.FindSet() then
                            OriginalBankData.ModifyAll(Select, false);
                    end;
                }
                action("select All Entries")
                {
                    Caption = 'Select All Entries';

                    trigger OnAction();
                    var
                        BankReconLine: Record "Bank Acc. Reconciliation Line";
                        OriginalBankData: Record "Original Bank Statement";
                    begin
                        BankReconLine.Reset();
                        BankReconLine.SetRange("Statement No.", "Statement No.");
                        BankReconLine.SetRange("Bank Account No.", "Bank Account No.");
                        BankReconLine.SetRange("Matching Status", BankReconLine."Matching Status"::"Not Matching");
                        if BankReconLine.FindSet() then
                            BankReconLine.ModifyAll(Select, true);
                        OriginalBankData.Reset();
                        OriginalBankData.SetRange("Statement No.", "Statement No.");
                        OriginalBankData.SetRange(Code, "Bank Account No.");
                        OriginalBankData.SetRange("Matching Status", OriginalBankData."Matching Status"::"Not Matching");
                        if OriginalBankData.FindSet() then
                            OriginalBankData.ModifyAll(Select, true);
                    end;
                }
                separator(Separator1000000016)
                {
                }
                action("Show &All Entries")
                {
                    Caption = 'Show &All Entries';


                    trigger OnAction();
                    begin
                        /*
                        ReconLineFilter.RESET;
                        ReconLineFilter.SETRANGE(ReconLineFilter."Matching Status");
                        CurrPage.StmtLine.Page.SETTABLEVIEW(ReconLineFilter);
                        OrgBnkStatementFilter.SETRANGE(OrgBnkStatementFilter."Matching Status");
                        CurrPage.BankStatementLine.Page.SETTABLEVIEW(OrgBnkStatementFilter);
                        Currpage.Update();*/
                        CurrPage.StmtLine.Page.GetMatchFil(5);
                        CurrPage.BankStatementLine.Page.GetMatchFil(5);
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                separator(Separator1000000004)
                {
                }
                action("Remove All Matching")
                {
                    Caption = 'Remove All Matching';

                    trigger OnAction();
                    begin
                        IF CONFIRM('Do you want to unmatch all the entries', FALSE) THEN BEGIN
                            AreaRec.RESET;
                            AreaRec.SETRANGE(Code, "Bank Account No.");
                            AreaRec.SETRANGE("Statement No.", "Statement No.");
                            IF AreaRec.FINDFIRST THEN
                                REPEAT
                                    AreaRec.Matching := FALSE;
                                    AreaRec."Matching Status" := AreaRec."Matching Status"::"Not Matching";
                                    AreaRec."Matched Line No." := 0;
                                    AreaRec."MatchLine No" := 0;
                                    AreaRec."Cancel No" := 0;
                                    AreaRec.MODIFY;
                                UNTIL AreaRec.NEXT = 0;

                            ReconLine.RESET;
                            ReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
                            ReconLine.SETRANGE("Statement No.", "Statement No.");
                            IF ReconLine.FINDFIRST THEN
                                REPEAT
                                    ReconLine."Cross Matching" := FALSE;
                                    ReconLine."Matching Status" := ReconLine."Matching Status"::"Not Matching";
                                    ReconLine."MatchLine No" := 0;

                                    ReconLine.MODIFY;
                                UNTIL ReconLine.NEXT = 0;

                            MESSAGE('All entries are successfully unmatched');
                        END;
                    end;
                }
                action("Remove Matching")
                {
                    Caption = 'Remove Matching';

                    trigger OnAction();
                    var
                        BnkReconLine: Record "Bank Acc. Reconciliation Line";
                        OgnlBnkStatement: Record "Original Bank Statement";
                        DeleteBnkReconLine: Record "Bank Acc. Reconciliation Line";
                        DeleteOgnlBnkStatement: Record "Original Bank Statement";
                    begin
                        IF CONFIRM('Do you want to unmatch selected the entries', FALSE) THEN BEGIN
                            CurrPage.StmtLine.Page.SelectedEntries(BnkReconLine);
                            CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);

                            /*BnkReconLine.SETFILTER(BnkReconLine."Matching Status",'<>%1|<>%2',0,1);  // 0:exact Match, 1:Probable
                            IF BnkReconLine.FINDFIRST THEN
                               ERROR('Please select Exact and  Probable match only from the Rigtht side');
                            OgnlBnkStatement.SETFILTER(OgnlBnkStatement."Matching Status",'<>%1|<>%2',0,1);  // 0:exact Match, 1:Probable
                            IF OgnlBnkStatement.FINDFIRST THEN
                               ERROR('Please select Exact and  Probable match only from the Left side');
                            */

                            IF BnkReconLine.COUNT >= OgnlBnkStatement.COUNT THEN BEGIN                 // MAX lines selection
                                IF BnkReconLine.FINDSET THEN
                                    REPEAT
                                        DeleteBnkReconLine.SETRANGE("Bank Account No.", BnkReconLine."Bank Account No.");
                                        DeleteBnkReconLine.SETRANGE("Statement No.", BnkReconLine."Statement No.");
                                        DeleteBnkReconLine.SETRANGE("MatchLine No", BnkReconLine."MatchLine No");
                                        IF DeleteBnkReconLine.FINDSET THEN BEGIN
                                            DeleteBnkReconLine.MODIFYALL(DeleteBnkReconLine."Matching Status", DeleteBnkReconLine."Matching Status"::"Not Matching");
                                            DeleteBnkReconLine.MODIFYALL("Cross Matching", FALSE);
                                            DeleteBnkReconLine.MODIFYALL(DeleteBnkReconLine."MatchLine No", 0);

                                        END;
                                        DeleteOgnlBnkStatement.SETRANGE(Code, BnkReconLine."Bank Account No.");
                                        DeleteOgnlBnkStatement.SETRANGE("Statement No.", BnkReconLine."Statement No.");
                                        DeleteOgnlBnkStatement.SETRANGE("MatchLine No", BnkReconLine."MatchLine No");
                                        IF DeleteOgnlBnkStatement.FINDSET THEN BEGIN
                                            DeleteOgnlBnkStatement.MODIFYALL(Matching, FALSE);
                                            DeleteOgnlBnkStatement.MODIFYALL("Cancel No", 0);
                                            DeleteOgnlBnkStatement.MODIFYALL(DeleteOgnlBnkStatement."Matching Status",
                                                                             DeleteOgnlBnkStatement."Matching Status"::"Not Matching");

                                            DeleteOgnlBnkStatement.MODIFYALL(DeleteOgnlBnkStatement."MatchLine No", 0);
                                        END;

                                    UNTIL BnkReconLine.NEXT = 0
                            END
                            ELSE
                                IF OgnlBnkStatement.FINDSET THEN
                                    REPEAT
                                        DeleteOgnlBnkStatement.SETRANGE(Code, OgnlBnkStatement.Code);
                                        DeleteOgnlBnkStatement.SETRANGE("Statement No.", OgnlBnkStatement."Statement No.");
                                        DeleteOgnlBnkStatement.SETRANGE("MatchLine No", OgnlBnkStatement."MatchLine No");
                                        IF DeleteOgnlBnkStatement.FINDSET THEN BEGIN
                                            DeleteOgnlBnkStatement.MODIFYALL(Matching, FALSE);
                                            DeleteOgnlBnkStatement.MODIFYALL("Cancel No", 0);
                                            DeleteOgnlBnkStatement.MODIFYALL(DeleteOgnlBnkStatement."Matching Status",
                                                                               DeleteOgnlBnkStatement."Matching Status"::"Not Matching");
                                            DeleteOgnlBnkStatement.MODIFYALL(DeleteOgnlBnkStatement."MatchLine No", 0);
                                        END;
                                        DeleteBnkReconLine.SETRANGE("Bank Account No.", OgnlBnkStatement.Code);
                                        DeleteBnkReconLine.SETRANGE("Statement No.", OgnlBnkStatement."Statement No.");
                                        DeleteBnkReconLine.SETRANGE("MatchLine No", OgnlBnkStatement."MatchLine No");
                                        IF DeleteBnkReconLine.FINDSET THEN BEGIN

                                            DeleteBnkReconLine.MODIFYALL("Cross Matching", FALSE);
                                            DeleteBnkReconLine.MODIFYALL(DeleteBnkReconLine."Matching Status", DeleteBnkReconLine."Matching Status"::"Not Matching");
                                            DeleteBnkReconLine.MODIFYALL(DeleteBnkReconLine."MatchLine No", 0);
                                        END;

                                    UNTIL OgnlBnkStatement.NEXT = 0
                        END;

                    end;
                }
                action("Reverse Cancellation (Bank)")
                {
                    Caption = 'Reverse Cancellation (Bank)';

                    trigger OnAction();
                    var
                        OgnlBnkStatement: Record "Original Bank Statement";
                        DeleteOgnlBnkStatement: Record "Original Bank Statement";
                    begin
                        IF CONFIRM('Do you want to reverse selected the Bank cancel entries', FALSE) THEN BEGIN
                            CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);
                            OgnlBnkStatement.SETRANGE(OgnlBnkStatement."Cancel No", 0);
                            IF OgnlBnkStatement.FINDFIRST THEN
                                ERROR('Please select cancel lines only');
                            OgnlBnkStatement.SETRANGE(OgnlBnkStatement."Cancel No");

                            IF OgnlBnkStatement.FINDSET THEN
                                REPEAT
                                    DeleteOgnlBnkStatement.SETRANGE(Code, OgnlBnkStatement.Code);
                                    DeleteOgnlBnkStatement.SETRANGE("Statement No.", OgnlBnkStatement."Statement No.");
                                    DeleteOgnlBnkStatement.SETRANGE("Cancel No", OgnlBnkStatement."Cancel No");
                                    IF DeleteOgnlBnkStatement.FINDSET THEN BEGIN
                                        DeleteOgnlBnkStatement.MODIFYALL("Matching Status", DeleteOgnlBnkStatement."Matching Status"::"Not Matching");
                                        DeleteOgnlBnkStatement.MODIFYALL(DeleteOgnlBnkStatement."Cancel No", 0);
                                    END;
                                UNTIL OgnlBnkStatement.NEXT = 0
                        END;
                    end;
                }
                separator(Separator1000000041)
                {
                }
                action("Reverse Cancellation (Navision)")
                {
                    Caption = 'Reverse Cancellation (Navision)';

                    trigger OnAction();
                    var
                        BankReconciliationLine: Record "Bank Acc. Reconciliation Line";
                        DeleteReconciliationLine: Record "Bank Acc. Reconciliation Line";
                    begin
                        IF CONFIRM('Do you want to reverse selected the Navision cancel entries', FALSE) THEN BEGIN
                            CurrPage.StmtLine.Page.SelectedEntries(BankReconciliationLine);
                            BankReconciliationLine.SETRANGE(BankReconciliationLine."Cancel No", 0);
                            IF BankReconciliationLine.FINDFIRST THEN
                                ERROR('Please select cancel lines only');
                            BankReconciliationLine.SETRANGE(BankReconciliationLine."Cancel No");

                            IF BankReconciliationLine.FINDSET THEN
                                REPEAT
                                    DeleteReconciliationLine.SETRANGE("Bank Account No.", BankReconciliationLine."Bank Account No.");
                                    DeleteReconciliationLine.SETRANGE("Statement No.", BankReconciliationLine."Statement No.");
                                    DeleteReconciliationLine.SETRANGE("Cancel No", BankReconciliationLine."Cancel No");
                                    IF DeleteReconciliationLine.FINDSET THEN BEGIN
                                        DeleteReconciliationLine.MODIFYALL("Matching Status", DeleteReconciliationLine."Matching Status"::"Not Matching");
                                        DeleteReconciliationLine.MODIFYALL(DeleteReconciliationLine."Cancel No", 0);
                                    END;
                                UNTIL DeleteReconciliationLine.NEXT = 0
                        END;
                    end;
                }
                separator(Separator1000000014)
                {
                }
                action("Apply Matched Entries")
                {
                    Caption = 'Apply Matched Entries';




                    trigger OnAction();
                    begin
                        IF CONFIRM('Do you want to Apply Matched Entries for Bank = %1, Statement No. = %2', FALSE, "Bank Account No.",
                        "Statement No.") THEN BEGIN
                            AreaRec.RESET;
                            AreaRec.SETRANGE(Code, "Bank Account No.");
                            AreaRec.SETRANGE("Statement No.", "Statement No.");
                            AreaRec.SETRANGE(Matching, TRUE);
                            AreaRec.SETRANGE(AreaRec."Mapped with BLE", FALSE);
                            AreaRec.SETFILTER("Matched Line No.", '<>%1', 0);
                            IF AreaRec.FINDFIRST THEN
                                REPEAT
                                    BankLedgR.RESET;
                                    BankLedgR.SETRANGE("Bank Account No.", AreaRec.Code);
                                    //BankLedgR.SETRANGE("Document No.",AreaRec."Bank Doc. No.");
                                    BankLedgR.SETRANGE("Posting Date", AreaRec."Bank Posting Date");
                                    IF BankLedgR.FINDFIRST THEN
                                        REPEAT
                                            IF ((AreaRec."Bank Doc. No." = BankLedgR."Document No.")) AND
                                            ((AreaRec."Bank Posting Date" = BankLedgR."Posting Date")) AND
                                            ((AreaRec."Bank Narration") = (BankLedgR.Description + '' + BankLedgR."Description 2" + '' + BankLedgR."Description 3")) AND
                                            ((AreaRec."Bank Debit Amount" = ABS(BankLedgR."Debit Amount")) OR (
                                            (AreaRec."Bank Credit Amount" = ABS(BankLedgR."Credit Amount")))) THEN BEGIN
                                                BankLedgR."Statement Status" := BankLedgR."Statement Status"::"Bank Acc. Entry Applied";
                                                BankLedgR.VALIDATE("Statement No.", AreaRec."Statement No.");
                                                BankLedgR.VALIDATE("Statement Line No.", AreaRec."Matched Line No.");
                                                BankLedgR.MODIFY;
                                                AreaRec."Mapped with BLE" := TRUE;
                                                AreaRec."Mapped with BLE Entry No." := BankLedgR."Entry No.";
                                                AreaRec.MODIFY;
                                            END ELSE
                                                IF ((STRSUBSTNO(AreaRec."Bank Doc. No.") = STRSUBSTNO(BankLedgR."Document No.")) AND
                                                ((AreaRec."Bank Posting Date" = BankLedgR."Posting Date")) OR
                                             ((STRSUBSTNO(AreaRec."Bank Narration")) = STRSUBSTNO(BankLedgR.Description +
                                             BankLedgR."Description 2" + BankLedgR."Description 3"))) AND
                                              ((AreaRec."Bank Debit Amount" = ABS(BankLedgR."Debit Amount")) OR (
                                              (AreaRec."Bank Credit Amount" = ABS(BankLedgR."Credit Amount"))))
                                              THEN BEGIN
                                                    BankLedgR."Statement Status" := BankLedgR."Statement Status"::"Bank Acc. Entry Applied";
                                                    BankLedgR.VALIDATE("Statement No.", AreaRec."Statement No.");
                                                    BankLedgR.VALIDATE("Statement Line No.", AreaRec."Matched Line No.");
                                                    BankLedgR.MODIFY;
                                                    AreaRec."Mapped with BLE" := TRUE;
                                                    AreaRec."Mapped with BLE Entry No." := BankLedgR."Entry No.";
                                                    AreaRec.MODIFY;
                                                END ELSE BEGIN
                                                    AreaRec."Mapped with BLE" := FALSE;
                                                    AreaRec."Mapped with BLE Entry No." := 0;
                                                    AreaRec.MODIFY;
                                                END;
                                        UNTIL BankLedgR.NEXT = 0;
                                UNTIL AreaRec.NEXT = 0;
                        END;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Delete Right Side All data")
                {
                    Caption = 'Delete Right Side All data';


                    trigger OnAction();
                    var
                        recOriginalBnk: Record "Original Bank Statement";
                        UserSetup: Record "User Setup";
                    begin
                        UserSetup.GET(USERID);
                        IF NOT UserSetup."Delete RBS Right side data" THEN
                            ERROR('You dont have the permission to delete the data from right side ! Contact you system administrator');


                        IF CONFIRM('Do you want to delete all the entries from right side', FALSE) THEN BEGIN
                            recOriginalBnk.SETRANGE(recOriginalBnk.Code, "Bank Account No.");
                            recOriginalBnk.SETRANGE(recOriginalBnk."Statement No.", "Statement No.");
                            IF recOriginalBnk.FINDFIRST THEN
                                REPEAT
                                    recOriginalBnk.DELETE;
                                UNTIL recOriginalBnk.NEXT = 0;
                        END;
                    end;
                }
                separator(Separator1000000054)
                {
                }
                action("Calculate Statement Ending Balance")
                {
                    Caption = 'Calculate Statement Ending Balance';

                    trigger OnAction();
                    var
                        recOriginalBnk: Record "Original Bank Statement";
                        ClosingBal: Decimal;
                    begin
                        ClosingBal := 0;
                        recOriginalBnk.SETRANGE(recOriginalBnk.Code, "Bank Account No.");
                        recOriginalBnk.SETRANGE(recOriginalBnk."Statement No.", "Statement No.");
                        recOriginalBnk.SETRANGE(recOriginalBnk."Outstanding Entry", FALSE);
                        IF recOriginalBnk.FINDFIRST THEN
                            REPEAT
                                ClosingBal += -recOriginalBnk."Bank Debit Amount" + recOriginalBnk."Bank Credit Amount"
                            UNTIL recOriginalBnk.NEXT = 0;
                        "Closing Balance BANK" := ClosingBal + "Opening Balance BANK";
                        MODIFY;
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Download Outstanding entries")
                {
                    Caption = 'Download Outstanding entries';

                    trigger OnAction();
                    begin
                        TESTFIELD("Outstanding Entry Downloaded", FALSE);
                        DownloadOutstandingEntries;
                        "Outstanding Entry Downloaded" := TRUE;
                        MODIFY;
                    end;
                }
                separator(Separator1000000046)
                {
                }
                action("Suggest Lines")
                {
                    Caption = 'Suggest Lines';

                    Ellipsis = true;
                    ShortCutKey = 'Shift+F4';

                    trigger OnAction();
                    begin
                        SuggestBankAccStatement.SetStmt(Rec);
                        SuggestBankAccStatement.RUNMODAL;
                        CLEAR(SuggestBankAccStatement);
                        //PKON22JA4>>
                        /*
                        AreaRec.RESET;
                        AreaRec.SETRANGE(Code, "Bank Account No.");
                        AreaRec.SETRANGE("Statement No.", "Statement No.");
                        IF AreaRec.FINDFIRST THEN
                            REPEAT
                                AreaRec.Matching := FALSE;
                                AreaRec."Matching Status" := AreaRec."Matching Status"::"Not Matching";
                                AreaRec."Matched Line No." := 0;
                                AreaRec."MatchLine No" := 0;
                                AreaRec."Cancel No" := 0;
                                AreaRec.MODIFY;
                            UNTIL AreaRec.NEXT = 0;

                        ReconLine.RESET;
                        ReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
                        ReconLine.SETRANGE("Statement No.", "Statement No.");
                        IF ReconLine.FINDFIRST THEN
                            REPEAT
                                ReconLine."Cross Matching" := FALSE;
                                ReconLine."Matching Status" := ReconLine."Matching Status"::"Not Matching";
                                ReconLine."MatchLine No" := 0;

                                ReconLine.MODIFY;
                            UNTIL ReconLine.NEXT = 0;*/
                        //PKON22JA4<<
                    end;
                }
                action("Transfer to General Journal")
                {
                    Caption = 'Transfer to General Journal';

                    Ellipsis = true;
                    Visible = false;

                    trigger OnAction();
                    begin
                        TransferToGLJnl.SetBankAccRecon(Rec);
                        TransferToGLJnl.RUN;
                    end;
                }
                separator(Separator1000000042)
                {
                }
                action("Cancel Navision Data")
                {
                    Caption = 'Cancel Navision Data';


                    trigger OnAction();
                    var
                        NavBnkStatement: Record "Bank Acc. Reconciliation Line";
                        NavBnkStatementMaxCancelNo: Record "Bank Acc. Reconciliation Line";
                        LastNoInternal: Integer;
                        Total: Decimal;
                        CreditAmount: Decimal;
                    begin
                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        //CurrPage.StmtLine.Page.SelectedEntries(NavBnkStatement);
                        NavBnkStatement.Reset();
                        NavBnkStatement.SetRange("Statement No.", "Statement No.");
                        NavBnkStatement.SetRange("Bank Account No.", "Bank Account No.");
                        NavBnkStatement.SetRange(Select, true);
                        IF NavBnkStatement.FINDSET THEN
                            REPEAT
                                Total += NavBnkStatement."Statement Amount";
                            UNTIL NavBnkStatement.NEXT = 0
                        else
                            Error('No selected lines to cancel the data');

                        IF Total = 0 THEN BEGIN
                            NavBnkStatementMaxCancelNo.RESET;
                            NavBnkStatementMaxCancelNo.SETCURRENTKEY("Cancel No");
                            NavBnkStatementMaxCancelNo.SETRANGE("Bank Account No.", "Bank Account No.");
                            NavBnkStatementMaxCancelNo.SETRANGE("Statement No.", "Statement No.");
                            IF NavBnkStatementMaxCancelNo.FINDLAST THEN
                                LastNoInternal := NavBnkStatementMaxCancelNo."Cancel No";
                        END ELSE BEGIN
                            ERROR('Debit amount and Credit amount is not matching for the selected lines');
                        END;

                        NavBnkStatement.MODIFYALL(NavBnkStatement."Cancel No", LastNoInternal + 1);
                        NavBnkStatement.MODIFYALL("Matching Status", NavBnkStatement."Matching Status"::Cancel);
                        NavBnkStatement.ModifyAll(Select, false);
                    end;
                }
                separator(Separator1000000001)
                {
                }
                action("Cancel Bank Data")
                {
                    Caption = 'Cancel Bank Data';

                    ShortCutKey = 'Shift+F3';

                    trigger OnAction();
                    var
                        OgnlBnkStatement: Record "Original Bank Statement";
                        OgnlBnkStatementMaxCancelNo: Record "Original Bank Statement";
                        LastNoInternal: Integer;
                        DebitAmount: Decimal;
                        CreditAmount: Decimal;
                    begin
                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        //CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);
                        OgnlBnkStatement.Reset();
                        OgnlBnkStatement.SetRange(Code, "Bank Account No.");
                        OgnlBnkStatement.SetRange("Statement No.", "Statement No.");
                        OgnlBnkStatement.SetRange(Select, true);
                        IF OgnlBnkStatement.FINDSET THEN
                            REPEAT
                                DebitAmount += OgnlBnkStatement."Bank Debit Amount";
                                CreditAmount += OgnlBnkStatement."Bank Credit Amount";
                            UNTIL OgnlBnkStatement.NEXT = 0
                        else
                            Error('No selected lines to cancel');

                        IF DebitAmount = CreditAmount THEN BEGIN
                            OgnlBnkStatementMaxCancelNo.RESET;
                            OgnlBnkStatementMaxCancelNo.SETCURRENTKEY("Cancel No");
                            OgnlBnkStatementMaxCancelNo.SETRANGE(Code, "Bank Account No.");
                            OgnlBnkStatementMaxCancelNo.SETRANGE("Statement No.", "Statement No.");
                            IF OgnlBnkStatementMaxCancelNo.FINDLAST THEN
                                LastNoInternal := OgnlBnkStatementMaxCancelNo."Cancel No";
                        END ELSE BEGIN
                            ERROR('Debit amount and Credit amount is not matching for the selected lines');
                        END;

                        OgnlBnkStatement.MODIFYALL(OgnlBnkStatement."Cancel No", LastNoInternal + 1);
                        OgnlBnkStatement.MODIFYALL("Matching Status", OgnlBnkStatement."Matching Status"::Cancel);
                        OgnlBnkStatement.MODIFYALL(select, false);
                    end;
                }
                separator(Separator1000000039)
                {
                }
                action("Map Manually")
                {
                    Caption = 'Map Manually';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        LineVal: Integer;
                        MatLine: Integer;
                        BnkReconLine: Record "Bank Acc. Reconciliation Line";
                        OgnlBnkStatement: Record "Original Bank Statement";
                        MatchLineNo: Integer;
                        LeftSideTotal: Decimal;
                        "RightSideCr.Total": Decimal;
                        BnkReconLineUpdate: Record "Bank Acc. Reconciliation Line";
                        OgnlBnkStatementUpdate: Record "Original Bank Statement";
                        "RightSideDr.Total": Decimal;
                    begin
                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        //CurrPage.StmtLine.Page.SelectedEntries(BnkReconLine);
                        //CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);
                        MatchLineNo := LastMatchLine();
                        MatchLineNo := MatchLineNo + 1;
                        //BnkReconLineUpdate.COPY(BnkReconLine);
                        //OgnlBnkStatementUpdate.COPY(OgnlBnkStatement);
                        // Total for the left side
                        CLEAR(LeftSideTotal);
                        BnkReconLine.Reset();
                        BnkReconLine.SetRange("Bank Account No.", "Bank Account No.");
                        BnkReconLine.SetRange("Statement No.", "Statement No.");
                        BnkReconLine.SetRange(Select, true);
                        IF BnkReconLine.FindSet() THEN begin
                            BnkReconLineUpdate.COPY(BnkReconLine);
                            REPEAT
                                LeftSideTotal += BnkReconLine."Statement Amount";
                                IF BnkReconLine."Matching Status" <> BnkReconLine."Matching Status"::"Not Matching" THEN  // exact Line checks
                                    ERROR('There is EXACT or PROBABAL or Cancel Line exists, Please unmatch that line and do manual matching ');
                            UNTIL BnkReconLine.NEXT = 0;
                        end else
                            Error('No lines seleted for reconciliation');
                        // Total for the right side
                        CLEAR("RightSideDr.Total");
                        CLEAR("RightSideCr.Total");
                        //OgnlBnkStatement.SETCURRENTKEY(Code);
                        OgnlBnkStatement.Reset();
                        OgnlBnkStatement.SetRange(Select, true);
                        OgnlBnkStatement.SetRange(Code, "Bank Account No.");
                        OgnlBnkStatement.SetRange("Statement No.", "Statement No.");
                        IF OgnlBnkStatement.FindSet() THEN begin
                            OgnlBnkStatementUpdate.COPY(OgnlBnkStatement);
                            REPEAT
                                "RightSideDr.Total" += OgnlBnkStatement."Bank Debit Amount";
                                "RightSideCr.Total" += OgnlBnkStatement."Bank Credit Amount";
                                IF OgnlBnkStatement."Matching Status" <> BnkReconLine."Matching Status"::"Not Matching" THEN  // exact Line checks
                                    ERROR('There is EXACT or PROBABAL or Cancel Line exists, Please unmatch that line and do manual matching ');
                            UNTIL OgnlBnkStatement.NEXT = 0;
                        end else
                            Error('No lines seleted for reconciliation');
                        IF LeftSideTotal = "RightSideCr.Total" - "RightSideDr.Total" THEN BEGIN
                            BnkReconLineUpdate.MODIFYALL("MatchLine No", MatchLineNo);
                            BnkReconLineUpdate.MODIFYALL("Cross Matching", TRUE);
                            BnkReconLineUpdate.MODIFYALL("Matching Status", BnkReconLineUpdate."Matching Status"::"Manually Matched"); // G2S 7708-CAS-01421-Z6M7V9 2023-10-23
                            BnkReconLineUpdate.MODIFYALL("MatchLine No", MatchLineNo);
                            BnkReconLineUpdate.ModifyAll(Select, false);
                            //BnkReconLineUpdate."MatchLine No":=MatchLineNo;
                            // BnkReconLineUpdate.MODIFY;
                            //Update original Original Bank statement Table
                            OgnlBnkStatementUpdate.MODIFYALL("MatchLine No", MatchLineNo);
                            OgnlBnkStatementUpdate.MODIFYALL(Matching, TRUE);
                            OgnlBnkStatementUpdate.MODIFYALL("Matching Status", OgnlBnkStatementUpdate."Matching Status"::"Manually Matched"); // G2S 7708-CAS-01421-Z6M7V9 2023-10-23
                            OgnlBnkStatementUpdate.MODIFYALL("Manually Mapped", TRUE);
                            OgnlBnkStatementUpdate.MODIFYALL("Manually Mapped By", USERID);
                            OgnlBnkStatementUpdate.MODIFYALL("Manually Mapped on", CURRENTDATETIME);
                            OgnlBnkStatementUpdate.ModifyAll(Select, false);
                        END ELSE
                            ERROR('Amount is not matching for both the sides, please check amount and select manual match.');
                    end;
                }
                separator(Separator1000000012)
                {
                }
                action("Match Sheets")
                {
                    Caption = 'Match Sheets';

                    ShortCutKey = 'Shift+F1';

                    trigger OnAction();
                    var
                        MatchLineNo: Integer;
                    begin
                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        IF CONFIRM('Do you want to run automatic match ', FALSE) THEN BEGIN
                            ReconLine.RESET;
                            ReconLine.SETCURRENTKEY("Bank Account No.", "Statement No.", "Statement Line No.");
                            ReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
                            ReconLine.SETRANGE("Cross Matching", FALSE);
                            ReconLine.SETRANGE("Statement No.", "Statement No.");
                            IF ReconLine.FINDFIRST THEN BEGIN
                                Dialog1.OPEN(Text01 +
                                            '@1@@@@@@@@@@@@@@@@@@@@@@@');
                                CountV := ReconLine.COUNT;
                                t := 0;
                                MatchLineNo := LastMatchLine();
                                MatchLineNo := MatchLineNo + 1;

                                REPEAT
                                    //Message('%1...%2', ReconLine."Statement Amount", ReconLine."Matching Status");
                                    IF ReconLine."Statement Amount" = 314270 then;
                                    t := t + 1;
                                    Flag := 2; //added by sanjoy ********
                                    Dialog1.UPDATE(1, ROUND(t * 10000 / CountV, 1));
                                    AreaRec.RESET;
                                    AreaRec.SETCURRENTKEY(Code, "Statement No.", "Line No.");
                                    AreaRec.SETRANGE(Code, ReconLine."Bank Account No.");
                                    AreaRec.SETRANGE("Statement No.", ReconLine."Statement No.");
                                    AreaRec.SETRANGE(Matching, FALSE);
                                    AreaRec.SETRANGE(AreaRec."Cancel No", 0);
                                    IF AreaRec.FINDFIRST THEN
                                        REPEAT
                                            //Fix19Jun2021>>
                                            IF (((AreaRec."Bank Narration" = ReconLine.Description2)) AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                                                                    (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) AND (AreaRec."Bank Posting Date" = ReconLine."Transaction Date")) THEN BEGIN
                                                IF Flag = 2 THEN BEGIN
                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                    AreaRec.Matching := TRUE;
                                                    AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                    ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                    AreaRec.MODIFY;
                                                    ReconLine."Cross Matching" := TRUE;
                                                    ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                    ReconLine.MODIFY;
                                                    MatchLineNo := MatchLineNo + 1;
                                                    Flag := 0;                                                     // added sanjoy ********
                                                END//Fix19Jun2021<<
                                            end else
                                                IF (CheqAndDcoNoMath() AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                                                                    (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) AND (AreaRec."Bank Posting Date" = ReconLine."Transaction Date")) THEN BEGIN
                                                    IF Flag = 2 THEN BEGIN
                                                        AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                        AreaRec.Matching := TRUE;
                                                        AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                        ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                        AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                        AreaRec.MODIFY;
                                                        ReconLine."Cross Matching" := TRUE;
                                                        ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                        ReconLine.MODIFY;
                                                        MatchLineNo := MatchLineNo + 1;
                                                        Flag := 0;                                                     // added sanjoy ********
                                                    END;
                                                END else//FIX06Jul2021>>
                                                    IF ((AreaRec."Bank Narration" = ReconLine.Description2) AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                                                               (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) AND (AreaRec."Bank Posting Date" <> ReconLine."Transaction Date")) THEN BEGIN
                                                        IF Flag = 2 THEN BEGIN
                                                            AreaRec."Matching Status" := AreaRec."Matching Status"::Probable;
                                                            AreaRec.Matching := true;
                                                            AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                            ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                            AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                            AreaRec.MODIFY;
                                                            ReconLine."Cross Matching" := true;
                                                            ReconLine."Matching Status" := ReconLine."Matching Status"::Probable; // Added sanjoy paul ********
                                                            ReconLine.MODIFY;
                                                            MatchLineNo := MatchLineNo + 1;
                                                            Flag := 0;
                                                        end;//FIX06Jul2021<<                                                           // added sanjoy ********
                                                    END else
                                                        IF (/*(AreaRec."Bank Narration" = ReconLine.Description2)*/ CheqAndDcoNoMath() AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                                                                   (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) AND (AreaRec."Bank Posting Date" <> ReconLine."Transaction Date")) THEN BEGIN
                                                            IF Flag = 2 THEN BEGIN
                                                                AreaRec."Matching Status" := AreaRec."Matching Status"::Probable;
                                                                AreaRec.Matching := true;
                                                                AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                                ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                                AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                AreaRec.MODIFY;
                                                                ReconLine."Cross Matching" := true;
                                                                ReconLine."Matching Status" := ReconLine."Matching Status"::Probable; // Added sanjoy paul ********
                                                                ReconLine.MODIFY;
                                                                MatchLineNo := MatchLineNo + 1;
                                                                Flag := 0;                                                     // added sanjoy ********
                                                            END Else
                                                                IF Flag = 2 THEN BEGIN
                                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::"Not Matching";
                                                                    AreaRec.Matching := false;
                                                                    AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                                    ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                    AreaRec.MODIFY;
                                                                    ReconLine."Cross Matching" := False;
                                                                    ReconLine."Matching Status" := ReconLine."Matching Status"::"Not Matching"; // Added sanjoy paul ********
                                                                    ReconLine.MODIFY;
                                                                    MatchLineNo := MatchLineNo + 1;
                                                                    Flag := 0;                                                     // added sanjoy ********
                                                                END;
                                                        end;

                                        /*IF ((AreaRec."Bank Narration" = ReconLine.Description2) AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                    (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) AND (AreaRec."Bank Posting Date" = ReconLine."Transaction Date") AND (AreaRec."Bank Doc. No." = ReconLine."Document No.")) THEN BEGIN
                                            IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                AreaRec.Matching := TRUE;
                                                AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                AreaRec.MODIFY;
                                                ReconLine."Cross Matching" := TRUE;
                                                ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                ReconLine.MODIFY;
                                                MatchLineNo := MatchLineNo + 1;
                                                Flag := 0;                                                     // added sanjoy ********
                                            END;                                                          // added sanjoy ********
                                        END else
                                            IF ((AreaRec."Bank Narration" <> ReconLine.Description2) AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                        (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) AND (AreaRec."Bank Posting Date" = ReconLine."Transaction Date") AND (AreaRec."Bank Doc. No." = ReconLine."Document No.")) THEN BEGIN
                                                IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::Probable;
                                                    AreaRec.Matching := TRUE;
                                                    AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                    ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                    AreaRec.MODIFY;
                                                    ReconLine."Cross Matching" := TRUE;
                                                    ReconLine."Matching Status" := ReconLine."Matching Status"::Probable; // Added sanjoy paul ********
                                                    ReconLine.MODIFY;
                                                    MatchLineNo := MatchLineNo + 1;
                                                    Flag := 0;                                                     // added sanjoy ********
                                                END;                                                          // added sanjoy ********
                                            END else
                                                // Added Later sanjoy >>
                                                IF ((AreaRec."Bank Narration" = ReconLine.Description2)) AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                                (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount") AND (AreaRec."Bank Doc. No." = ReconLine."Document No.")) THEN BEGIN
                                                    IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                        AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                        AreaRec.Matching := TRUE;
                                                        AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                        ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                        AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                        AreaRec.MODIFY;
                                                        ReconLine."Cross Matching" := TRUE;
                                                        ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                        ReconLine.MODIFY;
                                                        MatchLineNo := MatchLineNo + 1;
                                                        Flag := 0;                                                     // added sanjoy ********
                                                    END;                                                          // added sanjoy ********
                                                END
                                                // Added later Sanjoy <<
                                                ELSE
                                                    IF (AreaRec."Bank Doc. No." = ReconLine."Document No.") AND
                                                   (AreaRec."Bank Posting Date" = ReconLine."Transaction Date") AND
                                                   ((AreaRec."Bank Narration") = (ReconLine.Description + ReconLine.Description2 + ReconLine.Description3)) AND
                                                   ((AreaRec."Bank Deposit Slip No.") = (ReconLine."Deposit Slip No.")) AND
                                                   ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                                   (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) THEN BEGIN
                                                        IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                            AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                            AreaRec.Matching := TRUE;
                                                            AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                            ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                            AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                            AreaRec.MODIFY;
                                                            ReconLine."Cross Matching" := TRUE;
                                                            ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                            ReconLine.MODIFY;
                                                            MatchLineNo := MatchLineNo + 1;
                                                            Flag := 0;                                                     // added sanjoy ********
                                                        END;                                                          // added sanjoy ********
                                                    END ELSE
                                                        IF ((STRSUBSTNO(AreaRec."Bank Doc. No.") = STRSUBSTNO(ReconLine."Document No.")) OR
                                                           ((AreaRec."Bank Posting Date" = ReconLine."Transaction Date")) AND
                                                           (((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR (
                                                           (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount"))) OR
                                                           ((STRPOS(AreaRec."Bank Narration", ReconLine.Description + '' + ReconLine.Description2 + '' + ReconLine.Description3) <> 0)))
                                               )
                                                           THEN BEGIN
                                                            IF ReconLine."Deposit Slip No." <> '' THEN BEGIN
                                                                IF ((STRPOS(AreaRec."Bank Narration", ReconLine."Deposit Slip No.") <> 0)) OR
                                                                   ((AreaRec."Bank Deposit Slip No.") = (ReconLine."Deposit Slip No.")) THEN BEGIN
                                                                    IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                                        AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                                        AreaRec.Matching := TRUE;
                                                                        AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                        AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                                        ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                                        AreaRec.MODIFY;
                                                                        ReconLine."Cross Matching" := TRUE;
                                                                        ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                                        ReconLine.MODIFY;
                                                                        MatchLineNo := MatchLineNo + 1;
                                                                        Flag := 0;                                                     // added sanjoy ********
                                                                    END;                                                          // added sanjoy ********

                                                                END;
                                                            END ELSE BEGIN
                                                                IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                                                                                                  //AreaRec."Matching Status" := AreaRec."Matching Status"::Probable;
                                                                                                                                  //AreaRec.Matching := TRUE;
                                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::"Not Matching";
                                                                    AreaRec.Matching := False;
                                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                    AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                                    ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                                    AreaRec.MODIFY;
                                                                    ReconLine."Cross Matching" := false;
                                                                    ReconLine."Matching Status" := ReconLine."Matching Status"::"Not Matching";
                                                                    ReconLine.MODIFY;
                                                                    MatchLineNo := MatchLineNo + 1;
                                                                    Flag := 1;                                                     // added sanjoy ********
                                                                END;                                                          // added sanjoy ********

                                                            END;


                                                        END ELSE BEGIN
                                                            IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                                AreaRec."Matching Status" := AreaRec."Matching Status"::"Not Matching";
                                                                ReconLine."Matching Status" := ReconLine."Matching Status"::"Not Matching";   // Added sanjoy paul ********
                                                                AreaRec.Matching := FALSE;
                                                                AreaRec.MODIFY;
                                                            END;                                                            // added sanjoy ********

                                                        END;*/
                                        UNTIL AreaRec.NEXT = 0;
                                //Message('Last %1...%2', ReconLine."Statement Amount", ReconLine."Matching Status");
                                UNTIL ReconLine.NEXT = 0;
                                Dialog1.CLOSE;
                            END;
                        END;

                    end;
                }
                // action("Upload Bank Statement")
                // {
                //     Caption = 'Upload Bank Statement';

                //     trigger OnAction();
                //     begin
                //         CLEAR(Bankstatement); // added sanjoy paul ********
                //         Bankstatement.GetBankNo("Bank Account No.","Statement No.");
                //         Bankstatement.RUNMODAL;
                //     end;
                // }//CHI1.0
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("&Test Report")
                {
                    Caption = '&Test Report';

                    Ellipsis = true;
                    Visible = false;

                    trigger OnAction();
                    begin
                        ReportPrint.PrintBankAccRecon(Rec);
                    end;
                }
                action("P&ost")
                {
                    Caption = 'P&ost';
                    // RunObject = Codeunit "Bank Acc. Recon. Post (Yes/No) cust";Bank Acc. Recon Post Y/N Cust
                    //RunObject = Codeunit "Bank Acc. Recon Post Y/N Cust";
                    ShortCutKey = 'F11';
                    trigger OnAction()
                    var
                        BankAcc: Codeunit "Bank Acc. Recon Post Y/N Cust";
                    begin
                        TestField("Approval Status", "Approval Status"::Released);
                        BankAcc.Run(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    Caption = 'Post and &Print';
                    RunObject = Codeunit "Bank Acc. Recon. Post+Print";
                    ShortCutKey = 'Shift+F11';
                    Visible = false;
                }
                action(Print)
                {
                    Caption = 'Print';

                    trigger OnAction();
                    var
                        BankRecSumm: Report "Bank Recon Summary";
                        ClosingBalance: Decimal;
                    begin
                        ClosingBalance := "Balance Last Statement" + CalculateNetChange;
                        BankRecSumm.SetData("Bank Account No.", "Statement No.", "Statement Date", "Statement Ending Balance");
                        BankRecSumm.RUNMODAL;
                    end;
                }
            }
            group(Approvals)
            {
                action(Approve)
                {
                    ApplicationArea = All;
                    Image = Action;
                    //Visible = openapp;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        IF allinoneCU.CheckBankAccRecApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendBankAccRecForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelBankAccRecForApproval(rec);
                    end;
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    var
                        WorkflowManagement: Codeunit "Workflow Management";
                    begin
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendBankAccRecforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF "Approval Status" <> "Approval Status"::Released then BEGIN
                            "Approval Status" := "Approval Status"::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    var
                        RecordRest: Record "Restricted Record";
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 273);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF "Approval Status" <> "Approval Status"::Open then BEGIN
                            "Approval Status" := "Approval Status"::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord();
    var
        BnkReconLine: Record "Bank Acc. Reconciliation Line";
        OgnlBnkStatement: Record "Original Bank Statement";
    begin
        //CurrForm.StmtLine.Page.SelectedEntry(SelectedLine);
        //CurrForm.BankStatementLine.Page.GetLineNo(SelectedLine);
        //CurrForm.BankStatementLine.ACTIVATE;
        CurrPage.StmtLine.Page.SelectedEntries(BnkReconLine);
        CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);
        LeftSideTotal := 0;
        RightSideTotal := 0;
        BnkReconLine.SETRANGE(BnkReconLine."Matching Status", BnkReconLine."Matching Status"::"Not Matching");
        IF BnkReconLine.FINDFIRST THEN
            REPEAT
                LeftSideTotal += BnkReconLine."Statement Amount";
            UNTIL BnkReconLine.NEXT = 0;
        OgnlBnkStatement.SETRANGE(OgnlBnkStatement."Matching Status", OgnlBnkStatement."Matching Status"::"Not Matching");
        IF OgnlBnkStatement.FINDFIRST THEN
            REPEAT
                RightSideTotal += OgnlBnkStatement."Bank Debit Amount" - OgnlBnkStatement."Bank Credit Amount";
            UNTIL OgnlBnkStatement.NEXT = 0;
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);

    end;

    trigger OnAfterGetCurrRecord()
    var
        BnkReconLine: Record "Bank Acc. Reconciliation Line";
        OgnlBnkStatement: Record "Original Bank Statement";
    begin
        CurrPage.StmtLine.Page.SelectedEntries(BnkReconLine);
        CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);
        LeftSideTotal := 0;
        RightSideTotal := 0;
        BnkReconLine.SETRANGE(BnkReconLine."Matching Status", BnkReconLine."Matching Status"::"Not Matching");
        IF BnkReconLine.FINDFIRST THEN
            REPEAT
                LeftSideTotal += BnkReconLine."Statement Amount";
            UNTIL BnkReconLine.NEXT = 0;
        OgnlBnkStatement.SETRANGE(OgnlBnkStatement."Matching Status", OgnlBnkStatement."Matching Status"::"Not Matching");
        IF OgnlBnkStatement.FINDFIRST THEN
            REPEAT
                RightSideTotal += OgnlBnkStatement."Bank Debit Amount" - OgnlBnkStatement."Bank Credit Amount";
            UNTIL OgnlBnkStatement.NEXT = 0;
    end;

    trigger OnClosePage();
    begin
        /*
        CREATE(WindowsShell);
        WindowsShell.SendKeys('%{F1}');
        CLEAR(WindowsShell);*///CHI WF
    end;

    /*trigger OnFindRecord(Which: Text): Boolean
    var
        BnkReconLine: Record "Bank Acc. Reconciliation Line";
        OgnlBnkStatement: Record "Original Bank Statement";
    begin
        CurrPage.StmtLine.Page.SelectedEntries(BnkReconLine);
        CurrPage.BankStatementLine.Page.SelectedEntries(OgnlBnkStatement);
        LeftSideTotal := 0;
        RightSideTotal := 0;
        BnkReconLine.SETRANGE(BnkReconLine."Matching Status", BnkReconLine."Matching Status"::"Not Matching");
        IF BnkReconLine.FINDFIRST THEN
            REPEAT
                LeftSideTotal += BnkReconLine."Statement Amount";
            UNTIL BnkReconLine.NEXT = 0;
        OgnlBnkStatement.SETRANGE(OgnlBnkStatement."Matching Status", OgnlBnkStatement."Matching Status"::"Not Matching");
        IF OgnlBnkStatement.FINDFIRST THEN
            REPEAT
                RightSideTotal += OgnlBnkStatement."Bank Debit Amount" - OgnlBnkStatement."Bank Credit Amount";
            UNTIL OgnlBnkStatement.NEXT = 0;
        CurrPage.Update();
        ;
    end;*/

    trigger OnOpenPage();
    begin
        /*
        CREATE(WindowsShell);
        WindowsShell.SendKeys('%{F1}');
        CLEAR(WindowsShell);*///CHI WF
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);
    END;

    var
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit IJLSubEvents;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        SuggestBankAccStatement: Report "Suggest Bank Acc. Recon. Line";//PK
        TransferToGLJnl: Report "Trans. Bank Rec. to Gen. Jnl.";
        ReportPrint: Codeunit "Test Report-Print";
        //Bankstatement : XMLport XMLport50075;//CHI1.0
        "---GJ1.0---": Integer;
        AreaRec: Record "Original Bank Statement";
        ReconLine: Record "Bank Acc. Reconciliation Line";
        ReconLine1: Record "Bank Acc. Reconciliation Line";
        CountV: Integer;
        Dialog1: Dialog;
        t: Integer;
        Text01: Label 'Checking for Matches!!!';
        // WindowsShell : Automation "'{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0:'{72C24DD5-D70A-438B-8A42-98424B88AFB8}':''{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0'.WshShell";
        SelectedLine: Integer;
        BankLedgR: Record "Bank Account Ledger Entry";
        Flag: Integer;
        ReconLineFilter: Record "Bank Acc. Reconciliation Line";
        OrgBnkStatementFilter: Record "Original Bank Statement";
        LeftSideTotal: Decimal;
        RightSideTotal: Decimal;


    procedure LastMatchLine(): Integer;
    var
        LastNoInternal: Integer;
        LastNoExternat: Integer;
        LOriginalBankStatement: Record "Original Bank Statement";
        LBankReconciliationLine: Record "Bank Acc. Reconciliation Line";
    begin
        // Checking Matching line for both the Tables
        LOriginalBankStatement.RESET;
        LOriginalBankStatement.SETCURRENTKEY("MatchLine No");
        LOriginalBankStatement.SETRANGE(Code, "Bank Account No.");
        LOriginalBankStatement.SETRANGE("Statement No.", "Statement No.");
        IF LOriginalBankStatement.FINDLAST THEN
            LastNoInternal := LOriginalBankStatement."MatchLine No";
        LBankReconciliationLine.RESET;
        LBankReconciliationLine.SETCURRENTKEY("MatchLine No");
        LBankReconciliationLine.SETRANGE("Bank Account No.", "Bank Account No.");
        LBankReconciliationLine.SETRANGE("Statement No.", "Statement No.");
        IF LBankReconciliationLine.FINDLAST THEN
            LastNoExternat := LBankReconciliationLine."MatchLine No";

        IF LastNoInternal = LastNoExternat THEN
            EXIT(LastNoInternal)
        ELSE
            IF LastNoInternal > LastNoExternat THEN BEGIN
                MESSAGE('Please press the Refresh button and start from the fresh');
                EXIT(LastNoInternal);
            END
            ELSE
                IF LastNoInternal < LastNoExternat THEN BEGIN
                    MESSAGE('Please press the Refresh button and start from the fresh');
                    EXIT(LastNoExternat);
                END
    end;

    procedure DownloadOutstandingEntries();
    var
        lBRSOutstandingData: Record "BRS Outstanding Data";
        lBankLedgerEntry: Record "Bank Account Ledger Entry";
        lBankAccReconLine: Record "Bank Acc. Reconciliation Line";
        BankAccSetStmtNo: Codeunit "Bank Acc. Entry Set Recon.-No.";
        lLastLineNo: Integer;
        lOrgBnkStatement: Record "Original Bank Statement";
    begin
        lBankAccReconLine.RESET;
        lBankAccReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
        lBankAccReconLine.SETRANGE("Statement No.", "Statement No.");
        lBankAccReconLine.SETRANGE(lBankAccReconLine."Outstanding Entry", TRUE);
        lBankAccReconLine.DELETEALL(TRUE);

        lOrgBnkStatement.RESET;
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement.Code, "Bank Account No.");
        lOrgBnkStatement.SETRANGE("Statement No.", "Statement No.");
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement."Outstanding Entry", TRUE);
        lOrgBnkStatement.DELETEALL(TRUE);

        lBankAccReconLine.RESET;
        lOrgBnkStatement.RESET;
        // Dowanloading Navision Data  >>
        //LBankReconciliationLine.SETCURRENTKEY("MatchLine No");
        lBankAccReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
        lBankAccReconLine.SETRANGE("Statement No.", "Statement No.");
        IF lBankAccReconLine.FINDLAST THEN
            lLastLineNo := lBankAccReconLine."Statement Line No.";
        lBRSOutstandingData.RESET;
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Type, lBRSOutstandingData.Type::Navision);
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Reconciled, FALSE);
        lBRSOutstandingData.SETRANGE("Bank No", "Bank Account No.");
        IF lBRSOutstandingData.FINDSET THEN
            REPEAT
                IF NOT ((lBRSOutstandingData."Debit Amount" = 0) AND (lBRSOutstandingData."Credit Amount" = 0)) THEN BEGIN
                    lBankLedgerEntry.RESET;
                    lBankLedgerEntry.SETCURRENTKEY("Bank Account No.", "Posting Date");
                    lBankLedgerEntry.SETRANGE("Bank Account No.", lBRSOutstandingData."Bank No");
                    lBankLedgerEntry.SETRANGE(lBankLedgerEntry."Document No.", lBRSOutstandingData."Document No");
                    lBankLedgerEntry.SETRANGE(Open, TRUE);
                    lBankLedgerEntry.SETRANGE("Statement Status", lBankLedgerEntry."Statement Status"::Open);
                    IF lBRSOutstandingData."Debit Amount" <> 0 THEN
                        lBankLedgerEntry.SETRANGE(lBankLedgerEntry.Amount, lBRSOutstandingData."Debit Amount");
                    IF lBRSOutstandingData."Credit Amount" <> 0 THEN
                        lBankLedgerEntry.SETRANGE(lBankLedgerEntry.Amount, -lBRSOutstandingData."Credit Amount");
                    lBankLedgerEntry.SETRANGE(lBankLedgerEntry."Statement No.", '');

                    IF lBankLedgerEntry.FINDFIRST THEN BEGIN
                        lBankAccReconLine.INIT;
                        lBankAccReconLine."Statement Line No." := lLastLineNo + 10000;
                        lBankAccReconLine."Transaction Date" := lBankLedgerEntry."Posting Date";
                        lBankAccReconLine.Description := lBankLedgerEntry.Description;
                        lBankAccReconLine."Document No." := lBankLedgerEntry."Document No.";
                        lBankAccReconLine."Bank Account No." := "Bank Account No.";
                        lBankAccReconLine."Statement No." := "Statement No.";
                        lBankAccReconLine."Statement Amount" := lBankLedgerEntry."Remaining Amount";
                        lBankAccReconLine."External Document No." := lBankLedgerEntry."External Document No.";
                        lBankAccReconLine."Applied Amount" := lBankAccReconLine."Statement Amount";
                        lBankAccReconLine.Type := lBankAccReconLine.Type::"Bank Account Ledger Entry";
                        IF lBankLedgerEntry."Old_Description 2" <> '' THEN
                            lBankAccReconLine.Description2 := lBankLedgerEntry."Old_Description 2"
                        ELSE
                            lBankAccReconLine.Description2 := lBankLedgerEntry."Description 2";
                        IF lBankLedgerEntry."Old_Description 3" <> '' THEN
                            lBankAccReconLine.Description3 := lBankLedgerEntry."Old_Description 3"
                        ELSE
                            lBankAccReconLine.Description3 := lBankLedgerEntry."Description 3";
                        lBankAccReconLine."Deposit Slip No." := lBankLedgerEntry."Deposit Slip No.";
                        lBankAccReconLine."Applied Entries" := 1;
                        //lBankAccReconLine."Global Dimension 9 Code" := lBankLedgerEntry."Shortcut Dimension 9 Code";//PK
                        lBankAccReconLine."Outstanding Entry" := TRUE;
                        lBankAccReconLine."Matching Status" := lBankAccReconLine."Matching Status"::"Not Matching";
                        BankAccSetStmtNo.SetReconNo(lBankLedgerEntry, lBankAccReconLine);
                        lBankAccReconLine.INSERT;
                        lLastLineNo += 10000;
                    END;
                END;
            UNTIL lBRSOutstandingData.NEXT = 0;
        //<<


        // Downloading Statement data
        CLEAR(lLastLineNo);
        lOrgBnkStatement.RESET;
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement.Code, "Bank Account No.");
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement."Statement No.", "Statement No.");
        IF lOrgBnkStatement.FINDLAST THEN
            lLastLineNo := lOrgBnkStatement."Line No." + 10000;

        lBRSOutstandingData.RESET;
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Type, lBRSOutstandingData.Type::Statement);
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Reconciled, FALSE);
        lBRSOutstandingData.SETRANGE("Bank No", "Bank Account No.");
        IF lBRSOutstandingData.FINDSET THEN
            REPEAT
                IF NOT ((lBRSOutstandingData."Debit Amount" = 0) AND (lBRSOutstandingData."Credit Amount" = 0)) THEN BEGIN
                    lOrgBnkStatement.INIT;
                    lOrgBnkStatement.Code := "Bank Account No.";
                    lOrgBnkStatement."Statement No." := "Statement No.";
                    lOrgBnkStatement."Line No." := lLastLineNo;
                    lOrgBnkStatement."Bank Posting Date" := lBRSOutstandingData."Posting date";
                    lOrgBnkStatement."Bank Narration" := lBRSOutstandingData.Narrations;
                    lOrgBnkStatement."Bank Debit Amount" := lBRSOutstandingData."Debit Amount";
                    lOrgBnkStatement."Bank Credit Amount" := lBRSOutstandingData."Credit Amount";
                    lOrgBnkStatement."Matching Status" := lOrgBnkStatement."Matching Status"::"Not Matching";
                    lOrgBnkStatement."Outstanding Entry" := TRUE;
                    lOrgBnkStatement."Outstanding Entry Line" := lBRSOutstandingData."Line No";
                    lOrgBnkStatement.INSERT;
                    lLastLineNo += 1;
                END;
            UNTIL lBRSOutstandingData.NEXT = 0;
    end;

    procedure CalculateNetChange(): Decimal;
    var
        BankLedgerEntry: Record "Bank Account Ledger Entry";
        Total: Decimal;
    begin
        Total := 0;
        BankLedgerEntry.Reset;
        BankLedgerEntry.SETRANGE(BankLedgerEntry."Bank Account No.", "Bank Account No.");
        //BankLedgerEntry.SETRANGE(BankLedgerEntry."Statement No.","Statement No.");
        BankLedgerEntry.SETRANGE(BankLedgerEntry."Posting Date", CALCDATE('-1M+1D', "Statement Date"), "Statement Date");
        //BankAccRecLine.SETFILTER(BankAccRecLine."Matching Status",'<>%1',BankAccRecLine."Matching Status"::"Not Matching");
        IF BankLedgerEntry.FINDSET THEN
            REPEAT
                Total += BankLedgerEntry."Amount (LCY)";
            UNTIL BankLedgerEntry.NEXT = 0;
        EXIT(Total);
    end;

    procedure CheqAndDcoNoMath(): Boolean;
    var
        Str1: Text[1024];
        str2: Text[1024];
        i: Integer;
        Arr: array[20] of Text[50];
        Value: Text[20];
        j: Integer;
        Flag: Boolean;
    begin
        Str1 := ReconLine.Description + ReconLine.Description2 + ReconLine.Description3;
        str2 := (AreaRec."Bank Narration" + ' ' + AreaRec."Bank Doc. No.");
        i := 1;
        j := 1;
        WHILE STRLEN(str2) <> 0 DO BEGIN
            Value := COPYSTR(str2, 1, 1);
            IF Value IN ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'] THEN BEGIN
                Arr[j] := Arr[j] + Value;
                Flag := TRUE;
            END
            ELSE
                IF Flag THEN BEGIN
                    j += 1;
                    Flag := FALSE;
                END;
            str2 := COPYSTR(str2, 2, STRLEN(str2));
        END;

        IF STRLEN(Arr[1]) > 3 THEN
            IF STRPOS(Str1, Arr[1]) <> 0 THEN
                EXIT(TRUE)
            ELSE
                IF STRLEN(Arr[2]) > 3 THEN
                    IF STRPOS(Str1, Arr[2]) <> 0 THEN
                        EXIT(TRUE)
                    ELSE
                        IF STRLEN(Arr[3]) > 3 THEN
                            IF STRPOS(Str1, Arr[3]) <> 0 THEN
                                EXIT(TRUE)
                            ELSE
                                IF STRLEN(Arr[4]) > 3 THEN
                                    IF STRPOS(Str1, Arr[4]) <> 0 THEN
                                        EXIT(TRUE)
                                    ELSE
                                        IF STRLEN(Arr[5]) > 3 THEN
                                            IF STRPOS(Str1, Arr[5]) <> 0 THEN
                                                EXIT(TRUE)
                                            ELSE
                                                IF STRLEN(Arr[6]) > 3 THEN
                                                    IF STRPOS(Str1, Arr[6]) <> 0 THEN
                                                        EXIT(TRUE)
                                                    ELSE
                                                        IF STRLEN(Arr[7]) > 3 THEN
                                                            IF STRPOS(Str1, Arr[7]) <> 0 THEN
                                                                EXIT(TRUE)
                                                            ELSE
                                                                IF STRLEN(Arr[8]) > 3 THEN
                                                                    IF STRPOS(Str1, Arr[8]) <> 0 THEN
                                                                        EXIT(TRUE)
                                                                    ELSE
                                                                        IF STRLEN(Arr[9]) > 3 THEN
                                                                            IF STRPOS(Str1, Arr[9]) <> 0 THEN
                                                                                EXIT(TRUE)
                                                                            ELSE
                                                                                IF STRLEN(Arr[10]) > 3 THEN
                                                                                    IF STRPOS(Str1, Arr[10]) <> 0 THEN
                                                                                        EXIT(TRUE)
                                                                                    ELSE
                                                                                        IF STRLEN(Arr[11]) > 3 THEN
                                                                                            IF STRPOS(Str1, Arr[11]) <> 0 THEN
                                                                                                EXIT(TRUE)
                                                                                            ELSE
                                                                                                IF STRLEN(Arr[12]) > 3 THEN
                                                                                                    IF STRPOS(Str1, Arr[12]) <> 0 THEN
                                                                                                        EXIT(TRUE)

                                                                                                    ELSE
                                                                                                        EXIT(FALSE);
    end;

    local procedure LeftSideTotal43RightSideTotalO();
    begin
        IF LeftSideTotal - RightSideTotal <> 0 THEN;
    end;

}

