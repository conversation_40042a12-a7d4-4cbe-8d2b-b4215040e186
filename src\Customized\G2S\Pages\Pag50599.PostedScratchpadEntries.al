/// <summary>
/// Page Posted Scratchpad Entries (ID 50599).
/// </summary>
page 50599 "Posted Scratchpad Entries"
{
    //300624
    //scratchpad update 050724
    ApplicationArea = All;
    Caption = 'Posted Scratchpad Entries';
    PageType = List;
    UsageCategory = Lists;
    SourceTable = "WHI Scratchpad Entry";
    SourceTableView = where(Posted = const(true));
    InsertAllowed = false;
    ModifyAllowed = False;
    DeleteAllowed = false;
    Editable = false;


    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(fldLineNo; Rec."Line No.")
                {
                }
                field(fldCategory; Rec.Category)
                {
                }
                field(fldJobID; Rec."Job ID")
                {
                }
                field(fldLocationCode; Rec."Location Code")
                {
                }
                field(fldCreationDate; Rec."Creation Date")
                {
                }
                field(fldUserID; Rec."User ID")
                {
                }
                field(fldBarcode; Rec.Barcode)
                {
                    Visible = false;
                }
                field(fldBin; Rec."Bin Code")
                {
                    Visible = false;
                }
                field(fldItemNo; Rec."Item No.")
                {
                }
                field(fldVariantCode; Rec."Variant Code")
                {
                    Visible = false;
                }
                field(fldUoMCode; Rec."Unit of Measure Code")
                {
                }
                field(fldLotNo; Rec."Lot No.")
                {
                }
                field(fldSerialNo; Rec."Serial No.")
                {
                    Visible = false;
                }
                field(fldQuantity; Rec.Quantity)
                {
                }
                field(fldComment; Rec.Comment)
                {
                    Visible = false;
                }
                field(fldState; Rec.State)
                {
                    Visible = false;
                }
                field(Posted; Rec.Posted) { }
                field("Posted By"; Rec."Posted By") { }
                field("Date Posted"; "Date Posted") { }
            }
        }
    }
}
