page 50943 BranchListReleased
{
    Caption = 'Branch Request List Released';
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = BranchRequest;
    CardPageId = BranchRequestCardReleased;
    SourceTableView = where("Transfer Order No." = filter(<> ''));
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(Control1102152000)
            {
                field(No; No)
                {
                    ApplicationArea = all;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = All;

                }
                field("To Location"; "To Location")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Transfer Order No."; "Transfer Order No.")
                {
                    ApplicationArea = all;
                }
                field("No. Transfer Shipments"; "No. Transfer Shipments")
                {
                    ApplicationArea = all;
                }
                field("No. Transfer Receipts"; "No. Transfer Receipts")
                {
                    ApplicationArea = all;
                }
            }
        }
    }


    trigger OnModifyRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not modify branch request. Already transfer order is created.');
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not deleted branch request. Already transfer order is created.');
    end;
}