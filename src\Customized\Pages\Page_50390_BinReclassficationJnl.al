page 50390 "Bin Reclassfication Jnl"
{
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = List;
    SourceTable = "Bin Reclassfication Jnl";


    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {

                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                }

                Field("Variant Code"; "Variant Code")
                {
                    ApplicationArea = All;

                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = All;
                }


                field("Bin Code"; "Bin Code")
                {
                    ApplicationArea = All;
                }




                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var

                    begin

                        Clear(TotBinQty);
                        BinReclassJnl2.reset;
                        BinReclassJnl2.SetRange("Document Type", "Document Type");
                        BinReclassJnl2.SetRange("Document No.", "Document No.");
                        BinReclassJnl2.SetRange("Document Line No.", "Document Line No.");
                        BinReclassJnl2.Setfilter("Line No.", '<>%1', "Line No.");
                        if BinReclassJnl2.Findset then begin
                            Repeat
                                TotBinQty += BinReclassJnl2.Quantity;
                            until BinReclassJnl2.next = 0;

                        end;
                        if DocQty < (TotBinQty + rec.Quantity) then
                            Error(BinQtyErr, DocQty, DocQty - TotBinQty);
                    end;
                }

                field("Qty.(Base)"; "Qty.(Base)")
                {
                    ApplicationArea = All;
                }

                Field("Qty.Reclassfied Base"; "Qty.Reclassfied Base")
                {
                    ApplicationArea = All;
                }

                field(Reclassfication; Reclassfication)
                {
                    ApplicationArea = All;
                }

            }
        }



    }

    trigger OnNewRecord(BelowXRec: Boolean)
    var
        myInt: Integer;
    begin
        Case CrntSourceType of
            Crntsourcetype::Sale:
                Begin
                    "Item No." := CrntSalesLines."No.";
                    "Location Code" := CrntSalesLines."Location Code";
                    "Variant Code" := CrntSalesLines."Variant Code";
                    "Qty. per Unit of Measure" := CrntSalesLines."Qty. per Unit of Measure";

                End;
            Crntsourcetype::Transfer:
                Begin
                    "Item No." := CrntTransLine."Item No.";
                    "Location Code" := CrntTransLine."Transfer-from Code";
                    "Variant Code" := CrntTransLine."Variant Code";
                    "Qty. per Unit of Measure" := CrnttransLine."Qty. per Unit of Measure";

                End;
            Crntsourcetype::WhsShip:
                Begin
                    "Item No." := CrntWhseShipLine."Item No.";
                    "Location Code" := CrntWhseShipLine."Location Code";
                    "Variant Code" := CrntWhseShipLine."Variant Code";
                    "Qty. per Unit of Measure" := CrntWhseShipLine."Qty. per Unit of Measure";

                End;
        end;
    end;


    procedure SetSourceValues(SourceType: Option; var SalesLine: Record "Sales Line";
                Var TransLine: Record "Transfer Line"; Var WhseShipLine: Record "Warehouse Shipment Line")
    begin

        CrntsourceType := SourceType;
        CrntSalesLines := SalesLine;
        CrntTransLine := TransLine;
        CrntWhseShipLine := WhseShipLine;
        Case CrntSourceType of
            Crntsourcetype::Sale:
                DocQty := CrntSalesLines.Quantity;
            Crntsourcetype::WhsShip:
                DocQty := CrntWhseShipLine.Quantity;
            Crntsourcetype::Transfer:
                DocQty := CrntTransLine.Quantity;
        end;

    end;

    var
        CrntSourceType: Option Sale,Transfer,WhsShip;
        CrntSalesLines: Record "Sales Line";
        CrntTransLine: Record "Transfer Line";
        CrntWhseShipLine: Record "Warehouse Shipment Line";
        DocQty: Decimal;
        TotBinQty: Decimal;

        BinReclassJnl2: record "Bin Reclassfication Jnl";
        BinQtyErr: Label 'Document Total Qty. %1 can not more than %2 Quantity';




}