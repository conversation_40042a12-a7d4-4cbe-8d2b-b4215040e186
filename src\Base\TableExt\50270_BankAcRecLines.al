tableextension 50270 BankAccRecLinesExt extends "Bank Acc. Reconciliation Line"
{
    fields
    {
        /*field(50000; "Matching Status"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = 'Exact,Probable,Not Matching,Cancel';
            OptionMembers = Exact,Probable,"Not Matching",Cancel;
        }
        field(50001; "MatchLine No"; Integer)
        {

            DataClassification = CustomerContent;
        }
        field(50002; "Outstanding Entry"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "External Document No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50004; Description2; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50005; Description3; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Deposit Slip No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Cancel No"; integer)
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Cross Matching"; Boolean)
        {
            DataClassification = CustomerContent;
        }*/ //PK
        field(50000; Description2; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Shortcut Dimension 1 Code1"; Code[20])
        {
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));
            DataClassification = CustomerContent;
        }
        field(50002; "Shortcut Dimension 2 Code2"; Code[20])
        {
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(2));
            DataClassification = CustomerContent;
        }
        field(50003; "Pending Tellers"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50004; Description3; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "External Document No."; Code[35])
        {
            DataClassification = CustomerContent;//B2BPKON2905 increased to 35
        }
        field(50006; "Deposit Slip No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Global Dimension 9 Code"; Code[20])
        {
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(9));
            DataClassification = CustomerContent;
        }
        field(60009; "Paid To / Received By"; Text[50])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(65000; "Cross Matching"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65001; "Matching Status"; Option)
        {
            OptionCaption = 'Exact,Probable,Not Matching,Cancel,Manually Matched'; // G2S 7708-CAS-01421-Z6M7V9
            OptionMembers = Exact,Probable,"Not Matching",Cancel,"Manually Matched"; // G2S 7708-CAS-01421-Z6M7V9
            DataClassification = CustomerContent;
        }
        field(65002; "MatchLine No"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65003; "Outstanding Entry"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65017; "Cancel No"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65020; Select; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65030; "Teller No."; Text[50])
        {
            DataClassification = CustomerContent; //PKON22AP28 
        }
    }
}