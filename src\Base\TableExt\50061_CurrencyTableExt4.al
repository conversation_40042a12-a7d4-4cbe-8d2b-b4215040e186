tableextension 50061 CurrencyExt extends Currency
{
    fields
    {
        field(50000; "Currency Numeric Description"; code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Currency Decimal Description"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Currency Symbol"; Code[20])
        {
            DataClassification = CustomerContent;
        }
    }

    var
        myInt: Integer;
}