pageextension 50063 FixedAssetUp extends "Fixed Asset Setup"
{
    layout
    {
        addlast(General)
        {
            field("PMS Dim Code"; "PMS Dim Code")
            {
                ApplicationArea = all;
            }
            field("Capex Budget  No. Series"; "Capex Budget  No. Series")
            {
                ApplicationArea = ALL;
            }
            field("Capex No. Series"; "Capex No. Series")
            {
                ApplicationArea = ALL;
            }
            field("Insurance Period"; "Insurance Period")
            {
                ApplicationArea = all;
            }
            field("E-Mail"; "E-Mail")
            {
                ApplicationArea = all;
            }
            field("FA Disposal No. Series"; "FA Disposal No. Series")
            {
                ApplicationArea = all;
            }
            field("Disposal Account"; "Disposal Account")
            {
                ApplicationArea = all;
            }
            field("FA Movement Reg. Series"; "FA Movement Reg. Series")
            {
                ApplicationArea = all;
            }
            field("Fa Verification Nos"; "Fa Verification Nos")
            {
                ApplicationArea = all;
            }
            field("PRS No. Series"; "PRS No. Series")
            {
                ApplicationArea = all;
            }
        }
    }
}