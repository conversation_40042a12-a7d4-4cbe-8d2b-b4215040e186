page 50929 "Variable Discount Posting "
{
    ApplicationArea = All;
    Caption = 'Variable Discount Posting ';
    PageType = List;
    SourceTable = "Rebate Discount Header";
    UsageCategory = Lists;
    InsertAllowed = false;
    DeleteAllowed = false;
    Editable = false;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("Sales Header No."; Rec."Sales Header No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Sales Header No. field.';
                }
                field("Sales Inv. No."; Rec."Sales Inv. No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Sales Inv. No. field.';
                }
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Customer No."; Rec."Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer No. field.';
                }
                field("Customer Name"; Rec."Sell-to Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer Name field.';
                }
                field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bill to customer name field.';
                    Visible = false;
                }
                field("Discount Account"; Rec."Discount Account")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Discount Account field.';
                }
                field("Variable Discount Amount"; Rec."Rebate Discount Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Rebate Discount Amount field.';
                }
                field("Fixed Discount Amount"; Rec."Fixed Rebate Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Rebate Discount Amount field.';
                }
                field("Applies-to Doc. No."; Rec."Applies-to Doc. No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Applies-to Doc. No. field.';
                }
                field("Date Processed"; Rec."Date Processed")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Processed field.';
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Posting Date field.';
                }
                field("DateTime Processed"; Rec."DateTime Processed")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Processed field.';
                }
                field(Processed; Rec.Processed)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Processed field.';
                }
            }
        }
    }
}
