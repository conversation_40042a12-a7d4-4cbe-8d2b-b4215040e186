tableextension 50067 InventoryPostingGroupTabExt94 extends "Inventory Posting Group"
{
    fields
    {
        field(50000; "Branch Prod. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50001; "Prod. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50003; "Active for Sales"; Boolean)
        {
        }
        field(50004; Blocked; Boolean)
        {
            Description = 'HO';
        }
        
    }

}