tableextension 50033 SalInvLineTabExt extends "Sales Invoice Line"
{
    fields
    {

        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Promo. No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Promo Schedule"."No." WHERE(Status = CONST(Released));
            Editable = false;
        }
        field(50003; "Promo. Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Promo Schedule Line"."Line No." WHERE("Document No." = FIELD("Promo. No."), Active = CONST(true));
            Editable = false;
        }
        field(50004; "Gift Item"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50012; "POS Window"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50007; Bags; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50020; "Old_Bags"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            //b2bpksalecorr9 end
        }
        field(50026; "Posted Loading Slip Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = false;
            //b2bpksalecorr9 end

        }
        // 270324 G2S KD Rebate
        field(60010; "Rebate Discount"; Decimal)
        {
            // DataClassification = CustomerContent;
            Editable = false;
            Caption = 'Variable Discount';
        }
        // 270324 G2S KD Rebate
        // rebate process
        field(60016; "AmtafterRebate"; Decimal)
        {
            DataClassification = CustomerContent;
            //  AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Amount after Discounts';
            Editable = false;
            /* trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin
                if ("VAT %" <> 0) and (AmtafterRebate <> 0) then
                    Validate(AmtafterRebateIncVAT, (round(AmtafterRebate + (AmtafterRebate * ("VAT %" / 100)), currency."Amount Rounding Precision"))) else
                    Validate(AmtafterRebateIncVAT, AmtafterRebate);
            end;
 */
        }
        field(60011; "Rebate Disc. Amount to Inv."; Decimal)
        {
            DataClassification = CustomerContent;
            // AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Variable Disc. Amount to Inv.';
            Editable = false;

        }
        field(60012; "Fixed Rebate Code"; Code[10])
        {
            DataClassification = CustomerContent;

            Caption = 'Fixed Discount Code';
            Editable = false;
            trigger OnValidate()
            var
                myInt: Integer;
            begin

            end;
        }
        field(60013; "Fixed Rebate"; Decimal)
        {
            DataClassification = CustomerContent;

            Caption = 'Fixed Discount';
            Editable = false;

        }
        field(60014; "Fixed Rebate Amount"; Decimal)
        {
            DataClassification = CustomerContent;

            Caption = 'Fixed Discount Amount';
            Editable = false;
            /* trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin
                if (Amount <> 0) then //begin
                    Validate(AmtafterRebate, Amount - "Fixed Rebate Amount") else
                    Validate(AmtafterRebate, 0);
                // if "VAT %" <> 0 then
                //  Validate(AmtafterRebateIncVAT, (round(AmtafterRebate + (AmtafterRebate * ("VAT %" / 100)), currency."Amount Rounding Precision")));
                // end;
            end; */

        }
        field(60015; "Fixed Rebate Amount to Inv."; Decimal)
        {
            DataClassification = CustomerContent;
            // AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Fixed Discount Amount to Inv.';
            Editable = false;

        }
        field(60017; "AmtafterRebateIncVAT"; Decimal)
        {
            DataClassification = CustomerContent;
            // AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Amount after Discounts Inc. VAT';
            Editable = false;
        }
        // <<<
    }

}