page 50956 "Nibss Notifications History"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    Editable = false;
    SourceTable = "Nibss Notifications History";

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(sessionID; sessionID)
                {
                    ApplicationArea = All;

                }
                field("Source Bank Code"; sourceBankCode)
                {
                    ApplicationArea = All;

                }
                field("source Bank Name"; sourceBankName)
                {
                    ApplicationArea = All;

                }
                field("customer Name"; customerName)
                {
                    ApplicationArea = All;

                }
                field("Customer Account Number"; CustomerAccountNumber)
                {
                    ApplicationArea = All;

                }
                field(amount; amount)
                {
                    ApplicationArea = All;

                }
                field("Total Amount"; totalAmount)
                {
                    ApplicationArea = All;

                }
                field(narration; narration)
                {
                    ApplicationArea = All;

                }
                field(Remark; Remark)
                {
                    ApplicationArea = All;

                }
                field("Date Entered"; dateEntered)
                {
                    ApplicationArea = All;

                }
                field("Date Updated"; dateUpdated)
                {
                    ApplicationArea = All;

                }

            }
        }
        area(Factboxes)
        {

        }
    }

    actions
    {
        area(Processing)
        {
            action("Remove Special Char")
            {
                ApplicationArea = All;

                trigger OnAction();
                var
                    NibssNot: Record "Nibss Notifications History";
                begin
                    NibssNot.Reset();
                    //NibssNot.SetRange();
                    if NibssNot.FindSet() then
                        repeat
                            if StrPos(NibssNot.sessionID, '#') = 1 then
                                NibssNot.sessionID := DelStr(NibssNot.sessionID, 1, 1);
                            if StrPos(NibssNot.sourceBankCode, '#') = 1 then
                                NibssNot.sourceBankCode := DelStr(NibssNot.sourceBankCode, 1, 1);
                            if StrPos(NibssNot.CustomerAccountNumber, '#') = 1 then
                                NibssNot.CustomerAccountNumber := DelStr(NibssNot.CustomerAccountNumber, 1, 1);
                            if StrPos(NibssNot.transactionInitiatedDate, '#') = 1 then
                                NibssNot.transactionInitiatedDate := DelStr(NibssNot.transactionInitiatedDate, 1, 1);
                            if StrPos(NibssNot.transactionApprovalDate, '#') = 1 then
                                NibssNot.transactionApprovalDate := DelStr(NibssNot.transactionApprovalDate, 1, 1);
                            NibssNot.Modify();
                        until NibssNot.next() = 0;
                    Message('Completed');
                end;
            }
        }
    }
}