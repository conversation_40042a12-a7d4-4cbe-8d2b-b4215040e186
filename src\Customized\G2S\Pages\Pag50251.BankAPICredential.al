/// <summary>
/// Page Bank API Credential (ID 50605).
/// </summary>
/// G2S Providus Integration 7th Aug 2024 
page 50605 "Bank API Credential Setup"
{
    ApplicationArea = All;
    Caption = 'Bank API Credential';
    PageType = List;
    SourceTable = "Bank API Credentials";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    Editable = false;
                }
                field("Providus Bank?"; Rec."Providus Bank?")
                {

                }
                field("First Bank?"; Rec."First Bank?")
                {

                }
                field("Zenith Bank?"; "Zenith Bank?")
                {

                }
                field("Bank Account No."; Rec."Bank Account No.")
                {

                }
                field("Bank Account Number"; Rec."Bank Account Number")
                {

                }
                field("Company Code"; "Company Code")
                {

                }

                field(BaseURL; BaseURL)
                {
                    MultiLine = true;
                }

                field(PasswordTxt; PasswordTxt)
                {
                    Caption = 'Password';
                    // MultiLine = true;
                }
                field(Username; UName)
                {
                    // MultiLine = true;
                }
                field("Currency Code"; "Currency Code")
                {
                }
                field("Fund Trf. Endpoint"; Rec."Fund Trf. Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Fund Trf. Endpoint field.', Comment = '%';
                }
                field("NIP Account Endpoint"; Rec."NIP Account Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the NIP Account Endpoint field.', Comment = '%';
                }
                field("NIP Txn. Status Endpoint"; Rec."NIP Txn. Status Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the NIP Txn. Status Endpoint field.', Comment = '%';
                }
                field("Inter Bank Payment Type"; "Inter Bank Payment Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Inter Bank Payment Type Endpoint field.', Comment = '%';
                }
                field("Intra Fund Trf. Endpoint"; Rec."Intra Fund Trf. Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Intra Fund Trf. Endpoint field.', Comment = '%';
                }
                field("Intra NIP Account Endpoint"; Rec."Intra NIP Account Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Intra NIP Account Endpoint field.', Comment = '%';
                }
                field("Intra NIP Txn. Status Endpoint"; Rec."Intra NIP Txn. Status Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Intra NIP Txn. Status Endpoint field.', Comment = '%';
                }
                field("Intra Bank Payment Type"; "Intra Bank Payment Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Intra Bank Payment Type Endpoint field.', Comment = '%';
                }
                // field("Bulk Payment"; "Bulk Payment")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies the value of the Bulk Payment field.', Comment = '%';
                // }
                field("Session Endpoint"; "Session Endpoint")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Session ID field.', Comment = '%';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group(Providus)
            {
                action(GetProvidus)
                {
                    Caption = 'Providus Details';
                    ToolTip = 'Get Providus account details';
                    ApplicationArea = All;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                    begin
                        Rec.TestField("Bank Account No.");
                        Rec.TestField("Bank Account Number");
                        ProvidusBankAPI.GetProvidusAccount();
                    end;
                }
            }
        }
        area(Navigation)
        {
            action(SetCredentials)
            {
                Image = NewBank;
                Caption = 'Set Credentials';
                trigger OnAction()
                var
                    MyOutStream: OutStream;
                begin
                    UpdatedDetails := false;
                    // Rec.TestField("Providus Bank?", true);
                    if Rec."Providus Bank?" OR Rec."First Bank?" OR Rec."Zenith Bank?" then begin
                        Rec.TestField("Bank Account No.");
                        Rec.TestField("Bank Account Number");
                        if Rec."Access Details Provided" then
                            UpdatedDetails := true;
                        if UpdatedDetails then
                            Txt001 := 'Credentails for %1 has already been provided. Do you want to update the record?' else
                            Txt001 := 'Please confirm you want to create the access credentials or %1';

                        If Confirm(Txt001, false, Rec."Bank Account No.") then begin
                            Rec.TestField("Bank Account No.");
                            if BaseURL <> '' then begin
                                Clear(MyOutStream);
                                Rec."Base Url".CreateOutStream(MyOutStream);
                                MyOutStream.WriteText(BaseURL);
                                Rec.Validate("Base Url");
                                Rec.Modify();
                            end;
                            if PasswordTxt <> '' then begin
                                Clear(MyOutStream);
                                Rec.Password.CreateOutStream(MyOutStream);
                                MyOutStream.WriteText(PasswordTxt);
                                Rec.Validate(Password);
                                Rec.Modify();
                            end;
                            if UName <> '' then begin
                                Clear(MyOutStream);
                                Rec.Username.CreateOutStream(MyOutStream);
                                MyOutStream.WriteText(UName);
                                Rec.Validate(Username);
                                Rec.Modify();
                            end;
                            Message('Credential uploaded successfully');
                        end;
                        clear(BaseURL);
                        Clear(PasswordTxt);
                        Clear(UName);
                    end;
                end;

            }
            action("First Bank Payment Status")
            {
                Caption = 'First Bank Payment Status';
                image = Payment;
                trigger OnAction()
                var
                    FirstBankPmtStatus: Page "Bank Payment Status";
                begin
                    FirstBankPmtStatus.Run();
                end;
            }
        }
    }

    var
        BaseURL: Text[1500];
        PasswordTxt: Text[1500];
        UName: Text[1500];
        Txt001: Text;// 'Please confirm you want to create the access credentials or %1';
        UpdatedDetails: Boolean;
        Txt002: Text; // 'Credentails for %1 has already been provided. Do you want to update the record?';

    trigger OnOpenPage()
    var
        myInt: Integer;
    begin
        clear(BaseURL);
        Clear(PasswordTxt);
        Clear(UName);
    end;

}
///Providus Integration 7th Aug 2024 