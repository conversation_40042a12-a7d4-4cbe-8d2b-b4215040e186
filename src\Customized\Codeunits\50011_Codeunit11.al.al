codeunit 50011 "Gate Entry- Post"
{
    // version NAVIN7.00

    TableNo = "Gate Entry Header";
    Permissions = tabledata "E-mail Log" = rimd;

    trigger OnRun();
    var
        InvSetUp: Record "Inventory Setup";
    begin
        GateEntryHeader := Rec;
        with GateEntryHeader do begin
            TESTFIELD("Posting Date");
            TESTFIELD("Document Date");
            TestField("Approval Status", "Approval Status"::Released);

            GateEntryLine.RESET;
            GateEntryLine.SETRANGE("Entry Type", "Entry Type");
            GateEntryLine.SETRANGE("Gate Entry No.", "No.");
            if not GateEntryLine.FIND('-') then
                ERROR(Text16500);

            if GateEntryLine.FINDSET then
                repeat
                    if GateEntryLine."Source Type" <> 0 then
                        GateEntryLine.TESTFIELD("Source No.");
                    if GateEntryLine."Source Type" = 0 then
                        GateEntryLine.TESTFIELD(Description);
                until GateEntryLine.NEXT = 0;

            if GUIALLOWED then
                Window.OPEN(
                  '#1###########################\\' +
                  Text16501);
            if GUIALLOWED then
                Window.UPDATE(1, STRSUBSTNO('%1 %2', Text16502, "No."));

            if "Posting No. Series" = '' then begin
                IF GateEntryLocSetup.GET("Entry Type", "Type", "Location Code") and (GateEntryLocSetup."Posting No. Series" <> '') then begin
                    "Posting No. Series" := GateEntryLocSetup."Posting No. Series";
                    MODIFY;
                    COMMIT;
                    //B2B FIX 19Apr2021>>
                end else begin
                    InvSetUp.Get();
                    if "Entry Type" = "Entry Type"::Inward then
                        if Type = Type::RGP then begin
                            InvSetUp.TestField("Inward RGP No. Series");
                            "Posting No. Series" := InvSetUp."Inward RGP No. Series";
                        end else
                            if Type = Type::NRGP then begin
                                InvSetUp.TestField("Inward NRGP No. Series");
                                "Posting No. Series" := InvSetUp."Inward NRGP No. Series";
                            end;
                    if "Entry Type" = "Entry Type"::Outward then
                        if Type = Type::RGP then begin
                            InvSetUp.TestField("Outward RGP No. Series");
                            "Posting No. Series" := InvSetUp."Outward RGP No. Series";
                        end else
                            if Type = Type::NRGP then begin
                                InvSetUp.TestField("Outward NRGP No. Series");
                                "Posting No. Series" := InvSetUp."Outward NRGP No. Series";
                            end;
                    MODIFY;
                    COMMIT;
                end;
                //B2B FIX 19Apr2021<<
            end;
            if "Posting No." = '' then begin
                "Posting No." := NoSeriesMgt.GetNextNo("Posting No. Series", "Posting Date", true);
                ModifyHeader := true;
            end;
            if ModifyHeader then begin
                MODIFY;
                COMMIT;
            end;

            GateEntryLine.LOCKTABLE;

            PostedGateEntryHeader.INIT;
            PostedGateEntryHeader.TRANSFERFIELDS(GateEntryHeader);
            PostedGateEntryHeader."Entry Type" := "Entry Type";
            PostedGateEntryHeader.Type := Type;
            PostedGateEntryHeader."No." := "Posting No.";
            PostedGateEntryHeader."No. Series" := "Posting No. Series";
            PostedGateEntryHeader."Gate Entry No." := "No.";

            if GUIALLOWED then
                Window.UPDATE(1, STRSUBSTNO(Text16503, "No.", PostedGateEntryHeader."No."));
            PostedGateEntryHeader.INSERT;

            // Posting Comments to posted tables.
            CopyCommentLines("Entry Type", "Entry Type", "No.", PostedGateEntryHeader."No.");

            Clear(PostdLoadSlpNoGVar);

            GateEntryLine.RESET;
            GateEntryLine.SETRANGE("Entry Type", "Entry Type");
            GateEntryLine.SetRange(Type, Type);
            GateEntryLine.SETRANGE("Gate Entry No.", "No.");
            LineCount := 0;
            GateEntType := GateEntryLine."Source Type";
            PostdLoadSlpNoGVar := GateEntryLine."Source No.";
            if GateEntryLine.FINDSET then
                repeat
                    LineCount += 1;
                    if GUIALLOWED then
                        Window.UPDATE(2, LineCount);
                    PostedGateEntryLine.INIT;
                    PostedGateEntryLine.TRANSFERFIELDS(GateEntryLine);
                    PostedGateEntryLine."Entry Type" := PostedGateEntryHeader."Entry Type";
                    PostedGateEntryLine.Type := PostedGateEntryHeader.Type;
                    PostedGateEntryLine."Gate Entry No." := PostedGateEntryHeader."No.";
                    PostedGateEntryLine.INSERT;

                until GateEntryLine.NEXT = 0;

            PostGate();
            Message('Posted GateEntry No is %1', PostedGateEntryHeader."No.");

            //PK Identified
            DELETE;
            GateEntryLine.DELETEALL;
        end;
        if GUIALLOWED then
            Window.CLOSE;
        Rec := GateEntryHeader;
        Commit();//PK-Added
        SendMailAlerts(GateEntryHeader);//PK-Added                       
    end;

    var
        PostdLoadSlpNoGVar: Code[20];
        GateEntType: integer;
        GateEntryHeader: Record "Gate Entry Header";
        GateEntryLine: Record "Gate Entry Line";
        PostedGateEntryHeader: Record "Posted Gate Entry Header";
        PostedGateEntryLine: Record "Posted Gate Entry Line";
        Text16500: Label 'There is nothing to post.';
        Text16501: Label 'Posting Lines #2######\';
        Text16502: Label 'Gate Entry.';
        Text16503: Label 'Gate Entry %1 -> Posted Gate Entry %2.';
        GateEntryLocSetup: Record "Gate Entry Location Setup";
        GateEntryCommentLine: Record "Gate Entry Comment Line";
        GateEntryCommentLine2: Record "Gate Entry Comment Line";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        Window: Dialog;
        ModifyHeader: Boolean;
        LineCount: Integer;
        Mailmng: Codeunit "Mail Management";

    procedure CopyCommentLines(FromEntryType: Integer; ToEntryType: Integer; FromNumber: Code[20]; ToNumber: Code[20]);
    begin
        GateEntryCommentLine.SETRANGE("Gate Entry Type", FromEntryType);
        GateEntryCommentLine.SETRANGE("No.", FromNumber);
        if GateEntryCommentLine.FINDSET then
            repeat
                GateEntryCommentLine2 := GateEntryCommentLine;
                GateEntryCommentLine2."Gate Entry Type" := ToEntryType;
                GateEntryCommentLine2."No." := ToNumber;
                GateEntryCommentLine2.INSERT;
            until GateEntryCommentLine.NEXT = 0;
    end;



    procedure PostGate()
    var
        PostdLoadSlpHdr: Record "Posted Loading Slip Header";
        PostLoadSlpLne: Record "Posted Loading Slip Line";
        LoadSlpLne: Record "Loading Slip Line";
        LoadSlpLVar: Code[10];
        LadSlpLnLRec: Record "Loading Slip Line";
        VehicleNo: Code[20];
        TransportLedger: Record "Transport Ledger Entries";
        VehicleTransporter: record "Transporter Vehicle";
        DetailedTransport: record "Detailed Tran Ledger Entry";
        NoSeriesMng: Codeunit NoSeriesManagement;
        InvSetUp: Record "Inventory Setup";
        TempInv: Record "Inventory Setup";
        TransportContract: record "Transport Contract Types";
        LoadingSlipLRec: record "Loading Slip Line";
        LastDeatiledLedger: Record "Detailed Tran Ledger Entry";
        WareShpLnrLRec: Record "Warehouse Shipment Line";
        SalesHeader: record "Sales Header";
        TransferHeader: record "Transfer Header";
        ShipToAddress: Record "Ship-to Address";
        TraLedgerEnt: Record "Transport Ledger Entries";
        TraLdgExist: Boolean;
        VendGRec: Record Vendor;
    BEGIN
        //B2B.P.K.T Code Changed in following procedure

        //IF VendGRec."Transport Contract" = VendGRec."Transport Contract"::Aglevinthis THEN BEGIN
        clear(LoadSlpLVar);
        clear(VehicleNo);
        PostdLoadSlpHdr.Reset();
        PostdLoadSlpHdr.SetRange("No.", PostdLoadSlpNoGVar);
        PostdLoadSlpHdr.SetRange("Vehicle By", PostdLoadSlpHdr."Vehicle By"::Transporter);//PK on 01.16.2021
        IF PostdLoadSlpHdr.FindFirst() then begin
            LoadSlpLVar := PostdLoadSlpHdr."Loading Slip Ref No.";
            VehicleNo := PostdLoadSlpHdr."Vehicle No.";
            //if VehicleTransporter.Get(VehicleNo) then begin
            VehicleTransporter.RESET;
            VehicleTransporter.SetRange("Vehicle Reg No.", VehicleNo);
            IF VehicleTransporter.findfirst then BEGIN
                IF NOT VendGRec.GET(VehicleTransporter."Vendor No.") THEN
                    ERROR('Vendor Does not Exists.');
                IF PostdLoadSlpHdr."Loading Slip Ref No." <> '' then BEGIN
                    TraLdgExist := false;
                    TransportLedger.Init();
                    TransportLedger."Vehicle No" := VehicleNo;
                    InvSetUp.Get();
                    InvSetUp.TestField("Trip ID NoSeries");
                    TransportLedger."Trip Id" := NoSeriesMng.GetNextNo(InvSetUp."Trip ID NoSeries", 0D, true);
                    TransportLedger."Vehicle Type" := VehicleTransporter."Vehicle Type";
                    TransportLedger."Vehicle Capacity" := VehicleTransporter."Vehicle Tonage";
                    TransportLedger."Vehicle Loaded" := PostdLoadSlpHdr."Loaded Qty";
                    TransportLedger."loading Slip No." := LoadSlpLVar;
                    TransportLedger."Posted Slip Number" := PostdLoadSlpHdr."No.";
                    TransportLedger."Contract Type" := VendGRec."Transport Contract";
                    TransportLedger."Transport Location" := PostdLoadSlpHdr."Transport to Location";
                    TransportLedger."Vehicle By" := PostdLoadSlpHdr."Vehicle By";
                    TransportLedger."Posted Gate Entry No." := PostedGateEntryLine."Gate Entry No.";
                    TransportLedger."Own Vehicle No." := PostdLoadSlpHdr."Own Vehicle No.";
                    TransportLedger.Insert(true);
                    Message('Transport Ledger Entries posted succesfully');
                END else begin
                    TraLdgExist := TRUE;
                    TraLedgerEnt.reset;
                    TraLedgerEnt.SetRange("Posted Slip Number", PostdLoadSlpHdr."Loading Slip Ref No.");
                    IF TraLedgerEnt.findfirst then;
                end;
                PostLoadSlpLne.reset;
                PostLoadSlpLne.SetRange("Document No.", PostdLoadSlpHdr."No.");
                if PostLoadSlpLne.FindSet() then
                    repeat
                        LastDeatiledLedger.Reset();
                        if LastDeatiledLedger.FindLast() then;
                        DetailedTransport.Init();
                        DetailedTransport."Entry No" := LastDeatiledLedger."Entry No" + 1;
                        DetailedTransport."Vehicle No" := VehicleNo;
                        DetailedTransport."loading Slip No." := LoadSlpLVar;
                        DetailedTransport."Posted Slip Number" := PostLoadSlpLne."Document No.";
                        DetailedTransport."LoadingSlip Lines No." := PostLoadSlpLne."Line No.";
                        IF NOT TraLdgExist THEN begin
                            DetailedTransport."Vehicle Type" := TransportLedger."Vehicle Type";
                            DetailedTransport."Vehicle Capacity" := TransportLedger."Vehicle Capacity";
                            DetailedTransport."Vehicle Loaded" := TransportLedger."Vehicle Loaded";
                            DetailedTransport."Trip Date" := TransportLedger."Trip Date";
                            DetailedTransport."Trip Id" := TransportLedger."Trip Id";
                            DetailedTransport."Contract Type" := TransportLedger."Contract Type";
                            DetailedTransport."Transprt Ledger Entrires" := TransportLedger."Entry No";
                            IF VendGRec."Transport Contract" = VendGRec."Transport Contract"::Aglevinthis THEN BEGIN
                                TransportContract.reset;
                                TransportContract.SetRange("Vendor No.", VehicleTransporter."Vendor No.");
                                TransportContract.SetRange("From Location", PostLoadSlpLne."From Location");
                                TransportContract.SetRange("To-Location", PostLoadSlpLne."To Location");
                                TransportContract.SetRange("Contract Type", VendGRec."Transport Contract");
                                IF TransportContract.findfirst then begin
                                    //TransportContract.TestField("New Distance");
                                    DetailedTransport."Distance Travelled" := TransportContract."New Distance";
                                end;
                                // else Error('Transport Contract Type does not exist with filters %1 .%2.%3..%4', VehicleTransporter."Vendor No.", PostLoadSlpLne."From Location", PostLoadSlpLne."To Location", VendGRec."Transport Contract");
                            end;
                        END ELSE BEGIN
                            DetailedTransport."Vehicle Type" := TraLedgerEnt."Vehicle Type";
                            DetailedTransport."Vehicle Capacity" := TraLedgerEnt."Vehicle Capacity";
                            DetailedTransport."Vehicle Loaded" := TraLedgerEnt."Vehicle Loaded";
                            DetailedTransport."Trip Date" := TraLedgerEnt."Trip Date";
                            DetailedTransport."Trip Id" := TraLedgerEnt."Trip Id";
                            DetailedTransport."Contract Type" := TraLedgerEnt."Contract Type";
                            DetailedTransport."Transprt Ledger Entrires" := TraLedgerEnt."Entry No";
                            IF VendGRec."Transport Contract" = VendGRec."Transport Contract"::Aglevinthis THEN BEGIN
                                TransportContract.reset;
                                TransportContract.SetRange("Vendor No.", VehicleTransporter."Vendor No.");
                                TransportContract.SetRange("From Location", PostLoadSlpLne."From Location");
                                TransportContract.SetRange("To-Location", PostLoadSlpLne."To Location");
                                TransportContract.SetRange("Contract Type", VendGRec."Transport Contract");
                                IF TransportContract.findfirst then begin
                                    //TransportContract.TestField("New Distance");
                                    DetailedTransport."Distance Travelled" := TransportContract."New Distance";
                                end;
                                //else Error('Transport Contract Type does not exist with filters %1 .%2.%3..%4', VehicleTransporter."Vendor No.", PostLoadSlpLne."From Location", PostLoadSlpLne."To Location", VendGRec."Transport Contract");
                            end;
                        end;
                        DetailedTransport."From Location" := PostLoadSlpLne."From Location";
                        DetailedTransport."To Location" := PostLoadSlpLne."To Location";
                        DetailedTransport.Insert();
                    until PostLoadSlpLne.Next() = 0;
            end;
            //end;
        end;
    END;


    procedure SendMailAlerts(GEH: Record "Gate Entry Header")
    var
        DocAmounttoPay: Decimal;
        PostdLoadSlpHdr: Record "Posted Loading SLip Header";
        PostedLoadingSlipLine: Record "Posted Loading Slip Line";
        PostedLoadingSlipLine2: Record "Posted Loading Slip Line";
        BranchItemTransportRates: Record "Branch Item Transport Rates";
        PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header";
        WhseShipmentHeader: Record "Warehouse Shipment Header";
        TransporterVehicleGV: Record "Transporter Vehicle";
        Vend: Record Vendor;
        Cust: Record Customer;
        I: Integer;
        VendNum: Code[20];
        CustNum: Code[20];
        PostedWhseShipmentLne: Record "Posted Whse. Shipment Line";
        WhseShipmntLine: Record "Warehouse Shipment Line";
        PrevCust: code[20];
        TransOrd: Record "Transfer Header";
        Loc: Record Location;
    begin
        //PKONJ15 entire function
        Clear(PrevCust);
        PostdLoadSlpHdr.Reset();
        PostdLoadSlpHdr.SetRange("Gate Entry No.", GEH."No.");//PKONJ16
        IF PostdLoadSlpHdr.FindFirst() then begin
            PostedLoadingSlipLine.Reset;
            PostedLoadingSlipLine.SetCurrentKey("Customer No.");
            PostedLoadingSlipLine.SetRange("Document No.", PostdLoadSlpHdr."No.");
            PostedLoadingSlipLine.SetFilter("Customer No.", '<>%1', '');
            if PostedLoadingSlipLine.FindSet then
                Repeat
                    IF PrevCust <> PostedLoadingSlipLine."Customer No." then begin
                        PrevCust := PostedLoadingSlipLine."Customer No.";
                        IF Cust.get(PrevCust) then
                            IF (Cust."E-Mail" <> '') THEN
                                SendingMailCust(Cust."E-Mail", PostedLoadingSlipLine, PostedWhseShipmentLne, GEH)
                            else
                                InsertLog('Email ID Not Defined. Customer - ' + Cust."No.", PostedLoadingSlipLine."Document No.", 2);
                    end;
                until PostedLoadingSlipLine.Next() = 0;
            PostedLoadingSlipLine.Reset;
            PostedLoadingSlipLine.SetCurrentKey("Customer No.");
            PostedLoadingSlipLine.SetRange("Document No.", PostdLoadSlpHdr."No.");
            PostedLoadingSlipLine.SetFilter("Customer No.", '');
            if PostedLoadingSlipLine.FindSet then
                Repeat
                    IF PrevCust <> PostedLoadingSlipLine."Order No." then begin
                        PrevCust := PostedLoadingSlipLine."Order No.";
                        //PKONJU5
                        IF TransOrd.get(PrevCust) AND (Loc.GET(TransOrd."Transfer-to Code")) then
                            IF (Loc."E-Mail" <> '') THEN
                                SendingMailLoc(Loc."E-Mail", PostedLoadingSlipLine, PostedWhseShipmentLne, GEH)
                            else
                                InsertLog('Email ID Not Defined. Location - ' + Loc.code, PostedLoadingSlipLine."Document No.", 3);
                        //PKONJU5
                    end;
                until PostedLoadingSlipLine.Next() = 0;
            //PKONJ15 full function
            /*PostedWhseShipmentHeader.Reset;
            PostedWhseShipmentHeader.Setrange("Whse. Shipment No.", PostedLoadingSlipLine."No.");
            if PostedWhseShipmentHeader.FindFirst then begin
                PostedWhseShipmentLne.RESET;
                PostedWhseShipmentLne.SetRange("No.", PostedWhseShipmentHeader."No.");
                PostedWhseShipmentLne.SetRange("Source Document", PostedWhseShipmentLne."Source Document"::"Sales Order");
                IF PostedWhseShipmentLne.findfirst then begin
                    CustNum := PostedLoadingSlipLine."Customer No.";
                    IF Cust.get(CustNum) AND (Cust."E-Mail" <> '') then
                        SendingMailcust(Cust."E-Mail", PostedLoadingSlipLine, PostedWhseShipmentLne)
                end else begin
                    //Balu 05232021>>
                    WhseShipmentHeader.Reset;
                    WhseShipmentHeader.Setrange("No.", PostedLoadingSlipLine."No.");
                    if WhseShipmentHeader.FindFirst then begin
                        WhseShipmntLine.RESET;
                        WhseShipmntLine.SetRange("No.", WhseShipmentHeader."No.");
                        WhseShipmntLine.SetRange("Source Document", WhseShipmntLine."Source Document"::"Sales Order");
                        IF WhseShipmntLine.findfirst then begin
                            CustNum := PostedLoadingSlipLine."Customer No.";
                            IF Cust.get(CustNum) AND (Cust."E-Mail" <> '') then
                                SendingMailcust2(Cust."E-Mail", PostedLoadingSlipLine, WhseShipmntLine)
                        end;
                        //Balu 05232021<<
                    end;
                end;
            end;
            until PostedLoadingSlipLine.Next() = 0;*///PKONJ15
            TransporterVehicleGV.Reset();
            TransporterVehicleGV.SetRange("Vehicle Reg No.", PostdLoadSlpHdr."Vehicle No.");
            if TransporterVehicleGV.findfirst then BEGIN
                VendNum := TransporterVehicleGV."Vendor No.";
                IF Vend.get(VendNum) then
                    IF (Vend."E-Mail" <> '') THEN
                        SendingMailVend(Vend."E-Mail", PostdLoadSlpHdr, GEH)
                    else
                        InsertLog('Email ID Not Defined. Vendor- ' + Vend."No." + ' ' + Vend.name, PostedLoadingSlipLine."Document No.", 3);
            end;
        end;
    end;

    procedure SendingMailVend(ToId: Text[200]; PostLdSli: Record "Posted Loading SLip Header"; GEH: Record "Gate Entry Header")
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
        PSLHD: Record "Posted Loading SLip Header";
        Vend: Record Vendor;
    begin
        //PKONJ16.2 entire function
        SMTPMailSetup.get();
        SenderAddr := SMTPMailSetup."User ID";
        IF Vend.GET(PostLdSli."Party No.") then; //Moved Place
        IF Mailmng.CheckValidEmailAddress(ToId) THEN
            RecepientAddr.Add(ToId)
        else
            InsertLog('Issue In Vendor Email - ' + Vend."No." + ' ' + ToId, PostLdSli."No.", 1);
        //RecepientAddr.Add('<EMAIL>');
        IF Mailmng.CheckValidEmailAddress('<EMAIL>') THEN
            RecepientAddr.Add('<EMAIL>')
        else
            InsertLog('Issue In Email - ' + '<EMAIL>', PostLdSli."No.", 1);
        IF Mailmng.CheckValidEmailAddress('<EMAIL>') THEN
            RecepientAddr.Add('<EMAIL>')
        else
            InsertLog('Issue In Email - ' + '<EMAIL>', PostLdSli."No.", 1);

        //RecepientAddr.Add('<EMAIL>');


        IF (SenderAddr <> '') and (ToId <> '') THEN BEGIN
            SubjectTxt := 'Gate Entry Posted For Loading Slip -' + PostLdSli."No." + ' On ' + FORMAT(WorkDate());
            SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
            SmtpMail.AppendBody('Dear ' + Vend.Name + ',');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('The Following Materials are left for delivery with your vehicle No. - ' + GEH."Vehicle No.");
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Gate Entry No.-' + GEH."No.");
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Vehicle No.-' + GEH."Vehicle No.");
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Transfer From -' + PostLdSli."Transport From Location");
            SmtpMail.AppendBody('<br>');
            //PKONJU5
            SmtpMail.AppendBody('Transfer To -' + PostLdSli."Transport To Location");
            SmtpMail.AppendBody('<br>');
            //PKONJU5
            SmtpMail.AppendBody('Transporter Name -' + Vend.Name);
            /* SmtpMail.AppendBody('<br>');
             SmtpMail.AppendBody('Transporter Mobile No.-' + FORMAT(Vend."Phone No."));*/ //PKONJ17.2
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Driver Name -' + FORMAT(PostLdSli."Driver Name"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Driver Contact No.-' + FORMAT(PostLdSli."Driver Contact No."));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Date-' + FORMAT(GEH."Posting Date"));
            SMTPMail.AppendBody(CreateEmailBodyVend(PostLdSli, GEH));
            SmtpMail.AppendBody('</table>');
            SmtpMail.AppendBody('</body>');
            SmtpMail.AppendBody('</html>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Please Check with the vehicle driver for delivery related queries.');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Regards,');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody(FORMAT('Despatch team,'));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody(FORMAT('CHI Limited.'));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
            //PKONJU5>>
            IF SMTPMAil.send then
                InsertLog('SMTP Mail Sent Succesfully. Vendor No. - ' + Vend."No." + ' ' + Vend.Name, PostLdSli."No.", 1)
            else
                InsertLog('Failure in sending Email. Vendor No. - ' + Vend."No." + ' ' + Vend.Name, PostLdSli."No.", 1);
            //PKONJU5<<
        end;
    end;

    Local procedure CreateEmailBodyVend(PostLdSliLp: Record "Posted Loading SLip Header"; GEH: Record "Gate Entry Header") EmailBodyText: Text
    var
        CheckDate: Date;
        PSTLS: Record "Posted Loading SLip Header";
        PSTLSLine: Record "Posted Loading Slip Line";
    begin

        PSTLSLine.Reset();
        PSTLSLine.SetRange("Document No.", PostLdSliLp."No.");
        IF PSTLSLine.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += '</tr>';
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Posted Loading Slip No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Order No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity Despatched');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty.Loaded in Tons');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Trays');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Pallets');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Document No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Order No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine.Quantity);
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty. Loading");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Wt. of the Qty Loading in Tons");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Trays");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Pallets");
                EmailBodyText += '</tr>';
            until PSTLSLine.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    /*procedure SendingMailCust(ToId: Text[200]; PostLdSli: Record "Posted Loading SLip line"; pwsl: Record "Posted Whse. Shipment Line")
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
    begin
        SMTPMailSetup.get();
        SenderAddr := SMTPMailSetup."User ID";
        RecepientAddr.Add(ToId);
        RecepientAddr.Add('<EMAIL>');//Balu 05092021
        //RecepientAddr.Add('<EMAIL>');
        IF (SenderAddr <> '') and (ToId <> '') THEN BEGIN
            SubjectTxt := 'Gate Entry Posted For Loading Slip - ' + PostLdSli."No." + ' - ' + FORMAT(WorkDate());
            SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
            SMTPMail.AppendBody(CreateEmailBodyCust(PostLdSli, pwsl));
            SMTPMAil.send;
        end;
    end;*/

    Local procedure CreateEmailBodyCust(PostLdSliLp: Record "Posted Loading SLip line"; pwsl: Record "Posted Whse. Shipment Line") EmailBodyText: Text
    var
        CheckDate: Date;
        PSTLS: Record "Posted Loading SLip Header";
        PSTLSLine: Record "Posted Loading Slip Line";
    begin

        PSTLSLine.Reset();
        //PSTLSLine.SetRange("Document No.", PostLdSliLp."No.");//b2bpksalecorr13
        PSTLSLine.SetRange("Document No.", PostLdSliLp."Document No.");//b2bpksalecorr13
        PSTLSLine.SetRange("Line No.", PostLdSliLp."Line No.");
        IF PSTLSLine.FindSet() then begin
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Posted Loading Slip No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty.Loaded in Tons');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Trays');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Pallets');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Document No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine.Quantity);
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Wt. of the Qty Loading in Tons");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Trays");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Pallets");
                EmailBodyText += '</tr>';
            until PSTLSLine.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;
    //Balu 05232021>>
    procedure SendingMailCust2(ToId: Text[200]; PostLdSli: Record "Posted Loading SLip line"; wsl: Record "Warehouse Shipment Line")
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
    begin
        SMTPMailSetup.get();
        SenderAddr := SMTPMailSetup."User ID";
        RecepientAddr.Add(ToId);
        RecepientAddr.Add('<EMAIL>');//Balu 05092021
        //RecepientAddr.Add('<EMAIL>');
        IF (SenderAddr <> '') and (ToId <> '') THEN BEGIN
            SubjectTxt := 'Gate Entry Posted For Loading Slip - ' + PostLdSli."No." + ' - ' + FORMAT(WorkDate());
            SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
            SMTPMail.AppendBody(CreateEmailBodyCust2(PostLdSli, wsl));
            SMTPMAil.send;
        end;
    end;

    Local procedure CreateEmailBodyCust2(PostLdSliLp: Record "Posted Loading SLip line"; wsl: Record "Warehouse Shipment Line") EmailBodyText: Text
    var
        CheckDate: Date;
        PSTLS: Record "Posted Loading SLip Header";
        PSTLSLine: Record "Posted Loading Slip Line";
    begin

        PSTLSLine.Reset();
        //PSTLSLine.SetRange("Document No.", PostLdSliLp."No.");//b2bpksalecorr13
        PSTLSLine.SetRange("Document No.", PostLdSliLp."Document No.");//b2bpksalecorr13
        PSTLSLine.SetRange("Line No.", PostLdSliLp."Line No.");
        IF PSTLSLine.FindSet() then begin
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Posted Loading Slip No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty.Loaded in Tons');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Trays');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Pallets');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Document No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine.Quantity);
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Wt. of the Qty Loading in Tons");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Trays");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Pallets");
                EmailBodyText += '</tr>';
            until PSTLSLine.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;
    //Balu 05232021<<

    procedure SendingMailCust(ToId: Text[200]; PostLdSli: Record "Posted Loading SLip line"; pwsl: Record "Posted Whse. Shipment Line"; GEH: Record "Gate Entry Header")
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
        PSLHD: Record "Posted Loading SLip Header";
        Loc: record Location;
        sale: Record "Sales Header";
        Vend: Record Vendor;
        OwnDet: Boolean;
        Respo: Record "Responsibility Center";
    begin
        SMTPMailSetup.get();
        SenderAddr := SMTPMailSetup."User ID";

        IF Mailmng.CheckValidEmailAddress(ToId) THEN
            RecepientAddr.Add(ToId)
        else
            InsertLog('Issue In Customer Email ID - ' + ToId + ' ' + PostLdSli."Customer No.", PostLdSli."Document No.", 2);
        //RecepientAddr.Add('<EMAIL>');
        IF sale.GET(1, PostLdSli."Order No.") AND Loc.get(sale."Location Code") then begin
            IF Mailmng.CheckValidEmailAddress('<EMAIL>') THEN
                RecepientAddr.Add('<EMAIL>')
            else
                InsertLog('Issue in Email ID in Branch Transfer - <EMAIL>', PostLdSli."Document No.", 2);

            IF Mailmng.CheckValidEmailAddress('<EMAIL>') THEN
                RecepientAddr.Add('<EMAIL>')
            else
                InsertLog('Issue in Email ID in Transfer - <EMAIL>', PostLdSli."Document No.", 2);
            //PKONAU18>>
            PSLHD.GET(PostLdSli."Document No.");
            IF Respo.GET(PSLHD."Responsibility Center") AND Respo."Despatch Mail Alert" THEN BEGIN
                IF Respo."Despatch Mail Alert 1" <> '' THEN
                    IF Mailmng.CheckValidEmailAddress(Respo."Despatch Mail Alert 1") THEN
                        RecepientAddr.Add(Respo."Despatch Mail Alert 1")
                    else
                        InsertLog(sale."No." + ' Issue in Email ID 1 in Responsibility Centre ' + PSLHD."Responsibility Center" + ' Email ID ' + Respo."Despatch Mail Alert 1", PostLdSli."Document No.", 2);
                IF Respo."Despatch Mail Alert 2" <> '' THEN
                    IF Mailmng.CheckValidEmailAddress(Respo."Despatch Mail Alert 2") THEN
                        RecepientAddr.Add(Respo."Despatch Mail Alert 2")
                    else
                        InsertLog(sale."No." + ' Issue in Email ID 2 in Responsibility Centre ' + PSLHD."Responsibility Center" + ' Email ID ' + Respo."Despatch Mail Alert 2", PostLdSli."Document No.", 2);
                IF Respo."Despatch Mail Alert 3" <> '' THEN
                    IF Mailmng.CheckValidEmailAddress(Respo."Despatch Mail Alert 3") THEN
                        RecepientAddr.Add(Respo."Despatch Mail Alert 3")
                    else
                        InsertLog(sale."No." + ' Issue in Email ID 3 in Responsibility Centre ' + PSLHD."Responsibility Center" + ' Email ID ' + Respo."Despatch Mail Alert 3", PostLdSli."Document No.", 2);

            END ELSE BEGIN
                //PKONAU18<<
                IF Loc."E-Mail" <> '' THEN
                    IF Mailmng.CheckValidEmailAddress(Loc."E-Mail") THEN
                        RecepientAddr.Add(Loc."E-Mail")
                    else
                        InsertLog(sale."No." + ' Issue in Email ID in Location ' + Loc.code + ' Email ID ' + Loc."E-Mail", PostLdSli."Document No.", 2);
                IF Loc."E-Mail2" <> '' THEN
                    IF Mailmng.CheckValidEmailAddress(Loc."E-Mail2") THEN
                        RecepientAddr.Add(Loc."E-Mail2")
                    else
                        InsertLog(sale."No." + ' Issue in Email ID 2 in Location ' + Loc.code + ' Email ID ' + Loc."E-Mail2", PostLdSli."Document No.", 2);

                IF Loc."E-Mail3" <> '' THEN
                    IF Mailmng.CheckValidEmailAddress(Loc."E-Mail3") THEN
                        RecepientAddr.Add(Loc."E-Mail3")
                    else
                        InsertLog(sale."No." + ' Issue in Email ID 3 in Location ' + Loc.code + ' Email ID ' + Loc."E-Mail3", PostLdSli."Document No.", 2);
            end;//PKONAU18
        end;
        //RecepientAddr.Add('<EMAIL>');
        IF (SenderAddr <> '') and (ToId <> '') THEN BEGIN
            IF PSLHD.GET(PostLdSli."Document No.") then begin
                IF Not Vend.GET(PSLHD."Party No.") then
                    OwnDet := true
                else
                    OwnDet := false;
                SubjectTxt := 'Gate Entry Posted For Loading Slip - ' + PSLHD."No." + ' - ' + FORMAT(WorkDate());

                SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
                //SMTPMail.AppendBody(CreateEmailBodyCust(PostLdSli, pwsl));//PKONj15
                SmtpMail.AppendBody('Dear ' + PostLdSli."Customer Name" + ',');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('The Following Materials are left for delivery.');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Gate Entry No.-' + GEH."No.");
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Vehicle No.-' + GEH."Vehicle No.");
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Transfer From -' + PSLHD."Transport From Location");
                SmtpMail.AppendBody('<br>');//PKONJ18.2
                SmtpMail.AppendBody('Transfer To - Customer');//PKONJ18.2
                IF Not OwnDet THEN BEGIN
                    SmtpMail.AppendBody('<br>');
                    SmtpMail.AppendBody('Transporter Name -' + Vend.Name);
                    /*SmtpMail.AppendBody('<br>');
                    SmtpMail.AppendBody('Transporter Mobile No.-' + FORMAT(Vend."Phone No."));*///PKONJ17.2
                END;
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Driver Name -' + FORMAT(PSLHD."Driver Name"));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Driver Contact No.-' + FORMAT(PSLHD."Driver Contact No."));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Date-' + FORMAT(GEH."Posting Date"));
                SMTPMail.AppendBody(CreateEmailBodCust(PSLHD, PostLdSli, GEH));//PKONj15
                SmtpMail.AppendBody('</table>');
                SmtpMail.AppendBody('</body>');
                SmtpMail.AppendBody('</html>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Please Check with the vehicle driver for delivery related queries.');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Regards,');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody(FORMAT('Despatch team,'));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody(FORMAT('CHI Limited.'));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
                //>>PKONJU5
                IF SMTPMAil.send then
                    InsertLog('SMTP Mail Sent Succesfully To Customer - ' + PostLdSli."Customer No." + ' ' + PostLdSli."Customer Name", PostLdSli."Document No.", 2)
                else
                    InsertLog('Failure in sending Email To Customer - ' + PostLdSli."Customer No." + ' ' + PostLdSli."Customer Name", PostLdSli."Document No.", 2);
                //<<PKONJU5
            end;
        end;
    end;

    procedure SendingMailLoc(ToId: Text[200]; PostLdSli: Record "Posted Loading SLip line"; pwsl: Record "Posted Whse. Shipment Line"; GEH: Record "Gate Entry Header")
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
        PSLHD: Record "Posted Loading SLip Header";
        TransOrd: Record "Transfer Header";
        Locat: Record Location;
        Vend: Record Vendor;
        OwnDet: Boolean;
    begin
        SMTPMailSetup.get();
        SenderAddr := SMTPMailSetup."User ID";

        IF TransOrd.GET(PostLdSli."Order No.") AND Locat.get(TransOrd."Transfer-to Code") THEN BEGIN
            IF Mailmng.CheckValidEmailAddress(ToId) THEN
                RecepientAddr.Add(ToId)
            else
                InsertLog('Issue In Location Card Email - ' + Locat.Code + ' ' + ToId, PostLdSli."Document No.", 3);
            //RecepientAddr.Add('<EMAIL>');
            IF Mailmng.CheckValidEmailAddress('<EMAIL>') THEN
                RecepientAddr.Add('<EMAIL>')
            else
                InsertLog('Issue in Email ID in Branch Transfer - <EMAIL>', PostLdSli."Document No.", 3);
            //PKONAU18>>
            /*IF Mailmng.CheckValidEmailAddress('<EMAIL>') THEN
                RecepientAddr.Add('<EMAIL>')
            else
                InsertLog('Issue in Email ID in Transfer - <EMAIL>', PostLdSli."Document No.", 3);*/ //PKONAU18<<


            IF Locat."E-Mail2" <> '' then
                IF Mailmng.CheckValidEmailAddress(Locat."E-Mail2") THEN
                    RecepientAddr.Add(Locat."E-Mail2")
                else
                    InsertLog('Issue in Email ID 2 in location Card - ' + Locat.Code, PostLdSli."Document No.", 3);
            IF Locat."E-Mail3" <> '' then
                IF Mailmng.CheckValidEmailAddress(Locat."E-Mail3") THEN
                    RecepientAddr.Add(Locat."E-Mail3")
                else
                    InsertLog('Issue in Email ID 3 in location Card - ' + Locat.code, PostLdSli."Document No.", 3);
        END;
        IF TransOrd.GET(PostLdSli."Order No.") AND Locat.get(TransOrd."Transfer-from Code") THEN BEGIN
            IF Locat."E-Mail" <> '' then
                IF Mailmng.CheckValidEmailAddress(Locat."E-Mail") THEN
                    RecepientAddr.Add(Locat."E-Mail")
                else
                    InsertLog('Issue in Email ID in location Card - ' + Locat.code, PostLdSli."Document No.", 3);

            IF Locat."E-Mail2" <> '' then
                IF Mailmng.CheckValidEmailAddress(Locat."E-Mail2") THEN
                    RecepientAddr.Add(Locat."E-Mail2")
                else
                    InsertLog('Issue in Email ID 2 in location Card - ' + Locat.code, PostLdSli."Document No.", 3);

            IF Locat."E-Mail3" <> '' then
                IF Mailmng.CheckValidEmailAddress(Locat."E-Mail3") THEN
                    RecepientAddr.Add(Locat."E-Mail3")
                else
                    InsertLog('Issue in Email ID 3 in location Card - ' + Locat.code, PostLdSli."Document No.", 3);
        END;
        IF (SenderAddr <> '') and (ToId <> '') THEN BEGIN
            IF PSLHD.GET(PostLdSli."Document No.") then begin
                IF Not Vend.GET(PSLHD."Party No.") then
                    OwnDet := true
                else
                    OwnDet := false;
                SubjectTxt := 'Gate Entry Posted For Loading Slip - ' + PSLHD."No." + ' - ' + FORMAT(WorkDate());
                SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
                //SMTPMail.AppendBody(CreateEmailBodyCust(PostLdSli, pwsl));//PKONj15
                SmtpMail.AppendBody('Dear ' + PostLdSli."Customer Name" + ',');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('The Following Materials are left for delivery.');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Gate Entry No.-' + GEH."No.");
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Vehicle No.-' + GEH."Vehicle No.");
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Transfer From -' + PSLHD."Transport From Location");
                SmtpMail.AppendBody('<br>');//PKONJ18.2
                SmtpMail.AppendBody('Transfer To -' + TransOrd."Transfer-to Code");//PKONJ18.2
                IF Not OwnDet THEN BEGIN
                    SmtpMail.AppendBody('<br>');
                    SmtpMail.AppendBody('Transporter Name -' + Vend.Name);
                    /*SmtpMail.AppendBody('<br>');
                    SmtpMail.AppendBody('Transporter Mobile No.-' + FORMAT(Vend."Phone No."));*///PKONJ17.2
                END;
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Driver Name -' + FORMAT(PSLHD."Driver Name"));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Driver Contact No.-' + FORMAT(PSLHD."Driver Contact No."));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Date-' + FORMAT(GEH."Posting Date"));
                SMTPMail.AppendBody(CreateEmailBodtrans(PSLHD, PostLdSli, GEH));//PKONj15
                SmtpMail.AppendBody('</table>');
                SmtpMail.AppendBody('</body>');
                SmtpMail.AppendBody('</html>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Please Check with the vehicle driver for delivery related queries.');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Regards,');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody(FORMAT('Despatch team,'));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody(FORMAT('CHI Limited.'));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
                //>>PKONJU5
                IF SMTPMAil.send then
                    InsertLog('SMTP Mail Sent Succesfully To Branch ', PostLdSli."Document No.", 3)
                else
                    InsertLog('Failure in sending Email To Branch ', PostLdSli."Document No.", 3);
                //<<PKONJU5
            end;
        end;
    end;

    Local procedure CreateEmailBodCust(PostLdSliLp: Record "Posted Loading SLip Header"; PSTLSLineLPA: Record "Posted Loading Slip Line"; GEH: Record "Gate Entry Header") EmailBodyText: Text
    var
        CheckDate: Date;
        PSTLS: Record "Posted Loading SLip Header";
        PSTLSLine: Record "Posted Loading Slip Line";
        Vend: Record Vendor;
        OwnDet: Boolean;
    begin
        PSTLSLine.Reset();
        PSTLSLine.SetRange("Document No.", PostLdSliLp."No.");
        PSTLSLine.SetRange("Customer No.", PSTLSLineLPA."Customer No.");
        IF PSTLSLine.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += '</tr>';
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Posted Loading Slip No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Order No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity Despatched');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty.Loaded in Tons');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Trays');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Pallets');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Document No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Order No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine.Quantity);
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty. Loading");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Wt. of the Qty Loading in Tons");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Trays");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Pallets");
                EmailBodyText += '</tr>';
            until PSTLSLine.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    Local procedure CreateEmailBodtrans(PostLdSliLp: Record "Posted Loading SLip Header"; PSTLSLineLPA: Record "Posted Loading Slip Line"; GEH: Record "Gate Entry Header") EmailBodyText: Text
    var
        CheckDate: Date;
        PSTLS: Record "Posted Loading SLip Header";
        PSTLSLine: Record "Posted Loading Slip Line";
        Vend: Record Vendor;
        OwnDet: Boolean;
    begin
        PSTLSLine.Reset();
        PSTLSLine.SetRange("Document No.", PostLdSliLp."No.");
        PSTLSLine.SetRange("Order No.", PSTLSLineLPA."Order No.");
        IF PSTLSLine.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += '</tr>';
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Posted Loading Slip No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Order No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity Despatched');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty.Loaded in Tons');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Trays');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Qty Loaded in Pallets');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Document No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Order No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine.Quantity);
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty. Loading");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Wt. of the Qty Loading in Tons");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Trays");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSTLSLine."Qty Loaded in Pallets");
                EmailBodyText += '</tr>';
            until PSTLSLine.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    Procedure InsertLog(Res: Text[1024]; Detail: text[1024]; DocType: Integer) //PKONJU5
    var
        maillog: record "E-mail Log";
        EntryNo: Integer;
    begin
        IF maillog.FINDLAST THEN
            EntryNo := maillog."Entry No" + 1
        ELSE
            EntryNo := 1;

        maillog.INIT;
        maillog."Entry No" := EntryNo;

        IF DocType = 1 THEN
            maillog."Document Type" := maillog."Document Type"::"Despatch Mail-Trans"
        else
            IF DocType = 2 THEN
                maillog."Document Type" := maillog."Document Type"::"Despatch Mail-Cust"
            else
                IF DocType = 3 THEN
                    maillog."Document Type" := maillog."Document Type"::"Despatch Mail-Branch";

        maillog.Result := Res;
        maillog.Details := Detail;
        Res := '';
        Detail := '';
        maillog."User Id" := USERID;
        maillog."Date Time" := CURRENTDATETIME;
        maillog.Insert();
    end;
}

