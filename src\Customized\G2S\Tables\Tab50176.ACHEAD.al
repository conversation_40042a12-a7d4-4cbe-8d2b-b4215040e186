table 50176 "AC HEAD"
{
    Caption = 'AC HEAD';
    DataClassification = ToBeClassified;
    Permissions = tabledata 50176 = RMID;

    fields
    {
        field(1; "A/C Head"; Code[20])
        {
            Caption = 'A/C Head';
        }
        field(2; "ERP Code"; Code[20])
        {
            Caption = 'ERP Code';
            TableRelation = "G/L Account";
        }
        field(3; "ERP Item Description"; Text[100])
        {
            Caption = 'ERP Item Description';
            FieldClass = FlowField;
            CalcFormula = lookup("G/L Account".Name where("No." = field("ERP Code")));
        }
    }
    keys
    {
        key(PK; "A/C Head", "ERP Code")
        {
            Clustered = true;
        }
    }

    fieldgroups
    {
        fieldgroup(DrillWown; "A/C Head", "ERP Code", "ERP Item Description")
        {

        }
        fieldgroup(Brick; "A/C Head", "ERP Code", "ERP Item Description")
        {

        }
    }
}
