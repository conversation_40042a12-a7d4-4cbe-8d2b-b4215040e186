pageextension 50344 BankAccountStatementEXT extends "Bank Account Statement"
{
    layout
    {
        addafter("Statement Ending Balance")
        {
            field(Reversed; Reversed)
            {
                ApplicationArea = All;
                Editable = false;
            }
        }
    }

    actions
    {
        addafter("&Card")
        {
            action("Show Matching Lines")
            {
                ApplicationArea = All;

                trigger OnAction()
                begin
                    CurrPage.Control11.Page.ShowMatchLines();
                end;
            }
            action("Reverse")
            {
                ApplicationArea = All;
                Image = ReverseLines;
                trigger OnAction()
                var
                    BankAccountStatement: Record "Bank Account Statement";
                    UserSetup: Record "User Setup";
                begin
                    UserSetup.Get(UserId);
                    if not UserSetup."Reverse BRS" then
                        Error('Do not have permission to reverse bank account statement');
                    if not Confirm('Do you reverse bank account statement') then
                        exit;
                    BankAccountStatement.Reset();
                    BankAccountStatement.SetRange("Bank Account No.", "Bank Account No.");
                    BankAccountStatement.SetRange("Statement No.", "Statement No.");
                    if BankAccountStatement.FindFirst() then
                        Report.Run(Report::"Reverse Bank Statements", false, false, BankAccountStatement);
                end;
            }
            action("Closed")
            {
                ApplicationArea = All;
                Image = Closed;
                trigger OnAction()
                var
                    BankAccountStatement: Record "Bank Account Statement";
                    UserSetup: Record "User Setup";
                begin
                    UserSetup.Get(UserId);
                    if not UserSetup."Reverse BRS" then
                        Error('Do not have permission to reverse bank account statement');
                    if not Confirm('Do you want to close bank account statement') then
                        exit;
                    BankAccountStatement.Reset();
                    BankAccountStatement.SetRange("Bank Account No.", "Bank Account No.");
                    BankAccountStatement.SetRange("Statement No.", "Statement No.");
                    if BankAccountStatement.FindFirst() then
                        Report.Run(Report::"Correct Bank Statements", false, false, BankAccountStatement);
                end;
            }
        }
    }

    var
        myInt: Integer;
}