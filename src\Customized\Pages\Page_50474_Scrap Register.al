page 50474 "Scrap Register"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      : Henry
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 3.0      SAA       07-Sept-12    -> Form created for Material Replacement Scrap Register.

    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Material Replacement (Scrap)";
    SourceTableView = SORTING("Entry No.", "Item No.")
                      WHERE("Entry No." = FILTER(<> 0),
                            "Item No." = FILTER(<> ''),
                            "Sales Inv. Document No." = FILTER(''));
    UsageCategory = Lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Item No."; "Item No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Qty. to Dispose"; "Qty. to Dispose")
                {
                    ApplicationArea = all;
                }
                field("Quantity Disposed"; "Quantity Disposed")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Remaining Qty. Not Disposed"; "Remaining Qty. Not Disposed")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Mark for Sales Inv. Creation"; "Mark for Sales Inv. Creation")
                {
                    ApplicationArea = all;
                }
                field(Disposed; Disposed)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Mark for Disposal")
                {
                    ApplicationArea = all;
                    Caption = 'Mark for Disposal';

                    trigger OnAction();
                    begin
                        TESTFIELD("Qty. to Dispose");
                        if UserSetupRec.GET(USERID) then begin
                            if not "Mark for Sales Inv. Creation" then begin
                                if UserSetupRec."Mark-Scrap App" then
                                    "Mark for Sales Inv. Creation" := true else
                                    ERROR(Text002);
                                MODIFY;
                            end;
                        end;
                    end;
                }
                action("Remove Mark for Disposal")
                {
                    ApplicationArea = all;
                    Caption = 'Remove Mark for Disposal';

                    trigger OnAction();
                    begin
                        if UserSetupRec.GET(USERID) then begin
                            if "Mark for Sales Inv. Creation" then begin
                                if UserSetupRec."Mark-Scrap App" then
                                    "Mark for Sales Inv. Creation" := false else
                                    ERROR(Text003);
                                MODIFY;
                            end;
                        end;
                    end;
                }
                action("Create Sales Invoice")
                {
                    ApplicationArea = all;
                    Caption = 'Create Sales Invoice';

                    trigger OnAction();
                    begin
                        ScrapRegister.SETFILTER(ScrapRegister."Mark for Sales Inv. Creation", '%1', true);
                        if not ScrapRegister.FINDSET then
                            ERROR(Text001);
                        //CreateSalesInvoice;
                    end;
                }
            }
        }
    }

    var
        UserSetupRec: Record "User Setup";
        ScrapRegister: Record "Material Replacement (Scrap)";
        Text001: Label 'No scrap register line has been selected for sales invoice creation.';
        Text002: Label 'You do not have approval to mark the register line for Sales Invoice creation.';
        Text003: Label 'You do not have approval to remove this register line from Sales Invoice Creation.';
}

