pageextension 50060 FixedAssetList extends "Fixed Asset List"
{
    layout
    {
        addafter(Description)
        {
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
            field("PMS Card No."; "PMS Card No.")
            {
                ApplicationArea = all;
            }
            field("PMS Card Weekly Limit"; "PMS Card Weekly Limit")
            {
                ApplicationArea = all;
            }

            field("Acquisition Date"; "Acquisition Date")
            {
                ApplicationArea = all;
            }
            field("Depreciation Start Date"; "Depreciation Start Date")
            {
                ApplicationArea = all;
            }
            field("Depreciation No. Of Years"; "Depreciation No. Of Years")
            {
                ApplicationArea = all;
            }
            field("Depreciation End Date"; "Depreciation End Date")
            {
                ApplicationArea = all;
            }
            //PhaniFeb192021>>
            field("Total Acquisition Cost"; "Total Acquisition Cost")
            {
                ApplicationArea = all;
            }
            field("FA Tagging Code"; "FA Tagging Code")
            {
                ApplicationArea = ALL;
            }
            field("Book Value"; "Book Value")
            {
                ApplicationArea = ALL;
            }
            field("Salvage Value"; "Salvage Value")
            {
                ApplicationArea = ALL;

            }
            field("Maintenance Cost"; "Maintenance Cost")
            {
                ApplicationArea = ALL;

            }
            field("Depreciation Cost"; "Depreciation Cost")
            {
                ApplicationArea = ALL;

            }
            //PhaniFeb192021<<
            field("Global Dimension 1 Code"; "Global Dimension 1 Code")
            {
                ApplicationArea = all;
            }
            field("Global Dimension 2 Code"; "Global Dimension 2 Code")
            {
                ApplicationArea = all;
            }
            field("Description 1"; "Description 1")
            {
                ApplicationArea = all;
            }
            field("No. Series"; "No. Series")
            {
                ApplicationArea = all;
            }
            field("FA Posting Group"; "FA Posting Group")
            {
                ApplicationArea = all;
            }
            field(Blocked; Blocked)
            {
                ApplicationArea = all;
            }
            field(Inactive; Inactive)
            {
                ApplicationArea = all;
            }
            field("Serial No."; "Serial No.")
            {
                ApplicationArea = all;
            }
        }
    }
}