page 50227 "Branch Request  View"  //Baluon Apr 11 2022 Whole Object
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    Caption = 'Branch Request Line  View_50402';
    SourceTable = BranchRequestLine;
    Insertallowed = false;
    ModifyAllowed = false;
    DeleteAllowed = false;


    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;

                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;

                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Unit Of Measure"; "Unit Of Measure")
                {
                    ApplicationArea = all;
                }
                field("Issued Quantity"; "Issued Quantity")
                {
                    ApplicationArea = all;
                }
                field("Requested Qty(Base)"; "Requested Qty(Base)")
                {
                    ApplicationArea = all;
                }
                field("Requested Quantity"; "Requested Quantity")
                {
                    ApplicationArea = all;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = all;
                }
                field("To Location"; "To Location")
                {
                    ApplicationArea = all;
                }
                field("Created By2"; "Created By")
                {
                    ApplicationArea = all;
                }

                field("Created Date2"; "Created Date")
                {
                    ApplicationArea = all;
                }

                field("Modified By2"; "Modified By")
                {
                    ApplicationArea = all;
                }

                field("Modified Date2"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                //B2BSPON22SEPT16>>

                field("Created Date"; "Created Date")
                {
                    ApplicationArea = ALL;
                }
                //B2BSPON22SEPT16<<

            }
        }

    }


    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;
                Caption = 'Update Create and Modi Dates';

                trigger OnAction()//PKON22M16-CR220034-2
                var
                    BranchReq: Record BranchRequest;
                    BranchReqLines: Record BranchRequestLine;
                begin
                    BranchReqLines.RESET;
                    IF BranchReqLines.FINDSET THEN
                        REPEAT
                            BranchReq.GET(BranchReqLines."Document No.");
                            BranchReqLines."Created By" := BranchReq."Created By";
                            BranchReqLines."Created Date" := BranchReq."Created Date";
                            BranchReqLines."Modified By" := BranchReq."Modified By";
                            BranchReqLines."Modified Date" := BranchReq."Modified Date";
                            BranchReqLines.Modify;
                        Until BranchReqLines.Next = 0;
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        BranchRequestHdr.Reset();
        BranchRequestHdr.SetRange(No, "Document No.");
        if BranchRequestHdr.FindFirst() then;
    end;

    var
        myInt: Integer;
        BranchRequestHdr: Record BranchRequest;
}