page 50248 "Loan Posting Groups"
{
    Editable = false;
    PageType = List;
    SourceTable = "Loan Posting Groups";
    UsageCategory = lists;
    ApplicationArea = all;
    CardPageId = "Loan Posting Group";
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Posting Group"; "Posting Group")
                {
                    ApplicationArea = ALL;
                }
                field(Description; Description)
                {
                    ApplicationArea = ALL;
                }
                field("Loan Refundable Acc."; "Loan Refundable Acc.")
                {
                    ApplicationArea = ALL;
                }
            }
        }
    }
}

