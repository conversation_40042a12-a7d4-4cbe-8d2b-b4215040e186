tableextension 50037 GLSetupTabExt98 extends "General Ledger Setup"
{
    fields
    {

        field(50000; "Pms No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; "Bank Teller Confirmation CC"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(2));
        }
        field(50002; "Bank Debit Memo Nos"; Code[20])
        {
            DataClassification = CustomerContent;
        }

        field(50003; "Bank Receipt Voucher No"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50004; "Bank Payment Voucher No"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50005; "Cash Receipt Voucher No"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50006; "Cash Payment Voucher No"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50007; "Bank Jv Nos"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50008; "Bank Credit Memo Nos"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50009; "IC Purchase JV Nos."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50010; "Bank Lodgement JV Nos."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Recurring JV Nos."; code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50012; "IC JV Nos."; code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50013; "Purchase JV Nos."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50014; "Sales JV Nos."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50015; "Lagos Bank Sttle. Voucher No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Journal Voucher Nos."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50017; "Branch CPV Nos"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50018; "Posted Bank Receipt Voucher No"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50019; "Posted Cash Receipt Voucher No"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50020; "Posted Cash Payment Voucher No"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50021; "Posting Cash Recpts No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Posted Bank Nos"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50023; "Posted Bank Debit Memo Nos"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Posted Bank Credit Memo Nos"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Posted IC Purchase JV Nos."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50026; "Posted Bank Lodgement JV Nos."; code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50027; "Posted Recurring JV Nos."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50028; "Posted IC JV Nos."; code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50029; "Posted Purchase JV Nos."; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50030; "Posted Sale JV Nos."; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50031; "Posted Journal Voucher Nos."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50032; "Posted LOS Bank Sttle.No."; code[10])
        {
            DataClassification = CustomerContent;
        }

        field(50033; "Posted Branch CPV Nos"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50034; "Posted Bank Payment Voucher No"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }

        Field(50050; "Loading Jnl Template Name"; Code[10])
        {
            DataClassification = CustomerContent;
            Caption = 'Loading Jnl Template Name';
            TableRelation = "Gen. Journal Template";
        }

        Field(50052; "Loading Jnl Batch Name"; Code[10])
        {
            DataClassification = CustomerContent;
            Caption = 'Loading Jnl Batch Name';
            TableRelation = "Gen. Journal Batch".Name WHERE("Journal Template Name" = FIELD("Loading Jnl Template Name"));
        }

        Field(50054; "Loading G/L Acct No."; Code[20])
        {
            DataClassification = CustomerContent;
            Caption = 'Loading G/L Acct No.';
            TableRelation = "G/L Account"."No." WHERE("Account Type" = CONST(Posting),
               Blocked = CONST(false));
        }
        field(50055; "COA Blocking Period"; DateFormula)
        {
            DataClassification = CustomerContent;
        }
        field(50058; "Loan Series"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50059; "Bank Journal No. Series"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(60060; "Voucher Half Life"; Integer)
        {
            Description = 'HO1.0';
            DataClassification = CustomerContent;
        }
        field(60061; "XML Bank file path"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(60062; "Online Payment Template"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Gen. Journal Template".Name WHERE(Type = CONST(Payments));
        }
        field(60063; "Online Payment Batch Name"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(60065; "G/L Account for Damage Qty"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(60066; "Master Modify Templ. Nos"; Code[20])
        {
            Description = 'SAA_250917';
            TableRelation = "No. Series";
        }
        field(60067; "CWIP No."; Code[20])
        {
            Description = 'SAA_250917';
            TableRelation = "No. Series";
        }
        field(60068; "Page Other Filters"; Text[10])
        {
            DataClassification = CustomerContent;
        }
        field(60069; "Bank Confirmation SharedPath"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(60071; "Others Bank file path"; Text[200])
        {
            DataClassification = CustomerContent;
        }

        // >>>>>> G2S 06/09/2023
        //CR: RFC#39  
        //Signed: 31st Oct., 2023
        //Name: Go2Solve Nig. Ltd
        //Published: 10th Nov., 2023

        field(60072; "Notification Nos."; Code[20])
        {
            DataClassification = ToBeClassified;
            TableRelation = "No. Series";
        }

        field(60073; "Notification Batch Name"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        // <<<<<< G2S 06/09/2023
        field(60074; CHIERP_ItemValidityPeriod; DateFormula)
        {
            DataClassification = CustomerContent;
            Caption = 'Item Validity Period';
            trigger OnValidate()
            var
                UserSetup: Record "User Setup";
            begin
                if Rec.CHIERP_ItemValidityPeriod <> xRec.CHIERP_ItemValidityPeriod then begin
                    UserSetup.Get(UserId);
                    UserSetup.TestField(CHIERP_AllowValidPeriodChange, true);
                end;
            end;
        }
        field(60075; CHIERP_CustomerValidityPeriod; DateFormula)
        {
            DataClassification = CustomerContent;
            Caption = 'Customer Validity Period';
            trigger OnValidate()
            var
                UserSetup: Record "User Setup";
            begin
                if Rec.CHIERP_CustomerValidityPeriod <> xRec.CHIERP_CustomerValidityPeriod then begin
                    UserSetup.Get(UserId);
                    UserSetup.TestField(CHIERP_AllowValidPeriodChange, true);
                end;
            end;
        }
        field(60076; CHIERP_VendorValidityPeriod; DateFormula)
        {
            DataClassification = CustomerContent;
            Caption = 'Vendor Validity Period';
            trigger OnValidate()
            var
                UserSetup: Record "User Setup";
            begin
                if Rec.CHIERP_VendorValidityPeriod <> xRec.CHIERP_VendorValidityPeriod then begin
                    UserSetup.Get(UserId);
                    UserSetup.TestField(CHIERP_AllowValidPeriodChange, true);
                end;
            end;
        }
        field(60077; CHIERP_BankValidityPeriod; DateFormula)
        {
            DataClassification = CustomerContent;
            Caption = 'Bank Validity Period';
            trigger OnValidate()
            var
                UserSetup: Record "User Setup";
            begin
                if Rec.CHIERP_BankValidityPeriod <> xRec.CHIERP_BankValidityPeriod then begin
                    UserSetup.Get(UserId);
                    UserSetup.TestField(CHIERP_AllowValidPeriodChange, true);
                end;
            end;
        }
        field(60078; CHIERP_FAValidityPeriod; DateFormula)
        {
            DataClassification = CustomerContent;
            Caption = 'FA Validity Period';
            trigger OnValidate()
            var
                UserSetup: Record "User Setup";
            begin
                if Rec.CHIERP_FAValidityPeriod <> xRec.CHIERP_FAValidityPeriod then begin
                    UserSetup.Get(UserId);
                    UserSetup.TestField(CHIERP_AllowValidPeriodChange, true);
                end;
            end;
        }
    }


    var
        myInt: Integer;


}