pageextension 50090 WareHouseReceipt extends "Warehouse Receipt"
{
    layout
    {
        addafter("Sorting Method")
        {
            field(Status; Status)
            {
                ApplicationArea = ALL;
            }
            field("Import File No."; "Import File No.")
            {
                ApplicationArea = ALL;
            }
            field("Clearing File No."; "Clearing File No.")
            {
                ApplicationArea = ALL;
            }
            field("Receiving No. Series"; "Receiving No. Series")
            {
                ApplicationArea = ALL;
            }

        }

    }
    actions
    {
        modify("Post Receipt")
        {
            trigger OnBeforeAction()
            var
                WarHosueRecLine: Record "Warehouse Receipt Line";
                PurchHdr: Record "Purchase Header";
                TraHdrGRec: Record "Transfer Header";
                SalHdrGRec: Record "Sales Header";
            BEGIN
                TestField(Status, Status::Released);
                UpdateExpDate();//B2BPKON310521
                WarHosueRecLine.reset;
                WarHosueRecLine.SetRange("No.", "No.");
                IF WarHosueRecLine.findset then
                    repeat
                        OrderNo := WarHosueRecLine."Source No.";//PKONJ9
                        case WarHosueRecLine."Source Document" of
                            WarHosueRecLine."Source Document"::"Sales Order":
                                BEGIN
                                    SalHdrGRec.reset;
                                    SalHdrGRec.SetRange("No.", WarHosueRecLine."Source No.");
                                    IF SalHdrGRec.findfirst then begin
                                        IF SalHdrGRec."Loading Slip Required" THEN begin
                                            WarHosueRecLine.TestField("Posted Loading Slip No.");
                                            WarHosueRecLine.TestField("Posted Loading Slip Line No.");
                                        end;
                                    end
                                End;
                            WarHosueRecLine."Source Document"::"Inbound Transfer":
                                BEGIN
                                    TraHdrGRec.reset;
                                    TraHdrGRec.SetRange("No.", WarHosueRecLine."Source No.");
                                    TraHdrGRec.SetRange("Transfer Type", TraHdrGRec."Transfer Type"::"Branch Request");
                                    IF TraHdrGRec.findfirst then begin
                                        IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then BEGIN
                                            WarHosueRecLine.TestField("Posted Loading Slip No.");
                                            WarHosueRecLine.TestField("Posted Loading Slip Line No.");
                                        END;
                                    end;
                                END;
                        end;
                    until WarHosueRecLine.next = 0;
                /// 

                if "Posting Date" = 0D then
                    Error('Please enter the Posting date');
                CurrPage.WhseReceiptLines.PAGE.CreateItemJournalLines(Rec);

                WarHosueRecLine.reset;
                WarHosueRecLine.SetRange("No.", "No.");
                WarHosueRecLine.SetRange("Source Document", WarHosueRecLine."Source Document"::"Purchase Order");
                IF WarHosueRecLine.Findfirst() then begin
                    PurchHdr.reset;
                    PurchHdr.SetRange("No.", WarHosueRecLine."Source No.");
                    IF PurchHdr.FindFirst() then BEGIN
                        IF PurchHdr."Purchase Type" = PurchHdr."Purchase Type"::Import THEN BEGIN
                            TestField("Clearing File No.");
                            TestField("Import File No.");
                        end;
                    END;
                end;

                // >>>>>> G2S 8399-CAS-01406-B3C2S9 05/03/2023
                // Validate source document qty
                WarHosueRecLine.reset;
                WarHosueRecLine.SetRange("No.", "No.");
                WarHosueRecLine.SetRange("Source Document", WarHosueRecLine."Source Document"::"Purchase Order");
                if WarHosueRecLine.findset then
                    repeat
                        WarHosueRecLine.ValidateSourceDocumentQty(Rec."No.");
                    until WarHosueRecLine.next = 0;
                // >>>>>> G2S 8399-CAS-01406-B3C2S9 05/03/2023
            END;

            trigger OnAfterAction()
            begin
                GenerateWhseShpmntReport();//Balu 05232021
            end;
        }
        modify("Post and &Print")
        {
            Visible = false;//Balu 05232021
            trigger OnBeforeAction()
            var
                WarHosueRecLine: Record "Warehouse Receipt Line";
                PurchHdr: Record "Purchase Header";
                TraHdrGRec: Record "Transfer Header";
                SalHdrGRec: Record "Sales Header";
            ////
            BEGIN
                TestField(Status, Status::Released);
                UpdateExpDate();//B2BPKON310521
                WarHosueRecLine.reset;
                WarHosueRecLine.SetRange("No.", "No.");
                IF WarHosueRecLine.findset then
                    repeat
                        OrderNo := WarHosueRecLine."Source No.";//PKONJ9
                        case WarHosueRecLine."Source Document" of
                            WarHosueRecLine."Source Document"::"Sales Order":
                                BEGIN
                                    SalHdrGRec.reset;
                                    SalHdrGRec.SetRange("No.", WarHosueRecLine."Source No.");
                                    IF SalHdrGRec.findfirst then begin
                                        IF SalHdrGRec."Loading Slip Required" THEN begin
                                            WarHosueRecLine.TestField("Posted Loading Slip No.");
                                            WarHosueRecLine.TestField("Posted Loading Slip Line No.");
                                        end;
                                    end
                                End;
                            WarHosueRecLine."Source Document"::"Inbound Transfer":
                                BEGIN
                                    TraHdrGRec.reset;
                                    TraHdrGRec.SetRange("No.", WarHosueRecLine."Source No.");
                                    TraHdrGRec.SetRange("Transfer Type", TraHdrGRec."Transfer Type"::"Branch Request");
                                    IF TraHdrGRec.findfirst then begin
                                        IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then BEGIN
                                            WarHosueRecLine.TestField("Posted Loading Slip No.");
                                            WarHosueRecLine.TestField("Posted Loading Slip Line No.");
                                        END;
                                    end;
                                END;
                        end;
                    until WarHosueRecLine.next = 0;
                /// 

                if "Posting Date" = 0D then
                    Error('Please enter the Posting date');
                CurrPage.WhseReceiptLines.PAGE.CreateItemJournalLines(Rec);

                WarHosueRecLine.reset;
                WarHosueRecLine.SetRange("No.", "No.");
                WarHosueRecLine.SetRange("Source Document", WarHosueRecLine."Source Document"::"Purchase Order");
                IF WarHosueRecLine.Findfirst() then begin
                    PurchHdr.reset;
                    PurchHdr.SetRange("No.", WarHosueRecLine."Source No.");
                    IF PurchHdr.FindFirst() then BEGIN
                        IF PurchHdr."Purchase Type" = PurchHdr."Purchase Type"::Import THEN BEGIN
                            TestField("Clearing File No.");
                            TestField("Import File No.");
                        end;
                    END;
                end;
            END;
        }
        addafter("Post Receipt")
        {
            action(Reopen)
            {
                Image = ReOpen;
                trigger OnAction()
                BEGIN
                    TestField(Status, Status::Released);
                    Status := Status::Open;
                END;
            }
            action(Release)
            {
                image = ReleaseDoc;
                trigger OnAction()
                BEGIN
                    TestField(Status, Status::Open);
                    Status := Status::Released;
                END;

            }
            action(ReleaseWHEntry)
            {
                image = ReleaseDoc;
                ApplicationArea = ALL;
                trigger OnAction()
                Var
                    WarehouseRequest: record "Warehouse Request";
                    WarehouseReceiptLine: record "Warehouse receipt Line";
                BEGIN
                    WarehouseReceiptLine.reset;
                    WarehouseReceiptLine.setrange("No.", rec."No.");
                    IF WarehouseReceiptLine.FindFirst() then begin
                        warehouserequest.Reset();
                        warehouserequest.setrange("Source type", 39);
                        warehouserequest.setrange("Source subtype", 1);
                        warehouserequest.setrange("Source No.", WarehouseReceiptLine."Source No.");
                        if warehouserequest.FINDFIRST then begin
                            warehouserequest."document status" := warehouserequest."document status"::Released;
                            warehouserequest.MODIFY;
                        end;
                    end;
                END;

            }
        }

    }
    trigger OnOpenPage()
    var
        PurchHdr: Record "Purchase Header";
        WareHouse: Record "Warehouse Receipt Header";
        WareHouseLi: Record "Warehouse Receipt Line";
    begin
        WareHouse.RESET;
        WareHouse.SETRANGE("No.", "No.");
        IF WareHouse.findfirst then begin
            WareHouseLi.reset;
            WareHouseLi.SetRange("No.", WareHouse."No.");
            IF WareHouseLi.FindFirst() THEN BEGIN
                PurchHdr.Reset();
                PurchHdr.SetRange("Document Type", PurchHdr."Document Type"::Order);
                PurchHdr.SetRange("No.", WareHouseLi."Source No.");
                if PurchHdr.FindFirst() then begin
                    WareHouse."Clearing File No." := PurchHdr."Clearing File No.";
                    WareHouse."Import File No." := PurchHdr."Import File No.";
                    WareHouse.Modify(true);
                end;
            end;
        end;
    end;

    trigger OnDeleteRecord(): Boolean
    var
        LoadSlipHdrLin: Record "Loading Slip Line";
        PLoadSlipHdrLin: Record "Posted Loading SLip Line";
    begin
        LoadSlipHdrLin.RESET;
        LoadSlipHdrLIn.SetRange("Document Type", LoadSlipHdrLIn."Document Type"::Receipt);
        LoadSlipHdrLin.SetRange("No.", "No.");
        IF LoadSlipHdrLin.FindFirst() then
            error('Loading Slip No. %1 is existing for this warehouse receipt.', LoadSlipHdrLin."No.");
        PLoadSlipHdrLin.RESET;
        PLoadSlipHdrLin.SetRange("Document Type", PLoadSlipHdrLin."Document Type"::Receipt);
        PLoadSlipHdrLin.SetRange("No.", "No.");
        IF PLoadSlipHdrLin.FindFirst() then
            error('Posted Loading Slip No. %1 is existing for this warehouse receipt.', pLoadSlipHdrLin."No.");
        // B2BMS08022021>>
        TestField(Status, Status::Open);
        // B2BMS08022021<<
    end;
    // B2BMS08022021>>
    trigger OnModifyRecord(): Boolean
    begin
        //TestField(Status, Status::Open);
    end;
    // B2BMS08022021<<
    //Balu 05232021>>

    procedure GenerateWhseShpmntReport()//PKONJ9
    var
        Whsercpthdr: Record "Posted Whse. Receipt Header";
        Whsercpthdr1: Record "Posted Whse. Receipt Header";
        Trnsrcpthdr: Record "Transfer Receipt Header";
        Trnsrcpthdr2: Record "Transfer Receipt Header";
        Prchsrcpthdr: Record "Purch. Rcpt. Header";
        Prchsrcpthdr2: Record "Purch. Rcpt. Header";
    begin

        Trnsrcpthdr.Reset();
        Trnsrcpthdr.SetRange("Transfer Order No.", OrderNo);
        IF Trnsrcpthdr.FindLast() then begin
            Trnsrcpthdr2.Reset();
            Trnsrcpthdr2.SetRange("No.", Trnsrcpthdr."No.");
            IF Trnsrcpthdr2.FindFirst() then
                Report.RunModal(50168, true, false, Trnsrcpthdr2);
            exit;
        end;

        Prchsrcpthdr.Reset();
        Prchsrcpthdr.SetRange("Order No.", OrderNo);
        IF Prchsrcpthdr.FindLast() then begin
            Prchsrcpthdr2.Reset();
            Prchsrcpthdr2.SetRange("No.", Prchsrcpthdr."No.");
            IF Prchsrcpthdr2.FindFirst() then
                Report.RunModal(50037, true, false, Prchsrcpthdr2);
            exit;
        end;

        Whsercpthdr.Reset();
        Whsercpthdr.SetRange("Whse. Receipt No.", "No.");
        IF Whsercpthdr.FindLast() then begin
            Whsercpthdr1.Reset();
            Whsercpthdr1.SetRange("No.", Whsercpthdr."No.");
            if Whsercpthdr1.FindFirst() then
                Report.RunModal(7308, true, false, Whsercpthdr1);
            exit;
        end;
    end;

    procedure UpdateExpDate()
    var
        ITEMLE: Record "Item Ledger Entry";
        Item: Record Item;
        wareshplines: Record "Warehouse Shipment Line";
        Invsetup: Record "Inventory Setup";
    begin
        Invsetup.GET();
        IF Not Invsetup."Update Exp Date" then
            exit;
        wareshplines.Reset();
        wareshplines.SetRange("No.", "No.");
        if wareshplines.findset then
            repeat
                ITEMLE.Reset();
                //ITEMLE.SetFilter("Document Type", '%1|%2', ITEMLE."Document Type"::"Transfer Receipt", ITEMLE."Document Type"::"Transfer Shipment");//PKON06012021
                ITEMLE.SetRange("Item No.", wareshplines."Item No.");
                IF ITEMLE.findset then
                    repeat
                        if Item.Get(ITEMLE."Item No.") and (Item."Item Tracking Code" <> '') then
                            UpdateLotno(ITEMLE);
                    until ITEMLE.next = 0;
            until wareshplines.next = 0;
        Commit();
    end;

    local procedure UpdateLotno(ItemLedgerEntry: Record "Item Ledger Entry")
    var
        Item: Record Item;
        ILE: Record "Item Ledger Entry";
        ILE2: Record "Item Ledger Entry";
        Application: Record "Item Application Entry";
        Application2: Record "Item Application Entry";
    begin
        with ItemLedgerEntry do begin
            Application.Reset();
            Application.SetRange("Item Ledger Entry No.", "Entry No.");
            if Application.FindFirst() then begin
                if Application."Outbound Item Entry No." = Application."Item Ledger Entry No." then begin
                    if ILE.get(Application."Inbound Item Entry No.") then begin
                        if ILE."Lot No." <> '' then begin
                            "Lot No." := ILE."Lot No.";
                            "Expiration Date" := ILE."Expiration Date";
                            Modify();
                        end else
                            UpdateLotno(ILE);
                    end;
                end else
                    if ILE.get(Application."Outbound Item Entry No.") then begin
                        if ILE."Lot No." <> '' then begin
                            "Lot No." := ILE."Lot No.";
                            "Expiration Date" := ILE."Expiration Date";
                            Modify();
                        end else
                            UpdateLotno(ILE);
                    end;
            end;

        end;

    end;

    var
        OrderNo: code[20];//PKONJ9
}

