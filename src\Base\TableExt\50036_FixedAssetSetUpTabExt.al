tableextension 50036 FixedAssetSetUpTabExt extends "FA Setup"
{
    fields
    {
        field(50000; "PMS Dim Code"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Capex No. Series"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50002; "Capex Budget  No. Series"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50003; "Insurance Period"; DateFormula)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "E-Mail"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "FA Disposal No. Series"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50006; "Disposal Account"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50007; "FA Movement Reg. Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50008; "Fa Verification Nos"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50009; "PRS No. Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
    }

    var
        myInt: Integer;
}