page 50148 "Material Disposal"
{

    PageType = Document;
    SourceTable = "MDV Header";
    SourceTableView = SORTING("MDV No.")
                      ORDER(Ascending);
    UsageCategory = documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MDV No."; "MDV No.")
                {
                    ApplicationArea = all;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE();
                    end;
                }
                field("Manual MDV. No"; "Manual MDV. No")
                {
                    Visible = false;
                    ApplicationArea = all;
                }
                field("Disposal Type"; "Disposal Type")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        MdvLne: Record "MDV Line";
                    BEGIN
                        MdvLne.reset;
                        MdvLne.SetRange("Document No.", "MDV No.");
                        IF MdvLne.findset then BEGIN
                            MdvLne."Shortcut Dimension 1 Code" := "Shortcut Dimension 1 Code";
                            MdvLne.modify;
                        END;
                    END;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        MdvLne: Record "MDV Line";
                    BEGIN
                        MdvLne.reset;
                        MdvLne.SetRange("Document No.", "MDV No.");
                        IF MdvLne.findset then BEGIN
                            MdvLne."Shortcut Dimension 2 Code" := "Shortcut Dimension 2 Code";
                            MdvLne.modify;
                        END;
                    END;

                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = all;
                }
                field("QC Checked"; "QC Checked")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    var
                        recMDVLine: Record "MDV Line";
                        recLocation: Record Location;
                        Flag: Boolean;
                    begin
                        TESTFIELD("QC Checked By", '');
                        if not (User.GET(UPPERCASE(USERID())) and User."QC Checked (BAD Location)") then
                            ERROR(UPPERCASE(USERID()) + ' does not have permission to Approve this Document');
                        "QC Checked By" := COPYSTR(UPPERCASE(USERID()), 1, 80);
                        "QC Checked Date" := WORKDATE();
                        Flag := true;
                        recMDVLine.SETRANGE(recMDVLine."Document No.", "MDV No.");
                        if recMDVLine.FINDSET() then
                            repeat
                                if recLocation.GET(recMDVLine."Location Code") then
                                    Flag := false;
                            until (recMDVLine.NEXT() = 0) or Flag;
                        if not Flag then
                            ERROR('Disposal can be done from Bad Location Only. ' + recMDVLine."Location Code" + ' is not a bad Location. \' +
                                  'Please contact your system administrator');
                    end;
                }
                field("QC Checked By"; "QC Checked By")
                {
                    ApplicationArea = all;
                }
                field("QC Checked Date"; "QC Checked Date")
                {
                    ApplicationArea = all;
                }
            }
            part(MatDispLinesSubform; "Material Disp. Lines Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Document No." = FIELD("MDV No.");
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Disposal")
            {
                Caption = '&Disposal';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';
                    Image = Dimensions;
                    trigger OnAction();
                    begin
                        ShowDocDim();
                    end;
                }
                separator(Separator1102152034)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()

                    begin
                        CheckMandFields();
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendMDVHeaderforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF "Approval Status" <> "Approval Status"::Released then BEGIN
                            "Approval Status" := "Approval Status"::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();

                    begin
                        IF "Approval Status" = "Approval Status"::"Pending for Approval" THEN
                            ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50004);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF "Approval Status" <> "Approval Status"::Open then BEGIN
                            "Approval Status" := "Approval Status"::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("-")
                {
                    Caption = '-';
                }
                action(Approve)
                {
                    ApplicationArea = All;
                    Image = Action;
                    //Visible = openapp;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        IF allinoneCU.MDVHeaderApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendMDVHeaderForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelMDVHeaderForApproval(Rec);
                    end;
                }
            }
            group("&Registering")
            {
                Caption = '&Registering';
                action("Register Material Disposal")
                {
                    ApplicationArea = all;
                    Caption = 'Register Material Disposal';
                    Ellipsis = true;
                    ShortCutKey = 'Shift+F11';
                    Image = Post;
                    trigger OnAction();
                    begin
                        PostMDV();
                    end;
                }
            }
            group("&Print")
            {
                Caption = '&Print';
                action("Material Disposal")
                {
                    ApplicationArea = all;
                    Caption = 'Material Disposal';
                    Ellipsis = true;
                    Image = Print;
                    trigger OnAction();
                    var
                    begin
                        SETRECFILTER();
                        Message('Need to add report');//PK
                                                      //REPORT.RUNMODAL(REPORT::"Material Disposal Slip",TRUE,FALSE,Rec);//CHIUPG
                    end;
                }
            }
        }
    }


    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        VALIDATE("Responsibility Center", UserMg.GetMDVFilter());
        "Dim. Document Type" := "Dim. Document Type"::" ";
    end;

    trigger OnOpenPage();
    begin
        SETFILTER(Posted, '<>%1', true);
        if UserMg.GetMDVFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetMDVFilter());
            FILTERGROUP(0);
            //Message('Need to add filter here');
        end;
    end;

    local procedure CheckMandFields()
    var
        mdvLineLr: record "MDV Line";
    begin

        mdvLineLr.reset();
        mdvLineLr.SetRange("Document No.", "MDV No.");
        IF mdvLineLr.findset() then BEGIN
            repeat
                mdvLineLr.TestField("Location Code");
                mdvLineLr.TestField(Quantity);
            until mdvLineLr.next() = 0;
        end else
            error('No lines are existing for this document.')
    end;

    var
        User: Record "User Setup";
        RecordRest: Record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit Codeunit1;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        WorkflowManagement: Codeunit "Workflow Management";
        UserMg: Codeunit Mgmt;
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;

    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;
}

