pageextension 50016 DimValPagExt extends "Dimension Values"
{
    layout
    {
        addafter("Dimension Value Type")
        {
            field("MRS Series Code"; "MRS Series Code")
            {
                ApplicationArea = all;
            }
            field("Branch Zones"; "Branch Zones")
            {
                ApplicationArea = all;
            }
            field("Accloc code"; "Accloc code")
            {
                ApplicationArea = all;
            }
            field(Region; Region)
            {
                ApplicationArea = all;
                //b2bpksalecorr10
            }

            field("KD Region"; "KD Region")
            {
                ApplicationArea = all;
            }
            field("DSR Report Grouping"; "DSR Report Grouping") //PKON22M10-CR220063
            {
                ApplicationArea = all;
            }
            field("DSR Report Main Grouping"; "DSR Report Main Grouping") //PKON22M10-CR220063
            {
                ApplicationArea = all;
            }
        }
    }

    actions
    {

    }

}