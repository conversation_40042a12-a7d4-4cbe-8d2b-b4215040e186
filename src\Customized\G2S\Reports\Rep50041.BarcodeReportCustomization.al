/// <summary>
/// Report Barcode Report Customization (ID 50041).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
report 50041 "Barcode Report Customization"
{
    ApplicationArea = All;
    Caption = 'Barcode label';
    UsageCategory = ReportsAndAnalysis;
    EnableExternalAssemblies = true;
    DefaultLayout = RDLC;
    //RDLCLayout = './Production Order Barcode-new WIP.rdl';
    RDLCLayout = './layout/Production Order Barcode-new WIP.rdl';

    dataset
    {
        dataitem(ProductionOrder; "Production Order")
        {
            DataItemTableView = sorting("No.");
            RequestFilterFields = "No.";
            column(No_; "No.")
            {

            }
            column(Source_No_; "Source No.")
            {

            }
            column(Description; Description)
            {

            }
            column(BUOM; BUOM)
            {

            }
            column(Due_Date; "Due Date")
            {

            }
            column(Production_Batch_No_; "Production Batch No.")
            {

            }
            column(Logo; CompInfo.Picture)
            {
            }
            dataitem("Tracking Specification"; "Tracking Specification")
            {
                DataItemLink = "Source ID" = FIELD("No.");
                DataItemTableView = sorting("Entry No.");

                column(Item_No_; "Item No.")
                {

                }
                column(Lot_No_; "Lot No.")
                {

                }
                column(Expiration_Date; "Expiration Date")
                {

                }
            }
            // dataitem(Item; Item)
            // {
            //     DataItemLink = "No." = field("Source No.");
            //     DataItemTableView = sorting("No.");
            // }

            dataitem("IWX LP Header"; "IWX LP Header")
            {
                DataItemLink = "Production Order No." = field("No.");
                DataItemTableView = sorting("No.");
                RequestFilterFields = "No.";
                column("LPNo"; "No.")
                {

                }

                // trigger OnPreDataItem()
                // var
                //     myInt: Integer;
                // begin
                //     //SetFilter("No.", '%1..%2', FromLicPlate, ToLicPlate);
                //     //SetFilter("No.", '%1', FromLicPlate);
                //     SetFilter("No.", '%1', UpDatedLicRec);
                // end;

                // trigger OnAfterGetRecord()
                // var
                //     myInt: Integer;
                // begin
                //     if "No." = '' then CurrReport.Skip();
                // end;
            }


            trigger OnAfterGetRecord()
            var
                Item: Record Item;
                ProdOrderLine: Record "Prod. Order Line";
            begin
                //7th Nove 2023
                ProdOrderLine.Reset();
                ProdOrderLine.SetRange("Prod. Order No.", "No.");

                if ProdOrderLine.FindFirst() then begin
                    Item.Reset();
                    Item.SetRange("No.", "Source No.");
                    Item.SetRange("License Plate Enabled?", true);
                    if Item.FindFirst() then begin
                        //BUOM := Item."Base Unit of Measure";
                        BUOM := 'PALLETS';
                    end else begin
                        Error(Txt001, "No.");
                    end;
                end else begin
                    Error(Txt001, "No.");
                end;
            end;
        }
        // dataitem("IWX LP Header2"; "IWX LP Header")
        // {
        //     DataItemLink = "Production Order No." = field( "No.");
        //     DataItemTableView = sorting("No.");
        //     column("LPNo2"; "No.")
        //     {

        //     }
        //     trigger OnAfterGetRecord()
        //     var
        //         myInt: Integer;
        //     begin
        //         if "No." = '' then CurrReport.Skip();
        //     end;
        // }
    }

    requestpage
    {
        layout
        {
            area(content)
            {
                // group("Licence Plate")
                // {
                // field("From Lic. Plate"; FromLicPlate)
                // {
                //     TableRelation = "IWX LP Header";
                //     Caption = 'Licence Plate filter';
                //     trigger OnValidate()
                //     var
                //     begin
                //         IWXLPHeader.Reset();
                //         IWXLPHeader.SetRange("Production Order No.", ProdOrder);
                //         "IWX LP Header".SetRange("Production Order No.", ProdOrder);
                //         // "IWX LP Header".SetFilter("No.", '%1', FromLicPlate);
                //         UpDatedLicRec := FromLicPlate;
                //         // IWXLPHeader.SetRange("No.", FromLicPlate);
                //         // IWXLPHeader.SetFilter("No.", '%1', FromLicPlate);
                //         // if not IWXLPHeader.FindFirst() then Error(Txt002, FromLicPlate, ProdOrder);
                //         // if not IWXLPHeader.findset() then Error(Txt002, FromLicPlate, ProdOrder);
                //     end;
                // }
                // field("To Lic. Plate"; ToLicPlate)
                // {
                //     TableRelation = "IWX LP Header";
                //     trigger OnValidate()
                //     var
                //     begin
                //         IWXLPHeader.Reset();
                //         IWXLPHeader.SetRange("Production Order No.", ProdOrder);
                //         IWXLPHeader.SetRange("No.", ToLicPlate);
                //         if not IWXLPHeader.FindFirst() then Error(Txt002, ToLicPlate, ProdOrder);
                //     end;
                // }
                //}
            }
        }

        actions
        {
            area(processing)
            {
            }
        }
    }


    trigger OnPreReport()
    var
    begin
        CompInfo.GET;
        CompInfo.CALCFIELDS(CompInfo.Picture);
    end;



    // trigger OnInitReport()
    // var
    //     ProdOrderRep: Record "Production Order Report";
    //     LP: Record "IWX LP Header";
    // begin
    //     ProdOrderRep.Reset();
    //     ProdOrderRep.SetRange(UserID, UserId);
    //     ProdOrderRep.SetRange(SessionID, SessionId());
    //     if ProdOrderRep.FindFirst() then begin
    //         ProdOrder := ProdOrderRep."Production Order No.";
    //         IWXLPHeader.Reset();
    //         IWXLPHeader.SetRange("Production Order No.", ProdOrderRep."Production Order No.");
    //         if IWXLPHeader.FindFirst() then begin
    //             FromLicPlate := IWXLPHeader."No.";
    //         end;
    //         if IWXLPHeader.FindLast() then begin
    //             ToLicPlate := IWXLPHeader."No.";
    //         end;
    //         if ToLicPlate <> FromLicPlate then begin
    //             FromLicPlate := FromLicPlate + '..' + ToLicPlate;
    //         end;
    //     end;
    // end;


    // trigger OnPostReport()
    // var
    //     ProdOrderRep: Record "Production Order Report";
    // begin
    //     ProdOrderRep.Reset();
    //     ProdOrderRep.SetRange(UserID, UserId);
    //     ProdOrderRep.SetRange(SessionID, SessionId());
    //     ProdOrderRep.DeleteAll();
    // end;


    var
        CompInfo: Record "Company Information";
        BUOM: Code[10];
        LineSeparator: Text;
        Txt001: Label 'Barcode label for order %1 cannot be generated.';
        FromLicPlate, UpDatedLicRec, ToLicPlate : Code[1024];
        //ToLicPlate: Code[20];
        IWXLPHeader: Record "IWX LP Header";
        Txt002: Label 'The Lic. Plate record %1 does not exist for production order %2';
        ProdOrder: Code[20];
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<