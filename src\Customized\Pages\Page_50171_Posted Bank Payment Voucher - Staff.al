page 50171 "Posted Bank Pmt Voucher Staff"
{
    // version CHI6.0

    // This page saved as from Posted Bank Payment Voucher (50387, Document) by B2BMSOn17Jan2022

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display posted bank payment vouchers.
    // 3.0      SAA      18-Apr-17      -> Added codes to OnPush() of Print button to flag error if user does not have
    //                                     permission to reprint.

    Caption = 'Posted Bank Paymentt Voucher - Staff';
    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Document;
    SourceTable = "Posted Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(BPV));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                }
                field("Posting Date"; "Posting Date")
                {
                }
                field("Account Type"; "Account Type")
                {
                }
                field("Account No."; "Account No.")
                {
                    Caption = 'Credit Account No.';
                }
                field("Account Name"; "Account Name")
                {
                }
                field("Import File No."; "Import File No.")
                {
                }
                field(Narration; Narration)
                {
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                }
                field("Currency Code"; "Currency Code")
                {
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                }
            }
            part(VoucherLines; "Posted Bank Pmt. Vchr. Subform")
            {
                SubPageLink = "Document No." = FIELD("Document No."),
                                "Debit Amount" = FILTER(<> 0);
                SubPageView = SORTING("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                }
                field("Created By Name"; "Created By Name")
                {
                }
                field("Created Date"; "Created Date")
                {
                }
                field("Created Time"; "Created Time")
                {
                }
                field("Posted By"; "Posted By")
                {
                }
                field("Posted By Name"; "Posted By Name")
                {
                }
                field("Posted Date"; "Posted Date")
                {
                }
                field("Posted Time"; "Posted Time")
                {
                }
                field("Voucher No."; "Voucher No.")
                {
                }
                field(ToBeCollectedBy; ToBeCollectedBy)
                {
                }
                field("Modified By"; "Modified By")
                {
                }
                field("Modified By Name"; "Modified By Name")
                {
                }
                field("Modified Date"; "Modified Date")
                {
                }
                field("Modified Time"; "Modified Time")
                {
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50118),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                action(Dimensions)
                {
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries: Page "Posted Approval Entries";
                    begin
                        //PostedApprovalEntries.Setfilters(DATABASE::"Posted Voucher Header","Document No.");}//CHI2018
                        PostedApprovalEntries.RUN;
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                separator(Separator1000000070)
                {
                }
                action("Create Bank CSV File")
                {
                    Caption = 'Create Bank CSV File';

                    trigger OnAction();
                    begin
                        CLEAR(BankLedgerList);
                        BankLedgerList.CreateFilter("Document No.");
                        BankLedgerList.RUNMODAL;
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();

                var
                    GLENt: Record "G/L Entry";
                begin
                    if UserSetup.GET(USERID) then BEGIN
                        if UserSetup."Reprint Payment Documents" then begin  //SAA3.0 >>
                            GLENt.RESET;
                            //GLENt.SETRANGE("Voucher Type", "Voucher Type");
                            GLENt.SETRANGE("Document No.", "Document No.");
                            if GLENt.FindSet() then
                                REPORT.RUN(50496, true, false, GLENt);
                        end else
                            ERROR(TEXT001);
                    end;
                end;
            }


            action("&Navigate")
            {
                Caption = '&Navigate';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Navigate;
                end;
            }
        }
    }

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        UserSetup.GET(USERID);
        if UserSetup.FilterResponsibilityCenter <> '' then begin
            FILTERGROUP(2);
            SETFILTER("Responsibility Center", UserSetup.FilterResponsibilityCenter);
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<

        //B2BMSOn17Jan2022>>
        UserSetupGRec.Get(UserId);
        if not UserSetupGRec."Open Staff Bank Pmt Voucher" then
            Error('You donot have permissions to access this page. Please Enable it in User Setup.');
        //B2BMSOn17Jan2022<<
    end;

    var
        PostedApprovalEntries: Page "Posted Approval Entries";
        VoucherHeader: Record "Posted Voucher Header";
        "--GJ1.0--": Integer;
        BankList: Option Zenith,UBA,"Standard Chartered";
        BankVal: Text[50];
        BankLedgerList: Page "Bank CSV File List";
        UserSetup: Record "User Setup";
        TEXT001: Label 'You do not have permission to reprint payment vouchers';
        UserSetupGRec: Record "User Setup"; //B2BMSOn17Jan2022
}

