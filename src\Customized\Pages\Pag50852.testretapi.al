page 50852 testretapi
{
    ApplicationArea = All;
    Caption = 'testretapi';
    PageType = List;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

            }
        }
    }
    actions
    {

        area(Navigation)
        {
            group(GroupName)
            {


                // action(TestAPI)
                // {
                //     trigger OnAction()
                //     var
                //         fetchsales: Codeunit RetailAPI2;
                //     begin
                //         fetchsales.Run();
                //     end;
                // }
                action(FETCHFG)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.FetchItemsToRetailProduct();
                    end;
                }
                action(postFG)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.CreatePayloadFromRetailProduct();
                    end;
                }
                action(UpdateInvoice)
                {
                    trigger OnAction()
                    var
                        Cu: Codeunit "CHI Retail Integrations";
                    begin
                        cu.UpdateSalesInHdr();
                    end;
                }
                action(GETSales)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.GetSalesfromAPI();
                    end;
                }
                action(GETpricechange)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.UpdateProductPrices();
                    end;
                }
                action(AssigOutletID)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.AssignOutletID();
                    end;
                }
                action(Getoutlets)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.GetOutlets();
                    end;
                }
                action(CreateOrders)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.CreateSalesTransactions();
                    end;
                }
                action(PostSales)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.PostSalesTransactions();
                    end;
                }
                action(PostStocks)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.PostStock();
                    end;
                }
                action(UPDATESTOCKS)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.StockUpdate();
                    end;
                }
                action(manualStockupdate)
                {
                    trigger OnAction()
                    var
                        fetchitem: codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.manualStockupdate();
                    end;
                }


                action(getproductfromapi)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "Retail Inventory Update";
                        testt: code[100];
                    begin
                        // testt := '8718951584648';
                        // fetchitem.GetProductfromAPI(testt);
                    end;
                }
                action(checksalesprice)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.CheckSalesPrice();
                    end;
                }
                action(fetchproductID)
                {
                    trigger OnAction()
                    var
                        fetchitem: Codeunit "CHI Retail Integrations";
                    begin
                        fetchitem.AssignProductID();
                    end;
                }
                action(UpdateItemUom)
                {
                    trigger OnAction()
                    var
                        itemUOM: Record "Item Barcodes Unit of Measure";
                        itemrec: Record Item;
                        itemunit: Record "Unit of Measure";
                        itemunitofm: Record "Item Unit of Measure";
                    begin
                        itemUOM.Reset();
                        if itemUOM.FindSet() then begin
                            repeat
                                itemrec.SetRange("No.", itemUOM."Item No.");
                                if itemrec.FindFirst() then begin
                                    itemUOM."Unit Of Measure" := itemrec."Base Unit of Measure";
                                    itemUOM.Modify();
                                end;

                            until itemUOM.Next() = 0;
                        end;
                        Message('Done');

                    end;
                }
                action(createUOM)
                {
                    trigger OnAction()
                    var
                        ItemUOM: Record "Item Unit of Measure";
                        itemunit: Record "Item Unit of Measure";
                        itembarcode: Record "Item Barcodes Unit of Measure";
                        ItemRec: Record Item;
                        uom: Decimal;
                        barcode: Text;
                    begin
                        ItemRec.Reset();
                        ItemRec.SetFilter("Item Category Code", 'FG');
                        ItemRec.SetFilter(BarCode, '<>%1', '');
                        if ItemRec.FindSet() then begin
                            repeat
                                ItemUOM.SetRange("Item No.", ItemRec."No.");
                                ItemUOM.SetFilter(Code, '<>%1', 'PALLETS');
                                if ItemUOM.FindSet() then begin
                                    repeat
                                        itemunit.Reset();
                                        itemunit.SetRange(Code, ItemRec."Base Unit of Measure");
                                        if itemunit.FindFirst() then begin
                                            Clear(uom);
                                            uom := (ItemUOM."Qty. per Unit of Measure" / itemunit."Qty. per Unit of Measure");

                                            barcode := ItemRec.BarCode + Format(uom);
                                            itembarcode.Reset();
                                            itembarcode.SetRange(Barcode, barcode);
                                            if not itembarcode.FindFirst() then begin
                                                itembarcode.Init();
                                                itembarcode.Barcode := barcode;
                                                itembarcode."Unit Of Measure" := ItemUOM.Code;
                                                itembarcode."Item No." := ItemRec."No.";
                                                itembarcode.Insert();
                                            end;
                                        end;
                                    until ItemUOM.Next() = 0;
                                end;
                            //  end;

                            until ItemRec.Next() = 0;
                        end;

                    end;
                }


            }
        }
    }
}
