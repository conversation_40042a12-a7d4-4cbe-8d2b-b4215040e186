page 50131 "Online Cust. Discount"
{

    DelayedInsert = true;
    PageType = List;
    SourceTable = "Online Cust. Discount";
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = all;
                }
                field("Discount %"; "Discount %")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnAfterGetRecord();
    begin
        if UPPERCASE(USERID) in ['PRABODH.AGRAWAL'] then
            CurrPage.EDITABLE := true
        else
            CurrPage.EDITABLE := false;
    end;
}

