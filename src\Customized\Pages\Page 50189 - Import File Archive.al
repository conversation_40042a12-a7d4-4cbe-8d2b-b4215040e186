page 50908 "Import File Archive"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      HO       06-Dec-11      -> Form Created to Import File Archive Management.

    Caption = 'Import File Archive';
    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    PageType = Document;
    RefreshOnActivate = true;
    SourceTable = "Vendor Archive";
    UsageCategory = Documents;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                }
                field(Name; Name)
                {
                    Caption = 'Description';
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                }
                field("Vendor Posting Group"; "Vendor Posting Group")
                {
                }
                field("Search Name"; "Search Name")
                {
                }
                field(Blocked; Blocked)
                {
                }
                field(Status_; Status_)
                {
                }
                field("Last Date Modified"; "Last Date Modified")
                {
                }
            }
            part(Control1000000006; "Import File Subform Archive")
            {
                SubPageLink = "Document No." = FIELD("No.");
            }
            part(Control1000000035; "Import File Subform2 Archive")
            {
                SubPageLink = "Import File No." = FIELD("No.");
            }
            group("Form M")
            {
                Caption = 'Form M';
                field("File No._"; "File No._")
                {
                }
                field("PFI No._"; "PFI No._")
                {
                }
                field("PFI Date_"; "PFI Date_")
                {
                }
                field("Order Value_"; "Order Value_")
                {
                }
                field("Import Currency Code_"; "Import Currency Code_")
                {
                }
                field("Form M No._"; "Form M No._")
                {
                }
                field("Form M Submitted Date_"; "Form M Submitted Date_")
                {
                }
                field("Form M Approval No._"; "Form M Approval No._")
                {
                }
                field("Form M Approved Date_"; "Form M Approved Date_")
                {
                }
                field("Form M Expiry Date_"; "Form M Expiry Date_")
                {
                }
            }
            group(LC)
            {
                Caption = 'LC';
                field("Bank No._"; "Bank No._")
                {
                }
                field("Bank Name_"; "Bank Name_")
                {
                }
                field("Facility Bank No._"; "Facility Bank No._")
                {
                }
                field("Facility Bank Name_"; "Facility Bank Name_")
                {
                }
                field("Facility Code_"; "Facility Code_")
                {
                }
                field("Import Payment Term Code_"; "Import Payment Term Code_")
                {
                }
                field("LC Value Variation_"; "LC Value Variation_")
                {
                    Editable = false;
                }
                field("LC Opening Date_"; "LC Opening Date_")
                {
                }
                field("LC Expiry Period_"; "LC Expiry Period_")
                {
                }
                field("LC Expiry Date_"; "LC Expiry Date_")
                {
                }
                field("LC Amount_"; "LC Amount_")
                {
                }
            }
            group(Insurance)
            {
                Caption = 'Insurance';
                field("Insurance Policy No._"; "Insurance Policy No._")
                {
                }
                field("Insurance Company_"; "Insurance Company_")
                {
                }
                field("Insurance Date_"; "Insurance Date_")
                {
                }
                field("Inspection Agent Code_"; "Inspection Agent Code_")
                {
                }
            }
        }
    }

    actions
    {
    }

    trigger OnFindRecord(Which: Text): Boolean;
    var
        RecordFound: Boolean;
    begin
        RecordFound := FIND(Which);
        CurrPage.EDITABLE := RecordFound or (GETFILTER("No.") = '');
        exit(RecordFound);
    end;

    trigger OnOpenPage();
    begin
        SETRANGE("Vendor Type_", "Vendor Type_"::"Import File");
    end;

    var
        CalendarMgmt: Codeunit "Calendar Management";
        PaymentToleranceMgt: Codeunit "Payment Tolerance Management";
        CustomizedCalEntry: Record "Customized Calendar Entry";
        CustomizedCalendar: Record "Customized Calendar Change";
        Text001: Label 'Do you want to allow payment tolerance for entries that are currently open?';
        Text002: Label 'Do you want to remove payment tolerance from entries that are currently open?';
        ApprovalMgt: Codeunit 1535;
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";

    procedure ActivateFields();
    begin
    end;
}

