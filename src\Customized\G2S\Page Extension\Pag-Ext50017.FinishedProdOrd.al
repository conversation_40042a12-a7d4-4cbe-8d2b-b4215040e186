pageextension 50470 FinishedProdOrd extends "Finished Production Order"
{
    //<<<<<< G2S CAS-01312-L5Q5B8 6/21/2024
    trigger OnDeleteRecord(): Boolean
    var
        UserSetup: Record "User Setup";
        ProdOrderLine: Record "Prod. Order Line";
    begin
        UserSetup.SetRange("User ID", UserId);
        if UserSetup.FindFirst() then begin
            if not UserSetup."Delete Production Order" then begin
                Error('You do not have permmission to delete this Order!');
            end else begin
                ProdOrderLine.SetRange("Prod. Order No.", "No.");
                ProdOrderLine.SetRange("Item No.", "Source No.");
                if ProdOrderLine.FindFirst() then begin
                    if ProdOrderLine."Finished Quantity" <> 0 then Error('Your can not delete a Production order with Entires!');
                end;
            end;
        end else
            Error('User Not Found');
    end;
    //>>>>>> G2S CAS-01312-L5Q5B8 6/21/2024
}