/// <summary>
/// Page Shipment Transactions Hdr Log (ID 50100).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
page 50153 "Shipment Transactions Hdr Log"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    ModifyAllowed = false;
    InsertAllowed = false;
    Caption = 'CHI_Outreach Json Transaction List';
    CardPageId = "Shipment Transactions Log Hdr";
    SourceTable = "Shipment Transactions Hdr Log";


    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Sell-to Customer No."; Rec."Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Sell-to Customer No. field.';
                }
                field("Veh. No."; Rec."Veh. No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Veh. No. field.';
                }
                field(CompanyName; Rec.CompanyName)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the CompanyName field.';
                }
                field("Shipment Date"; Rec."Shipment Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Shipment Date field.';
                }
                field("Temp. Omit Rec"; "Temp. Omit Rec")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Date Sent to Outreach"; "Date Sent to Outreach")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Sent Status"; Rec."Sent Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Sent Status field.';
                }
                field(Remark; Rec.Remark)
                {
                    ToolTip = 'Specifies the value of the Remark field.';
                }


            }
        }

    }
    actions
    {
        area(Processing)
        {
            action(MarkandExclude)
            {
                Caption = 'Ignore Selected records from batch';
                ApplicationArea = All;
                Image = ImportDatabase;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                trigger OnAction()
                var
                    ShipHeaer: Record "Shipment Transactions Hdr Log";
                begin
                    RecToOmitExists := false;

                    if Confirm(Text001, false, TextConfirm) then begin
                        CurrPage.SetSelectionFilter(Rec);
                        Rec.Mark(true);
                        if Rec.FindFirst() then begin
                            repeat
                                if ((Rec."Sent Status" <> true) and (Rec."Temp. Omit Rec" = false)) then begin
                                    Rec."Temp. Omit Rec" := true;
                                    Rec.Modify();
                                    RecToOmitExists := true;
                                end
                            until Rec.Next() = 0;
                        end;
                        Rec.ClearMarks();
                        Rec.MARKEDONLY(FALSE);
                        if RecToOmitExists then
                            Message(ProcessCompleteMsg, 'marked and ommitted from the transactions to be sent to Outreach');
                    end else
                        Message(CancelConfirmation);
                end;
            }

            action(UnMarkandInclude)
            {
                Caption = 'Include Selected records from batch';
                ApplicationArea = All;
                Image = ImportDatabase;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                trigger OnAction()
                var
                    ShipHeaer: Record "Shipment Transactions Hdr Log";
                begin
                    RecToOmitExists := false;
                    if Confirm(Text001, false, TextConfirm) then begin
                        CurrPage.SetSelectionFilter(Rec);
                        Rec.Mark(true);
                        if Rec.FindFirst() then begin
                            repeat
                                if ((Rec."Sent Status" = false) and (Rec."Temp. Omit Rec" = true)) then begin
                                    Rec."Temp. Omit Rec" := false;
                                    Rec.Modify();
                                    RecToOmitExists := true;
                                end;
                            until Rec.Next() = 0;
                        end;
                        Rec.ClearMarks();
                        Rec.Mark(false);
                        if RecToOmitExists then
                            Message(ProcessCompleteMsg, 'marked and ommitted from the transactions to be sent to Outreach');
                    end else
                        Message(CancelConfirmation);
                end;
            }
        }
    }
    var
        Text001: Label 'Please confirm you want to %1';
        TextConfirm: Label 'temporarily ignore the selected records from the transactions to be sent to Outreach';
        TextUncheck: Label 'include the selected records. These records will be added to the list of records to be sent to Outreach.';
        CancelConfirmation: Label 'Confirmation canceled';
        TranLogList: Page "Shipment Transactions Hdr Log";
        ProcessCompleteMsg: Label 'The selected records have been successfully %1 ';
        RecToOmitExists: Boolean;
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<