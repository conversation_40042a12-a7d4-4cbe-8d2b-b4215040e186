pageextension 50010 SalesOrderPageExt42 extends "Sales Order"
{

    layout
    {
        addafter("External Document No.")
        {
            field("Shipping No. Series"; "Shipping No. Series")
            {
                ApplicationArea = all;
                //b2bpksalecorr10
            }
            field("Resp Wise Sell-to Customer No."; "Resp Wise Sell-to Customer No.")
            {
                ApplicationArea = all;
            }
            field("Applies-to Doc. Type"; "Applies-to Doc. Type")
            {
                ApplicationArea = ALL;
            }
            field("Applies-to Doc. No."; "Applies-to Doc. No.")
            {
                ApplicationArea = ALL;
            }
            field("Applies-to ID"; "Applies-to ID")
            {
                ApplicationArea = ALL;
            }
            field("Loading Slip Required"; "Loading Slip Required")
            {
                ApplicationArea = ALL;
                Editable = false;
            }
            field("Reason Codes"; "Reason Codes")
            {
                ApplicationArea = all;
                Visible = false;
            }
            //B2BMS
            field("Created By"; "Created By")
            {
                ApplicationArea = All;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = All;
            }
            field("Modified By"; "Modified By")
            {
                ApplicationArea = All;
            }
            field("Modified date"; "Modified date")
            {
                ApplicationArea = All;
            }
            //B2BMS

        }
        addafter("Outbound Whse. Handling Time")
        {
            field("Customer Reference No."; "Customer Reference No.")
            {
                ApplicationArea = all;
            }
        }
        addafter(Status)
        {
            field("Order Tracking"; "Order Tracking")
            {
                ApplicationArea = All;
            }
            field("Released By"; "Released By")
            {
                ApplicationArea = ALL;//B2BSPON22OCT18
            }
        }
        addbefore("EU 3-Party Trade")
        {
            field("Delivery Effected By"; "Delivery Effected By")
            {
                ApplicationArea = all;
            }
        }
        modify("Sell-to Customer No.")
        {
            Editable = false;
            trigger OnBeforeValidate()
            var
                CustGRec: Record Customer;
                RespError: Label 'Responsibility Centre in Customer and Sales order must match.';
            BEGIN
                TestField("Responsibility Center");
                IF CustGRec.Get("Sell-to Customer No.") AND (CustGRec."Responsibility Center" <> "Responsibility Center") AND (CustGRec."Responsibility Center" <> '') AND ("Responsibility Center" <> '') then
                    Error(RespError);
            END;
        }
        modify("Sell-to Customer Name")
        {
            Editable = false;
        }

    }
    actions
    {
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            BEGIN
                //CalPromtionSchemas();//PKONAU3
                CheckResponsilibilityCentre();
                CheckSalesMandValues(True);
                TestField("Order Status", 0);
                TestField("Location Code");
                IF CustGRec.GET("Sell-to Customer No.") AND (CustGRec."Customer Credit type" = CustGRec."Customer Credit type"::"Cash n Carry") then
                    //PKON22JA6-CR220007
                    IF ("Applies-to ID" = '') AND ("Applies-to Doc. No." = '') THEN
                        Error('Applies to ID or Applies to Doc No. Must have a value');
                //TestField("Applies-to ID");
                //PKON22JA6-CR220007


            end;
        }
        //modify()
        modify(Reopen)
        {
            trigger OnBeforeAction()
            var
                SalesLine: Record "Sales Line";
                UserSetup: Record "User Setup";
            BEGIN
                TestField("Order Status", 0);
                TestField("Location Code");
                CheckResponsilibilityCentre();
                if UserSetup.Get(UserId) then
                    if not UserSetup."ReOpen App Sales order" then
                        Error('Do not have permission to reopen');
                SalesLine.Reset();
                SalesLine.SetRange("Document Type", "Document Type");
                SalesLine.SetRange("Document No.", "No.");
                SalesLine.SetRange("Gift Item", true);
                if SalesLine.FindSet() then begin
                    SalesLine.DeleteAll();
                    Message('Promotion Lines Deleted Successfully');
                end;
            END;
        }
        modify(CancelApprovalRequest)
        {
            trigger OnBeforeAction()
            var
                SalesLine: Record "Sales Line";
            begin
                SalesLine.Reset();
                SalesLine.SetRange("Document Type", "Document Type");
                SalesLine.SetRange("Document No.", "No.");
                SalesLine.SetRange("Gift Item", true);
                if SalesLine.FindSet() then begin
                    SalesLine.DeleteAll();
                    Message('Promotion Lines Deleted Successfully');
                end;

            end;
        }

        modify(Release)
        {
            trigger OnAfterAction()
            var
                myInt: Integer;
            begin
                "Released By" := UserId;//b2bspon22oct18
            end;

            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
                TestField("Location Code");
                if not "POS Window" then begin
                    //CalPromtionSchemas();//PKONAU3
                    CheckResponsilibilityCentre();
                    CheckSalesMandValues(True);
                end;
            END;
        }
        modify(Post)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
                IF "Loading Slip Required" then
                    Error('Loading Slip is required and you can not post the orders directly. Please use Warehouse Shipment and Sales Invoice pages.');
                IF Cutlr.get("Sell-to Customer No.") AND (Cutlr."Customer Credit type" = Cutlr."Customer Credit type"::"Cash n Carry") and not "POS Window" then
                    IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') then
                        ERRor('Applies to doc no. or Applies to ID must have a value.');
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;

        }
        modify(PostAndNew)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
                IF "Loading Slip Required" then
                    Error('Loading Slip is required and you can not post the orders directly. Please use Warehouse Shipment and Sales Invoice pages.');
                IF Cutlr.get("Sell-to Customer No.") AND (Cutlr."Customer Credit type" = Cutlr."Customer Credit type"::"Cash n Carry") then
                    IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') then
                        ERRor('Applies to doc no. or Applies to ID must have a value.');
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;
        }
        Modify(PostAndSend)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
                IF "Loading Slip Required" then
                    Error('Loading Slip is required and you can not post the orders directly. Please use Warehouse Shipment and Sales Invoice pages.');
                IF Cutlr.get("Sell-to Customer No.") AND (Cutlr."Customer Credit type" = Cutlr."Customer Credit type"::"Cash n Carry") then
                    IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') then
                        ERRor('Applies to doc no. or Applies to ID must have a value.');
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;
        }
        modify(PreviewPosting)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
            END;
        }
        modify("Create &Warehouse Shipment")
        {
            trigger OnBeforeAction()
            begin
                TestField(Status, Status::Released);
                CheckSalesMandValues(True);
            end;
        }


        addafter(CopyDocument)
        {
            action("Calc. Promotion Schemes")
            {
                image = Copy;
                ApplicationArea = all;
                Caption = 'Calc. Promotion Schemes';
                visible = false;
                trigger OnAction()
                begin
                    IF ("Gen. Bus. Posting Group" <> 'TRANSFER') AND ("Customer Disc. Group" IN ['DISTRIBU', 'KEYDISTRIB'])
  AND ("Gen. Bus. Posting Group" <> 'RETAILSHOP') THEN
                        UpdatePromotionLines(0)
                    else
                        Message('No Promo Schemas Available for this customer');
                end;

            }
            action("Reselect Gift Item Flavour")
            {
                Image = Select;
                Caption = 'Reselect Gift Item Flavour';
                ApplicationArea = all;
                trigger OnAction()
                begin
                    CurrPage.SalesLines.Page.SelectGiftItemFlavour;

                end;

            }

            action("Short Close Order")
            {
                Image = CloseDocument;
                ApplicationArea = all;
                Caption = 'Short Close Order';
                trigger OnAction();
                begin
                    Rec.ShortCloseCheck();
                    CurrPage.Update();
                    ShortCloseSalesOrder;
                end;
            }

            /*action("Generate Sales Volume Sheets")
            {
                Image = CloseDocument;
                ApplicationArea = all;
                RunObject = codeunit "Sales Volume";

            }*/

            action(ApplyEntries)
            {
                ApplicationArea = Basic, Suite;
                Caption = 'Apply Entries';
                Ellipsis = true;
                Enabled = "No." <> '';
                Image = ApplyEntries;
                Promoted = true;
                PromotedCategory = Category7;
                ShortCutKey = 'Shift+F11';
                ToolTip = 'Select one or more ledger entries that you want to apply this record to so that the related posted documents are closed as paid or refunded.';

                trigger OnAction()
                begin
                    CODEUNIT.Run(CODEUNIT::"Sales Header Apply", Rec);
                end;
            }

            /*action("Apply Entries")
            {
                ApplicationArea = all;
                Caption = 'Apply Entries';
                trigger OnAction();
                begin
                    CustLedgEntry.FILTERGROUP(2);
                    CustLedgEntry.SETCURRENTKEY("Customer No.", Open, Positive, "Due Date");
                    CustLedgEntry.SETRANGE("Customer No.", "Bill-to Customer No.");
                    CustLedgEntry.SETRANGE("Responsibility Center", "Responsibility Center"); // SAA3.0
                    CustLedgEntry.SETRANGE(Open, true);
                    CustLedgEntry.FILTERGROUP(0);
                    if "Applies-to Doc. No." <> '' then begin
                        CustLedgEntry.SETRANGE("Document Type", "Applies-to Doc. Type");
                        CustLedgEntry.SETRANGE("Document No.", "Applies-to Doc. No.");
                        if CustLedgEntry.FIND('-') then;
                        CustLedgEntry.SETRANGE("Document Type");
                        CustLedgEntry.SETRANGE("Document No.");
                    end else
                        if "Applies-to Doc. Type" <> 0 then begin
                            CustLedgEntry.SETRANGE("Document Type", "Applies-to Doc. Type");
                            if CustLedgEntry.FIND('-') then;
                            CustLedgEntry.SETRANGE("Document Type");
                        end else
                            if Amount <> 0 then begin
                                CustLedgEntry.SETRANGE(Positive, Amount < 0);
                                if CustLedgEntry.FIND('-') then;
                                CustLedgEntry.SETRANGE(Positive);
                            end;

                    ApplyCustEntries.SetSales(Rec, CustLedgEntry, FIELDNO("Applies-to Doc. No."));
                    ApplyCustEntries.SETTABLEVIEW(CustLedgEntry); //SAA3.0
                    ApplyCustEntries.SETRECORD(CustLedgEntry);//SAA3.0
                    ApplyCustEntries.LOOKUPMODE(true);
                    if ApplyCustEntries.RUNMODAL = ACTION::LookupOK then begin
                        ApplyCustEntries.GetCustLedgEntry(CustLedgEntry);
                        GenJnlApply.CheckAgainstApplnCurrency("Currency Code", CustLedgEntry."Currency Code", GenJnILine."Account Type"::Customer, true);
                        "Applies-to Doc. Type" := CustLedgEntry."Document Type";
                        "Applies-to Doc. No." := CustLedgEntry."Document No.";
                    end;

                    CLEAR(ApplyCustEntries);
                    CLEAR(CustLedgEntry);
                end;
            }*/



            /*action(Test)
            {
                ApplicationArea = ALL;
                trigger OnAction()
                var
                    WorkFlow: Record "Workflow Event";
                BEGIN
                    WorkFlow.reset;
                    IF WorkFlow.findset then
                        WorkFlow.DeleteAll();
                    message('Done.');
                END;

            }*/
            action("Generate Bin Reclasification Report")
            {
                ApplicationArea = All;
                Image = Report;
                trigger OnAction()
                var
                    Binrecjnl: Record "Bin Reclassfication Jnl";
                begin
                    //50476
                    Binrecjnl.Reset();
                    Binrecjnl.SetRange("Document Type", Binrecjnl."Document Type"::Sale);
                    Binrecjnl.SetRange("Document No.", "No.");
                    IF Binrecjnl.FindSet() then
                        Report.RunModal(50476, true, false, Binrecjnl);

                end;
            }



        }
    }
    trigger OnOpenPage()
    var
        myInt: Integer;
    begin
        InitOutstandingQutyT(Rec);//PKONOC28
    end;

    var
        CustGRec: Record Customer;
        TestCU2: Codeunit Codeunit2;
        CustLedgEntry: Record "Cust. Ledger Entry";
        ApplyCustEntries: Page "Apply Customer Entries";
        GenJnlApply: Codeunit "Gen. Jnl.-Apply";
        GenJnILine: Record "Gen. Journal Line";
        cutlr: Record Customer;
}