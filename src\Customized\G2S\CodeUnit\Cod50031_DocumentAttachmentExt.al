/// <summary>
/// Codeunit DocumentAttachmentPROCESS (ID 50031).
/// </summary>
/// 050824
codeunit 50033 "Document Attachment Ext"
{

    [EventSubscriber(ObjectType::Page, Page::"Document Attachment Factbox", 'OnBeforeDrillDown', '', false, false)]
    local procedure OnBeforeDrillDown(DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef);
    var
        mrsHeader: Record MRSHeader;
        bpVoucher: Record "Voucher Header";
        CapexHeader: Record "Capex Header";
        PostedVouHdr: Record "Posted Voucher Header";
        BudgetHdr: Record "Budget Header";
        vendor2Table: Record "Vendor 2";
    begin
        case DocumentAttachment."Table ID" of
            DATABASE::MRSHeader:
                begin
                    RecRef.Open(DATABASE::MRSHeader);
                    if mrsHeader.Get(DocumentAttachment."No.") then
                        RecRef.GetTable(mrsHeader);
                end;

            DATABASE::"Voucher Header":
                begin
                    RecRef.Open(DATABASE::"Voucher Header");
                    // if bpVoucher.Get(DocumentAttachment.Type, DocumentAttachment."No.") then
                    // if bpVoucher.Get(DocumentAttachment."No.") then
                    //     RecRef.GetTable(bpVoucher);
                    bpVoucher.Reset();
                    bpVoucher.SetRange("Document No.", DocumentAttachment."No.");
                    if bpVoucher.FindFirst() then begin
                        RecRef.GetTable(bpVoucher);
                    end;
                end;
            DATABASE::"Capex Header":
                begin
                    RecRef.Open(DATABASE::"Capex Header");
                    // if bpVoucher.Get(DocumentAttachment.Type, DocumentAttachment."No.") then
                    // if bpVoucher.Get(DocumentAttachment."No.") then
                    //     RecRef.GetTable(bpVoucher);
                    CapexHeader.Reset();
                    CapexHeader.SetRange("No.", DocumentAttachment."No.");
                    if CapexHeader.FindFirst() then begin
                        RecRef.GetTable(CapexHeader);
                    end;
                end;

            DATABASE::"Posted Voucher Header":
                begin
                    RecRef.Open(DATABASE::"Posted Voucher Header");
                    // if bpVoucher.Get(DocumentAttachment.Type, DocumentAttachment."No.") then
                    // if bpVoucher.Get(DocumentAttachment."No.") then
                    //     RecRef.GetTable(bpVoucher);
                    PostedVouHdr.Reset();
                    PostedVouHdr.SetRange("Document No.", DocumentAttachment."No.");
                    if PostedVouHdr.FindFirst() then begin
                        RecRef.GetTable(PostedVouHdr);
                    end;
                end;

            DATABASE::"Budget Header":
                begin
                    RecRef.Open(DATABASE::"Budget Header");
                    // if bpVoucher.Get(DocumentAttachment.Type, DocumentAttachment."No.") then
                    // if bpVoucher.Get(DocumentAttachment."No.") then
                    //     RecRef.GetTable(bpVoucher);
                    BudgetHdr.Reset();
                    BudgetHdr.SetRange("No.", DocumentAttachment."No.");
                    if BudgetHdr.FindFirst() then begin
                        RecRef.GetTable(BudgetHdr);
                    end;
                end;

            DATABASE::"Vendor 2":
                begin
                    RecRef.Open(DATABASE::"Vendor 2");

                    vendor2Table.Reset();
                    vendor2Table.SetRange("No.", DocumentAttachment."No.");
                    if vendor2Table.FindFirst() then begin
                        RecRef.GetTable(vendor2Table);
                    end;
                end;

        end;
    end;

    [EventSubscriber(ObjectType::Page, Page::"Document Attachment Details", 'OnAfterOpenForRecRef', '', false, false)]
    local procedure OnAfterOpenForRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef);
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
        DocType: Option;
    begin
        case RecRef.Number of
            DATABASE::MRSHeader,
            DATABASE::"Vendor 2":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.SetRange("No.", RecNo);
                end;

            Database::"Voucher Header",
            Database::"Capex Header",
            DATABASE::"Posted Voucher Header",
            DATABASE::"Budget Header":

                begin
                    FieldRef := RecRef.Field(2);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.SetRange("No.", RecNo);
                end;
        end;
    end;




    [EventSubscriber(ObjectType::Table, Database::"Document Attachment", 'OnAfterInitFieldsFromRecRef', '', false, false)]
    local procedure OnAfterInitFieldsFromRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
        DocType: Option;
    begin

        //141223
        DocumentAttachment.Validate("Table ID", RecRef.Number);
        case RecRef.Number of
            DATABASE::MRSHeader,
            // DATABASE::"Voucher Header",
            DATABASE::"Vendor 2":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.Validate("No.", RecNo);
                end;
            DATABASE::"Voucher Header",
            DATABASE::"Capex Header",
            DATABASE::"Posted Voucher Header",
            DATABASE::"Budget Header":
                begin
                    FieldRef := RecRef.Field(2);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.Validate("No.", RecNo);
                end;
        end;
        //141223
    end;

    //Confirm If attachment is enabled on Custom Setup.
    procedure IsAttachmentsEnabled(): Boolean
    var
        CustomSetup: Record "Custom Setup";
    begin
        CustomSetup.Reset();
        CustomSetup.SetRange(Category, CustomSetup.Category::Attachments);
        IF CustomSetup.FindFirst() THEN
            EXIT(TRUE);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Document Attachment", 'OnBeforeDeleteEvent', '', false, false)]
    local procedure OnBeforeDeleteEvent(var Rec: Record "Document Attachment"; RunTrigger: Boolean)
    var
        "Posted Bank Pmt Voucher Staff": Record "Posted Voucher Header";
        DeleteErrMsg: Label 'You cannot delete attachement of a posted Document';
    begin
        "Posted Bank Pmt Voucher Staff".SetCurrentKey("Document No.");
        "Posted Bank Pmt Voucher Staff".SetRange("Document No.", Rec."No.");
        if "Posted Bank Pmt Voucher Staff".FindFirst() then begin
            if "Posted Bank Pmt Voucher Staff".Status = "Posted Bank Pmt Voucher Staff".Status::Released then Error(DeleteErrMsg);
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Document Attachment", 'OnBeforeInsertAttachment', '', false, false)]
    local procedure OnBeforeInsertAttachment(var DocumentAttachment: Record "Document Attachment")
    var
        "Posted Bank Pmt Voucher Staff": Record "Posted Voucher Header";
        InsertErrMsg: Label 'You cannot Insert attachement to a posted Document';
    begin
        "Posted Bank Pmt Voucher Staff".SetCurrentKey("Document No.");
        "Posted Bank Pmt Voucher Staff".SetRange("Document No.", DocumentAttachment."No.");
        if "Posted Bank Pmt Voucher Staff".FindFirst() then begin
            if "Posted Bank Pmt Voucher Staff".Status = "Posted Bank Pmt Voucher Staff".Status::Released then Error(InsertErrMsg);
        end;
    end;
}

