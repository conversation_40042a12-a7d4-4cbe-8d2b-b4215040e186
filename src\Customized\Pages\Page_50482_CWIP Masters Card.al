page 50482 "CWIP Card"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Documents;
    SourceTable = "CWIP Masters";

    layout
    {
        area(Content)
        {
            group(General)
            {
                field("CWIP No."; "CWIP No.")
                {
                    ApplicationArea = All;
                    trigger OnAssistEdit()
                    var
                        myInt: Integer;
                    begin
                        IF AssistEdit(Rec) THEN
                            CurrPage.UPDATE;

                    end;

                }
                field("CWIP Name"; "CWIP Name")
                {
                    ApplicationArea = all;
                }
                field("GL Account No."; "GL Account No.")
                {
                    ApplicationArea = all;
                }
                field("FA No."; "FA No.")
                {
                    ApplicationArea = all;
                }
                field(Certified; Certified)
                {
                    ApplicationArea = all;
                }
                field("Certified Date"; "Certified Date")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field(Balance; Balance2)
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Capex No."; "Capex No.")
                {
                    ApplicationArea = all;
                }
                field("Capex Line No."; "Capex Line No.")
                {
                    ApplicationArea = all;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                }

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Ledger Entries")
            {
                RunObject = page "CWIP Master Ledg Entries List";
                RunPageLink = "CWIP No." = field("CWIP No.");
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
            action("&Approvals")
            {
                Caption = '&Approvals';

                trigger OnAction();
                var
                    ApprovalEntries: Page "Approval Entries";
                begin
                    ApprovalEntries.Setfilters(DATABASE::"CWIP Masters", 0, "CWIP No.");
                    ApprovalEntries.RUN;
                end;
            }
            action("&Dimensions")
            {
                Caption = '&Dimensions';
                Image = Dimensions;
                RunObject = Page "Default Dimensions";
                RunPageLink = "Table ID" = CONST(50321), "No." = FIELD("CWIP No.");
            }

            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Release")
                {
                    Caption = 'Release';

                    trigger OnAction();
                    var
                        Text002: label 'This document can only be released when the approval process is complete.';
                    begin
                        CheckMandFields();
                        // IF allinoneCU.CheckAddCWIPApprovalsWorkflowEnabled(Rec) then
                        //Error(Text002);
                        testfield(status, status::Open);
                        Status := Status::Release;
                    end;
                }
                action("Reopen")
                {
                    Caption = 'Reopen';

                    trigger OnAction();
                    begin

                        TestField(status, Status::Release);
                        status := Status::Open;

                    end;
                }
                action("Send A&pproval Request")
                {
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    var
                    begin
                        CheckMandFields();
                        IF allinoneCU.CheckAddCWIPApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendAddCWIPForApproval(Rec);

                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin

                        allinoneCU.OnCancelAddCWIPForApproval(Rec);

                    end;
                }
                action(Post)
                {
                    Caption = 'Capitalize To FA';

                    trigger OnAction();
                    begin
                        TestField(Certified, true);
                        TestField("Certified Date");
                        testfield(Status, Status::Release);
                        if not "Capitalized To FA" then
                            PostFALedger()
                        Else
                            Message('Already CWIP is Capitalized to FA');
                    end;
                }
            }

        }


    }

    var
        myInt: Integer;
        allinoneCU: Codeunit Codeunit1;

    local procedure CheckMandFields()
    begin
        TestField("Capex No.");
        TestField("Capex Line No.");
        TestField("Global Dimension 1 Code");
        TestField("Global Dimension 2 Code");
    end;

    local procedure PostFALedger()
    var
        GenJourn: Record "Gen. Journal Line";
        NextGLLineNo: Integer;
        GenJournle: Record "Gen. Journal Line";
        GenJnLne: Codeunit "Gen. Jnl.-Post Line2";
        FADespreBook: Record "FA Depreciation Book";
        Text001: Label 'Capitalized To FA Sucessfully';
        FADpeB: Record "FA Depreciation Book";
        FAPostingGr: Record "FA Posting Group";
        FixedAsset: Record "Fixed Asset";
    begin
        GenJourn.reset;
        GenJourn.SetRange("Journal Batch Name", 'ASSET');
        GenJourn.SetRange("Journal Template Name", 'ASSETS');
        IF GenJourn.FindLast() then
            NextGLLineNo := GenJourn."Line No." + 10000
        else
            NextGLLineNo := 10000;
        FixedAsset.Get("FA No.");
        FixedAsset.TestField("Approval Status", FixedAsset."Approval Status"::Released);
        FixedAsset.TestField(Blocked, false);
        if FixedAsset."Main Asset/Component" <> FixedAsset."Main Asset/Component"::Component then
            if not (Format(FixedAsset."FA Posting Group") IN ['COMP-SOFTW', 'F&F OFFICE', 'F&F RESIDE', 'MHEQUIP', 'ROADS', 'ROADS 2', 'BUILDING', 'LAND', 'LEASEBLDG',
                                         'LEASELAND']) then
                if FixedAsset."FA Tagging Required" then
                    FixedAsset.TestField("FA Tagging Code");
        //FIX23Jun2021>>
        FADpeB.RESET;
        FADpeB.SetRange("FA No.", "FA No.");
        IF Not FADpeB.FindFirst() THEN
            Error('Depreciation Book must no be empty in FANo %1', "FA No.");
        //FIX23Jun2021<<
        GenJournle.init;
        GenJournle."Journal Template Name" := 'ASSETS';
        GenJournle."Journal Batch Name" := 'ASSET';
        GenJournle."Line No." := NextGLLineNo;
        GenJournle."Posting Date" := Today;
        GenJournle.VALIDATE("Document No.", FORMAT(NextGLLineNo));
        FADespreBook.Reset();
        FADespreBook.SetRange("FA No.", "FA No.");
        if FADespreBook.FindFirst() then;
        GenJournle.VALIDATE("Account Type", GenJournle."Bal. Account Type"::"Fixed Asset");
        GenJournle.Validate("Account No.", "FA No.");
        GenJournle.VALIDATE("Depreciation Book Code", FADespreBook."Depreciation Book Code");
        GenJournle."FA Posting Type" := GenJournle."FA Posting Type"::"Acquisition Cost";
        GenJournle.VALIDATE("Bal. Account Type", GenJournle."Bal. Account Type"::"G/L Account");
        GenJournle.VALIDATE("Bal. Account No.", "GL Account No.");
        //FIX08Jun2021>>
        GenJournle.Validate("Bal. Gen. Posting Type", GenJournle."Bal. Gen. Posting Type"::" ");
        GenJournle.Validate("Bal. Gen. Bus. Posting Group", '');
        GenJournle.Validate("Bal. Gen. Prod. Posting Group", '');
        //FIX08Jun2021<<
        CalcFields(Balance);
        GenJournle.VALIDATE(Quantity, 1);
        //GenJournle.VALIDATE(Amount, Balance);
        GenJournle.VALIDATE(Amount, Balance2);//PKONNO5
        //GenJournle.Validate("Debit Amount", Balance);//PKONNO5
        GenJournle.Validate("Capex No.", "Capex No.");
        GenJournle.Validate("Capex Line No.", "Capex Line No.");
        GenJournle."CWIP No." := "CWIP No.";
        GenJournle.Insert();
        NextGLLineNo += 10000;

        GenJnLne.RunWithCheck(GenJournle);
        FADpeB.RESET;
        FADpeB.SetRange("FA No.", "FA No.");
        IF FADpeB.FINDSET THEN
            repeat
                IF FADpeB."Depreciation Starting Date" = 0D THEN
                    FADpeB."Depreciation Starting Date" := Today;
                IF FAPostingGr.GET(FADpeB."FA Posting Group") then begin
                    IF (FAPostingGr."No. of Depreciation Years" <> 0) AND (FADpeB."No. of Depreciation Years" = 0) then
                        FADpeB.Validate("No. of Depreciation Years", FAPostingGr."No. of Depreciation Years");
                end;
                FADpeB.Modify();
            until FADpeB.NEXT = 0;



        "Capitalized To FA" := true;
        Modify();
        Message(Text001);
    end;
}