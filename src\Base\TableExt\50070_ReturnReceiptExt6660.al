tableextension 50070 ReturnReceiptExt extends "Return Receipt Header"
{
    fields
    {
        field(50018; "GRN No."; Code[20])
        {
            DataClassification = CustomerContent;

        }
        field(50019; "Branch GRN No."; code[20])
        {
            DataClassification = CustomerContent;

        }
        field(50020; "Transporter Code"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor."No.";

        }
        field(50021; "Transporter Name"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Sales Ret. Created By"; code[50])
        {
            DataClassification = CustomerContent;
        }
        field(50023; "Amount Including VAT"; Decimal)
        {

            FieldClass = FlowField;
            CalcFormula = Sum ("Return Receipt Line"."Amount Including VAT" where("Document No." = FIELD("No.")));
            Editable = false;
        }

        modify("Sell-to Customer No.")
        {
            trigger OnAfterValidate()
            var

            BEGIN
                IF (CustGRec.get("Sell-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
            END;

        }
        modify("Bill-to Customer No.")
        {
            trigger OnAfterValidate()
            var

            BEGIN
                IF (CustGRec.get("Bill-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
            END;

        }
    }

    var
        myInt: Integer;
        CustGRec: Record customer;
}