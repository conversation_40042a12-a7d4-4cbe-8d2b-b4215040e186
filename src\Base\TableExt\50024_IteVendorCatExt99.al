tableextension 50024 ItemVendCat extends "Item Vendor"
{
    fields
    {
        field(50001; "Qty. Supplied With in DueDate"; Decimal)
        {
            DataClassification = CustomerContent; 
            Editable = false;
        }
        field(50002; "Delivery Rating"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50003; "Avg. Delivery Rating"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50004; "Total Qty. Supplied"; Decimal)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum ("Purch. Rcpt. Line".Quantity WHERE(Type = CONST(Item), "Buy-from Vendor No." = FIELD("Vendor No."), "No." = FIELD("Item No.")));
        }
        field(50005; "Avg. Quality Rating"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Vendor Min.Ord.Qty"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
    }

}