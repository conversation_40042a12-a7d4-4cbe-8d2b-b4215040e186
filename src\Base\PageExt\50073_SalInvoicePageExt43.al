pageextension 50073 SalInvoice extends "Sales Invoice"
{
    layout
    {

        addafter("Salesperson Code")
        {
            field("Applies-to Doc. No."; "Applies-to Doc. No.")
            {
                ApplicationArea = ALL;
            }
            field("Applies-to Doc. Type"; "Applies-to Doc. Type")
            {
                ApplicationArea = ALL;
            }
            field("Applies-to ID"; "Applies-to ID")
            {
                ApplicationArea = ALL;
            }
        }

    }
    actions
    {
        modify(Release)
        {
            trigger OnBeforeAction()
            begin
                CCDChecking();
            end;
        }
        modify(Post)
        {
            trigger OnBeforeAction()
            begin
                CCDChecking();
            end;
        }
        modify(PostAndNew)
        {
            trigger OnBeforeAction()
            begin
                CCDChecking();
            end;
        }
        modify(PostAndSend)
        {
            trigger OnBeforeAction()
            begin
                CCDChecking();
            end;
        }

    }

    procedure CCDChecking()
    begin
        if ("Document Type" = "Document Type"::Invoice) then begin
            SalesLineGRec.reset();
            SalesLineGRec.SetRange("Document Type", "Document Type");
            SalesLineGRec.SetRange("Document No.", "No.");
            IF SalesLineGRec.FINDSET THEN
                repeat
                    PostedWhseRcptLineGRec.RESET;
                    PostedWhseRcptLineGRec.SetRange("No.", SalesLineGRec."Shipment No.");
                    PostedWhseRcptLineGRec.Setfilter("Posted Loading Slip No.", '<>%1', '');
                    IF PostedWhseRcptLineGRec.FindFirst() THEN begin
                        PostedLdngSlipHdr.GET(PostedWhseRcptLineGRec."Posted Loading Slip No.");
                        IF PostedLdngSlipHdr."SSD Acknowledge Date" = 0D then
                            Error('Loading Slip is Not Acknowledged %1', PostedLdngSlipHdr."No.");
                    end;
                until SalesLineGRec.next = 0;
        end
    end;



    var

        SalesLineGRec: Record "Sales Line";
        PostedWhseRcptLineGRec: Record "Posted Whse. Receipt Line";
        PostedLdngSlipHdr: Record "Posted Loading SLip Header";

}