/// <summary>
/// TableExtension DocumentAttachment (ID 50276) extends Record Document Attachment.
/// </summary>
tableextension 50276 DocumentAttachment extends "Document Attachment"
{
    fields
    {
        field(50000; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
            end;
        }
        //>>>>>G2S 191223 
        field(50001; "Type"; Option)
        {
            OptionMembers = JV,CPV,CRV,BPV,BRV,BCM,BDM;
        }
        //<<<<<<G2S 191223
    }

    // procedure InitFieldsFromRecRef(RecRef: RecordRef)
    // var
    //     DocumentAttachmentMgmt: Codeunit "Document Attachment_MGT";
    //     FieldRef: FieldRef;
    //     RecNo: Code[20];
    //     DocType: Option Quote,"Order",Invoice,"Credit Memo","Blanket Order","Return Order";
    //     FieldNo: Integer;
    //     LineNo: Integer;
    // begin
    //     Validate("Table ID", RecRef.Number);

    //     if DocumentAttachmentMgmt.TableHasNumberFieldPrimayKey(RecRef.Number(), FieldNo) then begin
    //         FieldRef := RecRef.Field(FieldNo);
    //         RecNo := FieldRef.Value();
    //         Validate("No.", RecNo);
    //     end;

    //     if DocumentAttachmentMgmt.TableHasDocTypePrimaryKey(RecRef.Number(), FieldNo) then begin
    //         FieldRef := RecRef.Field(FieldNo);
    //         DocType := FieldRef.Value();
    //         Validate("Document Type", DocType);
    //     end;

    //     if DocumentAttachmentMgmt.TableHasLineNumberPrimaryKey(RecRef.Number(), FieldNo) then begin
    //         FieldRef := RecRef.Field(FieldNo);
    //         LineNo := FieldRef.Value();
    //         Validate("Line No.", LineNo);
    //     end;

    //     OnAfterInitFieldsFromRecRef(Rec, RecRef);
    // end;



    [IntegrationEvent(false, false)]
    local procedure OnAfterInitFieldsFromRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    begin
    end;

    var
        myInt: Integer;
}