/// <summary>
/// Page Shipment Transactions Log Hdr (ID 50102).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
page 50164 "Shipment Transactions Log Hdr"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    Editable = false;
    ModifyAllowed = false;
    InsertAllowed = false;
    Caption = 'CHI_Outreach Json Transaction';
    SourceTable = "Shipment Transactions Hdr Log";
    SourceTableView = sorting(ID) order(descending);

    layout
    {
        area(Content)
        {
            group("CHI Integration Log")
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Sell-to Customer No."; Rec."Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Sell-to Customer No. field.';
                }
                field("Shipment Date"; Rec."Shipment Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Shipment Date field.';
                }
                field(CompanyName; Rec.CompanyName)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the CompanyName field.';
                }
                field("Veh. No."; Rec."Veh. No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Veh. No. field.';
                }
                field("Sent Status"; Rec."Sent Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Sent Status field.';
                }
                field("DateTime Sent to Outreach"; "Date Sent to Outreach")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Temp. Omit Rec"; "Temp. Omit Rec")
                {
                    ApplicationArea = All;
                }
            }
            part(shipmentTransactionsLog; "Shipment Transaction Lines Log")
            {
                ApplicationArea = All;
                SubPageLink = "Document No." = field("No.");
                Editable = false;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(RunJSON)
            {
                ApplicationArea = All;
                Caption = 'Run JSON';
                Image = Payroll;
                ToolTip = 'Executes the Run JSON action.';
                trigger OnAction()
                var
                    jsonSend: Codeunit "Json Integration";
                begin
                    jsonSend.sendBatchShipmentJSONData(Rec);
                end;
            }
        }
    }
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<