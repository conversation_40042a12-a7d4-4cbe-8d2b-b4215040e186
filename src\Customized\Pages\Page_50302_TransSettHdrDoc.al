page 50302 "Transport Settlement Doc"
{
    PageType = Document;
    SourceTable = "Transport Settlement Header";

    layout
    {
        area(Content)
        {
            group(General)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Creation Date"; "Creation Date")
                {
                    ApplicationArea = all;
                }
                field("Description"; "Description")
                {
                    ApplicationArea = all;
                }
                field("Transporter Cont. Type"; "Transporter Cont. Type")
                {
                    ApplicationArea = all;
                }
                field("Inclusive VAT %"; "Inclusive VAT %")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
            }
            part(Control1500028; "Trans Setll Subform")
            {
                SubPageLink = "Document No." = FIELD("No.");
                ApplicationArea = ALL;
            }
        }
    }
    actions
    {
        area(navigation)
        {
            group("&Actions")
            {
                action("Get Transport ledger Entries")
                {
                    Caption = 'Get Transport Ledger Entries';
                    ApplicationArea = all;
                    Image = GetEntries;
                    trigger OnAction();
                    begin
                        IF not ("Transporter Cont. Type" = "Transporter Cont. Type"::BHN) then
                            Error('Transporter contract type must be BHN');
                        UpdateFromTraspLedEntry();
                    end;
                }
                action("Update Line Amounts")
                {
                    ApplicationArea = all;
                    Image = Calculate;
                    trigger OnAction();
                    begin
                        UpdateLineAmnt();
                    end;
                }
                action("Create Purchase Order")
                {
                    Caption = 'Create Purchase Order';
                    ApplicationArea = all;
                    Image = Create;
                    trigger OnAction();
                    begin
                        /*IF not (("Transporter Cont. Type" = "Transporter Cont. Type"::Aglevinthis) or ("Transporter Cont. Type" = "Transporter Cont. Type"::BHN)) then*/
                        IF "Transporter Cont. Type" <> "Transporter Cont. Type"::BHN then
                            Error('Transporter contract type must be BHN');
                        CreatePo();
                    end;
                }
            }
        }
    }

    procedure UpdateFromTraspLedEntry();
    Var
        TraspLedEntry: Record "Transport Ledger Entries";
        DetaildTraLedEnt: record "Transfer Settlement Lines";
        EntryNo: integer;
        PostdLoaSlipLne: Record "Posted Loading Slip Line";
        DetTransLedgerEntry: Record "Detailed Tran Ledger Entry";
        MaxDistanceValue: Decimal;
        PostLadSlpHdr: Record "Posted Loading SLip Header";
        VehicleTransporter: Record "Transporter Vehicle";
        PostLadSlpHdr2: Record "Posted Loading SLip Header";
        PosLodSlpLine: Record "Posted Loading Slip Line";
        transContr: Record "Transport Contract Types";//PKONJU19
        VendorL: Record Vendor;//PKONJU19
    begin
        //b2bpksalecorr15
        /*IF Not (("Transporter Cont. Type" = "Transporter Cont. Type"::BHN) or ("Transporter Cont. Type" = "Transporter Cont. Type"::Aglevinthis)) then
            Error('You can not select other than BHN and AGleventhis Option.');*/
        IF Not ("Transporter Cont. Type" = "Transporter Cont. Type"::BHN) then
            Error('You can not select other than AGleventhis Option.');
        if ("Start Date" = 0D) OR ("End Date" = 0D) then
            ERROR('Please select the Start and End dates');
        if CONFIRM('Lines will delete and insert again. Do you want to insert transport ledger entries ?', false) then begin
            DetaildTraLedEnt.RESET;
            DetaildTraLedEnt.SetRange("Document No.", "No.");
            if DetaildTraLedEnt.FindSet() then
                DetaildTraLedEnt.DeleteAll();
            DetaildTraLedEnt.reset;
            EntryNo := 10000;
            TraspLedEntry.RESET();
            TraspLedEntry.SETRANGE("Trip Date", "Start Date", "End Date");
            TraspLedEntry.SETRANGE("Vehicle By", TraspLedEntry."Vehicle By"::Transporter);
            TraspLedEntry.SetRange("Contract Type", "Transporter Cont. Type");//b2bpksalecorr15
            if TraspLedEntry.FINDSET() then begin
                repeat
                    PostLadSlpHdr2.reset;
                    PostLadSlpHdr2.SetRange("No.", TraspLedEntry."Posted Slip Number");
                    PostLadSlpHdr2.Setfilter("Purchase Order No.", '=%1', '');
                    IF "Transporter Cont. Type" = "Transporter Cont. Type"::BHN then
                        PostLadSlpHdr2.SetRange("Invoice Type", PostLadSlpHdr2."Invoice Type"::Multiple);//b2bpksalecorr14
                    IF PostLadSlpHdr2.findfirst then begin
                        IF "Transporter Cont. Type" = "Transporter Cont. Type"::BHN then begin
                            //B2BFIX 23Apr2021>>
                            PosLodSlpLine.reset;
                            PosLodSlpLine.SetRange("Document No.", PostLadSlpHdr2."No.");
                            PosLodSlpLine.SetRange("Document Type", PosLodSlpLine."Document Type"::Shipment);
                            PosLodSlpLine.Setfilter("Customer No.", '<>%1', '');
                            IF not PosLodSlpLine.findset then
                                PostLadSlpHdr2.TESTfield("CCD No.");
                            //B2BFIX 23Apr2021<<
                            PostLadSlpHdr2.TESTfield("SSD No.");
                            //PostLadSlpHdr2.TESTfield("LOADing & OFFLOADiNG TIME COMB");//PKONJU19
                            //PostLadSlpHdr2.TESTfield("Full Diseal Price");//PKONJU19
                        end;
                        //PKONJU19>>
                        VendorL.get(PostLadSlpHdr2."Party No.");
                        transContr.Reset();
                        transContr.SetRange("Vendor No.", VendorL."No.");
                        transContr.SetRange("Contract Type", VendorL."Transport Contract");
                        transContr.Setfilter("Starting Date", '<=%1', WorkDate());
                        transContr.Setfilter("Ending Date", '>=%1', WorkDate());
                        transContr.SetRange(Status, transContr.status::Release);
                        transContr.SetRange("From Location", PostLadSlpHdr2."Transport From Location");
                        transContr.SetRange("To-Location", PostLadSlpHdr2."Transport To Location");
                        transContr.SetRange("Non Branded Vehicle", false);
                        //transContr.SetFilter(Rate, '>%1', 0);
                        IF transContr.FindFirst() then BEGIN
                            transContr.TESTfield("LOADING & OFFLOAd TIME COMBINE");
                            transContr.TESTfield("Full Diesel Price");
                            transContr.TESTfield("Half Diesel Price");
                        end;
                        //PKONJU19<<
                        // Message('%1', PostLadSlpHdr2."No.");//PKON22JA7-CR220005-2
                        TraspLedEntry.CalcFields("Distance Travelled");
                        DetaildTraLedEnt.INIT();
                        DetaildTraLedEnt."Document No." := "No.";
                        DetaildTraLedEnt."Line No." := EntryNo;
                        DetaildTraLedEnt."Transport Ledger Entry No." := TraspLedEntry."Entry No";
                        DetaildTraLedEnt."Trip Id" := TraspLedEntry."Trip Id";
                        DetaildTraLedEnt."Trip Date" := TraspLedEntry."Trip Date";
                        DetaildTraLedEnt."Vehicle By" := TraspLedEntry."Vehicle By";
                        DetaildTraLedEnt."Vehicle No" := TraspLedEntry."Vehicle No";
                        DetaildTraLedEnt."Vehicle Type" := TraspLedEntry."Vehicle Type";
                        DetaildTraLedEnt."Contract Type" := TraspLedEntry."Contract Type";
                        TraspLedEntry.CALCFIELDS("Distance Travelled");
                        DetaildTraLedEnt.Distance := TraspLedEntry."Distance Travelled";
                        /*
                        if VehicleTransporter.Get(TraspLedEntry."Vehicle No") then
                            DetaildTraLedEnt."Vendor No." := VehicleTransporter."Vendor No.";*/
                        VehicleTransporter.Reset();
                        VehicleTransporter.SetRange("Vehicle Reg No.", TraspLedEntry."Vehicle No");
                        if VehicleTransporter.FindFirst() then begin
                            DetaildTraLedEnt."Vendor No." := VehicleTransporter."Vendor No.";
                        end;
                        DetTransLedgerEntry.reset;
                        DetTransLedgerEntry.SetRange("Transprt Ledger Entrires", TraspLedEntry."Entry No");
                        DetTransLedgerEntry.SetCurrentKey("Distance Travelled");
                        IF DetTransLedgerEntry.findset then
                            repeat
                                IF (DetTransLedgerEntry."Distance Travelled" >= MaxDistanceValue) THEN
                                    MaxDistanceValue := DetTransLedgerEntry."Distance Travelled";

                                DetaildTraLedEnt."From Location" := DetTransLedgerEntry."From Location";
                            until DetTransLedgerEntry.next = 0;

                        PostLadSlpHdr.reset;
                        PostLadSlpHdr.SetRange("No.", TraspLedEntry."Posted Slip Number");
                        IF PostLadSlpHdr.findfirst then begin
                            DetaildTraLedEnt."CCD No." := PostLadSlpHdr."CCD No.";
                            DetaildTraLedEnt."CCD Acknowledge Date" := PostLadSlpHdr."CCD Acknowledge Date";
                            DetaildTraLedEnt."CCD Acknowledge By" := PostLadSlpHdr."CCD Acknowledge By";
                            DetaildTraLedEnt."SSD No." := PostLadSlpHdr."SSD No.";
                            DetaildTraLedEnt."SSD Acknowledge By" := PostLadSlpHdr."SSD Acknowledge By";
                            DetaildTraLedEnt."SSD Acknowledge Date" := PostLadSlpHdr."SSD Acknowledge Date";
                            DetaildTraLedEnt."PLS From-Location" := PostLadSlpHdr."Transport From Location";
                            DetaildTraLedEnt."PLS To-Location" := PostLadSlpHdr."Transport To Location";
                            //DetaildTraLedEnt."LOADing & OFFLOADiNG TIME COMB" := PostLadSlpHdr."LOADing & OFFLOADiNG TIME COMB";//PKONJU19
                            //DetaildTraLedEnt."Full Diseal Price" := PostLadSlpHdr."Full Diseal Price";//PKONJU19
                            //DetaildTraLedEnt."Half Diseal Price" := PostLadSlpHdr."Half Diseal Price";//PKONJU19
                            DetaildTraLedEnt."LOADing & OFFLOADiNG TIME COMB" := transContr."LOADING & OFFLOAd TIME COMBINE";//PKON22JA6//PKON22JA7-CR220005-2
                            DetaildTraLedEnt."Full Diseal Price" := transContr."Full Diesel Price";//PKON22JA6//PKON22JA7-CR220005-2
                            DetaildTraLedEnt."Half Diseal Price" := transContr."Half Diesel Price";//PKON22JA6//PKON22JA7-CR220005-2
                            DetaildTraLedEnt."Posted Loading Slip No." := PostLadSlpHdr."No.";
                        end;
                        //EntryNo += 1;
                        DetaildTraLedEnt.INSERT();
                        EntryNo += 10000;
                    end;
                until TraspLedEntry.NEXT() = 0;
                Message('Lines inserted succesfully.');
            end;
        end;
    end;

    procedure CreatePo();
    Var
        DetaildTraLedEnt: record "Transfer Settlement Lines";
        vendLrec: Record Vendor;
    begin
        if ("Start Date" = 0D) OR ("End Date" = 0D) then
            ERROR('Please select the Start and End dates');
        if CONFIRM('Do you want to Create Purchase Order ?', false) then begin
            DetaildTraLedEnt.reset;
            DetaildTraLedEnt.Reset();
            DetaildTraLedEnt.SetRange("Document No.", "No.");
            iF DetaildTraLedEnt.IsEmpty then
                Error('There are no lines existing for this Document.')
            else
                IF DetaildTraLedEnt.FindFirst then begin
                    IF "Transporter Cont. Type" = "Transporter Cont. Type"::BHN then
                        BHNCalCul()
                    else
                        if "Transporter Cont. Type" = "Transporter Cont. Type"::Aglevinthis then
                            AGLCalCul();
                end;
        end;
    end;


    procedure UpdateLineAmnt()
    Var
        DetaildTraLedEnt: record "Transfer Settlement Lines";
        PSLHdr: Record "Posted Loading SLip Header";
        VendorL: Record Vendor;
        VATGrp: code[20];
    begin
        DetaildTraLedEnt.reset;
        DetaildTraLedEnt.SetCurrentKey("Vendor No.");
        DetaildTraLedEnt.SetRange("Document No.", "No.");
        DetaildTraLedEnt.SetRange("Order Created", false); //24.02.2021 for testing below code
        IF DetaildTraLedEnt.FindSet() then begin
            VendorL.get(DetaildTraLedEnt."Vendor No.");
            repeat
                TestField("Inclusive VAT %");
                PSLHdr.GET(DetaildTraLedEnt."Posted Loading Slip No.");
                /*DetaildTraLedEnt."Amount(Vat)" := GETAMOUNT2VAT(VATGrp, PSLHdr);
                DetaildTraLedEnt.VATProdPosGrp := VATGrp;
                DetaildTraLedEnt."Amount(Non Vat)" := GETAMOUNT3(PSLHdr);
                DetaildTraLedEnt.Modify();*/ // PK on 27.04.2021
                DetaildTraLedEnt."Amount(Vat)" := GETAMOUNT2VAT(VATGrp, PSLHdr);
                DetaildTraLedEnt.VATProdPosGrp := VATGrp;
                DetaildTraLedEnt.Modify()
            until DetaildTraLedEnt.next = 0;
        end;
    end;

    procedure BHNCalCul();
    Var
        DetaildTraLedEnt: record "Transfer Settlement Lines";
        DetaildTraLedEnt2: record "Transfer Settlement Lines";
        PSLHdr: Record "Posted Loading SLip Header";
        VendorL: Record Vendor;
        PrevVend: Text[50];
        VATGrp: code[20];
        BHNNNVAT: Decimal;
        BHNAmnt: Decimal;
        PPSetup: Record "Purchases & Payables Setup";
        PurchaseHeaderOrder: Record "Purchase Header";
        PurchaseLineOrder: Record "Purchase Line";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        PurcPay: Record "Purchases & Payables Setup";
        TransLedArch: Record "Transport Settlement Archieve";
        TempDetaildTraLedEnt: record "Transfer Settlement Lines" temporary;
        DetaildTraLedEUpdateL: record "Transfer Settlement Lines";
    begin
        TempDetaildTraLedEnt.DeleteAll();
        PurcPay.GET();
        PurcPay.TestField("Transport Inv Account");
        DetaildTraLedEnt.reset;
        DetaildTraLedEnt.SetCurrentKey("Vendor No.");
        DetaildTraLedEnt.SetRange("Document No.", "No.");
        DetaildTraLedEnt.SetRange("Order Created", false); //24.02.2021 for testing below code
        IF DetaildTraLedEnt.FindSet() then begin
            VendorL.get(DetaildTraLedEnt."Vendor No.");
            repeat
                if PrevVend <> DetaildTraLedEnt."Vendor No." then begin
                    //Message('Vehicle No. %1', DetaildTraLedEnt."Vendor No.");
                    Clear(BHNAmnt);
                    Clear(BHNNNVAT);
                    PrevVend := DetaildTraLedEnt."Vendor No.";
                    DetaildTraLedEnt2.reset;
                    DetaildTraLedEnt2.SetCurrentKey("Vendor No.");
                    DetaildTraLedEnt2.SetRange("Document No.", "No.");
                    //DetaildTraLedEnt2.SetRange("Order Created", false);//24.02.2021 for testing
                    DetaildTraLedEnt2.SetRange("Vendor No.", DetaildTraLedEnt."Vendor No.");
                    IF DetaildTraLedEnt2.findset then begin
                        repeat
                            PSLHdr.GET(DetaildTraLedEnt2."Posted Loading Slip No.");
                            //BHNNNVAT += GETAMOUNT2VAT(VATGrp, PSLHdr);//PK ON 27.04.2021
                            //BHNAmnt += GETAMOUNT3(PSLHdr);//PK ON 27.04.2021
                            BHNNNVAT += DetaildTraLedEnt2."Amount(Vat)";
                            Message('Vendor No. %1 and vehicle No. %2 amnts %3 and %4', DetaildTraLedEnt."Vendor No.", DetaildTraLedEnt."Vehicle No",
                            BHNNNVAT, BHNAmnt);
                            TempDetaildTraLedEnt.init;
                            TempDetaildTraLedEnt."Document No." := DetaildTraLedEnt2."Document No.";
                            TempDetaildTraLedEnt."Line No." := DetaildTraLedEnt2."Line No.";
                            TempDetaildTraLedEnt.Insert();
                        until DetaildTraLedEnt2.next = 0;
                    end;
                    //Create PO
                    PPSetup.Get();
                    PurchaseHeaderOrder.INit;
                    PurchaseHeaderOrder."Document Type" := PurchaseHeaderOrder."Document Type"::Order;
                    IF VendorL."Vendor Type" = VendorL."Vendor Type"::Local then begin
                        PPSetup.TestField("Local Purchase Order");
                        PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Local Purchase Order", WORKDATE(), TRUE);
                    END else
                        IF VendorL."Vendor Type" = VendorL."Vendor Type"::Import then BEGIN
                            PPSetup.TestField("Import Purchase Order");
                            PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Import Purchase Order", WORKDATE(), TRUE);
                        END;
                    PurchaseHeaderOrder."Purchase Type" := VendorL."Vendor Type";
                    PurchaseHeaderOrder.INSERT(true);
                    PurchaseHeaderOrder."Posting Date" := WORKDATE();
                    PurchaseHeaderOrder."Document Date" := WORKDATE();
                    PurchaseHeaderOrder.VALIDATE("Buy-from Vendor No.", VendorL."No.");
                    PurchaseHeaderOrder."Expected Receipt Date" := WORKDATE();
                    PurchaseHeaderOrder."Posted Loading Slip No." := "No.";
                    PurchaseHeaderOrder.Modify();
                    /*IF BHNNNVAT <> 0 then begin
                        PurchaseLineOrder.INIT();
                        PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                        PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                        PurchaseLineOrder."Line No." := 10000;
                        PurchaseLineOrder.INSERT();

                        PurchaseLineOrder.VALIDATE("Buy-from Vendor No.", PurchaseHeaderOrder."Buy-from Vendor No.");
                        PurchaseLineOrder.Type := PurchaseLineOrder.Type::"G/L Account";
                        PurchaseLineOrder.VALIDATE("No.", PurcPay."Transport Inv Account");//B2B.P.K.T
                        PurchaseLineOrder.Description := 'Settlement for Pos. LoadSlip ' + "No.";
                        PurchaseLineOrder.VALIDATE(Quantity, 1);

                        PurchaseLineOrder."Direct Unit Cost" := BHNNNVAT;
                        PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                        PurchaseLineOrder."Location Code" := PurchaseHeaderOrder."Location Code";
                        PurchaseLineOrder.Validate("VAT Prod. Posting Group", VATGrp);
                        PurchaseLineOrder.modify;
                    end;
                    DetaildTraLedEnt."Purchase Document No." := PurchaseHeaderOrder."No.";
                    DetaildTraLedEnt."Order Created" := true;
                    DetaildTraLedEnt.Modify();
                    PSLHdr."Purchase Order No." := PurchaseLineOrder."Document No.";
                    PSLHdr.Modify();
                    IF BHNAmnt <> 0 then begin
                        PurchaseLineOrder.INIT();
                        PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                        PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                        PurchaseLineOrder."Line No." := 20000;
                        PurchaseLineOrder.INSERT();

                        PurchaseLineOrder.VALIDATE("Buy-from Vendor No.", PurchaseHeaderOrder."Buy-from Vendor No.");
                        PurchaseLineOrder.Type := PurchaseLineOrder.Type::"G/L Account";
                        PurchaseLineOrder.VALIDATE("No.", PurcPay."Transport Inv Account");//B2B.P.K.T
                        PurchaseLineOrder.Description := 'Settlement for Pos. LoadSlip ' + "No.";
                        PurchaseLineOrder.VALIDATE(Quantity, 1);

                        PurchaseLineOrder."Direct Unit Cost" := BHNAmnt;
                        PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                        PurchaseLineOrder."Location Code" := PurchaseHeaderOrder."Location Code";
                        PurchaseLineOrder.modify;
                    end;*///Pk on 27.04.2021
                    PurchaseLineOrder.INIT();
                    PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                    PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                    PurchaseLineOrder."Line No." := 10000;
                    PurchaseLineOrder.INSERT();

                    PurchaseLineOrder.VALIDATE("Buy-from Vendor No.", PurchaseHeaderOrder."Buy-from Vendor No.");
                    PurchaseLineOrder.Type := PurchaseLineOrder.Type::"G/L Account";
                    PurchaseLineOrder.VALIDATE("No.", PurcPay."Transport Inv Account");//B2B.P.K.T
                    PurchaseLineOrder.Description := 'Settlement for Pos. LoadSlip ' + "No.";
                    PurchaseLineOrder.VALIDATE(Quantity, 1);

                    PurchaseLineOrder."Direct Unit Cost" := BHNNNVAT;
                    PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                    PurchaseLineOrder."Location Code" := PurchaseHeaderOrder."Location Code";
                    PurchaseLineOrder.Validate("VAT Prod. Posting Group", VATGrp);
                    PurchaseLineOrder.modify;

                    DetaildTraLedEnt."Purchase Document No." := PurchaseHeaderOrder."No.";
                    DetaildTraLedEnt."Order Created" := true;
                    DetaildTraLedEnt.Modify();
                    PSLHdr."Purchase Order No." := PurchaseLineOrder."Document No.";
                    PSLHdr.Modify();

                    PrevVend := DetaildTraLedEnt."Vendor No.";
                    IF TempDetaildTraLedEnt.FindSet() then
                        repeat
                            DetaildTraLedEUpdateL.Init();
                            DetaildTraLedEUpdateL.SetRange("Document No.", TempDetaildTraLedEnt."Document No.");
                            DetaildTraLedEUpdateL.SetRange("Line No.", TempDetaildTraLedEnt."Line No.");
                            IF DetaildTraLedEUpdateL.FindFirst() then begin
                                DetaildTraLedEUpdateL."Document Type" := DetaildTraLedEUpdateL."Document Type"::Order;
                                DetaildTraLedEUpdateL."Purchase Document No." := PurchaseHeaderOrder."No.";
                                DetaildTraLedEUpdateL."Order Created" := true;
                                DetaildTraLedEUpdateL.Modify;
                            end;
                        until TempDetaildTraLedEnt.next = 0;
                end;
            until DetaildTraLedEnt.next = 0;
        end;
    end;

    procedure GETAMOUNT2VAT(Var GENProdPosGrp: code[20]; PostedLLS: Record "Posted Loading SLip Header"): Integer
    Var
        transContr: Record "Transport Contract Types";
        Lamt: Decimal;
        Lamt2: Decimal;
        VendorL: Record Vendor;
    begin
        /*
        VendorL.get(PostedLLS."Party No.");
        clear(Lamt);
        transContr.Reset();
        transContr.SetRange("Vendor No.", VendorL."No.");
        transContr.SetRange("Contract Type", VendorL."Transport Contract");
        transContr.Setfilter("Starting Date", '<=%1', WorkDate());
        transContr.Setfilter("Ending Date", '>=%1', WorkDate());
        transContr.SetRange(Status, transContr.status::Release);
        transContr.SetRange("From Location", PostedLLS."Transport From Location");
        transContr.SetRange("To-Location", PostedLLS."Transport To Location");
        transContr.SetRange("Non Branded Vehicle", false);
        //transContr.SetFilter(Rate, '>%1', 0);
        IF transContr.FindFirst() then BEGIN
            GENProdPosGrp := transContr."VAT Prod. Posting Group";
            IF (VendorL."Transport Contract" = VendorL."Transport Contract"::BHN) AND (PostedLLS."Invoice Based On" = PostedLLS."Invoice Based On"::Document) then begin
                IF transContr."Vat On Diesel" then
                    Lamt := transContr."New Distance" * PostedLLS."Half Diseal Price";
                IF transContr."Vat On Tyres" then
                    Lamt += transContr."New Distance" * transContr."TYRE PER KM";
                IF transContr."Vat On R&M" then
                    Lamt += transContr."New Distance" * transContr."R&M Per KM";
                IF transContr."Vat On Others" then
                    Lamt += transContr."New Distance" * transContr."Others Per KM";
                IF transContr."VAT On Fixed Cost" then
                    Lamt += (PostedLLS."LOADing & OFFLOADiNG TIME COMB" + transContr."IN-TRANSIT TIME TO & FRO") * transContr."FIXED COST PER HOUR";
                exit(ROUND(Lamt, 1));
            end;
            exit(ROUND(0));
        END else
            Error('There is no contract setup or Rate in combination Contract Type %1, Date %2, Status release, from location %3, to location %4 and vendor No %5', VendorL."Transport Contract", WorkDate(), PostedLLS."Transport From Location", PostedLLS."Transport To Location", VendorL."No.")
    */  //Prasanna On 27.04.2021
        VendorL.get(PostedLLS."Party No.");
        clear(Lamt);
        transContr.Reset();
        transContr.SetRange("Vendor No.", VendorL."No.");
        transContr.SetRange("Contract Type", VendorL."Transport Contract");
        transContr.Setfilter("Starting Date", '<=%1', WorkDate());
        transContr.Setfilter("Ending Date", '>=%1', WorkDate());
        transContr.SetRange(Status, transContr.status::Release);
        transContr.SetRange("From Location", PostedLLS."Transport From Location");
        transContr.SetRange("To-Location", PostedLLS."Transport To Location");
        transContr.SetRange("Non Branded Vehicle", false);
        //transContr.SetFilter(Rate, '>%1', 0);
        IF transContr.FindFirst() then BEGIN
            GENProdPosGrp := transContr."VAT Prod. Posting Group";
            IF (VendorL."Transport Contract" = VendorL."Transport Contract"::BHN) AND (PostedLLS."Invoice Based On" = PostedLLS."Invoice Based On"::Document) then begin
                // IF transContr."Vat On Diesel" then
                //Lamt := transContr."New Distance" * PostedLLS."Half Diseal Price";//PKONJU19
                Lamt := transContr."New Distance" * transContr."Half Diesel Price";//PKONJU19
                // IF transContr."Vat On Tyres" then
                Lamt += transContr."New Distance" * transContr."TYRE PER KM";
                // IF transContr."Vat On R&M" then
                Lamt += transContr."New Distance" * transContr."R&M Per KM";
                //IF transContr."Vat On Others" then
                Lamt += transContr."New Distance" * transContr."Others Per KM";
                //IF transContr."VAT On Fixed Cost" then
                //Lamt += (PostedLLS."LOADing & OFFLOADiNG TIME COMB" + transContr."IN-TRANSIT TIME TO & FRO") * transContr."FIXED COST PER HOUR";//PKONJU19
                Lamt += (transContr."LOADING & OFFLOAd TIME COMBINE" + transContr."IN-TRANSIT TIME TO & FRO") * transContr."FIXED COST PER HOUR";//PKONJU19
                Lamt += Lamt * ("Inclusive VAT %" / 100);
                exit(ROUND(Lamt, 1));
            end;
            exit(ROUND(0));
        END else
            Error('There is no contract setup or Rate in combination Contract Type %1, Date %2, Status release, from location %3, to location %4 and vendor No %5', VendorL."Transport Contract", WorkDate(), PostedLLS."Transport From Location", PostedLLS."Transport To Location", VendorL."No.")
    end;

    procedure GETAMOUNT3(PostedLLS: Record "Posted Loading SLip Header"): Integer
    Var
        transContr: Record "Transport Contract Types";
        Lamt: Decimal;
        VendorL: Record Vendor;
    begin
        VendorL.get(PostedLLS."Party No.");
        clear(Lamt);
        transContr.Reset();
        transContr.SetRange("Vendor No.", VendorL."No.");
        transContr.SetRange("Contract Type", VendorL."Transport Contract");
        transContr.Setfilter("Starting Date", '<=%1', WorkDate());
        transContr.Setfilter("Ending Date", '>=%1', WorkDate());
        transContr.SetRange(Status, transContr.status::Release);
        transContr.SetRange("From Location", PostedLLS."Transport From Location");
        transContr.SetRange("To-Location", PostedLLS."Transport To Location");
        transContr.SetRange("Non Branded Vehicle", false);
        IF transContr.FindFirst() then BEGIN
            IF (VendorL."Transport Contract" = VendorL."Transport Contract"::BHN) AND (PostedLLS."Invoice Based On" = PostedLLS."Invoice Based On"::Document) then begin
                IF NOT transContr."Vat On Diesel" then
                    Lamt := transContr."New Distance" * transContr."Half Diesel Price";//PKONJU19
                //Lamt := transContr."New Distance" * PostedLLS."Half Diseal Price";//PKONJU19
                IF Not transContr."Vat On Tyres" then
                    Lamt += transContr."New Distance" * transContr."TYRE PER KM";
                IF Not transContr."Vat On R&M" then
                    Lamt += transContr."New Distance" * transContr."R&M Per KM";
                IF Not transContr."Vat On Others" then
                    Lamt += transContr."New Distance" * transContr."Others Per KM";
                //Message('%1', Lamt);
                IF Not transContr."VAT On Fixed Cost" then
                    Lamt += (transContr."LOADING & OFFLOAd TIME COMBINE" + transContr."IN-TRANSIT TIME TO & FRO") * transContr."FIXED COST PER HOUR";//PKONJU19
                //Lamt += (PostedLLS."LOADing & OFFLOADiNG TIME COMB" + transContr."IN-TRANSIT TIME TO & FRO") * transContr."FIXED COST PER HOUR";//PKONJU19
                /*Message('PostedLLS."LOADing & OFFLOADiNG TIME COMB" %1..transContr."IN-TRANSIT TIME TO & FRO"...%2...transContr."FIXED COST PER HOUR" %3 and total %4',
                PostedLLS."LOADing & OFFLOADiNG TIME COMB", transContr."IN-TRANSIT TIME TO & FRO", transContr."FIXED COST PER HOUR", Lamt);*/
                exit(ROUND(Lamt, 1));
            end;
            exit(ROUND(0));
        END else
            Error('There is no contract setup or Rate in combination Contract Type %1, Date %2, Status release, from location %3, to location %4 and vendor No %5', VendorL."Transport Contract", WorkDate(), PostedLLS."Transport From Location", PostedLLS."Transport To Location", VendorL."No.")
    end;

    procedure AGLCalCul();
    Var
        DetaildTraLedEnt: record "Transfer Settlement Lines";
        DetaildTraLedEnt2: record "Transfer Settlement Lines";
        PSLHdr: Record "Posted Loading SLip Header";
        VendorL: Record Vendor;
        PrevVend: Text[50];
        VATGrp: code[20];
        BHNNNVAT: Decimal;
        BHNAmnt: Decimal;
        PPSetup: Record "Purchases & Payables Setup";
        PurchaseHeaderOrder: Record "Purchase Header";
        PurchaseLineOrder: Record "Purchase Line";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        PurcPay: Record "Purchases & Payables Setup";
        TransLedArch: Record "Transport Settlement Archieve";
        TempDetaildTraLedEnt: record "Transfer Settlement Lines" temporary;
        DetaildTraLedEUpdateL: record "Transfer Settlement Lines";
    begin
        DetaildTraLedEnt.reset;
        DetaildTraLedEnt.SetCurrentKey("Vehicle No");
        DetaildTraLedEnt.SetRange("Document No.", "No.");
        //DetaildTraLedEnt.SetRange("Order Created", false);
        IF DetaildTraLedEnt.FindSet() then begin
            VendorL.get(DetaildTraLedEnt."Vendor No.");
            repeat
                if PrevVend <> DetaildTraLedEnt."Vehicle No" then begin
                    Clear(BHNAmnt);
                    Clear(BHNNNVAT);
                    PrevVend := DetaildTraLedEnt."Vendor No.";
                    DetaildTraLedEnt2.reset;
                    DetaildTraLedEnt2.SetCurrentKey("Vendor No.");
                    DetaildTraLedEnt2.SetRange("Document No.", "No.");
                    //DetaildTraLedEnt2.SetRange("Order Created", false);
                    DetaildTraLedEnt2.SetRange("Vendor No.", DetaildTraLedEnt."Vendor No.");
                    IF DetaildTraLedEnt2.findset then begin
                        repeat
                            PSLHdr.GET(DetaildTraLedEnt2."Posted Loading Slip No.");
                            BHNNNVAT += GETAMOUNT2VAT(VATGrp, PSLHdr);
                            BHNAmnt += GETAMOUNT3(PSLHdr);
                            TempDetaildTraLedEnt.init;
                            TempDetaildTraLedEnt."Document No." := DetaildTraLedEnt2."Document No.";
                            TempDetaildTraLedEnt."Line No." := DetaildTraLedEnt2."Line No.";
                            TempDetaildTraLedEnt.Insert();
                        until DetaildTraLedEnt2.next = 0;
                    end;
                    //Create PO
                    PurchaseHeaderOrder.INit;
                    IF VendorL."Vendor Type" = VendorL."Vendor Type"::Local then begin
                        PPSetup.TestField("Local Purchase Order");
                        PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Local Purchase Order", WORKDATE(), TRUE);
                    END else
                        IF VendorL."Vendor Type" = VendorL."Vendor Type"::Import then BEGIN
                            PPSetup.TestField("Import Purchase Order");
                            PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Import Purchase Order", WORKDATE(), TRUE);
                        END;
                    PurchaseHeaderOrder.INSERT(true);
                    PurchaseHeaderOrder."Posting Date" := WORKDATE();
                    PurchaseHeaderOrder."Document Date" := WORKDATE();
                    PurchaseHeaderOrder.VALIDATE("Buy-from Vendor No.", VendorL."No.");
                    PurchaseHeaderOrder."Expected Receipt Date" := WORKDATE();
                    PurchaseHeaderOrder."Purchase Type" := VendorL."Vendor Type";
                    PurchaseHeaderOrder."Posted Loading Slip No." := "No.";
                    PurchaseHeaderOrder.Modify();
                    PurchaseLineOrder.INIT();
                    PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                    PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                    PurchaseLineOrder."Line No." := 10000;
                    PurchaseLineOrder.INSERT();
                    DetaildTraLedEnt."Purchase Document No." := PurchaseHeaderOrder."No.";
                    DetaildTraLedEnt."Order Created" := true;
                    DetaildTraLedEnt.Modify();
                    PurchaseLineOrder.VALIDATE("Buy-from Vendor No.", PurchaseHeaderOrder."Buy-from Vendor No.");
                    PurchaseLineOrder.Type := PurchaseLineOrder.Type::"G/L Account";
                    PurchaseLineOrder.VALIDATE("No.", PurcPay."Transport Inv Account");//B2B.P.K.T
                    PurchaseLineOrder.Description := 'Settlement for Pos. LoadSlip ' + "No.";
                    PurchaseLineOrder.VALIDATE(Quantity, 1);

                    PurchaseLineOrder."Direct Unit Cost" := BHNNNVAT;
                    PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                    PurchaseLineOrder."Location Code" := PurchaseHeaderOrder."Location Code";
                    PurchaseLineOrder.Validate("VAT Prod. Posting Group", VATGrp);
                    PurchaseLineOrder.modify;

                    PSLHdr."Purchase Order No." := PurchaseLineOrder."Document No.";
                    PSLHdr.Modify();

                    PrevVend := DetaildTraLedEnt."Vehicle No";

                    IF TempDetaildTraLedEnt.FindSet() then
                        repeat
                            DetaildTraLedEUpdateL.Init();
                            DetaildTraLedEUpdateL.SetRange("Document No.", TempDetaildTraLedEnt."Document No.");
                            DetaildTraLedEUpdateL.SetRange("Line No.", TempDetaildTraLedEnt."Line No.");
                            IF DetaildTraLedEUpdateL.FindFirst() then begin
                                DetaildTraLedEUpdateL."Document Type" := DetaildTraLedEUpdateL."Document Type"::Order;
                                DetaildTraLedEUpdateL."Purchase Document No." := PurchaseHeaderOrder."No.";
                                DetaildTraLedEUpdateL.Modify;
                            end;
                        until TempDetaildTraLedEnt.next = 0;
                end;
            until DetaildTraLedEnt.next = 0;
        end;


    end;

    procedure GETAMOUNT(DetaildTraLedEntLPA: record "Transfer Settlement Lines"): Integer
    Var
        transContr: Record "Transport Contract Types";
        VendorL: Record Vendor;
    begin
        /*
        VendorL.get(DetaildTraLedEntLPA."Vendor No.");
        transContr.Reset();
        transContr.SetRange("Vendor No.", VendorL."No.");
        transContr.SetRange("Contract Type", VendorL."Transport Contract");
        //transContr.Setfilter("Starting Date", '<=%1', "Purchase Order Cret. Date");
        //transContr.Setfilter("Ending Date", '>=%1', "Purchase Order Cret. Date");
        transContr.SetRange(Status, transContr.status::Release);
        transContr.SetRange("From Location", DetaildTraLedEntLPA."PLS From-Location");
        transContr.SetRange("To-Location", DetaildTraLedEntLPA."PLS To-Location");
        transContr.SetFilter(Rate, '>%1', 0);
        IF transContr.FindFirst() then BEGIN
            IF (VendorL."Transport Contract" = VendorL."Transport Contract"::BHN) AND (DetaildTraLedEntLPA."Invoice Based On" = DetaildTraLedEntLPA."Invoice Based On"::Palletwise) then
                exit(GETLInEAm(transContr.Rate))
            else
                IF (VendorL."Transport Contract" = VendorL."Transport Contract"::BHN) AND (DetaildTraLedEntLPA."Invoice Based On" = DetaildTraLedEntLPA."Invoice Based On"::Document) then
                    exit(ROUND((transContr."New Distance" * DetaildTraLedEntLPA."Half Diseal Price") + (transContr."New Distance" * transContr."Others Per KM") + (transContr.TYRES + transContr."R&M") + (transContr."FIXED COST PER HOUR" * (DetaildTraLedEntLPA."LOADing & OFFLOADiNG TIME COMB" + transContr."IN-TRANSIT TIME TO & FRO")), 1));
        END else
            Error('There is no contract setup or Rate in combination Contract Type %1, Date %2, Status release, from location %3, to location %4 and vendor No %5', VendorL."Transport Contract", "Purchase Order Cret. Date", DetaildTraLedEntLPA."PLS From-Location", DetaildTraLedEntLPA."PLS To-Location", VendorL."No.")*/ // Continue from here PK
    end;
}