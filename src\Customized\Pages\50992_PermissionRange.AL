page 50961 "Permission Range"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Permission Range";

    layout
    {
        area(Content)
        {
            Repeater(GroupName)
            {

                field("Object Type"; "Object Type")
                {
                    Applicationarea = all;
                }
                field(Index; Index)
                {
                    Applicationarea = all;
                }
                field(Form; From)
                {
                    Applicationarea = all;
                }
                field("To"; "TO")
                {
                    Applicationarea = all;
                }
                field("Read Permission"; "Read Permission")
                {
                    Applicationarea = all;
                }
                field("Insert Permission"; "Insert Permission")
                {
                    applicationarea = all;
                }
                field("Modify Permission"; "Modify Permission")
                {
                    Applicationarea = all;
                }
                field("Delete Permission"; "Delete Permission")
                {
                    Applicationarea = all;
                }
                field("Execute Permission"; "Execute Permission")
                {
                    Applicationarea = all;
                }
                field("Limited Usage Permission"; "Limited Usage Permission")
                {
                    applicationarea = all;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}