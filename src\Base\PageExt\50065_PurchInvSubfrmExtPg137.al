pageextension 50065 PurchInvSubformExt extends 139
{
    layout
    {
        addafter(Quantity)
        {
            field("Import File No."; "Import File No.")
            {
                ApplicationArea = ALL;

            }
            field("Capex No."; "Capex No.")
            {
                ApplicationArea = ALL;
            }
            field("Capex Line No."; "Capex Line No.")
            {
                ApplicationArea = ALL;
            }
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = ALL;
            }

            field("WHT Applicable"; "WHT Applicable")
            {
                ApplicationArea = ALL;
            }
        }

    }

    actions
    {
        addafter(ItemTrackingEntries)
        {
            action(TaskLines)
            {
                Image = List;
                RunObject = page "Task Codes Lines for Posting";
                RunPageLink = "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                ApplicationArea = All;
            }
        }
    }
}