page 50122 "Promo. Schedule Lines"
{
    Editable = false;
    PageType = List;
    SourceTable = "Promo Schedule Line";
    UsageCategory = Lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field(Type; Type)
                {
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field(Active; Active)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Show Document")
                {
                    ApplicationArea = all;
                    Caption = 'Show Document';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        PromoSchdGRec.GET("Document No.");
                        PAGE.RUN(PAGE::"Promo. Schedule", PromoSchdGRec);
                    end;
                }
            }
        }
    }

    var
        PromoSchdGRec: Record "Promo Schedule";
}

