/// <summary>
/// Page Treasury Payment Notification (ID 50601).
/// </summary>
/// G2S Providus Integration 7th Aug 2024 
page 50601 "Treasury Payment Notification"
{
    ApplicationArea = All;
    Caption = 'Treasury Payment Notification';
    PageType = List;
    SourceTable = "Treasury Payment Notification";
    SourceTableView = where(Status = filter('<>Successful')); //13/01/2025 CAS-01386-K2V6T3
    UsageCategory = Lists;
    DeleteAllowed = false;
    // Editable = false;
    Permissions = tabledata "Bank API Credentials" = R, tabledata "Bank Payment Status" = R, tabledata "Bank Payment Code" = R, tabledata "Bank Notifications" = RIM;


    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("Payment Voucher No."; Rec."Payment Voucher No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Payment Voucher No. field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Source Acct Name"; Rec."Source Acct Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Source Acct Name field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Beneficiary Acct. Name"; Rec."Beneficiary Acct. Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Beneficiary Acct. Name field.', Comment = '%';
                    Editable = IsNotEditable;
                    Visible = false;
                }
                field("Beneficiary Acct. No."; Rec."Beneficiary Acct. No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Beneficiary Acct. No. field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Beneficiary Bank Code"; Rec."Beneficiary Bank Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Beneficiary Bank Code field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Beneficiary Bank Name"; Rec."Beneficiary Bank Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Beneficiary Bank Name field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field(Amount; Rec.Amount)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Amount field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Currency Code field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Date Sent to Bank"; Rec."Date Sent to Bank")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Sent to Bank field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("DateTime Sent to Bank"; Rec."DateTime Sent to Bank")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the DateTime Sent to Bank field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field(Narration; Rec.Narration)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Narration field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                // field("Session ID"; Rec."Session ID")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies the value of the Session ID field.', Comment = '%';
                // Editable = IsNotEditable;
                // }
                field("Bank Status Code Desc."; "Bank Status Code Desc.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Status Code Desc. field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Status field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Transaction Reference"; Rec."Transaction Reference")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Transaction Reference field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Bank Transaction Reference"; "Bank Transaction Reference")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Transaction Reference field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Transaction Date"; Rec."Transaction Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Transaction Date field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Transaction DateTime"; Rec."Transaction DateTime")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Transaction DateTime field.', Comment = '%';
                    Editable = IsNotEditable;
                }
                field("Temp Omit"; "Temp Omit")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Temp Omit field.', Comment = '%';
                    Editable = IsIntegratedBank;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group(Providus)
            {
                action(GetProvidus)
                {
                    Caption = 'Providus Details';
                    ToolTip = 'Get Providus account details';
                    Promoted = true;
                    PromotedCategory = Process;
                    ApplicationArea = All;

                    trigger OnAction()
                    begin
                        ProvidusBankAPI.GetProvidusAccount();
                    end;
                }

                action("Send Request")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = Process;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.ProvidusAPIPayload(TreasuryPmtNotif);
                    end;
                }

                action("Transaction status")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = Process;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.GetTransactionStatus(TreasuryPmtNotif);
                    end;
                }
            }
            group(FirstBank)
            {
                Caption = 'First Bank';

                action("Validate Acct No")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.ValidateFBAcctNumber(TreasuryPmtNotif);
                    end;
                }
                action("Get Payment")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.FBGetPaymentStatus(TreasuryPmtNotif);
                    end;
                }

                action("FirstBank Transaction status")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.CreateSinglePaymentPayload(TreasuryPmtNotif, false);
                    end;
                }
                // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
                action("Resend Transaction")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then begin
                            IF TreasuryPmtNotif.Status = TreasuryPmtNotif.Status::Failed THEN
                                ProvidusBankAPI.CreateSinglePaymentPayload(TreasuryPmtNotif, True) ELSE
                                Message('Only Failed transactions can be resend current status is %1', TreasuryPmtNotif.Status);
                        end
                    end;
                }
                // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
            }

            group(ZenithBank)
            {
                Caption = 'Zenith Bank';


                action("ZenithBank SendPayment")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.CreateZenithPaymentPayload(TreasuryPmtNotif, false);
                    end;
                }

                action("ZenithBank FetchTransaction")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then
                            ProvidusBankAPI.FetchZenithPaymentRequest(TreasuryPmtNotif);
                    end;
                }
                action("Resend Zenith Transaction")
                {
                    ApplicationArea = Basic, Suite;
                    Promoted = true;
                    PromotedCategory = NEW;

                    trigger OnAction()
                    var
                        ProvidusBankAPI: Codeunit "Providus Bank API";
                        TreasuryPmtNotif: Record "Treasury Payment Notification";
                    begin
                        TreasuryPmtNotif.Reset();
                        CurrPage.SetSelectionFilter(TreasuryPmtNotif);
                        if TreasuryPmtNotif.FindSet() then begin
                            IF TreasuryPmtNotif.Status = TreasuryPmtNotif.Status::Failed THEN
                                ProvidusBankAPI.CreateZenithPaymentPayload(TreasuryPmtNotif, true) ELSE
                                Message('Only Failed transactions can be resend current status is %1', TreasuryPmtNotif.Status);
                        end
                    end;
                }
            }



        }
    }

    trigger OnOpenPage()
    BEGIN
        SetControlAppearance();
    END;

    trigger OnAfterGetRecord()
    BEGIN
        SetControlAppearance();
    END;

    var
        ProvidusBankAPI: Codeunit "Providus Bank API";
        IsIntegratedBank, IsNotEditable : Boolean;

    local procedure SetControlAppearance()
    begin
        IsNotEditable := false;
        IsIntegratedBank := True;
        CurrPage.Update(false)
    end;
}
/// //Providus Integration 7th Aug 2024 
