codeunit 50069 "Teller Capture"
{
    // version AutomationError,nibss

    TableNo = "Job Queue Entry";

    trigger OnRun();
    Var
        Usersetup: Record "User Setup";
        GLSetup: Record "General Ledger Setup";
        FASetup: Record "FA Setup";
        SalesRecvSetup: Record "Sales & Receivables Setup";
    begin
        //Postdateupdate.RUN;
        Usersetup.RESET;
        Usersetup.SETFILTER("Allow Posting To", '<>%1', 0D);
        if not Usersetup.ISEMPTY then
            Usersetup.MODIFYALL(Usersetup."Allow Posting To", DT2DATE(CurrentDateTime));

        Usersetup.RESET;
        Usersetup.SETFILTER("Allow FA Posting To", '<>%1', 0D);
        if not Usersetup.ISEMPTY then
            Usersetup.MODIFYALL("Allow FA Posting To", DT2DATE(CurrentDateTime));

        if GLSetup.GET then begin
            GLSetup."Allow Posting To" := DT2DATE(CurrentDateTime);
            GLSetup.MODIFY;
        end;

        if FASetup.GET then begin
            FASetup."Allow FA Posting To" := DT2DATE(CurrentDateTime);
            FASetup.MODIFY;
        end;

        CaptureTeller();
        COMMIT;
        NibssNotErrorLog.DELETEALL;
        NibssNotifications2.RESET;
        NibssNotifications2.SETRANGE(Processed, 0);
        IF NibssNotifications2.FINDSET THEN
            REPEAT
                IF CheckNibssLine(NibssNotifications2) THEN
                    PostTellers(NibssNotifications2)
            UNTIL NibssNotifications2.NEXT = 0;
        deleteProcessedlog;


        //Inter Switch
        SalesRecvSetup.Get();
        if SalesRecvSetup."Enable InterSwitch" then begin
            CaptureInterSwitch();
            COMMIT;
            InterSwitchErrorLog.DELETEALL;
            InterSwitchNotification2.RESET;
            InterSwitchNotification2.SETRANGE(Processed, 0);
            IF InterSwitchNotification2.FINDSET THEN
                REPEAT
                    IF CheckInterSwitchLine(InterSwitchNotification2) THEN
                        PostInterSwitch(InterSwitchNotification2)
                UNTIL InterSwitchNotification2.NEXT = 0;
            deleteInterSwitchProcessedlog();
        end;
    end;

    var
        NibssNotifications2: Record "Nibss Notifications";
        CustRec: Record Customer;
        GLSetup: Record "General Ledger Setup";
        Postdateupdate: Codeunit "Allow Posting Control Manual";
        NibssNotErrorLog: Record "Nibss Notifications Error Log";
        Success: Boolean;
        InterSwitchNotification2: Record "InterSwitch Notifications";
        InterSwitchErrorLog: Record "InterSwitch Notif Error Log";

    procedure CaptureTeller()
    var
        SQLDataSet: DotNet SQLDataSet;
        SQLDataAdapter: DotNet SQLDataAdapter;
        SQLConnection: DotNet SqlConnection;
        SQLConnectionString: Text;
        SQLQuery: Text;
        SQLQuery2: Text;
        SQLCommand: DotNet SqlCommand;
        SQLReader: DotNet SqlReader;
        NibsNotification: Record "Nibss Notifications";
        TempNibsNotification: Record "Nibss Notifications" temporary;
        DecimalText1: Text;
        DecimalText2: Text;
        DecimalText3: Text;
    begin
        TempNibsNotification.DeleteAll();
        //connection using authentication with username & password
        //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
        //Con.Open('DRIVER=SQL Server;UID=nibbss;Password=**********;DATABASE=biller2;Trusted_Connection=Yes;SERVER=192.168.1.106');
        SQLConnectionString := 'Server=192.168.1.106;Database=biller2;User Id=nibbss;Password=**********;';
        SqlConnection := SqlConnection.SqlConnection(SQLConnectionString);
        SQLConnection.Open();
        // connection using windows authentication 
        //SQLConnection := 'Server=b2bsrv-308;Database=nibstest;Trusted_Connection=True;'; 

        // sql query 
        SQLQuery := 'SELECT * FROM Notifications where [Processed]=0'; //PJ
        //SQLQuery := 'update Notifications SET customerName = Palla where [ID] = 1';
        SQLCommand := SQLCommand.SqlCommand(SQLQuery, SqlConnection);
        SQLCommand.CommandTimeout(0);
        SQLReader := SQLCommand.ExecuteReader();

        // Reading each record using SQL Reader and inserting each record in BC table
        IF SQLReader.HasRows THEN BEGIN
            WHILE SQLReader.Read DO BEGIN
                CLear(DecimalText1);
                CLear(DecimalText2);
                CLear(DecimalText3);
                NibsNotification.Init();
                NibsNotification.id := SQLReader.GetInt32(0);
                NibsNotification.sessionID := DELCHR(SQLReader.GetString(1), '<>');
                NibsNotification.sourceBankCode := SQLReader.GetString(2);
                NibsNotification.channelCode := SQLReader.GetInt32(3);
                NibsNotification.customerName := DELCHR(SQLReader.GetString(4), '<>');
                NibsNotification.CustomerAccountNumber := SQLReader.GetString(5);
                DecimalText1 := SQLReader.GetString(6);
                Evaluate(NibsNotification.amount, DecimalText1);
                DecimalText2 := SQLReader.GetString(7);
                Evaluate(NibsNotification.totalAmount, DecimalText2);
                DecimalText3 := SQLReader.GetString(8);
                Evaluate(NibsNotification.fee, DecimalText3);
                NibsNotification.transactionFeeBearer := DELCHR(SQLReader.GetString(9), '<>');
                NibsNotification.splitType := DELCHR(SQLReader.GetString(10), '<>');
                NibsNotification.destinationBankCode := DELCHR(SQLReader.GetString(11), '<>');
                NibsNotification.narration := DELCHR(SQLReader.GetString(12), '<>');
                NibsNotification.PaymentReference := DELCHR(SQLReader.GetString(13), '<>');
                NibsNotification.transactionInitiatedDate := SQLReader.GetString(14);
                NibsNotification.transactionApprovalDate := SQLReader.GetString(15);
                NibsNotification.dateEntered := SQLReader.GetDateTime(16);
                NibsNotification.dateUpdated := SQLReader.GetDateTime(17);
                NibsNotification.Processed := SQLReader.GetInt32(18);
                NibsNotification.sourceBankName := DELCHR(SQLReader.GetString(19), '<>');
                NibsNotification.Customercode := DELCHR(SQLReader.GetString(20), '<>');
                NibsNotification.Insert();
                //Inserting records in temporary table and use them to update to third party table 
                TempNibsNotification.Init();
                TempNibsNotification.Id := SQLReader.GetInt32(0);
                TempNibsNotification.Insert();
            end;
        end;

        // Closing SQL Connection
        IF SqlConnection.State = 1 THEN
            SqlConnection.Close;

        // Updating third party table
        IF TempNibsNotification.FindSet() then
            repeat
                SQLQuery2 := 'update Notifications SET [Processed]=1 where [ID] =' + format(TempNibsNotification.id);
                SQLDataAdapter := SQLDataAdapter.SqlDataAdapter(SQLQuery2, SQLConnection);
                SQLDataSet := SQLDataSet.DataSet();
                SQLDataAdapter.Fill(SQLDataSet);
            until TempNibsNotification.Next = 0;
    end;

    procedure PostTellers(var Rec: Record "Nibss Notifications");
    var
        GenJnlLine: Record "Gen. Journal Line";
        LineNo: Integer;
        NewDocNo: Code[50];
        PostBankReceipt: Codeunit "Gen. Jnl.-Post Line";
        GenJnlBat: Record "Gen. Journal Batch";
        GenJnlTemp: Record "Gen. Journal Template";
        noseriesmgt: Codeunit NoSeriesManagement;
        DOcNo: Code[50];
        NibssRec: Record "Nibss Notifications";
        GenJnlLine2: Record "Gen. Journal Line";
        DimMgt: Codeunit DimensionManagement;
        GenJnlLine5: Record "Gen. Journal Line";
        custrec: Record Customer;
        CustSalesPriceDiscount: Record "Customer Sales Price/Discount";
        OnlineBankRec: Record "Nibss Notifications" temporary;
        cust: Record Customer;
        CustRespC: Record "Customer Resp. Cent. Lines";
    begin
        OnlineBankRec := Rec;
        LineNo := 10000;
        WITH OnlineBankRec DO BEGIN
            DOcNo := '';
            GLSetup.GET;

            GenJnlTemp.SETRANGE(Name, GLSetup."Online Payment Template");
            IF GenJnlTemp.ISEMPTY THEN BEGIN
                GenJnlTemp.INIT;
                GenJnlTemp.Name := GLSetup."Online Payment Template";
                GenJnlTemp.INSERT;
            END ELSE BEGIN
                IF GenJnlTemp.FINDFIRST THEN;
            END;

            GenJnlBat.RESET;
            GenJnlBat.SETRANGE("Journal Template Name", GLSetup."Online Payment Template");
            GenJnlBat.SETRANGE(Name, GLSetup."Online Payment Batch Name");
            IF GenJnlBat.ISEMPTY THEN BEGIN
                GenJnlBat.INIT;
                GenJnlBat.VALIDATE("Journal Template Name", GLSetup."Online Payment Template");
                GenJnlBat.Name := GLSetup."Online Payment Batch Name";
                GenJnlBat.Description := 'Auto Teller receipt';
                GenJnlBat.INSERT(TRUE);
            END ELSE BEGIN
                GenJnlBat.FINDFIRST;
            END;
            DOcNo := noseriesmgt.GetNextNo(GenJnlTemp."No. Series", TODAY, TRUE);
            GenJnlLine.Reset();
            GenJnlLine.SetRange("Journal Template Name", GLSetup."Online Payment Template");
            GenJnlLine.SetRange("Journal Batch Name", GLSetup."Online Payment Batch Name");
            IF GenJnlLine.FindLast() then
                GenJnlLine."Line No." := GenJnlLine."Line No." + LineNo
            Else
                GenJnlLine."Line No." := LineNo;
            GenJnlLine."Journal Template Name" := GLSetup."Online Payment Template";
            GenJnlLine."Journal Batch Name" := GLSetup."Online Payment Batch Name";
            GenJnlLine.VALIDATE("Account Type", 1);
            GenJnlLine."Document Type" := GenJnlLine."Document Type"::Payment;
            GenJnlLine."Document No." := DOcNo;
            GenJnlLine.VALIDATE("Account No.", Customercode);

            //GET Cust. Accounting Location
            IF cust.GET(Customercode) THEN BEGIN
                cust.CALCFIELDS("Accounting Location");
                IF GenJnlLine."Shortcut Dimension 1 Code" = '' THEN
                    GenJnlLine.VALIDATE("Shortcut Dimension 1 Code", cust."Accounting Location");
                IF GenJnlLine."Shortcut Dimension 2 Code" = '' THEN
                    GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", 'SLBR'); //cust."Global Dimension 2 Code");
            END;

            //Getting Cust Responsibility Center
            CustRespC.SETRANGE(CustRespC."Customer No.", Customercode);
            IF CustRespC.FINDFIRST THEN
                GenJnlLine.VALIDATE("Responsibility Center", CustRespC."Resp. Center Code");

            GenJnlLine.VALIDATE("Posting Date", DT2DATE(dateUpdated));
            GenJnlLine.VALIDATE(Amount, -amount);
            GenJnlLine.VALIDATE(Narration, COPYSTR(sessionID, 1, 100));
            GenJnlLine.Cleared := TRUE;
            GenJnlLine."Online Bank Entry" := TRUE;
            GenJnlLine.INSERT;

            //insert bank
            GenJnlLine.Reset();
            GenJnlLine.SetRange("Journal Template Name", GLSetup."Online Payment Template");
            GenJnlLine.SetRange("Journal Batch Name", GLSetup."Online Payment Batch Name");
            IF GenJnlLine.FindLast() then
                GenJnlLine."Line No." := GenJnlLine."Line No." + LineNo
            else
                GenJnlLine."Line No." := LineNo;
            GenJnlLine."Journal Template Name" := GLSetup."Online Payment Template";
            GenJnlLine."Journal Batch Name" := GLSetup."Online Payment Batch Name";
            GenJnlLine.VALIDATE("Account Type", 3);
            GenJnlLine."Document Type" := GenJnlLine."Document Type"::Payment;
            GenJnlLine."Document No." := DOcNo;
            //PKONJ18.3
            IF OnlineBankRec.destinationBankCode = '057' THEN
                GenJnlLine.VALIDATE("Account No.", 'ZIL162')
            ELSE
                IF OnlineBankRec.destinationBankCode = '033' THEN
                    GenJnlLine.VALIDATE("Account No.", 'UBA0038')
                else
                    GenJnlLine.VALIDATE("Account No.", 'CITI131');
            //PKONJ18.3
            GenJnlLine.VALIDATE("Shortcut Dimension 1 Code", cust."Accounting Location");
            GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", 'FABK');
            GenJnlLine.VALIDATE("Posting Date", DT2DATE(dateUpdated));
            GenJnlLine.VALIDATE(Amount, amount);
            GenJnlLine.VALIDATE(Narration, COPYSTR(sessionID, 1, 100));
            //GenJnlLine.Cleared := TRUE;
            GenJnlLine."Online Bank Entry" := TRUE;
            GenJnlLine.INSERT;

            //post the batch
            GenJnlLine2.RESET;
            GenJnlLine2.SETRANGE("Document No.", DOcNo);
            IF GenJnlLine2.FINDSET THEN
                REPEAT
                    PostBankReceipt.RunWithCheck(GenJnlLine2);
                UNTIL GenJnlLine2.NEXT = 0;

            NibssRec.SETRANGE(id, id);
            IF NibssRec.FINDSET THEN BEGIN
                NibssRec.Processed := 1;
                NibssRec.Remark := 'Success';
                NibssRec.MODIFY;
            END;
        END;

    end;

    procedure CheckNibssLine(var Rec: Record "Nibss Notifications"): Boolean;
    var
        ErrorExist: Boolean;
        cust: Record Customer;
        NibssRec: Record "Nibss Notifications";
        CustRespC: Record "Customer Resp. Cent. Lines";
        NibssHRec: Record "Nibss Notifications History";
    begin
        Success := TRUE;
        //Check History Table to fix bug SAA 10/11/2020 >>
        NibssHRec.SETCURRENTKEY(sessionID);
        NibssHRec.SETRANGE(sessionID, Rec.sessionID);
        NibssHRec.SETRANGE(Processed, 1);
        IF NOT NibssHRec.ISEMPTY THEN BEGIN
            CreateLog(Rec.id, 'Session ID ' + Rec.sessionID + '   duplicate');
            Success := FALSE;
            EXIT(Success);
        END;

        //Check History Table to fix bug SAA 10/11/2020 <<


        NibssRec.SETCURRENTKEY(sessionID);
        NibssRec.SETRANGE(sessionID, Rec.sessionID);
        NibssRec.SETRANGE(Processed, 1);
        IF NOT NibssRec.ISEMPTY THEN BEGIN
            CreateLog(Rec.id, 'Session ID ' + Rec.sessionID + '   duplicate');
            Success := FALSE;
            //  SendLogAlert.SendNIBSSerrorlogalert(rec);
            EXIT(Success);
        END;
        //03032020CRF:2020-04 <<
        IF NOT CustRec.GET(NibssNotifications2.Customercode) THEN BEGIN
            CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' does not Exist');
            Success := FALSE;
            EXIT(Success);
        END ELSE
            CustRec.CALCFIELDS("Accounting Location");

        IF CustRec.GET(Rec.Customercode) THEN
            IF CustRec."Approval Status" <> CustRec."Approval Status"::Released THEN BEGIN
                CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' is Not Active');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        IF CustRec.GET(Rec.Customercode) THEN
            IF CustRec."Gen. Bus. Posting Group" = '' THEN BEGIN
                CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' Gen. Bus. Posting Group is not available');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        IF CustRec.GET(Rec.Customercode) THEN
            IF CustRec."Customer Posting Group" = '' THEN BEGIN
                CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' Customer Posting Group is not available');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        /*IF CustRec.GET(Rec.Customercode) THEN
            IF CustRec."Payment Terms Code" = '' THEN BEGIN
                CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' Payment Terms Code is not available');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location"); */

        IF CustRec.GET(Rec.Customercode) THEN
            IF CustRec.Blocked = CustRec.Blocked::All THEN BEGIN
                CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' is blocked');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        //CustRec.CALCFIELDS("Accounting Location");
        IF CustRec."Global Dimension 1 Code" = '' THEN BEGIN
            CreateLog(Rec.id, 'Customer ' + CustRec."No." + ' Accounting Location is blank');
            Success := FALSE;
        END;

        /*CustRec.CALCFIELDS("Accounting Location");
        IF CustRec."Accounting Location" = '' THEN BEGIN
            CreateLog(Rec.id, 'Customer ' + CustRec."No." + ' Accounting Location is blank');
            Success := FALSE;
        END;*/

        CustRespC.SETRANGE("Customer No.", Rec.Customercode);
        IF CustRespC.ISEMPTY THEN BEGIN
            CreateLog(Rec.id, 'Customer ' + CustRec."No." + ' responsibility center is blank');
            Success := FALSE;
        END;
        EXIT(Success);
    end;

    procedure CreateLog(transactionid: Integer; errortext: Text[1024]);
    var
        Nibss: Record "Nibss Notifications";
        LogNibss: Record "Nibss Notifications Error Log";
        I: Integer;
    begin
        CLEAR(I);
        LogNibss.RESET;
        LogNibss.SETRANGE(id, transactionid);
        IF LogNibss.FINDLAST THEN
            I := LogNibss."Error Log No."
        ELSE
            I := 0;

        IF Nibss.GET(transactionid) THEN BEGIN
            LogNibss.INIT;
            LogNibss.TRANSFERFIELDS(Nibss);
            LogNibss."Error Log No." := I + 1;
            LogNibss.Remark := errortext;
            LogNibss.INSERT;
            Nibss.Remark := 'Error while Posting';
            Nibss.MODIFY;
        END;
    end;

    procedure deleteProcessedlog();
    var
        Nibss: Record "Nibss Notifications";
        LogNibss: Record "Nibss Notifications Error Log";
    begin
        IF LogNibss.FINDFIRST THEN
            REPEAT
                IF Nibss.GET(LogNibss.id) THEN
                    IF Nibss.Remark = 'Success' THEN
                        LogNibss.DELETE;
            UNTIL LogNibss.NEXT = 0;
    end;

    procedure CaptureInterSwitch()
    var
        SQLDataSet: DotNet SQLDataSet;
        SQLDataAdapter: DotNet SQLDataAdapter;
        SQLConnection: DotNet SqlConnection;
        SQLConnectionString: Text;
        SQLQuery: Text;
        SQLQuery2: Text;
        SQLCommand: DotNet SqlCommand;
        SQLReader: DotNet SqlReader;
        InterSwitchNotification: Record "InterSwitch Notifications";
        TempInterSwitchNotification: Record "InterSwitch Notifications" temporary;
        DecimalText1: Text;
        DecimalText2: Text;
        DecimalText3: Text;
    begin
        TempInterSwitchNotification.DeleteAll();
        //connection using authentication with username & password
        //SQLConnectionString := 'Server=b2bsrv-308;Database=Nibstest;User Id=sa;Password=***********;';
        //Con.Open('DRIVER=SQL Server;UID=nibbss;Password=**********;DATABASE=biller2;Trusted_Connection=Yes;SERVER=192.168.1.106');
        SQLConnectionString := 'Server=192.168.1.70;Database=biller;User Id=INTERSWITCH;Password=*****;';
        SqlConnection := SqlConnection.SqlConnection(SQLConnectionString);
        SQLConnection.Open();
        // connection using windows authentication 
        //SQLConnection := 'Server=b2bsrv-308;Database=nibstest;Trusted_Connection=True;'; 

        // sql query 
        SQLQuery := 'SELECT * FROM payments where [Processed]=0'; //PJ
        //SQLQuery := 'update Notifications SET customerName = Palla where [ID] = 1';
        SQLCommand := SQLCommand.SqlCommand(SQLQuery, SqlConnection);
        SQLCommand.CommandTimeout(0);
        SQLReader := SQLCommand.ExecuteReader();

        // Reading each record using SQL Reader and inserting each record in BC table
        IF SQLReader.HasRows THEN BEGIN
            WHILE SQLReader.Read DO BEGIN
                CLear(DecimalText1);
                CLear(DecimalText2);
                CLear(DecimalText3);
                InterSwitchNotification.Init();
                InterSwitchNotification.id := SQLReader.GetInt32(0);
                InterSwitchNotification.ProductGroupCode := DELCHR(SQLReader.GetString(1), '<>');
                InterSwitchNotification.PaymentLogId := SQLReader.GetString(2);
                InterSwitchNotification.CustReference := SQLReader.GetString(3);
                InterSwitchNotification.AlternateCustReference := DELCHR(SQLReader.GetString(4), '<>');
                DecimalText1 := SQLReader.GetString(5);
                Evaluate(InterSwitchNotification.amount, DecimalText1);
                InterSwitchNotification.PaymentStatus := DELCHR(SQLReader.GetString(6), '<>');
                InterSwitchNotification.PaymentMethod := DELCHR(SQLReader.GetString(7), '<>');
                InterSwitchNotification.PaymentReference := DELCHR(SQLReader.GetString(8), '<>');
                InterSwitchNotification.ChannelName := DELCHR(SQLReader.GetString(9), '<>');
                InterSwitchNotification.Location := DELCHR(SQLReader.GetString(10), '<>');
                InterSwitchNotification.IsReversal := SQLReader.GetString(11);
                Evaluate(InterSwitchNotification.PaymentDate, SQLReader.GetString(12));
                Evaluate(InterSwitchNotification.SettlementDate, SQLReader.GetString(13));
                InterSwitchNotification.InstitutionId := DELCHR(SQLReader.GetString(14), '<>');
                InterSwitchNotification.InstitutionName := DELCHR(SQLReader.GetString(15), '<>');
                InterSwitchNotification.BranchName := DELCHR(SQLReader.GetString(16), '<>');
                InterSwitchNotification.BankName := DELCHR(SQLReader.GetString(17), '<>');
                InterSwitchNotification.CustomerName := DELCHR(SQLReader.GetString(18), '<>');
                InterSwitchNotification.OtherCustomerInfo := DELCHR(SQLReader.GetString(19), '<>');
                InterSwitchNotification.ReceiptNo := DELCHR(SQLReader.GetString(20), '<>');
                InterSwitchNotification.CollectionsAccount := DELCHR(SQLReader.GetString(21), '<>');
                InterSwitchNotification.BankCode := DELCHR(SQLReader.GetString(22), '<>');
                InterSwitchNotification.DepositSlipNumber := DELCHR(SQLReader.GetString(23), '<>');
                InterSwitchNotification.PaymentCurrency := DELCHR(SQLReader.GetString(24), '<>');
                InterSwitchNotification.OriginalPaymentLogId := DELCHR(SQLReader.GetString(25), '<>');
                InterSwitchNotification.OriginalPaymentReference := DELCHR(SQLReader.GetString(26), '<>');
                InterSwitchNotification.Teller := DELCHR(SQLReader.GetString(27), '<>');
                InterSwitchNotification.dateEntered := SQLReader.GetDateTime(28);
                InterSwitchNotification.dateUpdated := SQLReader.GetDateTime(29);
                InterSwitchNotification.Processed := SQLReader.GetInt32(30);
                InterSwitchNotification.Insert();
                //Inserting records in temporary table and use them to update to third party table 
                TempInterSwitchNotification.Init();
                TempInterSwitchNotification.Id := SQLReader.GetInt32(0);
                TempInterSwitchNotification.Insert();
            end;
        end;

        // Closing SQL Connection
        IF SqlConnection.State = 1 THEN
            SqlConnection.Close;

        // Updating third party table
        IF TempInterSwitchNotification.FindSet() then
            repeat
                SQLQuery2 := 'update payments SET [Processed]=1 where [ID] =' + format(TempInterSwitchNotification.id);
                SQLDataAdapter := SQLDataAdapter.SqlDataAdapter(SQLQuery2, SQLConnection);
                SQLDataSet := SQLDataSet.DataSet();
                SQLDataAdapter.Fill(SQLDataSet);
            until TempInterSwitchNotification.Next = 0;
    end;

    procedure CheckInterSwitchLine(var Rec: Record "InterSwitch Notifications"): Boolean;
    var
        ErrorExist: Boolean;
        cust: Record Customer;
        NibssRec: Record "InterSwitch Notifications";
        CustRespC: Record "Customer Resp. Cent. Lines";
        NibssHRec: Record "InterSwitch Notifi History";
    begin
        Success := TRUE;
        //Check History Table to fix bug SAA 10/11/2020 >>
        /*NibssHRec.SETCURRENTKEY(sessionID);
        NibssHRec.SETRANGE(sessionID, Rec.sessionID);
        NibssHRec.SETRANGE(Processed, 1);
        IF NOT NibssHRec.ISEMPTY THEN BEGIN
            CreateLog(Rec.id, 'Session ID ' + Rec.sessionID + '   duplicate');
            Success := FALSE;
            EXIT(Success);
        END;*/

        //Check History Table to fix bug SAA 10/11/2020 <<


        NibssRec.SETCURRENTKEY(id);
        NibssRec.SETRANGE(PaymentLogId, Rec.PaymentLogId);
        NibssRec.SetRange(PaymentReference, Rec.PaymentReference);
        NibssRec.SetRange(ReceiptNo, Rec.ReceiptNo);
        NibssRec.SETRANGE(Processed, 1);
        IF NOT NibssRec.ISEMPTY THEN BEGIN
            CreateInterSwitchLog(Rec.id, 'Session ID ' + Rec.PaymentLogId + '   duplicate');
            Success := FALSE;
            //  SendLogAlert.SendNIBSSerrorlogalert(rec);
            EXIT(Success);
        END;
        //03032020CRF:2020-04 <<
        IF NOT CustRec.GET(Rec.CustReference) THEN BEGIN
            CreateInterSwitchLog(Rec.id, 'Customer ' + Rec.CustomerName + ' does not Exist');
            Success := FALSE;
            EXIT(Success);
        END ELSE
            CustRec.CALCFIELDS("Accounting Location");

        IF CustRec.GET(Rec.CustReference) THEN
            IF CustRec."Approval Status" <> CustRec."Approval Status"::Released THEN BEGIN
                CreateInterSwitchLog(Rec.id, 'Customer ' + Rec.CustomerName + ' is Not Active');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        IF CustRec.GET(Rec.CustReference) THEN
            IF CustRec."Gen. Bus. Posting Group" = '' THEN BEGIN
                CreateInterSwitchLog(Rec.id, 'Customer ' + Rec.CustomerName + ' Gen. Bus. Posting Group is not available');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        IF CustRec.GET(Rec.CustReference) THEN
            IF CustRec."Customer Posting Group" = '' THEN BEGIN
                CreateInterSwitchLog(Rec.id, 'Customer ' + Rec.CustomerName + ' Customer Posting Group is not available');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        /*IF CustRec.GET(Rec.Customercode) THEN
            IF CustRec."Payment Terms Code" = '' THEN BEGIN
                CreateLog(Rec.id, 'Customer ' + Rec.Customercode + ' Payment Terms Code is not available');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location"); */

        IF CustRec.GET(Rec.CustReference) THEN
            IF CustRec.Blocked = CustRec.Blocked::All THEN BEGIN
                CreateInterSwitchLog(Rec.id, 'Customer ' + Rec.CustomerName + ' is blocked');
                Success := FALSE;
            END ELSE
                CustRec.CALCFIELDS("Accounting Location");

        //CustRec.CALCFIELDS("Accounting Location");
        IF CustRec."Global Dimension 1 Code" = '' THEN BEGIN
            CreateInterSwitchLog(Rec.id, 'Customer ' + CustRec."No." + ' Accounting Location is blank');
            Success := FALSE;
        END;

        /*CustRec.CALCFIELDS("Accounting Location");
        IF CustRec."Accounting Location" = '' THEN BEGIN
            CreateLog(Rec.id, 'Customer ' + CustRec."No." + ' Accounting Location is blank');
            Success := FALSE;
        END;*/

        CustRespC.SETRANGE("Customer No.", Rec.CustReference);
        IF CustRespC.ISEMPTY THEN BEGIN
            CreateInterSwitchLog(Rec.id, 'Customer ' + CustRec."No." + ' responsibility center is blank');
            Success := FALSE;
        END;
        EXIT(Success);
    end;

    procedure CreateInterSwitchLog(transactionid: Integer; errortext: Text[1024]);
    var
        Nibss: Record "InterSwitch Notifications";
        LogNibss: Record "InterSwitch Notif Error Log";
        I: Integer;
    begin
        CLEAR(I);
        LogNibss.RESET;
        LogNibss.SETRANGE(id, transactionid);
        IF LogNibss.FINDLAST THEN
            I := LogNibss."Error Log No."
        ELSE
            I := 0;

        IF Nibss.GET(transactionid) THEN BEGIN
            LogNibss.INIT;
            LogNibss.TRANSFERFIELDS(Nibss);
            LogNibss."Error Log No." := I + 1;
            LogNibss.Remark := errortext;
            LogNibss.INSERT;
            Nibss.Remark := 'Error while Posting';
            Nibss.MODIFY;
        END;
    end;

    procedure deleteInterSwitchProcessedlog();
    var
        Nibss: Record "InterSwitch Notifications";
        LogNibss: Record "InterSwitch Notif Error Log";
    begin
        IF LogNibss.FINDFIRST THEN
            REPEAT
                IF Nibss.GET(LogNibss.id) THEN
                    IF Nibss.Remark = 'Success' THEN
                        LogNibss.DELETE;
            UNTIL LogNibss.NEXT = 0;
    end;

    procedure PostInterSwitch(var Rec: Record "InterSwitch Notifications");
    var
        GenJnlLine: Record "Gen. Journal Line";
        LineNo: Integer;
        NewDocNo: Code[50];
        PostBankReceipt: Codeunit "Gen. Jnl.-Post Line";
        GenJnlBat: Record "Gen. Journal Batch";
        GenJnlTemp: Record "Gen. Journal Template";
        noseriesmgt: Codeunit NoSeriesManagement;
        DOcNo: Code[50];
        NibssRec: Record "InterSwitch Notifications";
        GenJnlLine2: Record "Gen. Journal Line";
        DimMgt: Codeunit DimensionManagement;
        GenJnlLine5: Record "Gen. Journal Line";
        custrec: Record Customer;
        CustSalesPriceDiscount: Record "Customer Sales Price/Discount";
        OnlineBankRec: Record "InterSwitch Notifications" temporary;
        cust: Record Customer;
        CustRespC: Record "Customer Resp. Cent. Lines";
    begin
        OnlineBankRec := Rec;
        LineNo := 10000;
        WITH OnlineBankRec DO BEGIN
            DOcNo := '';
            GLSetup.GET;

            GenJnlTemp.SETRANGE(Name, GLSetup."Online Payment Template");
            IF GenJnlTemp.ISEMPTY THEN BEGIN
                GenJnlTemp.INIT;
                GenJnlTemp.Name := GLSetup."Online Payment Template";
                GenJnlTemp.INSERT;
            END ELSE BEGIN
                IF GenJnlTemp.FINDFIRST THEN;
            END;

            GenJnlBat.RESET;
            GenJnlBat.SETRANGE("Journal Template Name", GLSetup."Online Payment Template");
            GenJnlBat.SETRANGE(Name, GLSetup."Online Payment Batch Name");
            IF GenJnlBat.ISEMPTY THEN BEGIN
                GenJnlBat.INIT;
                GenJnlBat.VALIDATE("Journal Template Name", GLSetup."Online Payment Template");
                GenJnlBat.Name := GLSetup."Online Payment Batch Name";
                GenJnlBat.Description := 'Auto Inter Switch receipt';
                GenJnlBat.INSERT(TRUE);
            END ELSE BEGIN
                GenJnlBat.FINDFIRST;
            END;
            DOcNo := noseriesmgt.GetNextNo(GenJnlTemp."No. Series", TODAY, TRUE);
            GenJnlLine.Reset();
            GenJnlLine.SetRange("Journal Template Name", GLSetup."Online Payment Template");
            GenJnlLine.SetRange("Journal Batch Name", GLSetup."Online Payment Batch Name");
            IF GenJnlLine.FindLast() then
                GenJnlLine."Line No." := GenJnlLine."Line No." + LineNo
            Else
                GenJnlLine."Line No." := LineNo;
            GenJnlLine."Journal Template Name" := GLSetup."Online Payment Template";
            GenJnlLine."Journal Batch Name" := GLSetup."Online Payment Batch Name";
            GenJnlLine.VALIDATE("Account Type", 1);
            GenJnlLine."Document Type" := GenJnlLine."Document Type"::Payment;
            GenJnlLine."Document No." := DOcNo;
            GenJnlLine.VALIDATE("Account No.", CustReference);

            //GET Cust. Accounting Location
            IF cust.GET(CustReference) THEN BEGIN
                cust.CALCFIELDS("Accounting Location");
                IF GenJnlLine."Shortcut Dimension 1 Code" = '' THEN
                    GenJnlLine.VALIDATE("Shortcut Dimension 1 Code", cust."Accounting Location");
                IF GenJnlLine."Shortcut Dimension 2 Code" = '' THEN
                    GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", 'SLBR'); //cust."Global Dimension 2 Code");
            END;

            //Getting Cust Responsibility Center
            CustRespC.SETRANGE(CustRespC."Customer No.", CustReference);
            IF CustRespC.FINDFIRST THEN
                GenJnlLine.VALIDATE("Responsibility Center", CustRespC."Resp. Center Code");

            GenJnlLine.VALIDATE("Posting Date", DT2Date(PaymentDate));
            GenJnlLine.VALIDATE(Amount, -amount);
            GenJnlLine.VALIDATE(Narration, COPYSTR(PaymentLogId, 1, 100));
            GenJnlLine.Cleared := TRUE;
            GenJnlLine."Online Bank Entry" := TRUE;
            GenJnlLine."Receipt Document No." := ReceiptNo;
            GenJnlLine.INSERT;

            //insert bank
            GenJnlLine.Reset();
            GenJnlLine.SetRange("Journal Template Name", GLSetup."Online Payment Template");
            GenJnlLine.SetRange("Journal Batch Name", GLSetup."Online Payment Batch Name");
            IF GenJnlLine.FindLast() then
                GenJnlLine."Line No." := GenJnlLine."Line No." + LineNo
            else
                GenJnlLine."Line No." := LineNo;
            GenJnlLine."Journal Template Name" := GLSetup."Online Payment Template";
            GenJnlLine."Journal Batch Name" := GLSetup."Online Payment Batch Name";
            GenJnlLine.VALIDATE("Account Type", 3);
            GenJnlLine."Document Type" := GenJnlLine."Document Type"::Payment;
            GenJnlLine."Document No." := DOcNo;
            IF OnlineBankRec.BankCode = 'ZIB' THEN
                GenJnlLine.VALIDATE("Account No.", 'ZIL163') ELSE
                GenJnlLine.VALIDATE("Account No.", 'ACC012');

            GenJnlLine.VALIDATE("Shortcut Dimension 1 Code", cust."Accounting Location");
            GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", 'FABK');
            GenJnlLine.VALIDATE("Posting Date", DT2DATE(dateUpdated));
            GenJnlLine.VALIDATE(Amount, amount);
            GenJnlLine.VALIDATE(Narration, COPYSTR(PaymentLogId, 1, 100));
            //GenJnlLine.Cleared := TRUE;
            GenJnlLine."Online Bank Entry" := TRUE;
            GenJnlLine."Receipt Document No." := ReceiptNo;
            GenJnlLine.INSERT;

            //post the batch
            GenJnlLine2.RESET;
            GenJnlLine2.SETRANGE("Document No.", DOcNo);
            IF GenJnlLine2.FINDSET THEN
                REPEAT
                    PostBankReceipt.RunWithCheck(GenJnlLine2);
                UNTIL GenJnlLine2.NEXT = 0;

            NibssRec.SETRANGE(id, id);
            IF NibssRec.FINDSET THEN BEGIN
                NibssRec.Processed := 1;
                NibssRec.Remark := 'Success';
                NibssRec.MODIFY;
            END;
        END;

    end;
}

