/// <summary>
/// Codeunit sales Value Mail Alert. (ID 50058).
/// </summary>
codeunit 50058 "sales Value Mail Alert."
{
    //Go2solve March 29 2023 >>
    trigger OnRun()
    begin
        SalesValueUpdate();
    end;

    /// <summary>
    /// SalesValueUpdate.
    /// </summary>
    procedure SalesValueUpdate()
    begin
        DaySales := 0;
        MTDSales := 0;
        GDaySales := 0;
        GMTDSales := 0;
        TMTDDiscSales := 0;
        TMTDInvSales := 0;
        TodayDiscount := 0;
        MTDDiscount := 0;
        TodayGrossAmount := 0;
        MTDGrossAmount := 0;

        //clear(Recepients);
        Beginofmonth := 0D;

        ValueEntry.Reset();
        ValueEntry.SetFilter("Posting Date", '%1', Today());
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntry.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP'); //RFC#2024_11
        ValueEntry.CalcSums("Sales Amount (Actual)");
        DaySales := ValueEntry."Sales Amount (Actual)";

        ValueEntry.Reset();
        ValueEntry.SetFilter("Posting Date", '%1', Today());
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntry.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP');
        ValueEntry.SetFilter("Discount Amount", '<%1', 0);
        //RFC#2024_11
        ValueEntry.CalcSums("Discount Amount");
        TodayDiscount := Abs(ValueEntry."Discount Amount");


        //get total invoices
        ValueEntry.Reset();
        ValueEntry.SetFilter("Posting Date", '%1', Today());
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntry.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP'); //RFC#2024_11
        ValueEntry.CalcSums("Sales Amount (Actual)");
        DaySales := ValueEntry."Sales Amount (Actual)";


        ValueEntry.Reset();
        ValueEntry.SetFilter("Posting Date", '%1', Today());
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntry.SetFilter("Document Type", '%1', ValueEntry."Document Type"::"Sales Invoice");
        ValueEntry.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP'); //RFC#2024_11
        ValueEntry.CalcSums("Sales Amount (Actual)");
        TodayGrossAmount := ValueEntry."Sales Amount (Actual)" + TodayDiscount;

        KOdates.reset;
        kodates.SetFilter("Start Date", '<=%1', Today());
        If KOdates.FindLast() then
            Beginofmonth := KOdates."Start Date";

        ValueEntryMTD.Reset();
        ValueEntryMTD.SetFilter("Posting Date", '%1..%2', Beginofmonth, Today());
        ValueEntryMTD.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntryMTD.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP'); //RFC#2024_11
        ValueEntryMTD.CalcSums("Sales Amount (Actual)");
        MTDSales := ValueEntryMTD."Sales Amount (Actual)";

        ValueEntryMTD.Reset();
        ValueEntryMTD.SetFilter("Posting Date", '%1..%2', Beginofmonth, Today());
        ValueEntryMTD.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntryMTD.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP');
        ValueEntryMTD.SetFilter("Discount Amount", '<%1', 0);

        //RFC#2024_11
        ValueEntryMTD.CalcSums("Discount Amount");

        MTDDiscount := Abs(ValueEntryMTD."Discount Amount");
        ValueEntryMTD.Reset();
        ValueEntryMTD.SetFilter("Posting Date", '%1..%2', Beginofmonth, Today());
        ValueEntryMTD.SetFilter("Item Ledger Entry Type", '%1', 1);
        ValueEntryMTD.SetFilter("Document Type", '%1', ValueEntry."Document Type"::"Sales Invoice");
        ValueEntryMTD.SetFilter("Source Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP'); //RFC#2024_11
        ValueEntryMTD.CalcSums("Sales Amount (Actual)");

        MTDGrossAmount := ValueEntryMTD."Sales Amount (Actual)" + MTDDiscount;
        //message('%1', Beginofmonth);
        //fetch pending order update 6/6/23
        SalesPendingValueUpdate();

        AlertUser.SetFilter("User ID", '<>%1', '');
        AlertUser.SetFilter(AlertUser.SVAlert, '%1', true);
        if AlertUser.FindFirst() then
            repeat
                SMTPMailSetup.get();
                SenderMail := SMTPMailSetup."User ID";
                Subject := 'Sales Notification';
                //DaySales := 0;
                //MTDSales := 0;
                clear(Recepients);
                //Beginofmonth := 0D;

                /*ValueEntry.Reset();
                ValueEntry.SetFilter("Posting Date", '%1', Today());
                ValueEntry.SetFilter("Item Ledger Entry Type", '%1', 1);
                ValueEntry.CalcSums("Sales Amount (Actual)");
                DaySales := ValueEntry."Sales Amount (Actual)";

                KOdates.reset;
                kodates.SetFilter("Start Date", '<=%1', Today());
                If KOdates.FindLast() then
                    Beginofmonth := KOdates."Start Date";

                ValueEntryMTD.Reset();
                ValueEntryMTD.SetFilter("Posting Date", '%1..%2', Beginofmonth, Today());
                ValueEntryMTD.SetFilter("Item Ledger Entry Type", '%1', 1);
                ValueEntryMTD.CalcSums("Sales Amount (Actual)");
                MTDSales := ValueEntryMTD."Sales Amount (Actual)";
                //message('%1', Beginofmonth); */

                Recepients.Add(AlertUser."E-Mail");

                SMTPMail.CreateMessage('ERP', SenderMail, Recepients, Subject, '', TRUE);
                SMTPMail.AppendBody('Hi,');
                SMTPMail.AppendBody('<BR><BR>');
                SMTPMail.AppendBody('Dynamics 365 Business Central - Sales notification');
                SMTPMail.AppendBody('<BR><BR>');
                //SMTPMail.AppendBody('This is a message to notify you that:');
                //SMTPMail.AppendBody('<BR><BR>');
                //SMTPMail.AppendBody('The current Sales Amount is');
                //SMTPMail.AppendBody('<BR><BR>');
                SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<th>Today’s Sales thus far</th>');
                SMTPMail.AppendBody('<th>MTD Sales</th>');


                SMTPMail.AppendBody('<th>Time </th>');
                SMTPMail.AppendBody('<th>Date</th>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DaySales) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTDSales) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(Time) + '</td>');



                InputDate := Today;
                Day := Date2DMY(InputDate, 1);
                Month := Date2DMY(InputDate, 2);
                Year := Date2DMY(InputDate, 3);
                SMTPMail.AppendBody('<td style="text-align: center">' + ' ' + Format(Day) + '/' + Format(Month) + '/' + Format(Year) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('</table>');
                SMTPMail.AppendBody('<BR><BR>');

                SMTPMail.AppendBody('<table border="1" width="500" cellspacing="0" cellpadding="0">');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<th>Today''s Gross Sales thus far</th>');
                SMTPMail.AppendBody('<th>MTD Gross Sales</th>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: center">' + Format(TodayGrossAmount) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + Format(MTDGrossAmount) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('</table>');
                SMTPMail.AppendBody('<BR><BR>');

                //salesPending order data
                SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<th>Category</th>');
                SMTPMail.AppendBody('<th>Count </th>');
                SMTPMail.AppendBody('<th>Amount</th>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + 'Total Pending Orders Summary:' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TotalSalesPendingNO) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TotalSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<th>Details</th>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '  ' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '  ' + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Modern Trade Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTpendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Direct Sales Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DirectPendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DirectSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                //upcnotext := Format(BajopendingNo);
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Bajo Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(BajopendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(BajoSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Up Country Sales Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(UPCPendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(UPCSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('</table>');
                //fetch pending order update 6/6/23
                // SalesPendingValueUpdate();

                SMTPMail.AppendBody('<BR><BR>');
                SMTPMail.AppendBody('Regards,');
                SMTPMail.AppendBody('<BR>');
                SMTPMail.AppendBody('CHI Ltd');
                SMTPMail.Send;
            until AlertUser.Next = 0;

        //Group e-mail notification
        SalesRecSetup.Get();
        if SalesRecSetup."Group Email Sales Notify" <> '' THEN BEGIN
            SMTPMailSetup.get();
            SenderMail := SMTPMailSetup."User ID";
            Subject := 'Sales Notification';
            clear(Recepients);
            Recepients.Add(SalesRecSetup."Group Email Sales Notify");

            SMTPMail.CreateMessage('ERP', SenderMail, Recepients, Subject, '', TRUE);
            SMTPMail.AppendBody('Hi,');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Dynamics 365 Business Central - Sales notification');
            SMTPMail.AppendBody('<BR><BR>');
            //SMTPMail.AppendBody('This is a message to notify you that:');
            //SMTPMail.AppendBody('<BR><BR>');
            //SMTPMail.AppendBody('The current Sales Amount is');
            //SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<th>Today’s Sales thus far</th>');
            SMTPMail.AppendBody('<th>Today Gross Amount</th>');
            SMTPMail.AppendBody('<th>MTD Sales</th>');
            SMTPMail.AppendBody('<th>MTD Gross Amount</th>');

            SMTPMail.AppendBody('<th>Time </th>');
            SMTPMail.AppendBody('<th>Date</th>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DaySales) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TodayGrossAmount) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTDSales) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTDGrossAmount) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(Time) + '</td>');
            InputDate := Today;
            Day := Date2DMY(InputDate, 1);
            Month := Date2DMY(InputDate, 2);
            Year := Date2DMY(InputDate, 3);
            SMTPMail.AppendBody('<td style="text-align: center">' + ' ' + Format(Day) + '/' + Format(Month) + '/' + Format(Year) + '</td>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('</table>');
            SMTPMail.AppendBody('<BR><BR>');

            //salesPending order data
            SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<th>Category</th>');
            SMTPMail.AppendBody('<th>Count </th>');
            SMTPMail.AppendBody('<th>Amount</th>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + 'Total Pending Orders Summary:' + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TotalSalesPendingNO) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TotalSalesPending) + '</td>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<th>Details</th>');
            SMTPMail.AppendBody('<td style="text-align: left">' + '  ' + '</td>');
            SMTPMail.AppendBody('<td style="text-align: left">' + '  ' + '</td>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Modern Trade Pending Orders' + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTpendingNo) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTSalesPending) + '</td>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Direct Sales Pending Orders' + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DirectPendingNo) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DirectSalesPending) + '</td>');
            SMTPMail.AppendBody('</tr>');
            //upcnotext := Format(BajopendingNo);
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Bajo Pending Orders' + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(BajopendingNo) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(BajoSalesPending) + '</td>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('<tr>');
            SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Up Country Sales Pending Orders' + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(UPCPendingNo) + '</td>');
            SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(UPCSalesPending) + '</td>');
            SMTPMail.AppendBody('</tr>');
            SMTPMail.AppendBody('</table>');
            //fetch pending order update 6/6/23
            // SalesPendingValueUpdate();

            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Regards,');
            SMTPMail.AppendBody('<BR>');
            SMTPMail.AppendBody('CHI Ltd');
            SMTPMail.Send;
        END;
    end;

    /// <summary>
    /// SalesPendingValueUpdate.
    /// </summary>
    procedure SalesPendingValueUpdate()
    begin
        DirectSalesPending := 0;
        MTSalesPending := 0;
        BajoSalesPending := 0;
        UPCSalesPending := 0;
        TotalSalesPending := 0;
        MTpendingNo := 0;
        BajopendingNo := 0;
        DirectPendingNo := 0;
        UPCPendingNo := 0;
        TotalSalesPendingNO := 0;
        BajoAcctLocFilter1 := 'LOS';
        BajoLocfilter1 := 'NBAJOWH';
        BajoLocfilter2 := 'WHABK';
        BajoLOCFilter3 := 'BAJOORG';

        /*KOdates.reset;
        kodates.SetFilter("Start Date", '<=%1', Today());
        if KOdates.FindLast() then
            Beginofmonth := KOdates."Start Date";*/

        //Message('%1--%2', Beginofmonth, Today());

        salesheader.Reset();
        salesheader.setrange("Document Type", salesheader."Document Type"::Order);
        salesheader.SetRange("Posting Date", Beginofmonth, Today());
        salesheader.SetRange(salesheader.Status, salesheader.Status::Released);
        salesheader.SetRange("Order Tracking", salesheader."Order Tracking"::"Not Yet Shipped");
        salesheader.SetRange("POS Window", false);
        salesheader.SetFilter("Customer Posting Group", '<>%1&<>%2', 'COMPLIMENT', 'SCRAP'); //RFC#2024_11
        if salesheader.findset then
            repeat

                salesheader.CalcFields("Amount Including VAT");
                MTCustomers.reset;

                if (MTCustomers.get(salesheader."Sell-to Customer No.")) and (MTCustomers."Customer Business Type" = MTCustomers."Customer Business Type"::"Modern Trade") then begin
                    MTSalesPending += salesheader."Amount Including VAT";
                    MTpendingNo += 1;
                end else begin
                    if salesheader."Sales Type" = salesheader."Sales Type"::Direct then begin
                        DirectSalesPending += salesheader."Amount Including VAT";
                        DirectPendingNo += 1;
                    end else
                        if (salesheader."Location Code" = BajoLocfilter1) OR
                              (salesheader."Location Code" = BajoLocfilter2) or
                              (salesheader."Location Code" = BajoLocFilter3)
                         then begin
                            BajoSalesPending += salesheader."Amount Including VAT";
                            BajopendingNo += 1;
                        end else
                            if (salesheader."Shortcut Dimension 1 Code" <> BajoAcctLocFilter1) AND
                               (salesheader."Location Code" <> BajoLocfilter1) and
                               (salesheader."Location Code" <> BajoLocfilter2) and
                               (salesheader."Location Code" <> BajoLocfilter3) and
                               (salesheader."Sales Type" <> salesheader."Sales Type"::Direct)
                            then begin
                                UPCSalesPending += salesheader."Amount IncludIng VAT";
                                UPCPendingNo += 1;
                            end;

                end;

            until salesheader.Next = 0;

        TotalSalesPending := MTSalesPending + DirectSalesPending + BajoSalesPending + UPCSalesPending;
        TotalSalesPendingNO := MTpendingNo + DirectPendingNo + BajopendingNo + UPCPendingNo;

        /*
                SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<th>Category</th>');
                SMTPMail.AppendBody('<th>Count </th>');
                SMTPMail.AppendBody('<th>Amount</th>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + 'Total Pending Orders Summary:' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TotalSalesPendingNO) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(TotalSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<th>Details</th>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '  ' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '  ' + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Modern Trade Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTpendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(MTSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Direct Sales Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DirectPendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(DirectSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                //upcnotext := Format(BajopendingNo);
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Bajo Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(BajopendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(BajoSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('<tr>');
                SMTPMail.AppendBody('<td style="text-align: left">' + '       ' + 'Up Country Sales Pending Orders' + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(UPCPendingNo) + '</td>');
                SMTPMail.AppendBody('<td style="text-align: center">' + '  ' + Format(UPCSalesPending) + '</td>');
                SMTPMail.AppendBody('</tr>');
                SMTPMail.AppendBody('</table>'); */
    end;

    var
        myInt: Integer;
        InputDate: Date;
        Day: Integer;
        Month: Integer;
        Year: Integer;
        Text000: Label 'Today is day %1 of month %2 of the year %3.';
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        PurchPaySetup: Record "Purchases & Payables Setup";
        SenderMail: Text;
        RecepientMail1: Text;
        RecepientMail2: Text;
        RecepientMail3: Text;
        UserSetupMail: Text;
        Recepients: List of [Text];
        Subject: text;
        ValueEntry: Record "Value Entry";
        ValueEntryMTD: Record "value entry";
        Beginofmonth: Date;
        DaySales, GDaySales, TDInvSales, TDDiscSales : Decimal;
        MTDSales, GMTDSales, TMTDInvSales, TMTDDiscSales : Decimal;
        KOdates: Record "Rebate Period Codes";
        CreatedDate: Date;
        AlertUser: Record "User Setup";
        SalesRecSetup: Record "sales & receivables Setup";
        salesheader: Record "Sales Header";
        MTCustomers: Record customer;
        DirectSalesPending: Decimal;
        MTSalesPending: Decimal;
        BajoSalesPending: Decimal;
        UPCSalesPending: Decimal;
        TotalSalesPending: Decimal;
        MTpendingNo, BajopendingNo, UPCPendingNo, TotalSalesPendingNO, DirectPendingNo : Integer;
        BajoLocfilter1, BajoLocfilter2, BajoAcctLocFilter1, BajoLocFilter3 : Code[240];
        upcnotext: Text[20];
        MTDGrossAmount: Decimal;
        MTDDiscount: Decimal;
        TodayDiscount: Decimal;
        TodayGrossAmount: Decimal;
}
//Go2solve March 29 2023 <<