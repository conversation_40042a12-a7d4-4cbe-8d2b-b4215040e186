pageextension 50061 GeneralLedgerSetPageExt118 extends "General Ledger Setup"
{
    layout
    {
        addafter("Local Currency Symbol")
        {


            field("Loading Jnl Template Name"; "Loading Jnl Template Name")
            {
                ApplicationArea = all;

            }
            field("Loading Jnl Batch Name"; "Loading Jnl Batch Name")
            {
                ApplicationArea = all;

            }
            field("Loading G/L Acct No."; "Loading G/L Acct No.")
            {
                ApplicationArea = all;

            }
            field("G/L Account for Damage Qty"; "G/L Account for Damage Qty")
            {
                ApplicationArea = ALL;
            }
            field("COA Blocking Period"; "COA Blocking Period")
            {
                ApplicationArea = all;
            }
            field(CHIERP_ItemValidityPeriod; CHIERP_ItemValidityPeriod)
            {
                ApplicationArea = all;
                ToolTip = 'Specifies the Valid peiod for Item data records';
            }
            field(CHIERP_CustomerValidityPeriod; CHIERP_CustomerValidityPeriod)
            {
                ApplicationArea = all;
                ToolTip = 'Specifies the Valid peiod for Customer data records';
            }
            field(CHIERP_VendorValidityPeriod; CHIERP_VendorValidityPeriod)
            {
                ApplicationArea = all;
                ToolTip = 'Specifies the Valid peiod for Vendor data records';
            }
            field(CHIERP_BankValidityPeriod; CHIERP_BankValidityPeriod)
            {
                ApplicationArea = all;
                ToolTip = 'Specifies the Valid peiod for Bank data records';
            }
            field(CHIERP_FAValidityPeriod; CHIERP_FAValidityPeriod)
            {
                ApplicationArea = all;
                ToolTip = 'Specifies the Valid peiod for FA data records';
            }


        }
        addafter(General)
        {
            group(Numbering)
            {
                field("Pms No."; "Pms No.")
                {
                    ApplicationArea = all;
                }
                field("Journal Voucher Nos."; "Journal Voucher Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Journal Voucher Nos."; "Posted Journal Voucher Nos.")
                {
                    ApplicationArea = all;
                }
                field("Purchase JV Nos."; "Purchase JV Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Purchase JV Nos."; "Posted Purchase JV Nos.")
                {
                    ApplicationArea = all;
                }
                field("Sales JV Nos."; "Sales JV Nos.")
                {
                    ApplicationArea = all;
                }
                field("Posted Sale JV Nos."; "Posted Sale JV Nos.")
                {
                    ApplicationArea = all;
                }
                field("Branch CPV Nos"; "Branch CPV Nos")
                {
                    ApplicationArea = all;
                }
                field("Posted Branch CPV Nos"; "Posted Branch CPV Nos")
                {
                    ApplicationArea = all;
                }
                field("Bank Jv Nos"; "Bank Jv Nos")
                {
                    ApplicationArea = all;
                }
                field("Posted Bank Nos"; "Posted Bank Nos")
                {
                    ApplicationArea = all;
                }
                field("Posted Cash Payment Voucher No"; "Posted Cash Payment Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Bank Receipt Voucher No"; "Bank Receipt Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Bank Payment Voucher No"; "Bank Payment Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Posted Bank Payment Voucher No"; "Posted Bank Payment Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Cash Payment Voucher No"; "Cash Payment Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Cash Receipt Voucher No"; "Cash Receipt Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Bank Journal No. Series"; "Bank Journal No. Series")
                {
                    ApplicationArea = ALL;
                }
                field("Posted Bank Receipt Voucher No"; "Posted Bank Receipt Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Posted Cash Receipt Voucher No"; "Posted Cash Receipt Voucher No")
                {
                    ApplicationArea = all;
                }
                field("Master Modify Templ. Nos"; "Master Modify Templ. Nos")
                {
                    ApplicationArea = all;
                }
                field("CWIP No."; "CWIP No.")
                {
                    ApplicationArea = all;
                }
                field("Loan Series"; "Loan Series")
                {
                    ApplicationArea = ALL;
                }
                field("Bank Teller Confirmation CC"; "Bank Teller Confirmation CC")
                {
                    ApplicationArea = all;
                }
                field("Lagos Bank Sttle. Voucher No."; "Lagos Bank Sttle. Voucher No.")
                {
                    ApplicationArea = all;
                }
                field("Posted LOS Bank Sttle.No."; "Posted LOS Bank Sttle.No.")
                {
                    ApplicationArea = all;//Balu ********
                }
                // >>>>>> G2S 06/09/2023
                //CR: RFC#39  
                //Signed: 31st Oct., 2023
                //Name: Go2Solve Nig. Ltd
                //Published: 10th Nov., 2023

                field("Notification Nos."; "Notification Nos.")
                {
                    ApplicationArea = All;
                }

                field("Notification Batch Name"; Rec."Notification Batch Name")
                {
                    ApplicationArea = All;
                }
                // <<<<<< G2S 06/05/2023
            }
        }
        addafter("Prepayment Unrealized VAT")
        {
            field("Voucher Half Life"; "Voucher Half Life")
            {
                ApplicationArea = all;
            }
            field("XML Bank file path"; "XML Bank file path")
            {
                ApplicationArea = all;
            }
            field("Others Bank file path"; "Others Bank file path")
            {
                ApplicationArea = all;
            }
            field("Online Payment Batch Name"; "Online Payment Batch Name")
            {
                ApplicationArea = all;
            }
            field("Online Payment Template"; "Online Payment Template")
            {
                ApplicationArea = all;
            }
            field("Page Other Filters"; "Page Other Filters")
            {
                ApplicationArea = all;
            }
            field("Bank Confirmation SharedPath"; "Bank Confirmation SharedPath")
            {
                ApplicationArea = all;
            }
        }
    }
}