codeunit 50121 ItyemJourSubscr
{
    trigger OnRun()
    begin

    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Item Jnl.-Post Line", 'OnAfterInitItemLedgEntry', '', false, false)]
    local procedure UpdateSNOPFields(var NewItemLedgEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; var ItemLedgEntryNo: Integer);
    var
        LocRec: Record Location;
        TranShip: Record "Transfer Shipment Header";
    begin
        LocRec.RESET();
        //LocRec.SETRANGE(Active, TRUE);B2B Prasanna
        LocRec.SETRANGE(Code, ItemJournalLine."Location Code");
        LocRec.SETRANGE("Use As In-Transit", TRUE);
        IF Not LocRec.IsEmpty() THEN
            NewItemLedgEntry."In-Transit" := TRUE;
        IF ((ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer) AND (ItemJournalLine."Document Type" = ItemJournalLine."Document Type"::"Transfer Shipment")) THEN BEGIN
            TranShip.RESET();
            TranShip.SETRANGE("No.", ItemJournalLine."Document No.");
            IF TranShip.FINDFIRST() THEN
                NewItemLedgEntry."To Location_SNOP" := TranShip."Transfer-to Code";
        END;
    END;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Production Journal Mgt", 'OnBeforeInsertConsumptionJnlLine', '', false, false)]
    Procedure UpdateProdBatch(var ItemJournalLine: Record "Item Journal Line"; ProdOrderComp: Record "Prod. Order Component"; ProdOrderLine: Record "Prod. Order Line"; Level: Integer)
    begin
        ItemJournalLine."Production Batch No." := ProdOrderLine."Production Batch No.";
        ItemJournalLine."External Document No." := ProdOrderLine."External Document No.";//PkOnEXT
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Production Journal Mgt", 'OnBeforeInsertOutputJnlLine', '', false, false)]
    Procedure UpdateProdBatch2(var ItemJournalLine: Record "Item Journal Line"; ProdOrderRtngLine: Record "Prod. Order Routing Line"; ProdOrderLine: Record "Prod. Order Line")
    begin
        ItemJournalLine."Production Batch No." := ProdOrderLine."Production Batch No.";
        ItemJournalLine."External Document No." := ProdOrderLine."External Document No.";//PkOnEXT
    end;

}