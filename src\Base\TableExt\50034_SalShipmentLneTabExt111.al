tableextension 50034 SalShipmentLneTabExt extends "Sales Shipment Line"
{
    fields
    {
      

        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Promo. No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Promo Schedule"."No." WHERE(Status = CONST(Released));
            Editable = false;
        }
        field(50003; "Promo. Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Promo Schedule Line"."Line No." WHERE("Document No." = FIELD("Promo. No."), Active = CONST(true));
            Editable = false;
        }
        field(50004; "Gift Item"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
    }
    

}