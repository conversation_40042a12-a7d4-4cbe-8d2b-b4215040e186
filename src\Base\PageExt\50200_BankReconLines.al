pageextension 50300 BankRecLiness extends "Bank Acc. Reconciliation Lines"
{
    layout
    {
        addafter("Document No.")
        {
            field("Matching Status"; "Matching Status")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("MatchLine No"; "MatchLine No")
            {
                ApplicationArea = ALL;
            }
        }
    }

    actions
    {

    }

    var
        BankAccRecon: Record "Bank Acc. Reconciliation";
        StmtApplyEntries: Codeunit "Bank Acc. Recon. Apply Entries";
        TotalDiff: Decimal;
        Balance: Decimal;
        TotalBalance: Decimal;
        OgnlBnkStatement: Record "Original Bank Statement";

    Procedure CalcBalance(BankAccReconLineNo: Integer)
    var
        TempBankAccReconLine: Record "Bank Acc. Reconciliation Line";
    begin
        IF BankAccRecon.GET("Bank Account No.", "Statement No.") THEN;

        TempBankAccReconLine.COPY(Rec);

        TotalDiff := -Difference;
        IF TempBankAccReconLine.CALCSUMS(Difference) THEN BEGIN
            TotalDiff := TotalDiff + TempBankAccReconLine.Difference;
            //CurrPage.TotalDiff.ENABLED := TRUE
        END; /*ELSE
            CurrPage.TotalDiff.ENABLED := FALSE;*/

        TotalBalance := BankAccRecon."Balance Last Statement" - "Statement Amount";
        IF TempBankAccReconLine.CALCSUMS("Statement Amount") THEN BEGIN
            TotalBalance := TotalBalance + TempBankAccReconLine."Statement Amount";
            //CurrPage.TotalBalance.ENABLED := TRUE
        END;
        //CurrPage.TotalBalance.ENABLED := FALSE;*/ PK

        Balance := BankAccRecon."Balance Last Statement" - "Statement Amount";
        TempBankAccReconLine.SETRANGE("Statement Line No.", 0, BankAccReconLineNo);
        IF TempBankAccReconLine.CALCSUMS("Statement Amount") THEN BEGIN
            Balance := Balance + TempBankAccReconLine."Statement Amount";
            //CurrPage.Balance.ENABLED := TRUE;//PK
        END;/* ELSE
            CurrPage.Balance.ENABLED := FALSE;*///pk
    end;

    Procedure ApplyEntries()
    BEGIn
        "Ready for Application" := TRUE;
        CurrPage.SAVERECORD;
        COMMIT;
        StmtApplyEntries.ApplyEntries(Rec);
    end;

    Procedure SelectedEntry(VAR SelectedEntryLine: Integer)
    begin
        SelectedEntryLine := "Statement Line No.";
    end;

    Procedure selectedEntries(VAR BankReconLines: Record "Bank Acc. Reconciliation Line")
    begin
        BankReconLines := Rec;
        CurrPage.SETSELECTIONFILTER(BankReconLines);
    end;

    procedure ToggleMatchedFilterforBankRec()
    begin
        SetFilter("Applied Entries", '<>%1', 0);
        CurrPage.Update;
    end;

    Procedure GetMatchFil(Lpar: option Exact,Probable,"Not Matching",Cancel,All)
    begin
        IF Lpar = lpar::Exact then
            setrange("Matching Status", "Matching Status"::Exact)
        else
            IF Lpar = lpar::Probable then
                setrange("Matching Status", "Matching Status"::Probable)
            Else
                IF Lpar = lpar::"Not Matching" then
                    setrange("Matching Status", "Matching Status"::"Not Matching")
                else
                    IF Lpar = lpar::Cancel then
                        setrange("Matching Status", "Matching Status"::Cancel)
                    else
                        IF Lpar = lpar::ALL then
                            ReSET;
        currpage.Update();
    end;
}