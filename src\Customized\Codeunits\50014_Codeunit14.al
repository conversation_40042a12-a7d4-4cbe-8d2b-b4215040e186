codeunit 50014 "Send Mail"
{
    trigger OnRun()
    begin

    end;

    procedure SendAppBrStkRequestAlertFromTF(BranchStkReq: Record "Transfer Header"; EMail: Text[200]);
    var
        LocFrom: Record Location;
        LocTo: Record Location;
        Usersetup: Record "User Setup";
        LocRec: Record Location;
        BrStkReqlines: Record "Transfer Line";
        Usersetup1: Record "User Setup";
        Recipients: List of [Text];
    begin
        //BEGIN
        SMTPSetup.GET;
        SMTPSetup.TESTFIELD("Trans. Rcpt. Report Path");
        Usersetup.GET(USERID);
        SmtpMail.CreateMessage(USERID, SMTPSetup."User ID", Usersetup."E-Mail", 'Branch Stock Request Notification', '', TRUE);
        //Need to Ask Above doubt
        Usersetup1.SETFILTER("Edit Loading Advice Qty.", '%1', TRUE);
        IF Usersetup1.FINDSET THEN
            REPEAT
                IF Usersetup1."E-Mail" <> '' THEN;
                Recipients.Add(Usersetup1."E-Mail");
            UNTIL Usersetup1.NEXT = 0;
        IF LocRec.GET(BranchStkReq."Transfer-to Code") THEN;
        SmtpMail.AppendBody('Dear All');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Find below details of the items requested:');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        //SmtpMail.AppendBody('Transfer from: '+ FORMAT(LocFrom.Code+'-'+LocFrom.Name));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Transfer to: ' + FORMAT(BranchStkReq."Transfer-to Code" + '-' + LocRec.Name));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Document No.: ' + FORMAT(BranchStkReq."No."));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Document Date: ' + FORMAT(CURRENTDATETIME));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Expected Date of Dispatch:  ' + FORMAT(BranchStkReq."Shipment Date"));
        SmtpMail.AppendBody('<br>');
        //BranchStkReq.CALCFIELDS("Gross Weight");
        SmtpMail.AppendBody('Total Weight: ' + FORMAT(''));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<table border="1", width="100%">');
        SmtpMail.AppendBody('<tr>');
        SmtpMail.AppendBody('<th>Item No.</th>');
        SmtpMail.AppendBody('<th>Item Description</th>');
        SmtpMail.AppendBody('<th>Quantity Requested</th>');
        //SmtpMail.AppendBody('<th>Quantity Dispatched</th>');
        SmtpMail.AppendBody('</tr>');
        //Document Type,Vendor No.,Posting Date,Currency Code
        BrStkReqlines.SETRANGE("Document No.", BranchStkReq."No.");
        IF BrStkReqlines.FINDSET THEN
            REPEAT
                SmtpMail.AppendBody('<tr>');
                SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Item No.") + '</td>');
                SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines.Description) + '</td>');
                SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Branch Request Qty") + '</td>');
                //SmtpMail.AppendBody('<td>'+FORMAT(SalesDispGPassLine."Qty Dispatched")+'</td>');
                SmtpMail.AppendBody('</tr>');
            UNTIL BrStkReqlines.NEXT = 0;
        SmtpMail.AppendBody('</table>');
        SmtpMail.AppendBody('</body>');
        SmtpMail.AppendBody('</html>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Please process the request.');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Regards');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody(FORMAT(Usersetup.Name));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');

        SmtpMail.Send;

        IF EXISTS(FilenameAttach) THEN
            ERASE(FilenameAttach);
        //END;
        //UNTIL Usersetup.NEXT =0;
    end;

    procedure SENDPROMRSNOTIFICATION(MRSHD: Record MRSHeader)
    var
        Usersetup: Record "User Setup";
        MRSLINE: Record MRSLine;
        Usersetup1: Record "User Setup";
        HDCREATED: Boolean;
        ICRec: Record "Item Category";
        ProdMRSHDR: Record MRSHeader;
        CCEmail: List of [text];
    Begin
        ERROR('');
        ProdMRSHDR.SETCURRENTKEY("Production Batch No.");
        ProdMRSHDR.SETRANGE(ProdMRSHDR."Production Batch No.", MRSHD."Production Batch No.");
        IF ProdMRSHDR.FINDSET THEN
            REPEAT
                Usersetup1.GET(USERID);
                SmtpMail.CreateMessage(USERID, Usersetup1."E-Mail", Usersetup1."E-Mail", 'Production MRS Notification', '', TRUE);
                Usersetup1.SETFILTER("Get Prod. MRS Notification", '%1', TRUE);
                Usersetup1.SETFILTER("Store Type", '%1|%2', '', ProdMRSHDR."MRS Category Code"); //MRSHD."MRS Category Code");
                IF Usersetup1.FINDSET THEN
                    REPEAT
                        IF Usersetup1."E-Mail" <> '' THEN begin
                            CCEmail.Add(Usersetup1."E-Mail");
                            SmtpMail.AddCC(CCEmail);
                        end;
                    UNTIL Usersetup1.NEXT = 0;
                SmtpMail.AppendBody('Dear All');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');

                //ActiveVersion:=VersionMgt.GetBOMVersion(ProdBomHD."No.",WORKDATE,TRUE);;


                SmtpMail.AppendBody('MRS NO.: ' + ProdMRSHDR."MRS No.");
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                //SmtpMail.AppendBody('Production Order No.: '+ MRSHD."Manual MRS No.");
                IF ICRec.GET(ProdMRSHDR."MRS Category Code") THEN
                    SmtpMail.AppendBody('MRS Category Code: ' + ICRec.Description);
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Production Batch No.: ' + ProdMRSHDR."Production Batch No.");
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Production Shop Floor: ' + ProdMRSHDR."Location Code");
                SmtpMail.AppendBody('<br>');

                SmtpMail.AppendBody('Find below detail of the Production MRS');

                SmtpMail.AppendBody('<table border="1", width="100%">');

                SmtpMail.AppendBody('<tr>');
                SmtpMail.AppendBody('<th>Item No.</th>');
                SmtpMail.AppendBody('<th>Item Description</th>');
                SmtpMail.AppendBody('<th>Unit Of Measure</th>');
                SmtpMail.AppendBody('<th>Quantity</th>');
                SmtpMail.AppendBody('</tr>');



                MRSLINE.RESET;
                MRSLINE.SETRANGE(MRSLINE."Document No.", ProdMRSHDR."MRS No.");
                IF MRSLINE.FINDSET THEN
                    REPEAT
                        SmtpMail.AppendBody('<tr>');
                        SmtpMail.AppendBody('<td>' + FORMAT(MRSLINE."No.") + '</td>');
                        SmtpMail.AppendBody('<td>' + FORMAT(MRSLINE.Description) + '</td>');
                        SmtpMail.AppendBody('<td>' + FORMAT(MRSLINE."Unit of Measure Code") + '</td>');
                        SmtpMail.AppendBody('<td>' + FORMAT(MRSLINE.Quantity) + '</td>');
                        SmtpMail.AppendBody('</tr>');
                    UNTIL MRSLINE.NEXT = 0;

                SmtpMail.AppendBody('</table>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');

                SmtpMail.AppendBody('</body>');
                SmtpMail.AppendBody('</html>');

                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('Kindly Process the request');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');

                SmtpMail.AppendBody('Regards');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody(FORMAT(Usersetup1.Name));
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('<br>');
                SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');

                SmtpMail.Send;

                IF EXISTS(FilenameAttach) THEN
                    ERASE(FilenameAttach);
            UNTIL ProdMRSHDR.NEXT = 0;
    end;

    procedure SendAppBrStkRequestAlertFromBR(BranchStkReq: Record BranchRequest; EMail: Text[200]);
    var
        LocFrom: Record Location;
        LocTo: Record Location;
        Usersetup: Record "User Setup";
        LocRec: Record Location;
        BrStkReqlines: Record BranchRequestLine;
        Usersetup1: Record "User Setup";
        Recipients: List of [Text];
    begin
        //BEGIN
        SMTPSetup.GET;
        //SMTPSetup.TESTFIELD("Trans. Rcpt. Report Path");
        Usersetup.GET(USERID);
        IF LocRec.GET(BranchStkReq."From Location") THEN;
        LocRec.testfield("E-Mail");
        SmtpMail.CreateMessage(USERID, SMTPSetup."User ID", LocRec."E-Mail", 'Branch Stock Request Notification', '', TRUE);

        //Need to Ask Above doubt
        /*Usersetup1.SETFILTER("Edit Loading Advice Qty.", '%1', TRUE);
        IF Usersetup1.FINDSET THEN
            REPEAT
                IF Usersetup1."E-Mail" <> '' THEN;
                Recipients.Add(Usersetup1."E-Mail");
            UNTIL Usersetup1.NEXT = 0;*/
        SmtpMail.AppendBody('Dear All');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Find below details of the items requested:');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Transfer from: ' + FORMAT(BranchStkReq."From Location"));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Transfer to: ' + FORMAT(BranchStkReq."To Location"));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Document No.: ' + FORMAT(BranchStkReq."No"));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Document Date: ' + FORMAT(CURRENTDATETIME));
        SmtpMail.AppendBody('<br>');
        //SmtpMail.AppendBody('Expected Date of Dispatch:  ' + FORMAT(BranchStkReq."Shipment Date"));
        SmtpMail.AppendBody('<br>');
        //BranchStkReq.CALCFIELDS("Gross Weight");
        SmtpMail.AppendBody('Total Weight: ' + FORMAT(''));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<table border="1", width="100%">');
        SmtpMail.AppendBody('<tr>');
        SmtpMail.AppendBody('<th>Item No.</th>');
        SmtpMail.AppendBody('<th>Item Description</th>');
        SmtpMail.AppendBody('<th>Quantity Requested</th>');
        //SmtpMail.AppendBody('<th>Quantity Dispatched</th>');
        SmtpMail.AppendBody('</tr>');
        //Document Type,Vendor No.,Posting Date,Currency Code
        BrStkReqlines.SETRANGE("Document No.", BranchStkReq."No");
        IF BrStkReqlines.FINDSET THEN
            REPEAT
                SmtpMail.AppendBody('<tr>');
                SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Item No.") + '</td>');
                SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines.Description) + '</td>');
                SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Requested Quantity") + '</td>');
                //SmtpMail.AppendBody('<td>'+FORMAT(SalesDispGPassLine."Qty Dispatched")+'</td>');
                SmtpMail.AppendBody('</tr>');
            UNTIL BrStkReqlines.NEXT = 0;
        SmtpMail.AppendBody('</table>');
        SmtpMail.AppendBody('</body>');
        SmtpMail.AppendBody('</html>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Please process the request.');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('Regards');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody(FORMAT(Usersetup.Name));
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
        //sending Second Email
        if LocRec."E-Mail2" <> '' then begin
            SmtpMail.Send;
            SmtpMail.CreateMessage(USERID, SMTPSetup."User ID", LocRec."E-Mail2", 'Branch Stock Request Notification', '', TRUE);
            //Need to Ask Above doubt
            SmtpMail.AppendBody('Dear All');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Find below details of the items requested:');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Transfer from: ' + FORMAT(BranchStkReq."From Location"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Transfer to: ' + FORMAT(BranchStkReq."To Location"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Document No.: ' + FORMAT(BranchStkReq."No"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Document Date: ' + FORMAT(CURRENTDATETIME));
            SmtpMail.AppendBody('<br>');
            //SmtpMail.AppendBody('Expected Date of Dispatch:  ' + FORMAT(BranchStkReq."Shipment Date"));
            SmtpMail.AppendBody('<br>');
            //BranchStkReq.CALCFIELDS("Gross Weight");
            SmtpMail.AppendBody('Total Weight: ' + FORMAT(''));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<table border="1", width="100%">');
            SmtpMail.AppendBody('<tr>');
            SmtpMail.AppendBody('<th>Item No.</th>');
            SmtpMail.AppendBody('<th>Item Description</th>');
            SmtpMail.AppendBody('<th>Quantity Requested</th>');
            //SmtpMail.AppendBody('<th>Quantity Dispatched</th>');
            SmtpMail.AppendBody('</tr>');
            //Document Type,Vendor No.,Posting Date,Currency Code
            BrStkReqlines.SETRANGE("Document No.", BranchStkReq."No");
            IF BrStkReqlines.FINDSET THEN
                REPEAT
                    SmtpMail.AppendBody('<tr>');
                    SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Item No.") + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines.Description) + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Requested Quantity") + '</td>');
                    //SmtpMail.AppendBody('<td>'+FORMAT(SalesDispGPassLine."Qty Dispatched")+'</td>');
                    SmtpMail.AppendBody('</tr>');
                UNTIL BrStkReqlines.NEXT = 0;
            SmtpMail.AppendBody('</table>');
            SmtpMail.AppendBody('</body>');
            SmtpMail.AppendBody('</html>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Please process the request.');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Regards');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody(FORMAT(Usersetup.Name));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
            SmtpMail.Send;
        end;
        //sending Third Email
        if LocRec."E-Mail3" <> '' then begin
            SmtpMail.Send;
            SmtpMail.CreateMessage(USERID, SMTPSetup."User ID", LocRec."E-Mail3", 'Branch Stock Request Notification', '', TRUE);
            //Need to Ask Above doubt
            SmtpMail.AppendBody('Dear All');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Find below details of the items requested:');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Transfer from: ' + FORMAT(BranchStkReq."From Location"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Transfer to: ' + FORMAT(BranchStkReq."To Location"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Document No.: ' + FORMAT(BranchStkReq."No"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Document Date: ' + FORMAT(CURRENTDATETIME));
            SmtpMail.AppendBody('<br>');
            //SmtpMail.AppendBody('Expected Date of Dispatch:  ' + FORMAT(BranchStkReq."Shipment Date"));
            SmtpMail.AppendBody('<br>');
            //BranchStkReq.CALCFIELDS("Gross Weight");
            SmtpMail.AppendBody('Total Weight: ' + FORMAT(''));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<table border="1", width="100%">');
            SmtpMail.AppendBody('<tr>');
            SmtpMail.AppendBody('<th>Item No.</th>');
            SmtpMail.AppendBody('<th>Item Description</th>');
            SmtpMail.AppendBody('<th>Quantity Requested</th>');
            //SmtpMail.AppendBody('<th>Quantity Dispatched</th>');
            SmtpMail.AppendBody('</tr>');
            //Document Type,Vendor No.,Posting Date,Currency Code
            BrStkReqlines.SETRANGE("Document No.", BranchStkReq."No");
            IF BrStkReqlines.FINDSET THEN
                REPEAT
                    SmtpMail.AppendBody('<tr>');
                    SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Item No.") + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines.Description) + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(BrStkReqlines."Requested Quantity") + '</td>');
                    //SmtpMail.AppendBody('<td>'+FORMAT(SalesDispGPassLine."Qty Dispatched")+'</td>');
                    SmtpMail.AppendBody('</tr>');
                UNTIL BrStkReqlines.NEXT = 0;
            SmtpMail.AppendBody('</table>');
            SmtpMail.AppendBody('</body>');
            SmtpMail.AppendBody('</html>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Please process the request.');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Regards');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody(FORMAT(Usersetup.Name));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
            SmtpMail.Send;
        end;


        IF EXISTS(FilenameAttach) THEN
            ERASE(FilenameAttach);
        //END;
        //UNTIL Usersetup.NEXT =0;
    end;


    var
        myInt: Integer;
        SmtpMail: Codeunit "SMTP Mail";
        SMTPSetup: record "SMTP Mail Setup";
        FilenameAttach: Text[200];


}