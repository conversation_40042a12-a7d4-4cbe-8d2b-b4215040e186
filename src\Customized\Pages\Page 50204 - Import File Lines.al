page 50911 "Import File Lines"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // UNL     : Univision Nigeria Ltd.
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       UNL      18-Feb-12    -> Form created to display the non edtiable FORM M Lines from where you can navigate FORM M Documen.

    Editable = false;
    PageType = List;
    SourceTable = "Import File Line";
    UsageCategory = Lists;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Document No."; "Document No.")
                {
                }
                field("Line No."; "Line No.")
                {
                }
                field("Document Type"; "Document Type")
                {
                }
                field("Order No."; "Order No.")
                {
                }
                field("Order Line No."; "Order Line No.")
                {
                }
                field(Type; Type)
                {
                }
                field("No."; "No.")
                {
                }
                field(Description; Description)
                {
                }
                field("Description 2"; "Description 2")
                {
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                }
                field(Quantity; Quantity)
                {
                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                }
                field("Amount (FCY)"; "Amount (FCY)")
                {
                }
                field("Item Category Code"; "Item Category Code")
                {
                }
                field("Product Group Code"; "Product Group Code")
                {
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Show Document")
                {
                    Caption = 'Show Document';

                    trigger OnAction();
                    begin
                        VendRec.GET("Document No.");
                        PAGE.RUN(PAGE::"Import File Card", VendRec);
                    end;
                }
            }
        }
    }

    var
        VendRec: Record "Vendor 2";
}

