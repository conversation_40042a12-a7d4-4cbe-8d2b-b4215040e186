table 50179 "13Wk Header"
{
    Caption = '13Wk Header';
    DataClassification = ToBeClassified;
    DrillDownPageId = "13Wk Plan Card";
    LookupPageId = "13Wk Plan List";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            Editable = false;
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
        }
        field(3; "Start Date"; Date)
        {
            Caption = 'Start Date';

            //TODO  Fix logic
            trigger OnValidate()
            begin

            end;
        }
        field(4; "End Date"; Date)
        {
            Caption = 'End Date';
            // Editable = false;
        }
        field(5; "Cc Code"; Code[20])
        {
            Caption = 'CC Code';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(2));
        }
        field(6; "Created by"; Code[20])
        {
            Caption = 'Created by';
            Editable = false;
        }
        field(7; "Approval Status"; Enum ApprovalStatus)
        {
            Caption = 'Approval Status';
        }
        field(8; "Created Date"; Date)
        {
            Caption = 'Created Date';
            DataClassification = ToBeClassified;
        }
        // field(9; "Active Version"; Code[50])
        // {
        //     Caption = 'Active Version';
        //     FieldClass = FlowField;
        //     CalcFormula = Max("13Wk Plan Log".Version WHERE("No." = FIELD("No.")));
        // }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    fieldgroups
    {
        fieldgroup(DropDown; "No.", Description, "Start Date", "End Date")
        {

        }
        fieldgroup(Brick; "No.", Description, "Start Date", "End Date")
        {

        }
    }

    trigger OnInsert()
    begin
        if "No." = '' then begin
            ManufacturingSetup.Get();
            ManufacturingSetup.TestField("13Wk Plan No. Series");
            "No." := NoSeries.GetNextNo(ManufacturingSetup."13Wk Plan No. Series", Today, true);
            "Created by" := UserId;
            "Created Date" := Today();
        end;
    end;

    trigger OnModify()
    begin

    end;

    trigger OnDelete()
    begin

    end;

    trigger OnRename()
    begin

    end;

    procedure TestStatusOpen()
    begin
        TESTFIELD("Approval Status", "Approval Status"::Open);
    end;

    var
        NoSeries: Codeunit NoSeriesManagement;
        ManufacturingSetup: Record "Manufacturing Setup";
}
