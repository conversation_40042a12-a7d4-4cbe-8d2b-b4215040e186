/// <summary>
/// TableExtension CustomerPostingGroup extends Record Customer Posting Group.
/// </summary>
/// >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9
tableextension 50372 CHIERP_CustomerPostingGroup extends "Customer Posting Group"
{
    fields
    {
        field(50372; CHIERP_Complimentary; Boolean)
        {
            Caption = 'Complimentary';
            DataClassification = CustomerContent;
        }
    }
}
//>>>>>> G2S 12/12/2024 CAS-01380-T1P0C9