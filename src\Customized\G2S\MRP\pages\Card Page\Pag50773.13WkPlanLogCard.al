page 50773 "13Wk Log Card"
{
    Caption = '13Wk Plan Log Card';
    PageType = Card;
    SourceTable = "13Wk Log Header";

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;
                }
                field("Cc Code"; "Cc Code")
                {
                    ApplicationArea = All;
                }
                field("Created By "; "Created By ")
                {
                    ApplicationArea = All;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = All;
                }
                field("Updated By"; "Updated By")
                {
                    ApplicationArea = All;
                }
                field("Update Date"; "Update Date")
                {
                    ApplicationArea = All;
                }
                field(Version; Version)
                {
                    ApplicationArea = All;
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = All;
                }
            }
            part("Log Lines"; "13Wk Log Line")
            {
                ApplicationArea = All;
                SubPageLink = "Document No" = FIELD("No.");
            }
        }
    }
}
