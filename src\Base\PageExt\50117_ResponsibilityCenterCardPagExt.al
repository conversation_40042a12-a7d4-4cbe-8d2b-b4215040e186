pageextension 50117 ResponsibilityCenterCardPagExt extends "Responsibility Center Card"
{
    layout
    {
        addafter(Communication)
        {
            group(Numbering)
            {
                Caption = 'Numbering';
                field("Posting Bank Recpts No."; "Posting Bank Recpts No.")
                {
                    ApplicationArea = all;
                }
                field("Posting Cash Recpts No."; "Posting Cash Recpts No.")
                {
                    ApplicationArea = all;
                }
                field("Bank Balance A/C"; "Bank Balance A/C")
                {
                    ApplicationArea = all;
                }
                field("Payable A/C"; "Payable A/C")
                {
                    ApplicationArea = all;
                }
                field("Cash Balancee A/C"; "Cash Balancee A/C")
                {
                    ApplicationArea = all;
                }
                field("Branch Cash"; "Branch Cash")
                {
                    ApplicationArea = all;
                }
                field("SCD & CCD Mail Alerts"; "SCD & CCD Mail Alerts")
                {
                    ApplicationArea = all;
                }
                field("SCD Mail Alert 1"; "SCD Mail Alert 1")
                {
                    ApplicationArea = all;
                }
                field("SCD Mail Alert 2"; "SCD Mail Alert 2")
                {
                    ApplicationArea = all;
                }
                field("SCD Mail Alert 3"; "SCD Mail Alert 3")
                {
                    ApplicationArea = all;
                }
                field("CCD Mail Alert 1"; "CCD Mail Alert 1")
                {
                    ApplicationArea = all;
                }
                field("CCD Mail Alert 2"; "CCD Mail Alert 2")
                {
                    ApplicationArea = all;
                }
                field("CCD Mail Alert 3"; "CCD Mail Alert 3")
                {
                    ApplicationArea = all;
                }
                //Balu ********>>
                field("Transport Account"; "Transport Account")
                {
                    ApplicationArea = all;
                }
                field("Item Self Lift rate"; "Item Self Lift rate")
                {
                    ApplicationArea = all;
                    Caption = 'Item wise self lifting rate';
                }
                //Balu ********<<
                //PKONAU18 >>
                field("Despatch Mail Alert"; "Despatch Mail Alert")
                {
                    ApplicationArea = all;
                }
                field("Despatch Mail Alert 1"; "Despatch Mail Alert 1")
                {
                    ApplicationArea = all;
                }
                field("Despatch Mail Alert 2"; "Despatch Mail Alert 2")
                {
                    ApplicationArea = all;
                }
                field("Despatch Mail Alert 3"; "Despatch Mail Alert 3")
                {
                    ApplicationArea = all;
                }
                //PKONAU18 <<
            }
        }


    }
    actions
    {

        addafter(Dimensions)
        {
            action("Warehouse Locations")
            {
                ApplicationArea = all;
                Caption = 'Warehouse Locations';
                RunObject = page "Responcibility Locations";
                RunPageLink = "Responsibility Center" = field(Code);
                /*
                                trigger OnAction();
                                begin
                                    ResLoc.SETFILTER("Responsibility Center", Code);
                                    PAGE.RUNMODAL(PAGE::"Responcibility Locations", ResLoc);


                                end;*/
            }
        }

    }
    var
        ResLoc: Record "Responsibility Center Location";


}