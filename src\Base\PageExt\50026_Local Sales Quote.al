
page 50026 "Local Sales Quote"
{
    Caption = 'Local Sales Quote';
    PageType = Document;
    PromotedActionCategories = 'New,Process,Report,Quote,View,Approve,Request Approval,History,Print/Send,Release,Navigate';
    RefreshOnActivate = true;
    SourceTable = "Sales Header";
    SourceTableView = WHERE("Document Type" = FILTER(Quote), "Sales Type" = filter("Local"));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = All;
                    Importance = Standard;
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                    Visible = DocNoVisible;

                    trigger OnAssistEdit()
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.Update;
                    end;
                }
                field("User Wise Resp Centr"; "User Wise Resp Centr")
                {
                    ApplicationArea = all;
                }
                field("Resp Wise Sell-to Customer No."; "Resp Wise Sell-to Customer No.")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    begin
                        IF CustGRec.GET("Resp Wise Sell-to Customer No.") then begin
                            IF CustGRec."Customer Type" = custGRec."Customer Type"::Export THEN
                                Error('You Cannot Select Export Customer %1 for this Local Sales Quote %2', "Sell-to Customer No.", "No.");
                        END;
                    end;
                }
                field("Sell-to Customer No."; "Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    Caption = 'Customer No.';
                    Importance = Additional;
                    NotBlank = true;
                    Editable = false;
                    ToolTip = 'Specifies the number of the customer who will receive the products and be billed by default.';

                    trigger OnValidate()
                    begin
                        SelltoCustomerNoOnAfterValidate(Rec, xRec);
                        IF CustGRec.GET("Sell-to Customer No.") then
                            CustGRec.TestField("Customer Type", CustGRec."Customer Type"::Local);

                        CurrPage.Update;
                    end;
                }
                field("Sell-to Customer Name"; "Sell-to Customer Name")
                {
                    ApplicationArea = All;
                    Caption = 'Customer Name';
                    Importance = Promoted;
                    NotBlank = true;
                    ShowMandatory = true;
                    Editable = false;
                    ToolTip = 'Specifies the name of the customer who will receive the products and be billed by default.';

                    trigger OnValidate()
                    var
                        ApplicationAreaMgmtFacade: Codeunit "Application Area Mgmt. Facade";
                    begin
                        SelltoCustomerNoOnAfterValidate(Rec, xRec);
                        CurrPage.Update;

                        if ApplicationAreaMgmtFacade.IsFoundationEnabled then
                            SalesCalcDiscByType.ApplyDefaultInvoiceDiscount(0, Rec);
                    end;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    Editable = false;
                    AccessByPermission = TableData "Responsibility Center" = R;
                    ApplicationArea = Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the code of the responsibility center, such as a distribution hub, that is associated with the involved user, company, customer, or vendor.';
                }
                field("External Document No."; "External Document No.")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies a document number that refers to the customer''s or vendor''s numbering system.';
                }
                //rebate issue >>>>
                field("Fixed Rebate code"; "Fixed Rebate Code")
                {
                    Editable = false;
                }
                field("Fixed Rebate"; "Fixed Rebate")
                {
                    Editable = false;
                }
                field("Fixed Rebate Amount"; "Fixed Rebate Amount")
                {
                    Editable = false;
                }
                field("Fixed Rebate Amount to Inv."; "Fixed Rebate Amount to Inv.")
                {
                    Editable = false;
                }
                //<<<<<
                group("Sell-to")
                {
                    Caption = 'Sell-to';
                    field("Sell-to Address"; "Sell-to Address")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Address';
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the address where the customer is located.';
                    }
                    field("Sell-to Address 2"; "Sell-to Address 2")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Address 2';
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies additional address information.';
                    }
                    field("Sell-to City"; "Sell-to City")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'City';
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the city of the customer on the sales document.';
                    }
                    group(Control105)
                    {
                        ShowCaption = false;
                        Visible = IsSellToCountyVisible;
                        field("Sell-to County"; "Sell-to County")
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'County';
                            Importance = Additional;
                            QuickEntry = false;
                            ToolTip = 'Specifies the county of the address.';
                        }
                    }
                    field("Sell-to Post Code"; "Sell-to Post Code")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Post Code';
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the postal code.';
                    }
                    field("Sell-to Country/Region Code"; "Sell-to Country/Region Code")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Country/Region';
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the country or region of the address.';

                        trigger OnValidate()
                        begin
                            IsSellToCountyVisible := FormatAddress.UseCounty("Sell-to Country/Region Code");
                        end;
                    }
                    field("Sell-to Contact No."; "Sell-to Contact No.")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Contact No.';
                        Importance = Additional;
                        ToolTip = 'Specifies the number of the contact that the sales document will be sent to.';

                        trigger OnValidate()
                        begin
                            ClearSellToFilter;
                            ActivateFields;
                            CurrPage.Update
                        end;
                    }
                }
                field("Sell-to Contact"; "Sell-to Contact")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Contact';
                    Editable = "Sell-to Customer No." <> '';
                    ToolTip = 'Specifies the name of the person to contact at the customer.';
                }
                field("Sell-to Customer Template Code"; "Sell-to Customer Template Code")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Customer Template Code';
                    Enabled = EnableSellToCustomerTemplateCode;
                    Importance = Additional;
                    ToolTip = 'Specifies the code for the template to create a new customer';

                    trigger OnValidate()
                    begin
                        ActivateFields;
                        CurrPage.Update;
                    end;
                }
                field("Sales Area"; "Sales Area")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Promoted;
                }
                field("No. of Archived Versions"; "No. of Archived Versions")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the number of archived versions for this document.';

                    trigger OnDrillDown()
                    begin
                        CurrPage.SaveRecord;
                        Commit;
                        SalesHeaderArchive.SetRange("Document Type", "Document Type"::Quote);
                        SalesHeaderArchive.SetRange("No.", "No.");
                        SalesHeaderArchive.SetRange("Doc. No. Occurrence", "Doc. No. Occurrence");
                        if SalesHeaderArchive.Get("Document Type"::Quote, "No.", "Doc. No. Occurrence", "No. of Archived Versions") then;
                        PAGE.RunModal(PAGE::"Sales List Archive", SalesHeaderArchive);
                        CurrPage.Update(false);
                    end;
                }
                field("Order Date"; "Order Date")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Additional;
                    QuickEntry = false;
                    ToolTip = 'Specifies the date when the related order was created.';
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the date when the related document was created.';
                }
                field("Quote Valid Until Date"; "Quote Valid Until Date")
                {
                    ApplicationArea = Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies how long the quote is valid.';
                }
                field("Due Date"; "Due Date")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Promoted;
                    ToolTip = 'Specifies when the related sales invoice must be paid.';
                }
                field("Requested Delivery Date"; "Requested Delivery Date")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the date that the customer has asked for the order to be delivered.';
                }
                field("Your Reference"; "Your Reference")
                {
                    ApplicationArea = Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the customer''s reference. The content will be printed on sales documents.';
                }
                field("Salesperson Code"; "Salesperson Code")
                {
                    ApplicationArea = Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the name of the salesperson who is assigned to the customer.';

                    trigger OnValidate()
                    begin
                        CurrPage.SalesLines.PAGE.UpdateForm(true)
                    end;
                }
                field("Campaign No."; "Campaign No.")
                {
                    ApplicationArea = RelationshipMgmt;
                    Importance = Additional;
                    QuickEntry = false;
                    ToolTip = 'Specifies the number of the campaign that the document is linked to.';
                }
                field("Opportunity No."; "Opportunity No.")
                {
                    ApplicationArea = RelationshipMgmt;
                    Importance = Additional;
                    QuickEntry = false;
                    ToolTip = 'Specifies the number of the opportunity that the sales quote is assigned to.';
                }

                field("Assigned User ID"; "Assigned User ID")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the ID of the user who is responsible for the document.';
                }
                field(Status; Status)
                {
                    ApplicationArea = Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies whether the document is open, waiting to be approved, has been invoiced for prepayment, or has been released to the next stage of processing.';
                }
                group("Work Description")
                {
                    Caption = 'Work Description';
                    field(WorkDescription; WorkDescription)
                    {
                        ApplicationArea = Basic, Suite;
                        Importance = Additional;
                        MultiLine = true;
                        ShowCaption = false;
                        ToolTip = 'Specifies the products or service being offered';

                        trigger OnValidate()
                        begin
                            SetWorkDescription(WorkDescription);
                        end;
                    }
                }
            }
            part(SalesLines; "Sales Quote Subform")
            {
                ApplicationArea = Basic, Suite;
                Editable = ("Sell-to Customer No." <> '') OR ("Sell-to Customer Template Code" <> '') OR ("Sell-to Contact No." <> '');
                Enabled = ("Sell-to Customer No." <> '') OR ("Sell-to Customer Template Code" <> '') OR ("Sell-to Contact No." <> '');
                SubPageLink = "Document No." = FIELD("No.");
                UpdatePropagation = Both;
            }
            group("Invoice Details")
            {
                Caption = 'Invoice Details';
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = Suite;
                    Importance = Promoted;
                    ToolTip = 'Specifies the currency of amounts on the sales document.';

                    trigger OnAssistEdit()
                    begin
                        Clear(ChangeExchangeRate);
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", WorkDate);
                        if ChangeExchangeRate.RunModal = ACTION::OK then begin
                            Validate("Currency Factor", ChangeExchangeRate.GetParameter);
                            SaveInvoiceDiscountAmount;
                        end;
                        Clear(ChangeExchangeRate);
                    end;

                    trigger OnValidate()
                    begin
                        CurrPage.SaveRecord;
                        SalesCalcDiscByType.ApplyDefaultInvoiceDiscount(0, Rec);
                    end;
                }
                field("Shipment Date"; "Shipment Date")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Promoted;
                    ToolTip = 'Specifies when items on the document are shipped or were shipped. A shipment date is usually calculated from a requested delivery date plus lead time.';
                }
                field("Prices Including VAT"; "Prices Including VAT")
                {
                    ApplicationArea = VAT;
                    ToolTip = 'Specifies if the Unit Price and Line Amount fields on document lines should be shown with or without VAT.';

                    trigger OnValidate()
                    begin
                        CurrPage.Update
                    end;
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the VAT specification of the involved customer or vendor to link transactions made for this record with the appropriate general ledger account according to the VAT posting setup.';

                    trigger OnValidate()
                    var
                        ApplicationAreaMgmtFacade: Codeunit "Application Area Mgmt. Facade";
                    begin
                        if ApplicationAreaMgmtFacade.IsFoundationEnabled then
                            SalesCalcDiscByType.ApplyDefaultInvoiceDiscount(0, Rec);
                    end;
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Promoted;
                    ToolTip = 'Specifies a formula that calculates the payment due date, payment discount date, and payment discount amount.';
                }
                field("Payment Method Code"; "Payment Method Code")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies how to make payment, such as with bank transfer, cash, or check.';
                }
                field("Tax Liable"; "Tax Liable")
                {
                    ApplicationArea = SalesTax;
                    ToolTip = 'Specifies if the customer or vendor is liable for sales tax.';
                }
                field("Tax Area Code"; "Tax Area Code")
                {
                    ApplicationArea = SalesTax;
                    ToolTip = 'Specifies the tax area that is used to calculate and post sales tax.';

                    trigger OnValidate()
                    var
                        SalesHeader: Record "Sales Header";
                    begin
                        if SalesHeader.Get("Document Type", "No.") then
                            CurrPage.SalesLines.PAGE.RedistributeTotalsOnAfterValidate;
                    end;
                }
                group(Control47)
                {
                    ShowCaption = false;
                    Visible = PaymentServiceVisible;
                    field(SelectedPayments; GetSelectedPaymentServicesText)
                    {
                        ApplicationArea = All;
                        Caption = 'Payment Service';
                        Editable = false;
                        Enabled = PaymentServiceEnabled;
                        MultiLine = true;
                        ToolTip = 'Specifies the online payment service, such as PayPal, that customers can use to pay the sales document.';

                        trigger OnAssistEdit()
                        begin
                            ChangePaymentServiceSetting;
                        end;
                    }
                }
                field("Transaction Type"; "Transaction Type")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the type of transaction that the document represents, for the purpose of reporting to INTRASTAT.';
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = Dimensions;
                    ToolTip = 'Specifies the code for Shortcut Dimension 1, which is one of two global dimension codes that you set up in the General Ledger Setup window.';

                    trigger OnValidate()
                    begin
                        CurrPage.Update
                    end;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = Dimensions;
                    ToolTip = 'Specifies the code for Shortcut Dimension 2, which is one of two global dimension codes that you set up in the General Ledger Setup window.';

                    trigger OnValidate()
                    begin
                        CurrPage.Update
                    end;
                }
                field("Payment Discount %"; "Payment Discount %")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the payment discount percentage that is granted if the customer pays on or before the date entered in the Pmt. Discount Date field. The discount percentage is specified in the Payment Terms Code field.';
                }
                field("Pmt. Discount Date"; "Pmt. Discount Date")
                {
                    ApplicationArea = Basic, Suite;
                    Importance = Additional;
                    ToolTip = 'Specifies the date on which the amount in the entry must be paid for a payment discount to be granted.';
                }

            }
            group("Shipping and Billing")
            {
                Caption = 'Shipping and Billing';
                group(Control60)
                {
                    ShowCaption = false;
                    group(Control53)
                    {
                        ShowCaption = false;
                        field(ShippingOptions; ShipToOptions)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Ship-to';
                            OptionCaption = 'Default (Sell-to Address),Alternate Shipping Address,Custom Address';
                            ToolTip = 'Specifies the address that the products on the sales document are shipped to. Default (Sell-to Address): The same as the customer''s sell-to address. Alternate Ship-to Address: One of the customer''s alternate ship-to addresses. Custom Address: Any ship-to address that you specify in the fields below.';

                            trigger OnValidate()
                            var
                                ShipToAddress: Record "Ship-to Address";
                                ShipToAddressList: Page "Ship-to Address List";
                            begin
                                case ShipToOptions of
                                    ShipToOptions::"Default (Sell-to Address)":
                                        begin
                                            Validate("Ship-to Code", '');
                                            CopySellToAddressToShipToAddress;
                                        end;
                                    ShipToOptions::"Alternate Shipping Address":
                                        begin
                                            ShipToAddress.SetRange("Customer No.", "Sell-to Customer No.");
                                            ShipToAddressList.LookupMode := true;
                                            ShipToAddressList.SetTableView(ShipToAddress);

                                            if ShipToAddressList.RunModal = ACTION::LookupOK then begin
                                                ShipToAddressList.GetRecord(ShipToAddress);
                                                Validate("Ship-to Code", ShipToAddress.Code);
                                                IsShipToCountyVisible := FormatAddress.UseCounty(ShipToAddress."Country/Region Code");
                                            end else
                                                ShipToOptions := ShipToOptions::"Custom Address";
                                        end;
                                    ShipToOptions::"Custom Address":
                                        begin
                                            Validate("Ship-to Code", '');
                                            IsShipToCountyVisible := FormatAddress.UseCounty("Ship-to Country/Region Code");
                                        end;
                                end;
                            end;
                        }
                        group(Control72)
                        {
                            ShowCaption = false;
                            Visible = NOT (ShipToOptions = ShipToOptions::"Default (Sell-to Address)");
                            field("Ship-to Code"; "Ship-to Code")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'Code';
                                Editable = ShipToOptions = ShipToOptions::"Alternate Shipping Address";
                                Importance = Promoted;
                                ToolTip = 'Specifies the code for another shipment address than the customer''s own address, which is entered by default.';

                                trigger OnValidate()
                                var
                                    ShipToAddress: Record "Ship-to Address";
                                begin
                                    if (xRec."Ship-to Code" <> '') and ("Ship-to Code" = '') then
                                        Error(EmptyShipToCodeErr);
                                    if "Ship-to Code" <> '' then begin
                                        ShipToAddress.Get("Sell-to Customer No.", "Ship-to Code");
                                        IsShipToCountyVisible := FormatAddress.UseCounty(ShipToAddress."Country/Region Code");
                                    end else
                                        IsShipToCountyVisible := false;
                                end;
                            }
                            field("Ship-to Name"; "Ship-to Name")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'Name';
                                Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                ToolTip = 'Specifies the name that products on the sales document will be shipped to.';
                            }
                            field("Ship-to Address"; "Ship-to Address")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'Address';
                                Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                QuickEntry = false;
                                ToolTip = 'Specifies the address that products on the sales document will be shipped to. By default, the field is filled with the value in the Address field on the customer card or with the value in the Address field in the Ship-to Address window.';
                            }
                            field("Ship-to Address 2"; "Ship-to Address 2")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'Address 2';
                                Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                QuickEntry = false;
                                ToolTip = 'Specifies additional address information.';
                            }
                            field("Ship-to City"; "Ship-to City")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'City';
                                Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                QuickEntry = false;
                                ToolTip = 'Specifies the city of the customer on the sales document.';
                            }
                            group(Control107)
                            {
                                ShowCaption = false;
                                Visible = IsShipToCountyVisible;
                                field("Ship-to County"; "Ship-to County")
                                {
                                    ApplicationArea = Basic, Suite;
                                    Caption = 'County';
                                    Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                    QuickEntry = false;
                                    ToolTip = 'Specifies the county of the address.';
                                }
                            }
                            field("Ship-to Post Code"; "Ship-to Post Code")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'Post Code';
                                Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                QuickEntry = false;
                                ToolTip = 'Specifies the postal code.';
                            }
                            field("Ship-to Country/Region Code"; "Ship-to Country/Region Code")
                            {
                                ApplicationArea = Basic, Suite;
                                Caption = 'Country/Region';
                                Editable = ShipToOptions = ShipToOptions::"Custom Address";
                                Importance = Additional;
                                QuickEntry = false;
                                ToolTip = 'Specifies the customer''s country/region.';

                                trigger OnValidate()
                                begin
                                    IsShipToCountyVisible := FormatAddress.UseCounty("Ship-to Country/Region Code");
                                end;
                            }
                        }
                        field("Ship-to Contact"; "Ship-to Contact")
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Contact';
                            ToolTip = 'Specifies the name of the contact person at the address that products on the sales document will be shipped to.';
                        }
                    }
                    group("Shipment Method")
                    {
                        Caption = 'Shipment Method';
                        field("Shipment Method Code"; "Shipment Method Code")
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Code';
                            Importance = Additional;
                            ToolTip = 'Specifies how items on the sales document are shipped to the customer.';
                        }
                        field("Shipping Agent Code"; "Shipping Agent Code")
                        {
                            ApplicationArea = Suite;
                            Caption = 'Agent';
                            Importance = Additional;
                            ToolTip = 'Specifies which shipping agent is used to transport the items on the sales document to the customer.';
                        }
                        field("Shipping Agent Service Code"; "Shipping Agent Service Code")
                        {
                            ApplicationArea = Suite;
                            Caption = 'Agent service';
                            Importance = Additional;
                            ToolTip = 'Specifies which shipping agent service is used to transport the items on the sales document to the customer.';
                        }

                        field("User Wise Locations"; "User Wise Locations")
                        {
                            ApplicationArea = all;
                        }
                        field("Location Code"; "Location Code")
                        {
                            ApplicationArea = Location;
                            Importance = Additional;
                            Editable = false;
                            ToolTip = 'Specifies the location from where inventory items to the customer on the sales document are to be shipped by default.';
                        }
                        field("Package Tracking No."; "Package Tracking No.")
                        {
                            ApplicationArea = Suite;
                            Importance = Additional;
                            ToolTip = 'Specifies the shipping agent''s package number.';
                        }
                    }
                }
                group(Control49)
                {
                    Enabled = NOT EnableSellToCustomerTemplateCode;
                    ShowCaption = false;
                    field(BillToOptions; BillToOptions)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Bill-to';
                        Editable = false;//b2bpksalecorr10
                        OptionCaption = 'Default (Customer),Another Customer,Custom Address';
                        ToolTip = 'Specifies the customer that the sales invoice will be sent to. Default (Customer): The same as the customer on the sales invoice. Another Customer: Any customer that you specify in the fields below.';

                        trigger OnValidate()
                        begin
                            if BillToOptions = BillToOptions::"Default (Customer)" then begin
                                Validate("Bill-to Customer No.", "Sell-to Customer No.");
                                RecallModifyAddressNotification(GetModifyBillToCustomerAddressNotificationId);
                            end;

                            CopySellToAddressToBillToAddress;
                        end;
                    }
                    group(Control41)
                    {
                        ShowCaption = false;
                        Visible = NOT (BillToOptions = BillToOptions::"Default (Customer)");
                    }
                    field("Bill-to Name"; "Bill-to Name")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Name';
                        Editable = BillToOptions = BillToOptions::"Another Customer";
                        Enabled = EnableBillToCustomerNo;
                        Importance = Promoted;
                        ToolTip = 'Specifies the customer to whom you will send the sales invoice, when different from the customer that you are selling to.';

                        trigger OnValidate()
                        var
                            ApplicationAreaMgmtFacade: Codeunit "Application Area Mgmt. Facade";
                        begin
                            if GetFilter("Bill-to Customer No.") = xRec."Bill-to Customer No." then
                                if "Bill-to Customer No." <> xRec."Bill-to Customer No." then
                                    SetRange("Bill-to Customer No.");

                            CurrPage.SaveRecord;
                            if ApplicationAreaMgmtFacade.IsFoundationEnabled then
                                SalesCalcDiscByType.ApplyDefaultInvoiceDiscount(0, Rec);

                            CurrPage.Update(false);
                        end;
                    }
                    field("Bill-to Address"; "Bill-to Address")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Address';
                        Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the address of the customer that you will send the invoice to.';
                    }
                    field("Bill-to Address 2"; "Bill-to Address 2")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Address 2';
                        Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies additional address information.';
                    }
                    field("Bill-to City"; "Bill-to City")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'City';
                        Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the city of the customer on the sales document.';
                    }
                    group(Control109)
                    {
                        ShowCaption = false;
                        Visible = IsBillToCountyVisible;
                        field("Bill-to County"; "Bill-to County")
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'County';
                            Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                            Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                            Importance = Additional;
                            QuickEntry = false;
                            ToolTip = 'Specifies the county of the address.';
                        }
                    }
                    field("Bill-to Post Code"; "Bill-to Post Code")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Post Code';
                        Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Importance = Additional;
                        QuickEntry = false;
                        ToolTip = 'Specifies the postal code.';
                    }
                    field("Bill-to Country/Region Code"; "Bill-to Country/Region Code")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Country/Region';
                        QuickEntry = false;
                        ToolTip = 'Specifies the customer''s country/region.';

                        trigger OnValidate()
                        begin
                            IsBillToCountyVisible := FormatAddress.UseCounty("Bill-to Country/Region Code");
                        end;
                    }
                    field("Bill-to Contact No."; "Bill-to Contact No.")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Contact No.';
                        Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Importance = Additional;
                        ToolTip = 'Specifies the number of the contact the invoice will be sent to.';
                    }
                    field("Bill-to Contact"; "Bill-to Contact")
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Contact';
                        Editable = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        Enabled = (BillToOptions = BillToOptions::"Custom Address") OR ("Bill-to Customer No." <> "Sell-to Customer No.");
                        ToolTip = 'Specifies the name of the person you should contact at the customer who you are sending the invoice to.';
                    }
                }
            }
            group("Foreign Trade")
            {
                Caption = 'Foreign Trade';
                field("EU 3-Party Trade"; "EU 3-Party Trade")
                {
                    ApplicationArea = BasicEU;
                    ToolTip = 'Specifies if the transaction is related to trade with a third party within the EU.';
                }
                field("Transaction Specification"; "Transaction Specification")
                {
                    ApplicationArea = BasicEU;
                    ToolTip = 'Specifies a specification of the document''s transaction, for the purpose of reporting to INTRASTAT.';
                }
                field("Transport Method"; "Transport Method")
                {
                    ApplicationArea = BasicEU;
                    ToolTip = 'Specifies the transport method, for the purpose of reporting to INTRASTAT.';
                }
                field("Exit Point"; "Exit Point")
                {
                    ApplicationArea = BasicEU;
                    ToolTip = 'Specifies the point of exit through which you ship the items out of your country/region, for reporting to Intrastat.';
                }
                field("Area"; Area)
                {
                    ApplicationArea = BasicEU;
                    ToolTip = 'Specifies the area of the customer or vendor, for the purpose of reporting to INTRASTAT.';
                }
            }
        }
        area(factboxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                SubPageLink = "Table ID" = CONST(36),
                              "No." = FIELD("No."),
                              "Document Type" = FIELD("Document Type");
            }
            part(IncomingDocAttachFactBox; "Incoming Doc. Attach. FactBox")
            {
                ApplicationArea = Basic, Suite;
                ShowFilter = false;
                Visible = false;
            }
            part(Control11; "Pending Approval FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Table ID" = CONST(36),
                              "Document Type" = FIELD("Document Type"),
                              "Document No." = FIELD("No.");
                Visible = OpenApprovalEntriesExistForCurrUser;
            }
            part(Control1903720907; "Sales Hist. Sell-to FactBox")
            {
                ApplicationArea = Basic, Suite;
                SubPageLink = "No." = FIELD("Sell-to Customer No.");
            }
            part(Control1907234507; "Sales Hist. Bill-to FactBox")
            {
                ApplicationArea = Basic, Suite;
                SubPageLink = "No." = FIELD("Bill-to Customer No.");
                Visible = false;
            }
            part(Control1902018507; "Customer Statistics FactBox")
            {
                ApplicationArea = Basic, Suite;
                SubPageLink = "No." = FIELD("Bill-to Customer No.");
                Visible = false;
            }
            part(Control1900316107; "Customer Details FactBox")
            {
                ApplicationArea = Basic, Suite;
                SubPageLink = "No." = FIELD("Sell-to Customer No.");
                Visible = false;
            }
            part(Control1906127307; "Sales Line FactBox")
            {
                ApplicationArea = Suite;
                Provider = SalesLines;
                SubPageLink = "Document Type" = FIELD("Document Type"),
                              "Document No." = FIELD("Document No."),
                              "Line No." = FIELD("Line No.");
            }
            part(Control1901314507; "Item Invoicing FactBox")
            {
                ApplicationArea = Basic, Suite;
                Provider = SalesLines;
                SubPageLink = "No." = FIELD("No.");
                Visible = false;
            }
            part(ApprovalFactBox; "Approval FactBox")
            {
                ApplicationArea = Suite;
                Visible = false;
            }
            part(Control1907012907; "Resource Details FactBox")
            {
                ApplicationArea = Basic, Suite;
                Provider = SalesLines;
                SubPageLink = "No." = FIELD("No.");
                Visible = false;
            }
            part(WorkflowStatus; "Workflow Status FactBox")
            {
                ApplicationArea = All;
                Editable = false;
                Enabled = false;
                ShowFilter = false;
                Visible = ShowWorkflowStatus;
            }
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
                Visible = false;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Quote")
            {
                Caption = '&Quote';
                Image = Quote;
                action(Approvals)
                {
                    AccessByPermission = TableData "Approval Entry" = R;
                    ApplicationArea = Suite;
                    Caption = 'Approvals';
                    Image = Approvals;
                    Promoted = true;
                    PromotedCategory = Category4;
                    ToolTip = 'View a list of the records that are waiting to be approved. For example, you can see who requested the record to be approved, when it was sent, and when it is due to be approved.';

                    trigger OnAction()
                    var
                        WorkflowsEntriesBuffer: Record "Workflows Entries Buffer";
                    begin
                        WorkflowsEntriesBuffer.RunWorkflowEntriesPage(RecordId, DATABASE::"Sales Header", "Document Type", "No.");
                    end;
                }
                action(Dimensions)
                {
                    AccessByPermission = TableData Dimension = R;
                    ApplicationArea = Dimensions;
                    Caption = 'Dimensions';
                    Enabled = "No." <> '';
                    Image = Dimensions;
                    Promoted = true;
                    PromotedCategory = Category4;
                    PromotedIsBig = true;
                    ShortCutKey = 'Alt+D';
                    ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                    trigger OnAction()
                    begin
                        ShowDocDim;
                        CurrPage.SaveRecord;
                    end;
                }
            }
            group("&View")
            {
                Caption = '&View';
                action(Customer)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Customer';
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = Customer;
                    Promoted = true;
                    PromotedCategory = Category11;
                    RunObject = Page "Customer Card";
                    RunPageLink = "No." = FIELD("Sell-to Customer No."),
                                  "Date Filter" = FIELD("Date Filter");
                    ShortCutKey = 'Shift+F7';
                    ToolTip = 'View or edit detailed information about the customer on the sales document.';
                }
                action("C&ontact")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'C&ontact';
                    Image = Card;
                    Promoted = true;
                    PromotedCategory = Category11;
                    RunObject = Page "Contact Card";
                    RunPageLink = "No." = FIELD("Sell-to Contact No.");
                    ToolTip = 'View or edit detailed information about the contact person at the customer.';
                }
            }
            group(History)
            {
                Caption = 'History';
                action(PageInteractionLogEntries)
                {
                    ApplicationArea = Suite;
                    Caption = 'Interaction Log E&ntries';
                    Image = InteractionLog;
                    //The property 'PromotedCategory' can only be set if the property 'Promoted' is set to 'true'
                    //PromotedCategory = Category8;
                    ShortCutKey = 'Ctrl+F7';
                    ToolTip = 'View a list of interaction log entries related to this document.';

                    trigger OnAction()
                    begin
                        ShowInteractionLogEntries;
                    end;
                }
            }
        }
        area(processing)
        {
            group(Action59)
            {
                Caption = '&Quote';
                Image = Quote;
                action(Statistics)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Statistics';
                    Enabled = "No." <> '';
                    Image = Statistics;
                    Promoted = true;
                    PromotedCategory = Category4;
                    PromotedIsBig = true;
                    ShortCutKey = 'F7';
                    ToolTip = 'View statistical information, such as the value of posted entries, for the record.';

                    trigger OnAction()
                    var
                        Handled: Boolean;
                    begin
                        OnBeforeStatisticsAction(Rec, Handled);
                        if not Handled then begin
                            CalcInvDiscForHeader;
                            Commit;
                            PAGE.RunModal(PAGE::"Sales Statistics", Rec);
                            SalesCalcDiscByType.ResetRecalculateInvoiceDisc(Rec);
                        end
                    end;
                }
                action("Co&mments")
                {
                    ApplicationArea = Comments;
                    Caption = 'Co&mments';
                    Image = ViewComments;
                    Promoted = true;
                    PromotedCategory = Category4;
                    RunObject = Page "Sales Comment Sheet";
                    RunPageLink = "Document Type" = FIELD("Document Type"),
                                  "No." = FIELD("No."),
                                  "Document Line No." = CONST(0);
                    ToolTip = 'View or add comments for the record.';
                }
                action(Print)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = '&Print';
                    Ellipsis = true;
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = Print;
                    Promoted = true;
                    PromotedCategory = Category9;
                    ToolTip = 'Prepare to print the document. A report request window for the document opens where you can specify what to include on the print-out.';
                    Visible = NOT IsOfficeAddin;

                    trigger OnAction()
                    begin
                        CheckSalesCheckAllLinesHaveQuantityAssigned;
                        DocPrint.PrintSalesHeader(Rec);
                    end;
                }
                action(Email)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Send by &Email';
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = Email;
                    Promoted = true;
                    PromotedCategory = Category9;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Prepare to mail the document. The Send Email window opens prefilled with the customer''s email address so you can add or edit information.';

                    trigger OnAction()
                    begin
                        CheckSalesCheckAllLinesHaveQuantityAssigned;
                        if not Find then
                            Insert(true);
                        DocPrint.EmailSalesHeader(Rec);
                    end;
                }
                action(GetRecurringSalesLines)
                {
                    ApplicationArea = Suite;
                    Caption = 'Get Recurring Sales Lines';
                    Ellipsis = true;
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = CustomerCode;
                    ToolTip = 'Get standard sales lines that are available to assign to customers.';

                    trigger OnAction()
                    var
                        StdCustSalesCode: Record "Standard Customer Sales Code";
                    begin
                        StdCustSalesCode.InsertSalesLines(Rec);
                    end;
                }
                action(CopyDocument)
                {
                    ApplicationArea = Suite;
                    Caption = 'Copy Document';
                    Ellipsis = true;
                    Enabled = "No." <> '';
                    Image = CopyDocument;
                    Promoted = true;
                    PromotedCategory = Process;
                    ToolTip = 'Copy document lines and header information from another sales document to this document. You can copy a posted sales invoice into a new sales invoice to quickly create a similar document.';

                    trigger OnAction()
                    begin
                        if not Find then begin
                            Insert(true);
                            Commit;
                        end;
                        CopySalesDoc.SetSalesHeader(Rec);
                        CopySalesDoc.RunModal;
                        Clear(CopySalesDoc);
                        if Get("Document Type", "No.") then;
                    end;
                }
                action(DocAttach)
                {
                    ApplicationArea = All;
                    Caption = 'Attachments';
                    Image = Attach;
                    Promoted = true;
                    PromotedCategory = Category4;
                    ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                    trigger OnAction()
                    var
                        DocumentAttachmentDetails: Page "Document Attachment Details";
                        RecRef: RecordRef;
                    begin
                        RecRef.GetTable(Rec);
                        DocumentAttachmentDetails.OpenForRecRef(RecRef);
                        DocumentAttachmentDetails.RunModal;
                    end;
                }
            }
            group(Approval)
            {
                Caption = 'Approval';
                action(Approve)
                {
                    ApplicationArea = All;
                    Caption = 'Approve';
                    Image = Approve;
                    Promoted = true;
                    PromotedCategory = Category6;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Approve the requested changes.';
                    Visible = OpenApprovalEntriesExistForCurrUser;

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                    begin
                        ApprovalsMgmt.ApproveRecordApprovalRequest(RecordId);
                    end;
                }
                action(Reject)
                {
                    ApplicationArea = All;
                    Caption = 'Reject';
                    Image = Reject;
                    Promoted = true;
                    PromotedCategory = Category6;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Reject the approval request.';
                    Visible = OpenApprovalEntriesExistForCurrUser;

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                    begin
                        ApprovalsMgmt.RejectRecordApprovalRequest(RecordId);
                    end;
                }
                action(Delegate)
                {
                    ApplicationArea = All;
                    Caption = 'Delegate';
                    Image = Delegate;
                    Promoted = true;
                    PromotedCategory = Category6;
                    PromotedOnly = true;
                    ToolTip = 'Delegate the approval to a substitute approver.';
                    Visible = OpenApprovalEntriesExistForCurrUser;

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                    begin
                        ApprovalsMgmt.DelegateRecordApprovalRequest(RecordId);
                    end;
                }
                action(Comment)
                {
                    ApplicationArea = All;
                    Caption = 'Comments';
                    Image = ViewComments;
                    Promoted = true;
                    PromotedCategory = Category6;
                    PromotedOnly = true;
                    ToolTip = 'View or add comments for the record.';
                    Visible = OpenApprovalEntriesExistForCurrUser;

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                    begin
                        ApprovalsMgmt.GetApprovalComment(Rec);
                    end;
                }
            }
            group(Create)
            {
                Caption = 'Create';
                Image = NewCustomer;
                action(MakeOrder)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Make &Order';
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = MakeOrder;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Convert the sales quote to a sales order.';

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        SalLine: Record "Sales Line";
                        ItemLedEntry: Record "Item Ledger Entry";
                        ItemQty: Decimal;
                        Textlbl: Text[250];
                        Gitem: Record Item;
                        ItemInv: Record "Inventory Posting Setup";
                        Customer: Record Customer;
                        salesHDR, salesHDR2 : Record "Sales Header";
                        salesLINErec: record "Sales Line";
                    begin
                        //FIX11Jun2021>>
                        Customer.Get("Sell-to Customer No.");
                        if (Customer."Bill-to Customer No." <> '') then
                            if Customer."Bill-to Customer No." <> "Bill-to Customer No." then
                                TestField("Bill-to Customer No.", Customer."Bill-to Customer No.");
                        if Customer."Bill-to Customer No." = '' then
                            TestField("Bill-to Customer No.", "Sell-to Customer No.");
                        //FIX11Jun2021<<
                        CheckSalesMandValues(True);
                        //>>>>>>> 12/7/24 CAS-01319-C7K2P6
                        //Rec.TestField(Status, Rec.Status::Released);
                        // UpdateFixedDiscount();
                        //>>>>>>> 12/7/24 CAS-01319-C7K2P6
                        //UpdateFixedDiscount();
                        //260424 3 G2S
                        //CalcRebateAmountonLines(); // >>>>>> G2S 05/11/2024 CAS-01367-P9K8C9
                        //G2s 260424 3 G2S

                        if ApprovalsMgmt.PrePostApprovalCheckSales(Rec) then
                            CODEUNIT.Run(CODEUNIT::"Sales-Quote to Order (Yes/No)c", Rec);

                    end;
                }
                action(MakeInvoice)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Make Invoice';
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = MakeOrder;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Convert the sales quote to a sales invoice.';

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                    begin
                        if ApprovalsMgmt.PrePostApprovalCheckSales(Rec) then begin
                            CheckSalesCheckAllLinesHaveQuantityAssigned;
                            CODEUNIT.Run(CODEUNIT::"Sales-Quote to Invoice Yes/No", Rec);
                        end;
                    end;
                }
                action("C&reate Customer")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'C&reate Customer';
                    Image = NewCustomer;
                    ToolTip = 'Create a new customer card for the contact.';
                    visible = false;//b2bpksalecorr10
                    trigger OnAction()
                    begin
                        if CheckCustomerCreated(false) then
                            CurrPage.Update(true);
                    end;
                }
                action("Create &Task")
                {
                    AccessByPermission = TableData Contact = R;
                    ApplicationArea = Basic, Suite;
                    Caption = 'Create &Task';
                    Image = NewToDo;
                    ToolTip = 'Create a new marketing task for the contact.';

                    trigger OnAction()
                    begin
                        CreateTask;
                    end;
                }
            }
            group(Action3)
            {
                Caption = 'Release';
                Image = ReleaseDoc;
                action(Release)
                {
                    ApplicationArea = Suite;
                    Caption = 'Re&lease';
                    Image = ReleaseDoc;
                    Promoted = true;
                    PromotedCategory = Category10;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ShortCutKey = 'Ctrl+F9';
                    ToolTip = 'Release the document to the next stage of processing. When a document is released, it will be included in all availability calculations from the expected receipt date of the items. You must reopen the document before you can make changes to it.';

                    trigger OnAction()
                    var
                        ReleaseSalesDoc: Codeunit "Release Sales Document";
                        Customer: Record Customer;
                        //260424
                        SalesH: Record "Sales Header";
                        salesLINErec, salesLINErecCopy : record "Sales Line";
                        TotalSalesAmt, InvoiceDiscount, RemainingDiscAmt, RebateDiscountPayable, InitialInvDiscAmt, InvoiceDiscountTotal : Decimal;
                    //260424
                    begin
                        //FIX11Jun2021>>
                        Customer.Get("Sell-to Customer No.");
                        if (Customer."Bill-to Customer No." <> '') then
                            if Customer."Bill-to Customer No." <> "Bill-to Customer No." then
                                TestField("Bill-to Customer No.", Customer."Bill-to Customer No.");
                        if Customer."Bill-to Customer No." = '' then
                            TestField("Bill-to Customer No.", "Sell-to Customer No.");
                        //>>>>>>> 12/7/24 CAS-01319-C7K2P6
                        UpdateFixedDiscount();
                        //>>>>>>> 12/7/24 CAS-01319-C7K2P6
                        //FIX11Jun2021<<
                        ReleaseSalesDoc.PerformManualRelease(Rec);

                        // //g2s 260424 1
                        // InvoiceDiscount := 0;
                        // InvoiceDiscountTotal := 0;
                        // RebateDiscountPayable := 0;
                        // RemainingDiscAmt := 0;
                        // TotalSalesAmt := 0;
                        // SalesH.Reset();
                        // SalesH.SetRange("No.", Rec."No.");
                        // SalesH.SetFilter("Initial Rebate Disc. Calc", '<>%1', 0);
                        // if SalesH.FindFirst() then begin
                        //     RebateDiscountPayable := SalesH."Initial Rebate Disc. Calc";
                        //     salesLINErec.Reset();
                        //     salesLINErec.setrange("Document No.", Rec."No.");
                        //     salesLINErec.Calcsums("Unit Price", Quantity, "Line Amount", Amount, "Inv. Discount Amount", "Amount Including VAT");
                        //     TotalSalesAmt := salesLINErec."Unit Price" * salesLINErec.Quantity;
                        //     InvoiceDiscount := 0.047 * TotalSalesAmt;
                        //     TotalSalesAmt -= InvoiceDiscount;
                        //     if RebateDiscountPayable >= TotalSalesAmt then begin
                        //         RemainingDiscAmt := RebateDiscountPayable - TotalSalesAmt;
                        //         // Rec.Amount := 0;
                        //         // Rec.Validate(Amount);
                        //         salesLINErecCopy.Reset();
                        //         salesLINErecCopy.SetRange("Document No.", Rec."No.");
                        //         if salesLINErecCopy.FindFirst() then begin
                        //             repeat
                        //                 salesLINErecCopy."Rebate Discount" := (salesLINErecCopy.Quantity * salesLINErecCopy."Unit Price");
                        //                 salesLINErecCopy."Rebate Discount" -= InvoiceDiscount;
                        //                 // salesLINErecCopy."Inv. Discount Amount" := 0.047 * (salesLINErecCopy."Line Amount" + salesLINErecCopy."Rebate Discount");
                        //                 // Rec.Validate("Inv. Discount Amount");
                        //                 InvoiceDiscountTotal += salesLINErecCopy."Rebate Discount";
                        //                 salesLINErecCopy.Modify();
                        //             until salesLINErecCopy.Next() = 0;
                        //         end;
                        //     end else begin
                        //         salesLINErecCopy.Reset();
                        //         salesLINErecCopy.SetRange("Document No.", Rec."No.");
                        //         if salesLINErecCopy.FindFirst() then begin
                        //             repeat
                        //                 salesLINErecCopy."Rebate Discount" := ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01);
                        //                 //   Rec.Amount -= Rec."Rebate Discount";
                        //                 //   Rec.Validate(Amount);

                        //                 salesLINErecCopy."Inv. Discount Amount" += salesLINErecCopy."Rebate Discount";
                        //                 // salesLINErecCopy.Validate("Inv. Discount Amount");
                        //                 salesLINErecCopy.Modify();
                        //             until salesLINErecCopy.Next() = 0;
                        //         end;
                        //     end;
                        //     // InvoiceDiscountAmount
                        //     InvoiceDiscountTotal += InvoiceDiscount;
                        //     // ValidateInvoiceDiscountAmount2(InvoiceDiscountTotal);
                        //     // Rec.Quantity := SetQty;
                        //     // Rec.Modify();
                        //     // CurrPage.SaveRecord();

                        // end;

                        // // G2S 260424


                    end;
                }
                action(Reopen)
                {
                    ApplicationArea = Suite;
                    Caption = 'Re&open';
                    Enabled = Status <> Status::Open;
                    Image = ReOpen;
                    Promoted = true;
                    PromotedCategory = Category10;
                    PromotedOnly = true;
                    ToolTip = 'Reopen the document to change it after it has been approved. Approved documents have the Released status and must be opened before they can be changed.';

                    trigger OnAction()
                    var
                        ReleaseSalesDoc: Codeunit "Release Sales Document";
                    begin
                        ReleaseSalesDoc.PerformManualReopen(Rec);
                    end;
                }
            }
            group("Request Approval")
            {
                Caption = 'Request Approval';
                Image = Approval;
                action(SendApprovalRequest)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Send A&pproval Request';
                    Enabled = NOT OpenApprovalEntriesExist AND CanRequestApprovalForFlow;
                    Image = SendApprovalRequest;
                    Promoted = true;
                    PromotedCategory = Category7;
                    PromotedIsBig = true;
                    ToolTip = 'Request approval of the document.';

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        Customer: Record Customer;
                    begin
                        //FIX11Jun2021>>
                        Customer.Get("Sell-to Customer No.");
                        if (Customer."Bill-to Customer No." <> '') then
                            if Customer."Bill-to Customer No." <> "Bill-to Customer No." then
                                TestField("Bill-to Customer No.", Customer."Bill-to Customer No.");
                        if Customer."Bill-to Customer No." = '' then
                            TestField("Bill-to Customer No.", "Sell-to Customer No.");
                        //FIX11Jun2021<<
                        if ApprovalsMgmt.CheckSalesApprovalPossible(Rec) then
                            ApprovalsMgmt.OnSendSalesDocForApproval(Rec);
                    end;
                }
                action(CancelApprovalRequest)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Cancel Approval Re&quest';
                    Enabled = CanCancelApprovalForRecord OR CanCancelApprovalForFlow;
                    Image = CancelApprovalRequest;
                    Promoted = true;
                    PromotedCategory = Category7;
                    ToolTip = 'Cancel the approval request.';

                    trigger OnAction()
                    var
                        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        WorkflowWebhookMgt: Codeunit "Workflow Webhook Management";
                    begin
                        ApprovalsMgmt.OnCancelSalesApprovalRequest(Rec);
                        WorkflowWebhookMgt.FindAndCancel(RecordId);
                    end;
                }
                group(Flow)
                {
                    Caption = 'Flow';
                    Image = Flow;
                    action(CreateFlow)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Create a Flow';
                        Image = Flow;
                        Promoted = true;
                        PromotedCategory = Category7;
                        ToolTip = 'Create a new Flow from a list of relevant Flow templates.';
                        Visible = IsSaaS;

                        trigger OnAction()
                        var
                            FlowServiceManagement: Codeunit "Flow Service Management";
                            FlowTemplateSelector: Page "Flow Template Selector";
                        begin
                            // Opens page 6400 where the user can use filtered templates to create new flows.
                            FlowTemplateSelector.SetSearchText(FlowServiceManagement.GetSalesTemplateFilter);
                            FlowTemplateSelector.Run;
                        end;
                    }
                    action(SeeFlows)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'See my Flows';
                        Image = Flow;
                        Promoted = true;
                        PromotedCategory = Category7;
                        RunObject = Page "Flow Selector";
                        ToolTip = 'View and configure Flows that you created.';
                    }
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Image = "Action";
                action(CalculateInvoiceDiscount)
                {
                    AccessByPermission = TableData "Cust. Invoice Disc." = R;
                    ApplicationArea = Basic, Suite;
                    Caption = 'Calculate &Invoice Discount';
                    Enabled = IsCustomerOrContactNotEmpty;
                    Image = CalculateInvoiceDiscount;
                    ToolTip = 'Calculate the invoice discount that applies to the sales quote.';

                    trigger OnAction()
                    begin
                        ApproveCalcInvDisc;
                        SalesCalcDiscByType.ResetRecalculateInvoiceDisc(Rec);
                    end;
                }
                separator(Action139)
                {
                }
                action("Archive Document")
                {
                    ApplicationArea = Suite;
                    Caption = 'Archi&ve Document';
                    Image = Archive;
                    ToolTip = 'Send the document to the archive, for example because it is too soon to delete it. Later, you delete or reprocess the archived document.';

                    trigger OnAction()
                    begin
                        ArchiveManagement.ArchiveSalesDocument(Rec);
                        CurrPage.Update(false);
                    end;
                }
                group(IncomingDocument)
                {
                    Caption = 'Incoming Document';
                    Image = Documents;
                    action(IncomingDocCard)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'View Incoming Document';
                        Enabled = HasIncomingDocument;
                        Image = ViewOrder;
                        ToolTip = 'View any incoming document records and file attachments that exist for the entry or document.';

                        trigger OnAction()
                        var
                            IncomingDocument: Record "Incoming Document";
                        begin
                            IncomingDocument.ShowCardFromEntryNo("Incoming Document Entry No.");
                        end;
                    }
                    action(SelectIncomingDoc)
                    {
                        AccessByPermission = TableData "Incoming Document" = R;
                        ApplicationArea = Basic, Suite;
                        Caption = 'Select Incoming Document';
                        Image = SelectLineToApply;
                        ToolTip = 'Select an incoming document record and file attachment that you want to link to the entry or document.';

                        trigger OnAction()
                        var
                            IncomingDocument: Record "Incoming Document";
                        begin
                            Validate("Incoming Document Entry No.", IncomingDocument.SelectIncomingDocument("Incoming Document Entry No.", RecordId));
                        end;
                    }
                    action(IncomingDocAttachFile)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Create Incoming Document from File';
                        Ellipsis = true;
                        Enabled = NOT HasIncomingDocument;
                        Image = Attach;
                        ToolTip = 'Create an incoming document record by selecting a file to attach, and then link the incoming document record to the entry or document.';

                        trigger OnAction()
                        var
                            IncomingDocumentAttachment: Record "Incoming Document Attachment";
                        begin
                            IncomingDocumentAttachment.NewAttachmentFromSalesDocument(Rec);
                        end;
                    }
                    action(RemoveIncomingDoc)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Remove Incoming Document';
                        Enabled = HasIncomingDocument;
                        Image = RemoveLine;
                        ToolTip = 'Remove any incoming document records and file attachments.';

                        trigger OnAction()
                        var
                            IncomingDocument: Record "Incoming Document";
                        begin
                            if IncomingDocument.Get("Incoming Document Entry No.") then
                                IncomingDocument.RemoveLinkToRelatedRecord;
                            "Incoming Document Entry No." := 0;
                            Modify(true);
                        end;
                    }
                }
            }
        }
    }

    trigger OnAfterGetCurrRecord()
    begin
        ActivateFields;
        CurrPage.IncomingDocAttachFactBox.PAGE.LoadDataFromRecord(Rec);
        CurrPage.ApprovalFactBox.PAGE.UpdateApprovalEntriesFromSourceRecord(RecordId);
        ShowWorkflowStatus := CurrPage.WorkflowStatus.PAGE.SetFilterOnWorkflowRecord(RecordId);
        UpdatePaymentService;
        SetControlAppearance;
    end;

    trigger OnAfterGetRecord()
    begin
        ActivateFields;
        SetControlAppearance;
        WorkDescription := GetWorkDescription;
        UpdateShipToBillToGroupVisibility;
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        CurrPage.SaveRecord;
        exit(ConfirmDeletion);
    end;

    trigger OnInit()
    begin
        EnableBillToCustomerNo := true;
        EnableSellToCustomerTemplateCode := true;
        "Sales Type" := "Sales Type"::"Local";
    end;

    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    begin
        if DocNoVisible then
            CheckCreditMaxBeforeInsert;

        if ("Sell-to Customer No." = '') and (GetFilter("Sell-to Customer No.") <> '') then
            CurrPage.Update(false);
        "Sales Type" := "Sales Type"::"Local";
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        xRec.Init;
        "Responsibility Center" := UserMgt.GetSalesFilter;
        "Sales Type" := "Sales Type"::"Local";
        if (not DocNoVisible) and ("No." = '') then
            SetSellToCustomerFromFilter;

        SetDefaultPaymentServices;
        SetControlAppearance;
        UpdateShipToBillToGroupVisibility;
    end;

    trigger OnOpenPage()
    var
        PaymentServiceSetup: Record "Payment Service Setup";
        OfficeMgt: Codeunit "Office Management";
        EnvironmentInfo: Codeunit "Environment Information";
        BuildFilter: Text[200];
        UserwiRespCentr: Codeunit Userwisefilterperm;
        GLSetup: Record "General Ledger Setup";
    begin
        // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024
        // if ConfirmSalesType() then
        //     ValidateDocAccess(Rec."Sales Type");
        // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024
        //b2bpksalecorr11 start
        GLSetup.Get();
        BuildFilter := UserwiRespCentr.BuildRespCentFilter();
        if BuildFilter <> '' then begin
            IF GLSetup."Page Other Filters" <> '' then
                BuildFilter := GLSetup."Page Other Filters" + BuildFilter;
            FilterGroup(2);
            SetFilter("Responsibility Center", BuildFilter);
            FilterGroup(0);
        end;
        /*
            if UserMgt.GetSalesFilter <> '' then begin
            FilterGroup(2);
            SetRange("Responsibility Center", UserMgt.GetSalesFilter);
            FilterGroup(0);*/
        //b2bpksalecorr11 End

        SetRange("Date Filter", 0D, WorkDate());

        ActivateFields;

        SetDocNoVisible;
        IsOfficeAddin := OfficeMgt.IsAvailable;
        SetControlAppearance;
        IsSaaS := EnvironmentInfo.IsSaaS;
        PaymentServiceVisible := PaymentServiceSetup.IsPaymentServiceVisible;
    end;

    var
        SalesHeaderArchive: Record "Sales Header Archive";
        CopySalesDoc: Report "Copy Sales Document";
        DocPrint: Codeunit "Document-Print";
        UserMgt: Codeunit "User Setup Management";
        ArchiveManagement: Codeunit ArchiveManagement;
        SalesCalcDiscByType: Codeunit "Sales - Calc Discount By Type";
        CustomerMgt: Codeunit "Customer Mgt.";
        FormatAddress: Codeunit "Format Address";
        ChangeExchangeRate: Page "Change Exchange Rate";
        [InDataSet]
        EnableBillToCustomerNo: Boolean;
        EnableSellToCustomerTemplateCode: Boolean;
        HasIncomingDocument: Boolean;
        DocNoVisible: Boolean;
        OpenApprovalEntriesExistForCurrUser: Boolean;
        OpenApprovalEntriesExist: Boolean;
        ShowWorkflowStatus: Boolean;
        IsOfficeAddin: Boolean;
        CanCancelApprovalForRecord: Boolean;
        PaymentServiceVisible: Boolean;
        PaymentServiceEnabled: Boolean;
        IsCustomerOrContactNotEmpty: Boolean;
        WorkDescription: Text;
        ShipToOptions: Option "Default (Sell-to Address)","Alternate Shipping Address","Custom Address";
        BillToOptions: Option "Default (Customer)","Another Customer","Custom Address";
        EmptyShipToCodeErr: Label 'The Code field can only be empty if you select Custom Address in the Ship-to field.';
        CanRequestApprovalForFlow: Boolean;
        CanCancelApprovalForFlow: Boolean;
        IsSaaS: Boolean;
        IsBillToCountyVisible: Boolean;
        IsSellToCountyVisible: Boolean;
        IsShipToCountyVisible: Boolean;

    local procedure ActivateFields()
    begin
        EnableBillToCustomerNo := "Bill-to Customer Template Code" = '';
        EnableSellToCustomerTemplateCode := "Sell-to Customer No." = '';
        IsBillToCountyVisible := FormatAddress.UseCounty("Bill-to Country/Region Code");
        IsSellToCountyVisible := FormatAddress.UseCounty("Sell-to Country/Region Code");
        IsShipToCountyVisible := FormatAddress.UseCounty("Ship-to Country/Region Code");
    end;

    local procedure ApproveCalcInvDisc()
    begin
        CurrPage.SalesLines.PAGE.ApproveCalcInvDisc;
    end;

    local procedure SaveInvoiceDiscountAmount()
    var
        DocumentTotals: Codeunit "Document Totals";
    begin
        CurrPage.SaveRecord;
        DocumentTotals.SalesRedistributeInvoiceDiscountAmountsOnDocument(Rec);
        CurrPage.Update(false);
    end;

    local procedure ClearSellToFilter()
    begin
        if GetFilter("Sell-to Customer No.") = xRec."Sell-to Customer No." then
            if "Sell-to Customer No." <> xRec."Sell-to Customer No." then
                SetRange("Sell-to Customer No.");
        if GetFilter("Sell-to Contact No.") = xRec."Sell-to Contact No." then
            if "Sell-to Contact No." <> xRec."Sell-to Contact No." then
                SetRange("Sell-to Contact No.");
    end;

    local procedure SetDocNoVisible()
    var
        DocumentNoVisibility: Codeunit DocumentNoVisibility;
        DocType: Option Quote,"Order",Invoice,"Credit Memo","Blanket Order","Return Order",Reminder,FinChMemo;
    begin
        DocNoVisible := DocumentNoVisibility.SalesDocumentNoIsVisible(DocType::Quote, "No.");
    end;

    local procedure SetControlAppearance()
    var
        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
        WorkflowWebhookMgt: Codeunit "Workflow Webhook Management";
    begin
        HasIncomingDocument := "Incoming Document Entry No." <> 0;

        OpenApprovalEntriesExistForCurrUser := ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(RecordId);
        OpenApprovalEntriesExist := ApprovalsMgmt.HasOpenApprovalEntries(RecordId);
        CanCancelApprovalForRecord := ApprovalsMgmt.CanCancelApprovalForRecord(RecordId);
        IsCustomerOrContactNotEmpty := ("Sell-to Customer No." <> '') or ("Sell-to Contact No." <> '');

        WorkflowWebhookMgt.GetCanRequestAndCanCancel(RecordId, CanRequestApprovalForFlow, CanCancelApprovalForFlow);
    end;



    //260424 2
    //g2sAdded function to drop rebate discount 270324
    procedure CalcRebateAmountonLines()
    var
        ReleaseSalesDoc: Codeunit "Release Sales Document";
        rebateRecordTab: Record "Rebate Records";
        rebatePeriodCodes: Record "Rebate Period Codes";
        salesHeaderRec: Record "Sales header";
        RemainingDiscAmt: Decimal;

        kdCustFocus: Record "KD Cust. Focus Brands ";
        MonthlyRebateSetup: Record "Monthly Rebate Setup";
        salesLINErec, salesLINErecCopy, salesLINErecCopy2 : record "Sales Line";
        startDate: Code[20];
        InvoiceDiscount, RebateDiscountPayable, TotalSalesAmt : Decimal;
        salesHDR, salesHDR2 : Record "Sales Header";
        RebateAmtFound, BalRebateAmtFound : Boolean;
        count: Integer;
        CustomSetup: Record "Custom Setup";
        CustSetupErr: Label 'KD rebate VAT setup is missing. Please check the setup.';
        CustSetupErrVAT: Label 'KD discount VAT combination is missing. Please check the VAT posting setup.';
        VATPostingSetup: Record "VAT Posting Setup";
    begin
        CurrPage.update(true);
        InvoiceDiscount := 0;
        RebateDiscountPayable := 0;
        RemainingDiscAmt := 0;
        TotalSalesAmt := 0;
        RebateDiscountPayable := salesHDR.pullDiscount(Rec);

        if RebateDiscountPayable <> 0 then begin
            //newly added today 260424
            Rec."Initial Rebate Disc. Calc" := RebateDiscountPayable;
            Rec.Modify();


            salesLINErec.Reset();
            salesLINErec.setrange("Document No.", Rec."No.");
            salesLINErec.Calcsums("Line Amount", Amount, "Inv. Discount Amount", "Amount Including VAT", AmtafterRebate, AmtafterRebateIncVAT);
            //290424
            if salesLINErec.AmtafterRebate <> 0 then
                TotalSalesAmt := salesLINErec.AmtafterRebate else
                TotalSalesAmt := salesLINErec.Amount;
            // TotalSalesAmt := salesLINErec."Amount Including VAT";
            //290424
            if RebateDiscountPayable > TotalSalesAmt then begin

                Error('The sub total %1 must be more than the variable rebate %2', TotalSalesAmt, RebateDiscountPayable);

                RemainingDiscAmt := RebateDiscountPayable - TotalSalesAmt;

                //rev sales Lines VAT
                CustomSetup.Reset();
                CustomSetup.SetRange(Category, CustomSetup.Category::"KD Rebate");
                CustomSetup.SetFilter("100% Disc. VAT Bus. Post Grp", '<>%1', '');
                CustomSetup.SetFilter("100% Disc. VAT Prod. Post Grp", '<>%1', '');
                if not CustomSetup.findfirst then Error(CustSetupErr);
                salesLINErecCopy2.Reset();
                salesLINErecCopy2.SetRange("Document No.", Rec."No.");
                //salesLINErec.SetRange("VAT Bus. Posting Group", CustomSetup."100% Disc. VAT Bus. Post Grp");
                salesLINErecCopy2.SetFilter("VAT Prod. Posting Group", '<>%1', CustomSetup."100% Disc. VAT Prod. Post Grp");
                if salesLINErecCopy2.findset then begin
                    VATPostingSetup.Reset();
                    VATPostingSetup.SetRange("VAT Bus. Posting Group", CustomSetup."100% Disc. VAT Bus. Post Grp");
                    VATPostingSetup.SetRange("VAT Prod. Posting Group", CustomSetup."100% Disc. VAT Prod. Post Grp");
                    if not VATPostingSetup.findfirst then ERROR(CustSetupErrVAT);
                    repeat
                        salesLINErecCopy2.Validate("VAT Prod. Posting Group", CustomSetup."100% Disc. VAT Prod. Post Grp");
                        salesLINErecCopy2.Modify();
                    until salesLINErecCopy2.Next() = 0;
                end;

                // salesLINErec.Modifyall(Amount, 0);
                // salesLINErec.ModifyAll("Amount Including VAT", 0);
                // salesLINErec.ModifyAll("Outstanding Amount", 0);
                // salesLINErec.ModifyAll("Outstanding Amount (LCY)", 0);
                // salesLINErec.reset();
                // salesLINErec.setrange("Document No.", Rec."No.");
                salesLINErecCopy.Reset();
                salesLINErecCopy.setrange("Document No.", Rec."No.");
                if salesLINErecCopy.findfirst() then begin
                    repeat
                        // salesLINErecCopy."Rebate Discount" := ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01) - salesLINErecCopy."Inv. Discount Amount";
                        //start 290424
                        if salesLINErecCopy.AmtafterRebate <> 0 then
                            salesLINErecCopy.validate("Rebate Discount", salesLINErecCopy.AmtafterRebate) else begin
                            salesLINErecCopy.validate("Rebate Discount", salesLINErecCopy.Amount);
                            // salesLINErecCopy.validate("Line Discount Amount", salesLINErecCopy.Amount);
                        end;

                        //salesLINErecCopy.validate("Rebate Disc. Amount to Inv.", salesLINErecCopy."Rebate Discount"); //salesLINErecCopy.Amount;
                        //salesLINErecCopy."Rebate Discount" := salesLINErecCopy."Amount Including VAT";
                        //end 290424
                        ////salesLINErecCopy."Inv. Discount Amount" += salesLINErecCopy."Rebate Discount";
                        ////salesLINErecCopy.Validate("Inv. Discount Amount");
                        //salesLINErecCopy."Inv. Disc. Amount to Invoice" += salesLINErecCopy."Rebate Discount";
                        ////// salesLINErecCopy.Amount := 0;
                        ////// salesLINErecCopy.Validate(Amount);
                        ///salesLINErecCopy."Amount Including VAT" := 0;
                        //salesLINErecCopy."Outstanding Amount" := 0;
                        //salesLINErecCopy."Outstanding Amount (LCY)" := 0;
                        salesLINErecCopy.Modify();
                    until salesLINErecCopy.next() = 0;
                end;
                if RemainingDiscAmt >= 0 then begin
                    rebateRecordTab.Reset();
                    rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                    //rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Posting Date");
                    rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Order Date");
                    rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
                    if rebateRecordTab.findfirst() then begin
                        RebateAmtFound := true;
                        BalRebateAmtFound := false;
                    end else begin
                        rebateRecordTab.setrange(rebateRecordTab."Rebate Amount");
                        rebateRecordTab.setrange(rebateRecordTab."Balance Rebate Amount", RebateDiscountPayable);
                        if rebateRecordTab.findfirst() then begin
                            RebateAmtFound := false;
                            BalRebateAmtFound := true;
                        end;
                    end;
                    rebateRecordTab."No. of Transaction in a month" += 1;
                    rebateRecordTab."Balance Rebate Amount" := RemainingDiscAmt; //- InvoiceDiscount;
                    if rebateRecordTab."Balance Rebate Amount" = 0 then
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Full else
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                    rebateRecordTab.modify();

                end;
            end else begin
                //salesLINErec."Inv. Discount Amount" += salesLINErec."Rebate Discount";
                //salesLINErec.Amount -= RebateDiscountPayable;
                //salesLINErec."Amount Including VAT" -= RebateDiscountPayable;
                salesLINErecCopy.Reset();
                salesLINErecCopy.SetRange("Document No.", Rec."No.");
                if salesLINErecCopy.findfirst() then begin
                    repeat
                        //290424 start
                        if salesLINErecCopy.AmtafterRebate <> 0 then
                            salesLINErecCopy.validate("Rebate Discount", ROUND(((salesLINErecCopy.AmtafterRebate / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01)) else begin
                            salesLINErecCopy.validate("Rebate Discount", ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01));
                            // salesLINErecCopy.validate("Line Discount Amount", "Rebate Discount");
                        end;
                        //salesLINErecCopy.validate("Rebate Disc. Amount to Inv.", salesLINErecCopy."Rebate Discount");
                        //salesLINErecCopy."Rebate Discount" := ROUND(((salesLINErecCopy."Amount Including VAT" / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01);
                        //290424 end;
                        // salesLINErecCopy."Rebate Discount" := ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01) - salesLINErecCopy."Inv. Discount Amount";
                        ////// salesLINErecCopy.Amount -= salesLINErecCopy."Rebate Discount";
                        ////// salesLINErecCopy.Validate(Amount);
                        //salesLINErecCopy."Amount Including VAT" -= salesLINErecCopy."Rebate Discount";
                        ////salesLINErecCopy."Inv. Discount Amount" += salesLINErecCopy."Rebate Discount";
                        ///salesLINErecCopy.Validate("Inv. Discount Amount");
                        // salesLINErecCopy."Inv. Disc. Amount to Invoice" += salesLINErecCopy."Rebate Discount";
                        // salesLINErecCopy."Outstanding Amount" -= salesLINErecCopy."Rebate Discount";
                        // salesLINErecCopy."Outstanding Amount (LCY)" -= salesLINErecCopy."Rebate Discount";
                        salesLINErecCopy.Modify;
                    until salesLINErecCopy.next() = 0;
                end;
                rebateRecordTab.Reset();
                rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Order Date");
                rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
                if rebateRecordTab.findfirst() then begin
                    RebateAmtFound := true;
                    BalRebateAmtFound := false;
                end else begin
                    rebateRecordTab.setrange(rebateRecordTab."Rebate Amount");
                    rebateRecordTab.setrange(rebateRecordTab."Balance Rebate Amount", RebateDiscountPayable);
                    if rebateRecordTab.findfirst() then begin
                        RebateAmtFound := false;
                        BalRebateAmtFound := true;
                    end;
                end;
                rebateRecordTab."No. of Transaction in a month" += 1;
                rebateRecordTab."Balance Rebate Amount" -= RebateDiscountPayable;
                if rebateRecordTab."Balance Rebate Amount" = 0 then
                    rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Full else
                    rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                rebateRecordTab.modify();
            end;
        end;

        CurrPage.update(true);
    end;
    //260424


    local procedure CheckSalesCheckAllLinesHaveQuantityAssigned()
    var
        ApplicationAreaMgmtFacade: Codeunit "Application Area Mgmt. Facade";
        LinesInstructionMgt: Codeunit "Lines Instruction Mgt.";
    begin
        if ApplicationAreaMgmtFacade.IsFoundationEnabled then
            LinesInstructionMgt.SalesCheckAllLinesHaveQuantityAssigned(Rec);
    end;

    local procedure UpdatePaymentService()
    var
        PaymentServiceSetup: Record "Payment Service Setup";
    begin
        PaymentServiceEnabled := PaymentServiceSetup.CanChangePaymentService(Rec);
    end;

    local procedure UpdateShipToBillToGroupVisibility()
    begin
        CustomerMgt.CalculateShipToBillToOptions(ShipToOptions, BillToOptions, Rec);
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeStatisticsAction(var SalesHeader: Record "Sales Header"; var Handled: Boolean)
    begin
    end;

    var
        CustGRec: Record Customer;


}

