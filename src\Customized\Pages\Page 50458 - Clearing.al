page 50458 Clearing
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO       15-Feb-12    -> Form created for IPO Clearing.
    // 
    //                    08-Sep-12    -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Import Clearing No.

    Caption = 'Clearing';
    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = Document;
    RefreshOnActivate = true;
    SourceTable = "Clearing Header";
    SourceTableView = SORTING("No.")
                      ORDER(Ascending);

    // WHERE("Approval Status"= FILTER(<> Posted));
    UsageCategory = Documents;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Import File No."; "Import File No.")
                {
                }
                field("Country/Region Code"; "Country/Region Code")
                {
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                }
                field("Operational File No."; "Operational File No.")
                {
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(ClearingLines; "Clearing SubForm")
            {
                SubPageLink = "Document No." = FIELD("No.");
            }
            group(Shipping)
            {
                Caption = 'Shipping';
                field("Scanned Shipping Doc. Date"; "Scanned Shipping Doc. Date")
                {
                }
                field("Shipment Documents Rcvd. Date"; "Shipment Documents Rcvd. Date")
                {
                    MultiLine = true;
                }
                field("Mode of Shipment"; "Mode of Shipment")
                {
                }
                field("Shipping Line"; "Shipping Line")
                {
                }
                field("Port of Loading"; "Port of Loading")
                {
                }
                field("Vessel/Voyage"; "Vessel/Voyage")
                {
                }
                field("BL No."; "BL No.")
                {
                }
                field("BL Date"; "BL Date")
                {
                }
                /*field("No. of Container";"No. of Container")
                {
                }*/
                field("Actual Sailng Date"; "Actual Sailng Date")
                {
                }
                field("Expected Date of Arrival"; "Expected Date of Arrival")
                {
                }
                field("Actual DateTime Arrived"; "Actual DateTime Arrived")
                {
                }
                field("Port of Discharge"; "Port of Discharge")
                {
                }
                field("Debit Note Collection Date"; "Debit Note Collection Date")
                {
                }
                field("Shipping Coy Release Date"; "Shipping Coy Release Date")
                {
                }
                field("Terminal DBNote Collection"; "Terminal DBNote Collection")
                {
                }
                field("Account No."; "Account No.")
                {
                }
            }
            group(RAR)
            {
                Caption = 'RAR';
                field("RAR Ref. No."; "RAR Ref. No.")
                {
                }
                field("RAR Date"; "RAR Date")
                {
                }
                field("RAR Request Date"; "RAR Request Date")
                {
                }
                field("RAR Received Date"; "RAR Received Date")
                {
                }
                field("RAR Value"; "RAR Value")
                {
                }
                field("RAR Handover Date"; "RAR Handover Date")
                {
                    MultiLine = true;
                }
                field("NAFDAC Stamping Date"; "NAFDAC Stamping Date")
                {
                }
                field("NCS Release Date"; "NCS Release Date")
                {
                }
                field("RAR Docs. given to agent"; "RAR Docs. given to agent")
                {
                }
                field("Docs. Endorsed Date"; "Docs. Endorsed Date")
                {
                }
            }
            group(Clearing)
            {
                Caption = 'Clearing';
                field("Clearance Type"; "Clearance Type")
                {
                }
                field("Clearing Agent"; "Clearing Agent")
                {
                }
                field("Assessment No."; "Assessment No.")
                {
                }
                field("Assessment Date"; "Assessment Date")
                {
                }
                field("Assessment Amount"; "Assessment Amount")
                {
                }
                field("Duty Request Date"; "Duty Request Date")
                {
                }
                field("Duty Payment Date"; "Duty Payment Date")
                {
                }
                field("Duty Receipt Date"; "Duty Receipt Date")
                {
                }
                field("Duty Amount (LCY)"; "Duty Amount (LCY)")
                {
                }
                field("Duty Docs. given to agent"; "Duty Docs. given to agent")
                {
                }
                field("TDO Date"; "TDO Date")
                {
                }
                field("Container Loaded Date"; "Container Loaded Date")
                {
                }
                field("Container Delivery Date"; "Container Delivery Date")
                {
                }
                field("ECD Receipt Date from Agent"; "ECD Receipt Date from Agent")
                {
                }
                field("ECD Sub. Date to Foreign Trade"; "ECD Sub. Date to Foreign Trade")
                {
                }
                field("ECD Sub. Date to Bank"; "ECD Sub. Date to Bank")
                {
                }
                field("Doc. Handover to Agent Date"; "Doc. Handover to Agent Date")
                {
                }
                field("Duty Certificate"; "Duty Certificate")
                {
                }
                field("Examination Date & Time"; "Examination Date & Time")
                {
                }
                field("Date&Time of Loading"; "Date&Time of Loading")
                {
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("P&ost")
            {
                Caption = 'P&ost';
                Visible = false;
                action("P&ost Receipt")
                {
                    Caption = 'P&ost Receipt';
                    Ellipsis = true;
                    ShortCutKey = 'F11';

                    trigger OnAction();
                    var
                        SalesHeader: Record "Sales Header";
                        ApprovalMgt: Codeunit "Approvals Mgmt.";
                    begin
                        PostReceipt;
                    end;
                }
            }
            group("&Clearing")
            {
                Caption = '&Clearing';
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        ApprovalEntries: Page "Approval Entries";
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Clearing Header", 38, "No.");
                        ApprovalEntries.RUN;
                    end;
                }
                separator(Separator1102152027)
                {
                }
                action("Container Details")
                {
                    Caption = 'Container Details';
                    RunObject = Page Container;
                    RunPageLink = "Clearing No." = FIELD("No.");
                }
                action("Clearing Advances")
                {
                    Caption = 'Clearing Advances';
                    RunObject = Page "Clearing Advance Register";
                    RunPageLink = "Import File No." = FIELD("Import File No."),
                                  "Clearing File No." = FIELD("No.");
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()

                    begin
                        CheckmandatoryFields();
                        container.RESET;
                        container.SETFILTER("Clearing No.", "No.");
                        if container.ISEMPTY then
                            ERROR('Please Enter the Container details');
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendIFCforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF "Approval Status" <> "Approval Status"::Released then BEGIN
                            VALIDATE("Approval Status", "Approval Status"::Released);
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();

                    begin
                        IF "Approval Status" = "Approval Status"::"Pending For Approval" THEN
                            ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50195);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF "Approval Status" <> "Approval Status"::Open then BEGIN
                            "Approval Status" := "Approval Status"::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                action("Send A&pproval Request")
                {
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    var
                        ApprovalMgt: Codeunit 1535;
                        "Release Sales Document": Codeunit "Release Sales Document";
                    begin
                        CheckmandatoryFields();
                        container.RESET;
                        container.SETFILTER("Clearing No.", "No.");
                        if container.ISEMPTY then
                            ERROR('Please Enter the Container details');
                        IF allinoneCU.CheckCLHApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendCLHForApproval(Rec);
                        //IF ApprovalMgt.SendClearingApprovalRequest(Rec) THEN;//CHI2018
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    var
                        ApprovalMgt: Codeunit 1535;
                        "Release Sales Document": Codeunit "Release Sales Document";
                    begin
                        allinoneCU.OnCancelCLHForApproval(Rec);
                        //IF ApprovalMgt.CancelClearingApprovalRequest(Rec,TRUE,TRUE) THEN;//CHI2018
                    end;
                }
                separator(Separator1102152033)
                {
                }
                /*action("Re&lease")
                {
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    var
                        ReleaseSalesDoc: Codeunit "Release Sales Document";
                    begin
                        PerformManualRelease;
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendLSPforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                    end;
                }
                action("Re&open")
                {
                    Caption = 'Re&open';

                    trigger OnAction();
                    var
                        ReleaseSalesDoc: Codeunit "Release Sales Document";
                    begin
                        PerformManualReopen;
                    end;
                }*/
                action("Import Report")
                {
                    Caption = 'Import Report';

                    trigger OnAction();
                    begin
                        ClearingGrec.Reset();
                        //SETFILTER("No.", "No.");
                        ClearingGrec.SetRange("No.", "No.");
                        if ClearingGrec.FindLast() then
                            REPORT.RUNMODAL(50426, true, false, ClearingGrec);
                    end;
                }
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("No.", 30, TODAY, TIME, USERID, DATABASE::"Clearing Header"); //HO1.0

        CurrPage.SAVERECORD;
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);

    END;

    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);

    END;

    local procedure CheckmandatoryFields()
    var
        myInt: Integer;
    begin
        TestField("Country/Region Code");
        TestField("Scanned Shipping Doc. Date");
        //TestField("Shipment Documents Rcvd. Date");
        //TestField("Mode of Shipment");
        TestField("Shipping Line");
        TestField("Port of Loading");
        TestField("Vessel/Voyage");
        TestField("BL No.");
        TestField("BL Date");
        TestField("Actual Sailng Date");
        TestField("Expected Date of Arrival");
        TestField("Actual DateTime Arrived");
        TestField("Port of Discharge");
        //TestField("Debit Note Collection Date");
        //TestField("Shipping Coy Release Date");
        //TestField("Terminal DBNote Collection");
        //TestField("Account No.");
        //TestField("RAR Date");
        //TestField("RAR Docs. given to agent");
        TestField("RAR Ref. No.");
        TestField("RAR Received Date");
        TestField("RAR Request Date");
        TestField("RAR Value");
        //TestField("RAR Handover Date");
        //TestField("NAFDAC Stamping Date");
        //TestField("NCS Release Date");
        //TestField("RAR Docs. given to agent");
        //TestField("Docs. Endorsed Date");
        if "Clearance Type" = "Clearance Type"::" " then
            Error('Clearance Type must not be empty');
        TestField("Clearing Agent");
        TestField("Assessment No.");
        TestField("Assessment Date");
        TestField("Assessment Amount");
        TestField("Duty Request Date");
        TestField("Duty Receipt Date");
        TestField("Duty Payment Date");
        TestField("Duty Amount (LCY)");
        //TestField("Duty Docs. given to agent");
        //TestField("Duty Certificate");
        //TestField("TDO Date");
        //TestField("Container Loaded Date");
        //TestField("Container Delivery Date");
        //TestField("ECD Receipt Date from Agent");
        //TestField("ECD Sub. Date to Bank");
        //TestField("ECD Sub. Date to Foreign Trade");
        //TestField("Doc. Handover to Agent Date");
        //TestField("Examination Date & Time");
        //TestField("Date&Time of Loading");
    end;

    var
        approvalmngmt: Codeunit "Approvals Mgmt.";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;

        ApprovalMgt: Codeunit 1535;
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        container: Record Container;
        allinoneCU: codeunit Codeunit1;
        WorkflowManagement: Codeunit "Workflow Management";
        RecordRest: record "Restricted Record";
        ClearingGrec: Record "Clearing Header";
}

