/// <summary>
/// PageExtension ConsJouExt (ID 50239) extends Record Consumption Journal.
/// </summary>
pageextension 50239 ConsJouExt extends "Consumption Journal"
{
    layout
    {

        addafter(Quantity)//PKONJ7
        {
            field("Qty on Inventory"; "Qty on Inventory")
            {
                editable = false;
            }
        }

        //G2S
        modify(CurrentJnlBatchName)
        {

            //Editable = not LPEnabledItem;

            trigger OnAfterValidate()
            var
                ItemJnlBatch: Record "Item Journal Batch";
            begin
                //  ItemJournalBatchPage := CurrentJnlBatchName;
            end;
        }

        modify("Posting Date")
        {
            Editable = not LPEnabledItem;
        }

        modify("Order No.")
        {
            Editable = not LPEnabledItem;
        }
        // modify("Order Line No.")
        // {
        //     Editable = not LPEnabledItem;
        // }

        // modify("Prod. Order Comp. Line No.")
        // {
        //     Editable = not LPEnabledItem;
        // }

        modify("Document No.")
        {
            Editable = not LPEnabledItem;
        }

        modify("Item No.")
        {
            Editable = not LPEnabledItem;
        }

        modify(Description)
        {
            Editable = not LPEnabledItem;
        }
        modify(Quantity)
        {
            Editable = not LPEnabledItem;
        }
        modify("Unit of Measure Code")
        {
            Editable = not LPEnabledItem;
        }
        modify("Unit Cost")
        {
            Editable = not LPEnabledItem;
        }
        modify("Unit Amount")
        {
            Editable = not LPEnabledItem;
        }
        modify("Applies-to Entry")
        {
            Editable = not LPEnabledItem;
        }
        modify("Applies-from Entry")
        {
            Editable = not LPEnabledItem;
        }
        modify("Shortcut Dimension 1 Code")
        {
            Editable = not LPEnabledItem;
        }
        modify("Shortcut Dimension 2 Code")
        {
            Editable = not LPEnabledItem;
        }
        modify(ShortcutDimCode3)
        {
            Editable = not LPEnabledItem;
        }
        modify(ShortcutDimCode4)
        {
            Editable = not LPEnabledItem;
        }
        modify(ShortcutDimCode5)
        {
            Editable = not LPEnabledItem;
        }
        modify(ShortcutDimCode6)
        {
            Editable = not LPEnabledItem;
        }
        modify(ShortcutDimCode7)
        {
            Editable = not LPEnabledItem;
        }
        modify(ShortcutDimCode8)
        {
            Editable = not LPEnabledItem;
        }
        //G2S
    }
    actions
    {
        addafter("Calc. Co&nsumption")
        {
            action("Reverse Production Order")
            {
                Image = ReverseLines;
                ApplicationArea = all;
                trigger OnAction()
                var
                    crtrevjou: Report CreateRevrseJournals;
                begin
                    crtrevjou.GetDefltValues("Journal Batch Name", "Journal Template Name");
                    crtrevjou.Run();
                end;
            }
        }

        //g2s

        modify("P&ost")
        {
            trigger OnBeforeAction()
            var

            begin
                //validate item tracking
                AssignItemTracking();
                //validate external no
                CheckExternalDocNo();

            end;
        }

        modify("Post and &Print")
        {

            trigger OnBeforeAction()
            var

            begin
                //validate item tracking
                AssignItemTracking();
                //validate external no
                CheckExternalDocNo();
            end;
        }
    }
    /// <summary>
    /// AssignItemTracking.
    /// </summary>
    procedure AssignItemTracking()
    var
        ItemJournalLines: Record "Item Journal Line";
        Item: Record Item;
        ReservationEntry: Record "Reservation Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;
        ReservationEntry2: Record "Reservation Entry";
    begin
        ItemJournalLines.Reset();
        ItemJournalLines.SetRange("Journal Template Name", "Journal Template Name");
        ItemJournalLines.SetRange("Journal Batch Name", "Journal Batch Name");
        ItemJournalLines.SetRange("Document No.", "Document No.");
        ItemJournalLines.SetRange("Entry Type", ItemJournalLines."Entry Type"::Consumption);
        if ItemJournalLines.FindSet() then
            repeat
                if Item.Get(ItemJournalLines."Item No.") and (Item."Item Tracking Code" <> '') then begin

                    LineQty := ItemJournalLines."Quantity (Base)";

                    ReservationEntry2.RESET;
                    IF ReservationEntry2.FINDLAST THEN
                        EntryNum := ReservationEntry2."Entry No." + 1
                    ELSE
                        EntryNum := 1;
                    ReservationEntry2.Reset();
                    ReservationEntry2.SetRange("Source ID", ItemJournalLines."Journal Template Name");
                    ReservationEntry2.SetRange("Source Subtype", 5);
                    ReservationEntry2.SetRange("Source Type", DATABASE::"Item Journal Line");
                    ReservationEntry2.SetRange("Source Ref. No.", ItemJournalLines."Line No.");
                    ReservationEntry2.SetRange("Source Batch Name", ItemJournalLines."Journal Batch Name");
                    if ReservationEntry2.FindSet() then
                        ReservationEntry2.DeleteAll();
                    ItemLedgerEntry.RESET;
                    ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
                    ItemLedgerEntry.SETRANGE("Item No.", ItemJournalLines."Item No.");
                    ItemLedgerEntry.SETRANGE("Location Code", ItemJournalLines."Location Code");
                    ItemLedgerEntry.SETRANGE("Variant Code", ItemJournalLines."Variant Code");
                    ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
                    IF ItemLedgerEntry.FINDSET THEN
                        REPEAT

                            IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                                AssignQty := LineQty;
                                LineQty := 0;
                            END ELSE BEGIN
                                AssignQty := (ItemLedgerEntry."Remaining Quantity");
                                LineQty -= AssignQty;
                            END;


                            ReservationEntry.INIT;
                            ReservationEntry."Entry No." := EntryNum;
                            ReservationEntry.VALIDATE(Positive, FALSE);
                            ReservationEntry.VALIDATE("Item No.", ItemJournalLines."Item No.");
                            ReservationEntry.VALIDATE("Location Code", ItemJournalLines."Location Code");
                            ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                            ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / ItemJournalLines."Qty. per Unit of Measure"));
                            ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                            ReservationEntry.VALIDATE("Creation Date", WorkDate());
                            ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
                            ReservationEntry.VALIDATE("Source Subtype", 5);
                            ReservationEntry.VALIDATE("Source ID", ItemJournalLines."Journal Template Name");

                            ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLines."Line No.");
                            ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                            ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                            ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                            ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                            ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                            ReservationEntry.Validate("Source Batch Name", ItemJournalLines."Journal Batch Name");
                            ReservationEntry."Created By" := USERID;
                            ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                            ReservationEntry.VALIDATE(Correction, FALSE);
                            ReservationEntry.INSERT;
                            EntryNum += 1;
                        until (ItemLedgerEntry.Next() = 0) OR (LineQty = 0);
                end;
            until ItemJournalLines.Next() = 0;
    end;

    /// <summary>
    /// CheckExternalDocNo.
    /// /// </summary>
    procedure CheckExternalDocNo()
    var

    begin
        ItemJournalLrec.Reset();
        ItemJournalLrec.SetRange("Document No.", "Document No.");
        ItemJournalLrec.SetRange("Journal Template Name", "Journal Template Name");
        ItemJournalLrec.SetRange("Journal Batch Name", "Journal Batch Name");
        if ItemJournalLrec.FindSet() then
            repeat
                if ItemJournalLrec."External Document No." = '' then
                    Error('Please Enter External Document Number');
            //ItemJournalLrec.TestField("External Document No.");
            until ItemJournalLrec.Next() = 0;
    end;

    /// <summary>
    /// ValidateEditableFields.
    /// </summary>
    /// <param name="ItemJnl">Record "Item Journal Line".</param>
    procedure ValidateEditableFields(ItemJnl: Record "Item Journal Line")
    var
        //170424
        CustomJnl: Record "Custom Journal Batch Setup";
    begin
        CustomJnl.Reset();
        CustomJnl.SetRange("User ID", UserId);
        CustomJnl.SetRange(Enabled, true);
        if CustomJnl.FindFirst() then begin
            ItemJnl.SetRange("Journal Batch Name", CustomJnl."Batch Name");
            If ItemJnl.FindFirst() then begin
                LPEnabledItem := true;
            end;
        end;
    end;

    trigger OnOpenPage()
    var
        myInt: Integer;
    begin
        ValidateEditableFields(Rec);
    end;

    trigger OnClosePage()
    var
        myInt: Integer;
    begin

    end;

    var
        ItemJournalLrec: Record "Item Journal Line";
        Item2: Record Item;
        LPEnabledItem: Boolean;
        ItemJournalBatchPage: Code[30];
}