page 50947 BranchRequestSubformRel
{
    Caption = 'Branch Request Subform RelClose';
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = ListPart;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = BranchRequestLine;

    layout
    {
        area(Content)
        {
            Repeater(Control1102152000)
            {
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var
                        BranchReqLine: record BranchRequestLine;
                    begin
                        BranchReqLine.Reset();
                        BranchReqLine.SetRange("Document No.", "Document No.");
                        BranchReqLine.SetRange("Item No.", "Item No.");
                        if BranchReqLine.FindFirst() then
                            Error('Item %1 Already Exists %2');
                    end;

                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Requested Quantity"; "Requested Quantity")
                {
                    ApplicationArea = all;
                }
                field("Unit Of Measure"; "Unit Of Measure")
                {
                    ApplicationArea = all;
                }
                field("Qty Per UOM"; "Qty Per UOM")
                {
                    ApplicationArea = all;
                }
                field("Requested Qty(Base)"; "Requested Qty(Base)")
                {
                    ApplicationArea = all;
                }
                //Balu 05142021>>
                field("Issued Quantity"; "Issued Quantity")
                {
                    ApplicationArea = all;
                }
                field("Wt. of the Qty Loading in Tons"; "Wt. of the Qty Loading in Tons")
                {
                    ApplicationArea = all;
                    Editable = false;//PKONJ18
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }
    trigger OnModifyRecord(): Boolean

    begin
        BranchGVar.Get("Document No.");
        if BranchGVar.TransferOrderCreated then
            Error('you can not modify branch request. Already transfer order is created.');
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        BranchGVar.Get("Document No.");
        if BranchGVar.TransferOrderCreated then
            Error('you can not deleted branch request. Already transfer order is created.');
    end;
    //Balu 05142021>>
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    begin
        BranchGVar.Get("Document No.");
        BranchGVar.TESTFIELD("From Location");
        BranchGVar.TESTFIELD("To Location");
        "From Location" := BranchGVar."From Location";
        "To Location" := BranchGVar."To Location";
    end;
    //Balu 05142021<<

    var
        myInt: Integer;
        BranchGVar: Record BranchRequest;
        item: Record Item;
}