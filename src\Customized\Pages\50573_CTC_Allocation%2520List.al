page 50573 "CTC_Allocation List"
{
    // version CTC

    CardPageID = CTC_Allocations;
    Editable = false;
    PageType = List;
    SourceTable = "Allocation Header";
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                }
                field("Journal Batch Name"; "Journal Batch Name")
                {
                    ApplicationArea = all;
                }
                field("Allocation Type"; "Allocation Type")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Recurring Frequency"; "Recurring Frequency")
                {
                    ApplicationArea = all;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Expiration Date"; "Expiration Date")
                {
                    ApplicationArea = all;
                }
                field("Maintenance Code"; "Maintenance Code")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

