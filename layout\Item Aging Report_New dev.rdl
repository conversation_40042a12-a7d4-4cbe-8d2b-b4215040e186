﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>321400f8-12a2-4cf1-bbd9-846ce1e7c77a</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Table1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.575cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>4.65cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.875cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.846cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Item__No__Caption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Item__No__Caption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>29</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Item_DescriptionCaption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Item_DescriptionCaption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>28</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_1__Control32Caption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_1__Control32Caption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>27</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodStartDate_2____1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PeriodStartDate_2____1.Value &amp; Chr(10) &amp; Fields!PeriodStartDate_3_.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>26</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodStartDate_3____1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PeriodStartDate_3____1.Value &amp; Chr(10) &amp; Fields!PeriodStartDate_4_.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>25</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodStartDate_4_____1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PeriodStartDate_4_____1.Value &amp; Chr(10) &amp; Fields!PeriodStartDate_5_.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>24</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_5__Control28Caption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_5__Control28Caption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>23</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalInvtValue_Control23Caption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalInvtValue_Control23Caption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>22</ZIndex>
                          <Style>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>0.075cm</PaddingLeft>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.423cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="FORMAT_TODAY_0_4_">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FORMAT_TODAY_0_4_.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>21</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="COMPANYNAME">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!COMPANYNAME.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>20</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Item_Age_Composition___ValueCaption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Item_Age_Composition___ValueCaption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>19</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CurrReport_PAGENOCaption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CurrReport_PAGENOCaption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>18</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox6">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox6</rd:DefaultName>
                          <ZIndex>17</ZIndex>
                          <Style>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.423cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Item__No__">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Item__No__.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>=IIF(IsNumeric(ReportItems!Item__No__.Value),"Right","Left")</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>7</ZIndex>
                          <Style>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Item_Description">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Item_Description.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>6</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_1__Control32.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Format>=Fields!InvtValue_1__Control32Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>5</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_2__Control47">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_2__Control47.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Format>=Fields!InvtValue_2__Control47Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>4</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_3__Control48">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_3__Control48.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Format>=Fields!InvtValue_3__Control48Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>3</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_4__Control49">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_4__Control49.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Format>=Fields!InvtValue_4__Control49Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_5__Control28">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvtValue_5__Control28.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Format>=Fields!InvtValue_5__Control28Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalInvtValue_Control23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalInvtValue_Control23.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <Format>=Fields!TotalInvtValue_Control23Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.423cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox1">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox1</rd:DefaultName>
                          <ZIndex>8</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>8</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.423cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalCaption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalCaption.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>16</ZIndex>
                          <Style>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox2">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox2</rd:DefaultName>
                          <ZIndex>15</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Last(Fields!InvtValueRTC_1_.Value)</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>=First(Fields!InvtValue_1__Control13Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>14</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_2__Control14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Last(Fields!InvtValueRTC_2_.Value)</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>=First(Fields!InvtValue_2__Control14Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>13</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_3__Control15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Last(Fields!InvtValueRTC_3_.Value)</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>=First(Fields!InvtValue_3__Control15Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>12</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_4__Control16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Last(Fields!InvtValueRTC_4_.Value)</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>=First(Fields!InvtValue_4__Control16Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>11</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvtValue_5__Control17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Last(Fields!InvtValueRTC_5_.Value)</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>=First(Fields!InvtValue_5__Control17Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>10</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingRight>0.075cm</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalInvtValue_Control19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Last(Fields!TotalInvtValueRTC.Value)</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>=First(Fields!TotalInvtValue_Control19Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>9</ZIndex>
                          <Style>
                            <PaddingLeft>0.075cm</PaddingLeft>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <Group Name="Table1_Group1">
                    <GroupExpressions>
                      <GroupExpression>=1</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Table1_Details_Group">
                        <DataElementName>Detail</DataElementName>
                      </Group>
                      <TablixMembers>
                        <TablixMember>
                          <Visibility>
                            <Hidden>=IIF(Fields!PrintLine.Value,False,True)</Hidden>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                      <DataElementName>Detail_Collection</DataElementName>
                      <DataElementOutput>Output</DataElementOutput>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                    <TablixMember>
                      <KeepWithGroup>Before</KeepWithGroup>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                    <TablixMember>
                      <KeepWithGroup>Before</KeepWithGroup>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.87245cm</Top>
            <Height>2.538cm</Height>
            <Width>17.85cm</Width>
            <Style />
          </Tablix>
          <Tablix Name="table2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>18.09524cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.423cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox3">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Item_TABLECAPTION__________ItemFilter.Value</Value>
                                  <Style>
                                    <FontSize>7pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox3</rd:DefaultName>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.423cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox5">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox5</rd:DefaultName>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <Filters>
              <Filter>
                <FilterExpression>=Fields!ItemFilter.Value</FilterExpression>
                <Operator>GreaterThan</Operator>
                <FilterValues>
                  <FilterValue />
                </FilterValues>
              </Filter>
            </Filters>
            <Height>0.846cm</Height>
            <Width>18.09524cm</Width>
            <ZIndex>1</ZIndex>
            <Visibility>
              <Hidden>=IIF(Fields!ItemFilter.Value = "",True,False)</Hidden>
            </Visibility>
            <DataElementOutput>NoOutput</DataElementOutput>
            <Style>
              <FontSize>7pt</FontSize>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>3.41045cm</Height>
        <Style />
      </Body>
      <Width>18.15cm</Width>
      <Page>
        <PageHeader>
          <Height>1.692cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="CurrReport_PAGENOCaption1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CurrReport_PAGENO.Value</Value>
                      <Style>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <Top>0.423cm</Top>
              <Left>16.95cm</Left>
              <Height>0.423cm</Height>
              <Width>0.75cm</Width>
              <Visibility>
                <Hidden>=IIF(ReportItems!Item_Age_Composition___ValueCaption.Value = "",True,False)</Hidden>
              </Visibility>
              <DataElementOutput>NoOutput</DataElementOutput>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Item_Age_Composition___ValueCaption1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Item_Age_Composition___ValueCaption.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <Height>0.423cm</Height>
              <Width>7.5cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="COMPANYNAME1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!COMPANYNAME.Value</Value>
                      <Style>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <Top>0.423cm</Top>
              <Height>0.423cm</Height>
              <Width>7.5cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="ExecutionTimeTextBox">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=ReportItems!FORMAT_TODAY_0_4_.Value</Value>
                      <Style>
                        <FontSize>7pt</FontSize>
                        <Format>g</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Left>15cm</Left>
              <Height>0.423cm</Height>
              <Width>3.15cm</Width>
              <ZIndex>3</ZIndex>
              <Visibility>
                <Hidden>=IIF(ReportItems!Item_Age_Composition___ValueCaption.Value = "",True,False)</Hidden>
              </Visibility>
              <DataElementOutput>NoOutput</DataElementOutput>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="UserIdTextBox">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=User!UserID</Value>
                      <Style>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Top>0.846cm</Top>
              <Left>14.9cm</Left>
              <Height>0.423cm</Height>
              <Width>3.25cm</Width>
              <ZIndex>4</ZIndex>
              <Visibility>
                <Hidden>=IIF(ReportItems!Item_Age_Composition___ValueCaption.Value = "",True,False)</Hidden>
              </Visibility>
              <DataElementOutput>NoOutput</DataElementOutput>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="PageNumberTextBox">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Globals!PageNumber</Value>
                      <Style>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Top>0.423cm</Top>
              <Left>17.7cm</Left>
              <Height>0.423cm</Height>
              <Width>0.45cm</Width>
              <ZIndex>5</ZIndex>
              <Visibility>
                <Hidden>=IIF(ReportItems!Item_Age_Composition___ValueCaption.Value = "",True,False)</Hidden>
              </Visibility>
              <DataElementOutput>NoOutput</DataElementOutput>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style />
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>1.5cm</LeftMargin>
        <TopMargin>2cm</TopMargin>
        <BottomMargin>2cm</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>93b144b9-db47-4623-b86d-c0c40da11d3b</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="FORMAT_TODAY_0_4_">
          <DataField>FORMAT_TODAY_0_4_</DataField>
        </Field>
        <Field Name="CurrReport_PAGENO">
          <DataField>CurrReport_PAGENO</DataField>
        </Field>
        <Field Name="COMPANYNAME">
          <DataField>COMPANYNAME</DataField>
        </Field>
        <Field Name="USERID">
          <DataField>USERID</DataField>
        </Field>
        <Field Name="Item_TABLECAPTION__________ItemFilter">
          <DataField>Item_TABLECAPTION__________ItemFilter</DataField>
        </Field>
        <Field Name="ItemFilter">
          <DataField>ItemFilter</DataField>
        </Field>
        <Field Name="PeriodStartDate_2____1">
          <DataField>PeriodStartDate_2____1</DataField>
        </Field>
        <Field Name="PeriodStartDate_3_">
          <DataField>PeriodStartDate_3_</DataField>
        </Field>
        <Field Name="PeriodStartDate_3____1">
          <DataField>PeriodStartDate_3____1</DataField>
        </Field>
        <Field Name="PeriodStartDate_4_">
          <DataField>PeriodStartDate_4_</DataField>
        </Field>
        <Field Name="PeriodStartDate_4_____1">
          <DataField>PeriodStartDate_4_____1</DataField>
        </Field>
        <Field Name="PeriodStartDate_5_">
          <DataField>PeriodStartDate_5_</DataField>
        </Field>
        <Field Name="PrintLine">
          <DataField>PrintLine</DataField>
        </Field>
        <Field Name="InvtValueRTC_1_">
          <DataField>InvtValueRTC_1_</DataField>
        </Field>
        <Field Name="InvtValueRTC_1_Format">
          <DataField>InvtValueRTC_1_Format</DataField>
        </Field>
        <Field Name="InvtValueRTC_2_">
          <DataField>InvtValueRTC_2_</DataField>
        </Field>
        <Field Name="InvtValueRTC_2_Format">
          <DataField>InvtValueRTC_2_Format</DataField>
        </Field>
        <Field Name="InvtValueRTC_5_">
          <DataField>InvtValueRTC_5_</DataField>
        </Field>
        <Field Name="InvtValueRTC_5_Format">
          <DataField>InvtValueRTC_5_Format</DataField>
        </Field>
        <Field Name="InvtValueRTC_4_">
          <DataField>InvtValueRTC_4_</DataField>
        </Field>
        <Field Name="InvtValueRTC_4_Format">
          <DataField>InvtValueRTC_4_Format</DataField>
        </Field>
        <Field Name="InvtValueRTC_3_">
          <DataField>InvtValueRTC_3_</DataField>
        </Field>
        <Field Name="InvtValueRTC_3_Format">
          <DataField>InvtValueRTC_3_Format</DataField>
        </Field>
        <Field Name="TotalInvtValueRTC">
          <DataField>TotalInvtValueRTC</DataField>
        </Field>
        <Field Name="TotalInvtValueRTCFormat">
          <DataField>TotalInvtValueRTCFormat</DataField>
        </Field>
        <Field Name="PeriodStartDate_6_">
          <DataField>PeriodStartDate_6_</DataField>
        </Field>
        <Field Name="PeriodStartDate_5_____1">
          <DataField>PeriodStartDate_5_____1</DataField>
        </Field>
        <Field Name="PeriodStartDate_7_">
          <DataField>PeriodStartDate_7_</DataField>
        </Field>
        <Field Name="PeriodStartDate_6_____1">
          <DataField>PeriodStartDate_6_____1</DataField>
        </Field>
        <Field Name="Invtqty_1_">
          <DataField>Invtqty_1_</DataField>
        </Field>
        <Field Name="Invtqty_1_Format">
          <DataField>Invtqty_1_Format</DataField>
        </Field>
        <Field Name="InvtQty_2_">
          <DataField>InvtQty_2_</DataField>
        </Field>
        <Field Name="InvtQty_2_Format">
          <DataField>InvtQty_2_Format</DataField>
        </Field>
        <Field Name="InvtQty_3_">
          <DataField>InvtQty_3_</DataField>
        </Field>
        <Field Name="InvtQty_3_Format">
          <DataField>InvtQty_3_Format</DataField>
        </Field>
        <Field Name="Invtqty_4_">
          <DataField>Invtqty_4_</DataField>
        </Field>
        <Field Name="Invtqty_4_Format">
          <DataField>Invtqty_4_Format</DataField>
        </Field>
        <Field Name="InvtQty_5_">
          <DataField>InvtQty_5_</DataField>
        </Field>
        <Field Name="InvtQty_5_Format">
          <DataField>InvtQty_5_Format</DataField>
        </Field>
        <Field Name="TotalInvtQty">
          <DataField>TotalInvtQty</DataField>
        </Field>
        <Field Name="TotalInvtQtyFormat">
          <DataField>TotalInvtQtyFormat</DataField>
        </Field>
        <Field Name="InvtValue_1_">
          <DataField>InvtValue_1_</DataField>
        </Field>
        <Field Name="InvtValue_1_Format">
          <DataField>InvtValue_1_Format</DataField>
        </Field>
        <Field Name="InvtValue_2_">
          <DataField>InvtValue_2_</DataField>
        </Field>
        <Field Name="InvtValue_2_Format">
          <DataField>InvtValue_2_Format</DataField>
        </Field>
        <Field Name="InvtValue_3_">
          <DataField>InvtValue_3_</DataField>
        </Field>
        <Field Name="InvtValue_3_Format">
          <DataField>InvtValue_3_Format</DataField>
        </Field>
        <Field Name="InvtValue_4_">
          <DataField>InvtValue_4_</DataField>
        </Field>
        <Field Name="InvtValue_4_Format">
          <DataField>InvtValue_4_Format</DataField>
        </Field>
        <Field Name="InvtValue_5_">
          <DataField>InvtValue_5_</DataField>
        </Field>
        <Field Name="InvtValue_5_Format">
          <DataField>InvtValue_5_Format</DataField>
        </Field>
        <Field Name="TotalInvtValue">
          <DataField>TotalInvtValue</DataField>
        </Field>
        <Field Name="TotalInvtValueFormat">
          <DataField>TotalInvtValueFormat</DataField>
        </Field>
        <Field Name="InvtQty_7_">
          <DataField>InvtQty_7_</DataField>
        </Field>
        <Field Name="InvtQty_7_Format">
          <DataField>InvtQty_7_Format</DataField>
        </Field>
        <Field Name="InvtValue_7_">
          <DataField>InvtValue_7_</DataField>
        </Field>
        <Field Name="InvtValue_7_Format">
          <DataField>InvtValue_7_Format</DataField>
        </Field>
        <Field Name="InvtQty_6_">
          <DataField>InvtQty_6_</DataField>
        </Field>
        <Field Name="InvtQty_6_Format">
          <DataField>InvtQty_6_Format</DataField>
        </Field>
        <Field Name="InvtValue_6_">
          <DataField>InvtValue_6_</DataField>
        </Field>
        <Field Name="InvtValue_6_Format">
          <DataField>InvtValue_6_Format</DataField>
        </Field>
        <Field Name="InvtValue_1__Control31">
          <DataField>InvtValue_1__Control31</DataField>
        </Field>
        <Field Name="InvtValue_1__Control31Format">
          <DataField>InvtValue_1__Control31Format</DataField>
        </Field>
        <Field Name="InvtValue_2__Control34">
          <DataField>InvtValue_2__Control34</DataField>
        </Field>
        <Field Name="InvtValue_2__Control34Format">
          <DataField>InvtValue_2__Control34Format</DataField>
        </Field>
        <Field Name="InvtValue_3__Control35">
          <DataField>InvtValue_3__Control35</DataField>
        </Field>
        <Field Name="InvtValue_3__Control35Format">
          <DataField>InvtValue_3__Control35Format</DataField>
        </Field>
        <Field Name="InvtValue_4__Control36">
          <DataField>InvtValue_4__Control36</DataField>
        </Field>
        <Field Name="InvtValue_4__Control36Format">
          <DataField>InvtValue_4__Control36Format</DataField>
        </Field>
        <Field Name="InvtValue_5__Control37">
          <DataField>InvtValue_5__Control37</DataField>
        </Field>
        <Field Name="InvtValue_5__Control37Format">
          <DataField>InvtValue_5__Control37Format</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control38">
          <DataField>TotalInvtValue_Control38</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control38Format">
          <DataField>TotalInvtValue_Control38Format</DataField>
        </Field>
        <Field Name="InvtQty_1__Control31">
          <DataField>InvtQty_1__Control31</DataField>
        </Field>
        <Field Name="InvtQty_1__Control31Format">
          <DataField>InvtQty_1__Control31Format</DataField>
        </Field>
        <Field Name="InvtQty_2__Control34">
          <DataField>InvtQty_2__Control34</DataField>
        </Field>
        <Field Name="InvtQty_2__Control34Format">
          <DataField>InvtQty_2__Control34Format</DataField>
        </Field>
        <Field Name="InvtQty_3__Control35">
          <DataField>InvtQty_3__Control35</DataField>
        </Field>
        <Field Name="InvtQty_3__Control35Format">
          <DataField>InvtQty_3__Control35Format</DataField>
        </Field>
        <Field Name="InvtQty_4__Control36">
          <DataField>InvtQty_4__Control36</DataField>
        </Field>
        <Field Name="InvtQty_4__Control36Format">
          <DataField>InvtQty_4__Control36Format</DataField>
        </Field>
        <Field Name="InvtQty_5__Control37">
          <DataField>InvtQty_5__Control37</DataField>
        </Field>
        <Field Name="InvtQty_5__Control37Format">
          <DataField>InvtQty_5__Control37Format</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control38">
          <DataField>TotalInvtQty_Control38</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control38Format">
          <DataField>TotalInvtQty_Control38Format</DataField>
        </Field>
        <Field Name="InvtQty_6__Control37">
          <DataField>InvtQty_6__Control37</DataField>
        </Field>
        <Field Name="InvtQty_6__Control37Format">
          <DataField>InvtQty_6__Control37Format</DataField>
        </Field>
        <Field Name="InvtValue_6__Control37">
          <DataField>InvtValue_6__Control37</DataField>
        </Field>
        <Field Name="InvtValue_6__Control37Format">
          <DataField>InvtValue_6__Control37Format</DataField>
        </Field>
        <Field Name="InvtQty_7__Control37">
          <DataField>InvtQty_7__Control37</DataField>
        </Field>
        <Field Name="InvtQty_7__Control37Format">
          <DataField>InvtQty_7__Control37Format</DataField>
        </Field>
        <Field Name="InvtValue_7__Control37">
          <DataField>InvtValue_7__Control37</DataField>
        </Field>
        <Field Name="InvtValue_7__Control37Format">
          <DataField>InvtValue_7__Control37Format</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control19">
          <DataField>TotalInvtValue_Control19</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control19Format">
          <DataField>TotalInvtValue_Control19Format</DataField>
        </Field>
        <Field Name="InvtValue_1__Control13">
          <DataField>InvtValue_1__Control13</DataField>
        </Field>
        <Field Name="InvtValue_1__Control13Format">
          <DataField>InvtValue_1__Control13Format</DataField>
        </Field>
        <Field Name="InvtValue_2__Control14">
          <DataField>InvtValue_2__Control14</DataField>
        </Field>
        <Field Name="InvtValue_2__Control14Format">
          <DataField>InvtValue_2__Control14Format</DataField>
        </Field>
        <Field Name="InvtValue_3__Control15">
          <DataField>InvtValue_3__Control15</DataField>
        </Field>
        <Field Name="InvtValue_3__Control15Format">
          <DataField>InvtValue_3__Control15Format</DataField>
        </Field>
        <Field Name="InvtValue_4__Control16">
          <DataField>InvtValue_4__Control16</DataField>
        </Field>
        <Field Name="InvtValue_4__Control16Format">
          <DataField>InvtValue_4__Control16Format</DataField>
        </Field>
        <Field Name="InvtValue_5__Control17">
          <DataField>InvtValue_5__Control17</DataField>
        </Field>
        <Field Name="InvtValue_5__Control17Format">
          <DataField>InvtValue_5__Control17Format</DataField>
        </Field>
        <Field Name="InvtQty_1__Control13">
          <DataField>InvtQty_1__Control13</DataField>
        </Field>
        <Field Name="InvtQty_1__Control13Format">
          <DataField>InvtQty_1__Control13Format</DataField>
        </Field>
        <Field Name="InvtQty_2__Control14">
          <DataField>InvtQty_2__Control14</DataField>
        </Field>
        <Field Name="InvtQty_2__Control14Format">
          <DataField>InvtQty_2__Control14Format</DataField>
        </Field>
        <Field Name="InvtQty_3__Control15">
          <DataField>InvtQty_3__Control15</DataField>
        </Field>
        <Field Name="InvtQty_3__Control15Format">
          <DataField>InvtQty_3__Control15Format</DataField>
        </Field>
        <Field Name="InvtQty_4__Control16">
          <DataField>InvtQty_4__Control16</DataField>
        </Field>
        <Field Name="InvtQty_4__Control16Format">
          <DataField>InvtQty_4__Control16Format</DataField>
        </Field>
        <Field Name="InvtQty_5__Control17">
          <DataField>InvtQty_5__Control17</DataField>
        </Field>
        <Field Name="InvtQty_5__Control17Format">
          <DataField>InvtQty_5__Control17Format</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control19">
          <DataField>TotalInvtQty_Control19</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control19Format">
          <DataField>TotalInvtQty_Control19Format</DataField>
        </Field>
        <Field Name="InvtQty_6__Control17">
          <DataField>InvtQty_6__Control17</DataField>
        </Field>
        <Field Name="InvtQty_6__Control17Format">
          <DataField>InvtQty_6__Control17Format</DataField>
        </Field>
        <Field Name="InvtValue_6__Control17">
          <DataField>InvtValue_6__Control17</DataField>
        </Field>
        <Field Name="InvtValue_6__Control17Format">
          <DataField>InvtValue_6__Control17Format</DataField>
        </Field>
        <Field Name="InvtQty_7__Control17">
          <DataField>InvtQty_7__Control17</DataField>
        </Field>
        <Field Name="InvtQty_7__Control17Format">
          <DataField>InvtQty_7__Control17Format</DataField>
        </Field>
        <Field Name="InvtValue_7__Control17">
          <DataField>InvtValue_7__Control17</DataField>
        </Field>
        <Field Name="InvtValue_7__Control17Format">
          <DataField>InvtValue_7__Control17Format</DataField>
        </Field>
        <Field Name="Item_Age_Composition___ValueCaption">
          <DataField>Item_Age_Composition___ValueCaption</DataField>
        </Field>
        <Field Name="CurrReport_PAGENOCaption">
          <DataField>CurrReport_PAGENOCaption</DataField>
        </Field>
        <Field Name="InvtValue_5__Control28Caption">
          <DataField>InvtValue_5__Control28Caption</DataField>
        </Field>
        <Field Name="InvtValue_1__Control32Caption">
          <DataField>InvtValue_1__Control32Caption</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control23Caption">
          <DataField>TotalInvtValue_Control23Caption</DataField>
        </Field>
        <Field Name="Item_DescriptionCaption">
          <DataField>Item_DescriptionCaption</DataField>
        </Field>
        <Field Name="Item__No__Caption">
          <DataField>Item__No__Caption</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control23Caption">
          <DataField>TotalInvtQty_Control23Caption</DataField>
        </Field>
        <Field Name="InvtValue_1_Caption">
          <DataField>InvtValue_1_Caption</DataField>
        </Field>
        <Field Name="InvtValue_1__Control31Caption">
          <DataField>InvtValue_1__Control31Caption</DataField>
        </Field>
        <Field Name="TotalCaption">
          <DataField>TotalCaption</DataField>
        </Field>
        <Field Name="Item_No_">
          <DataField>Item_No_</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control23">
          <DataField>TotalInvtValue_Control23</DataField>
        </Field>
        <Field Name="TotalInvtValue_Control23Format">
          <DataField>TotalInvtValue_Control23Format</DataField>
        </Field>
        <Field Name="InvtValue_5__Control28">
          <DataField>InvtValue_5__Control28</DataField>
        </Field>
        <Field Name="InvtValue_5__Control28Format">
          <DataField>InvtValue_5__Control28Format</DataField>
        </Field>
        <Field Name="InvtValue_4__Control49">
          <DataField>InvtValue_4__Control49</DataField>
        </Field>
        <Field Name="InvtValue_4__Control49Format">
          <DataField>InvtValue_4__Control49Format</DataField>
        </Field>
        <Field Name="InvtValue_3__Control48">
          <DataField>InvtValue_3__Control48</DataField>
        </Field>
        <Field Name="InvtValue_3__Control48Format">
          <DataField>InvtValue_3__Control48Format</DataField>
        </Field>
        <Field Name="InvtValue_2__Control47">
          <DataField>InvtValue_2__Control47</DataField>
        </Field>
        <Field Name="InvtValue_2__Control47Format">
          <DataField>InvtValue_2__Control47Format</DataField>
        </Field>
        <Field Name="InvtValue_1__Control32">
          <DataField>InvtValue_1__Control32</DataField>
        </Field>
        <Field Name="InvtValue_1__Control32Format">
          <DataField>InvtValue_1__Control32Format</DataField>
        </Field>
        <Field Name="Item_Description">
          <DataField>Item_Description</DataField>
        </Field>
        <Field Name="Item__No__">
          <DataField>Item__No__</DataField>
        </Field>
        <Field Name="Integer_Number">
          <DataField>Integer_Number</DataField>
        </Field>
        <Field Name="InvtQty_1__Control32">
          <DataField>InvtQty_1__Control32</DataField>
        </Field>
        <Field Name="InvtQty_1__Control32Format">
          <DataField>InvtQty_1__Control32Format</DataField>
        </Field>
        <Field Name="Invtqty_2__Control47">
          <DataField>Invtqty_2__Control47</DataField>
        </Field>
        <Field Name="Invtqty_2__Control47Format">
          <DataField>Invtqty_2__Control47Format</DataField>
        </Field>
        <Field Name="InvtQty_3__Control48">
          <DataField>InvtQty_3__Control48</DataField>
        </Field>
        <Field Name="InvtQty_3__Control48Format">
          <DataField>InvtQty_3__Control48Format</DataField>
        </Field>
        <Field Name="InvtQty_4__Control49">
          <DataField>InvtQty_4__Control49</DataField>
        </Field>
        <Field Name="InvtQty_4__Control49Format">
          <DataField>InvtQty_4__Control49Format</DataField>
        </Field>
        <Field Name="InvtQty_5__Control28">
          <DataField>InvtQty_5__Control28</DataField>
        </Field>
        <Field Name="InvtQty_5__Control28Format">
          <DataField>InvtQty_5__Control28Format</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control23">
          <DataField>TotalInvtQty_Control23</DataField>
        </Field>
        <Field Name="TotalInvtQty_Control23Format">
          <DataField>TotalInvtQty_Control23Format</DataField>
        </Field>
        <Field Name="InvtValue_6__Control28">
          <DataField>InvtValue_6__Control28</DataField>
        </Field>
        <Field Name="InvtValue_6__Control28Format">
          <DataField>InvtValue_6__Control28Format</DataField>
        </Field>
        <Field Name="InvtQty_6__Control28">
          <DataField>InvtQty_6__Control28</DataField>
        </Field>
        <Field Name="InvtQty_6__Control28Format">
          <DataField>InvtQty_6__Control28Format</DataField>
        </Field>
        <Field Name="InvtQty_7__Control28">
          <DataField>InvtQty_7__Control28</DataField>
        </Field>
        <Field Name="InvtQty_7__Control28Format">
          <DataField>InvtQty_7__Control28Format</DataField>
        </Field>
        <Field Name="InvtValue_7__Control28">
          <DataField>InvtValue_7__Control28</DataField>
        </Field>
        <Field Name="InvtValue_7__Control28Format">
          <DataField>InvtValue_7__Control28Format</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>