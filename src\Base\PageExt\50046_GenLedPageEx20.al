pageextension 50046 GenLedPageEx20 extends "General Ledger Entries"
{
    layout
    {
        //BaluonAug26>>
        modify("Global Dimension 1 Code")
        {
            Visible = true;
        }
        modify("Global Dimension 2 Code")
        {
            Visible = true;
        }
        //BaluonAug26<<
        addafter("VAT Amount")
        {
            field(Narration; Narration)
            {
                ApplicationArea = all;
                Editable = false;
            }
            field(Narration1; Narration1)
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Description 2"; "Description 2")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("Provision Entry"; "Provision Entry")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Provisional Entry Reversed"; "Provisional Entry Reversed")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Reversal For Prov Entry No."; "Reversal For Prov Entry No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Source No."; "Source No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("ShortCut Dimension code 3"; "ShortCut Dimension code 3")
            {
                ApplicationArea = all;
                Editable = false;
            }
            //Fix05Jul2021>>
            field("ShortCut Dimension code 4"; "ShortCut Dimension code 4")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("ShortCut Dimension code 5"; "ShortCut Dimension code 5")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("ShortCut Dimension code 6"; "ShortCut Dimension code 6")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("ShortCut Dimension code 7"; "ShortCut Dimension code 7")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("ShortCut Dimension code 8"; "ShortCut Dimension code 8")
            {
                ApplicationArea = all;
                Editable = false;
            }
            //Fix05Jul2021<<
            field("Capex No."; "Capex No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Capex Line No."; "Capex Line No.")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Budget Name"; "Budget Name")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Source Description"; "Source Description")
            {
                ApplicationArea = all;

            }
        }
        addafter("Document No.")
        {
            field("Allocation Maintenance Code"; "Allocation Maintenance Code")
            {
                ApplicationArea = all;
            }
            field("Old Document No."; "Old Document No.")
            {
                ApplicationArea = all;
            }
        }
    }


    actions
    {
        modify(ReverseTransaction)
        {
            trigger OnBeforeAction()
            var
                WHTLedgerLRec: Record "WHT Buffer";
                USerset: Record "User Setup";
            begin
                //PKONJU27 >>
                IF USerset.get(UserId) AND NOT USerset."Reverse G/L Entry" then
                    error('You dont have any permissions to reverse the transaction.');
                //PKONJU27 <<
                //Check if settlement is done.
                WHTLedgerLRec.Reset();
                WHTLedgerLRec.SetRange("DocNo.", "Document No.");
                WHTLedgerLRec.Setfilter("Posted App. Voucher No", '<>%1', '');
                if WHTLedgerLRec.FindSet() then
                    Error('You can not reverse this transaction as settlement is done.');
            end;

            trigger OnAfterAction()
            var
                WHTLedgerLRec: Record "WHT Buffer";
            begin
                //Check if settlement is done.
                /*WHTLedgerLRec.Reset();
                WHTLedgerLRec.Setfilter("Posted App. Voucher No", '<>%1', '');
                if WHTLedgerLRec.FindSet() then
                    IF WHTLedgerLRec."Posted App. Voucher No" <> "Document No." then
                        Error('You can not reverse this transaction as settlement is done.');*/

                //Reverse Payment
                WHTLedgerLRec.Reset();
                WHTLedgerLRec.SetRange("DocNo.", "Document No.");
                WHTLedgerLRec.Setfilter("Posted App. Voucher No", '=%1', '');
                if WHTLedgerLRec.FindSet() then
                    repeat
                        WHTLedgerLRec.Reversed := True;
                        WHTLedgerLRec.Modify;
                    until WHTLedgerLRec.Next() = 0;

                //Reverse Settlement
                WHTLedgerLRec.Reset();
                WHTLedgerLRec.SetRange("Posted App. Voucher No", "Document No.");
                if WHTLedgerLRec.FindSet() then
                    repeat
                        CLEAR(WHTLedgerLRec."Posted App. Voucher No");
                        CLEAR(WHTLedgerLRec."App. Voucher No");
                        WHTLedgerLRec."Remaining Amount" := WHTLedgerLRec."WHT Amount";
                        WHTLedgerLRec."Remaining Amount(LCY)" := WHTLedgerLRec."WHT Amount(LCY)";
                        WHTLedgerLRec.Modify;
                    until WHTLedgerLRec.Next() = 0;
            END;


        }

    }

    var
        myInt: Integer;
}