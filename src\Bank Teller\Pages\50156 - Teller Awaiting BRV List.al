page 50156 "Teller Awaiting BRV List"
{

    Caption = 'Teller Awaiting BRV';
    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Confirmed Teller Receipt";
    SourceTableView = WHERE("Bank Receipt Created" = FILTER(false));
    UsageCategory = Lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posted By"; "Posted By")
                {
                    ApplicationArea = all;
                }
                field("Posted Date"; "Posted Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Issued"; "Bank Issued")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cheque No."; "Cheque No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Cheque Date"; "Cheque Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Create BRV"; "Create BRV")
                {
                    ApplicationArea = all;
                }
                field("Return Confirmed Bank Teller"; "Return Confirmed Bank Teller")
                {
                    ApplicationArea = all;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                }
                field("Chq Value Date"; "Chq Value Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnOpenPage();
    begin
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);

        /*
        RespCentCount :=0;
        
        UserIDRespCent.SETCURRENTKEY("User ID","Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
         REPEAT
          RespCentCount +=1;
          //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
          IF RespCentCount = 1 THEN
           BuildFilter :=UserIDRespCent."Resp. Center Code"
          ELSE
           BuildFilter+='|' + UserIDRespCent."Resp. Center Code";
         UNTIL UserIDRespCent.NEXT=0;
        
        IF RespCentCount > 0 THEN
         //SETRANGE("Responsibility Center",TempResp[1],TempResp[RespCentCount]);
         SETFILTER("Responsibility Center",BuildFilter);
         */

    end;

    var
        BankConfirmTellersRec: Record "Confirmed Teller Receipt";
        BankTellerConfirmationRec: Record "Request Teller Receipt";
        Linecount: Integer;
        Window: Dialog;
        OldBankTellerConfirmationRec: Record "Request Teller Receipt";
        Text50200: Label 'Return Confirmed Bank Teller must not be false';
        Text50201: Label 'You do not have permission to Reverse Confirmed Bank Tellers';
        Text50202: Label 'Posting lines         #2######';
        UserSetup: Record "User Setup";
        Text50203: Label 'You do not have permission for Teller/Cheque Awaiting BRV.';
        UserMgt: Codeunit "User Setup Management";
        RespCentCount: Integer;
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";//B2BSB.1.0
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

