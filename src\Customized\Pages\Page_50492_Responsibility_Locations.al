page 50188 "Responcibility Locations"
{
    PageType = List;
    SourceTable = "Responsibility Center Location";
    UsageCategory = lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

