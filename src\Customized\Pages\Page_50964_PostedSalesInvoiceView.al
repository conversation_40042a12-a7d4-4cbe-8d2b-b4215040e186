page 50964 "Posted Sales Invoice View"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Sales Invoice Line";
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;
    //Permissions = tabledata "Sales Invoice Line" = rd;

    layout
    {
        area(Content)
        {
            repeater(Details)
            {
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;

                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;

                }
                field(Type; Type)
                {
                    ApplicationArea = All;

                }
                field("No."; "No.")
                {
                    ApplicationArea = All;

                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = All;

                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = All;

                }
                field("Unit Price"; "Unit Price")
                {
                    ApplicationArea = All;

                }
                field(Amount; Amount)
                {
                    ApplicationArea = All;
                }
                field("Amount Including VAT"; "Amount Including VAT")
                {
                    ApplicationArea = All;
                }


                field("Sell-to Customer No."; "Sell-to Customer No.")
                {
                    ApplicationArea = All;

                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = All;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = All;
                }
                field("Posted Loading Slip No."; "Posted Loading Slip No.")
                {
                    ApplicationArea = All;
                }
                field("Posted Loading Slip Line No."; "Posted Loading Slip Line No.")
                {
                    ApplicationArea = All;
                }







                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = All;
                }

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}