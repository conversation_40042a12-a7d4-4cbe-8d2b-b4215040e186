pageextension 50087 TransferOrderSubform extends "Transfer Order Subform"
{
    layout
    {
        addafter(Quantity)
        {
            field("Transfer Price"; "Transfer Price")
            {
                ApplicationArea = all;
                Visible = false;
            }
            field(Amount; Amount)
            {
                ApplicationArea = all;
            }
            field("Branch Request Qty"; "Branch Request Qty")
            {
                ApplicationArea = all;
            }
            field(ItemInventory; itemAv.Inventory)
            {
                ApplicationArea = Location;
                Caption = 'Inventory';
                DecimalPlaces = 0 : 5;
                DrillDown = true;
                Editable = false;
                ToolTip = 'Specifies the inventory level of an item.';//B2BPKON200521 Move entire field

                trigger OnDrillDown()
                begin
                    SetItemFilter;
                    //Message('%1', itemAv."No.");
                    ItemAvailFormsMgt.ShowItemLedgerEntries(itemAv, false);
                end;
            }
            field(ProjAvailableBalance; ProjAvailableBalance)
            {
                ApplicationArea = Location;
                Caption = 'Projected Available Balance';//B2BPKON200521 Move entire field
                DecimalPlaces = 0 : 5;
                ToolTip = 'Specifies the item''s availability. This quantity includes all known supply and demand but does not include anticipated demand from demand forecasts or blanket sales orders or suggested supplies from planning or requisition worksheets.';
                Editable = false;
                trigger OnDrillDown()
                begin
                    ShowItemAvailLineList(4);
                end;
            }
            field("Production Order No."; "Production Order No.")
            {
                ApplicationArea = all;
            }
            field("Production Order Line No."; "Production Order Line No.")
            {
                ApplicationArea = all;
            }
            field("Gen. Prod. Posting Group"; "Gen. Prod. Posting Group")
            {
                ApplicationArea = all;
            }
        }
        modify(Quantity)
        {
            trigger OnBeforeValidate()
            var
                Validation: Codeunit Validations;
            begin
                Validation.CheckQtyValidations(Rec);//PKON052421
            end;
        }
    }
    actions
    {
        addafter("Item &Tracking Lines")
        {
            action("Bin Reclassfication Jnl")
            {
                Caption = 'Bin Reclassfication Jnl';
                ApplicationArea = all;
                Image = Journal;
                trigger OnAction()
                var
                    Usersetup: Record "User Setup";
                begin
                    TestField("Item No.");
                    TestField(Quantity);
                    Usersetup.get(UserId);
                    IF Usersetup."Bin Reclassification" then
                        BinReclassJnlLine()
                    else
                        Error('You Dont have permissions to Open and Process Bin Reclassification.');
                end;
            }
            action("Bin Reclassfication Shipment")
            {
                Caption = 'Bin Reclassfication Shipment';
                ApplicationArea = all;
                Image = InwardEntry;
                trigger OnAction()
                var
                    Usersetup: Record "User Setup";
                begin
                    Usersetup.get(UserId);
                    IF Usersetup."Bin Reclassification" then
                        BinReclassfication
                    else
                        Error('You Dont have permissions to Open and Process Bin Reclassification.');
                    SendBinRecMai();
                end;

            }
            action("Insert Item tracking for Receipt")
            {
                Caption = 'Insert Item tracking for Receipt';
                ApplicationArea = all;
                Image = InwardEntry;
                //Visible = false;
                trigger OnAction()
                begin
                    if Not Confirm('Do you want to insert item tracking for receipt for missing Lines ?', True, False) then
                        exit;
                    Insertitemtracking();
                end;

            }
            // >>>>>> G2S 29/11/2024 CAS-01332-P7K0T6_AssignLotNo
            action(AssignMissingLotNo)
            {
                Caption = 'Assign Missing Lot No. On Receipt';
                ToolTip = 'Assign Missing Lot No. On Receipt with ''You must assign a lot number for item.''';
                ApplicationArea = all;
                Image = InwardEntry;
                //Visible = false;
                trigger OnAction()
                begin
                    if Not Confirm('Do you want to insert item tracking for receipt with Missing Lot No. ?', True, False) then
                        exit;
                    InsertitemReservation();
                end;

            }
        }
        //B2BMSOnAug11>>
        modify(Shipment)
        {
            visible = false;
        }
        modify(Receipt)
        {
            visible = false;
        }
        addafter(Shipment)
        {
            action(Shipment1)
            {
                ApplicationArea = ItemTracking;
                Caption = 'Shipment';
                Image = Shipment;
                ToolTip = 'View or edit serial numbers and lot numbers that are assigned to the item on the document or journal line.';

                trigger OnAction()
                begin
                    //OpenItemTrackingLines(0);
                    OpenItemTrackingLinesCopy(0);
                end;
            }
            action(Receipt1)
            {
                ApplicationArea = ItemTracking;
                Caption = 'Receipt';
                Image = Receipt;
                ToolTip = 'View or edit serial numbers and lot numbers that are assigned to the item on the document or journal line.';

                trigger OnAction()
                begin
                    //OpenItemTrackingLines(1);
                    OpenItemTrackingLinesCopy(1);
                end;
            }
        }
        //B2BMSOnAug11<<


    }

    procedure BinReclassfication()
    var
        WareHouseEntry: Record "Warehouse Entry";
        WareHouseEntry2: record "Warehouse Entry";
        ItemJnlLine2: Record "Item Journal Line";
        ItemJnlBatch: Record "Item Journal Batch";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        ILELotRec: Record "Item Ledger Entry";
        AvaliableQty: Decimal;
        LineQtyBase: Decimal;
        ReqQtyBase: Decimal;

        BinQty: Decimal;

        ItemJnlBatchErr: Label 'you must specify No. series in journal template name % Journal bacth name %2';
        ItemJnlReclassMsg: Label 'There is nothing to process';
        ItemJnlMsg: Label 'Bin Reclassfication successfully';
        ItemJnlConfirm: Label 'Do you want to process reclassfication?';
        ReservEntry: Record "Reservation Entry";
        ItemJoulineLrec: Record "Item Journal Line";
    begin
        //B2BPKON260521>>
        BinReclassJnl.Reset;
        //BinReclassJnl.Setrange("Document Type", BinReclassJnl."Document Type"::Whsship);
        BinReclassJnl.Setrange("Document No.", Rec."Document No.");
        BinReclassJnl.Setrange("Document Line No.", Rec."Line No.");
        BinReclassJnl.Setrange("Bin Code", Rec."Transfer-from Bin Code");
        BinReclassJnl.SetFilter(Quantity, '>%1', 0);
        BinReclassJnl.Setfilter(Reclassfication, '%1', false);
        if BinReclassJnl.FindFIRST then
            Error('From Bin and To Bin Should not be same');
        //B2BPKON260521<<

        if Not Confirm(ItemJnlConfirm, False) then
            Exit;


        Clear(ItemJnlPostBatch);
        Clear(LineQtyBase);
        Clear(ReqQtyBase);

        clear(NextLineNo);
        CLEAR(NoSeriesMgt);
        clear(InsertedLine);
        clear(LotFilterText);


        ReservEntry.RESET;
        ReservEntry.SETCURRENTKEY(
        "Source ID", "Source Ref. No.", "Source Type", "Source Subtype", "Source Batch Name", "Source Prod. Order Line");
        ReservEntry.SETRANGE("Source ID", Rec."Document No.");
        ReservEntry.SETRANGE("Source Ref. No.", Rec."Line No.");
        ReservEntry.SETRANGE("Source Type", DATABASE::"Transfer Line");
        ReservEntry.SETRANGE("Source Subtype", 0);
        IF ReservEntry.FINDSET THEN
            ReservEntry.DELETEALL(TRUE);
        Testfield("Qty. to Ship");

        LineQtyBase := "Qty. to Ship (Base)";
        TransferHeader.get(rec."Document No.");
        Location.get(TransferHeader."Transfer-from Code");

        ItemJnlLine2.Reset;
        ItemJnlLine2.SETRANGE("Journal Template Name", 'RECLASS');
        ItemJnlLine2.SETRANGE("Journal Batch Name", 'DEFAULT');
        if ItemJnlLine2.FindFirst() then
            ItemJnlLine2.DeleteAll();

        ItemJnlBatch.GET('RECLASS', 'DEFAULT');
        IF ItemJnlBatch."No. Series" = '' THEN
            Error(ItemJnlBatchErr);
        NextDocNo := NoSeriesMgt.GetNextNo(ItemJnlBatch."No. Series", workdate, FALSE);






        ILELotRec.Reset;
        ILELotRec.SetCurrentKey("Item No.", "Location Code", "Expiration Date");
        if Location."Batch Assign" = Location."Batch Assign"::LEFO then
            ILELotRec.SetAscending("Expiration Date", False);
        ILELotRec.SETRANGE("Location Code", "Transfer-from Code");
        ILELotRec.SETRANGE("Item No.", "Item No.");
        ILELotRec.SETRANGE("Variant Code", "Variant Code");
        ILELotRec.SETfilter(Open, '%1', true);
        ILElotRec.SetFilter("Lot No.", '<>%1', '');
        IF ILELotRec.Findset THEN begin
            Repeat

                Clear(AvaliableQty);
                AvaliableQty := ILELotRec."Remaining Quantity";



                if LineQtyBase <= AvaliableQty then begin
                    ReqQtyBase := LineQtyBase;
                    LineQtyBase := 0;

                    InsertReclassJnl(ILELotRec, ReqQtyBase);
                end else begin
                    ReqQtyBase := AvaliableQty;
                    LineQtyBase -= AvaliableQty;
                    InsertReclassJnl(ILELotRec, ReqQtyBase);
                end;
                if LotFilterText <> '' then
                    LotFilterText += '|' + ILELotRec."Lot No."
                else
                    LotFilterText := ILELotRec."Lot No.";


            until (ILELotRec.next = 0) or (LineQtyBase = 0);
            if insertedline then begin
                CLEAR(ItemJoulineLrec);
                ItemJoulineLrec.RESET;
                ItemJoulineLrec.SETRANGE("Journal Template Name", 'RECLASS');
                ItemJoulineLrec.SETRANGE("Journal Batch Name", 'DEFAULT');
                if ItemJoulineLrec.FindSET() then
                    ItemJnlPostBatch.RUN(ItemJoulineLrec);//PKONAU25
                //ItemJnlPostBatch.RUN(ItemJnlLine);//PKONAU25
                //AssignItemTracking();

            end else
                Message(ItemJnlReclassMsg);

        end;

    end;

    procedure InsertReclassJnl(var ItemLedgerEntryParRec: Record "Item Ledger Entry"; ReqQtyBasePar: decimal)
    var

        ItemUOM: Record "Item Unit of Measure";
        ReclassQtyBase: Decimal;
    begin
        clear(ReclassQtyBase);
        BinReclassJnl.Reset;
        BinReclassJnl.Setrange("Document Type", BinReclassJnl."Document Type"::Transfer);
        BinReclassJnl.Setrange("Document No.", Rec."Document No.");
        BinReclassJnl.Setrange("Document Line No.", Rec."Line No.");
        BinReclassJnl.SetFilter(Quantity, '>%1', 0);
        BinReclassJnl.Setfilter(Reclassfication, '%1', false);
        if BinReclassJnl.FindSet then begin

            repeat
                clear(ReclassQtyBase);
                if ReqQtyBasePar < (BinReclassJnl."Qty.(Base)" - BinReclassJnl."Qty.Reclassfied Base") then begin
                    ReclassQtyBase := ReqQtyBasePar;
                    ReqQtyBasePar := 0;
                    BinReclassJnl."Qty.Reclassfied Base" += ReclassQtyBase;
                    BinReclassJnl.Modify;
                end else begin
                    ReclassQtyBase := (BinReclassJnl."Qty.(Base)" - BinReclassJnl."Qty.Reclassfied Base");
                    ReqQtyBasePar -= ReclassQtyBase;
                    BinReclassJnl."Qty.Reclassfied Base" += ReclassQtyBase;
                    BinReclassJnl.Reclassfication := true;
                    BinReclassJnl.Modify;
                end;
                NextLineNo := NextLineNo + 10000;
                ItemUOM.Get(Rec."Item No.", Rec."Unit of Measure Code");
                ItemJnlLine.INIT();
                ItemJnlLine."Journal Template Name" := 'RECLASS';
                ItemJnlLine."Journal Batch Name" := 'DEFAULT';
                ItemJnlLine."Posting Date" := WORKDATE();
                ItemJnlLine."Document Date" := WORKDATE();
                ItemJnlLine."Document No." := NextDocNo;
                ItemJnlLine."Line No." := NextLineNo;
                ItemJnlLine."Entry Type" := ItemJnlLine."Entry Type"::Transfer;
                ItemJnlLine.VALIDATE("Item No.", ItemLedgerEntryParRec."Item No.");

                ItemJnlLine.VALIDATE("Variant Code", ItemLedgerEntryParRec."Variant Code");
                ItemJnlLine.VALIDATE("Location Code", ItemLedgerEntryParRec."Location Code");
                ItemJnlLine.VALIDATE("Unit of Measure Code", REC."Unit of Measure Code");
                ItemJnlLine.validate(Quantity, ROUND(ReclassQtyBase / ItemUOM."Qty. per Unit of Measure", 0.00001));
                ItemJnlLine.validate("Quantity (Base)", ReclassQtyBase);


                ItemJnlLine."Bin Code" := BinReclassJnl."Bin Code";
                ItemJnlLine."New Bin Code" := "Transfer-from Bin Code";
                ItemJnlLine."Warranty Date" := ItemLedgerEntryParRec."Warranty Date";
                ItemJnlLine."Expiration Date" := ItemLedgerEntryParRec."Expiration Date";
                ItemJnlLine.INSERT();
                CreateDime(ItemJnlLine, ItemLedgerEntryParRec);//PKONAU12
                ItemJnlLine.Modify();//PKONAU12
                UpdateResEntry(ItemJnlLine, ItemLedgerEntryParRec."Lot No.");


                insertedline := true;

            until (BinReclassJnl.Next = 0) or (ReqQtyBasePar = 0);
        end;


    END;

    procedure CreateDime(Var IJLDim: Record "Item Journal Line"; ILEQt: Record "Item Ledger Entry")
    var
        TrackingSpecLv: Record "Tracking Specification";
        ReservationEntry: Record "Reservation Entry";
    begin
        IJLDim.Validate("Dimension Set ID", ILEQt."Dimension Set ID");
        IJLDim."Shortcut Dimension 1 Code" := ILEQt."Global Dimension 1 Code";
        IJLDim."Shortcut Dimension 2 Code" := ILEQt."Global Dimension 2 Code";
        IJLDim.Validate("New Dimension Set ID", ILEQt."Dimension Set ID");
        IJLDim."New Shortcut Dimension 1 Code" := ILEQt."Global Dimension 1 Code";
        IJLDim."New Shortcut Dimension 2 Code" := ILEQt."Global Dimension 2 Code";
    end;


    procedure AssignItemTracking()
    var


        ItemLedgerEntry: Record "Item Ledger Entry";
        ReservationEntry: record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";

        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;

        WhseTrackErr: Label 'Sufficient quantity not exist in Bin code %1 .';
        LotNumErr: Label 'Item Ledger entries not exist in item lot combination';

        LineInserted: Boolean;
        TrackingAssigned: Label 'Tacking Assigned successfully.';
    Begin

        Testfield("Qty. to Ship (Base)");

        LineQty := "Qty. to Ship (Base)";



        if LotFilterText = '' then
            Error(LotNumErr);
        ReservationEntry2.RESET;
        IF ReservationEntry2.FINDLAST THEN
            EntryNum := ReservationEntry2."Entry No." + 1
        ELSE
            EntryNum := 1;
        ItemLedgerEntry.RESET;
        ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
        if Location."Batch Assign" = Location."Batch Assign"::LEFO then
            ItemLedgerEntry.SetAscending("Expiration Date", false);
        ItemLedgerEntry.SETRANGE("Item No.", Rec."Item No.");
        ItemLedgerEntry.SETRANGE("Location Code", Rec."Transfer-from Code");
        ItemLedgerEntry.SETRANGE("Variant Code", Rec."Variant Code");
        ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
        ItemLedgerEntry.SETFILTER("Lot No.", LotFilterText);
        IF ItemLedgerEntry.FINDSET THEN BEGIN

            REPEAT

                IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                    AssignQty := LineQty;
                    LineQty := 0;
                END ELSE BEGIN
                    AssignQty := (ItemLedgerEntry."Remaining Quantity");
                    LineQty -= AssignQty;
                END;


                ReservationEntry.INIT;
                ReservationEntry."Entry No." := EntryNum;
                ReservationEntry.VALIDATE(Positive, FALSE);
                ReservationEntry.VALIDATE("Item No.", Rec."Item No.");
                ReservationEntry.VALIDATE("Location Code", Rec."Transfer-from Code");
                ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / Rec."Qty. per Unit of Measure"));
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                ReservationEntry.VALIDATE("Source Type", DATABASE::"Transfer line");
                ReservationEntry.VALIDATE("Source Subtype", 0);
                ReservationEntry.VALIDATE("Source ID", rec."Document No.");

                ReservationEntry.VALIDATE("Source Ref. No.", Rec."Line No.");
                ReservationEntry.VALIDATE("Shipment Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                ReservationEntry."Created By" := USERID;
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.INSERT;
                EntryNum += 1;
                LineInserted := true;

                ReservationEntry.INIT;
                ReservationEntry."Entry No." := EntryNum;
                ReservationEntry.VALIDATE(Positive, true);
                ReservationEntry.VALIDATE("Item No.", Rec."Item No.");
                ReservationEntry.VALIDATE("Location Code", Rec."Transfer-to Code");
                ReservationEntry.VALIDATE("Quantity (Base)", AssignQty);
                ReservationEntry.VALIDATE(Quantity, ROUND(AssignQty / Rec."Qty. per Unit of Measure"));
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                ReservationEntry.VALIDATE("Source Type", DATABASE::"Transfer line");
                ReservationEntry.VALIDATE("Source Subtype", 1);
                ReservationEntry.VALIDATE("Source ID", rec."Document No.");

                ReservationEntry.VALIDATE("Source Ref. No.", Rec."Line No.");
                ReservationEntry.VALIDATE("Shipment Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                ReservationEntry."Created By" := USERID;
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.INSERT;
                EntryNum += 1;


            UNTIL (ItemLedgerEntry.NEXT = 0) OR (LineQty = 0);

        END;

        if LineInserted then
            Message(TrackingAssigned)
    END;

    procedure UpdateResEntry(VAR ItemJournalLineParRec2: Record "Item Journal Line"; LotCodePar: Code[20]);
    Var
        ReservationEntry: Record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";
        EntryNum: Integer;

    begin
        IF ReservationEntry2.FINDlast() THEN
            EntryNum := ReservationEntry2."Entry No."
        ELSE
            EntryNum := 0;
        ReservationEntry.INIT();
        ReservationEntry."Entry No." := EntryNum + 1;
        ReservationEntry.VALIDATE(Positive, FALSE);
        ReservationEntry.VALIDATE("Item No.", ItemJournalLineParRec2."Item No.");
        ReservationEntry.VALIDATE("Location Code", ItemJournalLineParRec2."Location Code");
        ReservationEntry.VALIDATE("variant Code", ItemJournalLineParRec2."Variant Code");

        ReservationEntry.VALIDATE("Quantity (Base)", -ItemJournalLineParRec2."Quantity (Base)");
        ReservationEntry.VALIDATE(Quantity, -ItemJournalLineParRec2.Quantity);
        ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Prospect);
        ReservationEntry.VALIDATE("Creation Date", ItemJournalLineParRec2."Posting Date");
        ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
        ReservationEntry.VALIDATE("Source Subtype", 4);
        ReservationEntry.VALIDATE("Source ID", ItemJournalLineParRec2."Journal Template Name");
        ReservationEntry.VALIDATE("Source Batch Name", ItemJournalLineParRec2."Journal Batch Name");
        ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLineParRec2."Line No.");
        ReservationEntry.VALIDATE("Shipment Date", ItemJournalLineParRec2."Posting Date");

        ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
        ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);

        ReservationEntry.VALIDATE("Lot No.", LotCodePar);
        ReservationEntry.VALIDATE("New Lot No.", LotCodePar);
        ReservationEntry.VALIDATE("Expiration Date", ItemJournalLineParRec2."Expiration Date");
        //PKONJ25.2
        IF ItemJournalLineParRec2."Expiration Date" <> 0D THEN
            ReservationEntry.VALIDATE("New Expiration Date", ItemJournalLineParRec2."Expiration Date")
        else begin
            ReservationEntry.VALIDATE("Expiration Date", WorkDate());
            ReservationEntry.VALIDATE("New Expiration Date", CalcDate('6M', Today));
        end;
        //PKONJ25.2
        ReservationEntry.VALIDATE(Correction, FALSE);
        ReservationEntry.INSERT();



    end;

    procedure BinReclassJnlLine()
    var
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        BinReclassJnlLines: Page "Bin Reclassfication Jnl";
        SalesLine: Record "Sales Line";
        WhseShipmenLine: Record "Warehouse Shipment Line";
    begin
        Clear(BinReclassJnlLines);
        BinReclassJnl2.SetRange("Document Type", BinReclassJnl2."Document Type"::Transfer);
        BinReclassJnl2.SetRange("Document No.", "Document No.");
        BinReclassJnl2.SetRange("Document Line No.", "Line No.");

        BinReclassJnlLines.SetSourceValues(BinReclassJnl2."Document Type"::Transfer, SalesLine, Rec, WhseShipmenLine);
        BinReclassJnlLines.SetTableView(BinReclassJnl2);
        BinReclassJnlLines.RunModal;
    end;

    procedure SendBinRecMai()
    var
        InvSetup: record "Inventory Setup";
        ItemLedgerEntry: record "Item Ledger Entry";
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
    begin
        IF InvSetup.get() And (InvSetup."Bin Reclass Mail Alert ID" <> '') then begin
            SMTPMailSetup.get();
            SenderAddr := SMTPMailSetup."User ID";
            BinReclassJnl2.RESET();
            BinReclassJnl2.SetRange("Document No.", "Document No.");
            BinReclassJnl2.SetRange("Document Line No.", "Line No.");
            IF BinReclassJnl2.FINDFIRST THEN begin
                RecepientAddr.Add(InvSetup."Bin Reclass Mail Alert ID");
                if InvSetup."Bin Reclass Mail Alert ID2" <> '' then
                    RecepientAddr.Add(InvSetup."Bin Reclass Mail Alert ID2");
                SubjectTxt := 'Bin Reclassification Posted For  -' + FORMAT(BinReclassJnl2."Document No.") + '-' + FORMAT(BinReclassJnl2."Line No.") + ' On ' + FORMAT(WorkDate());
                SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
                SMTPMail.AppendBody(CreateEmailBodyVend(BinReclassJnl2));
                SMTPMAil.send;
            end;
        end;
    end;

    Local procedure CreateEmailBodyVend(BinReclassJnl2Lpa: Record "Bin Reclassfication Jnl") EmailBodyText: Text
    var
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
    begin
        BinReclassJnl2.Reset();
        BinReclassJnl2.SetRange("Document No.", BinReclassJnl2Lpa."Document No.");
        BinReclassJnl2.SetRange("Line No.", BinReclassJnl2Lpa."Line No.");
        IF BinReclassJnl2.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Document No.-' + BinReclassJnl2."Document No.");
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Line No.-' + FORMAT(BinReclassJnl2Lpa."Line No."));
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Date-' + FORMAT(WorkDate()));
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'UserID-' + USERID);
            EmailBodyText += '<tr>';
            EmailBodyText += '</tr>';
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Variant Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'From Location Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'From Bin Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'TO Location Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'TO Bin Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Variant Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Location Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Bin Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', "Transfer-from Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', "Transfer-from Bin Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2.Quantity);
                EmailBodyText += '</tr>';
            until BinReclassJnl2.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    local procedure Insertitemtracking()
    var
        ReservationEntry2: Record "Reservation Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        EntryNum: Integer;
        ReservationEntry: Record "Reservation Entry";
        TransferShipment: Record "Transfer Shipment Line";
        TransferLine: Record "Transfer Line";
    begin
        ReservationEntry2.RESET;
        IF ReservationEntry2.FINDLAST THEN
            EntryNum := ReservationEntry2."Entry No." + 1
        ELSE
            EntryNum := 1;
        TransferShipment.Reset();
        TransferShipment.SetRange("Transfer Order No.", "Document No.");
        TransferShipment.SetRange("Item No.", "Item No.");
        TransferShipment.SetRange("Line No.", "Line No.");
        if TransferShipment.FindFirst() then;
        ItemLedgerEntry.RESET;
        ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
        ItemLedgerEntry.SETRANGE("Item No.", Rec."Item No.");
        ItemLedgerEntry.SetRange("Document Type", ItemLedgerEntry."Document Type"::"Transfer Shipment");
        ItemLedgerEntry.SetRange("Document No.", TransferShipment."Document No.");
        ItemLedgerEntry.SetRange("Document Line No.", TransferShipment."Line No.");
        ItemLedgerEntry.SETRANGE("Location Code", Rec."Transfer-from Code");
        //ItemLedgerEntry.SETRANGE("Variant Code", Rec."Variant Code");
        //ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
        //ItemLedgerEntry.SETFILTER("Lot No.", LotFilterText);
        IF ItemLedgerEntry.FindSet() THEN BEGIN
            repeat
                TransferLine.Reset();
                TransferLine.SetRange("Document No.", "Document No.");
                TransferLine.SetRange("Derived From Line No.", "Line No.");
                if TransferLine.FindLast() then;
                ReservationEntry.INIT;
                ReservationEntry."Entry No." := EntryNum;
                ReservationEntry.VALIDATE(Positive, true);
                ReservationEntry.VALIDATE("Item No.", Rec."Item No.");
                ReservationEntry.VALIDATE("Location Code", Rec."Transfer-to Code");
                ReservationEntry.VALIDATE("Quantity (Base)", -1 * ItemLedgerEntry.Quantity);
                ReservationEntry.VALIDATE(Quantity, -1 * ItemLedgerEntry.Quantity);
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                ReservationEntry.VALIDATE("Source Type", DATABASE::"Transfer line");
                ReservationEntry.VALIDATE("Source Subtype", 1);
                ReservationEntry.VALIDATE("Source ID", rec."Document No.");
                ReservationEntry.VALIDATE("Source Prod. Order Line", Rec."Line No.");
                ReservationEntry.VALIDATE("Source Ref. No.", TransferLine."Line No.");
                ReservationEntry.VALIDATE("Expected Receipt Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                ReservationEntry."Created By" := USERID;
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.INSERT;
                EntryNum += 1;
            until ItemLedgerEntry.Next() = 0;
        end;
    end;

    // >>>>>> G2S 29/11/2024 CAS-01332-P7K0T6_AssignLotNo
    local procedure InsertitemReservation()
    var
        ReservationEntry2: Record "Reservation Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        EntryNum: Integer;
        ReservationEntry: Record "Reservation Entry";
        TransferShipment: Record "Transfer Shipment Line";
        TransferLine: Record "Transfer Line";
    begin
        ReservationEntry2.RESET;
        IF ReservationEntry2.FINDLAST THEN
            EntryNum := ReservationEntry2."Entry No." + 1
        ELSE
            EntryNum := 1;
        TransferLine.Reset();
        TransferLine.SetRange("Document No.", "Document No.");
        TransferLine.SetRange("Derived From Line No.", "Line No.");
        TransferLine.Setrange("Item No.", "Item No.");
        if TransferLine.FindSet() then
            repeat
                TransferShipment.Reset();
                TransferShipment.SetRange("Transfer Order No.", "Document No.");
                TransferShipment.SetRange("Item No.", "Item No.");
                TransferShipment.SetRange("Line No.", "Line No.");
                TransferShipment.SetRange(Quantity, TransferLine.Quantity);
                if TransferShipment.FindFirst() then begin
                    ItemLedgerEntry.RESET;
                    ItemLedgerEntry.SETCURRENTKEY("Item No.", "Document Type", "Document No.", "Document Line No.");
                    ItemLedgerEntry.SETRANGE("Item No.", Rec."Item No.");
                    ItemLedgerEntry.SetRange("Document Type", ItemLedgerEntry."Document Type"::"Transfer Shipment");
                    ItemLedgerEntry.SetRange("Document No.", TransferShipment."Document No.");
                    ItemLedgerEntry.SetRange("Document Line No.", TransferShipment."Line No.");
                    ItemLedgerEntry.SETRANGE("Location Code", Rec."Transfer-from Code");
                    IF ItemLedgerEntry.FindFirst() then;

                    TransferLine.SetRange(Quantity, TransferShipment.Quantity);

                    ReservationEntry.INIT;
                    ReservationEntry."Entry No." := EntryNum;
                    ReservationEntry.VALIDATE(Positive, true);
                    ReservationEntry.VALIDATE("Item No.", Rec."Item No.");
                    ReservationEntry.VALIDATE("Location Code", Rec."Transfer-to Code");
                    ReservationEntry.VALIDATE("Quantity (Base)", 1 * TransferLine."Quantity (Base)");
                    ReservationEntry.VALIDATE(Quantity, 1 * TransferLine.Quantity);
                    ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                    ReservationEntry.VALIDATE("Creation Date", WorkDate());
                    ReservationEntry.VALIDATE("Source Type", DATABASE::"Transfer line");
                    ReservationEntry.VALIDATE("Source Subtype", 1);
                    ReservationEntry.VALIDATE("Source ID", rec."Document No.");
                    ReservationEntry.VALIDATE("Source Prod. Order Line", Rec."Line No.");
                    ReservationEntry.VALIDATE("Source Ref. No.", TransferLine."Line No.");
                    ReservationEntry.VALIDATE("Expected Receipt Date", WorkDate());
                    ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                    ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                    ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                    ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                    ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                    ReservationEntry."Created By" := USERID;
                    ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                    ReservationEntry.VALIDATE(Correction, FALSE);
                    ReservationEntry.INSERT;
                    EntryNum += 1;
                end;
            until TransferLine.Next() = 0;
    end;
    // >>>>>> G2S 29/11/2024 CAS-01332-P7K0T6_AssignLotNo


    trigger OnAfterGetRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    local procedure CalcAvailQuantities(var GrossRequirement: Decimal; var PlannedOrderRcpt: Decimal; var ScheduledRcpt: Decimal; var PlannedOrderReleases: Decimal; var ProjAvailableBalance: Decimal; var ExpectedInventory: Decimal; var AvailableInventory: Decimal)
    var
        DummyQtyAvailable: Decimal;
    begin
        SetItemFilter;
        ItemAvailFormsMgt.CalcAvailQuantities(
          itemAv, AmountType = AmountType::"Balance at Date",
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, DummyQtyAvailable, AvailableInventory);
    end;

    local procedure SetItemFilter()
    begin
        itemAv.RESEt;
        itemAv.SetRange("No.", "Item No.");
        /*if AmountType = AmountType::"Net Change" then
            itemAv.SetRange("Date Filter", PeriodStart, PeriodEnd)
        else*/
        itemAv.SetRange("Date Filter", 0D, WorkDate());
        itemAv.SetRange("Location Filter", "Transfer-from Code");
        IF itemAv.findfirst then
            itemAv.CalcFields(Inventory);
    end;

    local procedure ShowItemAvailLineList(What: Integer)
    begin
        SetItemFilter;
        ItemAvailFormsMgt.ShowItemAvailLineList(itemAv, What);
    end;

    var
        InsertedLine: Boolean;
        NextLineNo: Integer;
        NextDocNo: code[20];
        ItemJnlLine: Record "Item Journal Line";
        TransferHeader: Record "Transfer Header";
        Location: Record Location;

        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
        BinReclassJnl: Record "Bin Reclassfication Jnl";
        LotFilterText: Text;
        itemAv: Record Item;
        ItemAvailFormsMgt: Codeunit "Item Availability Forms Mgt";
        PlannedOrderReleases: Decimal;
        GrossRequirement: Decimal;
        PlannedOrderRcpt: Decimal;
        ScheduledRcpt: Decimal;
        ProjAvailableBalance: Decimal;
        PeriodStart: Date;
        PeriodEnd: Date;
        AmountType: Option "Net Change","Balance at Date";
        ExpectedInventory: Decimal;
        QtyAvailable: Decimal;




}