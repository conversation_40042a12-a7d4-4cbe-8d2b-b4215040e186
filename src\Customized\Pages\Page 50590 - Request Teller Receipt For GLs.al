page 50899 "Request Teller Receipt For GLs"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      HO       18-Jun-13    -> Form created for Request Teller Receipt functionality.
    // CRF:2019-0090  SAA 28-10-19    -> New Controls added "Currency Code","Narration","Teller Amount (LCY)"

    Caption = 'Request Teller Receipt For GLs';
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Tasks;
    SourceTable = "Request Teller Receipt";
    SourceTableView = WHERE("Released for Confirmation" = FILTER(false),
                            "Teller Returned" = FILTER(false),
                            "Credit Account Type" = FILTER("G/L Account"));


    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field(Company; Company)
                {
                    ApplicationArea = All;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = All;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    Editable = true;
                    ApplicationArea = All;
                    trigger onvalidate()
                    var
                        ResponsibilityCenter: Record "Responsibility Center";
                    begin
                        if ResponsibilityCenter.GET("Responsibility Center") then
                            "Global Dimension 1 Code" := ResponsibilityCenter."Global Dimension 1 Code";
                    end;

                    trigger OnLookup(var Text: Text): Boolean;
                    var
                        ResponsibilityCenter: Record "Responsibility Center";
                    begin
                        RespCent.SETCURRENTKEY(Code);
                        if RespCent.FINDSET then
                            repeat
                                if UserIDRespCent.GET(USERID, RespCent.Code) then
                                    RespCent.MARK(true);
                            until RespCent.NEXT = 0;

                        RespCent.MARKEDONLY(true);
                        if PAGE.RUNMODAL(0, RespCent) = ACTION::LookupOK then
                            "Responsibility Center" := RespCent.Code;
                        if ResponsibilityCenter.GET("Responsibility Center") then
                            "Global Dimension 1 Code" := ResponsibilityCenter."Global Dimension 1 Code";

                    end;
                }
                field("Credit Account Type"; "Credit Account Type")
                {
                    ApplicationArea = All;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = All;
                }
                field("Paid By"; "Paid By")
                {
                    ApplicationArea = All;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    Editable = false;
                    MultiLine = true;
                    ApplicationArea = All;
                }
                field(Narration; Narration)
                {
                    MultiLine = true;
                    ApplicationArea = All;
                }
            }
            group("Teller Details")
            {
                Caption = 'Teller Details';
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = All;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = All;
                }
                field("Chq. Value Date"; "Chq. Value Date")
                {
                    ApplicationArea = All;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = All;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = All;

                    trigger OnDrillDown();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", TODAY);
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = All;
                }
                field("Teller Amount(LCY)"; "Teller Amount(LCY)")
                {
                    ApplicationArea = All;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = All;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = All;
                }
            }
            group("User Trail")
            {
                Caption = 'User Trail';
                field("Created by"; "Created by")
                {
                    ApplicationArea = All;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = All;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = All;
                }
                field("Last Modified By"; "Last Modified By")
                {
                    ApplicationArea = All;
                }
                field("Last Modified Date"; "Last Modified Date")
                {
                    ApplicationArea = All;
                }
                field("Released By"; "Released By")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Released Date"; "Released Date")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Released time"; "Released time")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Re&lease")
                {
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        PerformManualRelease;
                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        //"Responsibility Center" := UserMgt.GetBankConfRegFilter();
        "Credit Account Type" := "Credit Account Type"::"G/L Account";
        "Released for Confirmation" := false;
        "Teller Returned" := false;
    end;

    trigger OnOpenPage();
    begin
        /*
        IF UserMgt.GetBankConfRegFilter() <> '' THEN BEGIN
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMgt.GetBankConfRegFilter());
          FILTERGROUP(0);
        END;
        */

        if UserSetup.GET(USERID) then
            if not UserSetup."Request for Teller Receipt" then
                ERROR(Text001);

    end;

    var
        UserSetup: Record "User Setup";
        UserMgt: Codeunit "User Setup Management";
        UserIDRespCent: Record "UserID Resp. Cent. Lines";
        RespCent: Record "Responsibility Center";
        Text001: Label 'You do not have permission for Request for Teller Receipt.';
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
        ChangeExchangeRate: Page "Change Exchange Rate";
}

