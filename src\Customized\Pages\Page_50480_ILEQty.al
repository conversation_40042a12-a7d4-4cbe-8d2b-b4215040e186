page 50480 ILEQty
{
    // Editable = false;
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = ILEQty;
    AutoSplitKey = true;
    DelayedInsert = true;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Line No."; Rec."Line No.")
                {
                    ApplicationArea = All;
                    editable = false;
                }
                field("ILE No."; Rec."ILE No.")
                {
                    ApplicationArea = All;
                }
                field("Item No."; Rec."Item No.")
                {
                    ApplicationArea = All;
                }
                field("LOT No."; "LOT No.")
                {
                    ApplicationArea = ALL;
                }

                /*field("Posted Loading SLip Line No."; Rec."Posted Loading SLip Line No.")
                {
                    ApplicationArea = All;
                }
                field("Posted Loading SLip No."; Rec."Posted Loading SLip No.")
                {
                    ApplicationArea = All;
                }*/
                field(Quantity; Rec.Quantity)
                {
                    ApplicationArea = All;
                }
                field("Remaining Qty"; Rec."Remaining Qty")
                {
                    ApplicationArea = All;
                }
                field("Deduction Type"; "Deduction Type")
                {
                    ApplicationArea = ALL;
                }
            }

        }
    }

    actions
    {
    }
}

