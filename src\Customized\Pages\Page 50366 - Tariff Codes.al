page 50366 "Tariff Codes"
{
    // version CHI6.0

    PageType = Document;
    SourceTable = "Tariff Codes";
    SourceTableView = WHERE(Status = FILTER(Released | Open));
    UsageCategory = Documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(Control1000000005; "Tariff No. Lines SubForm")
            {
                ApplicationArea = all;
                SubPageLink = "Tariff No." = FIELD("No.");
            }
            group(Percentages)
            {
                Caption = 'Percentages';
                field("Duty Rate %"; "Duty Rate %")
                {
                    ApplicationArea = all;
                }
                field("Surcharge %"; "Surcharge %")
                {
                    ApplicationArea = all;
                }
                field("CISS %"; "CISS %")
                {
                    ApplicationArea = all;
                }
                field("TLS %"; "TLS %")
                {
                    ApplicationArea = all;
                }
                field("VAT %"; "VAT %")
                {
                    ApplicationArea = all;
                }
                field("LEAVY %"; "LEAVY %")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Tariff")
            {
                Caption = '&Tariff';
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries: Page "Posted Approval Entries";
                    begin
                        /*
                        ApprovalEntries.SetTariffCodesFilters(50123,60,"No.",Rec);
                        ApprovalEntries.RUN;*///CHI WF
                    end;
                }
            }
            group("&Functions")
            {
                Caption = '&Functions';
                action("Update Items")
                {
                    ApplicationArea = all;
                    Caption = 'Update Items';
                    Visible = false;

                    trigger OnAction();
                    begin
                        UpdateItems;
                    end;
                }
                separator(Separator1000000018)
                {
                }
                action("Send A&pproval Request")
                {
                    ApplicationArea = all;
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.SendTariffApprovalRequest(Rec) THEN;
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    ApplicationArea = all;
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.CancelTariffApprovalRequest(Rec,TRUE,FALSE) THEN;
                    end;
                }
            }
        }
    }

    var
        ApprovalMgt: Codeunit 1535;
        ApprovalEntries: Page 658;
}

