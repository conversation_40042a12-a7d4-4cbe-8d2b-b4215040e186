
codeunit 50009 Mgmt
{
    trigger OnRun()
    begin

    end;

    procedure GetVoucherFilter2(UserCode: Code[20]): Code[10]
    begin
        IF NOT HasGotVoucherUserSetup THEN BEGIN
            CompanyInfo.GET();
            VoucherUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."Voucher Resp. Ctr. Filter" <> '' THEN
                    VoucherUserRespCenter := UserSetup."Voucher Resp. Ctr. Filter";
            HasGotVoucherUserSetup := TRUE;
        END;
        EXIT(VoucherUserRespCenter);
    end;

    procedure GetMRSFilter(): Code[10]
    begin
        EXIT(GetMRSFilter2(COPYSTR(USERID(), 1, 50)));
    end;

    procedure GetMRSFilter2(UserCode: Code[50]): Code[10]
    begin
        IF NOT HasGotMRSUserSetup THEN BEGIN
            CompanyInfo.GET();
            MRSUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."MRS-Sample Resp. Ctr. Filter" <> '' THEN
                    MRSUserRespCenter := UserSetup."MRS-Sample Resp. Ctr. Filter";
            HasGotMRSUserSetup := TRUE;
        END;
        EXIT(MRSUserRespCenter);
    end;

    procedure GetFRSFilter(): Code[10];
    begin
        EXIT(GetFRSFilter2(Copystr(USERID(), 1, 50)));
    end;

    procedure GetFRSFilter2(UserCode: Code[50]): Code[10]
    begin
        IF NOT HasGotFRSUserSetup THEN BEGIN
            CompanyInfo.GET();
            FRSUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."FA Req. Resp. Ctr. Filter" <> '' THEN
                    FRSUserRespCenter := UserSetup."FA Req. Resp. Ctr. Filter";
            HasGotFRSUserSetup := TRUE;
        END;
        EXIT(FRSUserRespCenter);
    end;

    procedure GetSReqFilter(): Code[10]
    begin
        EXIT(GetSReqFilter2(Copystr(USERID(), 1, 50)));
    end;

    procedure GetSReqFilter2(UserCode: Code[50]): Code[10]
    begin
        IF NOT HasGotSReqUserSetup THEN BEGIN
            CompanyInfo.GET();
            SReqUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."Service Request Resp. Ctr. Ftr" <> '' THEN
                    SReqUserRespCenter := UserSetup."Service Request Resp. Ctr. Ftr";
            HasGotSReqUserSetup := TRUE;
        END;
        EXIT(SReqUserRespCenter);

    end;

    procedure GetMDVFilter(): Code[10]
    begin
        EXIT(GetMDVFilter2(CopyStr(USERID(), 1, 50)));
    end;

    procedure GetMDVFilter2(UserCode: Code[50]): Code[10]
    begin
        IF NOT HasGotMDVUserSetup THEN BEGIN
            CompanyInfo.GET();
            MDVUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."MDV Resp. Ctr. Filter" <> '' THEN
                    MDVUserRespCenter := UserSetup."MDV Resp. Ctr. Filter";
            HasGotMDVUserSetup := TRUE;
        END;
        EXIT(MDVUserRespCenter);
    end;

    procedure GetWOdrFilter(): Code[10]
    begin
        EXIT(GetWOdrFilter2(COPYSTR(USERID(), 1, 50)));
    end;

    procedure GetWOdrFilter2(UserCode: Code[50]): Code[10]
    begin
        IF NOT HasGotWOdrUserSetup THEN BEGIN
            CompanyInfo.GET();
            WOdrUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."WrkOrdReq Resp. Ctr. Filter" <> '' THEN
                    WOdrUserRespCenter := UserSetup."WrkOrdReq Resp. Ctr. Filter";
            HasGotWOdrUserSetup := TRUE;
        END;
        EXIT(WOdrUserRespCenter);
    end;

    procedure GetPRSFilter(): Code[10]
    begin
        EXIT(GetPRSFilter2(COPYSTR(USERID(), 1, 50)));
    end;

    procedure GetPRSFilter2(UserCode: Code[50]): Code[10]
    begin
        IF NOT HasGotPRSUserSetup THEN BEGIN
            CompanyInfo.GET();
            PRSUserRespCenter := CompanyInfo."Responsibility Center";
            //UserLocation := CompanyInfo."Location Code";
            IF (UserSetup.GET(UserCode)) AND (UserCode <> '') THEN
                IF UserSetup."Prj. Req. Resp. Ctr. Filter" <> '' THEN
                    PRSUserRespCenter := UserSetup."Prj. Req. Resp. Ctr. Filter";
            HasGotPRSUserSetup := TRUE;
        END;
        EXIT(PRSUserRespCenter);
    end;

    var
        CompanyInfo: Record "Company Information";
        UserSetup: Record "User Setup";
        HasGotPRSUserSetup: Boolean;
        PRSUserRespCenter: Code[10];
        HasGotVoucherUserSetup: Boolean;
        VoucherUserRespCenter: Code[10];
        HasGotMRSUserSetup: Boolean;
        MRSUserRespCenter: Code[10];
        HasGotFRSUserSetup: Boolean;
        FRSUserRespCenter: code[10];
        HasGotSReqUserSetup: Boolean;
        SReqUserRespCenter: Code[10];
        HasGotMDVUserSetup: Boolean;
        MDVUserRespCenter: Code[10];
        HasGotWOdrUserSetup: Boolean;
        WOdrUserRespCenter: Code[10];
}