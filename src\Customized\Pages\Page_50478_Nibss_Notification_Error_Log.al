page 50478 "Nibss Notifications Error Log"
{
    // version NIBSS

    Editable = false;
    PageType = List;
    SourceTable = "Nibss Notifications Error Log";
    UsageCategory = Lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(id; id)
                {
                    ApplicationArea = all;
                }
                field(sessionID; sessionID)
                {
                    ApplicationArea = all;
                }
                field(Customercode; Customercode)
                {
                    ApplicationArea = all;
                }
                field(customerName; customerName)
                {
                    ApplicationArea = all;
                }
                field(amount; amount)
                {
                    ApplicationArea = all;
                }
                field(narration; narration)
                {
                    ApplicationArea = all;
                }
                field(PaymentReference; PaymentReference)
                {
                    ApplicationArea = all;
                }
                field(transactionInitiatedDate; transactionInitiatedDate)
                {
                    ApplicationArea = all;
                }
                field(transactionApprovalDate; transactionApprovalDate)
                {
                    ApplicationArea = all;
                }
                field(dateEntered; dateEntered)
                {
                    ApplicationArea = all;
                }
                field(dateUpdated; dateUpdated)
                {
                    ApplicationArea = all;
                }
                field(Processed; Processed)
                {
                    ApplicationArea = all;
                }
                field("Error Log No."; "Error Log No.")
                {
                    ApplicationArea = all;
                }
                field(Remark; Remark)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

