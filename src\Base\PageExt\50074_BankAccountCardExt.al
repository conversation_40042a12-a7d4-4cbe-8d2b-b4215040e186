pageextension 50074 BankAccountCardExt extends "Bank Account Card"
{
    layout
    {
        addlast(General)
        {
            field("Last Cheque No."; "Last Cheque No.")
            {
                ApplicationArea = all;
            }
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Account Type"; "Account Type")
            {
                ApplicationArea = all;
            }
            field("Teller Bank Name"; "Teller Bank Name")
            {
                ApplicationArea = all;
            }
            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = all;
            }
            field("Cash Account Type"; "Cash Account Type")
            {
                ApplicationArea = all;
            }
        }
        addafter("Positive Pay Export Code")
        {
            field("Bank Code"; "Bank Code")
            {
                ApplicationArea = all;
            }
            field("API Code"; "API Code")
            {
                ApplicationArea = all;
            }
            field("Enable API File"; "Enable API File")
            {
                ApplicationArea = all;
                
            }
             //<<<<<<< G2S 21st Aug., 2023
            //CR: RFC#39  
            //Signed: 31st Oct., 2023
            //Name: Go2Solve Nig. Ltd
            //Published: 10th Nov., 2023
            field("BankAPI Source Bank Code"; Rec."BankAPI Source Bank Code")
            {
                ApplicationArea = All;
            }
            //<<<<<<<
        }
        modify(Name)
        {
            trigger OnAfterValidate()
            begin
                Blocked := true;
            end;
        }

    }

    actions
    {
        addlast(navigation)
        {
            action("Bank Cheque Details")
            {
                Caption = 'Bank Cheque Details';
                ApplicationArea = all;
                RunObject = Page "Bank Cheque Details List";
                RunPageLink = "Bank No." = FIELD("No.");
            }
            group(Functions)
            {
                Caption = 'Functions';
                separator(Separator1102152034)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendBankAccforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF "Approval Status" <> "Approval Status"::Released then BEGIN
                            "Approval Status" := "Approval Status"::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                        if Blocked then begin
                            Blocked := false;
                            Modify();
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 270);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF "Approval Status" <> "Approval Status"::Open then BEGIN
                            "Approval Status" := "Approval Status"::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }

                action(Approve)
                {
                    ApplicationArea = All;
                    Image = Action;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin

                        IF allinoneCU.CheckBankAccApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendBankAccForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelBankAccForApproval(Rec);
                    end;
                }

            }
        }

    }
    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);
    END;

    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    BEGIN
        Blocked := TRUE;
    END;

    var
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";

    trigger OnAfterGetCurrRecord()
    var
        BankAccLedEntryLRec: Record "Bank Account Ledger Entry";
    begin
        /*         BankAccLedEntryLRec.Reset();
                BankAccLedEntryLRec.SetRange("Bank Account No.", "No.");
                IF BankAccLedEntryLRec.FindLast() then
                    "Last Cheque No." := BankAccLedEntryLRec."Cheque No." */ //PJTemp
    end;
}