codeunit 50150 Codeunit50
{
    // version NonEditableLotNo.

    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      B2BMS      08/11/2021     Created this codeunit for Customization

    trigger OnRun()
    begin

    end;

    var
        ItemTrackingMgt: Codeunit "Item Tracking Management";

    // Transfer Lines Item Tracking Start>>
    procedure CallItemTracking(var TransLine: Record "Transfer Line"; Direction: Enum "Transfer Direction")
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
        AvalabilityDate: Date;
    begin
        TrackingSpecification.InitFromTransLine(TransLine, AvalabilityDate, Direction);
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, AvalabilityDate);
        ItemTrackingLinesCopy.SetInbound(TransLine.IsInbound);
        ItemTrackingLinesCopy.RunModal;
        OnAfterCallItemTracking(TransLine);
    end;

    procedure CallItemTracking(var TransLine: Record "Transfer Line"; Direction: Enum "Transfer Direction"; SecondSourceQuantityArray: array[3] of Decimal)
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
        AvailabilityDate: Date;
    begin
        TrackingSpecification.InitFromTransLine(TransLine, AvailabilityDate, Direction);
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, AvailabilityDate);
        ItemTrackingLinesCopy.SetSecondSourceQuantity(SecondSourceQuantityArray);
        ItemTrackingLinesCopy.RunModal;
        OnAfterCallItemTracking(TransLine);
    end;
    // Transfer Lines Item Tracking End<<

    // Sales Lines Item Tracking Start>>
    procedure CallItemTracking1(var SalesLine: Record "Sales Line")
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
    begin
        TrackingSpecification.InitFromSalesLine(SalesLine);
        if ((SalesLine."Document Type" = SalesLine."Document Type"::Invoice) and
            (SalesLine."Shipment No." <> '')) or
           ((SalesLine."Document Type" = SalesLine."Document Type"::"Credit Memo") and
            (SalesLine."Return Receipt No." <> ''))
        then
            ItemTrackingLinesCopy.SetFormRunMode(2); // Combined shipment/receipt
        if SalesLine."Drop Shipment" then begin
            ItemTrackingLinesCopy.SetFormRunMode(3); // Drop Shipment
            if SalesLine."Purchase Order No." <> '' then
                ItemTrackingLinesCopy.SetSecondSourceRowID(ItemTrackingMgt.ComposeRowID(DATABASE::"Purchase Line",
                    1, SalesLine."Purchase Order No.", '', 0, SalesLine."Purch. Order Line No."));
        end;
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, SalesLine."Shipment Date");
        ItemTrackingLinesCopy.SetInbound(SalesLine.IsInbound);
        ItemTrackingLinesCopy.RunModal;
    end;

    procedure CallItemTracking1(var SalesLine: Record "Sales Line"; SecondSourceQuantityArray: array[3] of Decimal)
    begin
        CallItemTrackingSecondSource(SalesLine, SecondSourceQuantityArray, false);
    end;

    procedure CallItemTrackingSecondSource(var SalesLine: Record "Sales Line"; SecondSourceQuantityArray: array[3] of Decimal; AsmToOrder: Boolean)
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
    begin
        if SecondSourceQuantityArray[1] = DATABASE::"Warehouse Shipment Line" then
            ItemTrackingLinesCopy.SetSecondSourceID(DATABASE::"Warehouse Shipment Line", AsmToOrder);

        TrackingSpecification.InitFromSalesLine(SalesLine);
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, SalesLine."Shipment Date");
        ItemTrackingLinesCopy.SetSecondSourceQuantity(SecondSourceQuantityArray);
        ItemTrackingLinesCopy.RunModal;
    end;
    // Sales Lines Item Tracking End<<
    //B2BMSOn09Sep21>>
    // Service Line Item Tracking Start>>
    procedure CallItemTrackingService(var ServiceLine: Record "Service Line")
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
    begin
        TrackingSpecification.InitFromServLine(ServiceLine, false);
        if ((ServiceLine."Document Type" = ServiceLine."Document Type"::Invoice) and
            (ServiceLine."Shipment No." <> ''))
        then
            ItemTrackingLinesCopy.SetFormRunMode(2); // Combined shipment/receipt
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, ServiceLine."Needed by Date");
        ItemTrackingLinesCopy.SetInbound(ServiceLine.IsInbound);
        OnCallItemTrackingOnBeforeItemTrackingLinesRunModal(ServiceLine, ItemTrackingLinesCopy);
        ItemTrackingLinesCopy.RunModal();
    end;
    // Service Line Item Tracking End<<

    // Purchase Line Item Tracking Start>>
    procedure CallItemTrackingPurchase(var PurchLine: Record "Purchase Line")
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
    begin
        TrackingSpecification.InitFromPurchLine(PurchLine);
        if ((PurchLine."Document Type" = PurchLine."Document Type"::Invoice) and
            (PurchLine."Receipt No." <> '')) or
           ((PurchLine."Document Type" = PurchLine."Document Type"::"Credit Memo") and
            (PurchLine."Return Shipment No." <> ''))
        then
            ItemTrackingLinesCopy.SetFormRunMode(2); // Combined shipment/receipt
        if PurchLine."Drop Shipment" then begin
            ItemTrackingLinesCopy.SetFormRunMode(3); // Drop Shipment
            if PurchLine."Sales Order No." <> '' then
                ItemTrackingLinesCopy.SetSecondSourceRowID(ItemTrackingMgt.ComposeRowID(DATABASE::"Sales Line",
                    1, PurchLine."Sales Order No.", '', 0, PurchLine."Sales Order Line No."));
        end;
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, PurchLine."Expected Receipt Date");
        ItemTrackingLinesCopy.SetInbound(PurchLine.IsInbound);
        OnCallItemTrackingOnBeforeItemTrackingFormRunModal(PurchLine, ItemTrackingLinesCopy);
        ItemTrackingLinesCopy.RunModal();
    end;

    procedure CallItemTrackingPurchase(var PurchLine: Record "Purchase Line"; SecondSourceQuantityArray: array[3] of Decimal)
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingLinesCopy: Page "Item Tracking Lines Copy";
    begin
        TrackingSpecification.InitFromPurchLine(PurchLine);
        ItemTrackingLinesCopy.SetSourceSpec(TrackingSpecification, PurchLine."Expected Receipt Date");
        ItemTrackingLinesCopy.SetSecondSourceQuantity(SecondSourceQuantityArray);
        OnCallItemTrackingOnBeforeItemTrackingFormRunModal(PurchLine, ItemTrackingLinesCopy);
        ItemTrackingLinesCopy.RunModal();
    end;
    // Purchase Line Item Tracking End<<

    [IntegrationEvent(false, false)]
    local procedure OnCallItemTrackingOnBeforeItemTrackingLinesRunModal(var ServiceLine: Record "Service Line"; var ItemTrackingLinesCopy: Page "Item Tracking Lines Copy")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnCallItemTrackingOnBeforeItemTrackingFormRunModal(var PurchLine: Record "Purchase Line"; var ItemTrackingLinesCopy: Page "Item Tracking Lines Copy")
    begin
    end;

    //B2BMSOn09Sep21<<


    [IntegrationEvent(false, false)]
    local procedure OnAfterCallItemTracking(var TransferLine: Record "Transfer Line")
    begin
    end;


    var
        myInt: Integer;
}