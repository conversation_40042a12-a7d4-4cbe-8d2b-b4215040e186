page 50630 "Capex Budget List Factbox"
{
    PageType = CardPart;
    SourceTable = "Budget Line";

    layout
    {
        area(Content)
        {
            field("Account No."; AccountNo)
            {
                ApplicationArea = ALL;
            }
            field(Amount; Amount)
            {
                ApplicationArea = ALL;
            }
            field("Budget Utilized"; "Budget Utilized")
            {
                ApplicationArea = ALL;
            }
            field("FA No."; "FA No.")
            {
                ApplicationArea = ALL;
            }
            field("Budget Amount "; BudRem)
            {
                ApplicationArea = ALL;
            }
            field("Budget Utilized Pending LPO"; "Budget Utilized Pending LPO")
            {
                ApplicationArea = ALL;
            }

        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    trigger OnAfterGetRecord()
    var
        fapost: record "FA Posting Group";
    BEGIN
        IF type = type::"FA Posting Group" then BEGIN
            IF fapost.GET("No.") then
                AccountNo := fapost."Acquisition Cost Account";
        end;
        CalcFields("Budget Utilized");
        BudRem := (Amount - "Budget Utilized");
    END;


    var
        myInt: Integer;
        AccountNo: code[20];
        BudRem: Decimal;

}