page 50581 "BRS Outstanding Data"
{
    // version BNKRECON

    AutoSplitKey = true;
    //Editable = false;
    PageType = List;
    SourceTable = "BRS Outstanding Data";
    UsageCategory = Lists;
    applicationarea = ALL;
    DeleteAllowed = true;
    InsertAllowed = false;
    ModifyAllowed = false;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(Type; Type)
                {
                    applicationarea = ALL;
                }
                field("Bank No"; "Bank No")
                {
                    applicationarea = ALL;
                }
                field("Document No"; "Document No")
                {
                    applicationarea = ALL;
                }
                field("Posting date"; "Posting date")
                {
                    applicationarea = ALL;
                }
                field("Debit Amount"; "Debit Amount")
                {
                    applicationarea = ALL;
                }
                field("Credit Amount"; "Credit Amount")
                {
                    applicationarea = ALL;
                }
                field("Reconciled Statement No"; "Reconciled Statement No")
                {
                    applicationarea = ALL;
                }
                field(Reconciled; Reconciled)
                {
                    applicationarea = ALL;
                    Editable = false;
                }
                field("Reason Code"; "Reason Code")
                {
                    applicationarea = ALL;
                }

                field(Narrations; Narrations)
                {
                    applicationarea = ALL;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Create Duplicate")
            {
                Caption = 'Create Duplicate';
                Promoted = true;
                PromotedCategory = Process;
                applicationarea = ALL;

                trigger OnAction();
                begin
                    CreateDuplicateEntry(Rec);
                end;
            }
        }
    }

    var
        Text01: Label 'You do not have permission.';

    procedure CreateDuplicateEntry(BRSRec: Record "BRS Outstanding Data");
    var
        NewBRSRec: Record "BRS Outstanding Data";
        UserSetup: record "User Setup";
    begin
        //if (UPPERCASE(USERID) = 'RKD') or (UPPERCASE(USERID) = 'SAHEED.ADEOSUN') then begin
        UserSetup.get(UserId);
        IF UserSetup."Create BRS Out. Dupl Entrie" then begin
            TESTFIELD(Reconciled, false);
            TESTFIELD(Type, Type::Statement);
            NewBRSRec.INIT;
            NewBRSRec.TRANSFERFIELDS(BRSRec);
            NewBRSRec."Line No" := BRSRec."Line No" + 1;
            NewBRSRec.Narrations := BRSRec.Narrations + '-' + FORMAT(NewBRSRec."Line No");
            NewBRSRec.INSERT;
        end else
            ERROR(Text01);
    end;
}

