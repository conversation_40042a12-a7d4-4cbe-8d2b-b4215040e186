//CR: RFC#39  
//Signed: 31st Oct., 2023
//Name: Go2Solve Nig. Ltd
//Published: 10th Nov., 2023

/// <summary>
/// Page Bank Payment Notifications (ID 50190).
/// </summary>
page 50190 "Bank Payment Notifications"
{
    Caption = 'Bank Payment Notifications';
    PageType = List;
    SourceTable = "Bank Payment Notification";
    //CardPageId = "Bank Payment Notification";
    layout
    {
        area(content)
        {
            repeater(General)
            {
                field(ID; Rec.ID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the ID field.';
                }
                field("Customer ID"; Rec."Customer ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer ID field.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer Name field.';
                }
                field("Payment Reference"; Rec."Payment Reference")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Payment Reference field.';
                }
                field(Amount; Rec.Amount)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Amount field.';
                }
                field("Date Entered"; Rec."Date Entered")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Entered field.';
                }
                field("Session ID"; Rec."Session ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Session ID field.';
                }
                field(SourceBankCode; Rec.SourceBankCode)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the SourceBankCode field.';
                }
                field(Processed; Rec.Processed)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Processed field.';
                }
                field("Temp. Omit record from Batch"; Rec."Temp. Omit record from Batch")
                {
                    ApplicationArea = All;
                    ToolTip = 'Temporarily omit record from batch';
                }

                field("Duplicate Session ID?"; "Duplicate Session ID?")
                {
                    ApplicationArea = All;
                    ToolTip = 'Session ID';
                }
                field("Date Posted"; Rec."Date Posted")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Posted field.';
                }
                field(Narration; Rec.Narration)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Narration field.';
                }

            }
        }
    }
}
