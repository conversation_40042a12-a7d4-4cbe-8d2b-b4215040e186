pageextension 50153 SalesRolcenExt extends "Sales & Marketing Manager RC"
{
    layout
    {

    }

    actions
    {
        //BaluonMar8 2022>>
        addafter(Group)
        {
            group("Approve Pages")
            {
                action("Request to Approve")
                {
                    ApplicationArea = all;
                    Caption = 'Request to Approves';
                    RunObject = page "Requests to Approve";
                }
                action("Approval Entries View")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries View';
                    RunObject = page "Approval Entries View Page";
                }
                action("Approval Entries")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries';
                    RunObject = page "Approval Entries";
                }
            }
        }//BaluonMar8 2022<<

        addlast("Group13")
        {
            action("Warehouse Shipments")
            {
                ApplicationArea = Planning;
                RunObject = page "Warehouse Shipment List";
            }
            action("Warehouse Receipts")
            {
                ApplicationArea = Planning;
                RunObject = page "Warehouse Receipts";
            }
        }
        addafter("Group13")
        {
            group("Customized Pages")
            {
                action("Resp Wise Customers")
                {
                    ApplicationArea = all;
                    RunObject = page "Resp Wise Customers";
                }
                action("Direct Sales Quotes")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Direct Sales Quotes";
                }
                action("Local Sales Quotes")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Local Sales Quotes";
                }
                action("Export Sales Quotes")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Export Sales Quotes";
                }
                action("Direct Sales Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Direct Sales Orders";
                }
                action("Local Sales Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Local Sales Orders";
                }
                action("Export Sales Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Export Sales Orders";
                }
                group("WareHouse Despatch")
                {
                    action("Loading Slip List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Loading Slip List";
                    }
                    action("Outward Gate Entry List-NRGP")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Outward Gate Entry List-NRGP";
                    }
                    action("Inward Gate Entry List-NRGP")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Inward Gate Entry List-NRGP";
                    }
                    action("Posted Loading Slip List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Posted Loading Slip List";
                    }
                    action("Posted Inward GateEntry List-NRGP")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "PostedInwardGateEntryList-NRGP";
                    }
                    action("Posted outward GateEntry List-NRGP")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Posted Outward Gate Entry List";
                    }

                }
                group("Promotions")
                {
                    action("Promo. Schedule List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Promo. Schedule List";
                    }
                    action("Promotion Group List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Promotion Group List";
                    }
                    action("Promo Schd. restrictions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Promo Schd. restrictions";
                    }

                }
                group("Bank Tellers")
                {
                    action("Request For Teller Receipt List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Request Teller Receipt List";
                    }
                    action("Teller/Cheq. Awaiting Confirm.")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Teller/Cheq. Awaiting Confirm.";
                    }
                    action("Teller/Cheque Awaiting BRV")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Teller/Cheque Awaiting BRV";
                    }
                    action("Rejected Or Returned Tellers")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Returned Tellers";
                    }
                    group("Teller Cheque Status")
                    {
                        action("Teller/Cheq. Awaiting Confirmation")
                        {
                            ApplicationArea = Planning;
                            RunObject = page "Teller Awaiting Conf. List";
                        }
                        action("Teller/Cheq. Awaiting BRV")
                        {
                            ApplicationArea = Planning;
                            RunObject = page "Teller Awaiting BRV List";
                        }/*
                        action("Teller/Cheque Awaiting BRV")
                        {
                            ApplicationArea = Planning;
                            RunObject = page bank con te ;
                        }*/
                        action("Teller cheque Converted to BRV")
                        {
                            ApplicationArea = ALL;
                            RunObject = page "Teller/Cheque Converted to BRV";
                        }
                    }
                }
            }
            group("Approved Documents")
            {
                action("Approved Local Sales Order")
                {
                    ApplicationArea = all;
                    RunObject = page "Approved Local Sales Orders";
                }
                action("Approved Direct Sales Order")
                {
                    ApplicationArea = all;
                    RunObject = page "Approved Direct Sales Orders";
                }
                action("Approved Export Sales Order")
                {
                    ApplicationArea = all;
                    RunObject = page "Approved Export Sales Orders";
                }
            }
            group("Customized XML PORTS")
            {
                action(UploadKDTargets)
                {
                    ApplicationArea = ALL;
                    RunObject = xmlport "Upload Kd Target Scores";
                }
                action(SalesDispatchedDms)
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Sales Dispatched DMS";
                }
                action(PMSStatementImport)
                {
                    ApplicationArea = all;
                    RunObject = xmlport "PMS Statement Import";
                }
                action(RebateHistory)
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Rebate History Report";
                }
                action(ProductivityDays)
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Productivity Days Upload";
                }
                action("Zenith Bank Payment")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Zenith Bank Payment";
                }
                action("zenith Bank Payment Emp")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Zenith Bank Payment Emp";
                }
                action("Create HMO Batch JNL")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Create HMO Batch JNL";
                }
                action("salary JNL (Customer)")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "salary JNL (Customer)";
                }
                action("Create Cr Limit Scd")
                {
                    Caption = 'Create Cr Limit Schedule';
                    ApplicationArea = all;
                    RunObject = xmlport "Create Cr Limit Scd";
                }

            }
            group("Customized Reports")
            {
                action("Credit Memo new")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Credit Memo new";
                }
                action("Sales Return Receipt")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Sales Return Receipt";
                }
                action("Customer pmt req BR.")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Customer pmt req BR.";
                }
                action("Branch Invoice New")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Branch Invoice New";
                }
                action("New POS Print_L2")
                {
                    ApplicationArea = Planning;
                    RunObject = report "New POS Print_L2";
                }
                action("Monthly Cust. Disc. SchemeBR")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Monthly Cust. Disc. SchemeBR";
                }
                action("Relation Cr Cust. upcountry")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Relation Cr Cust. upcountry1";
                }
                action("Relation Cr Customer Lagos")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Relation Cr Customer Lagos";
                }
                action("Sales Register")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Sales Register";
                }
                action("Distributor Relation Upcountry")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Distributor Relation Upcountry";
                }
                action("Customer Transport Batch")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Customer Transport Batch";
                }
                action("Target Setting Pan Nigeria")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Target Setting Pan Nigeria";
                }
                action("Relation Cr Cust. upcountry1")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Relation Cr Cust. upcountry1";
                }
                action("Relation Cr Cust Lagos1")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Relation Cr Cust Lagos1";
                }
                action("Dist Relation Upcountry1")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Dist Relation Upcountry1";
                }
                action("Dist Relation Lagos1")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Dist Relation Lagos1";
                }
                action("SNOP Stock Exposure History")
                {
                    ApplicationArea = Planning;
                    RunObject = report "SNOP Stock Exposure History";
                }
                action("KD Rebate")
                {
                    ApplicationArea = Planning;
                    RunObject = report "KD Rebate";
                }
                action("Self Lifting Credit Memo")
                {
                    ApplicationArea = all;
                    RunObject = report "Self Lifting Credit Memo";
                }
            }
            group("Material Requisition")
            {
                action("Material Requisitions")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Material Requisitions.";
                }
                action("Material Requisitions-Released")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Material Requisitions-Released";
                }
                action("Production MRS List")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Production MRS List";
                }
                action("Approved Production MRS")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Approved Production MRS List";
                }
                action("Material Requisitions-Closed")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Material Requisitions- Closed";
                }
                action("Material Req Ack")
                {
                    Caption = 'Material Requisation Release Ack';
                    ApplicationArea = all;
                    RunObject = page "Material Requ-Rel Ack";
                }
            }
            group(PMS)
            {
                action("PMS Voucher")
                {
                    Caption = 'PMS Voucher';
                    ApplicationArea = all;
                    RunObject = page "PMS Voucher List";
                }
            }
        }
    }
    var
        myInt: Integer;
}