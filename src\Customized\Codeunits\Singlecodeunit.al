codeunit 50057 "Single Instance CU"
{
    SingleInstance = True;
    trigger OnRun()
    begin
        IF NOT StoreToTemp THEN BEGIN
            StoreToTemp := TRUE;
        END ELSE
            Page.RUNMODAL(0, TempGLEntry);
    END;

    VAR
        TempGLEntry: Record 17 TEMPORARY;
        StoreToTemp: Boolean;
    //B2BSPON22SEPT19
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Sales Document", 'OnAfterManualReleaseSalesDoc', '', false, false)]
    local procedure OnAfterReleaseSalesDoc(var SalesHeader: Record "Sales Header")
    var
        Resrv: Record 337;
        TrackSp: Record 336;
        SalesBatch: Record "Sales Batch Details";
    begin
        SalesBatch.Reset();
        SalesBatch.SetRange("Doc No.", SalesHeader."No.");
        if SalesBatch.FindSet() then
            SalesBatch.DeleteAll();
        Clear(SalesBatch);
        Resrv.RESET;
        Resrv.SetRange("Source ID", SalesHeader."No.");
        If Resrv.FINDSET then
            repeat
                SalesBatch.Init();
                SalesBatch."Doc Type" := SalesHeader."Document Type";
                SalesBatch."Doc No." := SalesHeader."No.";
                //b2bspon22oct18>>
                SalesBatch.Location := SalesHeader."Location Code";
                SalesBatch."Released by" := UserId;
                SalesBatch."Posting Date" := SalesHeader."Posting Date";
                //b2bspon22oct18<<
                SalesBatch."Line No." := Resrv."Source Ref. No.";
                SalesBatch."Item No." := Resrv."Item No.";
                SalesBatch."Lot No." := Resrv."Lot No.";
                SalesBatch.Quantity := Resrv.Quantity;
                SalesBatch."Source Type" := Resrv."Source Type";
                SalesBatch."Expiry Date" := Resrv."Expiration Date";

                IF SalesBatch.Insert() Then;
            Until Resrv.Next() = 0;
        TrackSp.RESET;
        TrackSp.SetRange("Source ID", SalesHeader."No.");
        If TrackSp.FINDSET then
            repeat
                SalesBatch.Init();
                SalesBatch."Doc Type" := SalesHeader."Document Type";
                SalesBatch."Doc No." := SalesHeader."No.";
                SalesBatch."Line No." := TrackSp."Source Ref. No.";
                SalesBatch."Item No." := TrackSp."Item No.";
                SalesBatch."Lot No." := TrackSp."Lot No.";
                SalesBatch.Quantity := TrackSp."Quantity (Base)";
                SalesBatch."Expiry Date" := TrackSp."Expiration Date";
                SalesBatch."Source Type" := TrackSp."Source Type";
                //b2bspon22oct18>>
                //SalesBatch."Released by" := SalesHeader."Released By";
                SalesBatch.Location := SalesHeader."Location Code";
                SalesBatch."Released by" := UserId;
                //SalesBatch.Validate("Posting Date", SalesHeader."Posting Date");
                SalesBatch."Posting Date" := SalesHeader."Posting Date";
                //b2bspon22oct18<<
                IF SalesBatch.Insert() Then;
            Until TrackSp.Next() = 0;
    end;
    //B2BSPON22SEPT19<<
    procedure OnAfterManualReOpenTransferOderDoc1(var TransferHeader: Record "Transfer Header")
    var
        Resrv: Record 337;
        TrackSp: Record 336;
        SalesBatch: Record "Sales Batch Details";
    begin
        SalesBatch.Reset();
        SalesBatch.SetRange("Doc No.", TransferHeader."No.");
        if SalesBatch.FindSet() then
            SalesBatch.DeleteAll();
        Clear(SalesBatch);
        Resrv.RESET;
        Resrv.SetRange("Source ID", TransferHeader."No.");
        Resrv.SetRange(Positive, true);
        If Resrv.FINDSET then
            repeat
                SalesBatch.Init();
                //SalesBatch."Doc Type" := SalesHeader."Document Type";
                SalesBatch."Doc No." := TransferHeader."No.";
                //b2bspon22oct18>>
                SalesBatch."To Location" := TransferHeader."Transfer-to Code";
                SalesBatch."From Location" := TransferHeader."Transfer-from Code";
                SalesBatch."Posting Date" := TransferHeader."Posting Date";
                //SalesBatch.Validate("Posting Date", TransferHeader."Posting Date");
                //SalesBatch."Released by" := TransferHeader."Released By";
                SalesBatch."Released by" := UserId;
                //b2bspon22oct18<<
                SalesBatch."Line No." := Resrv."Source Ref. No.";
                SalesBatch."Item No." := Resrv."Item No.";
                SalesBatch."Lot No." := Resrv."Lot No.";
                SalesBatch.Quantity := Resrv.Quantity;
                SalesBatch."Expiry Date" := Resrv."Expiration Date";
                SalesBatch."Source Type" := Resrv."Source Type";
                IF SalesBatch.Insert() Then;
            Until Resrv.Next() = 0;
        TrackSp.RESET;
        TrackSp.SetRange("Source ID", TransferHeader."No.");
        If TrackSp.FINDSET then
            repeat
                SalesBatch.Init();
                //SalesBatch."Doc Type" := TransferHeader."Document Type";
                SalesBatch."Doc No." := TransferHeader."No.";
                //b2bspon22oct18>>
                SalesBatch."To Location" := TransferHeader."Transfer-to Code";
                SalesBatch."From Location" := TransferHeader."Transfer-from Code";
                SalesBatch."Released by" := UserId;
                //SalesBatch.Validate("Posting Date", TransferHeader."Posting Date");
                SalesBatch."Posting Date" := TransferHeader."Posting Date";

                //b2bspon22oct18<<
                SalesBatch."Line No." := TrackSp."Source Ref. No.";
                SalesBatch."Item No." := TrackSp."Item No.";
                SalesBatch."Lot No." := TrackSp."Lot No.";
                SalesBatch.Quantity := TrackSp."Quantity (Base)";
                SalesBatch."Expiry Date" := TrackSp."Expiration Date";
                SalesBatch."Source Type" := TrackSp."Source Type";
                IF SalesBatch.Insert() Then;
            Until TrackSp.Next() = 0;
    end;
    //B2BSPON22SEPT19<<

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', false, false)]
    local procedure OnReleasedocumentSale(RecRef: RecordRef; var Handled: boolean)
    var
        SalesHeader: Record "Sales Header";
        Resrv: Record 337;
        TrackSp: Record 336;
        SalesBatch: Record "Sales Batch Details";
        TransferHeader: Record "Transfer Header";
    begin
        case RecRef.Number() of
            Database::"Sales Header":
                begin
                    RecRef.SetTable(SalesHeader);
                    SalesBatch.Reset();
                    SalesBatch.SetRange("Doc No.", SalesHeader."No.");
                    if SalesBatch.FindSet() then
                        SalesBatch.DeleteAll();
                    Clear(SalesBatch);
                    Resrv.RESET;
                    Resrv.SetRange("Source ID", SalesHeader."No.");
                    If Resrv.FINDSET then
                        repeat
                            SalesBatch.Init();
                            SalesBatch."Doc Type" := SalesHeader."Document Type";
                            SalesBatch."Doc No." := SalesHeader."No.";
                            //b2bspon22oct18>>
                            SalesBatch."Released by" := UserId;
                            SalesBatch.Location := SalesHeader."Location Code";
                            //SalesBatch.Validate("Posting Date", SalesHeader."Posting Date");
                            SalesBatch."Posting Date" := SalesHeader."Posting Date";

                            //b2bspon22oct18<<
                            SalesBatch."Line No." := Resrv."Source Ref. No.";
                            SalesBatch."Item No." := Resrv."Item No.";
                            SalesBatch."Lot No." := Resrv."Lot No.";
                            SalesBatch.Quantity := Resrv.Quantity;
                            SalesBatch."Expiry Date" := Resrv."Expiration Date";
                            IF SalesBatch.Insert() Then;

                        Until Resrv.Next() = 0;
                    TrackSp.RESET;
                    TrackSp.SetRange("Source ID", SalesHeader."No.");
                    If TrackSp.FINDSET then
                        repeat
                            SalesBatch."Doc Type" := SalesHeader."Document Type";
                            SalesBatch."Doc No." := SalesHeader."No.";
                            //b2bspon22oct18>>
                            SalesBatch."Released by" := UserId;
                            SalesBatch.Location := SalesHeader."Location Code";
                            SalesBatch."Posting Date" := SalesHeader."Posting Date";
                            //b2bspon22oct18<<
                            SalesBatch."Line No." := TrackSp."Source Ref. No.";
                            SalesBatch."Item No." := TrackSp."Item No.";
                            SalesBatch."Lot No." := TrackSp."Lot No.";
                            SalesBatch.Quantity := TrackSp."Quantity (Base)";
                            SalesBatch."Expiry Date" := TrackSp."Expiration Date";
                            IF SalesBatch.Insert() Then;
                        Until TrackSp.Next() = 0;
                end;
            Database::"Transfer Header":
                begin
                    RecRef.SetTable(TransferHeader);
                    OnAfterManualReOpenTransferOderDoc1(TransferHeader);
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Transfer Document", 'OnAfterReleaseTransferDoc', '', false, false)]
    local procedure OnAfterReleaseTransferDoc(var TransferHeader: Record "Transfer Header")
    begin
        OnAfterManualReOpenTransferOderDoc1(TransferHeader);
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post (Yes/No)", 'OnBeforeRunPurchPost', '', false, false)]
    local procedure OnBeforeRunPurchPost(var PurchaseHeader: Record "Purchase Header")

    var
        capex: Record "Budget Header";
        Purchline: Record "Purchase Line";
    begin
        //B2BSPON22OCT19>>

        Purchline.Reset();
        Purchline.SetRange("Document No.", PurchaseHeader."No.");
        Purchline.SetRange("Document Type", PurchaseHeader."Document Type");
        Purchline.SetFilter("No.", '<>%1', '');
        Purchline.SetFilter("Capex No.", '<>%1', '');
        if Purchline.FindSet() then begin
            repeat
                capex.Reset();
                capex.SetRange("No.", Purchline."Capex No.");
                if capex.FindFirst() then
                    capex.TestField(Status, capex.Status::Released);
            until Purchline.Next = 0;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post", 'OnBeforeCode', '', false, false)]
    local procedure OnBeforeCode(var GenJournalLine: Record "Gen. Journal Line"; var HideDialog: Boolean)
    var
        capex: Record "Budget Header";
        GenJounL: Record "Gen. Journal Line";

    begin
        GenJounL.RESET;
        GenJounL.CopyFilters(GenJournalLine);
        GenJounL.SetFilter("Capex No.", '<>%1', '');
        if GenJounL.FindSet() then begin
            repeat
                capex.Reset();
                capex.SetRange("No.", GenJounL."Capex No.");
                if capex.FindFirst() then
                    capex.TestField(Status, capex.Status::Released);
            until GenJounL.Next = 0;
        end;

    end;

    PROCEDURE InsertGL(GLEntry: Record 17);
    BEGIN
        IF StoreToTemp THEN BEGIN
            TempGLEntry := GLEntry;
            IF NOT TempGLEntry.INSERT THEN BEGIN
                TempGLEntry.DELETEALL;
                TempGLEntry.INSERT;
            END;
        END;
    END;

}
