page 50578 UserSetupCard
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Documents;
    SourceTable = "User Setup";
    Caption = 'UserSetUpCard';

    layout
    {
        area(Content)
        {
            group(General)
            {
                field("User ID"; "User ID")
                {
                    ApplicationArea = Basic, Suite;
                    LookupPageID = "User Lookup";
                    ToolTip = 'Specifies the ID of the user who posted the entry, to be used, for example, in the change log.';
                }
                field("Allow Posting From"; "Allow Posting From")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the earliest date on which the user is allowed to post to the company.';
                }
                field("Allow Posting To"; "Allow Posting To")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the last date on which the user is allowed to post to the company.';
                }
                field("Register Time"; "Register Time")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies whether to register the user''s time usage defined as the time spent from when the user logs in to when the user logs out. Unexpected interruptions, such as idle session timeout, terminal server idle session timeout, or a client crash are not recorded.';
                }
                field("Salespers./Purch. Code"; "Salespers./Purch. Code")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the code for the salesperson or purchaser for the user.';
                }
                field("Sales Resp. Ctr. Filter"; "Sales Resp. Ctr. Filter")
                {
                    ApplicationArea = Suite;
                    ToolTip = 'Specifies the code for the responsibility center to which you want to assign the user.';
                }
                field("Purchase Resp. Ctr. Filter"; "Purchase Resp. Ctr. Filter")
                {
                    ApplicationArea = Suite;
                    ToolTip = 'Specifies the code for the responsibility center to which you want to assign the user.';
                }
                field("Service Resp. Ctr. Filter"; "Service Resp. Ctr. Filter")
                {
                    ApplicationArea = Service;
                    ToolTip = 'Specifies the code for the responsibility center you want to assign to the user. The user will only be able to see service documents for the responsibility center specified in the field. This responsibility center will also be the default responsibility center when the user creates new service documents.';
                }
                field("Time Sheet Admin."; "Time Sheet Admin.")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies if a user is a time sheet administrator. A time sheet administrator can access any time sheet and then edit, change, or delete it.';
                }
                field(Email; "E-Mail")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the user''s email address.';
                }
                field(PhoneNo; "Phone No.")
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the user''s phone number.';
                }
                //Baluon Apr 18 2022>>
                field("Created By"; "Created By")
                {
                    ApplicationArea = All;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = All;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = All;
                }
                field("Modified date"; "Modified date")
                {
                    ApplicationArea = All;
                }
                //Baluon Apr 18 2022<<


            }
            group("Customization Fields")
            {
                field(State; State)
                {
                    ApplicationArea = all;
                }
                field("Employee ID"; "Employee ID")
                {
                    ApplicationArea = all;
                }
                field("Employee Acc. Loc"; "Employee Acc. Loc")
                {
                    ApplicationArea = all;
                }
                field("Employee CC. Code"; "Employee CC. Code")
                {
                    ApplicationArea = all;
                }
                field("Gen. Jouranl Line Resp. Centre"; "Gen. Jouranl Line Resp. Centre")
                {
                    ApplicationArea = All;
                }
                field(AllowPostingDateModify; AllowPostingDateModify)
                {
                    ApplicationArea = ALL;
                }
                field("Request for Teller Receipt"; "Request for Teller Receipt")
                {
                    ApplicationArea = All;
                }
                field("Edit Max Fuel Availed"; "Edit Max Fuel Availed")
                {
                    ApplicationArea = all;
                }
                field("Approve Transpoter Vehicle"; "Approve Transpoter Vehicle")
                {
                    ApplicationArea = all;
                }
                field("Post Multiple Rebate Cr.Memos"; "Post Multiple Rebate Cr.Memos")
                {
                    ApplicationArea = all;
                }
                field("Upload Productivity Days App."; "Upload Productivity Days App.")
                {
                    ApplicationArea = all;
                }
                field("Process Redistribution App."; "Process Redistribution App.")
                {
                    ApplicationArea = all;
                }
                field("Reprint Dispatch"; "Reprint Dispatch")
                {
                    ApplicationArea = all;
                }
                field("Reprint Branch Report"; "Reprint Branch Report")
                {
                    ApplicationArea = all;
                }
                field("Teller/Cheque Awaiting Confirm"; "Teller/Cheque Awaiting Confirm")
                {
                    ApplicationArea = all;
                }
                field("Teller/Cheque Awaiting BRV"; "Teller/Cheque Awaiting BRV")
                {
                    ApplicationArea = all;
                }
                field("Post Bank Confirmation Reg."; "Post Bank Confirmation Reg.")
                {
                    ApplicationArea = all;
                }
                field("Create Item from template"; "Create Item from template")
                {
                    ApplicationArea = all;
                }
                field("Edit Master Data Template"; "Edit Master Data Template")
                {
                    ApplicationArea = all;
                }
                field("Create Master Data Approval"; "Create Master Data Approval")
                {
                    ApplicationArea = all;
                }
                field("Modify Master Data Approval"; "Modify Master Data Approval")
                {
                    ApplicationArea = all;
                }
                field("Sales Order Modify"; "Sales Order Modify")
                {
                    ApplicationArea = ALL;
                }
                field("Show Item Proft & Othr Details"; "Show Item Proft & Othr Details")
                {
                    ApplicationArea = ALL;
                    //b2bpksalecorr10                
                }
                field("Validate Vendor Classification"; "Validate Vendor Classification")
                {
                    ApplicationArea = all;
                }
                field("Suppl Chain Approval Rights"; "Suppl Chain Approval Rights")
                {
                    ApplicationArea = all;
                }
                field("Revise Capex Budget"; "Revise Capex Budget")
                {
                    ApplicationArea = ALL;
                }
                field("QC Checked (BAD Location)"; "QC Checked (BAD Location)")
                {
                    ApplicationArea = all;
                }
                field("SCD Checked"; "SCD Checked")
                {
                    ApplicationArea = all;
                }
                field("CCD Checked"; "CCD Checked")
                {
                    ApplicationArea = all;
                }
                field("Short Close Checked"; "Short Close Checked")
                {
                    ApplicationArea = all;
                }

                field("Voucher Resp. Ctr. Filter"; "Voucher Resp. Ctr. Filter")
                {
                    ApplicationArea = all;
                }
                field("FA Req. Amt. Appr. Limit"; "FA Req. Amt. Appr. Limit")
                {
                    ApplicationArea = all;
                }
                field("Unlimited FA Req. Approval"; "Unlimited FA Req. Approval")
                {
                    ApplicationArea = all;
                }
                field("MRS-Sample Resp. Ctr. Filter"; "MRS-Sample Resp. Ctr. Filter")
                {
                    ApplicationArea = all;
                }
                field("FA Req. Resp. Ctr. Filter"; "FA Req. Resp. Ctr. Filter")
                {
                    ApplicationArea = all;
                }
                field("Service Request Resp. Ctr. Ftr"; "Service Request Resp. Ctr. Ftr")
                {
                    ApplicationArea = all;
                }
                field("MDV Resp. Ctr. Filter"; "MDV Resp. Ctr. Filter")
                {
                    ApplicationArea = all;
                }
                field("WrkOrdReq Resp. Ctr. Filter"; "WrkOrdReq Resp. Ctr. Filter")
                {
                    ApplicationArea = all;
                }
                field("Prj. Req. Resp. Ctr. Filter"; "Prj. Req. Resp. Ctr. Filter")
                {
                    ApplicationArea = all;
                }
                field("Create Rebate Cred. Memos"; "Create Rebate Cred. Memos")
                {
                    ApplicationArea = all;
                }
                field("Release Prod. Planning"; "Release Prod. Planning")
                {
                    ApplicationArea = all;
                }
                field("Release Prod. Capture"; "Release Prod. Capture")
                {
                    ApplicationArea = all;
                }
                field("Create Prod. order"; "Create Prod. order")
                {
                    ApplicationArea = all;
                }
                field("Release Stock Capture"; "Release Stock Capture")
                {
                    ApplicationArea = all;
                }
                field("Store Type"; "Store Type")
                {
                    ApplicationArea = all;
                }
                field("Get Prod. MRS Notification"; "Get Prod. MRS Notification")
                {
                    ApplicationArea = all;
                }
                field("Enter Excess PCR Count"; "Enter Excess PCR Count")
                {
                    ApplicationArea = all;
                }
                field("Approve Fa Mvt Reg"; "Approve Fa Mvt Reg")
                {
                    ApplicationArea = all;
                }
                field("FA Req. To BUH Approval"; "FA Req. To BUH Approval")
                {
                    ApplicationArea = all;
                }
                field("Cashier Permission"; "Cashier Permission")
                {
                    ApplicationArea = all;
                }
                field("Edit Chaeque/Teller No."; "Edit Chaeque/Teller No.")
                {
                    ApplicationArea = all;
                }
                field("View Main Cash Vouchers"; "View Main Cash Vouchers")
                {
                    ApplicationArea = all;
                }
                field("View Petty Cash Vouchers"; "View Petty Cash Vouchers")
                {
                    ApplicationArea = all;
                }
                field("View Direct Bank Receipts"; "View Direct Bank Receipts")
                {
                    ApplicationArea = all;
                }
                field("View Indirect Bank Receipts"; "View Indirect Bank Receipts")
                {
                    ApplicationArea = all;
                }
                field("Reprint Payment Documents"; "Reprint Payment Documents")
                {
                    ApplicationArea = all;
                }
                field("Reprint Receipt Documents"; "Reprint Receipt Documents")
                {
                    ApplicationArea = all;
                }
                field("CCD Mail Alert"; "CCD Mail Alert")
                {
                    ApplicationArea = all;
                }
                field("Resend Branch Request"; "Resend Branch Request")
                {
                    ApplicationArea = all;
                }
                field("Undo Shipment"; "Undo Shipment")
                {
                    ApplicationArea = all;
                }
                field("Undo Receipt"; "Undo Receipt")
                {
                    ApplicationArea = all;
                }
                field("Ack. Residence Diesel"; "Ack. Residence Diesel")
                {
                    ApplicationArea = all;
                }
                field("Mark-Scrap App"; "Mark-Scrap App")
                {
                    ApplicationArea = all;
                }
                field("Direct Posting"; "Direct Posting")
                {
                    ApplicationArea = all;
                }
                field("Ammend Br Send"; "Ammend Br Send")
                {
                    ApplicationArea = all;
                }
                field("Allow Inv. Posting From"; "Allow Inv. Posting From")
                {
                    ApplicationArea = all;
                }
                field("Allow Inv. Posting To"; "Allow Inv. Posting To")
                {
                    ApplicationArea = all;
                }
                field("Reprint Invoiced & Credm Docs"; "Reprint Invoiced & Credm Docs")
                {
                    ApplicationArea = all;
                }
                field("Reprint Shipmt & GatePass Docs"; "Reprint Shipmt & GatePass Docs")
                {
                    ApplicationArea = all;
                }
                field("Send Customer History Mail"; "Send Customer History Mail")
                {
                    ApplicationArea = all;
                }
                field("Process KD"; "Process KD")
                {
                    ApplicationArea = all;
                }
                field("Delete RBS Right side data"; "Delete RBS Right side data")
                {
                    ApplicationArea = ALL;
                }
                field("Reopen Receipt Page"; "Reopen Receipt Page")
                {
                    ApplicationArea = all;
                }
                field("Create BRS Out. Dupl Entrie"; "Create BRS Out. Dupl Entrie")
                {
                    ApplicationArea = all;
                }
                field("Prod. MRS Mail Alert"; "Prod. MRS Mail Alert")
                {
                    ApplicationArea = all;
                }
                field("Capt. Prod Qty. Mail Alert"; "Capt. Prod Qty. Mail Alert")
                {
                    ApplicationArea = all;
                }
                field(Password; Password)
                {
                    ApplicationArea = all;
                }
                field("Retail User"; "Retail User")
                {
                    ApplicationArea = all;
                }
                //Balu 05132021>>
                field("Reprint Res. Gpass"; "Reprint Res. Gpass")
                {
                    ApplicationArea = all;
                }
                field("Material Loading Reprint"; "Material Loading Reprint")
                {
                    ApplicationArea = all;
                }
                //Balu 05132021<<
                field("ReOpen App Sales order"; "ReOpen App Sales order")
                {
                    ApplicationArea = all;
                }
                field("MIS Approval"; "MIS Approval")
                {
                    ApplicationArea = all;
                }
                field("Delete No. Series"; "Delete No. Series")
                {
                    ApplicationArea = all;
                }
                field("Open Sales Invoice Page"; "Open Sales Invoice Page")
                {
                    ApplicationArea = all;
                }
                field("Short Close Trans. Order"; "Short Close Trans. Order")
                {
                    ApplicationArea = ALL;
                }
                field("Bin Reclassification"; "Bin Reclassification")
                {
                    ApplicationArea = ALL;
                }
                field("FG Bin Assign"; "FG Bin Assign")//PKONDE16
                {
                    //Editable = "Bin Reclassification";
                    ApplicationArea = All;
                }
                field("open Sales Quote Page"; "open Sales Quote Page")
                {
                    ApplicationArea = all;
                }
                field("Open Sales Order Page"; "Open Sales Order Page")
                {
                    ApplicationArea = all;
                }
                field("Open Purchase Order Page"; "Open Purchase Order Page")
                {
                    ApplicationArea = all;
                }
                field("Open Purchase Invoice Page"; "Open Purchase Invoice Page")
                {
                    ApplicationArea = all;
                }
                field("Open Purchase Quote page"; "Open Purchase Quote page")
                {
                    ApplicationArea = all;
                }
                field(Signature; Signature)
                {
                    ApplicationArea = Basic, Suite;
                    ToolTip = 'Specifies the picture that has been set up for the Employee Signature';

                    trigger OnValidate()
                    begin
                        CurrPage.SaveRecord;
                    end;
                }
                field("Release Service & Rate"; "Release Service & Rate")
                {
                    ApplicationArea = all;
                }
                field("Reverse G/L Entry"; "Reverse G/L Entry")
                {
                    ApplicationArea = all;
                }
                field("Reverse BRS"; "Reverse BRS")
                {
                    ApplicationArea = all;
                }
                field("Delete Approval Entries"; "Delete Approval Entries") //PKONAU11
                {
                    ApplicationArea = all;
                }
                field("Open Released Import PO"; "Open Released Import PO")//PKONAU20
                {
                    ApplicationArea = all;
                }
                field("Open Released Local PO"; "Open Released Local PO")//PKONAU20
                {
                    ApplicationArea = all;
                }
                field("Open Released PMS Voucher"; "Open Released PMS Voucher")//PKONAU20
                {
                    ApplicationArea = all;
                }
                //B2BMSOn17Jan2022>>
                field("Open Staff Bank Pmt Voucher"; "Open Staff Bank Pmt Voucher")
                {
                    ApplicationArea = all;
                }
                //B2BMSOn17Jan2022<<
                field("Edit PMS Entries"; "Edit PMS Entries")
                {
                    ApplicationArea = all;
                }
                //Go2solve March 29 2023 >>
                field(SVAlert; SVAlert)
                {
                    ApplicationArea = All;

                }
                //Go2solve March 29 2023 <<
                //>>>>>> G2S 12/12/2024 CAS-01279-Z0M2J6
                field("CHIERP_Complimentary Sales"; "CHIERP_Complimentary Sales")
                {
                    ApplicationArea = All;

                }
                //>>>>>> G2S 12/12/2024 CAS-01279-Z0M2J6
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}