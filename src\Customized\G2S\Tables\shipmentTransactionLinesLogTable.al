/// <summary>
/// Table Shipment Transaction Lines Log (ID 50100).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
table 50053 "Shipment Transaction Lines Log"
{
    DataClassification = CustomerContent;

    fields
    {
        field(1; ID; Integer)
        {
            DataClassification = CustomerContent;
            AutoIncrement = true;
        }
        field(2; "Sell-to Customer No."; Code[20])
        {
            Caption = 'Sell-to Customer No.';
            DataClassification = CustomerContent;
        }
        field(3; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            DataClassification = CustomerContent;
        }
        field(4; "Line No."; Integer)
        {
            Caption = 'Line No.';
            DataClassification = CustomerContent;
        }
        field(5; Type; Text[200])
        {
            Caption = 'Type';
            DataClassification = CustomerContent;
        }
        field(6; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = CustomerContent;
        }
        field(7; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            DataClassification = CustomerContent;
        }
        field(8; "Posting Group"; Code[20])
        {
            Caption = 'Posting Group';
            DataClassification = CustomerContent;
        }
        field(10; "Shipment Date"; Date)
        {
            Caption = 'Shipment Date';
            DataClassification = CustomerContent;
        }
        field(11; Description; Text[100])
        {
            Caption = 'Description';
            DataClassification = CustomerContent;
        }
        field(12; "Description 2"; Text[50])
        {
            Caption = 'Description 2';
            DataClassification = CustomerContent;
        }
        field(13; "Unit of Measure"; Text[50])
        {
            Caption = 'Unit of Measure';
            DataClassification = CustomerContent;
        }
        field(15; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DataClassification = CustomerContent;
        }
        field(22; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            DataClassification = CustomerContent;
        }
        field(23; "Unit Cost (LCY)"; Decimal)
        {
            Caption = 'Unit Cost (LCY)';
            DataClassification = CustomerContent;
        }
        field(25; "VAT %"; Decimal)
        {
            Caption = 'VAT %';
            DataClassification = CustomerContent;
        }
        field(27; "Line Discount %"; Decimal)
        {
            Caption = 'Line Discount %';
            DataClassification = CustomerContent;
        }
        field(100; "Batch Code"; Text[250])
        {
            DataClassification = CustomerContent;
        }
        field(101; "Pack Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(102; "Manufacturing Date"; Code[100])
        {
            DataClassification = CustomerContent;
        }
        field(103; "Expiration Date"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(104; "Gross Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(105; "Discount Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(99; "VAT Base Amount"; Decimal)
        {
            Caption = 'VAT Base Amount';
            DataClassification = CustomerContent;
        }
        field(106; "Tax 2 Percent"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(107; "Tax 2 Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(108; "Tax 3 Percent"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(109; "Tax 3 Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(110; "Net Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }

    }

    keys
    {
        key(PK; ID, "Line No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    begin

    end;

    trigger OnModify()
    begin

    end;

    trigger OnDelete()
    begin

    end;

    trigger OnRename()
    begin

    end;
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<