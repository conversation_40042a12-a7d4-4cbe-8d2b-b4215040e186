page 50303 "Trans Setll Subform"
{
    PageType = ListPart;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Transfer Settlement Lines";
    Editable = false;
    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Transport Ledger Entry No."; "Transport Ledger Entry No.")
                {
                    ApplicationArea = all;
                }
                field("Posted Loading Slip No."; "Posted Loading Slip No.")
                {
                    ApplicationArea = all;
                }
                field("Trip Id"; "Trip Id")
                {
                    ApplicationArea = all;
                }
                field("Trip Date"; "Trip Date")
                {
                    ApplicationArea = all;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = all;
                }
                field("Max Dist. Location"; "Max Dist. Location")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Vehicle By"; "Vehicle By")
                {
                    ApplicationArea = all;
                }

                field("Vehicle Type"; "Vehicle Type")
                {
                    ApplicationArea = all;
                }
                field("Vehicle No"; "Vehicle No")
                {
                    ApplicationArea = all;

                }
                field("Contract Type"; "Contract Type")
                {
                    ApplicationArea = all;
                }
                field("PLS From-Location"; "PLS From-Location")
                {
                    ApplicationArea = all;
                }
                field("PLS To-Location"; "PLS To-Location")
                {
                    ApplicationArea = all;
                }
                /*
                                field("Acknowledge"; "Acknowledge")
                                {
                                    ApplicationArea = all;
                                }
                                field("Acknowledge Date"; "Acknowledge Date")
                                {
                                    ApplicationArea = all;
                                }
                                field("Acknowledge By"; "Acknowledge By")
                                {
                                    ApplicationArea = all;
                                    Editable = FALSE;
                                }
                field(Distance; Distance)
                {
                    ApplicationArea = all;
                }*/
                field("CCD No."; "CCD No.")
                {
                    ApplicationArea = all;
                }
                field("CCD Acknowledge Date"; "CCD Acknowledge Date")
                {
                    ApplicationArea = all;
                }
                field("CCD Acknowledge By"; "CCD Acknowledge By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("SSD No."; "SSD No.")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    begin

                    end;
                }
                field("SSD Acknowledge Date"; "SSD Acknowledge Date")
                {
                    ApplicationArea = all;
                }
                field("SSD Acknowledge By"; "SSD Acknowledge By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = all;
                    TableRelation = Vendor;
                }
                field("LOADing & OFFLOADiNG TIME COMB"; "LOADing & OFFLOADiNG TIME COMB")
                {
                    ApplicationArea = all;
                    //TableRelation = Vendor;//PKON22JA6//PKON22JA7-CR220005-2
                }
                field("Full Diseal Price"; "Full Diseal Price")
                {
                    ApplicationArea = all;
                    //TableRelation = Vendor;//PKON22JA6//PKON22JA7-CR220005-2
                }
                field("Half Diseal Price"; "Half Diseal Price")
                {
                    ApplicationArea = all;
                    TableRelation = Vendor;
                }
                /*                field("Document Type"; "Document Type")
                {
                    ApplicationArea = all;
                                }*/
                field("Amount(Non Vat)"; "Amount(Non Vat)")
                {
                    ApplicationArea = all;
                }
                field("Amount(Vat)"; "Amount(Vat)")
                {
                    ApplicationArea = all;
                }
                field(VATProdPosGrp; VATProdPosGrp)
                {
                    ApplicationArea = all;
                }
                field("Purchase Document No."; "Purchase Document No.")
                {
                    ApplicationArea = all;
                }
                field("Order Created"; "Order Created")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}