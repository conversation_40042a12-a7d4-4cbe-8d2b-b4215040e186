page 50146 "WH Posted Mat. Disp. Subform"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      : Henry
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      CHI       29-Dec-11    -> Form created for Material Requisitions.
    // 
    // 1.0      HO        22-Feb-12    -> Code added to "OnOpenForm()"

    AutoSplitKey = true;
    DelayedInsert = true;
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = true;
    PageType = ListPart;
    SourceTable = "MDV Line";
    UsageCategory = tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("MDV Type"; "MDV Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Type; Type)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Type"; "Disposal Type")
                {
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Item Variant Code"; "Item Variant Code")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("FA Posting Type"; "FA Posting Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("FA No."; "FA No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Maintenance Code"; "Maintenance Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Location Code"; "Location Code")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Available Stock"; "Available Stock")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Qty. to Dispose"; "Qty. to Dispose")
                {
                    ApplicationArea = all;
                }
                field("Total Item Qty Batched"; "Total Item Qty Batched")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Qty. Batched Not Disposed"; "Qty. Batched Not Disposed")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Quantity Disposed"; "Quantity Disposed")
                {
                    ApplicationArea = all;
                }
                /* field("Indent Dept."; "Indent Dept.")
                 {
                     ApplicationArea = all;
                     Editable = false;
                 }
                 field("Indent Bus. Unit"; "Indent Bus. Unit")
                 {
                     ApplicationArea = all;
                     Editable = false;
                 }*/
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
    }
    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Item Tracking Lines")
                {
                    Caption = 'Item Tracking Lines';
                    //Visible = false;

                    trigger OnAction();
                    begin
                        OpenItemTracking(Rec);

                    end;
                }

                action(ShowItemJournalIssue)
                {
                    ApplicationArea = ALL;
                    Caption = 'Show Item Journal Disposal';
                    Image = ShowList;
                    trigger onaction()
                    var
                        ItemJournalLine: Record "Item Journal Line";
                        ItemJournal: Page "Item Journal";
                    BEGIN
                        ItemJournalLine.reset;
                        ItemJournalLine.SetRange("Journal Template Name", 'DISPOSALS');
                        ItemJournalLine.SetRange("Journal Batch Name", "Document No.");
                        ItemJournalLine.SetRange("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
                        ItemJournalLine.SetRange("Item No.", "No.");
                        IF ItemJournalLine.findset then;
                        Page.RunModal(40, ItemJournalLine);
                    END;
                }

            }
        }

    }
    procedure OpenItemTracking(LpaScrpJlines: Record "MDV Line")
    var
        ItemTrackingForm: page "Item Tracking Lines";
        TrackingSpecification: Record "Tracking Specification";
    begin
        TrackingSpecification.InitfromMDV(LpaScrpJlines);
        ItemTrackingForm.SetSourceSpec(TrackingSpecification, WorkDate);
        ItemTrackingForm.SetInbound(false);
        ItemTrackingForm.RUNMODAL;
    end;

}

