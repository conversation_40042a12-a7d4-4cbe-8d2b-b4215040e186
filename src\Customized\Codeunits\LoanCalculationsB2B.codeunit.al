codeunit 50122 "Loan Calculations B2B"
{
    trigger OnRun();
    begin
    end;

    var
        EMILimitAmount: Decimal;
        InstallmentsInserted: Boolean;
        Text002Txt: Label 'Loan Card and Installments Created for the Loan card %1';
        Text33001Txt: Label '<1M>';
        Text33002Txt: Label 'BASIC';
        Text33003Txt: Label 'DA';
        Text33004Txt: Label 'AFTER BASIC';
        Text33005Txt: Label 'AFTER BASIC AND DA';

    procedure LoanInstallments(Loan: Record Loan_B2B);
    var
    begin
        Loan.TestField("Loan Posting Group");
        case Loan."Interest Method" of
            Loan."Interest Method"::"Interest Free":
                InterestFree(Loan);
            Loan."Interest Method"::"Flat Rate":
                FlatRate(Loan);
            Loan."Interest Method"::"Dimnishing Rate":
                Dimnishing(Loan);
        end;
    end;

    procedure InterestFree(Loan: Record Loan_B2B);
    var
        LoanDetails: Record "Loan Details B2B";
        PaymentDate: Date;
        I: Integer;
    begin
        CLEAR(InstallmentsInserted);
        CLEAR(EMILimitAmount);
        PaymentDate := Loan."Loan Start Date";

        clear(LneNoLVar);
        LoanDetails.reset;
        LoanDetails.setrange("Loan Id", Loan.Id);
        IF LoanDetails.findlast then
            LneNoLVar := LoanDetails."Line No" + 10000
        else
            LneNoLVar := 10000;

        if Loan."Interest Method" = Loan."Interest Method"::"Interest Free" then begin
            for I := 1 to Loan."No of Installments" do begin
                LoanDetails.INIT();
                LoanDetails."Line No" := LneNoLVar;

                LoanDetails."Loan Id" := Loan.Id;

                LoanDetails."Pay Date" := PaymentDate;
                LoanDetails."EMI Amount" := (Loan."Loan Amount" / Loan."No of Installments");
                LoanDetails.Principal := (Loan."Loan Amount" / Loan."No of Installments");
                LoanDetails.Month := DATE2DMY(LoanDetails."Pay Date", 2);
                LoanDetails.Year := DATE2DMY(LoanDetails."Pay Date", 3);
                LoanDetails."Loan Amount" := Loan."Loan Amount";
                LoanDetails."Customer No." := Loan."Customer No.";
                LoanDetails.INSERT();
                InstallmentsInserted := true;
                LneNoLVar += 10000;
                PaymentDate := CALCDATE(Text33001Txt, PaymentDate);
            end;

            Loan."Effective Amount" := LoanDetails."EMI Amount";
            Loan."Installment Amount" := LoanDetails."EMI Amount";//B2BFix08Aug2021
            Loan.MODIFY()
        end;
        if InstallmentsInserted then
            MESSAGE(Text002Txt, LoanDetails."Loan Id");
    end;

    procedure FlatRate(Loan: Record Loan_B2B);
    var
        LoanDetails: Record "Loan Details B2B";
        PaymentDate: Date;
        InterestAmt: Decimal;
        MonthlyInterest: Decimal;
        IntRateperMonth: Decimal;
        EMIAmt: Decimal;
        I: Integer;
    begin
        CLEAR(InstallmentsInserted);
        IntRateperMonth := Loan."Interest Rate" / 12;
        MonthlyInterest := ROUND((Loan."Loan Amount" * IntRateperMonth) / 100, 0.01);
        InterestAmt := ROUND((MonthlyInterest * Loan."No of Installments"), 0.01);
        PaymentDate := Loan."Loan Start Date";
        EMIAmt := ROUND(((Loan."Loan Amount" + InterestAmt) / Loan."No of Installments"), 0.01);


        clear(LneNoLVar);
        LoanDetails.reset;
        LoanDetails.setrange("Loan Id", Loan.Id);
        IF LoanDetails.findlast then
            LneNoLVar := LoanDetails."Line No" + 10000
        else
            LneNoLVar := 10000;

        for I := 1 to Loan."No of Installments" do begin
            LoanDetails.INIT();
            LoanDetails."Line No" := LneNoLVar;

            LoanDetails."Loan Id" := Loan.Id;

            LoanDetails."Pay Date" := PaymentDate;
            LoanDetails."EMI Amount" := EMIAmt;
            LoanDetails.Interest := MonthlyInterest;
            LoanDetails.Principal := EMIAmt - MonthlyInterest;
            LoanDetails.Month := DATE2DMY(LoanDetails."Pay Date", 2);
            LoanDetails.Year := DATE2DMY(LoanDetails."Pay Date", 3);
            LoanDetails."Loan Amount" := Loan."Loan Amount";
            LoanDetails."Customer No." := Loan."Customer No.";
            PaymentDate := CALCDATE(Text33001Txt, PaymentDate);
            LoanDetails.INSERT();
            InstallmentsInserted := true;
            LneNoLVar += 10000;
        end;

        Loan."Total Loan Payable" := Loan."Loan Amount" + InterestAmt;
        Loan."Total Interest Amount" := InterestAmt;
        Loan."Effective Amount" := LoanDetails."EMI Amount";
        Loan."Installment Amount" := LoanDetails."EMI Amount";//B2BFix08Aug2021
        Loan.MODIFY();
        if InstallmentsInserted then
            MESSAGE(Text002Txt, LoanDetails."Loan Id");
    end;

    procedure Dimnishing(Loan: Record Loan_B2B);
    var
        LoanDetails: Record "Loan Details B2B";
        EMIAmt: Decimal;
        InterestPerMonth: Decimal;
        EMIValue2: Decimal;
        EMIValue3: Decimal;
        Balance: Decimal;
        J: Integer;
        I: Integer;
        PaymentDate: Date;
        TempDate: Date;
    begin
        CLEAR(InstallmentsInserted);
        Loan.TESTFIELD(Loan."No of Installments");
        PaymentDate := Loan."Loan Start Date";
        InterestPerMonth := (1200 + Loan."Interest Rate") / 1200;
        EMIValue2 := Loan."Loan Amount" * POWER(InterestPerMonth, Loan."No of Installments");
        EMIValue3 := 0;

        clear(LneNoLVar);
        LoanDetails.reset;
        LoanDetails.setrange("Loan Id", Loan.Id);
        IF LoanDetails.findlast then
            LneNoLVar := LoanDetails."Line No" + 10000
        else
            LneNoLVar := 10000;

        for J := 0 to (Loan."No of Installments" - 1) do
            EMIValue3 := EMIValue3 + POWER(InterestPerMonth, J);
        EMIAmt := ROUND((EMIValue2 / EMIValue3), 0.1, '=');
        Balance := Loan."Loan Amount";

        for I := 1 to Loan."No of Installments" do begin
            TempDate := PaymentDate;
            LoanDetails.INIT();
            LoanDetails."Line No" := LneNoLVar;

            LoanDetails."Loan Id" := Loan.Id;

            LoanDetails."Pay Date" := PaymentDate;
            LoanDetails."EMI Amount" := EMIAmt;
            LoanDetails.Interest := ROUND(((Balance * Loan."Interest Rate") / (1200)), 0.1, '=');
            LoanDetails.Principal := EMIAmt - LoanDetails.Interest;
            LoanDetails.Month := DATE2DMY(LoanDetails."Pay Date", 2);
            LoanDetails.Year := DATE2DMY(LoanDetails."Pay Date", 3);
            LoanDetails."Loan Amount" := Loan."Loan Amount";
            LoanDetails."Balance (Base)" := Balance;
            Balance := Balance - LoanDetails.Principal;
            PaymentDate := CALCDATE(Text33001Txt, PaymentDate);
            LoanDetails."Customer No." := Loan."Customer No.";
            LoanDetails.INSERT();
            InstallmentsInserted := true;

            LneNoLVar += 10000;
        end;

        Loan."Effective Amount" := LoanDetails."EMI Amount";
        Loan."Installment Amount" := LoanDetails."EMI Amount";//B2BFix08Aug2021
        Loan.MODIFY();
        if InstallmentsInserted then
            MESSAGE(Text002Txt, LoanDetails."Loan Id");
    end;

    procedure LoanInstallmentsAll();
    var
        Loan: Record Loan_B2B;
        Text000Txt: Label 'Loan Id..........#1###########\Employee code....#2###############\', Comment = '%1 = Loan ID;%2 = Employee';
        Window: Dialog;
    begin
        Window.OPEN(Text000Txt);
        Loan.RESET();
        if Loan.FINDFIRST() then
            repeat
                Window.UPDATE(1, Loan.Id);

                LoanInstallments(Loan);
            until Loan.NEXT() = 0;

        Window.CLOSE();
    end;

    procedure ExistingLoanInstallments(Loan: Record Loan_B2B);
    var
        LoanDetails: Record "Loan Details B2B";
        PaymentDate: Date;
        I: Integer;

    begin
        clear(LneNoLVar);
        LoanDetails.reset;
        LoanDetails.setrange("Loan Id", Loan.Id);
        IF LoanDetails.findlast then
            LneNoLVar := LoanDetails."Line No" + 10000
        else
            LneNoLVar := 10000;

        PaymentDate := Loan."Loan Start Date";
        if Loan."Interest Method" = Loan."Interest Method"::"Interest Free" then begin
            for I := 1 to Loan."No of Installments" do begin
                LoanDetails.INIT();
                LoanDetails."Loan Id" := Loan.Id;
                LoanDetails."Line No" := LneNoLVar;
                LoanDetails."Pay Date" := PaymentDate;
                LoanDetails."EMI Amount" := Loan."Installment Amount";
                LoanDetails.Month := DATE2DMY(LoanDetails."Pay Date", 2);
                LoanDetails.Year := DATE2DMY(LoanDetails."Pay Date", 3);
                LoanDetails."Loan Amount" := Loan."Loan Amount";
                PaymentDate := CALCDATE(Text33001Txt, PaymentDate);
                LoanDetails.INSERT();
                LneNoLVar += 10000;
            end;
            Loan."Effective Amount" := LoanDetails."EMI Amount";
            Loan."Installment Amount" := LoanDetails."EMI Amount";//B2BFix08Aug2021
            Loan.MODIFY();
        end;
    end;

    procedure ExistingLoanInstallmentsAll();
    var
        Loan: Record Loan_B2B;
        Text000Txt: Label 'Loan Id..........#1###########\Employee code....#2###############\', Comment = '%1 = Loan ID;%2 = Employee';
        Window: Dialog;
    begin
        Window.OPEN(Text000Txt);
        Loan.RESET();
        if Loan.FINDFIRST() then
            repeat
                Window.UPDATE(1, Loan.Id);

                ExistingLoanInstallments(Loan);
            until Loan.NEXT() = 0;
        Window.CLOSE();
    end;

    var
        LneNoLVar: integer;
}

