codeunit 50041 "LoadigSlip Acknw not done mail"
{
    //TableNo = "Job Queue Entry";
    trigger OnRun()
    begin
        SendMail()
    end;

    local procedure SendMail()
    var
        PSLHDLV: Record "Posted Loading SLip Header";
        SMTPMailSetup: Record "SMTP Mail Setup";
        InvSet: Record "Inventory Setup";
        SMTPMail: Codeunit "SMTP Mail";
        Vend: Record Vendor;
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
        VendNum: text;
    begin
        SMTPMailSetup.get();
        InvSet.get;
        IF InvSet."Mail ID If Loadslp Nt Ack" <> '' then begin
            VendNum := InvSet."Mail ID If Loadslp Nt Ack";
            SenderAddr := SMTPMailSetup."User ID";
            RecepientAddr.Add(VendNum);
            SubjectTxt := 'Not Acknowledged Posted Loading Slip' + FORMAT(WorkDate());
            SMTPMAil.CreateMessage('CCD and SCD Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
            //SMTPMail.AddCC();
            SMTPMail.AppendBody(CreateEmailBody());
            SMTPMAil.send;
        end;
    end;

    Local procedure CreateEmailBody() EmailBodyText: Text
    var
        PSLHD: Record "Posted Loading SLip Header";
    begin
        PSLHD.Reset();
        PSLHD.SetRange("SSD No.", '');
        IF PSLHD.FindSet() then BEGIN
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD.FieldCaption("No."));
            EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD.FieldCaption("Loading Slip Ref No."));
            EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD.FieldCaption("Vehicle No."));
            EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD.FieldCaption("Vehicle Capacity in Tons"));
            EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD.FieldCaption("Loaded Qty"));
            EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD.FieldCaption("Loaders Code"));
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD."No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD."Loading Slip Ref No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD."Vehicle No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD."Vehicle Capacity in Tons");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD."Loaded Qty");
                EmailBodyText += StrSubstNo('<td>%1</td>', PSLHD."Loaders Code");
                EmailBodyText += '</tr>';
            until PSLHD.Next = 0;
            EmailBodyText += '</table>';
            exit(EmailBodyText);
        end;
    end;
}