page 50695 "POS Line"
{
    // version GJ_CHI_POS_1.0

    AutoSplitKey = true;
    Caption = 'POS Line_L';
    DelayedInsert = true;
    LinksAllowed = false;
    PageType = ListPart;
    SourceTable = "Sales Line";
    UsageCategory = Lists;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                /*field("Item Barcode";"Item Barcode")
                {
                }*/
                field("No2."; "No2.")
                {
                    Caption = 'No.';
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field(Description; Description)
                {
                }
                field("Loc. wise Inventory"; "Loc. wise Inventory")
                {
                }
                field("Item Category Code"; "Item Category Code")
                {
                    Visible = false;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    Editable = "Unit of Measure CodeEditable";
                }
                field("Assorted Mix"; "Assorted Mix")
                {
                    Visible = false;
                    trigger OnValidate();
                    begin
                        AssortedMixOnPush;
                    end;
                }
                field("Assorted Quantity"; "Assorted Quantity")
                {
                    Visible = false;
                    Editable = "Assorted QuantityEditable";
                }
                field(Quantity; Quantity)
                {
                    Editable = QuantityEditable;

                    trigger OnValidate();
                    var
                        SalesLine: Record "Sales Line";
                    begin
                        if "POS Window" then begin
                            VALIDATE("Qty. to Ship", Quantity);
                            CalcFields("Loc. wise Inventory");
                            SalesLine.Reset();
                            SalesLine.SetRange("Document Type", "Document Type");
                            SalesLine.SetRange("Document No.", "Document No.");
                            SalesLine.SetRange(Type, SalesLine.Type::Item);
                            SalesLine.SetRange("No.", "No.");
                            SalesLine.SetFilter("Line No.", '<>%1', "Line No.");
                            if SalesLine.FindSet() then
                                SalesLine.CalcSums("Quantity (Base)");
                            if SalesLine."Quantity (Base)" + "Quantity (Base)" > "Loc. wise Inventory" then
                                Error('Inventory is not avaliable');
                            /*if not "Assorted Mix" then begin
                                if BUOMR.GET("Unit of Measure Code") then
                                    if (Quantity mod 1) <> 0 then
                                        ERROR('You cannot add decimals in the quantity field in sales line %1', "Line No.");
                            end;*/
                        end;

                        //SetItemTracking;
                    end;
                }
                field("Unit Price"; "Unit Price")
                {
                    Editable = false;
                }
                field("Amount Including VAT"; "Amount Including VAT")
                {
                    caption = 'Amount';
                }
                field("Location Code"; "Location Code")
                {
                    Visible = false;
                }
                field("Lot No."; "Lot No.")
                {
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action(ItemTrackingLines)
            {
                ApplicationArea = ItemTracking;
                Caption = 'Item &Tracking Lines';
                Image = ItemTrackingLines;
                ShortCutKey = 'Shift+Ctrl+I';
                Enabled = Type = Type::Item;
                ToolTip = 'View or edit serial and lot numbers for the selected item. This action is available only for lines that contain an item.';

                trigger OnAction()
                begin
                    OpenItemTrackingLines;
                end;
            }
            action(Cancel)
            {
                Caption = 'Cancel';
                Promoted = true;
                PromotedCategory = Process;
                Image = CancelLine;
                PromotedIsBig = true;
                trigger OnAction();
                var
                    SalesHeader: Record "Sales Header";
                begin
                    SalesHeader.Get("Document Type", "Document No.");
                    SalesHeader.Status := SalesHeader.Status::Open;
                    SalesHeader.Modify();
                    Rec.Delete(true);
                end;
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        if "Promo. No." <> '' then begin
            QuantityEditable := false;
            "Unit of Measure CodeEditable" := false;
        end else begin
            "Unit of Measure CodeEditable" := true;
            QuantityEditable := true;
        end;
        OnAfterGetCurrRecord;
        AssortedQuantityOnFormat;
        QuantityOnFormat;
    end;

    trigger OnInit();
    begin
        "Assorted QuantityEditable" := true;
        "Unit of Measure CodeEditable" := true;
        QuantityEditable := true;
    end;

    trigger OnInsertRecord(BelowxRec: Boolean): Boolean;
    begin
        "POS Window" := true;
    end;

    trigger OnModifyRecord(): Boolean;
    begin
        "POS Window" := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        Type := Type::Item;
        "POS Window" := true;
        "Item Category Code" := 'FG';
        OnAfterGetCurrRecord;
    end;

    trigger OnOpenPage();
    begin
        "POS Window" := true;
    end;

    var
        SalesHeader: Record "Sales Header";
        ShortcutDimCode: array[8] of Code[20];
        //WindowsShell : Automation "'{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0:'{72C24DD5-D70A-438B-8A42-98424B88AFB8}':''{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0'.WshShell";
        SalesLine: Record "Sales Line";
        AsortQty: Decimal;
        BUOMR: Record "Unit of Measure";
        [InDataSet]
        QuantityEditable: Boolean;
        [InDataSet]
        "Unit of Measure CodeEditable": Boolean;
        [InDataSet]
        "Assorted QuantityEditable": Boolean;
        TotalV: Decimal;
        salesOrersub: Page "Sales Order Subform";

    procedure ChangeGiftFlavr();
    begin
        SelectGiftItemFlavour;
    end;

    procedure CheckMixLot();
    begin
        CLEAR(AsortQty);
        SalesLine.RESET;
        SalesLine.SETRANGE("Document Type", "Document Type");
        SalesLine.SETRANGE("Document No.", "Document No.");
        SalesLine.SETRANGE("Assorted Mix", true);
        SalesLine.SETRANGE("Retail Product Code", "Retail Product Code");
        SalesLine.SETFILTER("Assorted Quantity", '<>%1', 0);
        if SalesLine.FINDFIRST then
            repeat
                AsortQty += SalesLine.Quantity;
            until SalesLine.NEXT = 0;
        if ((ROUND(AsortQty, 0.001, '=') mod 1) <> 0) then
            ERROR('Carton or Tray Quantity not Sufficient, Either Remove or Book %1 quantity Seperately', AsortQty mod 1);
        //MESSAGE('AQ := %1, AD := %2, AM := %3',AsortQty,(AsortQty DIV 1),(AsortQty MOD 1));
    end;

    local procedure OnAfterGetCurrRecord();
    begin
        xRec := Rec;
        if "Promo. No." <> '' then begin
            QuantityEditable := false;
            "Unit of Measure CodeEditable" := false;
        end else begin
            "Unit of Measure CodeEditable" := true;
            QuantityEditable := true;
        end;
    end;

    local procedure AssortedMixOnPush();
    begin
        if Quantity <> 0 then
            VALIDATE(Quantity, 0);
    end;

    local procedure AssortedQuantityOnFormat();
    begin
        if "Assorted Mix" then
            "Assorted QuantityEditable" := true
        else
            "Assorted QuantityEditable" := false;
    end;

    local procedure QuantityOnFormat();
    begin
        if (("Gift Item") or ("Assorted Mix")) then
            QuantityEditable := false
        else
            QuantityEditable := true;
    end;
}

