﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b578e8f8-d33e-4641-ba18-b5942211a9a0</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Invalid</rd:ReportUnitType>
  <rd:ReportID>dfd5f479-0693-4c18-a0b9-b0863f1b7670</rd:ReportID>
  <Body>
    <ReportItems>
      <Tablix Name="Table1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>3.541cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>14.787cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.804cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.582cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.2cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.2cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.2cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox1">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox11">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox12">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox13">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CPVDescription">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CPVDescription.Value</Value>
                              <Style>
                                <Color>#ff0000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Visibility>
                        <Hidden>true</Hidden>
                      </Visibility>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CASH_PAYMENT_SLIPCaption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CASH_PAYMENT_SLIPCaption.Value</Value>
                              <Style>
                                <Color>#ff0000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Visibility>
                        <Hidden>true</Hidden>
                      </Visibility>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PageCaption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!PageCaption.Value</Value>
                              <Style>
                                <Color>#ff0000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Visibility>
                        <Hidden>true</Hidden>
                      </Visibility>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>1.005cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Amount_in_words_Caption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!Amount_in_words_Caption.Value)</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>0cm</Top>
                      <Left>0.133cm</Left>
                      <Height>1.005cm</Height>
                      <Width>3.404cm</Width>
                      <Style>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>0.137cm</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="AmountInword">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!AmountInword.Value)</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>0cm</Top>
                      <Left>3.811cm</Left>
                      <Height>1.005cm</Height>
                      <Width>14.65cm</Width>
                      <Style>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>0.137cm</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox14">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox15">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox124">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.423cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox16">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="AmountCaption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!AmountCaption.Value)</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>1.244cm</Top>
                      <Left>9.626cm</Left>
                      <Height>0.423cm</Height>
                      <Width>3.22cm</Width>
                      <Style>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>0.137cm</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox17">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox18">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox125">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.423cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox19">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox110">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="amt">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!amt.Value)</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Format>=First(Fields!amtFormat.Value)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>1.322cm</Top>
                      <Left>13.04cm</Left>
                      <Height>0.423cm</Height>
                      <Width>2.691cm</Width>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox111">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox126">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.423cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Voucher_Header___Cash_Paid_By_Caption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!Voucher_Header___Cash_Paid_By_Caption.Value)</Value>
                              <Style>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>1.744cm</Top>
                      <Left>0.159cm</Left>
                      <Height>0.423cm</Height>
                      <Width>3.352cm</Width>
                      <Style>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>0.137cm</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox112">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox113">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox114">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox127">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.423cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Voucher_Header___Cash_Paid_By_">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!Voucher_Header___Cash_Paid_By_.Value)</Value>
                              <Style>
                                <FontSize>7pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>1.769cm</Top>
                      <Left>3.82cm</Left>
                      <Height>0.423cm</Height>
                      <Width>4.384cm</Width>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>0.137cm</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox115">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox116">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox117">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox128">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.423cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox118">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Cashier_SignatureCaption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!Cashier_SignatureCaption.Value)</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Top>2.802cm</Top>
                      <Left>8.806cm</Left>
                      <Height>0.423cm</Height>
                      <Width>4.577cm</Width>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>0.137cm</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox119">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox120">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox129">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.423cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox121">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox122">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Payee_SigntureCaption">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=First(Fields!Payee_SigntureCaption.Value)</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <Top>2.932cm</Top>
                      <Left>13.834cm</Left>
                      <Height>0.423cm</Height>
                      <Width>4.047cm</Width>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox123">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TextBox130">
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Table1_Details_Group">
                <DataElementName>Detail</DataElementName>
              </Group>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
              <DataElementName>Detail_Collection</DataElementName>
              <DataElementOutput>Output</DataElementOutput>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSet_Result</DataSetName>
        <Top>0cm</Top>
        <Height>3.543cm</Height>
      </Tablix>
    </ReportItems>
    <Height>3.543cm</Height>
  </Body>
  <Width>22.314cm</Width>
  <Page>
    <PageHeader>
      <Height>2.115cm</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Textbox Name="PageNumberTextBox">
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Globals!PageNumber</Value>
                  <Style>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>0.45cm</Top>
          <Left>17.783cm</Left>
          <Height>0.423cm</Height>
          <Width>0.582cm</Width>
          <Style>
            <VerticalAlign>Middle</VerticalAlign>
          </Style>
        </Textbox>
        <Textbox Name="UserIdTextBox">
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=User!UserID</Value>
                  <Style>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>0.898cm</Top>
          <Left>15.356cm</Left>
          <Height>0.423cm</Height>
          <Width>3.089cm</Width>
          <Style>
            <VerticalAlign>Middle</VerticalAlign>
          </Style>
        </Textbox>
        <Textbox Name="ExecutionTimeTextBox">
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Globals!ExecutionTime</Value>
                  <Style>
                    <FontSize>7pt</FontSize>
                    <Format>g</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>0cm</Top>
          <Left>15.588cm</Left>
          <Height>0.423cm</Height>
          <Width>2.804cm</Width>
          <Style>
            <VerticalAlign>Middle</VerticalAlign>
          </Style>
        </Textbox>
        <Textbox Name="CPVDescription1">
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=ReportItems!CPVDescription.Value</Value>
                  <Style>
                    <FontSize>14pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
            </Paragraph>
          </Paragraphs>
          <Top>1.269cm</Top>
          <Left>5.25cm</Left>
          <Height>0.846cm</Height>
          <Width>5.67cm</Width>
          <Style>
            <VerticalAlign>Middle</VerticalAlign>
          </Style>
        </Textbox>
        <Textbox Name="CASH_PAYMENT_SLIPCaption1">
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=ReportItems!CASH_PAYMENT_SLIPCaption.Value</Value>
                  <Style>
                    <FontSize>14pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
            </Paragraph>
          </Paragraphs>
          <Top>0.37cm</Top>
          <Left>5.168cm</Left>
          <Height>0.846cm</Height>
          <Width>5.76cm</Width>
          <Style>
            <VerticalAlign>Middle</VerticalAlign>
          </Style>
        </Textbox>
        <Textbox Name="PageCaption1">
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=ReportItems!PageCaption.Value</Value>
                  <Style>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>0.449cm</Top>
          <Left>16.626cm</Left>
          <Height>0.423cm</Height>
          <Width>1.103cm</Width>
          <Style>
            <VerticalAlign>Middle</VerticalAlign>
          </Style>
        </Textbox>
      </ReportItems>
    </PageHeader>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>25.114cm</PageWidth>
    <LeftMargin>2.1cm</LeftMargin>
    <RightMargin>0cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
  </Page>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="FORMAT_TODAY_0_4_">
          <DataField>FORMAT_TODAY_0_4_</DataField>
        </Field>
        <Field Name="CurrReport_PAGENO">
          <DataField>CurrReport_PAGENO</DataField>
        </Field>
        <Field Name="USERID">
          <DataField>USERID</DataField>
        </Field>
        <Field Name="CPVDescription">
          <DataField>CPVDescription</DataField>
        </Field>
        <Field Name="CASH_PAYMENT_SLIPCaption">
          <DataField>CASH_PAYMENT_SLIPCaption</DataField>
        </Field>
        <Field Name="PageCaption">
          <DataField>PageCaption</DataField>
        </Field>
        <Field Name="Voucher_Header_Voucher_Type">
          <DataField>Voucher_Header_Voucher_Type</DataField>
        </Field>
        <Field Name="Voucher_Header_Document_No_">
          <DataField>Voucher_Header_Document_No_</DataField>
        </Field>
        <Field Name="Voucher_Header__ToBeCollectedBy">
          <DataField>Voucher_Header__ToBeCollectedBy</DataField>
        </Field>
        <Field Name="PayTo">
          <DataField>PayTo</DataField>
        </Field>
        <Field Name="PayName">
          <DataField>PayName</DataField>
        </Field>
        <Field Name="Paycode">
          <DataField>Paycode</DataField>
        </Field>
        <Field Name="StaffDept_">
          <DataField>StaffDept_</DataField>
        </Field>
        <Field Name="Paydetail">
          <DataField>Paydetail</DataField>
        </Field>
        <Field Name="CompanyAddr_1_">
          <DataField>CompanyAddr_1_</DataField>
        </Field>
        <Field Name="CompanyAddr_2_">
          <DataField>CompanyAddr_2_</DataField>
        </Field>
        <Field Name="CompanyAddr_3_">
          <DataField>CompanyAddr_3_</DataField>
        </Field>
        <Field Name="CompanyAddr_4_">
          <DataField>CompanyAddr_4_</DataField>
        </Field>
        <Field Name="Voucher_Header__ToBeCollectedByCaption">
          <DataField>Voucher_Header__ToBeCollectedByCaption</DataField>
        </Field>
        <Field Name="PayToCaption">
          <DataField>PayToCaption</DataField>
        </Field>
        <Field Name="Page_Loop_Number">
          <DataField>Page_Loop_Number</DataField>
        </Field>
        <Field Name="Gen__Journal_Line__Document_No__">
          <DataField>Gen__Journal_Line__Document_No__</DataField>
        </Field>
        <Field Name="TODAY">
          <DataField>TODAY</DataField>
        </Field>
        <Field Name="Discr">
          <DataField>Discr</DataField>
        </Field>
        <Field Name="AmountInword">
          <DataField>AmountInword</DataField>
        </Field>
        <Field Name="Voucher_Header___Cash_Paid_By_">
          <DataField>Voucher_Header___Cash_Paid_By_</DataField>
        </Field>
        <Field Name="amt">
          <DataField>amt</DataField>
        </Field>
        <Field Name="amtFormat">
          <DataField>amtFormat</DataField>
        </Field>
        <Field Name="Gen__Journal_Line__Document_No__Caption">
          <DataField>Gen__Journal_Line__Document_No__Caption</DataField>
        </Field>
        <Field Name="TODAYCaption">
          <DataField>TODAYCaption</DataField>
        </Field>
        <Field Name="DiscrCaption">
          <DataField>DiscrCaption</DataField>
        </Field>
        <Field Name="Amount_in_words_Caption">
          <DataField>Amount_in_words_Caption</DataField>
        </Field>
        <Field Name="Voucher_Header___Cash_Paid_By_Caption">
          <DataField>Voucher_Header___Cash_Paid_By_Caption</DataField>
        </Field>
        <Field Name="Cashier_SignatureCaption">
          <DataField>Cashier_SignatureCaption</DataField>
        </Field>
        <Field Name="Payee_SigntureCaption">
          <DataField>Payee_SigntureCaption</DataField>
        </Field>
        <Field Name="AmountCaption">
          <DataField>AmountCaption</DataField>
        </Field>
        <Field Name="Gen__Journal_Line_Journal_Template_Name">
          <DataField>Gen__Journal_Line_Journal_Template_Name</DataField>
        </Field>
        <Field Name="Gen__Journal_Line_Journal_Batch_Name">
          <DataField>Gen__Journal_Line_Journal_Batch_Name</DataField>
        </Field>
        <Field Name="Gen__Journal_Line_Line_No_">
          <DataField>Gen__Journal_Line_Line_No_</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>