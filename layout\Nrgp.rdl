﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>aed81628-8373-4be4-8133-6c988f414db5</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.88972in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.07292in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.98425in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox20">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Source Type</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Source No.</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NoCaption.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox31">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DescriptionCaption.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox31</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox30">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Quantity</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox30</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Qty. In Tonns</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Source_Type.value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!source_No.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox32">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NoLRec.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox32</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox33">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DescriptionLVar.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox33</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!QtyDispatchLRec.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <Format>#,0.00;(#,0.00)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Tonn">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Tonn.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <Format>#,0.00;(#,0.00)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Tonn</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.23622in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox45">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox45</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox46">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox46</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox47">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox47</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox48">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Totals</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox48</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox43">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!QtyDispatchLRec.Value)</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>#,0.00;(#,0.00)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox44">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!Tonn.Value)</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>#,0.00;(#,0.00)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox26</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember>
                  <Group Name="Gate_Entry_No_">
                    <GroupExpressions>
                      <GroupExpression>=Fields!Gate_Entry_No_.Value</GroupExpression>
                      <GroupExpression>=Fields!Line_No_.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!Gate_Entry_No_.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details" />
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.07997in</Top>
            <Left>0.05in</Left>
            <Height>0.73622in</Height>
            <Width>7.94689in</Width>
            <Style>
              <Border>
                <Style>None</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>2.21423cm</Height>
        <Style />
      </Body>
      <Width>20.31211cm</Width>
      <Page>
        <PageHeader>
          <Height>3.6345cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox27">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>GatePass/WayBill</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox27</rd:DefaultName>
              <Top>0.4309in</Top>
              <Left>2.51959in</Left>
              <Height>0.25in</Height>
              <Width>1.625in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox7">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>CHI Limited</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox7</rd:DefaultName>
              <Top>0.10105in</Top>
              <Left>2.75833in</Left>
              <Height>0.26041in</Height>
              <Width>1.04861in</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox28">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Page : </Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Globals!PageNumber</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox28</rd:DefaultName>
              <Top>0.56631in</Top>
              <Left>5.16681in</Left>
              <Height>0.27084in</Height>
              <Width>1.60417in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox8">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!TodayDate.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox8</rd:DefaultName>
              <Top>0.23299in</Top>
              <Left>5.16681in</Left>
              <Height>0.26387in</Height>
              <Width>1.60417in</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox4">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!DispatchedNoCaption.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox4</rd:DefaultName>
              <Top>0.82674in</Top>
              <Left>0.0375in</Left>
              <Height>0.19792in</Height>
              <Width>1.53125in</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox5">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!DispatchedDateCaption.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox4</rd:DefaultName>
              <Top>1.13924in</Top>
              <Left>0.0375in</Left>
              <Height>0.19792in</Height>
              <Width>1.53125in</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox9">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!No_.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox9</rd:DefaultName>
              <Top>0.82674in</Top>
              <Left>1.71333in</Left>
              <Height>0.19792in</Height>
              <Width>2.09361in</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Posting_Date.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Format>MM/dd/yy</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Top>1.1566in</Top>
              <Left>1.71333in</Left>
              <Height>0.18056in</Height>
              <Width>2.09361in</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Image Name="Image1">
              <Source>Database</Source>
              <Value>=First(Fields!CompanyInfoPicture.Value, "DataSet_Result")</Value>
              <MIMEType>image/jpeg</MIMEType>
              <Sizing>FitProportional</Sizing>
              <Top>0.13541in</Top>
              <Left>0.127cm</Left>
              <Height>1.38555cm</Height>
              <Width>1.97555cm</Width>
              <ZIndex>8</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
            <Textbox Name="Textbox11">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Location Code : </Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Fields!Location_Code.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox11</rd:DefaultName>
              <Top>1.13924in</Top>
              <Left>4.98833in</Left>
              <Height>0.18056in</Height>
              <Width>2.15765in</Width>
              <ZIndex>9</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>3.91583cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Rectangle Name="Rectangle2">
              <ReportItems>
                <Textbox Name="Textbox13">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!TransporteCaption.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox13</rd:DefaultName>
                  <Top>0.10764in</Top>
                  <Left>0.49181in</Left>
                  <Height>0.0625in</Height>
                  <Width>1.53334in</Width>
                  <Visibility>
                    <Hidden>true</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox14">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!Transporter_No_.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Left</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox14</rd:DefaultName>
                  <Top>0.10764in</Top>
                  <Left>2.03209in</Left>
                  <Height>0.0625in</Height>
                  <Width>1.625in</Width>
                  <ZIndex>1</ZIndex>
                  <Visibility>
                    <Hidden>true</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox15">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!TransporterNameCaption.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox15</rd:DefaultName>
                  <Top>0.20208in</Top>
                  <Left>0.49181in</Left>
                  <Height>0.04861in</Height>
                  <Width>1.53334in</Width>
                  <ZIndex>2</ZIndex>
                  <Visibility>
                    <Hidden>true</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox16">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!Transporter_Name.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Left</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox16</rd:DefaultName>
                  <Top>0.20208in</Top>
                  <Left>2.03209in</Left>
                  <Height>0.04861in</Height>
                  <Width>1.625in</Width>
                  <ZIndex>3</ZIndex>
                  <Visibility>
                    <Hidden>true</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox17">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!VehicleFACaption.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value xml:space="preserve">    </Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value>=Fields!Vehicle_No_.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Left</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox17</rd:DefaultName>
                  <Top>0.27153in</Top>
                  <Left>0.49181in</Left>
                  <Height>0.25in</Height>
                  <Width>2.7764in</Width>
                  <ZIndex>4</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox25">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!StoreCaption.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox25</rd:DefaultName>
                  <Top>0.01458in</Top>
                  <Left>3.73139in</Left>
                  <Height>0.25in</Height>
                  <Width>1.04166in</Width>
                  <ZIndex>5</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox2">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>PostedBy</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox2</rd:DefaultName>
                  <Top>0.26458in</Top>
                  <Left>3.72653in</Left>
                  <Height>0.2336in</Height>
                  <Width>1.02777in</Width>
                  <ZIndex>6</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox3">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!User_ID.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Left</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox3</rd:DefaultName>
                  <Top>0.22153in</Top>
                  <Left>4.78694in</Left>
                  <Height>0.25in</Height>
                  <Width>3.06598in</Width>
                  <ZIndex>7</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox21">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!PostedLoadingSlip.Value, "DataSet_Result")</Value>
                          <Style />
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox21</rd:DefaultName>
                  <Top>1.33594cm</Top>
                  <Left>1.13453cm</Left>
                  <Height>0.6cm</Height>
                  <Width>10.84791cm</Width>
                  <ZIndex>8</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox22">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!PostedLoadingSlipCN.Value, "DataSet_Result")</Value>
                          <Style />
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox21</rd:DefaultName>
                  <Top>1.93594cm</Top>
                  <Left>1.13453cm</Left>
                  <Height>0.6cm</Height>
                  <Width>10.84791cm</Width>
                  <ZIndex>9</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox26">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Last(Fields!TranspoterName.Value, "DataSet_Result")</Value>
                          <Style />
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox21</rd:DefaultName>
                  <Top>2.56796cm</Top>
                  <Left>1.13453cm</Left>
                  <Height>0.6cm</Height>
                  <Width>10.84791cm</Width>
                  <ZIndex>10</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
              </ReportItems>
              <KeepTogether>true</KeepTogether>
              <Top>0.01389in</Top>
              <Left>0.01389in</Left>
              <Height>1.375in</Height>
              <Width>7.85292in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Rectangle>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0.25cm</LeftMargin>
        <RightMargin>0.25cm</RightMargin>
        <TopMargin>0.25cm</TopMargin>
        <BottomMargin>2cm</BottomMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="No_">
          <DataField>No_</DataField>
        </Field>
        <Field Name="Posting_Date">
          <DataField>Posting_Date</DataField>
        </Field>
        <Field Name="Location_Code">
          <DataField>Location_Code</DataField>
        </Field>
        <Field Name="Transporter_No_">
          <DataField>Transporter_No_</DataField>
        </Field>
        <Field Name="Transporter_Name">
          <DataField>Transporter_Name</DataField>
        </Field>
        <Field Name="User_ID">
          <DataField>User_ID</DataField>
        </Field>
        <Field Name="Vehicle_No_">
          <DataField>Vehicle_No_</DataField>
        </Field>
        <Field Name="DispatchedNoCaption">
          <DataField>DispatchedNoCaption</DataField>
        </Field>
        <Field Name="DispatchedDateCaption">
          <DataField>DispatchedDateCaption</DataField>
        </Field>
        <Field Name="NoCaption">
          <DataField>NoCaption</DataField>
        </Field>
        <Field Name="DescriptionCaption">
          <DataField>DescriptionCaption</DataField>
        </Field>
        <Field Name="QtytoDispatchCaption">
          <DataField>QtytoDispatchCaption</DataField>
        </Field>
        <Field Name="TransporteCaption">
          <DataField>TransporteCaption</DataField>
        </Field>
        <Field Name="TransporterNameCaption">
          <DataField>TransporterNameCaption</DataField>
        </Field>
        <Field Name="VehicleFACaption">
          <DataField>VehicleFACaption</DataField>
        </Field>
        <Field Name="VehicleLcCaptionLbl">
          <DataField>VehicleLcCaptionLbl</DataField>
        </Field>
        <Field Name="DriverNameCaption">
          <DataField>DriverNameCaption</DataField>
        </Field>
        <Field Name="TransporterCaption">
          <DataField>TransporterCaption</DataField>
        </Field>
        <Field Name="StoreCaption">
          <DataField>StoreCaption</DataField>
        </Field>
        <Field Name="Challan_No_">
          <DataField>Challan_No_</DataField>
        </Field>
        <Field Name="Source_Type">
          <DataField>Source_Type</DataField>
        </Field>
        <Field Name="source_No">
          <DataField>source_No</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="Line_No_">
          <DataField>Line_No_</DataField>
        </Field>
        <Field Name="Gate_Entry_No_">
          <DataField>Gate_Entry_No_</DataField>
        </Field>
        <Field Name="CompanyInfoPicture">
          <DataField>CompanyInfoPicture</DataField>
        </Field>
        <Field Name="TodayDate">
          <DataField>TodayDate</DataField>
        </Field>
        <Field Name="NoLRec">
          <DataField>NoLRec</DataField>
        </Field>
        <Field Name="DescriptionLVar">
          <DataField>DescriptionLVar</DataField>
        </Field>
        <Field Name="QtyDispatchLRec">
          <DataField>QtyDispatchLRec</DataField>
        </Field>
        <Field Name="QtyDispatchLRecFormat">
          <DataField>QtyDispatchLRecFormat</DataField>
        </Field>
        <Field Name="DriverGRec">
          <DataField>DriverGRec</DataField>
        </Field>
        <Field Name="Tonn">
          <DataField>Tonn</DataField>
        </Field>
        <Field Name="TonnFormat">
          <DataField>TonnFormat</DataField>
        </Field>
        <Field Name="PostedLoadingSlip">
          <DataField>PostedLoadingSlip</DataField>
        </Field>
        <Field Name="PostedLoadingSlipCN">
          <DataField>PostedLoadingSlipCN</DataField>
        </Field>
        <Field Name="TranspoterName">
          <DataField>TranspoterName</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>