page 50479 "Nibss Notifications"
{
    // version NIBSS

    Editable = false;
    PageType = List;
    SourceTable = "Nibss Notifications";
    UsageCategory = Lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(id; id)
                {
                    Caption = 'ID';
                    ApplicationArea = all;
                }
                field(sessionID; sessionID)
                {
                    Caption = 'Session ID';
                    ApplicationArea = all;
                }
                field(sourceBankCode; sourceBankCode)
                {
                    ApplicationArea = all;
                }
                field(Customercode; Customercode)
                {
                    Caption = 'Customer ID';
                    ApplicationArea = all;
                }
                field(customerName; customerName)
                {
                    Caption = 'Customer Name';
                    ApplicationArea = all;
                }
                field(PaymentReference; PaymentReference)
                {
                    ApplicationArea = all;
                }
                field(narration; narration)
                {
                    Caption = 'Narration';
                    ApplicationArea = all;
                }
                field(amount; amount)
                {
                    ApplicationArea = all;
                }
                field(dateEntered; dateEntered)
                {
                    ApplicationArea = all;
                }
                field(dateUpdated; dateUpdated)
                {
                    ApplicationArea = all;
                }
                field(Remark; Remark)
                {
                    Caption = 'Remarks';
                    ApplicationArea = all;
                }
                field(Processed; Processed)
                {
                    Caption = 'Processed';
                    ApplicationArea = all;
                }
            }
        }
    }
    actions
    {
        area(Navigation)
        {
            action("Upload Customers")
            {
                Caption = 'Upload Customers';
                trigger OnAction()
                var
                    UpCust: Codeunit "Upload Customers";
                Begin
                    UpCust.Run();
                End;
            }
        }

    }
}

