page 50699 "Retail Item Unit Price Update"
{
    ApplicationArea = All;
    Caption = 'Retail Item Unit Price Update';
    PageType = List;
    SourceTable = "Retail Price Change Log";
    UsageCategory = Lists;
    //Editable = FALSE;
    // SourceTableView = where("Sales Code" = filter('RSHOOP'), "Starting Date" = filter(> '09/01/2024'));

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    ApplicationArea = All;

                }
                field("Item Description"; Rec."Item Description")
                {
                    ApplicationArea = All;

                }
                field("Unit of Measure code"; Rec."Unit of Measure")
                {
                    ApplicationArea = All;

                }
                field("Bar code"; Rec."Bar Code")
                {
                    ApplicationArea = All;

                }
                field("Clearwox Item Code"; Rec."Clearwox Item Code")
                {
                    ApplicationArea = All;

                }
                field("Old Price"; Rec."Old Price")
                {
                    ApplicationArea = All;

                }
                field("New Price"; Rec."New Price")
                {
                    ApplicationArea = All;

                }

                field("Unit Qty"; Rec."Unit Qty")
                {
                    ApplicationArea = All;

                }
                field("Wholesale Qty"; Rec."Wholesale Qty")
                {
                    ApplicationArea = All;

                }

                field("Change Date"; Rec."Change Date")
                {
                    ApplicationArea = All;

                }
                field(PushedtoAPI; Rec.PushedtoAPI)
                {
                    ApplicationArea = All;

                }
                field("Response Message"; Rec."Response Message")
                {
                    ApplicationArea = All;

                }



            }
        }
    }
}
