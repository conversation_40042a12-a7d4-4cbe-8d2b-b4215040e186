/// <summary>
/// Codeunit Json Integration (ID 50105).
/// </summary>
//RFCOutreachAPIGo2solve ver #2065 25th Sept., 2023>>>>>>
codeunit 50026 "Json Integration"
{

    trigger OnRun()
    var
        //<<<<<< G2S 1 Aug 2023
        ShipmentHdr: Record "Shipment Transactions Hdr Log";
        ShipmentHdrCopy: Record "Shipment Transactions Hdr Log";
        ShipmentLine: Record "Shipment Transaction Lines Log";
        //>>>>>> G2S 1 Aug 2023
        //140524 >>>>g2s 140524 CRM:0035176
        SalesInv: Record "Sales Invoice Header";
        SalesHdr: Record "Sales Header";
        Customer: Record Customer;
    //140524 <<<< G2S 140524 CRM:0035176
    begin
        //140524 >>>>g2s 140524 CRM:0035176
        //outreach update for posted sales invoice
        SalesInv.Reset();
        SalesInv.SetCurrentKey("Posting Date");
        SalesInv.SetAscending(SalesInv."Posting Date", false);
        SalesInv.SetFilter("Posting Date", '>=%1', 20240501D);
        if SalesInv.FindFirst() then begin
            repeat
                Customer.Reset();
                Customer.SetRange("No.", SalesInv."Sell-to Customer No.");
                Customer.SetRange("DMS Customer", true);
                if Customer.FindFirst() then begin
                    ShipmentHdr.Reset();
                    ShipmentHdr.SetRange("No.", SalesInv."No.");
                    if not ShipmentHdr.FindFirst() then begin
                        MyProcedure(SalesHdr, SalesInv);
                    end;
                end;
            until SalesInv.Next() = 0;
        end;
        //outreach update for posted sales invoice

        //<<<<<< G2S 1 Aug 2023
        ShipmentHdr.Reset();
        ShipmentHdr.SetRange("Temp. Omit Rec", false);
        ShipmentHdr.SetRange("Sent Status", false);
        //>>>>>> G2S 1 Aug 2023
        if ShipmentHdr.FindFirst() then
            repeat
                //260723 start update for manufacturing date
                ShipmentLine.Reset();
                ShipmentLine.SetRange("Document No.", ShipmentHdr."No.");
                if ShipmentLine.FindFirst() then
                    repeat
                        ShipmentLine."Manufacturing Date" := format(ShipmentHdr."Shipment Date");
                        ShipmentLine.Modify();
                    until ShipmentLine.Next() = 0;
                //260723 end update for manufacturing date
                if ShipmentHdrCopy.get(ShipmentHdr.ID) then begin
                    ShipmentHdrCopy."Date Sent to Outreach" := Today();
                    ShipmentHdrCopy."DateTime Sent to Outreach" := CurrentDateTime;
                    ShipmentHdrCopy.Modify();
                end;
                sendBatchShipmentJSONData(ShipmentHdr);
            until ShipmentHdr.Next() = 0;
    end;


    /// <summary>
    /// sendData.
    /// </summary>
    /// <param name="json">JsonObject.</param>
    /// <param name="url">Text.</param>
    /// <returns>Return value of type Boolean.</returns>
    procedure sendData(json: Text; url: Text): Text
    var
        httpClientVar: HttpClient;
        httpRequestMessageVar: HttpRequestMessage;
        httpResponseMessageVar: HttpResponseMessage;
        httpContentVar: HttpContent;
        jsonObj: JsonObject;
        jsonObj1: JsonObject;
        jsonArr: JsonArray;
        jsonTok: JsonToken;
        jsonStatusToken: JsonToken;
        myContent, responseText, status : Text;
        i: Integer;
        sent: Boolean;
        HttpHeadersContent: HttpHeaders;

    begin
        httpResponseMessageVar.Headers.Clear();
        clear(httpResponseMessageVar);
        clear(httpRequestMessageVar);
        clear(httpContentVar);
        clear(httpClientVar);
        clear(HttpHeadersContent);
        // httpRequestMessageVar.Method('POST');
        // httpRequestMessageVar.SetRequestUri(url);
        // httpContentVar.WriteFrom(json);
        // httpRequestMessageVar.Content := httpContentVar;
        // httpClientVar.Send(httpRequestMessageVar, httpResponseMessageVar);
        httpContentVar.WriteFrom(json);
        httpContentVar.GetHeaders(HttpHeadersContent);
        HttpHeadersContent.Remove('Content-Type');
        HttpHeadersContent.Add('Content-Type', 'application/json');
        httpClientVar.SetBaseAddress(URL);
        httpClientVar.Post(url, httpContentVar, httpResponseMessageVar);

        if (httpResponseMessageVar.IsSuccessStatusCode) then begin
            httpResponseMessageVar.Content.ReadAs(myContent);
            // Message('response: %1 with code: %2', myContent, httpResponseMessageVar.HttpStatusCode);
            exit(myContent);
        end
        else begin
            sent := false;
            httpResponseMessageVar.Content.ReadAs(myContent);
            exit(myContent);
        end;
        httpClientVar.DefaultRequestHeaders.Clear();
    end;


    /// <summary>
    /// MyProcedure.
    /// </summary>
    /// <param name="SalesHeader">VAR Record "Sales Header".</param>
    /// <param name="PostedSalesInv">VAR Record "Sales Invoice Header".</param>
    //[EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterFinalizePosting', '', true, true)]
    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterPostInvPostBuffer', '', true, true)]

    // procedure MyProcedure(var SalesHeader: Record "Sales Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesShipmentHeader: Record "Sales Shipment Header")
    procedure MyProcedure(var SalesHeader: Record "Sales Header"; var PostedSalesInv: Record "Sales Invoice Header")
    var
        // SalesShipmentHdr: Record "Sales Shipment Header";
        salesShipmentLine: Record "Sales Shipment Line";
        loadSlip: Record "Posted Loading SLip Line";
        loadSlipHdr: Record "Posted Loading SLip Header";
        WSHdr: Record "Warehouse Shipment Header";
        itemLedgEntry: Record "Item Ledger Entry";
        Customer: Record Customer;
        shipmentHdrLog: Record "Shipment Transactions Hdr Log";
        shipmentLineLog: Record "Shipment Transaction Lines Log";
        grossAmt, discountAmt : Decimal;
        lotNo, expDate, vehicleNo : Text;
        //070224
        SalesShipmentHeader: Record "Sales Shipment Header";
        SalesInvLine: Record "Sales Invoice Line";
        Txt001: Label 'Posted Sales Invoice %1 already exist in the shipment table';

    begin
        //070224

        //090324 only send invoices that are yet to be pushed to ShipmentHdrLog
        //start A
        shipmentHdrLog.Reset();
        shipmentHdrLog.SetRange("No.", PostedSalesInv."No.");
        if not shipmentHdrLog.FindFirst() then begin
            //end A
            vehicleNo := '';
            loadSlip.Reset();
            loadSlip.SetRange("Order No.", PostedSalesInv."Order No.");
            if loadSlip.FindFirst() then begin
                loadSlipHdr.SetRange("No.", loadSlip."Document No.");
                if loadSlipHdr.FindFirst() then
                    vehicleNo := loadSlipHdr."Vehicle No.";
            end;
            customer.SetRange("No.", PostedSalesInv."Sell-to Customer No.");
            if (not customer.FindFirst()) then
                exit
            else
                // if ((SalesHeader.Ship = true) and customer."DMS Customer") then begin
                if (customer."DMS Customer") then begin
                    shipmentHdrLog.Validate("No.", PostedSalesInv."No.");
                    shipmentHdrLog.Validate("Sell-to Customer No.", PostedSalesInv."Sell-to Customer No.");
                    shipmentHdrLog.Validate("Shipment Date", PostedSalesInv."Posting Date");//CAS-01347-Q7Z5K6_POD_DMS_Date InvoiceDate.
                    shipmentHdrLog.Validate(CompanyName, CompanyName);
                    shipmentHdrLog.Validate("Veh. No.", vehicleNo);
                    //>>>>> G2S 061323 
                    //Message('vehicle no. %1', vehicleNo);
                    //<<<<< G2S 061323 
                    SalesInvLine.Reset();
                    SalesInvLine.SetRange("Document No.", PostedSalesInv."No.");
                    if (SalesInvLine.FindSet()) then begin
                        repeat
                            shipmentLineLog.Init();
                            grossAmt := SalesInvLine."Unit Price" * SalesInvLine.Quantity;
                            discountAmt := (SalesInvLine."Line Discount %" * grossAmt) / 100.0;
                            itemLedgEntry.SetRange("Document No.", SalesInvLine."Document No.");
                            itemLedgEntry.SetRange("Item No.", SalesInvLine."No.");

                            if (itemLedgEntry.FindFirst()) then begin
                                //270723 delete special xters in lot number
                                lotNo := DeleteSpecialChars2(itemLedgEntry."Lot No.");
                                //lotNo := itemLedgEntry."Lot No.";
                                //end
                                expDate := Format(itemLedgEntry."Expiration Date");
                            end;
                            shipmentLineLog.Validate("No.", SalesInvLine."No.");
                            shipmentLineLog.Validate("Line No.", SalesInvLine."Line No.");
                            shipmentLineLog.Validate("Document No.", SalesInvLine."Document No.");
                            shipmentLineLog.Validate(Type, Format(SalesInvLine.Type));
                            //shipmentLineLog.Validate("Manufacturing Date", lotNo);
                            shipmentLineLog.Validate("Manufacturing Date", Format(PostedSalesInv."Shipment Date"));
                            //>>>>> G2S 061323 
                            shipmentLineLog.Validate("Batch Code", lotNo);
                            //<<<<< G2S 061323 
                            shipmentLineLog.Validate("Expiration Date", expDate);
                            shipmentLineLog.Validate(Quantity, SalesInvLine.Quantity);
                            shipmentLineLog.Validate("Sell-to Customer No.", SalesInvLine."Sell-to Customer No.");
                            shipmentLineLog.Validate("Unit of Measure", SalesInvLine."Unit of Measure");
                            shipmentLineLog.Validate("Unit Price", SalesInvLine."Unit Price");
                            shipmentLineLog.Validate("Unit Cost (LCY)", SalesInvLine."Unit Cost (LCY)");
                            //shipmentLineLog.Validate("Gross Amount", grossAmt);
                            shipmentLineLog.Validate("Gross Amount", SalesInvLine."Line Amount");
                            // shipmentLineLog.Validate("Discount Amount", discountAmt);
                            shipmentLineLog.Validate("Discount Amount", SalesInvLine."Line Discount Amount");
                            shipmentLineLog.Validate("VAT %", SalesInvLine."VAT %");
                            shipmentLineLog.Validate("VAT Base Amount", SalesInvLine."VAT Base Amount");
                            // shipmentLineLog.Validate("Net Amount", grossAmt - discountAmt + SalesInvLine."VAT Base Amount");
                            shipmentLineLog.Validate("Net Amount", SalesInvLine."Amount Including VAT");
                            shipmentLineLog.Insert(true);
                            shipmentLineLog.Reset();
                        until SalesInvLine.Next() = 0;
                    end;
                    shipmentHdrLog.Insert(true);
                end;
            //end of begin A
        end
        else
            Error(Txt001, PostedSalesInv."No.");
    end;

    /// <summary>
    /// sendBatchJSONData.
    /// </summary>
    /// <param name="shipmentTransactionsHdr">Record "Shipment Transactions Hdr Log".</param>
    /// <returns>Return value of type Text.</returns>
    procedure sendBatchShipmentJSONData(shipmentTransactionsHdr: Record "Shipment Transactions Hdr Log"): Text
    var
        //shipmentTransactionsHdr: Record "Shipment Transactions Hdr Log";
        shipmentTransactionsLine: Record "Shipment Transaction Lines Log";
        masterObj: JsonObject;
        grandObj: JsonObject;
        jsonObj: JsonObject;
        jsonArr: JsonArray;
        hdrItemsObj: JsonObject;
        itemsObj: JsonObject;
        detailsArr: JsonArray;
        grnArray: JsonArray;
        content: Text;
        jsonIntegration: Codeunit "Json Integration";
        responseJsonObj, detailedJSONObject : JsonObject;
        responseArray: JsonArray;
        jsonTok, responseToken, statusToken, invToken, remarkToken : JsonToken;
        data, url, response, detailedResponse : Text;
        boolStatus, TxnErrCheckStatus : Boolean;
        i: Integer;
        VatAmount: Decimal;
    begin
        hdrItemsObj.Add('invno', '');
        hdrItemsObj.Add('dtcode', '');
        hdrItemsObj.Add('invdate', '');
        hdrItemsObj.Add('supcode', '');
        hdrItemsObj.Add('vehno', '');
        itemsObj.Add('skucode', '');
        itemsObj.Add('skutype', '');
        itemsObj.Add('batchcode', '');
        itemsObj.Add('pakdate', '');
        itemsObj.Add('mfd', '');
        itemsObj.Add('expdate', '');
        itemsObj.Add('qty', '');
        itemsObj.Add('uom', '');
        itemsObj.Add('mrp', '');
        itemsObj.Add('rate', '');
        itemsObj.Add('gross', '');
        itemsObj.Add('trddisc', '');
        itemsObj.Add('tax1per', '');
        itemsObj.Add('tax1amt', '');
        itemsObj.Add('tax2per', '');
        itemsObj.Add('tax2amt', '');
        itemsObj.Add('tax3per', '');
        itemsObj.Add('tax3amt', '');
        itemsObj.Add('netamt', '');
        hdrItemsObj.Add('details', '');
        jsonObj.Add('header', '');
        // shipmentTransactionsHdr.Reset();
        // shipmentTransactionsHdr.SetRange("Sent Status", false);
        // if (shipmentTransactionsHdr.FindFirst()) then begin
        //     repeat
        hdrItemsObj.Replace('invno', shipmentTransactionsHdr."No.");
        hdrItemsObj.Replace('dtcode', shipmentTransactionsHdr."Sell-to Customer No.");
        hdrItemsObj.Replace('invdate', shipmentTransactionsHdr."Shipment Date");
        hdrItemsObj.Replace('supcode', shipmentTransactionsHdr.CompanyName);
        hdrItemsObj.Replace('vehno', shipmentTransactionsHdr."Veh. No.");
        shipmentTransactionsLine.Reset();
        shipmentTransactionsLine.SetRange("Document No.", shipmentTransactionsHdr."No.");
        // shipmentTransactionsLine.SetFilter(Quantity, '<>%1', 0);
        // 300723 exclude transactions with no amount
        // shipmentTransactionsLine.SetFilter("Gross Amount", '<>%1', 0);
        // 300723 end
        if (shipmentTransactionsLine.FindSet()) then begin
            repeat
                if (((shipmentTransactionsLine."Gross Amount" <> 0) and (shipmentTransactionsLine.Quantity <> 0))
             OR
             (((shipmentTransactionsLine."Gross Amount" = 0) and (shipmentTransactionsLine.Quantity <> 0)) or ((shipmentTransactionsLine."Gross Amount" <> 0) and (shipmentTransactionsLine.Quantity = 0)))) then begin
                    VatAmount := 0;
                    itemsObj.Replace('skucode', shipmentTransactionsLine."No.");
                    if ((shipmentTransactionsLine.Quantity <> 0) and (shipmentTransactionsLine."Gross Amount" <> 0)) then
                        itemsObj.Replace('skutype', 'BILLED');
                    if ((shipmentTransactionsLine.Quantity <> 0) and (shipmentTransactionsLine."Gross Amount" = 0)) then
                        itemsObj.Replace('skutype', 'FREE');
                    //itemsObj.Replace('skutype', shipmentTransactionsLine.Type);
                    //270723 delete special xters in lot number
                    //200923 start
                    //replace lot no with skucode i.e item no
                    // shipmentTransactionsLine."Batch Code" := DeleteSpecialChars(shipmentTransactionsLine."Batch Code");
                    //itemsObj.Replace('batchcode', shipmentTransactionsLine."Batch Code");
                    //200923 end
                    //end
                    itemsObj.Replace('batchcode', shipmentTransactionsLine."No.");
                    itemsObj.Replace('pakdate', '');
                    //update mfd and change to shipment date 260723
                    //itemsObj.Replace('mfd', shipmentTransactionsLine."Manufacturing Date");
                    //update mfd and change to shipment date 260723

                    itemsObj.Replace('mfd', shipmentTransactionsLine."Manufacturing Date");
                    itemsObj.Replace('expdate', shipmentTransactionsLine."Expiration Date");
                    itemsObj.Replace('qty', shipmentTransactionsLine.Quantity);
                    //itemsObj.Replace('uom', shipmentTransactionsLine."Unit of Measure");
                    itemsObj.Replace('uom', 'CASE');
                    itemsObj.Replace('mrp', 0);
                    itemsObj.Replace('rate', shipmentTransactionsLine."Unit Cost (LCY)");
                    itemsObj.Replace('gross', shipmentTransactionsLine."Gross Amount");
                    itemsObj.Replace('trddisc', shipmentTransactionsLine."Discount Amount");
                    itemsObj.Replace('tax1per', shipmentTransactionsLine."VAT %");
                    //>>>>> G2S 061323 
                    if shipmentTransactionsLine."VAT Base Amount" <> 0 then
                        itemsObj.Replace('tax1amt', shipmentTransactionsLine."VAT Base Amount")
                    else begin
                        VatAmount := ((shipmentTransactionsLine."VAT %" / 100) * shipmentTransactionsLine."Gross Amount");
                        itemsObj.Replace('tax1amt', VatAmount);
                    end;
                    //<<<<< G2S 061323 
                    itemsObj.Replace('tax2per', 0);
                    itemsObj.Replace('tax2amt', 0);
                    itemsObj.Replace('tax3per', 0);
                    itemsObj.Replace('tax3amt', 0);
                    //>>>>> G2S 061323 
                    // if shipmentTransactionsLine."Net Amount" <> 0 then
                    //     itemsObj.Replace('netamt', shipmentTransactionsLine."Net Amount")
                    // else begin
                    if (shipmentTransactionsLine."VAT Base Amount" <> 0) then
                        itemsObj.Replace('netamt', (shipmentTransactionsLine."Gross Amount" + shipmentTransactionsLine."VAT Base Amount" - shipmentTransactionsLine."Discount Amount")) else
                        itemsObj.Replace('netamt', (shipmentTransactionsLine."Gross Amount" + VatAmount - shipmentTransactionsLine."Discount Amount"));
                    // end;
                    // itemsObj.Replace('netamt', (shipmentTransactionsLine."Gross Amount"));
                    //<<<<< G2S 061323 
                    detailsArr.Add(itemsObj.Clone());
                end;
            until shipmentTransactionsLine.Next() = 0;

            hdrItemsObj.Replace('details', detailsArr);
            jsonObj.Replace('header', hdrItemsObj);
            jsonArr.Add(jsonObj.Clone());
        end;
        //until shipmentTransactionsHdr.Next() = 0;
        grandObj.Add('grn', jsonArr);
        grandObj.WriteTo(content);

        url := getShipmentEndPoint();
        if (url <> '') then
            response := jsonIntegration.sendData(content, url)
        else
            Message('You have not setup the Endpoint Url yet!');

        if (response <> '') then begin
            responseJsonObj.ReadFrom(response);
            responseJsonObj.Get('grnresponse', jsonTok);
            responseArray := jsonTok.AsArray();
            //Message('Array Size: %1', responseArray.Count());
            for i := 0 to (responseArray.Count() - 1) do begin
                responseArray.Get(i, responseToken);
                responseToken.WriteTo(detailedResponse);
                detailedJSONObject.ReadFrom(detailedResponse);
                detailedJSONObject.Get('status', statusToken);
                detailedJSONObject.Get('invno', invToken);
                detailedJSONObject.Get('remark', remarkToken);
                //31 July 2023
                if (statusToken.AsValue().AsText() = '1') then begin
                    boolStatus := true;
                    TxnErrCheckStatus := false;
                end
                else begin
                    boolStatus := false;
                    TxnErrCheckStatus := true;
                end;
                //updateShipmentLog(invToken.AsValue().AsText(), boolStatus, remarkToken.AsValue().AsText(), TxnErrCheckStatus);
                //31 July 2023 resolve invalid JSON Token
                if ((not (invToken.AsValue().IsNull)) and (not (remarkToken.AsValue().IsNull))) then
                    updateShipmentLog(invToken.AsValue().AsText(), boolStatus, remarkToken.AsValue().AsText(), TxnErrCheckStatus);
                if ((invToken.AsValue().IsNull)) and (not (remarkToken.AsValue().IsNull)) then
                    updateShipmentLog('', boolStatus, remarkToken.AsValue().AsText(), TxnErrCheckStatus);
                if ((not (invToken.AsValue().IsNull)) and ((remarkToken.AsValue().IsNull))) then
                    updateShipmentLog(invToken.AsValue().AsText(), boolStatus, '', TxnErrCheckStatus);
                //end invalid JSON
            end;
        end;
        Message(content);
        exit(content);
        // end;
    end;

    /// <summary>
    /// createBatchProductJSON.
    /// </summary>
    /// <param name="ProductLog">Record "Product Transaction Log".</param>
    /// <returns>Return value of type Text.</returns>
    procedure sendBatchProductJSON(ProductLog: Record "Product Transaction Log"): Text
    var
        //productLog: Record "Product Transaction Log";
        itemObj: JsonObject;
        jsonArr: JsonArray;
        content: Text;
        jsonIntegration: Codeunit "Json Integration";
        jsonObj, responseJsonObj, detailedJSONObject : JsonObject;
        responseArray: JsonArray;
        jsonTok, responseToken, statusToken, prodToken, remarkToken : JsonToken;
        data, url, response, detailedResponse : Text;
        boolStatus: Boolean;
        i: Integer;
    begin
        itemObj.Add('prod_code', '');
        itemObj.Add('prod_custom_code', '');
        itemObj.Add('prod_name', '');
        itemObj.Add('prod_short_name', '');
        itemObj.Add('prod_cbb_volume', '');
        itemObj.Add('prod_pack_volume', '');
        itemObj.Add('product_type_code', '');
        itemObj.Add('product_type_name', '');
        itemObj.Add('prod_cat_code', '');
        itemObj.Add('prod_cat_name', '');
        itemObj.Add('brand_code', '');
        itemObj.Add('brand_name', '');
        itemObj.Add('sub_brand_code', '');
        itemObj.Add('sub_brand_name', '');
        itemObj.Add('sku_group_code', '');
        itemObj.Add('sku_group_name', '');
        itemObj.Add('prod_hsn_code', '');
        itemObj.Add('comp_code', '');
        itemObj.Add('tax1perc', '');
        itemObj.Add('tax2perc', '');
        itemObj.Add('tax3perc', '');
        // productLog.Reset();
        // productLog.SetRange();
        //productLog.SetRange(sentStatus, false);
        //if (productLog.FindFirst()) then begin
        //   repeat
        itemObj.Replace('prod_code', productLog."No.");
        itemObj.Replace('prod_custom_code', '');
        itemObj.Replace('prod_name', productLog.Description);
        itemObj.Replace('prod_short_name', productLog.prod_short_name);
        itemObj.Replace('prod_cbb_volume', productLog.prod_cbb_volume);
        itemObj.Replace('prod_pack_volume', '');
        itemObj.Replace('product_type_code', productLog.product_type_code);
        itemObj.Replace('product_type_name', productLog.product_type_name);
        itemObj.Replace('prod_cat_code', productLog.prod_cat_code);
        itemObj.Replace('prod_cat_name', productLog.prod_cat_name);
        itemObj.Replace('brand_code', productLog.brand_code);
        itemObj.Replace('brand_name', productLog.brand_name);
        itemObj.Replace('sub_brand_code', productLog.sub_brand_code);
        itemObj.Replace('sub_brand_name', productLog.sub_brand_name);
        itemObj.Replace('sku_group_code', productLog.sku_group_code);
        itemObj.Replace('sku_group_name', productLog.sku_group_name);
        itemObj.Replace('prod_hsn_code', '');
        itemObj.Replace('comp_code', CompanyName);
        itemObj.Replace('tax1perc', format(productLog.tax1perc));
        itemObj.Replace('tax2perc', '0');
        itemObj.Replace('tax3perc', '0');
        jsonArr.Add(itemObj.Clone());
        //until productLog.Next() = 0;
        jsonObj.Add('PRODUCTMASTER', jsonArr);
        jsonObj.WriteTo(content);

        url := getProductEndPoint();
        if (url <> '') then
            response := jsonIntegration.sendData(content, url)
        else
            Message('You have not setup the Endpoint Url yet!');

        if (response <> '') then begin
            responseJsonObj.ReadFrom(response);
            responseJsonObj.Get('PRODUCTMASTERRESPONSE', jsonTok);
            responseArray := jsonTok.AsArray();

            for i := 0 to (responseArray.Count() - 1) do begin
                responseArray.Get(i, responseToken);
                responseToken.WriteTo(detailedResponse);
                detailedJSONObject.ReadFrom(detailedResponse);
                detailedJSONObject.Get('status', statusToken);
                detailedJSONObject.Get('prod_code', prodToken);
                detailedJSONObject.Get('remark', remarkToken);

                if (statusToken.AsValue().AsText() = '1') then
                    boolStatus := true
                else
                    boolStatus := false;
                // Message('Content at %1: Status is: %2, Product No is: %3 and Remark is: %4', i + 1, statusToken.AsValue().AsText(), prodToken.AsValue().AsText(), remarkToken.AsValue().AsText());
                updateProductLog(prodToken.AsValue().AsText(), boolStatus, remarkToken.AsValue().AsText());
            end;
        end;
        // else
        //     Message('Response is empty');

        //Message(content);
        exit(content);
        //end;
    end;

    /// <summary>
    /// updateShipmentLog.
    /// </summary>
    /// <param name="shipmentNo">Text.</param>
    /// <param name="status">Text.</param>
    /// <param name="remark">Text.</param>
    /// <param name="TxnErrCheck">Boolean.</param>
    procedure updateShipmentLog(shipmentNo: Text; status: Boolean; remark: Text; TxnErrCheck: Boolean)
    var
        shipmentLog: Record "Shipment Transactions Hdr Log";
    begin
        shipmentLog.reset();
        shipmentLog.SetRange("No.", shipmentNo);
        if shipmentLog.FindFirst() then begin
            // shipmentLog.Init();
            shipmentLog.ModifyAll("Sent Status", status);
            shipmentLog.ModifyAll(Remark, remark);
            shipmentLog.ModifyAll("Temp. Omit Rec", TxnErrCheck);
            shipmentLog.ModifyAll("DateTime Sent to Outreach", CurrentDateTime);
            shipmentLog.ModifyAll("Date Sent to Outreach", Today());
        end;
        shipmentLog.Reset();
    end;

    /// <summary>
    /// updateProductLog.
    /// </summary>
    /// <param name="productNo">Text.</param>
    /// <param name="status">Boolean.</param>
    /// <param name="remark">Text.</param>
    procedure updateProductLog(productNo: Text; status: Boolean; remark: Text)
    var
        productLog: Record "Product Transaction Log";
    begin
        productLog.Reset();
        productLog.SetRange("No.", productNo);
        if productLog.FindFirst() then begin
            //productLog.Init();
            productLog.ModifyAll(sentStatus, status);
            productLog.ModifyAll(Remark, remark);
        end;
        productLog.Reset();
    end;

    /// <summary>
    /// getProductEndPoint.
    /// </summary>
    /// <returns>Return value of type Text.</returns>
    procedure getProductEndPoint(): Text
    var
        urlEndPoint: Record "End Point URL Setup";
    begin
        urlEndPoint.SetRange("Product End Point URL");
        if (urlEndPoint.FindFirst()) then
            exit(urlEndPoint."Product End Point URL");
    end;

    /// <summary>
    /// getShipmentEndPoint.
    /// </summary>
    /// <returns>Return value of type Text.</returns>
    procedure getShipmentEndPoint(): Text
    var
        urlEndPoint: Record "End Point URL Setup";
    begin
        urlEndPoint.SetRange("Shipment End Point URL");
        if (urlEndPoint.FindFirst()) then
            exit(urlEndPoint."Shipment End Point URL");
    end;

    local procedure DeleteSpecialChars(var InitialText: Text[100]): Code[100]
    var
        AllowedChars: Text;
        FormattedChars: Text;
        OldXtr: Label 'Old Lot Value: %1';
        NewXtr: Label 'New Lot Value: %2';
    begin
        AllowedChars := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_0123456789';
        FormattedChars := DelChr(InitialText, '=', DELCHR(InitialText, '=', AllowedChars));
        if FormattedChars <> InitialText then
            Message(OldXtr + '; ' + NewXtr, InitialText, FormattedChars);
        exit(FormattedChars);
    end;

    local procedure DeleteSpecialChars2(var InitialText: Code[100]): Code[100]
    var
        AllowedChars: Text;
        FormattedChars: Text;
        OldXtr: Label 'Old Lot Value: %1';
        NewXtr: Label 'New Lot Value: %2';
    begin
        AllowedChars := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_0123456789';
        FormattedChars := DelChr(InitialText, '=', DELCHR(InitialText, '=', AllowedChars));
        if FormattedChars <> InitialText then
            Message(OldXtr + '; ' + NewXtr, InitialText, FormattedChars);
        exit(FormattedChars);
    end;

    // [EventSubscriber(ObjectType::Table, Database::"Job Queue Entry", 'OnAfterModifyEvent', '', true, true)]
    local procedure CheckJobQueueNotification(var Rec: Record "Job Queue Entry")
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderMail, Subject : Text;
        Recipients: List of [Text];
        RecipientsCC: List of [Text];
        UserSetup: Record "User Setup";
        CustomSetup: Record "Custom Setup";
        JobQueue: Record "Job Queue Entry";
    begin
        Rec.SetRange("Object Type to Run", Rec."Object Type to Run"::Codeunit);
        Rec.SetRange("Object ID to Run", 50026);
        Rec.SetRange(Status, Rec.Status::Error);
        Rec.SetRange("Parameter String", 'CUSTOM');
        JobQueue.Copy(Rec);
        //Rec.SetFilter(Status, '%1|%2', Rec.Status::Error, Rec.Status::Finished);
        //if Rec.FindFirst() then begin
        //if not JobQueue.IsEmpty then begin
        if JobQueue.Count() <> 0 then begin
            UserSetup.Reset();
            Usersetup.SetRange("Can rec. Outreach Job Notif?", true);
            //010823 check that email is not empty
            UserSetup.setFilter("E-Mail", '<>%1', '');
            if UserSetup.findset then
                repeat
                    Recipients.Add(UserSetup."E-Mail")
            until UserSetup.next() = 0;
            //05/08/23 dist email 
            CustomSetup.Reset();
            CustomSetup.SetRange(Category, CustomSetup.Category::Outreach);
            if CustomSetup.FindFirst() then begin
                Recipients.Add(CustomSetup."Outreach Dist. Email");
            end;
            //end
            SMTPMailSetup.get();
            SenderMail := SMTPMailSetup."User ID";
            Subject := 'SALES SHIPMENT Job Queue Error Notification';
            SMTPMail.CreateMessage('CHI ERP', SenderMail, Recipients, Subject, '', TRUE);
            SMTPMail.AddCC(RecipientsCC);
            //SMTPMail.AddBCC();
            SMTPMail.AppendBody('Hi Team,');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('You are registered to receive notifications related to CHI.');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('This is a message to notify you that an error occurred while the job queue for pushing transactions to Outreach occurred. Please see error description below');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('<b>' + Rec."Error Message" + '</b>');
            SMTPMail.AppendBody('<BR><BR>');
            SMTPMail.AppendBody('Regards,');
            SMTPMail.AppendBody('<BR>');
            SMTPMail.AppendBody('CHI Limited');
            SMTPMail.Send();
        end;

    end;
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<