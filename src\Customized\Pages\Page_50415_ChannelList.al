page 50415 "Channel List"
{
    // version Channel

    DelayedInsert = true;
    //Editable = false; //b2bpksalecorr10
    PageType = List;
    SourceTable = Channels;
    SourceTableView = SORTING("Report Serial No");
    UsageCategory = Tasks;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Code"; Code)
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Qty. Wise Channels"; "Qty. Wise Channels")
                {
                    ApplicationArea = All;
                }
                field("Report Serial No"; "Report Serial No")
                {
                    ApplicationArea = All;
                }
                field("Reporting Channel"; "Reporting Channel")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Sales Team")
            {
                Caption = 'Sales Team';
                action(Action1000000012)
                {
                    Caption = 'Sales Team';
                    RunObject = Page "Channel Sales Team";
                    RunPageLink = "Channel No." = FIELD(Code);
                    ApplicationArea = All;
                }
            }
        }
    }
}
