pageextension 50101 SalesCrMemoSubformPagExt extends "Sales Cr. Memo Subform"
{
    layout
    {
        modify(Type)
        {
            trigger OnBeforeValidate()
            begin
                if Type = Type::Item then
                    Error('you can not select this option');
            end;
        }
        addafter(Description)
        {
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
        }
        addafter("Shortcut Dimension 2 Code")
        {
            field("Cust. Discount Code"; "Cust. Discount Code")
            {
                ApplicationArea = all;
                Editable = false;
            }
        }
        addbefore(Quantity)
        {
            field("Posted Loading Slip No."; "Posted Loading Slip No.")
            {
                ApplicationArea = all;
            }
            field("Gen. Prod. Posting Group"; "Gen. Prod. Posting Group")
            {
                ApplicationArea = ALL;
            }
            field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
            {
                ApplicationArea = ALL;
            }

        }
    }

}