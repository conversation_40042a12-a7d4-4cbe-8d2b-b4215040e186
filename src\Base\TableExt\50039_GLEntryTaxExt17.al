tableextension 50039 GLEntryTaxExt17 extends "G/L Entry"
{
    fields
    {
        field(50012; "Cheque No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50020; "Shortcut Dimension 4 Code"; Code[20])
        {
            CaptionClass = '1,2,4';
            Caption = 'Shortcut Dimension 4 Code';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(4),
                                                          Blocked = CONST(false));
        }
        field(50025; "Cheque Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50026; Narration; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50027; Narration1; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50029; "Description 2"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Voucher Type"; Enum VoucherType)
        {
            DataClassification = CustomerContent;
        }
        field(50033; "Payer/Collector Name"; Text[80])
        {
            DataClassification = CustomerContent;
        }
        field(50034; "Payer/Collector Address"; text[20])
        {
            DataClassification = CustomerContent;
        }
        field(50038; "Old Document No."; code[20])
        {
            DataClassification = CustomerContent;
        }

        field(50040; "Provision Entry"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'PROV1.0';
        }
        field(50041; "Provisional Entry Reversed"; Boolean)
        {
            FieldClass = FlowField;
            CalcFormula = EXIST ("G/L Entry" where("Entry No." = field("Reversal For Prov Entry No.")));
        }
        field(50042; "Fixed Asset No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Fixed Asset";
        }
        field(50043; "Branch GRN No."; Code[10])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50044; "Qty per branch"; Decimal)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50084; "Teller / Cheque No."; Code[30])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50085; "Teller / Cheque Date"; Date)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50110; "Reversal For Prov Entry No."; Integer)
        {
            DataClassification = CustomerContent;
            Description = 'PROV1.0';
        }
        field(50112; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50115; "Budget Name"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50116; "Line Account No."; Code[20])
        {
            Description = 'CTC';
        }
        field(50117; "Allocation Account Type"; Option)
        {
            Description = 'CTC';
            OptionCaption = 'Fixed Asset,G/L Account';
            OptionMembers = "Fixed Asset","G/L Account";
        }
        field(50118; "Allocation Account No."; Code[20])
        {
            Description = 'CTC';
            TableRelation = IF ("Allocation Account Type" = FILTER("G/L Account")) "G/L Account"."No."
            ELSE
            IF ("Allocation Account Type" = FILTER("Fixed Asset")) "Fixed Asset"."No.";
        }
        field(50119; "Line Account Type"; Option)
        {
            Description = 'CTC';
            OptionCaption = 'Fixed Asset,G/L Account';
            OptionMembers = "Fixed Asset","G/L Account";
        }
        field(50120; "Allocation Maintenance Code"; Code[20])
        {
            Caption = 'Maintenance Code';
            Description = 'CTC';
            TableRelation = Maintenance;
        }
        field(50121; "ShortCut Dimension code 3"; Code[20])
        {
            DataClassification = CustomerContent;
            CaptionClass = '1,2,3';
        }
        field(50122; "ShortCut Dimension code 4"; Code[20])
        {
            DataClassification = CustomerContent;
            CaptionClass = '1,2,4';
        }
        field(50123; "ShortCut Dimension code 5"; Code[20])
        {
            DataClassification = CustomerContent;
            CaptionClass = '1,2,5';
        }
        field(50124; "ShortCut Dimension code 6"; Code[20])
        {
            DataClassification = CustomerContent;
            CaptionClass = '1,2,6';
        }
        field(50125; "ShortCut Dimension code 7"; Code[20])
        {
            DataClassification = CustomerContent;
            CaptionClass = '1,2,7';
        }
        field(50126; "ShortCut Dimension code 8"; Code[20])
        {
            DataClassification = CustomerContent;
            CaptionClass = '1,2,8';
        }
        field(50127; "Source Description"; Text[250])
        {
            DataClassification = CustomerContent;
        }
        //BaluOnAug
        field(50130; "FA Posting Group"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50039; "Loan ID"; code[30]) //PKON22M6-CR220060
        {
            DataClassification = CustomerContent;
        }
    }
    var
        myInt: Integer;
}