tableextension 50185 ProdOrderCompExt extends "Prod. Order Component"
{

    fields
    {
        field(50010; "Available Qty"; Decimal)
        {
            /*CalcFormula = sum ("Item Ledger Entry".Quantity where("Item No." = field("Item No."), "Global Dimension 1 Code" = field("Shortcut Dimension 1 Code"), "Global Dimension 2 Code" = field("Shortcut Dimension 2 Code"), "Location Code" = field("Location Code"), "Variant Code" = field("Variant Code")));*/
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Item No."), "Global Dimension 1 Code" = field("Shortcut Dimension 1 Code"), "Location Code" = field("Location Code"), "Variant Code" = field("Variant Code")));
            Editable = false;
            FieldClass = FlowField;
        }

        //G2S 1ST DEC., 2023
        field(50011; "Is Substitue Item?"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        //G2S
    }

}