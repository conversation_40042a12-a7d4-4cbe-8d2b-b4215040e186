page 50468 "Fa Reconciliation Job"
{
    // version TRKIT
    Caption = 'Fa Reconsilation Job';
    PageType = Document;
    SourceTable = FA_VERIFICATION_JOB;
    UsageCategory = Administration;
    ApplicationArea = All;



    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Verification Task Number"; "Verification Task Number")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Verification Task Desc"; "Verification Task Desc")
                {
                    ApplicationArea = All;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Navision_Sync_ Time"; "Navision_Sync_ Time")
                {
                    Visible = false;
                    ApplicationArea = All;
                }
                field("Verification Task Code"; "Verification Task Code")
                {
                    Visible = false;
                    ApplicationArea = All;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("FA Location Code"; "FA Location Code")
                {
                    Visible = false;
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    MultiLine = true;
                    Visible = false;
                }
            }
            part(FAreconciliationdata; "FA Reconciliation Data")
            {
                SubPageLink = "Verification Task Number" = FIELD("Verification Task Number");
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Function")
            {
                Caption = 'Function';
                action("Get FA Verification Data")
                {
                    Caption = 'Get FA Verification Data';
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        TESTFIELD("FA Location Code");
                        IF Status = 'CLOSED' THEN
                            ERROR(Text001);
                        FAVerRec.SETRANGE(FAVerRec.VERIFICATION_TASK_NUMBER, "Verification Task Number");
                        FAVerRec.SETFILTER(FAVerRec.CURRENT_ASSET_FA_LOCATION, "FA Location Code");
                        FAVerRec.SETFILTER(FAVerRec.STATUS, 'MISPLACED');
                        if FAVerRec.FINDSET then begin
                            repeat
                                FAVERIFICATIONDATA.INIT;
                                FAVERIFICATIONDATA.TRANSFERFIELDS(FAVerRec);
                                FAVERIFICATIONDATA."Navision Process Time" := CURRENTDATETIME;
                                FAVERIFICATIONDATA."Navision Process Status" := 'SUCCESS';
                                if FAVERIFICATIONDATA.INSERT then begin
                                    FAVerRec.NAVISION_PROCESS_TIME := CURRENTDATETIME;
                                    FAVerRec.NAVISION_PROCESS_STATUS := 'SUCCESS';
                                    FAVerRec.NAVISION_PROCESSED_USER := USERID;
                                    FAVerRec.MODIFY;
                                end else begin
                                    FAVERIFICATIONDATA.MODIFY;
                                    FAVerRec.NAVISION_PROCESS_TIME := CURRENTDATETIME;
                                    FAVerRec.NAVISION_PROCESS_STATUS := 'SUCCESS';
                                    FAVerRec.NAVISION_PROCESSED_USER := USERID;
                                    FAVerRec.MODIFY;
                                end;
                            until FAVerRec.NEXT = 0;
                        end;
                        FAVERIFICATIONDATA.SETRANGE("Verification Task Number", "Verification Task Number");
                        FAVERIFICATIONDATA.SETFILTER("Tag Number", '%1', '');
                        if FAVERIFICATIONDATA.FINDFIRST then
                            FAVERIFICATIONDATA.DELETE;

                        FAVERIFICATIONJOB.SETRANGE(VERIFICATION_TASK_NUMBER, "Verification Task Number");
                        if FAVERIFICATIONJOB.FINDFIRST then
                            Status := FAVERIFICATIONJOB.STATUS;
                        MODIFY;
                    end;
                }
                action("Take Action")
                {
                    Caption = 'Take Action';
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        FAVERIFICATIONDATA.RESET;
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Verification Task Number", "Verification Task Number");
                        FAVERIFICATIONDATA.SETFILTER(FAVERIFICATIONDATA."Current Asset FA Location", "FA Location Code");
                        FAVERIFICATIONDATA.SETRANGE("Action to take", FAVERIFICATIONDATA."Action to take"::"FA Transfer");
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Action Taken", false);
                        if FAVERIFICATIONDATA.FINDSET then begin
                            FAMREC.INIT;
                            FAMREC."Document No." := NoSeriesMgt.GetNextNo('FAMOREG', TODAY, true);
                            FAMREC.INSERT;
                            repeat
                                FAMOVLINE.INIT;
                                FAMOVLINE."Document No." := FAMREC."Document No.";
                                FAMOVLINE.VALIDATE("FA No.", FAVERIFICATIONDATA."Asset Number");
                                FAMOVLINE.VALIDATE("New Accounting Location", FAVERIFICATIONDATA."Asset Found ACC Location");
                                FAMOVLINE.VALIDATE("New CC Code", FAVERIFICATIONDATA."Asset Found CC Location");
                                FAMOVLINE.VALIDATE("New FA Location", FAVERIFICATIONDATA."Asset Found Location");
                                FAMOVLINE.INSERT;
                                FAVERIFICATIONDATA."Action Taken By" := USERID;
                                FAVERIFICATIONDATA."Action Taken" := true;
                                FAVERIFICATIONDATA.MODIFY;
                            until FAVERIFICATIONDATA.NEXT = 0;
                            MESSAGE('The FA Transfer Journal %1 has been created successfully', FAMREC."Document No.");
                        end;

                        FAVERIFICATIONDATA.RESET;
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Verification Task Number", "Verification Task Number");
                        FAVERIFICATIONDATA.SETFILTER(FAVERIFICATIONDATA."Current Asset FA Location", "FA Location Code");
                        FAVERIFICATIONDATA.SETRANGE("Action to take", FAVERIFICATIONDATA."Action to take"::"Physical Movement");
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Action Taken", false);
                        if FAVERIFICATIONDATA.FINDSET then begin
                            REPORT.RUN(50331, true, false, FAVERIFICATIONDATA);
                            repeat
                                FAVERIFICATIONDATA."Action Taken By" := USERID;
                                FAVERIFICATIONDATA."Action Taken" := true;
                                FAVERIFICATIONDATA.MODIFY;
                            until FAVERIFICATIONDATA.NEXT = 0;
                        end;

                        FAVERIFICATIONDATA.RESET;
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Verification Task Number", "Verification Task Number");
                        FAVERIFICATIONDATA.SETFILTER(FAVERIFICATIONDATA."Current Asset FA Location", "FA Location Code");
                        FAVERIFICATIONDATA.SETRANGE("Action to take", FAVERIFICATIONDATA."Action to take"::"FA Disposal");
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Action Taken", false);
                        if FAVERIFICATIONDATA.FINDSET then begin
                            repeat
                                FADisposal.INIT;
                                FADisposal."No. Series" := 'FADP';
                                FADisposal."Document No." := NoSeriesMgt.GetNextNo('FADP', TODAY, true);
                                FADisposal.VALIDATE(FADisposal."FA No.", FAVERIFICATIONDATA."Asset Number");
                                if FADisposal.INSERT then;
                                FAVERIFICATIONDATA."Action Taken By" := USERID;
                                FAVERIFICATIONDATA."Action Taken" := true;
                                FAVERIFICATIONDATA.MODIFY;
                            until FAVERIFICATIONDATA.NEXT = 0;
                        end;
                    end;
                }
                action("Update FA Verification Job")
                {
                    Caption = 'Update FA Verification Job';
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        FAVERIFICATIONJOB.INIT;
                        FAVERIFICATIONJOB.TRANSFERFIELDS(Rec);
                        FAVERIFICATIONJOB.INSERT(true);
                    end;
                }
                action("FA Verification Report")
                {
                    Caption = 'FA Verification Report';
                    Visible = false;
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        FAVERIFICATIONDATA.SETRANGE(FAVERIFICATIONDATA."Verification Task Number", "Verification Task Number");
                        REPORT.RUN(50331, true, false, FAVERIFICATIONDATA);
                    end;
                }
            }
        }
    }

    var
        FAVerRec: Record XX_TRKIT_FA_VERIFICATION_DATA;
        FAVERIFICATIONDATA: Record FA_VERIFICATION_DATA;
        FAMREC: Record "FA Movement Register";
        FAMOVLINE: Record "FA movement Line";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        FAVERIFICATIONJOB: Record XX_TRKIT_FA_VERIFICATION_JOB;
        FADisposal: Record "FA Disposal";
        Text001: Label 'This job is Closed, hence you cannot get verification data.';
}

