/// <summary>
/// PageExtension ItemCardPagExt30 (ID 50062) extends Record Item Card.
/// </summary>
pageextension 50062 ItemCardPagExt30 extends "Item Card"
{
    layout
    {
        addafter("Purchasing Code")
        {
            field(PurchaseType; "Purchase Type")
            {
                ApplicationArea = all;
            }
            //Baluon17Jan2022>>
            field(Brand; Brand)
            {
                ApplicationArea = all;
            }
            field("Master Brand"; "Master Brand")
            {
                ApplicationArea = all;
            }
            //Baluon17Jan2022<<
            //>>>>>> 7th Sept., 2023  G2S 
            field("License Plate Enabled?"; Rec."License Plate Enabled?")
            {
                ApplicationArea = All;
            }
            //<<<<<<< 7th Sept., 2023 G2S
        }
        addafter(Description)
        {
            field("Description 1"; "Description 1")
            {
                ApplicationArea = all;
                //Pk-N on 14.05.2021
            }
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
            field("Conversion Unit"; "Conversion Unit")
            {
                ApplicationArea = all;
            }
            field("KO BPP Code"; "KO BPP Code")
            {
                ApplicationArea = all;
            }
            field("Customer's Transportation Rate"; "Customer's Transportation Rate")
            {
                ApplicationArea = all;
            }
            field(Barcode; Barcode)
            {
                ApplicationArea = all;
            }
            field("Clearwox Item ID"; "Clearwox Item ID")
            {
                ApplicationArea = all;
            }
            field("Allow Fraction"; "Allow Fraction")
            {
                ApplicationArea = All;
                ToolTip = 'Make item measurable in decimal in Clearwox storeapp';
            }

        }
        modify(Blocked)
        {
            Editable = false;
        }
        modify("Base Unit of Measure")
        {
            ShowMandatory = true;
        }
        modify("Item Category Code")
        {
            ShowMandatory = true;
        }


        modify("Gen. Prod. Posting Group")
        {

            trigger OnBeforeValidate()
            var
                ILE: Record "Item Ledger Entry";
            begin
                ILE.Reset();
                ILE.SetRange("Item No.", "No.");
                if ILE.FindFirst() then
                    Error('You Cannot Modify Gen. Prod. Posting Group');
            end;

        }
        modify("VAT Prod. Posting Group")
        {

            trigger OnBeforeValidate()
            var
                ILE: Record "Item Ledger Entry";
            begin
                ILE.Reset();
                ILE.SetRange("Item No.", "No.");
                if ILE.FindFirst() then
                    Error('You Cannot Modify VAT Prod. Posting Group');
            end;

        }
        modify("Inventory Posting Group")
        {
            trigger OnBeforeValidate()
            var
                ILE: Record "Item Ledger Entry";
            begin
                ILE.Reset();
                ILE.SetRange("Item No.", "No.");
                if ILE.FindFirst() then
                    Error('You Cannot Modify INV Posting Group');
            end;

        }

        addafter("Unit Cost")
        {
            /*field("Net Weight in KGS"; "Net Weight in KGS")
            {
                ApplicationArea = all;
            }
            field("Gross Weight in KGS"; "Gross Weight in KGS")
            {
                ApplicationArea = all;
            }*/
            field("Transfered FA Cost"; "Transfered FA Cost")
            {
                ApplicationArea = ALL;
            }

        }
        addafter("No.")
        {
            field("No. 2"; "No. 2")
            {
                ApplicationArea = ALL;
            }
        }
        addafter(Warehouse)
        {
            group(RebateSetup)
            {
                Caption = 'Rebate Setup';
                field("Spl. Quantity"; "Spl. Quantity")
                {
                    ApplicationArea = all;
                }
                field("Standard Quantity"; "Standard Quantity")
                {
                    ApplicationArea = all;
                }
                field("Discount Quantity Change"; "Discount Quantity Change")
                {
                    ApplicationArea = all;
                }
                field("Variant Size Multiplier"; "Variant Size Multiplier")
                {
                    ApplicationArea = all;
                }
                field("Variant Size"; "Variant Size")
                {
                    ApplicationArea = all;
                }
            }
        }
        addbefore("Costing Method")
        {
            field("Active/Obsolete"; "Active/Obsolete")
            {
                ApplicationArea = all;
            }
        }
        addafter(Blocked)
        {
            field("Item QR"; ItemQRData)
            {
                Editable = false;
            }
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
            }
            field("Per Tray"; "Per Tray")
            {
                ApplicationArea = all;
            }
            field("Litre Per Tray"; "Litre Per Tray")
            {
                ApplicationArea = all;
            }
            // >>>>>> G2S 29/11/2024
            field("Supply Chain Category"; "Supply Chain Category")
            {
                ApplicationArea = all;
            }
            field("Supply Chain Group"; "Supply Chain Group")
            {
                ApplicationArea = all;
            }
            field("Production Category"; "Production Category")
            {
                ApplicationArea = all;
            }
            // >>>>>> G2S 29/11/2024
            group("SNOP")
            {
                field("SNOP Include"; "SNOP Include")
                {

                }
                field("Minimun Stock Days"; "Minimun Stock Days")
                {

                }
                field("Maximum Stock Days"; "Maximum Stock Days")
                {
                    ApplicationArea = all;
                }
                field("SKU Unit"; "SKU Unit")
                {

                }
                field("Default Warehouse Location"; "Default Warehouse Location")
                {

                }
                field("Default Prod. Line No."; "Default Prod. Line No.")
                {

                }
                field("SNOP Lead Time"; "SNOP Lead Time")
                {

                }
                field("BOM Explosion Lead Time Calc."; "BOM Explosion Lead Time Calc.")
                {

                }
                field("Multiply Factor"; "Multiply Factor")
                {

                }

            }
        }
        //B2BBaluOn25Nov21>>
        addafter("Country/Region of Origin Code")
        {
            field("SON Required or NOT"; "SON Required or NOT")
            {
                ApplicationArea = all;
            }
            field("NAFDAC Required or NOT"; "NAFDAC Required or NOT")
            {
                ApplicationArea = all;
            }
            field("Duty %"; "Duty %")
            {
                ApplicationArea = all;
            }
            //Baluon11Jul2022>>
            field("Increase Stock at date"; "Increase Stock at date")
            {
                ApplicationArea = all;
            }
            //Baluon11Jul2022<<
        }
        //B2BBaluOn25Nov21<<
        //RFC# 2024_17 to 2024_18 >>>
        addafter(Item)
        {
            group(SKUData)
            {
                Caption = 'SKU Data';
                field("SKU Code"; "CHI SKU Code")
                {
                    ApplicationArea = all;
                }
                field("SKU Description"; "CHI SKU Description")
                {
                    ApplicationArea = all;
                }
                field("SKU Product"; "CHI SKU Product")
                {
                    ApplicationArea = all;
                }
                field("SKU Pack Size"; "CHI SKU Pack Size")
                {
                    ApplicationArea = all;
                }
                field("SKU Group"; "CHI SKU Group")
                {
                    ApplicationArea = all;
                }
                field("SKU Category"; "CHI SKU Category")
                {
                    ApplicationArea = all;
                }
            }
        }
        //project leap >>>>
        addafter("Qty. on Sales Order")
        {
            field("Qty on MRS Line"; "Qty on MRS Line")
            {
                ApplicationArea = all;
            }
        }
        //project leap <<<<
    }
    actions
    {
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            BEGIN
                CheckMandFields();
                TestField("Approval Status", "Approval Status"::Open);
            END;
        }
        modify(CancelApprovalRequest)
        {
            trigger OnBeforeAction()
            begin
                TestField("Approval Status", "Approval Status"::"Pending for Approval");
            end;
        }
        addafter(SendApprovalRequest)
        {
            action("Release")
            {
                Image = ReleaseDoc;
                ApplicationArea = all;
                trigger OnAction()
                var
                /*                     RecRef: RecordRef;
                                    SharePointInt: Codeunit "Share Point Integration"; */
                begin
                    CheckMandFields();
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, WorkflowEventHandling.RunWorkflowOnSendVendorForApprovalcode()) then
                        error('Workflow is enabled. You can not release manually.');
                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        Modify();
                    end;
                    /*                     //SharePoint>>
                                        RecRef.GETTABLE(Rec);
                                        SharePointInt.OnReleasedocumentDetails(RecRef, false);
                                        //SharePoint<< */
                end;
            }
            action("Open")
            {
                Image = Open;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF "Approval Status" = "Approval Status"::"Pending for Approval" THEN
                        ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 27);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        Modify();
                    end;
                end;
            }

            action("Block")
            {
                Image = Close;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF Blocked = false then BEGIN
                        IF Confirm('Do you want to block the Item?', True, False) then BEGIN
                            Blocked := true;
                            Modify();
                        end;
                    end;
                end;
            }
            action("UnBlock")
            {
                Image = Open;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF Blocked = true then BEGIN
                        IF Confirm('Do you want to Unblock the Item?', True, False) then BEGIN
                            Blocked := false;
                            Modify();
                        end;
                    end;
                end;
            }
            action(CreateSKUs)
            {
                ApplicationArea = All;
                Caption = 'Create SKU Cards';
                Image = CreateForm;
                ToolTip = 'Create SKU cards for all locations where the selected item has inventory balance and copy planning parameters from the item.';

                trigger OnAction()
                var
                    PMODWorkflow: Codeunit "PMOD Workflow Rqst Pg Handling";
                    Message: Text;
                begin
                    Message := PMODWorkflow.createSKUTemplate(Rec."No.");
                    Message(Message);
                end;
            }
        }
        addafter("E&xtended Texts")
        {
            action("Generate QR")
            {
                ApplicationArea = all;
                Image = GetEntries;
                trigger OnAction()
                var
                    ItemQRC: Codeunit ItemQR;
                begin
                    ItemQRC.run(rec);
                    Message('QR geberated succesfully.');
                end;
            }
        }
        addafter("Stockkeepin&g Units")
        {
            action("Transfer Book Value to FA")
            {
                image = TransferOrder;
                trigger OnAction()
                var
                    ItmFA: Record "Item Approval For Transfer FA";
                    CodeUnit3: Codeunit Codeunit3;
                BEGIN
                    IF NOT CONFIRM(Text0010, FALSE) THEN
                        EXIT;

                    ItmFA.reset;
                    ItmFA.SetRange("Item No.", "No.");
                    IF ItmFA.findfirst then
                        ItmFA.TestField(Status, ItmFA.Status::Released);

                    CodeUnit3.TransferBookValuetoAcc(Rec);

                END;
            }
        }
        //project leap >>>>>>
        addlast(Functions)
        {
            //group()
            action("Item Multiple Factors")
            {
                ApplicationArea = All;
                // ApplicationArea = Planning;
                Caption = 'Item Multiple Factors';
                Image = ItemVariant;
                RunObject = Page "Item Var Multiple Factor list.";
                RunPageLink = "Item No." = FIELD("No.");
                ToolTip = 'View or edit the item''s variants multiple factors.';

                trigger OnAction()
                begin

                end;
            }
        }
        //project leap
        addlast(navigation)
        {
            action(SalesItemRestricton)
            {
                ApplicationArea = All;
                Caption = 'Sales Restriction';
                Image = CoupledItem;
                RunObject = page CHIERP_SalesItemRestrictions;
                RunPageLink = CHIERP_ParentItemNo = field("No.");
                ToolTip = 'Open Item Sales Restriction';
            }
        }
    }

    local procedure TransferBookValuetoAcc()
    Var
        GenJournle: Record "Gen. Journal Line";
        ItmJounLne: Record "Item Journal Line";
        ItmJounLine: Record "Item Journal Line";
        PurchLne: Record "Purchase Line";
        NextLineNo: Integer;
        GenJourn: Record "Gen. Journal Line";
        DepGRec: Record "FA Depreciation Book";
        QtyPurch: Decimal;
        NextGLLineNo: Integer;
        GenPosSetup: Record "General Posting Setup";
        ItmQtyGVar: Decimal;
        ItmAmtGVar: Decimal;
        ItmLocation: COde[20];
        ItmLedEntry: Record "Item Ledger Entry";
        ItmGRec: Record "Fixed Asset";
        ItmNoGVar: code[20];
        ItemJnePost: Codeunit "Item Jnl.-Post Line";
        GenJnLne: Codeunit "Gen. Jnl.-Post Line";
        ItemError: Label 'FA Cost Value Already Transfered to Book value.';
        ItemLVar: record Item;
    BEGIN
        IF "Transfered FA Cost" = false then BEGIN
            clear(ItmNoGVar);
            TestField("Gen. Prod. Posting Group");
            ItmJounLine.reset;
            ItmJounLine.SetRange("Journal Batch Name", 'DEFAULT');
            ItmJounLine.SetRange("Journal Template Name", 'ITEM');
            IF ItmJounLine.FindLast() then
                NextLineNo := ItmJounLine."Line No."
            else
                NextLineNo := 0;
            clear(ItmAmtGVar);
            clear(ItmQtyGVar);
            clear(ItmLocation);

            DepGRec.reset;
            DepGRec.SetRange("FA No.", "No.");
            IF DepGRec.findfirst then;

            ItmLedEntry.Reset();
            ItmLedEntry.SetRange("Item No.", "No.");
            ItmLedEntry.SetRange("Entry Type", ItmLedEntry."Entry Type"::Purchase);
            IF ItmLedEntry.findfirst then BEGIN
                ItmLedEntry.CalcFields("Cost Amount (Actual)");
                ItmLocation := ItmLedEntry."Location Code";
                ItmQtyGVar := ItmLedEntry.Quantity;
                ItmAmtGVar := ItmLedEntry."Cost Amount (Actual)";
            END;

            ItmJounLne.init;
            ItmJounLne."Journal Batch Name" := 'DEFAULT';
            ItmJounLne."Journal Template Name" := 'ITEM';
            ItmJounLne."Line No." := NextLineNo + 10000;
            ItmJounLne.VALIDATE("Posting Date", WorkDate());
            ItmJounLne."Entry Type" := ItmJounLne."Entry Type"::"Negative Adjmt.";
            ItmJounLne."Document No." := "No.";
            ItmJounLne.VALIDATE("Item No.", "No.");
            ItmJounLne.VALIDATE("Location Code", ItmLocation);
            ItmJounLne.VALIDATE(Quantity, ItmQtyGVar);
            ItmJounLne.Insert();
            //CODEUNIT.Run(CODEUNIT::"Item Jnl.-Post", ItmJounLne);

            IF (ItemLVar.get(ItmJounLne."Item No.")) AND (ItemLVar."Item Tracking Code" <> '') THEN
                CreateItemTracking(ItmLedEntry, ItmJounLne);

            ItemJnePost.RunWithCheck(ItmJounLne);

            GenJourn.reset;
            GenJourn.SetRange("Journal Batch Name", 'ASSET');
            GenJourn.SetRange("Journal Template Name", 'ASSETS');
            IF GenJourn.FindLast() then
                NextGLLineNo := GenJourn."Line No." + 10000
            else
                NextGLLineNo := 10000;

            QtyPurch := 1;
            GenPosSetup.reset;
            GenPosSetup.SetRange("Gen. Prod. Posting Group", "Gen. Prod. Posting Group");
            IF GenPosSetup.findfirst then;



            //while QtyPurch <= ItmQtyGVar do begin
            ItmGRec.reset;
            ItmGRec.SetRange("MRS No.", "MRS No.");
            ItmGRec.SetRange("MRS Line No.", "MRS Line No.");
            IF ItmGRec.FindSet() then
                repeat
                    GenJournle.init;
                    GenJournle."Journal Template Name" := 'ASSETS';
                    GenJournle."Journal Batch Name" := 'ASSET';
                    GenJournle."Line No." := NextGLLineNo;
                    GenJournle."Posting Date" := Today;
                    GenJournle.VALIDATE("Document No.", FORMAT(NextGLLineNo));
                    //GenJournle."Account Type" := GenJournle."Bal. Account Type"::"G/L Account";
                    //GenJournle."Account No." := GenPosSetup."Inventory Adjmt. Account";

                    GenJournle.VALIDATE("Account Type", GenJournle."Bal. Account Type"::"Fixed Asset");
                    GenJournle.Validate("Account No.", ItmGRec."No.");
                    //GenJournle."Depreciation Book Code" := '';
                    GenJournle.VALIDATE("Depreciation Book Code", DepGRec."Depreciation Book Code");
                    GenJournle."FA Posting Type" := GenJournle."FA Posting Type"::"Acquisition Cost";
                    //GenJournle."Gen. Posting Type" := GenJournle."Gen. Posting Type"::Purchase;
                    GenJournle.VALIDATE("Bal. Account Type", GenJournle."Bal. Account Type"::"G/L Account");
                    GenJournle.VALIDATE("Bal. Account No.", GenPosSetup."Inventory Adjmt. Account");
                    //GenJournle."Bal. Gen. Posting Type" := GenJournle."Bal. Gen. Posting Type"::Purchase;
                    GenJournle.VALIDATE(Quantity, 1);
                    GenJournle.VALIDATE(Amount, ItmAmtGVar / ItmQtyGVar);
                    // GenJournle.Insert();
                    QtyPurch += 1;
                    NextGLLineNo += 10000;
                    GenJnLne.RunWithCheck(GenJournle);
                until ItmGRec.next = 0;
            //END;
            //GenJnLne.RunWithCheck(GenJournle);
            //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post", GenJournle);

            Blocked := true;
            "Transfered FA Cost" := true;
        end else
            error(ItemError);

    END;

    local procedure CheckMandFields()
    begin
        TestField(Description);
        TestField("Base Unit of Measure");
        TestField("Item Category Code");
        TestField("Gen. Prod. Posting Group");
        TestField("VAT Prod. Posting Group");
        TestField("Inventory Posting Group");
        //G2S 22nd September 2023
        TestField("Expiration Calculation");
        //>>>> G2S 22nd September 2023
    end;

    /// <summary>
    /// CreateItemTracking.
    /// </summary>
    /// <param name="IleLPA">Record "Item Ledger Entry".</param>
    /// <param name="IJLLpa">Record "Item Journal Line".</param>
    procedure CreateItemTracking(IleLPA: Record "Item Ledger Entry"; IJLLpa: Record "Item Journal Line")
    var
        TrackingSpecLv: Record "Tracking Specification";
        ILELv: Record "Item Ledger Entry";
        ReservationEntry: Record "Reservation Entry";
        TsEntryNo: Integer;
    begin
        Clear(TrackingSpecLv);
        ILELv.Reset();
        ILELv.SetRange("Order No.", IleLPA."Order No.");
        ILELv.SetRange("Order Line No.", IleLPA."Order Line No.");
        ILELv.SetRange("Item No.", IleLPA."Item No.");
        if ILELv.FindSet() then
            repeat
                IF ReservationEntry.FINDlast() THEN
                    TsEntryNo := ReservationEntry."Entry No." + 1
                ELSE
                    TsEntryNo := 0;
                clear(ReservationEntry);
                ReservationEntry.INIT();
                ReservationEntry."Entry No." := TsEntryNo;
                if ILELv."Entry Type" = ILELv."Entry Type"::"Negative Adjmt." then
                    ReservationEntry.VALIDATE(Positive, true)
                else
                    ReservationEntry.VALIDATE(Positive, false);
                ReservationEntry.VALIDATE("Item No.", IJLLpa."Item No.");
                ReservationEntry.VALIDATE("Location Code", IJLLpa."Location Code");
                ReservationEntry.VALIDATE("Quantity (Base)", -1 * ILELv.Quantity);
                ReservationEntry.VALIDATE(Quantity, -1 * ILELv.Quantity);
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Prospect);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                ReservationEntry.VALIDATE("Source Type", 83);
                ReservationEntry.VALIDATE("Source Subtype", 5);
                ReservationEntry.VALIDATE("Source ID", IJLLpa."Journal Template Name");
                ReservationEntry.VALIDATE("Source Batch Name", IJLLpa."Journal Batch Name");
                ReservationEntry.VALIDATE("Source Ref. No.", IJLLpa."Line No.");
                ReservationEntry.VALIDATE("Shipment Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Variant code", IJLLpa."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ILELv."Lot No.");
                ReservationEntry."Created By" := copystr(USERID(), 1, 50);
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";
                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.VALIDATE("Appl.-to Item Entry", ILELv."Entry No.");
                ReservationEntry.INSERT();
                TsEntryNo += 1;
            until ILELv.next() = 0;
    end;

    //<<<<< G2S 22nd September 2023
    trigger OnClosePage()
    begin
        if "License Plate Enabled?" then begin
            TestField("Expiration Calculation");
            TestField("Production BOM No.");
        end;
    end;
    //>>>>> G2S 22nd September 2023

    var
        CU: Codeunit "PModifier-CU";
        myInt: Integer;
        WorkflowManagement: codeunit "Workflow Management";
        WorkflowEventHandling: Codeunit "Workflow Event Handling";
        RecordRest: Record "Restricted Record";
        Text0010: label 'Do you want to Transfer Cost to FA Book Value?';
        sa: Page "Sales Order";
}