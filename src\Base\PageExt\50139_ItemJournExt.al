pageextension 50139 ItemJourExt extends "Item Journal"
{
    layout
    {
        // Add changes to page layout here
        addafter(Quantity)
        {
            field("FA No."; "FA No.")
            {

            }
            field("Maintenance Code"; "Maintenance Code")
            {
                ApplicationArea = All;
            }
            field("FA Posting Type"; "FA Posting Type")
            {
                ApplicationArea = All;
            }
            field("Date PMS Availed"; "Date PMS Availed")
            {
                ApplicationArea = All;
            }
            field("Previous Km Reading"; "Previous Km Reading")
            {
                ApplicationArea = All;
            }
            field("Km Reading"; "Km Reading")
            {
                ApplicationArea = All;
            }
            field(BinCode; "Bin Code")
            {
                ApplicationArea = All;
            }
            field("Production Batch No."; "Production Batch No.")
            {
                ApplicationArea = all;
            }
            field("Description 2"; "Description 2")
            {
                ApplicationArea = ALL;
            }

            field("Qty on Inventory"; "Qty on Inventory")
            {
                ApplicationArea = ALL;
            }
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = ALL;
            }
            field("Capex No."; "Capex No.")
            {
                ApplicationArea = ALL;
            }
            field("Capex Line No."; "Capex Line No.")
            {
                ApplicationArea = ALL;
            }

        }
        modify("Gen. Bus. Posting Group")
        {
            Visible = true;
            Editable = false;
        }
        modify("Item No.")
        {

            trigger OnAfterValidate()
            var
                ItemJournalTemplate: Record "Item Journal Template";
            BEGIN
                ItemJournalTemplate.reset;
                ItemJournalTemplate.SetRange(Name, "Journal Template Name");
                //ItemJournalTemplate.SetRange();
                IF ItemJournalTemplate.findfirst then
                    "Gen. Bus. Posting Group" := ItemJournalTemplate."Gen. Business Posting Group"
                else
                    Clear("Gen. Bus. Posting Group");
            END;

        }


    }


    actions
    {
        addafter("&Line")
        {
            group("Request Approval")
            {
                Caption = 'Request Approval';
                //>>>>> CAS-01383-Y5V4Y9 
                //>>>> Item Journal Batch Fix G2S 130225
                group(SendApprovalRequest)
                {
                    //Caption = 'Send Approval Request';
                    // Image = SendApprovalRequest;
                    action(SendApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Journal Batch';
                        Enabled = NOT OpenApprovalEntriesOnBatchOrAnyJnlLineExist;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        begin
                            IJLSubEvents.TrySendItemJournalBatchApprovalRequest(Rec);
                            SetControlAppearance();
                        end;
                    }
                    //<<<<< CAS-01383-Y5V4Y9 
                    //<<<<< Item Journal Batch Fix G2S 130225
                    action(SendApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Selected Journal Lines';
                        Enabled = NOT OpenApprovalEntriesOnBatchOrCurrJnlLineExist;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send selected journal lines for approval.';

                        trigger OnAction()
                        var
                            ItemJournalLine: Record "Item Journal Line";
                        begin
                            GetCurrentlySelectedLines(ItemJournalLine);
                            IJLSubEvents.TrySendItemJournalLineApprovalRequests(ItemJournalLine);
                        end;
                    }
                }
                group(CancelApprovalRequest)
                {
                    Caption = 'Cancel Approval Request';
                    Image = Cancel;
                    action(CancelApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Journal Batch';
                        Enabled = OpenApprovalEntriesOnJnlBatchExist;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        begin
                            IJLSubEvents.TryCancelItemJournalBatchApprovalRequest(Rec);
                            SetControlAppearance();
                        end;
                    }
                    action(CancelApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Selected Journal Lines';
                        Enabled = OpenApprovalEntriesOnJnlLineExist;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending selected journal lines for approval.';

                        trigger OnAction()
                        var
                            ItemJournalLine: Record "Item Journal Line";
                        begin
                            GetCurrentlySelectedLines(ItemJournalLine);
                            IJLSubEvents.TryCancelItemJournalLineApprovalRequests(ItemJournalLine);
                        end;
                    }
                }
                group(Approval)
                {
                    Caption = 'Approval';
                    action(Approve)
                    {
                        ApplicationArea = All;
                        Caption = 'Approve';
                        Image = Approve;
                        Promoted = true;
                        PromotedCategory = Category7;
                        PromotedIsBig = true;
                        PromotedOnly = true;
                        ToolTip = 'Approve the requested changes.';
                        // Visible = OpenApprovalEntriesExistForCurrUser;

                        trigger OnAction()
                        var
                            ItemJournalBatch: Record "Item Journal Batch";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            IF ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name") THEN BEGIN
                                //IF NOT ApprovalsMgmt.ApproveRecordApprovalRequest(ItemJournalBatch.RECORDID) THEN Need to clear this-Prasanna
                                ApprovalsMgmt.ApproveRecordApprovalRequest(ItemJournalBatch.RecordId);
                                CurrPage.Close();
                            END;
                        end;
                    }
                    action(Reject)
                    {
                        ApplicationArea = All;
                        Caption = 'Reject';
                        Image = Reject;
                        Promoted = true;
                        PromotedCategory = Category7;
                        PromotedIsBig = true;
                        PromotedOnly = true;
                        ToolTip = 'Reject the approval request.';
                        // Visible = OpenApprovalEntriesExistForCurrUser;

                        trigger OnAction()
                        var
                            ItemJournalBatch: Record "Item Journal Batch";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            IF ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name") THEN
                                //IF NOT ApprovalsMgmt.RejectRecordApprovalRequest(ItemJournalBatch.RECORDID) THENNeed to clear this-Prasanna
                                ApprovalsMgmt.RejectRecordApprovalRequest(ItemJournalBatch.RecordId);
                        end;
                    }
                    action(Delegate)
                    {
                        ApplicationArea = All;
                        Caption = 'Delegate';
                        Image = Delegate;
                        Promoted = true;
                        PromotedCategory = Category7;
                        PromotedOnly = true;
                        ToolTip = 'Delegate the approval to a substitute approver.';
                        Visible = OpenApprovalEntriesExistForCurrUser;

                        trigger OnAction()
                        var
                            ItemJournalBatch: Record "Item Journal Batch";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name");
                            //IF NOT ApprovalsMgmt.DelegateRecordApprovalRequest(ItemJournalBatch.RECORDID) THENTHENNeed to clear this-Prasanna
                            ApprovalsMgmt.DelegateRecordApprovalRequest(RECORDID());
                            //DelegateGenJournalLineRequest(Rec); Old code for above code in 2016
                        end;
                    }
                    action(CreateReserva)
                    {
                        ApplicationArea = All;
                        Caption = 'Update Gen Bus Posting Group';

                        trigger OnAction()
                        var
                            Resr: Record "Reservation Entry";
                            IJL: Record "Item Journal Line";
                            Line: Integer;
                            ItemLvar: Record Item;
                            WindPa: Dialog;
                            ItmjouT: record "Item Journal Template";
                        begin
                            IF Not Confirm('Do you want to update Gen Bus. Posting Group from Journal Batch ?') then
                                exit;
                            WindPa.Open('Processing #1###############');
                            IJL.Reset();
                            IJL.SetRange("Journal Batch Name", "Journal Batch Name");
                            IJL.SetRange("Journal Template Name", "Journal Template Name");
                            IF IJL.FindSet() THEN
                                repeat
                                    WindPa.Open(IJL."No.");
                                    ItmjouT.Get(IJL."Journal Template Name");//B2B.p.k.on.29.05.2021
                                    IJL.Validate("Gen. Bus. Posting Group", ItmjouT."Gen. Business Posting Group");//B2B.p.k.on.29.05.2021
                                    IJL.Modify()
                                Until IJL.Next() = 0;
                            WindPa.Close();
                            Message('Completed.');
                            /* Resr.INIT;
                             Resr.SetRange("Source Batch Name", "Journal Batch Name");
                             IF Resr.FindSet() then
                                 Resr.DeleteAll();

                             clear(Resr);
                             Resr.INIT;
                             IF Resr.FindLAST() then
                                 Line := Resr."Entry No.";

                             clear(Resr);
                             IJL.Reset();
                             IJL.SetRange("Journal Batch Name", "Journal Batch Name");
                             IJL.SetRange("Journal Template Name", "Journal Template Name");
                             IF IJL.FindSet() THEN
                                 repeat
                                     WindPa.Open('Processing #1###############');
                                     ItemLvar.GET(IJL."Item No.");
                                     IF ItemLvar."Item Tracking Code" <> '' THEN begin
                                         Line += 1;
                                         WindPa.Open(ItemLvar."No.");
                                         Resr.Init();
                                         Resr."Entry No." := line;
                                         Resr."Source Type" := 83;
                                         Resr."Source ID" := 'Item';
                                         IF IJL."Entry Type" = IJL."Entry Type"::"Positive Adjmt." then begin
                                             Resr.Positive := true;
                                             Resr."Source Subtype" := 2;
                                             Resr.Quantity := IJL.Quantity;
                                             Resr."Qty. per Unit of Measure" := IJL."Qty. per Unit of Measure";
                                             Resr."Quantity (Base)" := IJL."Quantity (Base)";
                                             Resr."Qty. to Handle (Base)" := IJL."Quantity (Base)";
                                             Resr."Qty. to Invoice (Base)" := IJL."Quantity (Base)";
                                         end;
                                         IF IJL."Entry Type" = IJL."Entry Type"::"Negative Adjmt." then begin
                                             Resr.Positive := false;
                                             Resr."Source Subtype" := 3;
                                             Resr.Quantity := -1 * IJL.Quantity;
                                             Resr."Qty. per Unit of Measure" := -1 * IJL."Qty. per Unit of Measure";
                                             Resr."Quantity (Base)" := -1 * IJL."Quantity (Base)";
                                             Resr."Qty. to Handle (Base)" := -1 * IJL."Quantity (Base)";
                                             Resr."Qty. to Invoice (Base)" := -1 * IJL."Quantity (Base)";
                                         end;
                                         Resr."Item No." := IJL."Item No.";
                                         Resr."Reservation Status" := Resr."Reservation Status"::Prospect;
                                         Resr."Creation Date" := IJL."Posting Date";
                                         Resr."Expected Receipt Date" := IJL."Posting Date";
                                         Resr."Source Batch Name" := "Journal Batch Name";
                                         Resr."Location Code" := IJL."Location Code";
                                         Resr."Source Ref. No." := IJL."Line No.";

                                         Resr."Lot No." := 'OPENLOT';
                                         Resr."Item Tracking" := Resr."Item Tracking"::"Lot No.";
                                         Resr."Expiration Date" := DMY2Date(31, 10, 2021);
                                         Resr."Created By" := UserId;
                                         Resr.Insert();
                                     end;
                                 Until IJL.Next() = 0;
                             WindPa.Close();
                             Message('Completed.');*/
                        end;
                    }
                }
            }

        }
    }

    var
        IJLSubEvents: codeunit IJLSubEvents;
        OpenApprovalEntriesExistForCurrUser: Boolean;
        OpenApprovalEntriesOnJnlBatchExist: Boolean;
        OpenApprovalEntriesOnJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrAnyJnlLineExist: Boolean;

    trigger OnOpenPage()
    begin
        SetControlAppearance();
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetControlAppearance();
    end;


    Procedure SetControlAppearance()
    var
        ItemJournalBatch: Record "Item Journal Batch";
        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
    Begin
        IF ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name") THEN BEGIN
            //ShowWorkflowStatusOnBatch := CurrPage.WorkflowStatusBatch.PAGE.SetFilterOnWorkflowRecord(ItemJournalBatch.RECORDID()); Need to add List Part
            OpenApprovalEntriesExistForCurrUser := ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(ItemJournalBatch.RECORDID());
            OpenApprovalEntriesOnJnlBatchExist := ApprovalsMgmt.HasOpenApprovalEntries(ItemJournalBatch.RECORDID());
        END;
        OpenApprovalEntriesExistForCurrUser :=
          OpenApprovalEntriesExistForCurrUser OR
          ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(RECORDID());

        OpenApprovalEntriesOnJnlLineExist := ApprovalsMgmt.HasOpenApprovalEntries(RECORDID());
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist := OpenApprovalEntriesOnJnlBatchExist OR OpenApprovalEntriesOnJnlLineExist;

        OpenApprovalEntriesOnBatchOrAnyJnlLineExist :=
          OpenApprovalEntriesOnJnlBatchExist OR
                                            IJLSubEvents.HasAnyOpenItemJournalLineApprovalEntries("Journal Template Name", "Journal Batch Name");

        //ShowWorkflowStatusOnLine := CurrPage.WorkflowStatusLine.PAGE.SetFilterOnWorkflowRecord(RECORDID());; Need to add List Part
    end;

    Procedure GetCurrentlySelectedLines(VAR ItemJournalLine: Record "Item Journal Line"): Boolean
    BEGIN
        CurrPage.SETSELECTIONFILTER(ItemJournalLine);
        EXIT(ItemJournalLine.FINDSET());
    END;
}