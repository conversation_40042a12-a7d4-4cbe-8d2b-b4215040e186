tableextension 50254 GenJnlAllTabExt extends "Gen. Jnl. Allocation"
{
    fields
    {
        field(50000; "FA Account No."; Code[10])
        {
            Description = 'CTC';
            DataClassification = CustomerContent;
        }
        field(50001; Description; Text[80])
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Line Account No."; Code[20])
        {
            Description = 'CTC';
            DataClassification = CustomerContent;
        }
        field(50003; "Line Account Type"; Option)
        {
            Description = 'CTC';
            OptionCaption = 'Fixed Asset,G/L Account,IC Partner';
            OptionMembers = "Fixed Asset","G/L Account","IC Partner";
            DataClassification = CustomerContent;
        }
        field(50004; "Fixed Asset No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "JV Document No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
    }

}