tableextension 50051 BankAccLedgerEntryExt extends "Bank Account Ledger Entry"
{
    fields
    {
        field(50000; "Cheque No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Cheque Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50026; Narration; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50027; Narration1; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50028; "Description 2"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50029; "Project Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }

        field(50030; "Old_Deposit Slip No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Payer/Collector Name"; Text[20])
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Teller / Cheque No."; Code[30])
        {
            DataClassification = CustomerContent;
        }
        field(50033; "Old_Description 2"; Text[250])
        {
            DataClassification = CustomerContent;
        }
        field(50034; "Teller / Cheque Date"; Date)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50035; "Old_Description 3"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50036; "Deposit Slip No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50037; "Description 3"; text[100])
        {
            DataClassification = CustomerContent;
        }
    }

}