tableextension 50056 RespCenterTabExt extends "Responsibility Center"
{
    fields
    {
        field(50000; "Posting Cash Recpts No."; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; "Posting Bank Recpts No."; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50002; "Bank Balance A/C"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Bank Account";
        }
        field(50003; "Payable A/C"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50004; "Cash Balancee A/C"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50005; "Branch Cash"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor;
            Caption = 'Branch Cash Vendor';
        }
        field(50006; "SCD & CCD Mail Alerts"; Boolean)
        {
            DataClassification = CustomerContent;
            trigger Onvalidate()
            begin
                if "SCD & CCD Mail Alerts" THEN begin
                    TestField("SCD Mail Alert 1");
                    TestField("CCD Mail Alert 1");
                end;
            end;
        }
        field(50007; "SCD Mail Alert 1"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50008; "SCD Mail Alert 2"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50009; "SCD Mail Alert 3"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50010; "CCD Mail Alert 1"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50011; "CCD Mail Alert 2"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50012; "CCD Mail Alert 3"; text[50])
        {
            DataClassification = CustomerContent;
        }
        //Balu ********>>
        field(50027; "Transport Account"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        //Balu ********>>
        field(50028; "Item Self Lift rate"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //PKONAU18 >>
        field(50030; "Despatch Mail Alert"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Despatch Mail Alert 1"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Despatch Mail Alert 2"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50033; "Despatch Mail Alert 3"; text[50])
        {
            DataClassification = CustomerContent;
        }
        //PKONAU18 <<
    }

    var
        myInt: Integer;
}