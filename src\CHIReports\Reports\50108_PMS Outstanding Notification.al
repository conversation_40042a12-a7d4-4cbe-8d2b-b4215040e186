report 50108 "PMS Outstanding Notification"
{
    // version RKD

    DefaultLayout = RDLC;
    RDLCLayout = './CHIReports\Reports\Layout\PMSOutstandingNotification.rdl';
    Caption = 'PMS Outstanding Notification_50108';
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;

    dataset
    {
        dataitem("Original PMS Statement"; "Original PMS Statement")
        {
            DataItemTableView = SORTING("Card Name")
                                ORDER(Ascending)
                                WHERE("Posting Date" = FILTER('>=01/01/16'));
            RequestFilterFields = "Card No.";
            column(COMPANYNAME; COMPANYNAME)
            {
            }
            column(FORMAT_TODAY_0_4_; FORMAT(TODAY, 0, 4))
            {
            }
            /*column(CurrReport_PAGENO; CurrReport.PAGENO)
            {
            }*/
            column(USERID; USERID)
            {
            }
            column(EmployeeMaster__No__________EmployeeMaster__Search_Name_; EmployeeMaster."No." + '  - ' + EmployeeMaster."Search Name")
            {
            }
            column(Original_PMS_Statement__Posting_Date_; "Posting Date")
            {
            }
            column(Original_PMS_Statement__Card_No__; "Card No.")
            {
            }
            column(Original_PMS_Statement__Receipt_No__; "Receipt No.")
            {
            }
            column(Original_PMS_Statement__Registration_No__; "Registration No.")
            {
            }
            column(Original_PMS_Statement_Amount; Amount)
            {
            }
            column(Original_PMS_Statement_Quantity; Quantity)
            {
            }
            column(I; I)
            {
            }
            column(Original_PMS_Statement__Card_Name_; "Card Name")
            {
            }
            column(TODAY__Posting_Date_; TODAY - "Posting Date")
            {
            }
            column(CC; CC)
            {
            }
            column(CountCaption; CountCaptionLbl)
            {
            }
            column(Original_PMS_Statement_QuantityCaption; FIELDCAPTION(Quantity))
            {
            }
            column(Original_PMS_Statement_AmountCaption; FIELDCAPTION(Amount))
            {
            }
            column(Original_PMS_Statement__Registration_No__Caption; FIELDCAPTION("Registration No."))
            {
            }
            column(Original_PMS_Statement__Receipt_No__Caption; FIELDCAPTION("Receipt No."))
            {
            }
            column(Original_PMS_Statement__Card_No__Caption; FIELDCAPTION("Card No."))
            {
            }
            column(Original_PMS_Statement__Posting_Date_Caption; FIELDCAPTION("Posting Date"))
            {
            }
            column(Original_PMS_Statement__Card_Name_Caption; FIELDCAPTION("Card Name"))
            {
            }
            column(Outstanding_PMS_receiptsCaption; Outstanding_PMS_receiptsCaptionLbl)
            {
            }
            column(CurrReport_PAGENOCaption; CurrReport_PAGENOCaptionLbl)
            {
            }
            column(Overdue_Days__Caption; Overdue_Days__CaptionLbl)
            {
            }
            column(CCCaption; CCCaptionLbl)
            {
            }
            column(Original_PMS_Statement_Time_Purchased; "Time Purchased")
            {
            }

            trigger OnAfterGetRecord();
            begin
                CALCFIELDS("Document No.");
                IF "Document No." <> '' THEN
                    CurrReport.SKIP;


                IF SendMail2 THEN
                    SLEEP(1000);
                CompInfoRec.GET;
                CLEAR(Def);
                CLEAR(SmtpMail);
                CLEAR(SendMail);
                CLEAR(EmployeeMaster);
                IF Def.GET(5600, DELCHR("Original PMS Statement"."Card Name", '=', ' '), 'STAFFCODE') THEN
                    IF EmployeeMaster.GET(Def."Dimension Value Code") AND (EmployeeMaster."E-Mail" <> '') THEN BEGIN //B2B
                        IF SendMail2 THEN BEGIN
                            SendMail := TRUE;
                            SmtpMail.CreateMessage('System', CompInfoRec."E-Mail", EmployeeMaster."E-Mail", //B2B
                            'Outstanding Submissions of PMS Notification', '', TRUE);

                            SmtpMail.AppendBody('<html>');
                            SmtpMail.AppendBody('<body>');

                            SmtpMail.AppendBody('<th>Dear ' + EmployeeMaster."Search Name" + ',</th>');
                            SmtpMail.AppendBody('<br>');
                            SmtpMail.AppendBody('<br>');
                            SmtpMail.AppendBody('The following PMS Receipts are outstanding to submit');
                            SmtpMail.AppendBody('<br>');
                            SmtpMail.AppendBody('<br>');

                            SmtpMail.AppendBody('<table border="1", width="100%", bgcolor="#f0f8ff">');
                            SmtpMail.AppendBody('<tr>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">S/N</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Vehicle No.</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Usage Date</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Receipt No.</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Cost Center</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Card No.</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Quantity</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Amount</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Past Milage</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Current Milage</th>');
                            SmtpMail.AppendBody('<th Style="text-align :Left">Overdue Days</th>');
                            SmtpMail.AppendBody('</tr>');
                            I := 0;
                        END;
                    END;
                IF NOT SendMail2 THEN
                    I := 0;


                IF NOT SendMail2 THEN BEGIN
                    I += 1;
                    CLEAR(CC);
                    CLEAR(Def);
                    IF FixedAsset.GET(DELCHR("Card Name", '=', ' ')) THEN
                        IF FixedAsset."Global Dimension 2 Code" <> '' THEN
                            CC := FixedAsset."Global Dimension 2 Code"
                        ELSE
                            IF Def.GET(5600, DELCHR("Card Name", '=', ' '), 'CC') THEN
                                CC := Def."Dimension Value Code"
                            ELSE
                                CC := '';
                END;

                IF SendMail AND SendMail2 THEN BEGIN
                    I += 1;
                    CLEAR(CC);
                    CLEAR(Def);
                    IF FixedAsset.GET(DELCHR("Card Name", '=', ' ')) THEN
                        IF FixedAsset."Global Dimension 2 Code" <> '' THEN
                            CC := FixedAsset."Global Dimension 2 Code"
                        ELSE
                            IF Def.GET(5600, DELCHR("Card Name", '=', ' '), 'CC') THEN
                                CC := Def."Dimension Value Code"
                            ELSE
                                CC := '';
                    SmtpMail.AppendBody('<tr>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT(I) + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + DELCHR("Card Name", '=', ' ') + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT("Posting Date") + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT("Receipt No.") + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + CC + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT("Card No.") + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT(Quantity) + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT(Amount) + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT("Past Mileage") + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT("Current Mileage") + '</th>');
                    SmtpMail.AppendBody('<th Style="text-align :Left">' + FORMAT(TODAY - "Posting Date") + '</th>');
                    SmtpMail.AppendBody('</tr>');
                END;

                IF SendMail AND (I > 0) AND SendMail2 THEN BEGIN
                    SmtpMail.AppendBody('</table>');
                    SmtpMail.AppendBody('<BR>');
                    SmtpMail.AppendBody('<BR>');
                    SmtpMail.AppendBody('please submit for ontime Loading of PMS Card');
                    SmtpMail.AppendBody('<BR>');
                    SmtpMail.AppendBody('<BR>');
                    SmtpMail.AppendBody('Regards,');
                    SmtpMail.AppendBody('<BR>');
                    SmtpMail.AppendBody('Management.');
                    SmtpMail.AppendBody('</body>');
                    SmtpMail.AppendBody('</html>');
                    SmtpMail.Send;
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
            area(content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(SendMail2; SendMail2)
                    {
                        ApplicationArea = All;
                        Caption = 'Send Notification';
                    }
                }
            }
        }

        actions
        {
        }
    }

    labels
    {
    }

    var
        FixedAsset: Record "Fixed Asset";
        Def: Record "Default Dimension";
        SmtpMail: Codeunit "SMTP Mail";
        CompInfoRec: Record "Company Information";
        EmployeeMaster: Record Employee;
        I: Integer;
        SendMail: Boolean;
        SendMail2: Boolean;
        OverdueDays: Integer;
        CC: Code[10];
        CountCaptionLbl: Label 'Count';
        Outstanding_PMS_receiptsCaptionLbl: Label 'Outstanding PMS receipts';
        CurrReport_PAGENOCaptionLbl: Label 'Page';
        Overdue_Days__CaptionLbl: Label 'Overdue Days';
        CCCaptionLbl: Label 'Cost Center';
}

