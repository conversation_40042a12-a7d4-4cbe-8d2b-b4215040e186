tableextension 50071 ReturnReceiptLineExt extends "Return Receipt Line"
{
    fields
    {
        field(50014; "Btach No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Date of MAnufacture"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Batch Timing"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "QA Reason Code"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50023; "Amount Including VAT"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
    }

    var
        myInt: Integer;
}