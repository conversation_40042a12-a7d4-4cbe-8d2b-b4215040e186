tableextension 50031 PurchReceHdrExt extends "Purch. Rcpt. Header"
{
    fields
    {
        field(50001; "Purchase Type"; Enum PurchaseType)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Purch Req. Ref. No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Purch. Req Header";
            Editable = false;
        }
        field(50009; "Reason Codes"; Enum ReasonCodes)
        {
            DataClassification = CustomerContent;

        }
        field(50018; "Mat. Requisition Ref. No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50019; "LPO Ref. No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50020; "Project code 2"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            //B2B.P.K.T
        }
        field(50048; "Import File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }

        field(50049; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        //B2BMS
        field(50052; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50053; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50054; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50058; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }
        field(50065; "Approval Status"; enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
        }

    }

    var
        myInt: Integer;
}