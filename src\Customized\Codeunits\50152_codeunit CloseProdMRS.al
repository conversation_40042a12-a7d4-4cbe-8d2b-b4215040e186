codeunit 50152 CloseProdMRS
{
    trigger OnRun()
    begin
        //close Prodution MRS fully transferred
        MRSHeaderRec.SetFilter("Production Batch No.", '<>%1', '');
        MRSHeaderRec.SetRange(Closed, false);
        MRSHeaderRec.SetRange(status, 2);
        if MRSHeaderRec.FindSet() then
            repeat
                mrslinerec.SetRange("Document No.", MRSHeaderRec."MRS No.");
                mrslinerec.SetRange("Line CLosed", false);
                if mrslinerec.IsEmpty then begin
                    MRSHeaderRec.CalcFields("Transfer Orders Created", "Transfer Shipment Posted", "Transfer Receipt Posted");
                    if mrsheaderrec."Transfer Orders Created" = 0 then
                        if mrsheaderrec."Transfer Shipment Posted" = mrsheaderrec."Transfer Receipt Posted" then begin
                            MRSHeaderRec.Closed := true;
                            MRSHeaderRec.modify;
                        end;
                end;
            until MRSHeaderRec.Next() = 0;
        //close Production worksheets in which all MRS have been closed
        ProdWorkSheet.SetRange(Closed, false);
        if ProdWorkSheet.FindSet() then
            repeat
                MRSHeaderRec.reset;
                MRSHeaderRec.SetFilter("Production Batch No.", ProdWorkSheet."No.");
                MRSHeaderRec.SetRange(Closed, false);
                MRSHeaderRec.SetRange(status, 2);
                if MRSHeaderRec.IsEmpty then begin
                    ProdWorkSheet.Closed := true;
                    ProdWorkSheet.modify;
                end;
            until ProdWorkSheet.Next() = 0;

    end;

    var
        MRSHeaderRec: Record mrsheader;
        mrslinerec: record mrsline;
        ProdWorkSheet: Record "Production Work Sheet Header";

}