//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
table 50170 "Purchase Doc Modifier"
{
    Caption = 'Purchase Doc Modifier';
    DrillDownPageId = "Purchase Doc Modifier";
    LookupPageId = "Purchase Document Modifiers";
    Permissions = tabledata "Purchase Mod log" = RIMD;

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            Editable = false;
        }
        field(2; "Modifier Type"; Option)
        {
            NotBlank = true;
            OptionCaption = 'Modify,Create';
            OptionMembers = Modify,Create;
            DataClassification = ToBeClassified;

            trigger OnValidate();
            begin
                // TestStatusOpen;
            end;
        }
        field(3; "Purchase Type"; Enum PurchaseType)
        {
            // DataClassification = CustomerContent;            
            DataClassification = ToBeClassified;
            Caption = 'Purchase Type';
        }
        field(4; "Record Type"; Option)
        {
            DataClassification = ToBeClassified;
            OptionMembers = " ","Purchase Header","Purchase Line";

            trigger OnValidate();
            begin
                if "Record Type" = "Record Type"::"Purchase Header" then begin
                    Rec."Document No." := ' ';
                    Rec."Vendor No." := ' ';
                    Rec."Vendor Name" := ' ';
                    Rec.TableID := 38;
                    Rec."Cc Code" := '';
                    ClearLineData();
                end;

                if "Record Type" = "Record Type"::"Purchase Line" then begin
                    Rec."Document No." := ' ';
                    Rec."Vendor No." := ' ';
                    Rec."Vendor Name" := ' ';
                    Rec.TableID := 39;
                    Rec."Cc Code" := '';
                    ClearLineData();
                end;
            end;
        }
        field(5; "Document type"; Enum "Purchase Document Type")
        {
            DataClassification = ToBeClassified;

            trigger OnValidate()
            begin
                if Rec."Document type" <> xRec."Document type" then ClearLineData();
            end;
        }
        field(6; "Document No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Purchase Header"."No." where("Document Type" = const(Order), "Purchase Order Tracking" = filter("Completely Received" | "Partially Received" | "Not Yet Received" | "Partially Invoiced"),
             Status = filter(Released | "Pending Prepayment"));

            trigger OnValidate()
            var
                PurchaseHeader: Record "Purchase Header";
            begin
                if Rec."Document No." <> xRec."Document No." then ClearLineData();

                if "Record Type" = "Record Type"::"Purchase Header" then begin
                    VALIDATE(TableID, 38);
                end else
                    VALIDATE(TableID, 39);

                PurchaseHeader.SetRange("Document Type", Rec."Document type");
                PurchaseHeader.SetRange("No.", Rec."Document No.");
                if PurchaseHeader.FindFirst() then begin
                    if PurchaseHeader."Purchase Order Tracking" = PurchaseHeader."Purchase Order Tracking"::"Completely Invoiced" then Error('Document Completely Invoiced');
                    Rec.CalcFields("Vendor No.", "Vendor Name");
                    Rec."Document type" := PurchaseHeader."Document Type";
                    if Rec."Created By" = '' then
                        Rec.Validate("Created By", UserId) else
                        Rec.Validate("Document Edit", true);
                    "Current Document Status" := Format(PurchaseHeader.Status);
                end;
            end;
        }
        field(7; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purchase Header"."Buy-from Vendor No." where("No." = field("Document No.")));
        }
        field(8; "Vendor Name"; Text[100])
        {
            Caption = 'Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purchase Header"."Buy-from Vendor Name" where("No." = field("Document No.")));
        }
        field(9; "Created By"; Code[100])
        {
            Editable = false;
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                Rec."created on" := CurrentDateTime;
            end;
        }
        field(11; "Approved By"; Code[100])
        {
            Editable = false;

            DataClassification = ToBeClassified;
        }
        field(12; "Edited On"; DateTime)
        {
            Editable = false;

            DataClassification = ToBeClassified;
        }
        field(13; "Edited By"; Code[50])
        {
            Editable = false;

            DataClassification = ToBeClassified;

            trigger OnValidate()
            begin
                Rec."Edited On" := CurrentDateTime;
            end;
        }
        field(14; "Modified By"; Code[100])
        {
            Editable = false;

            DataClassification = ToBeClassified;
            TableRelation = User;
        }
        field(15; "No. Series"; Code[10])
        {
            Caption = 'No. Series';
            Editable = false;

            DataClassification = ToBeClassified;
            TableRelation = "No. Series";
        }
        field(16; "Document Modified"; Boolean)
        {
            Editable = false;
            DataClassification = ToBeClassified;

            trigger OnValidate()
            begin
                if "Document Modified" then Rec.Status := Rec.Status::Archived;
            end;
        }
        field(17; "Document Edit"; Boolean)
        {
            Editable = false;
            DataClassification = ToBeClassified;

            trigger OnValidate()
            begin
                Rec.Validate("Edited By", UserId);
            end;
        }
        field(18; "Master Created"; Boolean)
        {
            Editable = false;
            DataClassification = ToBeClassified;
        }
        field(19; "Date Modified"; DateTime)
        {
            Editable = false;
            DataClassification = ToBeClassified;
        }
        field(20; "created on"; DateTime)
        {
            Editable = false;

            DataClassification = ToBeClassified;
        }
        field(21; "Modifier Template Code"; Code[20])
        {
            Editable = false;
            DataClassification = ToBeClassified;
        }
        field(22; Status; Option)
        {
            Editable = false;
            OptionCaption = 'Open,Pending Approval,Released,Archived';
            OptionMembers = Open,"Pending Approval",Released,Archived;
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                if Status = Status::Released then begin
                    "Approved By" := USERID;
                end;
            end;
        }
        field(23; TableID; Integer)
        {
            Caption = 'Table ID';
        }
        field(24; "Current Document Status"; Text[20])
        {
            DataClassification = ToBeClassified;
        }
        field(25; "workflow Handler"; Text[100])
        {
            DataClassification = ToBeClassified;
        }
        field(26; "Cc Code"; Code[20])
        {
            DataClassification = ToBeClassified;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(2));

            trigger OnValidate()
            var
                ModifierLine: Record "Purchase Doc Modifier Line";
            begin
                ModifierLine.SetFilter("Modifier Code", Rec."No.");
                if ModifierLine.FindSet() then
                    repeat
                        ModifierLine.Validate("Cc Code", Rec."Cc Code");
                        ModifierLine.Modify();
                    until ModifierLine.Next() = 0;
            end;
        }
    }

    keys
    {
        key(PK; "No.")
        {
        }
        key(Key2; "Created By")
        {
        }
        key(Key3; "Approved By")
        {
        }
        key(Key4; "created on")
        {
        }
        key(Key5; "Date Modified")
        {
        }
    }

    fieldgroups
    {

    }

    trigger OnInsert()
    begin
        if "No." = '' then begin
            GetPayableSetup;
            PayableSetup.TESTFIELD("PModifier Nos.");
            "No." := NoSeriesMgt.GetNextNo(PayableSetup."PModifier Nos.", TODAY, true);
            "Document type" := "Document type"::Order;
        end;
        "Created By" := USERID;
        "Created on" := CurrentDateTime;
        "Modifier Template Code" := Rec."No.";

        if Status = Status::"Pending Approval" then
            TESTFIELD(Status, Status::Open);

        if Status = Status::Released then
            TESTFIELD(Status, Status::Open);
    end;

    procedure GetPayableSetup()
    begin
        PayableSetup.Get()
    end;

    procedure ClearLineData()
    var
        ModifierLine: Record "Purchase Doc Modifier Line";
    begin
        ModifierLine.SetFilter("Modifier Code", '=%1', Rec."No.");
        if ModifierLine.FindSet() then ModifierLine.DeleteAll(true);
    end;

    procedure ModifyPurchaseDocument(ModiefierCode: Code[20])
    var
        i: Integer;
        CurrentStatus: Integer;
        RecordNo: Code[20];
        TableName: Text[100];
        PurchaseDocedited, BooleanVar, IsOptionValid, isPendingnPrepayment : Boolean;
        indicatorFieldNo, isModifiedFieldNo, statusfieldno, nooffieldsinRecord : Integer;
        newvaluecheck: Decimal;
        DateVar: Date;
        TimeVar: Time;
        DateTimeVar: DateTime;
        DateFormulaVar: DateFormula;
        PurchaseFieldRef: FieldRef;
        CurrencyCodeFieldRef: FieldRef;
        CurrencyFactorFieldRef: FieldRef;
        OptionArray: list of [Text];
        booleanOpt: Array[4] of Text;
        UsersSetup: Record "User Setup";
        ModifierHeader: Record "Purchase Doc Modifier";
        ModifierLine: Record "Purchase Doc Modifier Line";
        PurchaseHeaderRec: Record "Purchase Header";
        MigrationMgt: Codeunit "Config. Package Management";
        PurchaseHeaderPage: Page "Purchase Order";
        Modlog: Record "Purchase Mod log";
    begin
        UsersSetup.Get(UserId);
        if not UsersSetup."Modify Purchase Document" then Error('You do not have permmission to Modify Purchase Documents');

        // ReOpen Document
        ModifierHeader.SetRange("No.", ModiefierCode);
        if ModifierHeader.FindFirst() then begin
            PurchaseHeaderRec.SetCurrentKey("No.");
            PurchaseHeaderRec.SetRange("Document Type", ModifierHeader."Document type");
            PurchaseHeaderRec.SetRange("No.", ModifierHeader."Document No.");
            if PurchaseHeaderRec.FindFirst() then
                PurchaseRecRef.GetTable(PurchaseHeaderRec);

            PurchaseHeaderRec.SetHideValidationDialog(true);
            statusfieldno := 120;
            indicatorFieldNo := 50059;
            nooffieldsinRecord := PurchaseRecRef.FieldCount;
            PurchaseFieldRef := PurchaseRecRef.Field(statusfieldno);

            CurrentStatus := GetOption(Format(PurchaseFieldRef.Value), PurchaseFieldRef.OptionCaption);
            if CurrentStatus = 3 then isPendingnPrepayment := true;
            if CurrentStatus = 2 then Error(InvalidModify);

            //Insert Modifier Log
            ModLog.Init();
            ModLog."Modifier No." := ModiefierCode;
            ModLog."Document No." := PurchaseHeaderRec."No.";
            ModLog."Document Status" := PurchaseHeaderRec.Status;
            ModLog.Adjusted := false;
            ModLog."Created Time" := CURRENTDATETIME;
            ModLog.Insert();

            PurchaseFieldRef.Validate(0);
            PurchaseFieldRef := PurchaseRecRef.Field(indicatorFieldNo);
            PurchaseFieldRef.Validate(true);
            PurchaseRecRef.Modify();
        end;

        ModifierLine.SetRange("Modifier Code", ModiefierCode);
        ModifierLine.SetFilter("Line No.", '<>%1', 0);
        if ModifierLine.FindSet() then
            repeat
            begin
                case Rec."Record Type" of
                    Rec."Record Type"::"Purchase Header":
                        begin
                            PurchaseFieldRef := PurchaseRecRef.Field(ModifierLine.FieldID);
                            RecordNo := ModifierHeader."Document No.";
                            TableName := PurchaseHeaderRec.TableName;
                        end;

                    Rec."Record Type"::"Purchase Line":
                        begin
                            PurchaseLine.SetRange("Document No.", PurchaseHeaderRec."No.");
                            PurchaseLine.SetRange("Line No.", ModifierLine."Purchase Line No.");
                            if PurchaseLine.FindFirst() then
                                PurchaseRecRef.GetTable(PurchaseLine);

                            PurchaseFieldRef := PurchaseRecRef.Field(ModifierLine.FieldID);
                            RecordNo := Format(ModifierLine."Purchase Line No.");
                            TableName := PurchaseLine.TableName;
                        end;
                end;

                if (Format(PurchaseFieldRef.Type) = 'Decimal') or (Format(PurchaseFieldRef.Type) = 'Integer') then begin
                    if IsNumeric(ModifierLine."New Value") then begin
                        Evaluate(newvaluecheck, ModifierLine."New Value");
                        ModifierLine."Decimal Field" := newvaluecheck;
                    end else
                        Error('Invalid Decimal or Integer Value');

                    PurchaseFieldRef.VALIDATE(newvaluecheck);
                end;

                if (Format(PurchaseFieldRef.Type) = 'Option') then begin
                    OptionArray := PurchaseFieldRef.OPTIONCAPTION.Split(',');
                    for i := 1 to OptionArray.Count do begin
                        if ModifierLine."New Value" <> OptionArray.Get(i) then IsOptionValid := false else IsOptionValid := true;
                        if IsOptionValid then break;
                    end;
                    if not IsOptionValid then Error(InvalidText, FORMAT(PurchaseFieldRef.Type));

                    PurchaseFieldRef.VALIDATE(GetOption(Format(ModifierLine."New Value"), PurchaseFieldRef.OptionCaption));
                end;

                if (Format(PurchaseFieldRef.Type) = 'Time') then begin
                    if not Evaluate(TimeVar, ModifierLine."New Value") then Error(InvalidText, FORMAT(PurchaseFieldRef.Type));
                    PurchaseFieldRef.VALIDATE(TimeVar);
                end;

                if (Format(PurchaseFieldRef.Type) = 'Date') then begin
                    if not Evaluate(DateVar, ModifierLine."New Value") then Error(InvalidText, FORMAT(PurchaseFieldRef.Type));
                    ModifierLine."Date Field" := DateVar;
                    PurchaseFieldRef.VALIDATE(DateVar);
                end;

                if (Format(PurchaseFieldRef.Type) = 'Boolean') then begin
                    if not Evaluate(BooleanVar, ModifierLine."New Value") then Error(InvalidText, FORMAT(PurchaseFieldRef.Type), ', Kindly use "YES" or "NO" as the Value');
                    ModifierLine."Boolean Field" := true;
                    PurchaseFieldRef.VALIDATE(BooleanVar);
                end;

                if FORMAT(PurchaseFieldRef.Type) = 'DateTime' then begin
                    if not Evaluate(DateTimeVar, ModifierLine."New Value") then Error(InvalidText, FORMAT(PurchaseFieldRef.Type));
                    PurchaseFieldRef.Validate(DateTimeVar);
                end;

                if FORMAT(PurchaseFieldRef.Type) = 'DateFormula' then begin
                    if not Evaluate(DateFormulaVar, ModifierLine."New Value") then Error(InvalidText, FORMAT(PurchaseFieldRef.Type));
                    PurchaseFieldRef.Validate(DateFormulaVar);
                end;

                if ((Format(PurchaseFieldRef.Type) = 'Code') or (Format(PurchaseFieldRef.Type) = 'Text')) then begin
                    if PurchaseFieldRef.Name.Contains('Currency') then begin
                        PurchaseFieldRef.Validate(ModifierLine."Currency Code");
                        PurchaseFieldRef := PurchaseRecRef.Field(33);
                        PurchaseFieldRef.Validate(ModifierLine."Currency Factor");
                    end else
                        PurchaseFieldRef.Validate(ModifierLine."New Value");
                end;

                PurchaseDocedited := true;
                PurchaseRecRef.MODIFY(true);
                COMMIT;

            end;
            until ModifierLine.Next() = 0;

        if PurchaseDocedited then begin
            PurchaseRecRef.MODIFY(true);

            // Set Status back to Release
            ModifierHeader.SetRange("No.", ModiefierCode);
            if ModifierHeader.FindFirst() then begin
                PurchaseHeaderRec.SetCurrentKey("No.");
                PurchaseHeaderRec.SetRange("Document Type", ModifierHeader."Document type");
                PurchaseHeaderRec.SetRange("No.", ModifierHeader."Document No.");
                if PurchaseHeaderRec.FindFirst() then
                    PurchaseRecRef.GetTable(PurchaseHeaderRec);
                statusfieldno := 120;
                indicatorFieldNo := 50059;
                isModifiedFieldNo := 50060;
                PurchaseFieldRef := PurchaseRecRef.Field(statusfieldno);

                if isPendingnPrepayment then begin
                    PurchaseFieldRef.Validate(3);
                end else
                    PurchaseFieldRef.Validate(1);

                PurchaseFieldRef := PurchaseRecRef.Field(indicatorFieldNo);
                PurchaseFieldRef.Validate(false);
                PurchaseFieldRef := PurchaseRecRef.Field(isModifiedFieldNo);
                PurchaseFieldRef.Validate(true);
                PurchaseRecRef.MODIFY(true);

                //Update Modifier Log
                ModLog.SetRange("Modifier No.", ModiefierCode);
                if ModLog.FindFirst() then begin
                    ModLog.Adjusted := true;
                    ModLog."Adjusted Time" := CURRENTDATETIME;
                    ModLog.Modify();
                end;
            end;

            Rec.Validate("Document Modified", true);
            Rec."Modified By" := USERID;
            Rec."Date Modified" := CURRENTDATETIME;
            MODIFY;

            MESSAGE(SuccessModify, TableName, RecordNo);
        end;

    end;

    procedure GetOption(Value: Text[250]; OptionString: Text[1024]): Integer;
    var
        Option: Text[1024];
        CommaPos: Integer;
        OptionPos: Integer;
        ValueInOptionString: Boolean;
        OptionInteger: Integer;
    begin
        if Value = '' then
            exit;

        ValueInOptionString := false;

        CommaPos := STRPOS(OptionString, ',');

        while not ValueInOptionString and (CommaPos > 0) do begin
            if STRPOS(OptionString, ',') <> 0 then
                Option := COPYSTR(OptionString, 1, STRPOS(OptionString, ',') - 1)
            else
                Option := OptionString;
            if UpperCase(Option) = UpperCase(Value) then
                ValueInOptionString := true
            else
                OptionPos := OptionPos + 1;
            OptionString := DELSTR(OptionString, 1, CommaPos);
            CommaPos := STRPOS(OptionString, ',');
        end;

        if OptionString = Value then
            ValueInOptionString := true;

        OptionInteger := OptionPos;
        exit(OptionInteger);
    end;

    local procedure IsNumeric(Value: Text): Boolean
    var
        intVar: Integer;
        decimalVar: Decimal;
    begin
        if Evaluate(intVar, Value) or Evaluate(decimalVar, Value) then exit(true) else exit(false);
    end;

    var
        InvalidText: label 'Invalid %1 %2';
        InvalidModify: label 'Cannot a Purchase order duocument in an Approval process';
        SuccessModify: label 'The %1 data (%2) was succesfully modified';
        NoSeriesMgt: Codeunit NoSeriesManagement;
        PayableSetup: Record "Purchases & Payables Setup";
        PurchaseRecRef: RecordRef;
        PurchaseLine: Record "Purchase Line";
        PurchaseHeader: Record "Purchase Header";
        DeleteDocErrorMsg: Label 'You cannot delete a Modifer Doc that has already been used to modify a Purchase Document';

    trigger OnDelete()
    begin
        Rec.TestField(Rec.Status, Status::Open);
        PurchaseHeader.Reset();
        if Rec."Document No." <> '' then begin
            PurchaseHeader.SetRange("Document Type", Rec."Document type");
            PurchaseHeader.SetRange("No.", Rec."Document No.");
            if PurchaseHeader.FindFirst() then
                if PurchaseHeader.isModifed and Rec."Document Modified" then Error(DeleteDocErrorMsg);
        end;
    end;

    local procedure InsertModifierLog()
    var
        ModLog: Record "Purchase Mod log";
    begin
        ModLog.Init();
        ModLog."Modifier No." := Rec."No.";
        ModLog."Document No." := Rec."Document No.";
        ModLog."Document Status" := Rec.Status;
        ModLog.Adjusted := false;
        ModLog."Created Time" := CURRENTDATETIME;
        ModLog.Insert();
    end;
}
