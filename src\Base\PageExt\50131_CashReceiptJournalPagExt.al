pageextension 50131 CashReceiptJournalPagExt extends "Cash Receipt Journal"
{
    layout
    {
        addbefore("Reason Code")
        {
            field("WHT Group"; Rec."WHT Group")
            {
                ApplicationArea = All;
                trigger OnValidate()
                begin
                    CalculateWHT();
                end;

            }
            field("WHT Amount"; Rec."WHT Amount")
            {
                ApplicationArea = All;
            }
            field("WHT %"; Rec."WHT %")
            {
                ApplicationArea = All;
            }
            field("WHT Account"; Rec."WHT Account")
            {
                ApplicationArea = All;
            }
            field("WHT Amount(LCY)"; Rec."WHT Amount(LCY)")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        modify(Post)
        {
            Visible = false;
        }
        modify(Preview)
        {
            Visible = false;
        }
        modify("Post and &Print")
        {
            Visible = false;
        }
        addafter(Post)
        {
            action(Post2)
            {
                ApplicationArea = all;
                Caption = 'Post';
                Image = Post;
                trigger OnAction()
                begin
                    CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post2", Rec);
                    CurrPage.UPDATE(FALSE);
                end;
            }
            action("Preview Posting")
            {
                ApplicationArea = all;
                Caption = 'Preview Posting';
                Image = ViewPostedOrder;
                trigger OnAction()
                var
                    GenJnlPost: Codeunit "Gen. Jnl.-Post2";
                begin
                    GenJnlPost.Preview(Rec);
                end;
            }
        }

    }

    var
        myInt: Integer;

}