﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="FORMAT_TODAY_0_4_">
          <DataField>FORMAT_TODAY_0_4_</DataField>
        </Field>
        <Field Name="COMPANYNAME">
          <DataField>COMPANYNAME</DataField>
        </Field>
        <Field Name="CurrReport_PAGENO">
          <DataField>CurrReport_PAGENO</DataField>
        </Field>
        <Field Name="USERID">
          <DataField>USERID</DataField>
        </Field>
        <Field Name="CustomerFilters">
          <DataField>CustomerFilters</DataField>
        </Field>
        <Field Name="AttachmentStatus">
          <DataField>AttachmentStatus</DataField>
        </Field>
        <Field Name="Customer__No__">
          <DataField>Customer__No__</DataField>
        </Field>
        <Field Name="Customer__No___Control1000000011">
          <DataField>Customer__No___Control1000000011</DataField>
        </Field>
        <Field Name="Customer_Name">
          <DataField>Customer_Name</DataField>
        </Field>
        <Field Name="Customer_Address">
          <DataField>Customer_Address</DataField>
        </Field>
        <Field Name="Customer_City">
          <DataField>Customer_City</DataField>
        </Field>
        <Field Name="NoofAttachments">
          <DataField>NoofAttachments</DataField>
        </Field>
        <Field Name="CustDiscAttachment">
          <DataField>CustDiscAttachment</DataField>
        </Field>
        <Field Name="Customer_Status">
          <DataField>Customer_Status</DataField>
        </Field>
        <Field Name="Customer__Accounting_Location_">
          <DataField>Customer__Accounting_Location_</DataField>
        </Field>
        <Field Name="Customers__Attachments_SummaryCaption">
          <DataField>Customers__Attachments_SummaryCaption</DataField>
        </Field>
        <Field Name="CurrReport_PAGENOCaption">
          <DataField>CurrReport_PAGENOCaption</DataField>
        </Field>
        <Field Name="Report_ID___50172Caption">
          <DataField>Report_ID___50172Caption</DataField>
        </Field>
        <Field Name="Customer__No___Control1000000011Caption">
          <DataField>Customer__No___Control1000000011Caption</DataField>
        </Field>
        <Field Name="Customer_NameCaption">
          <DataField>Customer_NameCaption</DataField>
        </Field>
        <Field Name="Customer_AddressCaption">
          <DataField>Customer_AddressCaption</DataField>
        </Field>
        <Field Name="Customer_CityCaption">
          <DataField>Customer_CityCaption</DataField>
        </Field>
        <Field Name="NoofAttachmentsCaption">
          <DataField>NoofAttachmentsCaption</DataField>
        </Field>
        <Field Name="CustDiscAttachmentCaption">
          <DataField>CustDiscAttachmentCaption</DataField>
        </Field>
        <Field Name="Customer_StatusCaption">
          <DataField>Customer_StatusCaption</DataField>
        </Field>
        <Field Name="Customer__Accounting_Location_Caption">
          <DataField>Customer__Accounting_Location_Caption</DataField>
        </Field>
        <Field Name="Customer__No__Caption">
          <DataField>Customer__No__Caption</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>