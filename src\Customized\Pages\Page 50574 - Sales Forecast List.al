page 50574 "Sales Forecast List"
{
    // version SALB

    Editable = false;
    PageType = List;
    SourceTable = "Sales Forecast";
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Forecast Period"; "Forecast Period")
                {
                    ApplicationArea = all;
                }
                field("Inventory Posting Grp"; "Inventory Posting Grp")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field(Location; Location)
                {
                    ApplicationArea = all;
                }
                field("Avg Sales (12M)"; "Avg Sales (12M)")
                {
                    ApplicationArea = all;
                }
                field("Avg Sales (6M)"; "Avg Sales (6M)")
                {
                    ApplicationArea = all;
                }
                field("Avg Sales (3M)"; "Avg Sales (3M)")
                {
                    ApplicationArea = all;
                }
                field("Forecast Quantity"; "Forecast Quantity")
                {
                    ApplicationArea = all;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }

    var
        ForecastPeriod: Code[10];
}

