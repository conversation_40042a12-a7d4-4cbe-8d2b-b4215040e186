pageextension 50160 SaleInvSubForm extends "Sales Invoice Subform"
{
    layout
    {
        addafter("Line No.")
        {
            field("S Order No."; "S Order No.") { ApplicationArea = all; }
            field("S Line No."; "S Line No.") { ApplicationArea = all; }
        }
        addafter("Invoice Discount Amount")
        {
            field("Fixed Discount %"; "Fixed Rebate")
            {

            }
            field("Fixed Discount"; TotalFixedDisc)
            {

            }
            field("Variable Discount"; TotalVariableDisc)
            {

            }
            field("Total after discounts"; TotalAmtAfterDisc)
            {

            }
            field("Total VAT - Discounts"; TotalAmtIncAfterDisc - TotalAmtAfterDisc)
            {

            }
        }
        addafter("Total Amount Excl. VAT")
        {

            group(Control51)
            {
                field("Total Excl. VAT - Disc."; TotalAmtIncAfterDisc)
                {

                }
            }

        }

    }

    actions
    {
        // Add changes to page actions here
    }
    trigger OnOpenPage()
    var
        SInvH: record "Sales Invoice Header";
    begin
        if SInvH.get("Document No.") then begin
            SInvH.CalcFields("Rebate Discount", "Fixed Rebate Amount", AmtAfterRebate, AmtAfterRebateIncVAT);
            TotalFixedDisc := SInvH."Fixed Rebate Amount";
            TotalVariableDisc := SInvH."Rebate Discount";
            TotalAmtAfterDisc := SInvH.AmtAfterRebate;
            TotalAmtIncAfterDisc := SInvH.AmtAfterRebateIncVAT;
        end;
    end;

    trigger OnAfterGetCurrRecord()
    var
        SInvH: record "Sales Invoice Header";
    begin
        if SInvH.get("Document No.") then begin
            SInvH.CalcFields("Rebate Discount", "Fixed Rebate Amount", AmtAfterRebate, AmtAfterRebateIncVAT);
            TotalFixedDisc := SInvH."Fixed Rebate Amount";
            TotalVariableDisc := SInvH."Rebate Discount";
            TotalAmtAfterDisc := SInvH.AmtAfterRebate;
            TotalAmtIncAfterDisc := SInvH.AmtAfterRebateIncVAT;
        end;
    end;

    trigger OnAfterGetRecord()
    var
        SInvH: record "Sales Invoice Header";
    begin
        if SInvH.get("Document No.") then begin
            SInvH.CalcFields("Rebate Discount", "Fixed Rebate Amount", AmtAfterRebate, AmtAfterRebateIncVAT);
            TotalFixedDisc := SInvH."Fixed Rebate Amount";
            TotalVariableDisc := SInvH."Rebate Discount";
            TotalAmtAfterDisc := SInvH.AmtAfterRebate;
            TotalAmtIncAfterDisc := SInvH.AmtAfterRebateIncVAT;
        end;
    end;

    var
        myInt: Integer;
        TotalVariableDisc, TotalFixedDisc, FixedDiscpct : decimal;
        TotalAmtAfterDisc, TotalAmtIncAfterDisc : decimal;

}