pageextension 50156 FinanceRolcenExt extends "Finance Manager Role Center"
{
    layout
    {

    }

    actions
    {
        modify("Bank Account Reconciliations")
        {
            Visible = false;
        }
        //BaluonMar8 2022>>
        addafter(Group)
        {
            group("Approve Pages")
            {
                action("Request to Approve")
                {
                    ApplicationArea = all;
                    Caption = 'Request to Approves';
                    RunObject = page "Requests to Approve";
                }
                action("Approval Entries View")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries View';
                    RunObject = page "Approval Entries View Page";
                }
                action("Approval Entries")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries';
                    RunObject = page "Approval Entries";
                }
            }
        }//BaluonMar8 2022<<
        addafter("Bank Account Reconciliations")
        {

            action("Bank Account Reconsilation List")
            {
                ApplicationArea = all;
                Caption = 'Bank Account reconciliation List';
                RunObject = Page "Bank Acc. Reconci List(C)";
            }

        }
        addafter("Group55")
        {
            group("Customized Pages")
            {

                action("Bank Payment Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Bank Payment Voucher List";
                }
                action("Bank Receipt Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Bank Receipt Voucher List";
                }
                action("Cash Payment Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Cash Payment Voucher List";
                }
                action("Cash Receipt Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Cash Receipt Voucher List";
                }
                action("Approved Bank Payment Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Apprved Bank Pmt Voucher List";
                }
                action("Approved Bank Receipt Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Approved Bank Rcpt Vouch. List";
                }
                action("Approved Cash Payment Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Approved Cash Pmt Vouch List";
                }
                action("Approved Cash Receipt Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = page "Cash Recpt Voucher (Main) List";
                }

                group("Bank Payment Voucher (Batch)")
                {
                    action("Bank Payment Vouch (Batch)")
                    {
                        Caption = 'Bank Payment Voucher (Batch)';
                        ApplicationArea = all;
                        RunObject = page "Bank Pmt Vouch (Batch) List";
                    }

                }
                Group(Vouchers)
                {
                    action("UserID Resp. Cent. Lines")
                    {
                        ApplicationArea = ALL;
                        Caption = 'UserID Resp. Cent. Lines';
                        RunObject = Page "UserID Resp. Cent. Lines";
                    }
                    action("General JV")
                    {
                        ApplicationArea = ALL;
                        Caption = 'General JVs';
                        RunObject = Page "Journal Voucher List";
                    }
                    action("Sales JV")
                    {
                        ApplicationArea = all;
                        Caption = 'Sales JVs';
                        RunObject = page "Sales Journal Voucher List";
                    }
                    action("Purchase JV")
                    {
                        ApplicationArea = all;
                        Caption = 'Purchase JVs';
                        RunObject = page "Purchase Journal Voucher List";
                    }
                    action("Bank JV")
                    {
                        ApplicationArea = all;
                        Caption = 'Bank JV';
                        RunObject = page "Bank Journal Voucher List";
                    }
                    group("Approved JVs")
                    {
                        action("General Jvs")
                        {
                            ApplicationArea = all;
                            Caption = 'General JVs';
                            RunObject = Page "Approved General JVs List";
                        }
                        action("Sales JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Sales JVs';
                            RunObject = Page "Approved Sales JVs List";
                        }
                        action("Purchase JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Purchase JVs';
                            RunObject = Page "Approved Purchase JVs List";
                        }
                        action("Bank JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Bank JVs';
                            RunObject = Page "Released Bank JV List";
                        }

                    }

                    group(History)
                    {
                        action("Posted General JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Posted General JVs';
                            RunObject = Page "Posted Journal Voucher List";
                        }
                        action("Posted Sales JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Posted Sales JVs';
                            RunObject = page "Posted Sales Jnl Vouch List";
                        }
                        action("Posted Purchase JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Posted Purchase JVs';
                            RunObject = page "Posted Purch Jnl Vouch List";
                        }
                        action("Posted Bank JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Posted Bank JVs';
                            RunObject = page "Posted Bank Journal Vouch List";
                        }
                        action("Posted Bank Payment JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Posted Bank Payment JVs';
                            RunObject = page "Posted Bank Payment Vouch List";
                        }
                        action("Posted Bank Receipt JVs")
                        {
                            ApplicationArea = all;
                            Caption = 'Posted Bank Receipt JVs';
                            RunObject = page "Posted Bank Receipt Vouch List";
                        }

                    }
                    group("Cash Vouchers.")
                    {
                        action("Cash Receipt Voucher.")
                        {
                            ApplicationArea = ALL;
                            Caption = 'Cash Receipt Voucher';
                            RunObject = Page "CASH PAYMENT VOUCHER LIST";
                        }
                        action("Cash Payment Voucher.")
                        {
                            ApplicationArea = ALL;
                            Caption = 'Cash Payment Voucher';
                            RunObject = Page "Journal Voucher List";
                        }
                    }
                    group("Approved Cash Vouchers.")
                    {
                        action("Cash Receipt.")
                        {
                            ApplicationArea = ALL;
                            Caption = 'Cash Receipt';
                            RunObject = Page "CASH RECPT VOUCHER (MAIN) LIST";
                        }
                        action("Cash Payment")
                        {
                            ApplicationArea = ALL;
                            Caption = 'Cash Payment';
                            RunObject = Page "CASH PMT VOUCHERS (MAIN) LIST";
                        }
                    }
                }
                group("Master Data Management")
                {
                    group("Master Template")
                    {
                        action("Create Master Data Template list")
                        {
                            ApplicationArea = all;
                            Caption = 'Create Master Data Template list';
                            RunObject = Page "Create Master Data Temp list";
                        }
                        action("Modify Master Data Template list")
                        {
                            ApplicationArea = all;
                            Caption = 'Modify Master Data Template list';
                            RunObject = Page "Master Data Modify Temp list";
                        }
                    }
                    group("Approved Master Templates")
                    {
                        action("Approved Create Master Data List")
                        {
                            ApplicationArea = all;
                            Caption = 'Approved Create Master Data List';
                            RunObject = Page "Apprd Create Master Data List";
                        }
                        action("Approved Modify Master Data List")
                        {
                            ApplicationArea = all;
                            Caption = 'Approved Modify Master Data List';
                            RunObject = Page "Apprd Modify Master Data List";
                        }
                    }
                    group("HistoryMDT")
                    {
                        Caption = 'History';
                        action("Created Master Data")
                        {
                            ApplicationArea = all;
                            Caption = 'Created Master Data';
                            RunObject = Page "Create Master Data Hist. List";
                        }
                        action("Modify Master Data")
                        {
                            ApplicationArea = all;
                            Caption = 'Modify Master Data';
                            RunObject = Page "Modify Master Data Hist. List";
                        }
                        action("MDM Temp. Create Hist.")
                        {
                            ApplicationArea = all;
                            Caption = 'MDM Temp. Create Hist.';
                            RunObject = Page "MDM Temp. Create Hist.";
                        }
                        action("MDM Temp. Modification Hist.")
                        {
                            ApplicationArea = all;
                            Caption = 'MDM Temp. Modification Hist.';
                            RunObject = Page "MDM Temp. Modification Hist.";
                        }
                    }
                }

                group("Material Requisition")
                {
                    action("Material Requisitions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions.";
                    }
                    action("Material Requisitions-Released")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions-Released";
                    }
                    action("Production MRS List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production MRS List";
                    }
                    action("Approved Production MRS")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Approved Production MRS List";
                    }
                    action("Material Requisitions-Closed")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions- Closed";
                    }
                    action("Material Req Ack")
                    {
                        Caption = 'Material Requisation Release Ack';
                        ApplicationArea = all;
                        RunObject = page "Material Requ-Rel Ack";
                    }
                }

                group("Quotation Comparision")
                {
                    action("Quotation Comparisions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Quotation Comparisions";
                    }
                }
                group("Petrol Management System")
                {
                    action("PMS Cards")
                    {
                        ApplicationArea = Planning;
                        RunObject = page PMSManagement;
                    }
                    action("PMS Vouchers")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "PMS Voucher List";
                    }
                }
                group("FA Disposals")
                {
                    action("FA Disposal")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA Disposal List";
                    }
                }
                group("FA Transfers")
                {
                    action("FA Transfer")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA Movement Register List";
                    }
                }
                group(Capex)
                {
                    action("Capex Budget List")
                    {
                        ApplicationArea = Finance;
                        RunObject = Page "Capex Budget List";
                    }
                }
                group("Bank Tellers")
                {
                    action("Request For Teller Receipt List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Request Teller Receipt List";
                    }
                    action("Teller/Cheq. Awaiting Confirm.")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Teller/Cheq. Awaiting Confirm.";
                    }
                    action("Teller/Cheque Awaiting BRV")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Teller/Cheque Awaiting BRV";
                    }
                    action("Rejected Or Returned Tellers")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Returned Tellers";
                    }

                }
                group("FA Tagging")
                {
                    action("FA Verification Job List")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA Verification Job List";
                    }
                    action("Fa Verification Job")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "Fa Verification Job";
                    }
                    action("FA Reconciliation Job List")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA Reconciliation Job List";
                    }
                    action("FA Reconciliation Data")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA Reconciliation Data";
                    }
                    action("FA Stagging Table")
                    {
                        Caption = 'FA Stagging Table';
                        RunObject = page "X_TRKITVW_FA_REGISTER";
                    }
                    action("FA REGISTER VIEW")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA REGISTER VIEW";
                    }

                    action("Fa Reconciliation Job")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "Fa Reconciliation Job";
                    }
                    action("FA Verification Data")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "FA Verification Data";
                    }
                }
                group("Teller Cheque Status")
                {
                    action("Teller/Cheq. Awaiting Confirmation")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Teller Awaiting Conf. List";
                    }
                    action("Teller/Cheq. Awaiting BRV")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Teller Awaiting BRV List";
                    }/*
                        action("Teller/Cheque Awaiting BRV")
                        {
                            ApplicationArea = Planning;
                            RunObject = page bank con te ;
                        }*/
                    action("Teller cheque Converted to BRV")
                    {
                        ApplicationArea = ALL;
                        RunObject = page "Teller/Cheque Converted to BRV";
                    }
                    action("Posted Teller Register")
                    {
                        ApplicationArea = all;
                        Caption = 'Posted Teller Register';
                        RunObject = page "Posted Original Teller Receipt";
                    }
                }
            }
            group("Customized Reports")
            {
                action("Cash Receipt")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Cash Receipt";
                }

                action("Posted Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Posted Voucher";
                }
                action("Capex Budget")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Capex Budget Report";
                }
                action("Capex Report")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Capex Report";
                }
                action("Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Voucher";
                }

                action("Bank Receipt Voicher")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Bank Receipt Voucher";
                }
                action("Posted Bank Receipt Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Posted BRV";
                }
                action("Posted Cash Receipt Voucher")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Posted CRV";
                }
                action("BPV-CPV Document")
                {
                    ApplicationArea = Finance;
                    RunObject = report "BPV-CPV Document";
                }

                action("Pre-payment Ageing Report")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Pre-payment Aging Report";
                }
                action("Debit Note Report")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Debit Note Report";
                }
                action("New Capex Requisition")
                {
                    ApplicationArea = Finance;
                    RunObject = report "New Capex Requisition";
                }
                action("Summary of Capex Report")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Summary of Capex Report";
                }
                action("Capex Cash Movement")
                {
                    ApplicationArea = Finance;
                    RunObject = report "Capex Cash Movement";
                }
                action("PMS Outstanding Notification")
                {
                    ApplicationArea = Finance;
                    RunObject = report "PMS Outstanding Notification";
                }
                action("FA Verification Report")
                {
                    ApplicationArea = Finance;
                    RunObject = report "FA Verification Report";
                }
                action("Create Salvage")
                {
                    ApplicationArea = all;
                    Caption = 'Create Salvage Batch';
                    RunObject = report "Create Salvage Batch";
                }
                action("Bank Confirmation File")
                {
                    ApplicationArea = all;
                    Caption = 'Bank Confirmation Creation';
                    RunObject = report "Bank Confirmation FileCreation";
                }
                action("Customer Item Sales New")
                {
                    ApplicationArea = all;
                    Caption = 'Customer Item Sales New';
                    RunObject = report "Customer/Item Sales New";
                }
            }
            group("PMS Group")
            {
                action("Original Pms Entries")
                {
                    Caption = 'Original PMS Entries';
                    ApplicationArea = all;
                    RunObject = page OldPMSList;
                }
                action("PMS Reconsilation")
                {
                    Caption = 'PMS Reconsilation List';
                    ApplicationArea = all;
                    RunObject = page PmsReconsilationList;
                }
                action("PMS Voucher")
                {
                    Caption = 'PMS Voucher';
                    ApplicationArea = all;
                    RunObject = page "PMS Voucher List";
                }
            }
            group("Customized Xml Ports")
            {
                action("Create HMO Batch JNL")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Create HMO Batch JNL";
                }
                action("Salary JNL(Customer)")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "salary JNL (Customer)";
                }
                action("Perpetual Inv Batch")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Perpetual Inv. Batch";
                }
                action("Upload Item Journals -Prod")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Upload Item Journals -Prod";
                }

                action("Lc Charges Upload")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "LC Charges Upload";
                }

                action("BPV")
                {
                    ApplicationArea = all;
                    RunObject = xmlport BPV;
                }

                action("Hire Purchase van SJV")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Hire Purchase Van SJV";
                }
                action("FAGL Journal Batch")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Fa/Gl journal batch";
                }
                action("Export FA Register")
                {
                    ApplicationArea = all;
                    Caption = 'Export FA Register';
                    RunObject = xmlport "FA Register";
                }
                action("GL Prov Upload")
                {
                    ApplicationArea = all;
                    Caption = 'GL Prov Upload';
                    RunObject = xmlport "GL Prov Upload";
                }
                action("GL Prov Upload2")
                {
                    ApplicationArea = all;
                    Caption = 'GL Prov Upload 2';
                    RunObject = xmlport "GL Prov Upload2";
                }
                action("Rebate Journals")
                {
                    ApplicationArea = all;
                    Caption = 'Rebate Journals';
                    RunObject = xmlport "Rebate Journal";
                }
                action("Self Lifting")
                {
                    ApplicationArea = all;
                    Caption = 'Self Lifting';
                    RunObject = xmlport "Self Lifting";
                }
                action("Update Dimensions")
                {
                    ApplicationArea = all;
                    Caption = 'Update Dimensions';
                    RunObject = xmlport "Update Dimensions";
                }
                //Baluonjan21 2022>>
                action("Export GL Text")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Export GL (Text)";
                }
                action("Create Gl Gnl Batch")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Create Gl Jnl Batch";
                }
                //Baluonjan21 2022<<
                //B2BSPON22AU24>>
                action(BPVU)
                {
                    ApplicationArea = All;
                    RunObject = xmlport BPVU;
                }
                //B2BSPON22AU24<<
            }
        }
    }
    var
        myInt: Integer;
        FileVar: File;

}