codeunit 50037 "Retail Inventory Update"
{
    Permissions = tabledata "Retail Price Change Log" = rmi,
                tabledata "Retail Stock update" = rmi,
                tabledata "Item Barcodes Unit of Measure" = rmi;

    [EventSubscriber(ObjectType::Table, Database::"Item Ledger Entry", 'OnAfterInsertEvent', '', true, true)]
    local procedure OnAfterInsertItemLedgerEntry(var Rec: Record "Item Ledger Entry")
    var
        RetailStockUpdate: Record "Retail Stock Update";
        invsetup: record "Inventory Setup";
        itemrec: record item;
        itemunit: record "Item Unit of Measure";
        valueentry: record "Value Entry";
        itemBarcode: Record "Item Barcodes Unit of Measure";
        itemrec2: Record Item;

    begin
        // // Check if the location is Retail central location
        // invsetup.Get();
        // if Rec."Location Code" = invsetup."POS Central Location" then begin
        //     // Insert a new record
        //     RetailStockUpdate.Init();
        //     RetailStockUpdate."Document No." := rec."Document No.";
        //     RetailStockUpdate."Posting Date" := rec."Posting Date";
        //     RetailStockUpdate."Expiry Date" := rec."Expiration Date";
        //     RetailStockUpdate."Line No." := rec."Document Line No.";
        //     RetailStockUpdate."Date Created" := today;
        //     RetailStockUpdate."Supplier ID" := invsetup."Retail Stock SupplierID"; //'1';
        //     RetailStockUpdate.StoreID := '1';
        //     RetailStockUpdate."Ledger Entry No." := rec."Entry No.";

        //     RetailStockUpdate.OutletID := invsetup."Retail Stock outletID"; //'249851';
        //     RetailStockUpdate."Location code" := rec."Location Code";
        //     //Rec."Item No.";
        //     itemBarcode.Reset();
        //     itemBarcode.SetRange("Item No.", Rec."Item No.");
        //     itemBarcode.SetRange("Unit Of Measure", rec."Unit of Measure Code");
        //     if itemBarcode.FindFirst() then begin
        //         RetailStockUpdate."Clearwox Item Code" := itemBarcode."Clearwox Product ID";
        //         RetailStockUpdate."Item No." := itemBarcode."Item No.";
        //         RetailStockUpdate."Product ID" := itemBarcode."Clearwox Product ID";
        //         if itemrec2.Get(itemBarcode."Item No.") then begin
        //             RetailStockUpdate."Item Description" := itemrec2.Description;
        //         end
        //     end else
        //         if itemrec.get(rec."Item No.") then begin
        //             RetailStockUpdate."Clearwox Item Code" := itemrec."Clearwox Item ID";
        //             RetailStockUpdate."Product ID" := itemrec."Clearwox Item ID"; //itemrec.barcode;
        //             RetailStockUpdate."Item No." := itemrec."No.";
        //             RetailStockUpdate."Item Description" := itemrec.Description;

        //         end;

        //     RetailStockUpdate."Batch Number" := rec."Lot No.";
        //     RetailStockUpdate."Location Code" := Rec."Location Code";
        //     RetailStockUpdate."Quantity" := Rec.Quantity;
        //     RetailStockUpdate."Unit of Measure Code" := rec."Unit of Measure Code";
        //     // Rec.CalcFields("Cost Amount (Actual)");
        //     valueentry.Reset();
        //     valueentry.SetRange("Item No.", Rec."Item No.");
        //     valueentry.SetRange("Document No.", Rec."Document No.");
        //     valueentry.SetRange("Item Ledger Entry No.", Rec."Entry No.");
        //     if valueentry.FindFirst() then begin
        //         RetailStockUpdate.price := valueentry."Cost per Unit";
        //         RetailStockUpdate."Amount Paid" := Round(valueentry."Cost Amount (Actual)", 0.01)
        //     end;
        //     // RetailStockUpdate."Amount Paid" := round((RetailStockUpdate.Price * RetailStockUpdate.Quantity), 0.01);


        //     /*  rec.CalcFields("Cost Amount (Actual)");

        //      valueentry.reset;
        //      valueentry.SetRange("Item Ledger Entry No.", rec."Entry No.");
        //      if valueentry.findfirst then
        //          RetailStockUpdate.Price := round(valueentry."Cost per Unit", 0.01); */
        //     //RetailStockUpdate.Price := round(rec."Cost Amount (Actual)" / rec.Quantity, 0.01);

        //     // ItemUnit.reset;
        //     // itemunit.SetCurrentKey("Item No.", "Qty. per Unit of Measure");
        //     // ItemUnit.SetRange("Item No.", ItemRec."No.");
        //     // ItemUnit.Setfilter("Qty. per Unit of Measure", '<%1', 1);
        //     // if ItemUnit.FindLast()
        //     // then begin
        //     //     if rec."Unit of Measure Code" <> itemunit.Code then begin
        //     //         RetailStockUpdate.Price := round(itemrec."Unit Cost" / rec."Qty. per Unit of Measure", 0.01);
        //     //         RetailStockUpdate.Quantity := RetailStockUpdate.Quantity / rec."Qty. per Unit of Measure";
        //     //         RetailStockUpdate."Lowest unit Code" := itemunit.Code;
        //     //         RetailStockUpdate."Lowest unit Price" := round((itemrec."unit cost" * ItemUnit."Qty. per Unit of Measure"), 0.01);
        //     //         RetailStockUpdate."Lowest unit Qty" := round((rec.quantity / itemunit."Qty. per Unit of Measure"), 1);
        //     //         RetailStockUpdate."Amount Paid" := round((RetailStockUpdate.Price * RetailStockUpdate.Quantity), 0.01);
        //     //     end;
        //     //     // else
        //     //     //  RetailStockUpdate."Lowest unit Price" := RetailStockUpdate.Price;
        //     //     //   RetailStockUpdate."Lowest unit code" := ItemUnit.Code;
        //     //     if rec."Unit of Measure Code" = itemunit.Code then begin
        //     //         RetailStockUpdate.Price := itemrec."Unit Cost" * rec."Qty. per Unit of Measure";
        //     //         RetailStockUpdate."Lowest unit Code" := itemunit.Code;
        //     //         RetailStockUpdate."Lowest unit Price" := RetailStockUpdate.Price;
        //     //         RetailStockUpdate."Lowest unit Qty" := round((rec.quantity / ItemUnit."Qty. per Unit of Measure"), 0.01);
        //     //         RetailStockUpdate.Quantity := round((rec.quantity / ItemUnit."Qty. per Unit of Measure"), 0.01);
        //     //         RetailStockUpdate."Amount Paid" := round((RetailStockUpdate.Price * RetailStockUpdate.Quantity), 0.01);
        //     //     end;
        //     //     // else
        //     //     //RetailStockUpdate."Lowest unit Qty" := rec.quantity;
        //     // end else begin
        //     //     ItemUnit.reset;
        //     //     itemunit.SetCurrentKey("Item No.", "Qty. per Unit of Measure");
        //     //     ItemUnit.SetRange("Item No.", ItemRec."No.");
        //     //     ItemUnit.Setfilter("Qty. per Unit of Measure", '>%1', 0);
        //     //     if ItemUnit.Findfirst() then begin
        //     //         if rec."Unit of Measure Code" <> itemunit.Code then begin
        //     //             RetailStockUpdate.Price := round(itemrec."Unit Cost" / rec."Qty. per Unit of Measure", 0.01);
        //     //             RetailStockUpdate.Quantity := RetailStockUpdate.Quantity / rec."Qty. per Unit of Measure";
        //     //             RetailStockUpdate."Lowest unit Code" := itemunit.Code;
        //     //             RetailStockUpdate."Lowest unit Price" := round((itemrec."unit cost" * ItemUnit."Qty. per Unit of Measure"), 0.01);
        //     //             RetailStockUpdate."Lowest unit Qty" := round((rec.quantity / itemunit."Qty. per Unit of Measure"), 1);
        //     //             RetailStockUpdate."Amount Paid" := round((RetailStockUpdate.Price * RetailStockUpdate.Quantity), 0.01);
        //     //         end;
        //     //         // else
        //     //         //  RetailStockUpdate."Lowest unit Price" := RetailStockUpdate.Price;
        //     //         //   RetailStockUpdate."Lowest unit code" := ItemUnit.Code;
        //     //         if rec."Unit of Measure Code" = itemunit.Code then begin
        //     //             RetailStockUpdate.Price := itemrec."Unit Cost" * rec."Qty. per Unit of Measure";
        //     //             RetailStockUpdate."Lowest unit Price" := RetailStockUpdate.Price;
        //     //             RetailStockUpdate."Lowest unit Code" := itemunit.Code;
        //     //             RetailStockUpdate."Lowest unit Qty" := round((rec.quantity / ItemUnit."Qty. per Unit of Measure"), 0.01);
        //     //             RetailStockUpdate.Quantity := round((rec.quantity / ItemUnit."Qty. per Unit of Measure"), 0.01);
        //     //             RetailStockUpdate."Amount Paid" := round((RetailStockUpdate.Price * RetailStockUpdate.Quantity), 0.01);
        //     //         end;
        //     //    end;

        //     //end;
        //     //RetailStockUpdate.price := RetailStockUpdate."Lowest unit price";
        //     // RetailStockUpdate."Unit of Measure Code" := RetailStockUpdate."Lowest unit code";
        //     // RetailStockUpdate.Quantity := RetailStockUpdate."Lowest unit Qty";
        //     RetailStockUpdate."Last Updated" := CurrentDateTime();
        //     RetailStockUpdate.Insert();

        // end;
        // //RetailStockUpdate."Amount Paid" := round((RetailStockUpdate."Lowest unit Price" * RetailStockUpdate."Lowest unit Qty"), 0.01);


    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnAfterInsertValueEntry', '', true, true)]

    // local procedure OnAfterInsertValueEntry(var ItemLedgerEntry: Record "Item Ledger Entry"; var ValueEntry: Record "Value Entry")
    // var
    //     RetailStockUpdate: Record "Retail Stock Update";
    //     invsetup: record "Inventory Setup";
    //     itemrec: record item;
    //     itemunit: record "Item Unit of Measure";
    //     itemBarcode: Record "Item Barcodes Unit of Measure";
    //     itemrec2: Record Item;

    // begin
    //     // Check if the location is Retail central location
    //     invsetup.Get();
    //     if ItemLedgerEntry."Location Code" = invsetup."POS Central Location" then begin
    //         // Insert a new record
    //         RetailStockUpdate.Init();
    //         RetailStockUpdate."Document No." := ItemLedgerEntry."Document No.";
    //         RetailStockUpdate."Posting Date" := ItemLedgerEntry."Posting Date";
    //         RetailStockUpdate."Expiry Date" := ItemLedgerEntry."Expiration Date";
    //         RetailStockUpdate."Line No." := ItemLedgerEntry."Document Line No.";
    //         RetailStockUpdate."Date Created" := today;
    //         RetailStockUpdate."Supplier ID" := invsetup."Retail Stock SupplierID"; //'1';
    //         RetailStockUpdate.StoreID := '1';
    //         RetailStockUpdate."Ledger Entry No." := ItemLedgerEntry."Entry No.";

    //         RetailStockUpdate.OutletID := invsetup."Retail Stock outletID"; //'249851';
    //         RetailStockUpdate."Location code" := ItemLedgerEntry."Location Code";
    //         //Rec."Item No.";
    //         itemBarcode.Reset();
    //         itemBarcode.SetRange("Item No.", ItemLedgerEntry."Item No.");
    //         itemBarcode.SetRange("Unit Of Measure", ItemLedgerEntry."Unit of Measure Code");
    //         if itemBarcode.FindFirst() then begin
    //             RetailStockUpdate."Clearwox Item Code" := itemBarcode."Clearwox Product ID";
    //             RetailStockUpdate."Item No." := itemBarcode."Item No.";
    //             RetailStockUpdate."Product ID" := itemBarcode."Clearwox Product ID";
    //             if itemrec2.Get(itemBarcode."Item No.") then
    //                 RetailStockUpdate."Item Description" := itemrec2.Description;

    //         end else begin
    //             itemrec.Reset();
    //             itemrec.SetRange("No.", ItemLedgerEntry."Item No.");
    //             if itemrec.FindFirst() then begin
    //                 RetailStockUpdate."Clearwox Item Code" := itemrec."Clearwox Item ID";
    //                 RetailStockUpdate."Product ID" := itemrec."Clearwox Item ID"; //itemrec.barcode;
    //                 RetailStockUpdate."Item No." := itemrec."No.";
    //                 RetailStockUpdate."Item Description" := itemrec.Description;

    //             end;
    //         end;

    //         RetailStockUpdate."Batch Number" := ItemLedgerEntry."Lot No.";
    //         RetailStockUpdate."Location Code" := ItemLedgerEntry."Location Code";
    //         RetailStockUpdate."Quantity" := ItemLedgerEntry.Quantity;
    //         RetailStockUpdate."Unit of Measure Code" := ItemLedgerEntry."Unit of Measure Code";


    //         RetailStockUpdate.price := ValueEntry."Cost per Unit";
    //         RetailStockUpdate."Amount Paid" := Round(ValueEntry."Cost Amount (Actual)", 0.01);
    //         RetailStockUpdate."Last Updated" := CurrentDateTime();
    //         RetailStockUpdate.Insert();
    //     end;


    // end;

    // end;

    [EventSubscriber(ObjectType::Table, Database::Item, 'OnBeforeModifyEvent', '', true, true)]
    local procedure OnBeforeModifyItem(var Rec: Record Item)

    begin

        // Save the old record before modification
        /*  if rec."Item Category Code" = 'FG' then begin
             OldItem := Rec;
         end; */

    end;


    [EventSubscriber(ObjectType::Table, Database::Item, 'OnAfterModifyEvent', '', true, true)]
    local procedure OnAfterModifyItem(var Rec: Record Item)
    var
        RetailPriceLog: Record "Retail Price Change Log";
        OldPrice: Decimal;

    begin
        /* if rec."Item Category Code" = 'FG' then begin
            // Retrieve the old price (using Temporary OldRecord variable)
            OldPrice := OldItem."Unit Price";

            // If the price has changed
            if (OldPrice <> Rec."Unit Price") and (oldprice <> 0) then begin
                // Log the change
                RetailPriceLog.Init();
                RetailPriceLog."Item No." := Rec."No.";
                RetailPriceLog."Item Description" := rec.Description;
                RetailPriceLog."Starting Date" := today;
                RetailPriceLog."Source Table" := rec.TableName;
                RetailPriceLog."Old Price" := OldPrice;
                RetailPriceLog."New Price" := Rec."Unit Price";
                RetailPriceLog."Unit of Measure" := rec."Base Unit of Measure";
                RetailPriceLog."Change Date" := CurrentDateTime();
                RetailPriceLog."Bar Code" := rec.BarCode;
                retailpricelog."Clearwox Item Code" := rec."Clearwox Item ID";
                RetailPriceLog.Insert();
            end;
        end; */
    end;

    procedure GetProductfromAPI(var ID: code[100]): Text
    var
        HttpClient: HttpClient;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        Content: HttpContent;
        header, Header2 : HttpHeaders;
        ResponseText: Text;
        Jsonobj: JsonObject;
        Stoken: JsonToken;
        Code: Text[20];
        chiapiset: record "CHI API Setup";
        AText, Ttext : Text;
        ChiIntegration: Codeunit "CHI Retail Integrations";

    begin

        if chiapiset.get(1) then begin
            AText := '';
            Ttext := '';
            // Define the request
            //HttpRequestMessage.SetRequestUri('https://cloud.storeapp.biz/api/products/' + ID); //'
            HttpRequestMessage.SetRequestUri(chiapiset."API Endpoint2" + '/' + ID);
            HttpRequestMessage.Method := 'GET';
            HttpRequestMessage.GetHeaders(Header);
            //Header.Add('Authorization', 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAtNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuYW1lIjoiQURNSU5JU1RSQVRPUiIsInN1YiI6ImFkbWluIiwianRpIjoiYmU1MTI1OTktOTY2MC00NTFmLTljMzEtN2IzZDc3NjI0MGI0IiwiZXhwIjoxNzYzMTEzNDIzLCJpc3MiOiJodHRwczovL3N0b3JlYXBwLmJpeiIsImF1ZCI6Imh0dHBzOi8vc3RvcmVhcHAuYml6In0.RV_J-25B9R5LNxrF-De4EGc5ohDJ5Vqx76BAHaxnE3AS7hHp-jlalQT3og_sObGIzqlFo1JLaztv8cLgJl8ApA'); // Replace {{Key}} with the actual key
            AText := ChiIntegration.GetAccessDetails(chiapiset, 'Authorize');
            Header.Add('Authorization', atext);
            HttpRequestMessage.GetHeaders(Header);
            // Header.Add('Tenant', 'demo');
            TText := ChiIntegration.GetAccessDetails(chiapiset, 'Tenant');
            Header.Add('Tenant', TText);
            //header.Add('Content-Type', 'application/json');
        End;
        // Set the content for the request
        // Content.WriteFrom(JsonPayloadtext);
        //Content.GetHeaders(Header);
        //Header.clear;
        //Header.Add('Content-Type', 'application/json');
        //HttpRequestMessage.Content := Content;

        // Send the HTTP request
        if HttpClient.Send(HttpRequestMessage, HttpResponseMessage) then begin

            //  if HttpResponseMessage.Content.GetHeaders('Content-Type', 'application/json') then
            HttpResponseMessage.Content.ReadAs(ResponseText);
            Jsonobj.ReadFrom(ResponseText);
            if Jsonobj.Get('success', Stoken) then
                IF Stoken.AsValue().AsBoolean() then begin
                    // Message('Response: %1', ResponseText);
                    exit(ResponseText);
                end;// else
                    //message('not successful');
        end else begin
            //Error('Failed to send request');
            HttpResponseMessage.Content.ReadAs(ResponseText);
            Jsonobj.ReadFrom(ResponseText);
            exit(ResponseText);
        end;
    end;


    var
        OldItem: Record Item;
}
