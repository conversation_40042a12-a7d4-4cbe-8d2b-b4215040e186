/*pageextension 50120 BankPaymentVoucherPagExt extends "Bank Payment Voucher"
{
    layout
    {
        addafter(Amount)
        {
            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Capex No."; Rec."Capex No.")
            {
                ApplicationArea = All;
            }
            field("Capex Line No."; Rec."Capex Line No.")
            {
                ApplicationArea = All;
            }
            field("Cheque No."; Rec."Cheque No.")
            {
                ApplicationArea = All;
            }
            field("WHT Group"; Rec."WHT Group")
            {
                ApplicationArea = All;
            }
            field("WHT Amount"; Rec."WHT Amount")
            {
                ApplicationArea = All;
            }
            field("WHT %"; Rec."WHT %")
            {
                ApplicationArea = All;
            }
            field("WHT Account"; Rec."WHT Account")
            {
                ApplicationArea = All;
            }
            field("WHT Amount(LCY)"; Rec."WHT Amount(LCY)")
            {
                ApplicationArea = All;
            }
            field("PMS Card No."; Rec."PMS Card No.")
            {
                ApplicationArea = All;
            }
            field("Old_PMS Card No."; Rec."Old_PMS Card No.")
            {
                ApplicationArea = All;
            }
            field(Narration1; Rec.Narration1)
            {
                ApplicationArea = All;
            }
            field(Narration; Rec.Narration)
            {
                ApplicationArea = All;
            }
            field("Last Km Reading"; Rec."Last Km Reading")
            {
                ApplicationArea = All;
            }
            field("Last Meter Reading"; Rec."Last Meter Reading")
            {
                ApplicationArea = All;
            }
            field("FA Error Entry No."; Rec."FA Error Entry No.")
            {
                ApplicationArea = All;
            }
            field("FA Posting Date"; Rec."FA Posting Date")
            {
                ApplicationArea = All;
            }
            field("Depr. until FA Posting Date"; Rec."Depr. until FA Posting Date")
            {
                ApplicationArea = All;
            }
            field("Current Km Reading"; Rec."Current Km Reading")
            {
                ApplicationArea = All;
            }
            field("Current Meter Reading"; Rec."Current Meter Reading")
            {
                ApplicationArea = All;
            }

            field("Account Id"; Rec."Account Id")
            {
                ApplicationArea = All;
            }
            field("Account No."; Rec."Account No.")
            {
                ApplicationArea = All;
            }
            field("Account Type"; Rec."Account Type")
            {
                ApplicationArea = All;
            }
        }
    }
}*/
