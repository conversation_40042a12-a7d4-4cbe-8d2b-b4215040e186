tableextension 50015 PurchRecLines extends "Purch. Rcpt. Line"
{
    fields
    {
        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Sub Document Type"; Enum SubDocumentType)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Sub Document No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Sub Document Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50063; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50070; "WHT Applicable"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50802; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50803; "Clearing No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50804; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50805; "No.2"; code[20])
        {
            DataClassification = CustomerContent;
        }
        //FIX05Jun2021>>
        field(50806; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;//B2BPK270521
        }
        field(50807; "WHT %"; Decimal)
        {
            DataClassification = CustomerContent;//B2BPK270521

        }
        field(50808; "WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'WHT Base Amount';

        }
        field(50811; "WHT Amount 2"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'WHT Amount';

        }
        //FIX05Jun2021<<
        //FIX05Jul2021>>
        field(50809; "Orginal WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;

        }
        //FIX05Jul2021<<
        //Service08Jul2021>>
        field(50071; "Service Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Service Vendor Rate"."Service Code" where("Vendor Code" = field("Buy-from Vendor No."), Released = const(true));
        }
        //Service08Jul2021<<
        field(50821; "Posted Loading Slip No."; code[20])//PKONJU19
        {
            DataClassification = CustomerContent;

        }
    }

}