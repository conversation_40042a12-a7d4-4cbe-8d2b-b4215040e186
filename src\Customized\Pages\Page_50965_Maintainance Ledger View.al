page 50965 "Maintenance Ledger Entr view" //PKONAU27 Entire Object
{
    ApplicationArea = FixedAssets;
    Caption = 'Maint Ledger Entries For KM Change';
    DataCaptionFields = "FA No.", "Depreciation Book Code";
    //Editable = false;
    PageType = List;
    SourceTable = "Maintenance Ledger Entry";
    UsageCategory = History;
    Permissions = tabledata "Maintenance Ledger Entry" = rm;

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                ShowCaption = false;
                field("FA Posting Date"; "FA Posting Date")
                {
                    Editable = false;
                    ApplicationArea = FixedAssets;
                    ToolTip = 'Specifies the posting date of the related fixed asset transaction, such as a depreciation.';
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = FixedAssets;
                    Editable = false;
                    ToolTip = 'Specifies the document type that the entry belongs to.';
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = FixedAssets;
                    Editable = false;
                    ToolTip = 'Specifies the document number on the entry.';
                }
                field("Last Meter Reading"; "Last Meter Reading")
                {
                    ApplicationArea = all;
                    Editable = false;
                }

                field("New Last KM Reading"; "New Last KM Reading")
                {
                    ApplicationArea = all;

                }
                field("Current Meter Reading"; "Current Meter Reading")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("New Current KM Reading"; "New Current KM Reading")
                {
                    ApplicationArea = all;

                }
                field("FA No."; "FA No.")
                {
                    ApplicationArea = FixedAssets;
                    Editable = false;
                    ToolTip = 'Specifies the number of the related fixed asset. ';
                }
                field("Depreciation Book Code"; "Depreciation Book Code")
                {
                    ApplicationArea = FixedAssets;
                    Editable = false;
                    ToolTip = 'Specifies the code for the depreciation book to which the line will be posted if you have selected Fixed Asset in the Type field for this line.';
                }
                field(Description; Description)
                {
                    ApplicationArea = FixedAssets;
                    Editable = false;
                    ToolTip = 'Specifies a description of the entry.';
                }
                field(Amount; Amount)
                {
                    ApplicationArea = FixedAssets;
                    Editable = false;
                    ToolTip = 'Specifies the amount of the entry.';
                }

            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
                Visible = false;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
                Visible = false;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Ent&ry")
            {
                Caption = 'Ent&ry';
                Image = Entry;

                action(Dimensions)
                {
                    AccessByPermission = TableData Dimension = R;
                    ApplicationArea = Dimensions;
                    Caption = 'Dimensions';
                    Image = Dimensions;
                    ShortCutKey = 'Alt+D';
                    ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                    trigger OnAction()
                    begin
                        ShowDimensions;
                    end;
                }
                action(SetDimensionFilter)
                {
                    ApplicationArea = Dimensions;
                    Caption = 'Set Dimension Filter';
                    Ellipsis = true;
                    Image = "Filter";
                    ToolTip = 'Limit the entries according to the dimension filters that you specify. NOTE: If you use a high number of dimension combinations, this function may not work and can result in a message that the SQL server only supports a maximum of 2100 parameters.';

                    trigger OnAction()
                    begin
                        SetFilter("Dimension Set ID", DimensionSetIDFilter.LookupFilter);
                    end;
                }
            }
        }
        area(processing)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Image = "Action";
                action(UpdateLastKmReading)
                {
                    ApplicationArea = all;
                    Image = MoveDown;
                    trigger OnAction()
                    var
                        FMLEd: Record "Maintenance Ledger Entry";
                    begin
                        FMLEd.RESET;
                        FMLEd.SETFILTER("New Last KM Reading", '<>%1', 0);
                        IF FMLEd.FindSet() then begin
                            if Confirm('Do you want to modify Last KM reading ?', true, false) then begin
                                repeat
                                    FMLEd."Last Meter Reading" := FMLEd."New Last KM Reading";
                                    FMLEd."New Last KM Reading" := 0;
                                    FMLEd.Modify();
                                until FMLEd.next = 0;
                                Message('Modified');
                            end;
                        end;
                    end;
                }
                action(UpdateCurrentKmReading)
                {
                    ApplicationArea = all;
                    Image = ChangeBatch;
                    trigger OnAction()
                    var
                        FMLEd: Record "Maintenance Ledger Entry";
                    begin
                        FMLEd.RESET;
                        FMLEd.SETFILTER("New Current KM Reading", '<>%1', 0);
                        IF FMLEd.FindSet() then begin
                            if Confirm('Do you want to modify current KM reading ?', true, false) then begin
                                repeat
                                    FMLEd."Current Meter Reading" := FMLEd."New Current KM Reading";
                                    FMLEd."New Current KM Reading" := 0;
                                    FMLEd.Modify();
                                until FMLEd.next = 0;
                                Message('Modified');
                            end;
                        end;
                    end;
                }
                action(ReverseTransaction)
                {
                    ApplicationArea = FixedAssets;
                    Caption = 'Reverse Transaction';
                    Ellipsis = true;
                    Image = ReverseRegister;
                    ToolTip = 'Undo an erroneous journal posting.';

                    trigger OnAction()
                    var
                        ReversalEntry: Record "Reversal Entry";
                    begin
                        Clear(ReversalEntry);
                        if Reversed then
                            ReversalEntry.AlreadyReversedEntry(TableCaption, "Entry No.");
                        if "Journal Batch Name" = '' then
                            ReversalEntry.TestFieldError;
                        if "Transaction No." = 0 then
                            Error(CannotUndoErr, "Entry No.", "Depreciation Book Code");
                        TestField("G/L Entry No.");
                        ReversalEntry.ReverseTransaction("Transaction No.");
                    end;
                }
            }
            action("&Navigate")
            {
                ApplicationArea = FixedAssets;
                Caption = '&Navigate';
                Image = Navigate;
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Find all entries and documents that exist for the document number and posting date on the selected entry or document.';

                trigger OnAction()
                begin
                    Navigate.SetDoc("Posting Date", "Document No.");
                    Navigate.Run;
                end;
            }
        }
    }

    var
        Navigate: Page Navigate;
        CannotUndoErr: Label 'You cannot undo the Maintenance Ledger Entry No. %1 by using the Reverse Transaction function because Depreciation Book %2 does not have the appropriate G/L integration setup.';
        DimensionSetIDFilter: Page "Dimension Set ID Filter";
}
