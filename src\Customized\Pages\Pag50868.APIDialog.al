page 50868 "API Dialog"
{
    ApplicationArea = all;
    Caption = 'API Dialog';
    SourceTable = "CHI API Setup";
    UsageCategory = Documents;
    PageType = Card;

    layout
    {
        area(Content)
        {
            group("Authorization Details")
            {
                field("Authorization"; "Authorization")
                {
                    ApplicationArea = All;
                    ToolTip = 'Enter the Authorization value.';
                    //ShowCaption = false;
                }
            }
        }
    }
    actions
    {
        area(processing)
        {
            action("OK")
            {
                Caption = 'OK';
                ToolTip = 'Confirm the input value.';
                Promoted = true;
                PromotedCategory = Process;
                ApplicationArea = All;

                trigger OnAction()
                begin
                    Rec.Validate("Authorization");
                    Rec.Modify();
                    CurrPage.Close();
                end;
            }

            action("Cancel")
            {
                Caption = 'Cancel';
                ToolTip = 'Cancel the operation.';
                Promoted = true;
                PromotedCategory = Process;
                ApplicationArea = All;

                trigger OnAction()
                begin
                    CurrPage.Close();
                end;
            }
        }
    }
}
