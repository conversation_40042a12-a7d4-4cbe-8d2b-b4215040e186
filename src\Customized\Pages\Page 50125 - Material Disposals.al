page 50125 "Material Disposals"
{
    ApplicationArea = all;
    CardPageID = "Material Disposal";
    Editable = false;
    PageType = List;
    SourceTable = "MDV Header";
    SourceTableView = SORTING("MDV No.")
                      ORDER(Ascending)
                      WHERE(Posted = CONST(false));
    UsageCategory = Lists;

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("MDV No."; "MDV No.")
                {
                    ApplicationArea = all;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                }
                field("Manual MDV. No"; "Manual MDV. No")
                {
                    ApplicationArea = all;
                }/*
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                }
                field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                }*/
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("&Card")
                {
                    Caption = '&Card';
                    ShortCutKey = 'Shift+F5';
                    Image = Card;
                    trigger OnAction();
                    begin
                        if Posted then
                            PAGE.RUN(PAGE::"Material Disposal", Rec)
                        else
                            PAGE.RUN(PAGE::"Posted Material Disposals", Rec);
                    end;
                }
            }
        }
    }
    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);
    END;
}

