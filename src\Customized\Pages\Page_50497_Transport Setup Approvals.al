page 50497 "Transport Setup Approval"//BaluonFeb 03 2022 //CR220005-6-PKON22JA7
{
    PageType = Document;
    //ApplicationArea = All;
    //UsageCategory = Administration;
    SourceTable = "Transport Setup Approvals";
    //P.K on 15.05.2021

    layout
    {
        area(Content)
        {
            group("General")
            {
                field("No."; "No.")
                {
                    ApplicationArea = All;

                }
                field("Description"; "Description")
                {
                    ApplicationArea = All;

                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;

                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;

                }
                field(Status; Status)
                {
                    ApplicationArea = All;

                }
            }
            part(CreditLimitSchedules; TransportContractTypesList)
            {
                ApplicationArea = all;
                SubPageLink = "Approv Doc No." = FIELD("No.");
            }

        }
    }

    actions
    {
        area(Processing)
        {
            action("Get Transport Contract Lines")
            {
                ApplicationArea = All;

                trigger OnAction()

                begin
                    IF "No." = '' THEN
                        error('Please select No.');
                    TestField("Start Date");
                    TestField("End Date");
                    if not Confirm('Do you want to get the Transport Settlement lines ?', True, False) then
                        exit;
                    credilimitsc.RESET;
                    credilimitsc.SetFilter(credilimitsc."Starting Date", '%1..%2', "Start Date", "End Date");
                    credilimitsc.SetRange(Status, credilimitsc.Status::Open);
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl("Approv Doc No.", "No.");
                    Message('Lines Inserted Succesfully.');
                end;
            }
            action("Get Transport Contract All Lines")
            {
                ApplicationArea = All;

                trigger OnAction()

                begin
                    IF "No." = '' THEN
                        error('Please select No.');
                    TestField("Start Date");
                    TestField("End Date");
                    if not Confirm('Do you want to get the Transport Settlement ines ?', True, False) then
                        exit;
                    credilimitsc.RESET;
                    credilimitsc.SetFilter(credilimitsc."Starting Date", '%1..%2', "Start Date", "End Date");
                    //credilimitsc.SetRange(Status, credilimitsc.Status::Open);
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl("Approv Doc No.", "No.");
                    Message('Lines Inserted Succesfully.');
                end;
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                separator(Separator1102152034)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendTSAPforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            credilimitsc.RESET;
                            credilimitsc.SetFilter("Approv Doc No.", "No.");
                            IF credilimitsc.FindSet() THEN
                                credilimitsc.ModifyALl(Status, credilimitsc.Status::Release);
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50391);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            credilimitsc.RESET;
                            credilimitsc.SetFilter("Approv Doc No.", "No.");
                            IF credilimitsc.FindSet() THEN
                                credilimitsc.ModifyALl(Status, credilimitsc.Status::Open);
                            Message('Document has been Reopened.');
                        end;
                    end;
                }

                action(Approve)
                {
                    ApplicationArea = All;
                    Image = Action;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        IF allinoneCU.CheckTSAPApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendTSAPForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelTSAPForApproval(Rec);
                    end;
                }
                action("Close")
                {
                    ApplicationArea = All;
                    Image = Closed;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        IF Status = Status::Open then BEGIN
                            Status := Status::Closed;
                            Modify();
                            credilimitsc.RESET;
                            credilimitsc.SetFilter("Approv Doc No.", "No.");
                            IF credilimitsc.FindSet() THEN
                                credilimitsc.ModifyALl(Status, credilimitsc.Status::Close);
                            Message('Document has been Closed.');
                        end else
                            error('Status must be Open')
                    end;
                }
            }


        }

    }
    trigger OnAfterGetCurrRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    var
        myInt: Integer;
        RecordRest: record "Restricted Record";
        WorkflowManagement: Codeunit "Workflow Management";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        allinoneCU: codeunit Codeunit1;
        credilimitsc: Record "Transport Contract Types";
}