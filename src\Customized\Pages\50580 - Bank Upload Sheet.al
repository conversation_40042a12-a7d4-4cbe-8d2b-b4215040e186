page 50580 "Bank Upload Sheet"
{
    // version BNKRECON
    applicationarea = ALL;
    AutoSplitKey = true;
    DelayedInsert = true;
    //Editable = false;
    ModifyAllowed = true;
    PageType = Listpart;
    SourceTable = "Original Bank Statement";
    SourceTableView = SORTING("MatchLine No")
                      WHERE("Mapped with BLE" = FILTER(false));
    UsageCategory = Lists;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                Editable = true;
                field(Select; Select)
                {
                    applicationarea = ALL;
                }
                field(Matching; Matching)
                {
                    applicationarea = ALL;
                    Caption = 'Matching';
                    Editable = false;
                    //OptionCaption = 'Bitmap51,Bitmap48,Bitmap47';
                }
                field("Matching Status"; "Matching Status")
                {
                    applicationarea = ALL;
                    Editable = false;
                    StyleExpr = StyleTxt;
                }
                field("MatchLine No"; "MatchLine No")
                {
                    applicationarea = ALL;
                    Editable = false;
                }
                field("Bank Posting Date"; "Bank Posting Date")
                {
                    applicationarea = ALL;
                    StyleExpr = StyleTxt;
                    Editable = false;
                }
                field("Bank Doc. No."; "Bank Doc. No.")
                {
                    applicationarea = ALL;
                    StyleExpr = StyleTxt;
                    Editable = false;
                }
                field("Bank Narration"; "Bank Narration")
                {
                    applicationarea = ALL;
                    Editable = false;
                    StyleExpr = StyleTxt;
                }
                field("Bank Debit Amount"; "Bank Debit Amount")
                {
                    applicationarea = ALL;
                    Editable = false;
                }
                field("Cancel No"; "Cancel No")
                {
                    applicationarea = ALL;
                    Editable = false;
                }
                field("Bank Credit Amount"; "Bank Credit Amount")
                {
                    applicationarea = ALL;
                    Editable = false;
                }
                field("Bank Deposit Slip No."; "Bank Deposit Slip No.")
                {
                    applicationarea = ALL;
                    Editable = false;
                }

            }
            group(Control16)
            {
                //Visible = false;
                ShowCaption = false;
                label(Control13)
                {
                    ApplicationArea = Basic, Suite;
                    ShowCaption = false;
                    Caption = '';
                }
                field(Balance; Balance + "Bank Debit Amount" - "Bank Credit Amount")
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Balance';
                    Editable = false;
                    Enabled = BalanceEnable;
                    ToolTip = 'Specifies a balance, consisting of the Balance Last Statement field, plus the balance that has accumulated in the Statement Amount field.';
                }
                field(Amount; SeletedEntriesAmount)
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Amount';
                    Editable = false;
                    Enabled = BalanceEnable;
                    ToolTip = 'Selected Entries total amount';
                }
                field(TotalBalance; TotalBalance + "Bank Debit Amount" - "Bank Credit Amount")
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Total Balance';
                    Editable = false;
                    Enabled = TotalBalanceEnable;
                    ToolTip = 'Specifies the accumulated balance of the bank reconciliation, which consists of the Balance Last Statement field, plus the balance in the Statement Amount field.';
                }
                field(TotalDiff; TotalDiff)
                {
                    ApplicationArea = Basic, Suite;
                    AutoFormatExpression = GetCurrencyCode;
                    AutoFormatType = 1;
                    Caption = 'Total Difference';
                    Editable = false;
                    Enabled = TotalDiffEnable;
                    ToolTip = 'Specifies the total amount of the Difference field for all the lines on the bank reconciliation.';
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Select Entry")
            {
                Caption = 'Select Entries';
                Promoted = true;
                PromotedCategory = Process;
                applicationarea = ALL;

                trigger OnAction();
                var
                    OriginalBankStatement: Record "Original Bank Statement";
                begin
                    //CurrPage.
                    OriginalBankStatement.CopyFilters(Rec);
                    if OriginalBankStatement.FindSet() then
                        OriginalBankStatement.ModifyAll(Select, true);
                end;
            }
            action("UnSelect Entry")
            {
                Caption = 'UnSelect Entries';
                Promoted = true;
                PromotedCategory = Process;
                applicationarea = ALL;

                trigger OnAction();
                var
                    OriginalBankStatement: Record "Original Bank Statement";
                begin
                    //CurrPage.
                    OriginalBankStatement.CopyFilters(Rec);
                    if OriginalBankStatement.FindSet() then
                        OriginalBankStatement.ModifyAll(Select, false);
                end;
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        if "Line No." <> 0 then
            CalcBalance("Line No.");
        MatchingStatusOnFormat;
        MatchLineNoOnFormat;
        BankPostingDateOnFormat;
        BankDocNoOnFormat;
        BankNarrationOnFormat;
        BankDebitAmountOnFormat;
        CancelNoOnFormat;
        BankCreditAmountOnFormat;
        BankDepositSlipNoOnFormat;
        SetUserInteractions;
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetUserInteractions;
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        SetUserInteractions;
    end;

    var
        Boo: Boolean;
        LineNoVal: Integer;
        [InDataSet]
        "Matching StatusEmphasize": Boolean;
        [InDataSet]
        "MatchLine NoEmphasize": Boolean;
        [InDataSet]
        "Bank Posting DateEmphasize": Boolean;
        [InDataSet]
        "Bank Doc. No.Emphasize": Boolean;
        [InDataSet]
        "Bank NarrationEmphasize": Boolean;
        [InDataSet]
        "Bank Debit AmountEmphasize": Boolean;
        [InDataSet]
        "Cancel NoEmphasize": Boolean;
        [InDataSet]
        "Bank Credit AmountEmphasize": Boolean;
        [InDataSet]
        "Bank Deposit Slip No.Emphasize": Boolean;
        BankAccRecon: Record "Bank Acc. Reconciliation";
        StyleTxt: Text;
        TotalDiff: Decimal;
        Balance: Decimal;
        TotalBalance: Decimal;
        [InDataSet]
        TotalDiffEnable: Boolean;
        [InDataSet]
        TotalBalanceEnable: Boolean;
        [InDataSet]
        BalanceEnable: Boolean;
        ApplyEntriesAllowed: Boolean;
        SeletedEntriesAmount: Decimal;

    local procedure SetUserInteractions()
    begin
        StyleTxt := GetStyle2;
    end;

    procedure GetStyle2(): Text
    begin
        if ("Matching Status" = 0) or ("Matching Status" = 1) then
            exit('Favorable');

        exit('');
    end;

    procedure GetCurrencyCode(): Code[10]
    var
        BankAcc: Record "Bank Account";
    begin
        if Code = BankAcc."No." then
            exit(BankAcc."Currency Code");

        if BankAcc.Get(Code) then
            exit(BankAcc."Currency Code");

        exit('');
    end;

    local procedure CalcBalance(BankAccReconLineNo: Integer)
    var
        TempBankAccReconLine: Record "Original Bank Statement";
    begin
        if BankAccRecon.Get(BankAccRecon."Statement Type"::"Bank Reconciliation", Code, "Statement No.") then;

        TempBankAccReconLine.Copy(Rec);

        /*TotalDiff := -Difference;
        if TempBankAccReconLine.CalcSums(Difference) then begin
            TotalDiff := TotalDiff + TempBankAccReconLine.Difference;
            TotalDiffEnable := true;
        end else
            TotalDiffEnable := false;*/

        TotalBalance := BankAccRecon."Balance Last Statement" - ("Bank Debit Amount" - "Bank Credit Amount");
        if TempBankAccReconLine.CalcSums("Bank Credit Amount", "Bank Debit Amount") then begin
            TotalBalance := TotalBalance + (TempBankAccReconLine."Bank Debit Amount" - TempBankAccReconLine."Bank Credit Amount");
            TotalBalanceEnable := true;
        end else
            TotalBalanceEnable := false;

        Balance := BankAccRecon."Balance Last Statement" - ("Bank Debit Amount" - "Bank Credit Amount");
        TempBankAccReconLine.SetRange("Line No.", 0, BankAccReconLineNo);
        if TempBankAccReconLine.CalcSums("Bank Credit Amount", "Bank Debit Amount") then begin
            Balance := Balance + (TempBankAccReconLine."Bank Debit Amount" - TempBankAccReconLine."Bank Credit Amount");
            BalanceEnable := true;
        end else
            BalanceEnable := false;
        Clear(SeletedEntriesAmount);
        //CurrPage.SetSelectionFilter(TempBankAccReconLine);
        TempBankAccReconLine.Reset();
        TempBankAccReconLine.SetRange(Code, Code);
        TempBankAccReconLine.SetRange("Statement No.", "Statement No.");
        TempBankAccReconLine.SetRange(Select, true);
        TempBankAccReconLine.SETRANGE(TempBankAccReconLine."Matching Status", TempBankAccReconLine."Matching Status"::"Not Matching");
        IF TempBankAccReconLine.FINDFIRST THEN
            REPEAT
                SeletedEntriesAmount += TempBankAccReconLine."Bank Credit Amount" - TempBankAccReconLine."Bank Debit Amount";
            UNTIL TempBankAccReconLine.NEXT = 0;
    end;

    procedure GetLineNo(LineNo: Integer);
    begin
        LineNoVal := LineNo;
    end;

    procedure GetLineMM(var LineN: Integer);
    begin
        LineN := "Line No.";
    end;

    procedure SelectedEntries(var OnglBnkStatement: Record "Original Bank Statement");
    begin
        OnglBnkStatement := Rec;
        CurrPage.SETSELECTIONFILTER(OnglBnkStatement);
    end;

    local procedure OnDeactivateForm();
    begin
        CLEAR(LineNoVal);
    end;

    local procedure MatchingStatusOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
        IF "Matched Line No." = LineNoVal THEN BEGIN
            CurrForm."Matching Status".UPDATEFORECOLOR(205);
            CurrForm."Matching Status".UPDATEFONTBOLD(TRUE);
        END ELSE
          CurrForm."Matching Status".UPDATEFONTBOLD(FALSE);
        END
         */


        if "Matching Status" = "Matching Status"::Exact then begin
            "Matching StatusEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Matching StatusEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Matching StatusEmphasize" := true;
                end
                else
                    "Matching StatusEmphasize" := false;

    end;

    local procedure MatchLineNoOnFormat();
    begin

        if "Matching Status" = "Matching Status"::Exact then begin
            "MatchLine NoEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "MatchLine NoEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "MatchLine NoEmphasize" := true;
                end
                else
                    "MatchLine NoEmphasize" := false;
        //UpdateControls;
    end;

    local procedure BankPostingDateOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
        IF "Matched Line No." = LineNoVal THEN BEGIN
            CurrForm."Bank Posting Date".UPDATEFORECOLOR(205);
            CurrForm."Bank Posting Date".UPDATEFONTBOLD(TRUE);
        END ELSE
            CurrForm."Bank Posting Date".UPDATEFONTBOLD(FALSE);
        END  */

        if "Matching Status" = "Matching Status"::Exact then begin
            "Bank Posting DateEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Bank Posting DateEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Bank Posting DateEmphasize" := true;
                end
                else
                    "Bank Posting DateEmphasize" := false;

    end;

    local procedure BankDocNoOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
        IF "Matched Line No." = LineNoVal THEN BEGIN
            CurrForm."Bank Doc. No.".UPDATEFORECOLOR(205);
            CurrForm."Bank Doc. No.".UPDATEFONTBOLD(TRUE);
        END ELSE
          CurrForm."Bank Doc. No.".UPDATEFONTBOLD(FALSE);
        END; */

        if "Matching Status" = "Matching Status"::Exact then begin
            "Bank Doc. No.Emphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Bank Doc. No.Emphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Bank Doc. No.Emphasize" := true;
                end
                else
                    "Bank Doc. No.Emphasize" := false;

    end;

    local procedure BankNarrationOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
        IF "Matched Line No." = LineNoVal THEN BEGIN
            CurrForm."Bank Narration".UPDATEFORECOLOR(205);
            CurrForm."Bank Narration".UPDATEFONTBOLD(TRUE);
        END ELSE
            CurrForm."Bank Narration".UPDATEFONTBOLD(FALSE);
        END;
         */
        // UpdateControls
        if "Matching Status" = "Matching Status"::Exact then begin
            "Bank NarrationEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Bank NarrationEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Bank NarrationEmphasize" := true;
                end
                else
                    "Bank NarrationEmphasize" := false;

    end;

    local procedure BankDebitAmountOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
        IF "Matched Line No." = LineNoVal THEN BEGIN
            CurrForm."Bank Debit Amount".UPDATEFORECOLOR(205);
            CurrForm."Bank Debit Amount".UPDATEFONTBOLD(TRUE);
        END ELSE
          CurrForm."Bank Debit Amount".UPDATEFONTBOLD(FALSE);
        END;
        */
        //UpdateControls
        if "Matching Status" = "Matching Status"::Exact then begin
            "Bank Debit AmountEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Bank Debit AmountEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Bank Debit AmountEmphasize" := true;
                end
                else
                    "Bank Debit AmountEmphasize" := false;

    end;

    local procedure CancelNoOnFormat();
    begin
        //UpdateControls
        if "Matching Status" = "Matching Status"::Exact then begin
            "Cancel NoEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Cancel NoEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Cancel NoEmphasize" := true;
                end
                else
                    "Cancel NoEmphasize" := false;
    end;

    local procedure BankCreditAmountOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
          IF "Matched Line No." = LineNoVal THEN BEGIN
             CurrForm."Bank Credit Amount".UPDATEFORECOLOR(205);
             CurrForm."Bank Credit Amount".UPDATEFONTBOLD(TRUE);
          END ELSE
             CurrForm."Bank Credit Amount".UPDATEFONTBOLD(FALSE);
        END;
        */

        if "Matching Status" = "Matching Status"::Exact then begin
            "Bank Credit AmountEmphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Bank Credit AmountEmphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Bank Credit AmountEmphasize" := true;
                end
                else
                    "Bank Credit AmountEmphasize" := false;

    end;

    local procedure BankDepositSlipNoOnFormat();
    begin
        /*IF "Matching Status"<>"Matching Status"::"Not Matching" THEN BEGIN
        IF "Matched Line No." = LineNoVal THEN BEGIN
            CurrForm."Bank Deposit Slip No.".UPDATEFORECOLOR(205);
            CurrForm."Bank Deposit Slip No.".UPDATEFONTBOLD(TRUE);
        END ELSE
            CurrForm."Bank Deposit Slip No.".UPDATEFONTBOLD(FALSE);
        END;
        */
        //UpdateControls
        if "Matching Status" = "Matching Status"::Exact then begin
            "Bank Deposit Slip No.Emphasize" := true;
        end
        else
            if "Matching Status" = "Matching Status"::Probable then begin
                "Bank Deposit Slip No.Emphasize" := true;
            end
            else
                if "Matching Status" = "Matching Status"::Cancel then begin
                    "Bank Deposit Slip No.Emphasize" := true;
                end
                else
                    "Bank Deposit Slip No.Emphasize" := false;

    end;

    Procedure GetMatchFil(Lpar: option Exact,Probable,"Not Matching",Cancel,"Manually Matched",All) // G2S 7708-CAS-01421-Z6M7V9
    begin
        IF Lpar = lpar::Exact then
            setrange("Matching Status", "Matching Status"::Exact)
        else
            IF Lpar = lpar::Probable then
                setrange("Matching Status", "Matching Status"::Probable)
            Else
                IF Lpar = lpar::"Not Matching" then
                    setrange("Matching Status", "Matching Status"::"Not Matching")
                else
                    IF Lpar = lpar::Cancel then
                        setrange("Matching Status", "Matching Status"::Cancel)
                    else
                        IF Lpar = lpar::"Manually Matched" then // G2S 7708-CAS-01421-Z6M7V9
                            setrange("Matching Status", "Matching Status"::"Manually Matched")
                        else
                            IF Lpar = lpar::ALL then
                                ReSET;
        currpage.Update();
    end;
}

