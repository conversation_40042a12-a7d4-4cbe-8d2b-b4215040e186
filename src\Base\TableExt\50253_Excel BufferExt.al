tableextension 50253 ExcelBuff<PERSON> extends "Excel Buffer"
{
    fields
    {
        // Add changes to table fields here
    }
    procedure SetUseInfoSheed()
    begin
        UseInfoSheed := true;
    end;

    procedure CreateBookAndSaveExcel(FileName: Text[100]; SheetName: Text[100]; ReportHeader: Text[100]; CompanyName2: Text[100]; UserID2: Text[100])
    var
        TempFile: File;
        InstreamGVar: InStream;
        OutStreamGVar: OutStream;
        TempBlob: Record tempblob temporary;
    //FileName: Text;
    BEGIN
        CreateBook1(FileName, SheetName);
        WriteSheet1(ReportHeader, CompanyName2, UserID2);
        CloseBook1;
        //SaveExcelBy

        TempFile.OPEN(FileNameServer);
        TempFile.CREATEINSTREAM(InstreamGVar);

        TempBlob.DELETEALL();
        TempBlob.INIT;
        TempBlob.Blob.CREATEOUTSTREAM(OutStreamGVar);
        COPYSTREAM(OutStreamGVar, InstreamGVar);
        TempBlob.INSERT();

        FileName := '\\B2BSRV-282\Sales Volume Transactions\' + 'Item' + '.csv';
        IF EXISTS(FileName) THEN
            ERASE(FileName);
        TempBlob.Blob.EXPORT(FileName);
        TempFile.CLOSE();
        TempBlob.DELETEALL();
    END;

    [Scope('OnPrem')]
    procedure CreateBook1(FileName: Text; SheetName: Text)
    begin
        if SheetName = '' then
            Error(Text002);

        if FileName = '' then
            FileNameServer := FileManagement.ServerTempFileName('csv')
        else begin
            if Exists(FileName) then
                Erase(FileName);
            FileNameServer := FileName;
        end;

        XlWrkBkWriter1 := XlWrkBkWriter1.Create(FileNameServer);
        if IsNull(XlWrkBkWriter1) then
            Error(Text037);

        XlWrkShtWriter1 := XlWrkBkWriter1.FirstWorksheet;
        if SheetName <> '' then
            XlWrkShtWriter1.Name := SheetName;

        OpenXMLManagement.SetupWorksheetHelper(XlWrkBkWriter1);
    end;


    /*procedure SaveExcelBy();
    var
        FileManagement: Codeunit "File Management";
        FileNameClient: Text[100];
        FileNameServer: Text[100];
        TempFile: File;
        InstreamGVar: InStream;
        OutStreamGVar: OutStream;
        TempBlob: Record tempblob temporary;
        FileName: Text;
    BEGIN
        //FileNameServer := '\\B2BSRV-282\Sales Volume Transactions\';
        //FileNameClient := FileManagement.DownloadTempFile(FileNameServer);
        //FileNameClient := FileManagement.MoveAndRenameClientFile(FileNameClient, 'Book1.xlsx', '\\B2BSRV-282\Sales Volume Transactions\');

        TempFile.OPEN(FileNameServer);
        TempFile.CREATEINSTREAM(InstreamGVar);

        TempBlob.DELETEALL();
        TempBlob.INIT;
        TempBlob.Blob.CREATEOUTSTREAM(OutStreamGVar);
        COPYSTREAM(OutStreamGVar, InstreamGVar);
        TempBlob.INSERT();

        //>>B2BN1.0 25Sept2020
        //FileName := 'Daily Sales Report_' + DELCHR(FORMAT(TODAY), '=', '/\-,.@#$%^&*()');
        FileName := 'Sales_' + DELCHR(FORMAT(TODAY), '=', '/\-,.@#$%^&*()');
        //TempBlob.Blob.EXPORT('\\*************\dsr mail\Daily sales Report.Xlsx');
        TempBlob.Blob.EXPORT('\\B2BSRV-282\Sales Volume Transactions\' + FileName + '.csv');
        TempFile.CLOSE();
        TempBlob.DELETEALL();
    END;
    */

    local procedure GetFriendlyFilename1(): Text
    begin
        if FriendlyName = '' then
            exit('Book1' + ExcelFileExtensionTok1);

        exit(FileManagement.StripNotsupportChrInFileName(FriendlyName) + ExcelFileExtensionTok1);
    end;

    procedure OpenExcel1()
    begin
        if OpenUsingDocumentService1('') then
            exit;

        FileManagement.DownloadHandler(FileNameServer, '', '', Text034, GetFriendlyFilename1);
    end;

    [Scope('OnPrem')]
    procedure CreateBookAndOpenExcel1(FileName: Text; SheetName: Text[250]; ReportHeader: Text; CompanyName2: Text; UserID2: Text)
    begin
        CreateBook1(FileName, SheetName);
        WriteSheet1(ReportHeader, CompanyName2, UserID2);
        CloseBook1;
        OpenExcel1();
    end;

    procedure CloseBook1()
    begin
        if not IsNull(XlWrkBkWriter1) then begin
            XlWrkBkWriter1.ClearFormulaCalculations;
            XlWrkBkWriter1.ValidateDocument;
            XlWrkBkWriter1.Close;
            Clear(XlWrkShtWriter1);
            Clear(XlWrkBkWriter1);
        end;

        if not IsNull(XlWrkBkReader1) then begin
            Clear(XlWrkShtReader1);
            Clear(XlWrkBkReader1);
        end;
    end;

    procedure WriteSheet1(ReportHeader: Text; CompanyName2: Text; UserID2: Text)
    var
        TypeHelper: Codeunit "Type Helper";
        OrientationValues: DotNet OrientationValues;
        XmlTextWriter: DotNet XmlTextWriter;
        FileMode: DotNet FileMode;
        Encoding: DotNet Encoding;
        VmlDrawingPart: DotNet VmlDrawingPart;
    begin
        XlWrkShtWriter1.AddPageSetup(OrientationValues.Landscape, 9); // 9 - default value for Paper Size - A4
        if ReportHeader <> '' then
            XlWrkShtWriter1.AddHeader(
              true,
              StrSubstNo('%1%2%1%3%4', GetExcelReference(1), ReportHeader, TypeHelper.LFSeparator, CompanyName2));

        XlWrkShtWriter1.AddHeader(
          false,
          StrSubstNo('%1%3%4%3%5 %2', GetExcelReference(2), GetExcelReference(3), TypeHelper.LFSeparator, UserID2, PageTxt));

        //GR1.0 S
        IF ExcelBookCreated THEN
            XlWrkShtWriter1 := XlWrkBkWriter1.AddWorksheet(NewSheetName);
        //GR1.0 E
        XlWrkShtWriter1.AddPageSetup(OrientationValues.Landscape, 9); // 9 - default value for Paper Size - A4


        OpenXMLManagement.AddAndInitializeCommentsPart(XlWrkShtWriter1, VmlDrawingPart);

        StringBld := StringBld.StringBuilder;
        StringBld.Append(VmlDrawingXmlTxt);

        WriteAllToCurrentSheet(Rec);

        StringBld.Append(EndXmlTokenTxt);





        XmlTextWriter := XmlTextWriter.XmlTextWriter(VmlDrawingPart.GetStream(FileMode.Create), Encoding.UTF8);
        XmlTextWriter.WriteRaw(StringBld.ToString);
        XmlTextWriter.Flush;
        XmlTextWriter.Close;

        if UseInfoSheet then
            if not TempInfoExcelBuf.IsEmpty then begin
                SelectOrAddSheet(Text023);
                WriteAllToCurrentSheet(TempInfoExcelBuf);
            end;
    end;

    Procedure OnlyCreateBook(SheetName: Text[250]; ReportHeader: Text[80]; CompanyName: Text[30]; UserID2: Text; BookCreated: Boolean)
    var

    BEGIN
        ExcelBookCreated := BookCreated;
        NewSheetName := SheetName;
        IF NOT BookCreated THEN
            CreateBook(SheetName, SheetName);
        CurrentRow := 0;
        CurrentCol := 0;
        WriteSheet1(ReportHeader, CompanyName, UserID2);
    END;
    //ExcelBuf.OnlyCreateBook('Summary','Gross',COMPANYNAME,USERID,FALSE);



    local procedure OpenUsingDocumentService1(FileName: Text): Boolean
    var
        DocumentServiceMgt: Codeunit "Document Service Management";
        FileMgt: Codeunit "File Management";
        PathHelper: DotNet Path;
        DialogWindow: Dialog;
        DocumentUrl: Text;
    begin
        if not Exists(FileNameServer) then
            Error(Text003, FileNameServer);

        // if document service is configured we save the generated document to SharePoint and open it from there.
        if DocumentServiceMgt.IsConfigured then begin
            if FileName = '' then
                FileName := 'Book.' + PathHelper.ChangeExtension(PathHelper.GetRandomFileName, 'csv')
            else begin
                // if file is not applicable for the service it can not be opened using the document service.
                if not DocumentServiceMgt.IsServiceUri(FileName) then
                    exit(false);

                FileName := FileMgt.GetFileName(FileName);
            end;

            DialogWindow.Open(StrSubstNo(SavingDocumentMsg, FileName));
            DocumentUrl := DocumentServiceMgt.SaveFile(FileNameServer, FileName, true);
            DocumentServiceMgt.OpenDocument(DocumentUrl);
            DialogWindow.Close;
            exit(true);
        end;

        exit(false);
    end;

    local procedure CreateAndFillExcelBuffer(var TempExcelBuf: Record "Excel Buffer" temporary)
    begin
        TempExcelBuf.CreateNewBook('Summary');
        //FillExcelBuffer(TempExcelBuf);
        TempExcelBuf.WriteSheet('', CompanyName(), UserId());
        TempExcelBuf.CloseBook();
    end;



    /*WriteSheet1(ReportHeader : Text;CompanyName2 : Text;UserID2 : Text)
LastUpdate := CURRENTDATETIME;
ExcelBufferDialogMgt.Open(Text005);

CRLF := 10;
RecNo := 1;
TotalRecNo := COUNT + InfoExcelBuf.COUNT;
RecNo := 0;
//GR1.0 S
IF ExcelBookCreated THEN
   XlWrkShtWriter := XlWrkBkWriter.AddWorksheet(NewSheetName);
//GR1.0 E
XlWrkShtWriter.AddPageSetup(OrientationValues.Landscape,9); // 9 - default value for Paper Size - A4

// commit is required because of the result boolean check of ExcelBufferDialogMgt.RUN
COMMIT;

IF FINDSET THEN
  REPEAT
    RecNo := RecNo + 1;
    IF NOT UpdateProgressDialog(ExcelBufferDialogMgt,LastUpdate,RecNo,TotalRecNo) THEN BEGIN
      QuitExcel;
      ERROR(Text035)
    END;
    IF Formula = '' THEN
      WriteCellValue(Rec)
    ELSE
      WriteCellFormula(Rec)
  UNTIL NEXT = 0;

IF ReportHeader <> '' THEN
  XlWrkShtWriter.AddHeader(
    TRUE,
    STRSUBSTNO('%1%2%1%3%4',GetExcelReference(1),ReportHeader,CRLF,CompanyName2));

XlWrkShtWriter.AddHeader(
  FALSE,
  STRSUBSTNO(Text006,GetExcelReference(2),GetExcelReference(3),CRLF,UserID2));

IF UseInfoSheet THEN
  IF InfoExcelBuf.FINDSET THEN BEGIN
    XlWrkShtWriter := XlWrkBkWriter.AddWorksheet(Text023);
    REPEAT
      InfoRecNo := InfoRecNo + 1;
      IF NOT UpdateProgressDialog(ExcelBufferDialogMgt,LastUpdate,RecNo + InfoRecNo,TotalRecNo) THEN BEGIN
        QuitExcel;
        ERROR(Text035)
      END;
      IF InfoExcelBuf.Formula = '' THEN
        WriteCellValue(InfoExcelBuf)
      ELSE
        WriteCellFormula(InfoExcelBuf)
    UNTIL InfoExcelBuf.NEXT = 0;
  END;

ExcelBufferDialogMgt.Close;*/







    var
        UseInfoSheed: Boolean;
        ExcelFileExtensionTok1: Label '.csv', Locked = true;
        FriendlyName: Text;
        Text034: Label 'Excel Files (*.xls*)|*.xls*|All Files (*.*)|*.*', Comment = '{Split=r''\|\*\..{1,4}\|?''}{Locked="Excel"}';
        FileManagement: Codeunit "File Management";
        FileNameServer: Text;
        Text003: Label 'The file %1 does not exist.';
        SavingDocumentMsg: Label 'Saving the following document: %1.';
        Text002: Label 'You must enter an Excel worksheet name.', Comment = '{Locked="Excel"}';

        XlWrkShtWriter1: DotNet WorksheetWriter;
        Text037: Label 'Could not create the Excel workbook.', Comment = '{Locked="Excel"}';
        OpenXMLManagement1: Codeunit "OpenXML Management";
        XlWrkBkWriter1: DotNet WorkbookWriter;
        XlWrkBkReader1: DotNet WorkbookReader;
        XlWrkShtReader1: DotNet WorksheetReader;
        StringBld: DotNet StringBuilder;
        TempInfoExcelBuf: Record "Excel Buffer" temporary;
        PageTxt: Label 'Page';
        OpenXMLManagement: Codeunit "OpenXML Management";
        VmlDrawingXmlTxt: Label '<xml xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel"><o:shapelayout v:ext="edit"><o:idmap v:ext="edit" data="1"/></o:shapelayout><v:shapetype id="_x0000_t202" coordsize="21600,21600" o:spt="202"  path="m,l,21600r21600,l21600,xe"><v:stroke joinstyle="miter"/><v:path gradientshapeok="t" o:connecttype="rect"/></v:shapetype>', Locked = true;
        EndXmlTokenTxt: Label '</xml>', Locked = true;
        UseInfoSheet: Boolean;
        Text023: Label 'Information';
        BookCreated: boolean;

        ExcelBookCreated: Boolean;
        NewSheetName: Text;
        CurrentRow: integer;
        CurrentCol: Integer;


}