/// <summary>
/// Table CustomSetup (ID 50057).
/// </summary>
table 50057 "Custom Setup"
{
    Caption = 'Custom Setup';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "ID"; Integer)
        {
            AutoIncrement = true;
            DataClassification = ToBeClassified;
        }
        field(2; "Outreach Dist. Email"; Code[150])
        {
            DataClassification = ToBeClassified;
        }
        field(3; Category; Option)
        {
            OptionMembers = Outreach,"Bank API","Project LEAP",WMS,"KD Rebate","Customer Posting Group","Vendor Posting Group","Item Posting Grp","FA Posting Group","Bank Acc. Posting Grp",Attachments;
            OptionCaption = 'Outreach, Bank API, Project LEAP, WMS, KD Rebate,Customer Posting Group,Vendor Posting Group,Item Posting Group,FA Posting Group,Bank Acc. Posting Grp,Attachments';
            trigger OnValidate()
            begin
                "Posting Group" := '';
            end;
        }
        field(4; "Created By"; Code[150])
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(5; "Date Modified"; DateTime)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(6; "Date Created"; DateTime)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(7; "Modified By"; Code[150])
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(8; "Validate Material Availability"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        field(9; "WMS API Username"; Blob)
        {
            DataClassification = ToBeClassified;
        }
        field(10; "WMS API Password"; Blob)
        {
            DataClassification = ToBeClassified;
        }
        field(11; "WMS Base URL endpoint"; Blob)
        {
            DataClassification = ToBeClassified;
        }
        field(12; "Consumption Journal Temp."; Code[20])
        {
            TableRelation = "Item Journal Template";
        }
        //G2S KD rebate 280424
        field(13; "100% Disc. VAT Prod. Post Grp"; Code[20])
        {
            TableRelation = "VAT Product Posting Group";
        }
        field(14; "100% Disc. VAT Bus. Post Grp"; Code[20])
        {
            TableRelation = "VAT Business Posting Group";
        }
        //G2S KD rebate 280424
        //WMS setup for transfer from Location
        field(15; "Transfer from Location"; Code[20])
        {
            TableRelation = Location;
        }
        field(17; "Posting Group"; Code[20])
        {
            Caption = 'Posting Group';
            DataClassification = CustomerContent;
            TableRelation = if (Category = const("Customer Posting Group")) "Customer Posting Group".Code
            else
            if (Category = const("Vendor Posting Group")) "Vendor Posting Group".Code
            else
            if (Category = const("Item Posting Grp")) "Inventory Posting Group".Code
            else
            if (Category = const("FA Posting Group")) "FA Posting Group".Code
            else
            if (Category = const("Bank Acc. Posting Grp")) "Bank Account Posting Group".Code;
        }
        field(18; "Validity Period"; DateFormula)
        {
            Caption = 'Group Validity Period';
            DataClassification = CustomerContent;
        }
        field(19; "Scratchpad Journal Batch"; Code[20])
        {
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Scratchpad Jnl. Template"));
        }
        field(20; "Scratchpad Jnl. Template"; Code[20])
        {
            TableRelation = "Item Journal Template";
        }
    }
    keys
    {
        key(PK; "ID")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        myInt: Integer;
    begin
        "Created By" := UserId;
        "Date Created" := CurrentDateTime;
    end;

    trigger OnModify()
    var
        myInt: Integer;
    begin
        "Date Modified" := CurrentDateTime;
        "Modified By" := UserId;
    end;
}
