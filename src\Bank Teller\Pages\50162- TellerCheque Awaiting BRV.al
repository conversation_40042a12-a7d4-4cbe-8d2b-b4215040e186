page 50162 "Teller/Cheque Awaiting BRV"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 3.0       SAA      24-Nov-11    -> Form created for Bank Confirmed Tellers functionality.
    // CRF:2019-0090  SAA 28-10-19     -> New control added Narration, Currency Code,Teller Amount(LCY),G/L Account No.,
    //                                    G/L Account Name

    Caption = 'Bank Confirmed Tellers Register';
    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Confirmed Teller Receipt";
    SourceTableView = WHERE("Bank Receipt Created" = FILTER(false));
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posted By"; "Posted By")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Posted Date"; "Posted Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Issued"; "Bank Issued")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("G/L Account No."; "G/L Account No.")
                {
                    ApplicationArea = all;
                }
                field("G/L Account Name"; "G/L Account Name")
                {
                    ApplicationArea = all;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                }
                field("Teller Amount(LCY)"; "Teller Amount(LCY)")
                {
                    ApplicationArea = all;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cheque No."; "Cheque No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Cheque Date"; "Cheque Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Create BRV"; "Create BRV")
                {
                    ApplicationArea = all;
                }
                field("Return Confirmed Bank Teller"; "Return Confirmed Bank Teller")
                {
                    ApplicationArea = all;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                }
                field("Chq Value Date"; "Chq Value Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("F&unction")
            {
                Caption = 'F&unction';
                action("Return Confirmed Bank Teller.")
                {
                    ApplicationArea = all;
                    Caption = 'Return Confirmed Bank Teller';
                    Ellipsis = true;

                    trigger OnAction();
                    var
                        PurchaseHeader: Record "Purchase Header";
                        ApprovalMgt: Codeunit 1535;
                    begin
                        //IF UserPermLevelRec.GET(USERID) THEN
                        //  IF NOT UserPermLevelRec."Reverse Confirmed Tellers" THEN
                        //    ERROR(Text50201);

                        BankConfirmTellersRec.SETRANGE("Customer No.", "Customer No.");
                        BankConfirmTellersRec.SETFILTER(BankConfirmTellersRec."Return Confirmed Bank Teller", '%1', true);

                        if not BankConfirmTellersRec.FINDFIRST then
                            ERROR(Text50200)
                        else
                            if BankConfirmTellersRec.FINDFIRST then
                                TESTFIELD("Reason for Return");
                        Window.OPEN('#1############################\\' +
                          Text50202);

                        repeat
                            Linecount := Linecount + 1;
                            Window.UPDATE(2, Linecount);

                            BankTellerConfirmationRec.INIT;
                            if OldBankTellerConfirmationRec.FINDLAST then
                                BankTellerConfirmationRec."No." := OldBankTellerConfirmationRec."No." + 1;
                            //BankTellerConfirmationRec.INSERT;

                            //BankTellerConfirmationRec.TRANSFERFIELDS(BankConfirmTellersRec);

                            BankTellerConfirmationRec."Branch Code" := BankConfirmTellersRec."Branch Code";
                            BankTellerConfirmationRec."Bank Deposited" := BankConfirmTellersRec."Bank Deposited";
                            BankTellerConfirmationRec."Teller No." := BankConfirmTellersRec."Unposted Teller No.";
                            BankTellerConfirmationRec."Teller Date" := BankConfirmTellersRec."Teller Date";
                            BankTellerConfirmationRec."Teller Amount" := BankConfirmTellersRec."Teller Amount";
                            BankTellerConfirmationRec."Cheque No." := BankConfirmTellersRec."Cheque No.";
                            BankTellerConfirmationRec."Cheque Date" := BankConfirmTellersRec."Cheque Date";
                            BankTellerConfirmationRec."Bank No." := BankConfirmTellersRec."Bank Issued";
                            BankTellerConfirmationRec."Bank Name" := BankConfirmTellersRec."Bank Name";
                            BankTellerConfirmationRec."Customer No." := BankConfirmTellersRec."Customer No.";
                            BankTellerConfirmationRec."Customer Name" := BankConfirmTellersRec."Customer Name";
                            BankTellerConfirmationRec."Global Dimension 1 Code" := BankConfirmTellersRec."Global Dimension 1 Code";
                            BankTellerConfirmationRec."Global Dimension 2 Code" := BankConfirmTellersRec."Global Dimension 2 Code";
                            BankTellerConfirmationRec."Responsibility Center" := BankConfirmTellersRec."Responsibility Center";
                            BankTellerConfirmationRec.Company := BankConfirmTellersRec.Company;
                            BankTellerConfirmationRec."Chq. Value Date" := BankConfirmTellersRec."Chq Value Date";
                            //BankTellerConfirmationRec."Branch Names":=BankConfirmTellersRec."Branch names";
                            BankTellerConfirmationRec."Teller Type" := BankConfirmTellersRec."Teller Type";
                            BankTellerConfirmationRec."Bank Location" := BankConfirmTellersRec."Bank Location";
                            BankTellerConfirmationRec."Bank Location" := BankConfirmTellersRec."Bank Location";
                            BankTellerConfirmationRec."Paid By" := BankConfirmTellersRec."Paid By";
                            BankTellerConfirmationRec."Reason for Return" := BankConfirmTellersRec."Reason for Return";

                            BankTellerConfirmationRec."Released for Confirmation" := false;
                            BankTellerConfirmationRec."Released By" := '';
                            BankTellerConfirmationRec."Released Date" := 0D;
                            BankTellerConfirmationRec."Released time" := 000000T;

                            BankTellerConfirmationRec."Teller Is Confirmed" := false;
                            BankTellerConfirmationRec."Confirmation No." := '';
                            BankTellerConfirmationRec."Confirmation Date" := 0D;
                            BankTellerConfirmationRec."Confirmation Time" := 000000T;
                            BankTellerConfirmationRec."Confirmed By" := '';

                            BankTellerConfirmationRec."Bank No." := '';
                            BankTellerConfirmationRec."Bank Name" := '';
                            BankTellerConfirmationRec."Bank Code" := BankTellerConfirmationRec."Bank Code"::" ";
                            BankTellerConfirmationRec."Bank Location" := '';

                            BankTellerConfirmationRec."Reversed By" := USERID;
                            // NYO 26-09-2019
                            BankTellerConfirmationRec."Credit Account Type" := BankConfirmTellersRec."Credit Account Type";
                            BankTellerConfirmationRec."Account No." := BankConfirmTellersRec."G/L Account No.";
                            // NYO 26-09-2019

                            BankTellerConfirmationRec.INSERT;
                            //BankTellerConfirmationRec.MODIFY;

                            BankConfirmTellersRec.DELETE;

                        until BankConfirmTellersRec.NEXT = 0;

                        Window.CLOSE;
                    end;
                }
                separator("-")
                {
                    Caption = '-';
                }
                action("Create Bank Receipt")
                {
                    ApplicationArea = all;
                    Caption = 'Create Bank Receipt';

                    trigger OnAction();
                    begin
                        //CreateBankReceiptVoucher;//PK
                        CreateBankReceiptVoucherNpk;
                    end;
                }
            }
        }
    }

    trigger OnOpenPage();
    begin
        if UserSetup.GET(USERID) then
            if not UserSetup."Teller/Cheque Awaiting BRV" then
                ERROR(Text50203);

        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);

        /*
        RespCentCount :=0;
        
        UserIDRespCent.SETCURRENTKEY("User ID","Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
         REPEAT
          RespCentCount +=1;
          //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
          IF RespCentCount = 1 THEN
           BuildFilter :=UserIDRespCent."Resp. Center Code"
          ELSE
           BuildFilter+='|' + UserIDRespCent."Resp. Center Code";
         UNTIL UserIDRespCent.NEXT=0;
        
        IF RespCentCount > 0 THEN
         //SETRANGE("Responsibility Center",TempResp[1],TempResp[RespCentCount]);
         SETFILTER("Responsibility Center",BuildFilter);
        */

    end;

    var
        BankConfirmTellersRec: Record "Confirmed Teller Receipt";
        BankTellerConfirmationRec: Record "Request Teller Receipt";
        Linecount: Integer;
        Window: Dialog;
        OldBankTellerConfirmationRec: Record "Request Teller Receipt";
        Text50200: Label 'Return Confirmed Bank Teller must not be false';
        Text50201: Label 'You do not have permission to Reverse Confirmed Bank Tellers';
        Text50202: Label 'Posting lines         #2######';
        UserSetup: Record "User Setup";
        Text50203: Label 'You do not have permission for Teller/Cheque Awaiting BRV.';
        UserMgt: Codeunit "User Setup Management";
        RespCentCount: Integer;
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";//B2BSB.1.0
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

