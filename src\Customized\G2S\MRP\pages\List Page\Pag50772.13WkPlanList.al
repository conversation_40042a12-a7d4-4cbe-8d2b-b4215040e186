page 50772 "13Wk Plan List"
{
    ApplicationArea = All;
    Caption = '13Wk Plan List';
    PageType = List;
    SourceTable = "13Wk Header";
    UsageCategory = Lists;
    CardPageId = "13Wk Plan Card";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;
                }
                field("Cc Code"; "Cc Code")
                {
                    ApplicationArea = All;
                }
                field("Created by"; "Created by")
                {
                    ApplicationArea = All;
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}
