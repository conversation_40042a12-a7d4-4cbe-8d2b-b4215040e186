/// <summary>
/// Table End Point URL Setup (ID 50101).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
table 50056 "End Point URL Setup"
{
    DataClassification = CustomerContent;

    fields
    {
        field(1; ID; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(2; "Product End Point URL"; Text[500])
        {
            DataClassification = ToBeClassified;

        }
        field(3; "Shipment End Point URL"; Text[500])
        {
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; ID)
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    begin

    end;

    trigger OnModify()
    begin

    end;

    trigger OnDelete()
    begin

    end;

    trigger OnRename()
    begin

    end;

}
//RFCOutreachAPIGo2solveJuly2023<<<<<<