pageextension 50092 SalesOrderSubform extends "Sales Order Subform"
{
    layout
    {
        modify(Description)
        {
            Editable = false;
        }
        addafter(Type)
        {
            field("Item Category"; "Item Category")
            {
                ApplicationArea = all;//b2bpksalecorr12
            }
            field("Category Wise Items"; "Category Wise Items")
            {
                ApplicationArea = all;//b2bpksalecorr12
            }
        }
        addafter(Description)
        {
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
        }
        addafter("Quantity Invoiced")
        {
            field("Gift Item"; "Gift Item")
            {
                ApplicationArea = all;
            }
            field("Gift Item Quantity"; "Gift Item Quantity")
            {
                ApplicationArea = all;
            }
            field(BinCode; "Bin Code")
            {
                ApplicationArea = ALL;
            }
            field("Gen. Prod. Posting Group"; "Gen. Prod. Posting Group")
            {
                ApplicationArea = ALL;
            }
            field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
            {
                ApplicationArea = ALL;
            }

        }
        //rebate issue
        addafter("Line Amount")
        {
            field("Fixed Rebate"; "Fixed Rebate")
            {
                ApplicationArea = all;

            }
            field("Fixed Rebate Amount"; "Fixed Rebate Amount")
            {
                ApplicationArea = all;
            }
        }
        //rebate issue
        modify("No.")
        {
            trigger OnAfterValidate()
            begin
                IF type = type::Item then
                    Error('Please select the item from category wise items field.');//b2bpksalecorr12
            end;
        }
        modify("Unit Price")
        {
            Editable = false;//PKONJ17.2
            trigger OnBeforeValidate()
            var
                SalHdr: Record "sales header";
            BEGIN
                SalHdr.RESET;
                SalHdr.setrange("Document Type", salHdr."Document Type"::order);
                SalHdr.setrange("No.", "Document No.");
                IF salHdr.findfirst then BEGIN
                    IF SalHdr."Sales Type" <> salHdr."Sales Type"::Export THEN
                        Error('You Cannot give Unit Price Manually.');
                END
            END;
        }
        modify("Line Amount")
        {
            Editable = false;//b2bpksalecorr10
        }
        modify("Location Code")
        {
            Editable = false;//b2bpksalecorr11
        }
        modify("Line Discount %")
        {
            Editable = false;//b2bpksalecorr11
        }
        modify("Line Discount Amount")
        {
            Editable = false;//b2bpksalecorr11
        }
    }


    actions
    {
        // Add changes to page actions here
        addafter(ItemTrackingLines)
        {

            action("Bin Reclassfication Jnl")
            {
                Caption = 'Bin Reclassfication Jnl';
                ApplicationArea = all;
                Image = Journal;
                trigger OnAction()
                var
                    Usersetup: Record "User Setup";
                begin
                    TestField(Type, Type::Item);
                    TestField("No.");
                    TestField(Quantity);
                    Usersetup.get(UserId);
                    IF Usersetup."Bin Reclassification" then
                        BinReclassJnlLine()
                    else
                        Error('You Dont have permissions to Open and Process Bin Reclassification.');
                end;
            }
            action("Bin Reclassfication")
            {
                Caption = 'Bin Reclassfication';
                ApplicationArea = all;
                Image = InwardEntry;
                trigger OnAction()
                var
                    Usersetup: Record "User Setup";
                begin
                    Usersetup.get(UserId);
                    IF Usersetup."Bin Reclassification" then
                        BinReclassfication
                    else
                        Error('You Dont have permissions to Open and Process Bin Reclassification.');
                    SendBinRecMai();
                end;
            }

        }

    }

    procedure SelectGiftItemFlavour()
    var
        PromoSchItemGifts: record "Promo. Schedule Gift Items";
        //PromoSchItemGiftsForm: record "Gift Item Variant Selections";
        ItemGiftQuantity: Decimal;
        GiftItem: boolean;
        PromoQtyUtil: Decimal;
        Text50000: label 'This is not a Gift Item Line, please select a line with a gift item.';
        GenBPGroup: Code[20];
    begin
        IF NOT "Gift Item" THEN
            ERROR(Text50000);

        ItemGiftQuantity := 0;
        //GiftItem:=FALSE;
        PromoQtyUtil := 0;
        CLEAR(GenBPGroup);
        GenBPGroup := "Gen. Bus. Posting Group";
        ItemGiftQuantity := "Gift Item Quantity";
        GiftItem := "Gift Item";
        PromoQtyUtil := "Promo Qty. Util.";
        SuspendStatusCheck := TRUE;
        PromoSchItemGifts.RESET;
        PromoSchItemGifts.SETRANGE("Promo. Schd. Document No.", "Promo. No.");
        PromoSchItemGifts.SETRANGE("Promo. Schd. Line No.", "Promo. Line No.");
        If Page.RunModal(Page::"Gift Item Variants", PromoSchItemGifts, PromoSchItemGifts."Gift Item No.") = Action::LookupOK then begin
            VALIDATE("No.", PromoSchItemGifts."Gift Item No.");
            VALIDATE("Unit Price", 0);
            "Promo. No." := PromoSchItemGifts."Promo. Schd. Document No.";
            "Promo. Line No." := PromoSchItemGifts."Promo. Schd. Line No.";
            VALIDATE("Gift Item Quantity", ItemGiftQuantity);
            VALIDATE(Quantity, ItemGiftQuantity);
            VALIDATE("Gift Item", TRUE);
            VALIDATE("Promo Qty. Util.", PromoQtyUtil);
            VALIDATE("Promo. Offer Line No.", PromoSchItemGifts."Line No.");
            VALIDATE(Quantity);
            VALIDATE("Gen. Bus. Posting Group", GenBPGroup);
            COMMIT;
        END;
    end;

    Var

        InsertedLine: Boolean;
        NextLineNo: Integer;
        NextDocNo: code[20];
        ItemJnlLine: Record "Item Journal Line";
        Customer: Record Customer;


        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
        BinReclassJnl: Record "Bin Reclassfication Jnl";

        LotFilterText: Text;

}