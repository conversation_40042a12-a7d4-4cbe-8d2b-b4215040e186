pageextension 50335 AllocationsPageExt extends Allocations
{
    layout
    {
        addafter("Account No.")
        {
            field("FA Account No."; "FA Account No.")
            {
                ApplicationArea = all;
            }
        }
        addafter("Account Name")
        {
            field(Description; Description)
            {
                ApplicationArea = all;
            }
            field("Line Account Type"; "Line Account Type")
            {
                ApplicationArea = all;
            }
            field("JV Document No."; "JV Document No.")
            {
                ApplicationArea = all;
            }
        }
    }

}