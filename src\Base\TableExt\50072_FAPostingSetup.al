tableextension 50072 FAPostingGrpTabExt extends "FA Posting Group"
{
    fields
    {
        field(50000; "No. Series"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; "CWIP Posting Type"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Capital Work in Progress"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50003; "GRN Adj. Accural Ac. (Interim)"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50004; "Salvage Perentage"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50005; "No. of Depreciation Years"; Decimal)
        {
            BlankZero = true;
            Caption = 'No. of Depreciation Years';
            DecimalPlaces = 2 : 8;
            Description = 'CHI1.0';
            MinValue = 0;

            trigger OnValidate();
            var
                DeprBook2: Record "Depreciation Book";
            begin
            end;
        }
        field(50006; "GRN (Interim) Acct"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50007; Blocked; Boolean)
        {
            DataClassification = CustomerContent;
        }
    }
}