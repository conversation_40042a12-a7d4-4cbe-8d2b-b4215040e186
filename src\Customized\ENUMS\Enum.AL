enum 50000 ApprovalStatus
{
    Extensible = true;
    Value(0; Open)
    {

    }
    value(1; "Pending for Approval")
    {

    }
    value(2; Released)
    {

    }
}
enum 50158 TransApprovalStatus //CR220005-6-PKON22JA7
{
    Extensible = true;
    Value(0; Open)
    {

    }
    value(1; "Pending for Approval")
    {

    }
    value(2; Released)
    {

    }
    value(3; Closed)
    {

    }
}
enum 50003 PurchaseType
{
    Extensible = true;

    value(0; Import)
    {

    }
    value(1; "Local")
    {

    }
    value(2; "PMS")
    {

    }
    value(3; "Import File")
    {

    }
    value(4; "Service")
    {

    }
    Value(5; " ")
    {

    }
    value(6; "Import Charge")
    {

    }
}
enum 50004 SalesType
{
    Extensible = true;
    Value(0; "")
    {

    }
    value(1; Direct)
    {

    }

    value(2; "Local")
    {

    }
    value(3; Export)
    {

    }
    value(4; Complimentary)// >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9

    {

    }

}
enum 50005 CustomerCredittype
{
    Extensible = true;
    Value(0; "Cash n Carry")
    {

    }
    value(1; "Central")
    {

    }
    value(2; General)
    {
    }
}
enum 50006 TypeofBusiness
{
    Extensible = true;
    value(0; Retailer)
    {

    }
    value(1; Branch)
    {

    }
    value(2; "Key Customer")
    {

    }
    value(3; Scrap)
    {

    }
    value(4; Overseas)
    {

    }
    value(5; Staff)
    {

    }
}
enum 50007 OrderStatus
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; "Short Closed")
    {

    }
    value(2; "Cancelled")
    {

    }
    value(3; "Partially Short Closed")
    {

    }
    value(4; "Partially Cancelled")
    {

    }
}
enum 50008 PurcLineOrdrStatus
{
    Extensible = true;
    Value(0; " ")
    {

    }
    value(1; "Short Closed")
    {

    }
    value(2; Cancelled)
    {

    }
}
enum 50009 CustType
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; "Cash Customer")
    {

    }
    value(2; "Credit Customer")
    {

    }
}
enum 50010 DocType
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; Payment)
    {

    }
    value(2; Invoice)
    {

    }
}
enum 50011 PartyType
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; Vendor)
    {

    }
    value(2; Customer)
    {

    }
}
enum 50012 PaymentMethod
{
    Extensible = true;
    value(0; "G/L Account")
    {

    }
    value(1; "Bank Account")
    {

    }

}
enum 50013 OrderTracking
{
    Extensible = true;
    value(0; "Not Yet Shipped")
    {

    }
    value(1; "Partially Shipped")
    {

    }
    value(2; "Completely Shipped")
    {

    }
    value(3; "Partially Invoiced")
    {

    }
    value(4; "Completely Invoiced")
    {

    }
}
enum 50014 SubDocumentType
{
    Extensible = true;
    Value(0; " ")
    {

    }
    value(1; "Purchase Req.")
    {

    }
    value(2; "Purchase Order")
    {

    }
}
enum 50015 PurchDoctype
{
    Extensible = true;
    value(0; Quote)
    {

    }
    value(1; "Order")
    {

    }
    value(2; Invoice)
    {

    }
    value(3; "Credit Memo")
    {

    }
    value(4; "Blanket Order")
    {

    }
    value(5; "Return Order")
    {

    }
    value(6; Received)
    {

    }
    value(7; Invoiced)
    {

    }

}
enum 50016 FuelType
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; Petrol)
    {

    }
    value(2; Diesel)
    {

    }
    value(3; Gas)
    {

    }
    value(4; Electricity)
    {

    }
    value(5; Others)
    {

    }


}
enum 50017 DocumentType
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; Settlement)
    {

    }
    value(2; Payment)
    {

    }
    value(3; Close)
    {

    }
    value(4; Open)
    {

    }
}
enum 50018 VehicleType
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; Commercial)
    {

    }
    value(2; Private)
    {

    }
}
enum 50019 ServiceGroup
{
    Extensible = true;
    value(0; Supplier)
    {

    }
    value(1; "Overseas Suppliers")
    {

    }
    value(2; Contractor)
    {

    }
    value(3; Consultant)
    {

    }
    value(4; Hospital)
    {

    }
    value(5; PMS)
    {

    }
    value(6; Staff)
    {

    }
}
enum 50020 VehicleStatus
{
    Extensible = true;
    value(0; Open)
    {

    }
    Value(1; Release)
    {

    }
    Value(2; Close)
    {

    }
    value(3; "Pending For Approval")
    {

    }
}
enum 50021 TypeLoanSlip
{
    Extensible = true;
    value(0; Item)
    {

    }
}
enum 50022 VehicleTypes
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; "Mini Vehicle")
    {

    }
    value(2; Lorry)
    {

    }
    value(3; Truck)
    {

    }
    value(4; "Closed Vehicle")
    {

    }
    value(5; "Self Lifting")
    {

    }
    value(6; "Trailer")
    {

    }
    value(7; "Item-Rate Wise")
    {

    }
    value(8; Bus)
    {

    }
}
enum 50023 CompanyCusttype
{
    Extensible = true;
    value(0; "All Customers")
    {

    }
    value(1; "Customer Price Group")
    {

    }
    value(2; Customer)
    {

    }
}
enum 50024 PurcCmmntType
{
    Extensible = true;
    value(0; "Document Comments")
    {

    }
    value(1; "Terms & Conditions")
    {

    }
    value(2; "Archieve Reason")
    {

    }
    value(3; "Others")
    {

    }

}
enum 50025 ContractType
{
    Extensible = true;
    value(0; TripWise)
    {

    }
    value(1; BHN)
    {

    }
    value(2; Aglevinthis)
    {

    }
    value(3; TrayWise)
    {
        //B2B.P.K.T
    }
    value(4; PalletWise)
    {
        //B2B.P.K.T
    }
    //>PK On 24.04.2021
    value(5; "VT Leasing")
    {

    }
    value(7; "Dedicated") //>>G2S>>>19/8/25>>>CAS-01439-Q8F0H7
    {

    }
    value(6; " ")
    {

    }
    //<PK On 24.04.2021
}
enum 50026 "Transfer Request Type"
{
    Extensible = TRUE;
    value(0; " ")
    {

    }
    value(1; "Branch Request")
    {

    }
    value(2; "Location Transfer")
    {

    }
    value(3; "Transfer Ticket")
    {

    }
}
enum 50027 GateEntry
{

    Extensible = true;
    Value(0; " ")
    {

    }

    value(1; "Sales Return Order")
    {

    }
    value(2; "Purchase Order")
    {

    }

    value(3; "Transfer Receipt")
    {

    }

    value(4; "Sample")
    {

    }
}

enum 50028 GateEntryInOutWard
{
    Extensible = true;
    value(0; Inward)
    {

    }
    value(1; Outward)
    {

    }
}
enum 50029 ReasonCodes
{
    Extensible = true;
    value(0; "")
    {

    }
    value(1; Damage)
    {

    }
    value(2; "Packing Mistake")
    {

    }
    value(3; "Wrong Variant")
    {

    }
    value(4; "Wrong Product")
    {

    }
}
enum 50030 DocumentTypes
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; Promo)
    {

    }
    value(2; "Promo Group")
    {

    }
}
enum 50031 Status
{
    Extensible = true;
    value(0; "Open")
    {

    }
    value(1; Released)
    {

    }
    value(2; "Pending Approval")
    {

    }
    value(3; Closed)
    {

    }
}
enum 50032 Type
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; "G/L Account")
    {

    }
    value(2; Item)
    {

    }
    value(3; Resource)
    {

    }
    value(4; "Fixed Asset")
    {

    }
    value(5; "Charge (Item)")
    {

    }
    value(6; "Promo Group")
    {

    }

}
enum 50033 GateEntry1
{

    Extensible = true;
    Value(0; " ")
    {

    }
    value(1; "Sales Shipment")
    {

    }

    value(2; "Purchase Return Shipment")
    {

    }

    value(3; "Transfer Shipment")
    {

    }
    value(4; "Posted Loading Slip")
    {

    }
}
enum 50034 GateEntry2
{

    Extensible = true;
    Value(0; "")
    {

    }
    value(1; "Sales Shipment")
    {

    }

    value(2; "Purchase Return Shipment")
    {

    }

    value(3; "Transfer Shipment")
    {

    }
    value(4; "Posted Loading Slip")
    {

    }
    value(5; "Sales Return Order")
    {

    }
    value(6; "Purchase Order")
    {

    }

    value(7; "Transfer Receipt")
    {

    }
}

enum 50035 WareHouseSourceType
{
    Extensible = true;
    value(0; Receipt)
    {

    }
    value(1; Shipment)
    {

    }
}
enum 50036 PromoType
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; Group)
    {

    }

}
enum 50037 SchemeApplicable
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; "Pan Nigeria")
    {

    }
    value(2; "Restricted")
    {

    }
}
enum 50038 GateEntryType
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; RGP)
    {

    }
    value(2; NRGP)
    {

    }
}
enum 50039 "Dim. Document Type"
{
    Extensible = true;
    value(1; "Quote")
    {

    }
    value(2; Order)
    {

    }
    value(3; Invoice)
    {

    }

    value(4; "Credit Memo")
    {

    }
    value(5; "Blanket Order")
    {

    }
    value(6; "Return Order")
    {

    }
    value(7; JV)
    {

    }
    value(8; Payment)
    {

    }
    value(9; Receipt)
    {

    }
    value(10; IOU)
    {

    }
    value(11; Capex)
    {

    }
    value(12; Promo)
    {

    }
    value(13; "Promo Group")
    {

    }
    value(14; "RGP-Outward")
    {

    }
    value(15; "RGP-Inward")
    {

    }
    value(16; CPV)
    {

    }
    value(17; CRV)
    {

    }
    value(18; BPV)
    {

    }
    value(19; BRV)
    {

    }
}
enum 50040 AccountType
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; Bank)
    {

    }
    value(2; Cash)
    {

    }

    value(3; Loan)
    {

    }
    value(4; Overdraft)
    {

    }
    value(5; "Collection")
    {

    }
    value(6; "Cheques on hand")
    {

    }
    value(7; "Usance Facility")
    {

    }
    value(8; "Term Loan")
    {

    }
    value(9; BA)
    {
    }
}
enum 50041 "Teller Bank Name"
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; ZB)
    {

    }
    value(2; GTB)
    {

    }
    value(3; CITI)
    {

    }

    value(4; STANDARDCHART)
    {

    }
    value(5; WEMA)
    {

    }
    value(6; DIAMOND)
    {

    }
    value(7; SKY)
    {

    }
    value(8; STERLING)
    {

    }
    value(9; UBA)
    {

    }
    value(10; FBN)
    {

    }
    value(11; ACCESS)
    {

    }
    value(12; ECO)
    {

    }
    value(13; ETB)
    {

    }
    value(14; STANBIC)
    {

    }
    value(15; MAINSTREET)
    {

    }
    value(16; FIDELITY)
    {

    }
    value(17; CRV)
    {

    }
    value(18; KEYSTONE)
    {

    }
    value(19; ENTERPRISE)
    {

    }
    value(20; UNION)
    {

    }
    value(21; UNITY)
    {

    }
    value(22; CBN)
    {

    }
    value(23; FCMB)
    {

    }
    value(24; HERITAGE)
    {

    }
    value(25; FBNMERCHANT)
    {

    }
    value(26; RANDMERCHANT)
    {

    }
    value(27; CORONATION)
    {

    }
    //G2S 110324 CAS-01407-S2M2T7
    value(28; Parallex)
    { }
    value(29; "Premium Trust")
    { }
    value(30; Providus)
    { }
    value(31; Signature)
    { }
    value(32; SunTrust)
    { }
    value(33; "Titan Trust")
    { }
    value(34; Alternative)
    { }
    value(35; Jaiz)
    { }
    value(36; Lotus)
    { }
    value(37; TAJBank)
    { }
    value(38; Globus)
    { }
    value(39; Nova)
    { }
    value(40; Optimus)
    { }
    //G2S 110324 CAS-01407-S2M2T7
}
enum 50042 PaymentMode
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; Teller)
    {

    }
    value(2; Cheque)
    {

    }

    value(3; PDC)
    {

    }
    value(4; Transfer)
    {

    }
    value(5; Cash)
    {

    }
    value(6; "Other-Cheque")
    {

    }
    value(7; "Other-PDC")
    {

    }
    value(8; "Other-Transfer")
    {

    }
}
enum 50043 VoucherType
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; JV)
    {

    }
    value(2; CPV)
    {

    }

    value(3; CRV)
    {

    }
    value(4; BPV)
    {

    }
    value(5; BRV)
    {

    }
}
enum 50044 "Location Classification"
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; Branches)
    {

    }
    value(2; "Supply Chain")
    {

    }

    value(3; Engr)
    {

    }
    value(4; "Shop Floor")
    {

    }
    value(5; "RM Stores")
    {

    }
    value(6; "PM Stores")
    {

    }
    value(7; "Main Stores")
    {

    }

}
enum 50045 Acknowledge
{
    Extensible = true;
    value(0; Open)
    {

    }
    value(1; "In-Progress")
    {

    }
    value(2; Completed)
    {

    }
}
enum 50046 VehicleBy
{
    Extensible = true;
    value(0; Transporter)
    {

    }
    value(1; "Own Vehicle No.")
    {

    }
    value(2; "Customer(Self Lifting)")
    {

    }
    value(3; "Unregistered Transporter")
    {

    }
}
enum 50047 PurchaseOrderTracking
{
    Extensible = true;
    value(0; "Not Yet Received")
    {

    }
    value(1; "Partially Received")
    {

    }
    value(2; "Completely Received")
    {

    }
    value(3; "Partially Invoiced")
    {

    }
    value(4; "Completely Invoiced")
    {

    }
}
enum 50048 Capex
{
    Extensible = true;
    value(0; "")
    {
    }
    value(1; Capex)
    {
    }


}
enum 50049 CapexStatus
{

    Extensible = true;
    value(0; Open)
    {

    }
    value(1; Closed)

    {

    }
}
Enum 50050 DimDocumentType
{
    Extensible = TRUE;
    value(0; Quote)
    {

    }
    value(1; Order)
    {

    }
    Value(2; Invoice)
    {

    }
    Value(3; "Credit Memo")
    {

    }
    Value(4; "Blanket Order")
    {

    }
    Value(5; "Return Order")
    {

    }
    Value(6; "")
    {

    }
    Value(7; "Journal Voucher")
    {

    }
    value(8; Payment)
    {

    }
    value(9; Receipt)
    {

    }
    Value(10; IOU)
    {

    }
    Value(11; Capex)
    {

    }
    Value(12; Promo)
    {

    }
    Value(13; "Promo Group")
    {

    }
    value(14; "RGP-Outward")
    {

    }
    Value(15; "RGP-Inward")
    {

    }
    value(16; CPV)
    {


    }
    Value(17; CRV)
    {

    }
    Value(18; BPV)
    {

    }
    Value(19; BRV)
    {

    }
}
Enum 50051 ClassificationOfCapex
{
    Extensible = TRUE;
    value(0; "")
    {

    }
    value(1; "Manufacturing Operations")
    {

    }
    value(2; Projects)
    {

    }
    value(3; Utilities)
    {

    }
    value(4; "IT Infrastructure")
    {

    }
    value(5; "Building Maintenance")
    {

    }
    value(6; "Employee Benefit")
    {

    }
    Value(7; "General Maintenance")
    {

    }
    Value(8; "Sales Generating Assets")
    {
    }
    value(9; "MV Commercial")
    {

    }
    Value(10; "MV Private")
    {

    }
    Value(11; "Office Furniture")
    {

    }
    Value(12; Equipment)
    {
    }
    Value(13; "Material Handling Equipment")
    {

    }

}
enum 50052 NatureofExpenditure
{
    Extensible = TRUE;
    Value(0; "")
    {
    }
    Value(1; Planned)
    {

    }
    Value(2; Unplanned)
    {

    }
    Value(3; Exigency)
    {
    }
}
Enum 50053 CapexOpt
{

    Extensible = TRUE;
    Value(0; "")
    {
    }
    Value(1; Expansion)
    {

    }
    Value(2; Replacement)
    {

    }
}
Enum 50054 MRSLineType
{
    Extensible = TRUE;
    Value(0; "Fixed Asset")
    {

    }
    Value(1; Item)
    {

    }
    Value(2; "Charge(Item)")
    {

    }
}
Enum 50055 FAPostingType
{
    Extensible = TRUE;
    Value(0; "")
    {

    }
    Value(1; "Acquisition Cost")
    {

    }
    Value(2; Maintenance)
    {
    }
    Value(3; "capital work in progress")
    {

    }
}
Enum 50056 BudgetLineType
{
    Extensible = TRUE;
    Value(0; "")
    {

    }
    Value(1; "G/L Account")
    {

    }
    Value(2; Item)
    {

    }
    Value(3; Resource)
    {

    }
    Value(4; "Fixed Asset")
    {

    }
    Value(5; "Charge (Item)")
    {

    }
    Value(6; "FA Posting Group")
    {

    }

}
Enum 50057 CapexLineType
{
    Extensible = TRUE;
    Value(0; "")
    {

    }
    Value(1; "G/L Account")
    {

    }
    Value(2; Item)
    {

    }
    Value(3; Resource)
    {

    }
    Value(4; "Fixed Asset")
    {

    }
    Value(5; "Charge (Item)")
    {

    }
}
enum 50058 SubConStatus
{
    Extensible = TRUE;
    value(0; Open)
    { }
    value(1; Closed)
    { }
}
enum 50059 ContainerType
{
    Extensible = TRUE;
    Value(0; Sales)
    {

    }
    Value(1; Purchase)
    {

    }
}


enum 50060 PurchReqLineType
{
    Extensible = TRUE;
    value(0; "")
    { }
    value(1; "G/L Account")
    { }
    value(2; Item)
    { }
    value(3; "Fixed Asset")
    { }
}
enum 50061 BankName
{
    Extensible = true;
    value(0; "")
    { }
    value(1; ZB)
    { }
    value(2; GTB)
    { }
    value(3; CITI)
    { }
    value(4; STANDARDCHART)
    { }
    value(5; WEMA)
    { }
    value(6; DIAMOND)
    { }
    value(7; SKY)
    { }
    value(8; STERLING)
    { }
    value(9; UBA)
    { }
    value(10; FBN)
    { }
    value(11; ACCESS)
    { }
    value(12; ECO)
    { }
    value(13; ETB)
    { }
    value(14; STANBIC)
    { }
    value(15; MAINSTREET)
    { }
    value(16; FIDELITY)
    { }
    value(17; KEYSTONE)
    { }
    value(18; ENTERPRISE)
    { }
    value(19; UNION)
    { }
    value(20; UNITY)
    { }
    value(21; FCMB)
    { }
    value(22; HERITAGE)
    { }
    value(23; RMB)
    { }
    value(24; Polaris)
    { }
    //G2S 110324 CAS-01407-S2M2T7
    value(25; Globus)
    { }
    value(26; Nova)
    { }
    value(27; Optimus)
    { }
    value(28; Parallex)
    { }
    value(29; "Premium Trust")
    { }
    value(30; Providus)
    { }
    value(31; Signature)
    { }
    value(32; SunTrust)
    { }
    value(33; "Titan Trust")
    { }
    value(34; Alternative)
    { }
    value(35; Jaiz)
    { }
    value(36; Lotus)
    { }
    value(37; TAJBank)
    { }
    //G2S 110324 CAS-01407-S2M2T7
}
enum 50062 "Interest Method"
{
    value(1; "Interest Free")
    { }
    value(2; "Flat Rate")
    {

    }
    value(3; "Dimnishing Rate")
    {
    }

}
enum 50063 "Order Type"
{

    value(1; "G/L Account")
    {

    }
    value(2; "Item")
    {
    }
    value(3; "Fixed Asset")
    {

    }
    value(4; "Combination")
    {

    }

}
enum 50064 VehType
{
    value(0; "")
    {

    }
    value(1; Customer)
    {

    }
    value(2; "Hire Purchase")
    {

    }
}
enum 50065 ApplicationType
{
    value(0; Customer)
    {

    }
    value(1; "Customer Posting Group")
    {

    }
    value(2; "All Customers")
    {

    }
}
enum 50066 LoadingSlipType
{
    Value(0; Shipment)
    {

    }
    Value(1; Receipt)
    {

    }
}
enum 50067 ReleaseReopen
{
    Value(0; Open)
    {
    }
    Value(1; Released)
    {

    }
}
enum 50068 StatusField
{
    Value(0; Open)
    {
    }
    Value(1; PendingForApproval)
    {

    }
    value(2; Release)
    {
    }
}
enum 50069 EntryType
{
    Value(0; " ")
    {
    }
    Value(1; PendingForApproval)
    {

    }
    value(2; Release)
    {
    }
}
enum 50080 DeductionQty
{
    value(0; Shortage)
    {

    }
    value(1; Damage)
    {

    }
}
enum 50081 SharePointDocType
{
    value(0; "")
    {

    }
    value(1; "Purchase Order")
    {

    }
    value(2; "Purchase Invoice")
    {

    }
    value(3; "Purchase Returns")
    {

    }
    value(4; "Purchase Cr.Memo")
    {

    }
    value(5; "Sales Order")
    {

    }
    value(6; "Sales Invoice")
    {

    }
    value(7; "Sales Returns")
    {

    }
    value(8; "Sales Cr.Memo")
    {

    }
    value(9; "Transfer Order")
    {

    }
    value(10; "MRS")
    {

    }
    value(11; JV)
    {

    }
    value(12; "QUOTATION COMPARISIONS")
    {

    }
    value(13; "Capex Budget")
    {

    }
    value(14; "Purchase Quote")
    { }
    value(15; "Sales Quote")
    { }
    value(16; Item)
    { }
    value(17; Vendor)
    { }
    value(18; "G/L Account")
    { }
    value(19; "Fixed Asset")
    { }
    value(20; Customer)
    { }
    value(21; "Bank Account")
    { }

}

Enum 50082 PLSPReasonCode
{
    value(0; "")
    {

    }
    value(1; "Transport Shortage")
    {

    }
    value(2; "Transport Damage")
    {

    }
    value(3; "Cust. Adjustment")
    {

    }

}
Enum 50083 ModeofPayment
{

    value(1; Cash)
    {

    }
    value(2; Bank)
    {

    }
    value(3; "Branch Cash")
    {

    }

}
enum 50084 DocTypess
{
    value(0; Quote)
    {

    }
    value(1; Order)
    {
    }
    value(2; Invoice)
    {

    }
    value(3; "Credit Memo")
    {

    }
    value(4; "Blanket Order")
    {

    }
    value(5; "Return Order")
    {

    }
}
enum 50085 Typess
{
    value(0; " ")
    {

    }
    value(1; "G/L Account")
    {
    }
    value(2; Item)
    {

    }
    value(3; Resource)
    {

    }
    value(4; "Fixed Asset")
    {

    }
    value(5; "Charge(Item)")
    {

    }
}
enum 50086 Onetoseven
{
    value(0; " ")
    {

    }
    value(1; BUILDING)
    {

    }
    value(2; COMPUTERS)
    {
    }
    value(3; "EQUIP-OFF")
    {

    }
    value(4; "F&F OFFICE")
    {

    }
    value(5; "F&F RESIDE")
    {

    }
    value(6; "EQUIP-RES")
    {

    }
    value(7; "LEASELAND")
    {

    }
    value(8; MHEQUIP)
    {

    }
    value(9; MOTORVEHIC)
    {
    }
    value(10; MVCOM)
    {

    }
    value(11; PLANTMACHI)
    {

    }
    value(12; REEFCONT)
    {

    }
    value(13; ROADS)
    {

    }
    value(14; "COMP-SOFTW")
    {

    }
}
enum 50087 seventofourteen
{
    value(0; " ")
    {

    }
    value(1; BUILDING)
    {

    }
    value(2; COMPUTERS)
    {
    }
    value(3; "EQUIP-OFF")
    {

    }
    value(4; "F&F OFFICE")
    {

    }
    value(5; "F&F RESIDE")
    {

    }
    value(6; "EQUIP-RES")
    {

    }
    value(7; "LEASELAND")
    {

    }
    value(8; MHEQUIP)
    {

    }
    value(9; MOTORVEHIC)
    {
    }
    value(10; MVCOM)
    {

    }
    value(11; PLANTMACHI)
    {

    }
    value(12; REEFCONT)
    {

    }
    value(13; ROADS)
    {

    }
    value(14; "COMP-SOFTW")
    {

    }
}
enum 50088 WHTCalculation
{

    value(1; "Not Applicable")
    {

    }
    value(2; Federal)
    {
    }
    value(3; State)
    {

    }

}
enum 50089 OrderStat
{

    value(1; " ")
    {

    }
    value(2; "Short close")
    {
    }


}
enum 50090 SMTPLOgEntype
{
    value(0; "Customer Statement")
    {

    }
    value(1; "Despatch Mail-Trans")
    {
    }
    value(2; "Despatch Mail-Cust")
    {
    }
    value(3; "Despatch Mail-Branch")
    {
    }

}
enum 50096 "Cust Compl Status"//PKON22J2-CR220070
{
    value(0; Open)
    {

    }
    value(1; "In-Progress")
    {
    }
    value(2; "Resolved")
    {
    }

}

enum 50097 "SourcOfCompl"//PKON22J2-CR220070
{
    value(0; Customer)
    {

    }
    value(1; Public)
    {
    }
    value(2; Others)
    {
    }

}
enum 50095 "CustomerType" //PKON22M10-CR220063 
{
    value(0; " ")
    {

    }
    value(1; "General Trade")
    {
    }
    value(2; "Modern Trade")
    {
    }
    value(3; "Export & Retail")
    {
    }
    value(4; "OPM")
    {
    }


}
//RFC# 2024_17 and 18 >>
enum 50098 "SKU Category"
{
    value(0; " ")
    {

    }
    value(1; "Juice")
    {
    }
    value(2; "Yoghurt")
    {
    }
    value(3; "Hollandia Evaporated Milk")
    {
    }

}