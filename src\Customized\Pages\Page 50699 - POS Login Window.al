page 50923 "POS Login Window"
{
    // version AutomationERR

    PageType = Document;
    UsageCategory = Documents;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            field("UPPERCASE(USERID)"; UPPERCASE(USERID))
            {
                Caption = 'User ID';
                Editable = false;
            }
            field(Pass; Pass)
            {
                Caption = 'Password';
                ExtendedDatatype = Masked;
            }
            group("Password Change")
            {
                Caption = 'Password Change';
                field(ChangePass; ChangePass)
                {
                    Caption = 'Change Password';
                    Style = Strong;
                    StyleExpr = TRUE;

                    trigger OnValidate();
                    begin
                        ChangePassOnPush;
                        CurrPage.Update(true);
                    end;
                }
                field(CurrP; CurrPass)
                {
                    Caption = 'Current Password';
                    ExtendedDatatype = Masked;
                    Editable = CurrPVisible;
                    //Visible = CurrPVisible;
                }
                field(NewP; NewPass)
                {
                    Caption = 'New Password';
                    ExtendedDatatype = Masked;
                    Editable = NewPVisible;
                }
                field(ConP; ConfPass)
                {
                    Caption = 'Confirm Password';
                    ExtendedDatatype = Masked;
                    Editable = ConPVisible;
                }

            }
            field("Comp.Picture"; Comp.Picture)
            {
            }
            field(Control1000000000; '')
            {
                CaptionClass = Text19015060;
            }
        }
    }

    actions
    {
        area(processing)
        {
            action(Login)
            {
                Caption = 'Login';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    UserSetup.RESET;
                    UserSetup.SETRANGE("User ID", USERID);
                    IF UserSetup.FINDFIRST THEN BEGIN
                        IF UserSetup.Password <> '' THEN BEGIN
                            IF Pass = UserSetup.Password THEN BEGIN
                                ChiForm.RUNMODAL;
                                WrongPass := FALSE
                            END ELSE BEGIN
                                WrongPass := TRUE;
                                ERROR('User ID %1 Password do not Match \ Enter Valid Password or Contact System Administrator!!');
                            END;
                        END ELSE BEGIN
                            WrongPass := TRUE;
                            ERROR('Please Assign a Password, Contact System Admin!!');
                        END;
                    END;
                    CurrPage.CLOSE;
                end;
            }
            action(Change)
            {
                Caption = 'Change';
                Promoted = true;
                PromotedCategory = Process;
                Visible = ChangeVisible;

                trigger OnAction();
                begin
                    IF CONFIRM('Do you want to change the current Password %1?', FALSE, USERID) THEN BEGIN
                        UserSetup.RESET;
                        UserSetup.SETRANGE("User ID", USERID);
                        IF UserSetup.FINDFIRST THEN BEGIN
                            IF UserSetup.Password <> '' THEN BEGIN
                                IF CurrPass = UserSetup.Password THEN BEGIN
                                    IF ((NewPass <> '') AND (ConfPass <> '')) THEN BEGIN
                                        IF NewPass = ConfPass THEN BEGIN
                                            UserSetup.Password := ConfPass;
                                            UserSetup.MODIFY;
                                        END ELSE
                                            ERROR('New Password and Confirm Password Doesnot Match');
                                    END ELSE
                                        ERROR('Please Check if New Password or Confirm Password is not Empty');
                                END ELSE BEGIN
                                    ERROR('User ID %1 Current Password do not Match \ Enter Valid Password or Contact System Administrator!!');
                                END;
                            END ELSE BEGIN
                                ERROR('Please Assign a Password, Contact System Admin!!');
                            END;
                            MESSAGE('Password Changed Successfully');
                        END;
                    END;
                end;
            }
        }
    }

    trigger OnClosePage();
    begin
        IF Pass = '' THEN
            WrongPass := TRUE
        ELSE
            WrongPass := FALSE;
        IF WrongPass THEN BEGIN
            /*
            CREATE(WindowsShell);CHI WF
            WindowsShell.SendKeys('%{F1}');
            CLEAR(WindowsShell);
            *///CHI WF
        END;
    end;

    trigger OnInit();
    begin
        ChangeVisible := TRUE;
        ConPVisible := TRUE;
        NewPVisible := TRUE;
        CurrPVisible := TRUE;
    end;

    trigger OnOpenPage();
    begin
        IF Comp.GET THEN;
        Comp.CALCFIELDS(Picture);
        Comp.CALCFIELDS("Picture 2");
        /*
        CREATE(WindowsShell);CHI WF
        WindowsShell.SendKeys('%{F1}');
        CLEAR(WindowsShell);*///CHI WF

        CurrPVisible := FALSE;
        NewPVisible := FALSE;
        ConPVisible := FALSE;
        ChangeVisible := FALSE;
    end;

    var
        Comp: Record "Company Information";
        UserSetup: Record "User Setup";
        Pass: Text[100];
        ChiForm: Page "CHI POS list";
        //WindowsShell : Automation "'{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0:'{72C24DD5-D70A-438B-8A42-98424B88AFB8}':''{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0'.WshShell";
        WrongPass: Boolean;
        ChangePass: Boolean;
        CurrPass: Text[100];
        NewPass: Text[100];
        ConfPass: Text[100];
        [InDataSet]
        CurrPVisible: Boolean;
        [InDataSet]
        NewPVisible: Boolean;
        [InDataSet]
        ConPVisible: Boolean;
        [InDataSet]
        ChangeVisible: Boolean;
        Text19015060: Label 'CHI Retail POS Login';

    local procedure ChangePassOnPush();
    begin
        IF ChangePass THEN BEGIN
            CurrPVisible := TRUE;
            NewPVisible := TRUE;
            ConPVisible := TRUE;
            ChangeVisible := TRUE;
        END ELSE BEGIN
            CurrPVisible := FALSE;
            NewPVisible := FALSE;
            ConPVisible := FALSE;
            ChangeVisible := FALSE;
        END;
        CurrPage.Update(true);
    end;
}

