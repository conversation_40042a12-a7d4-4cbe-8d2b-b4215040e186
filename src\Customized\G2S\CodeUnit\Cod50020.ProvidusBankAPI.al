/// <summary>
/// Codeunit Providus Bank API (ID 50020).
/// </summary>
//G2S Providus Integration 7th Aug 2024 /// 
codeunit 50034 "Providus Bank API"
{
    TableNo = "Job Queue Entry";
    Permissions = tabledata "Bank API Credentials" = R, tabledata "Bank Payment Status" = R, tabledata "Bank Payment Code" = R, tabledata "Treasury Payment Notification" = RM;

    trigger OnRun()
    var
        TresPymntNot: Record "Treasury Payment Notification";
        BankAPI: Record "Bank API Credentials";
        VoucherHdr, VoucherHdr2 : Record "Voucher Header";
        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
        DocumentNo: Code[20];
        GLRegGRec: Record "G/L Register";
        VoucherPost: Codeunit "Voucher Post";
        Genjnlin2: Record "Gen. Journal Line 2";
        ApprvedBankPmtVouchers: Page "Apprved Bank Payment Vouchers";
        DocAttmtExt: Codeunit "Document Attachment Ext";
    begin

        case Rec."Parameter String" of
            'SENDPROVIDUSTXN':
                begin
                    BankAPI.Reset();
                    BankAPI.SetRange("Providus Bank?", true);
                    //check voucher for records not inserted when job queue is running
                    VoucherHdr.Reset();
                    VoucherHdr.SetFilter("Document Date", '>=1%', 20240725D);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    VoucherHdr.SetRange("Voucher Type", VoucherHdr."Voucher Type"::BPV);
                    VoucherHdr.SetRange("Account Type", VoucherHdr."Account Type"::"Bank Account");
                    VoucherHdr.SetRange("Account No.", BankAPI."Bank Account No.");
                    VoucherHdr.SetFilter("Bank Payment Status", '%1|%2', VoucherHdr."Bank Payment Status"::" ", VoucherHdr."Bank Payment Status"::New);
                    if VoucherHdr.FindFirst() then begin
                        repeat
                            TresPymntNot.Reset();
                            TresPymntNot.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                            if not TresPymntNot.FindFirst() then
                                InsertTreasuryNotificationRecord(VoucherHdr);
                        until VoucherHdr.Next() = 0;
                    end;

                    if BankAPI.FindFirst() then begin
                        TresPymntNot.Reset();
                        TresPymntNot.SetRange("Source Bank Code", BankAPI."Bank Account No.");
                        TresPymntNot.SetFilter(Status, '<>%1|<>%2', TresPymntNot.Status::Successful, TresPymntNot.Status::"Sent For Payment");
                        TresPymntNot.SetRange("Sent to Bank", false);
                        TresPymntNot.SetRange("Temp Omit", false);
                        TresPymntNot.SetRange("Date Sent to Bank", 0D);
                        if TresPymntNot.FindFirst() then begin
                            repeat
                                ProvidusAPIPayload(TresPymntNot);
                            until TresPymntNot.Next() = 0;
                        end;
                    end;
                end;
            'FETCHPROVIDUSTXNSTATUS':
                begin
                    BankAPI.Reset();
                    BankAPI.SetRange("Providus Bank?", true);
                    //check voucher for records not inserted when job queue is running
                    VoucherHdr.Reset();
                    VoucherHdr.SetFilter("Document Date", '>=1%', 20240725D);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    VoucherHdr.SetRange("Voucher Type", VoucherHdr."Voucher Type"::BPV);
                    VoucherHdr.SetRange("Account Type", VoucherHdr."Account Type"::"Bank Account");
                    VoucherHdr.SetRange("Account No.", BankAPI."Bank Account No.");
                    VoucherHdr.SetFilter("Bank Payment Status", '%1|%2', VoucherHdr."Bank Payment Status"::" ", VoucherHdr."Bank Payment Status"::New);
                    if VoucherHdr.FindFirst() then begin
                        repeat
                            TresPymntNot.Reset();
                            TresPymntNot.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                            if not TresPymntNot.FindFirst() then
                                InsertTreasuryNotificationRecord(VoucherHdr);
                        until VoucherHdr.Next() = 0;
                    end;

                    if BankAPI.FindFirst() then begin
                        TresPymntNot.Reset();
                        TresPymntNot.SetRange("Source Bank Code", BankAPI."Bank Account No.");
                        TresPymntNot.SetRange("Sent to Bank", true);
                        TresPymntNot.SetRange("Temp Omit", false);
                        TresPymntNot.SetFilter(Status, '<>%1', TresPymntNot.Status::Successful);
                        TresPymntNot.SetFilter("Date Sent to Bank", '<>%1', 0D);
                        if TresPymntNot.FindFirst() then begin
                            repeat
                                GetTransactionStatus(TresPymntNot);
                            until TresPymntNot.Next() = 0;
                        end;
                    end;
                end;
            'POSTPAIDBPV':
                begin
                    GeneralJrnlBtchLRec.Reset();
                    GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'BPV');
                    GeneralJrnlBtchLRec.SetRange(Name, 'BPV');
                    if GeneralJrnlBtchLRec.FindFirst() then begin
                        GeneralJrnlBtchLRec.TestField("No. Series", '');
                        GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                    end;

                    VoucherHdr.Reset();
                    VoucherHdr.SetCurrentKey("Bank Payment Status", Status);
                    VoucherHdr.SetRange("Document No.");
                    VoucherHdr.SetRange("Bank Payment Status", VoucherHdr."Bank Payment Status"::Successful);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    if VoucherHdr.FindSet() then begin
                        repeat
                            VoucherHdr2.Reset();
                            VoucherHdr2.Copy(VoucherHdr);

                            ClearValues(VoucherHdr2);
                            CheckAppliesAmounts(VoucherHdr2);
                            VoucherPost.RUN(VoucherHdr2);
                            Commit();
                            IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                                MoveAttachment(VoucherHdr2);
                                Sleep(2000);
                                CopyLinksAndNotes(VoucherHdr2);
                                Commit();
                            END;
                        // Message('Done with %1', VoucherHdr."Document No.");
                        until VoucherHdr.Next() = 0;
                    end;

                end;
            'SENDFIRSTBANKTXN':
                begin
                    BankAPI.Reset();
                    BankAPI.SetRange("First Bank?", true);
                    //check voucher for records not inserted when job queue is running
                    VoucherHdr.Reset();
                    VoucherHdr.SetFilter("Document Date", '>=1%', 20241013D);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    VoucherHdr.SetRange("Voucher Type", VoucherHdr."Voucher Type"::BPV);
                    VoucherHdr.SetRange("Account Type", VoucherHdr."Account Type"::"Bank Account");
                    VoucherHdr.SetRange("Account No.", BankAPI."Bank Account No.");
                    VoucherHdr.SetFilter("Bank Payment Status", '%1|%2', VoucherHdr."Bank Payment Status"::" ", VoucherHdr."Bank Payment Status"::New);
                    if VoucherHdr.FindFirst() then begin
                        repeat
                            TresPymntNot.Reset();
                            TresPymntNot.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                            if not TresPymntNot.FindFirst() then
                                InsertTreasuryNotificationRecord(VoucherHdr);
                        until VoucherHdr.Next() = 0;
                    end;

                    if BankAPI.FindFirst() then begin
                        TresPymntNot.Reset();
                        TresPymntNot.SetRange("Source Bank Code", BankAPI."Bank Account No.");
                        TresPymntNot.SetFilter(Status, '<>%1|<>%2', TresPymntNot.Status::Successful, TresPymntNot.Status::"Sent For Payment");
                        TresPymntNot.SetRange("Sent to Bank", false);
                        TresPymntNot.SetRange("Temp Omit", false);
                        TresPymntNot.SetRange("Date Sent to Bank", 0D);
                        if TresPymntNot.FindFirst() then begin
                            repeat
                                CreateSinglePaymentPayload(TresPymntNot, false); // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
                            until TresPymntNot.Next() = 0;
                        end;
                    end;
                end;
            'FETCHFIRSTBANKTXNSTATUS':
                begin
                    BankAPI.Reset();
                    BankAPI.SetRange("First Bank?", true);
                    //check voucher for records not inserted when job queue is running
                    VoucherHdr.Reset();
                    VoucherHdr.SetFilter("Document Date", '>=1%', 20241013D);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    VoucherHdr.SetRange("Voucher Type", VoucherHdr."Voucher Type"::BPV);
                    VoucherHdr.SetRange("Account Type", VoucherHdr."Account Type"::"Bank Account");
                    VoucherHdr.SetRange("Account No.", BankAPI."Bank Account No.");
                    VoucherHdr.SetFilter("Bank Payment Status", '%1|%2', VoucherHdr."Bank Payment Status"::" ", VoucherHdr."Bank Payment Status"::New);
                    if VoucherHdr.FindFirst() then begin
                        repeat
                            TresPymntNot.Reset();
                            TresPymntNot.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                            if not TresPymntNot.FindFirst() then
                                InsertTreasuryNotificationRecord(VoucherHdr);
                        until VoucherHdr.Next() = 0;
                    end;

                    if BankAPI.FindFirst() then begin
                        TresPymntNot.Reset();
                        TresPymntNot.SetRange("Source Bank Code", BankAPI."Bank Account No.");
                        TresPymntNot.SetRange("Sent to Bank", true);
                        TresPymntNot.SetRange("Temp Omit", false);
                        TresPymntNot.SetFilter(Status, '<>%1', TresPymntNot.Status::Successful);
                        TresPymntNot.SetFilter("Date Sent to Bank", '<>%1', 0D);
                        if TresPymntNot.FindFirst() then begin
                            repeat
                                FBGetPaymentStatus(TresPymntNot);
                            until TresPymntNot.Next() = 0;
                        end;
                    end;
                end;
            'SENDZENITHBANKTXN':
                begin
                    BankAPI.Reset();
                    BankAPI.SetRange("Zenith Bank?", true);
                    //check voucher for records not inserted when job queue is running
                    VoucherHdr.Reset();
                    VoucherHdr.SetFilter("Document Date", '>=1%', 20250701D);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    VoucherHdr.SetRange("Voucher Type", VoucherHdr."Voucher Type"::BPV);
                    VoucherHdr.SetRange("Account Type", VoucherHdr."Account Type"::"Bank Account");
                    VoucherHdr.SetRange("Account No.", BankAPI."Bank Account No.");
                    VoucherHdr.SetFilter("Bank Payment Status", '%1|%2', VoucherHdr."Bank Payment Status"::" ", VoucherHdr."Bank Payment Status"::New);
                    if VoucherHdr.FindFirst() then begin
                        repeat
                            TresPymntNot.Reset();
                            TresPymntNot.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                            if not TresPymntNot.FindFirst() then
                                InsertTreasuryNotificationRecord(VoucherHdr);
                        until VoucherHdr.Next() = 0;
                    end;

                    if BankAPI.FindFirst() then begin
                        TresPymntNot.Reset();
                        TresPymntNot.SetRange("Source Bank Code", BankAPI."Bank Account No.");
                        TresPymntNot.SetFilter(Status, '<>%1|<>%2', TresPymntNot.Status::Successful, TresPymntNot.Status::"Sent For Payment");
                        TresPymntNot.SetRange("Sent to Bank", false);
                        TresPymntNot.SetRange("Temp Omit", false);
                        TresPymntNot.SetRange("Date Sent to Bank", 0D);
                        if TresPymntNot.FindFirst() then begin
                            repeat
                                CreateZenithPaymentPayload(TresPymntNot, false);
                            until TresPymntNot.Next() = 0;
                        end;
                    end;
                end;
            'FETCHZENITHBANKTXNSTATUS':
                begin
                    BankAPI.Reset();
                    BankAPI.SetRange("Zenith Bank?", true);
                    //check voucher for records not inserted when job queue is running
                    VoucherHdr.Reset();
                    VoucherHdr.SetFilter("Document Date", '>=1%', 20250701D);
                    VoucherHdr.SetRange(Status, VoucherHdr.Status::Released);
                    VoucherHdr.SetRange("Voucher Type", VoucherHdr."Voucher Type"::BPV);
                    VoucherHdr.SetRange("Account Type", VoucherHdr."Account Type"::"Bank Account");
                    VoucherHdr.SetRange("Account No.", BankAPI."Bank Account No.");
                    VoucherHdr.SetFilter("Bank Payment Status", '%1|%2', VoucherHdr."Bank Payment Status"::" ", VoucherHdr."Bank Payment Status"::New);
                    if VoucherHdr.FindFirst() then begin
                        repeat
                            TresPymntNot.Reset();
                            TresPymntNot.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                            if not TresPymntNot.FindFirst() then
                                InsertTreasuryNotificationRecord(VoucherHdr);
                        until VoucherHdr.Next() = 0;
                    end;

                    if BankAPI.FindFirst() then begin
                        TresPymntNot.Reset();
                        TresPymntNot.SetRange("Source Bank Code", BankAPI."Bank Account No.");
                        TresPymntNot.SetRange("Sent to Bank", true);
                        TresPymntNot.SetRange("Temp Omit", false);
                        TresPymntNot.SetFilter(Status, '<>%1', TresPymntNot.Status::Successful);
                        TresPymntNot.SetFilter("Date Sent to Bank", '<>%1', 0D);
                        if TresPymntNot.FindFirst() then begin
                            repeat
                                FetchZenithPaymentRequest(TresPymntNot);
                            until TresPymntNot.Next() = 0;
                        end;
                    end;
                end;
        end;
    end;
    //Create Json payload NIPFundTransfer
    /// <summary>
    /// TreasurePaymentPayload.
    /// </summary>
    /// <param name="Var TreasuryTable">Record "Treasury Payment Notification".</param>
    procedure ProvidusAPIPayload(Var TreasuryTable: Record "Treasury Payment Notification")
    var
        JsonObj, JsonObj1, ResponseJson : JsonObject;
        JsonContent, Jsoncontent1 :
                Text;
        IsFundTransfered, IsProvidus :
                Boolean;
        BeneficiaryAcctName:
                Text[500];
        TxnSessionID, responseCode :
                Text[50];
        URLEndpoint1, responseMessage :
                Text[300];
        TreasuryTable2:
                Record "Treasury Payment Notification";
        ResponseToken, SessionToken, responseMessageTkn :
                JsonToken;
    begin
        Clear(JsonObj);
        Clear(SessionID);
        Clear(BeneficiaryAcctName);
        Clear(TreasuryTblCopy);
        Clear(TxnSessionID);
        Clear(ResponseJson);
        Clear(SessionToken);
        Clear(URLEndpoint);
        Clear(URLEndpoint1);
        TreasuryTable2.Reset();
        TreasuryTable2.Copy(TreasuryTable);
        TreasuryTable2.TestField("Source Bank Code");
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("Providus Bank?", true);
        BankAPICredentials.SetRange("Bank Account No.", TreasuryTable2."Source Bank Code");
        if not BankAPICredentials.FindFirst() then Error(BankDoesNtExistErr, TreasuryTable2."Source Bank Code");
        //Get BeneficiaryAcctName Payload
        JsonObj1.Add('accountNumber', TreasuryTable2."Beneficiary Acct. No.");
        JsonObj1.Add('beneficiaryBank', TreasuryTable2."Beneficiary Bank Code");
        JsonObj1.Add('userName', GetAccessDetails(BankAPICredentials, 'UserName'));
        JsonObj1.Add('password', GetAccessDetails(BankAPICredentials, 'Password'));
        JsonObj1.WriteTo(Jsoncontent1);

        URLEndpoint1 := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."NIP Account Endpoint");
        // Message('GetNIPAccount %1|%2', Jsoncontent1, URLEndpoint1);
        BeneficiaryAcctName := GetAccountName(JsonContent1, URLEndpoint1);

        IsProvidus := IsProvidusTransaction(TreasuryTable2."Beneficiary Bank Code");

        //SendTransfer Funds Payload
        if NOT IsProvidus then begin
            JsonObj.Add('beneficiaryAccountName', BeneficiaryAcctName);
            JsonObj.Add('transactionAmount', TreasuryTable2.Amount);
            JsonObj.Add('currencyCode', TreasuryTable2."Currency Code");
            JsonObj.Add('narration', TreasuryTable2.Narration);
            JsonObj.Add('sourceAccountName', TreasuryTable2."Source Acct Name");
            JsonObj.Add('beneficiaryAccountNumber', TreasuryTable2."Beneficiary Acct. No.");
            JsonObj.Add('beneficiaryBank', TreasuryTable2."Beneficiary Bank Code");
            JsonObj.Add('transactionReference', TreasuryTable2."Transaction Reference");
            JsonObj.Add('userName', GetAccessDetails(BankAPICredentials, 'UserName'));
            JsonObj.Add('password', GetAccessDetails(BankAPICredentials, 'Password'));
            JsonObj.WriteTo(JsonContent);
            URLEndpoint := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Fund Trf. Endpoint");
        end else begin
            JsonContent := ProvidusFundTransferPayload(TreasuryTable2);
            URLEndpoint := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Intra Fund Trf. Endpoint")
        end;

        // Message(JsonContent, URLEndpoint);
        ResponseJson := SendTransferFunds(JsonContent, URLEndpoint);

        if ResponseJson.Get('responseCode', ResponseToken) then
            if NOT (ResponseToken.AsValue().IsNull) then begin
                responseCode := ResponseToken.AsValue().AsText();

                if ResponseJson.Get('sessionId', SessionToken) then
                    if NOT (SessionToken.AsValue().IsNull) then
                        TxnSessionID := SessionToken.AsValue().AsText();

                if ResponseJson.Get('responseMessage', responseMessageTkn) then
                    if NOT (responseMessageTkn.AsValue().IsNull) then
                        responseMessage := responseMessageTkn.AsValue().AsText();
            end;

        // TxnSessionID := SendTransferFunds(JsonContent, GetEndPoint(TRUE));
        TreasuryTable2."DateTime Sent to Bank" := CurrentDateTime;
        TreasuryTable2."Beneficiary Acct. Name" := BeneficiaryAcctName;
        TreasuryTable2."Date Sent to Bank" := Today();
        //010824 Deji
        TreasuryTable2."Sent to Bank" := true;
        TreasuryTable2.Modify(true);
        UpdateReferenceTbl(TreasuryTable2, TxnSessionID, false, responseCode, responseMessage);
        BankNotifications(TreasuryTable2, responseCode, responseMessage, false);
    end;

    //Get Transaction status
    /// <summary>
    /// GetTransactionStatus.
    /// </summary>
    /// <param name="Var TreasuryTable">Record "Treasury Payment Notification".</param>
    procedure GetTransactionStatus(Var TreasuryTable: Record "Treasury Payment Notification")
    var
        JsonObj, ResponseJson : JsonObject;
        JsonContent: Text;
        TranSessionID, responseCode : Text[50];
        ResponseToken, TxnRef, responseMessageTkn : JsonToken;
        responseMessage: Text[300];
        TreasuryTable2:
                Record "Treasury Payment Notification";
        IsProvidus: Boolean;

    begin
        Clear(JsonObj);
        Clear(JsonContent);
        clear(TreasuryTblCopy);
        Clear(TranSessionID);
        Clear(URLEndpoint);
        Clear(ResponseJson);
        Clear(ResponseToken);
        Clear(TxnRef);
        Clear(responseMessageTkn);
        TreasuryTable2.Reset();
        TreasuryTable2.Copy(TreasuryTable);
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("Bank Account No.", TreasuryTable."Source Bank Code");
        if not BankAPICredentials.FindFirst() then Error(BankDoesNtExistErr, TreasuryTable."Source Bank Code");
        BankAPICredentials.TestField("Fund Trf. Endpoint");
        JsonObj.Add('transactionReference', TreasuryTable."Transaction Reference");
        JsonObj.Add('userName', GetAccessDetails(BankAPICredentials, 'UserName'));
        JsonObj.Add('password', GetAccessDetails(BankAPICredentials, 'Password'));
        JsonObj.WriteTo(JsonContent);
        IsProvidus := IsProvidusTransaction(TreasuryTable2."Beneficiary Bank Code");

        if IsProvidus then
            URLEndpoint := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Intra NIP Txn. Status Endpoint")
        else
            URLEndpoint := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."NIP Txn. Status Endpoint");


        // TranSessionID := SendTransferFunds(JsonContent, URLEndpoint);
        // Message(JsonContent, URLEndpoint);
        ResponseJson := SendTransferFunds(JsonContent, URLEndpoint);
        if ResponseJson.Get('responseCode', ResponseToken) then
            if NOT (ResponseToken.AsValue().IsNull) then begin
                responseCode := ResponseToken.AsValue().AsText();

                if ResponseJson.Get('transactionReference', TxnRef) then
                    if NOT (TxnRef.AsValue().IsNull) then
                        TranSessionID := TxnRef.AsValue().AsCode();

                if ResponseJson.Get('responseMessage', responseMessageTkn) then
                    if NOT (responseMessageTkn.AsValue().IsNull) then
                        responseMessage := responseMessageTkn.AsValue().AsText();
            end;
        if TranSessionID <> '' then begin
            TreasuryTable2."DateTime Sent to Bank" := CurrentDateTime;
            TreasuryTable2."Date Sent to Bank" := Today();
            TreasuryTable2.Modify(true);
            UpdateReferenceTbl(TreasuryTable2, TranSessionID, true, responseCode, responseMessage);
        end;
        BankNotifications(TreasuryTable2, responseCode, responseMessage, True);
    end;
    /// <summary>
    /// GetAccountName.
    /// </summary>
    /// <param name="Content">Text.</param>
    /// <param name="Url">Text.</param>
    /// <returns>Return value of type Text[500].</returns>
    procedure GetAccountName(Content: Text; Url: Text): Text[500]
    var
        ResponseText: Text;
        ResponseJson: JsonObject;
        ResponseToken, AcctName : JsonToken;
    begin
        Clear(HttpClient);
        Clear(HttpContent);
        Clear(HttpHeaders);
        Clear(HttpRequestMessage);
        Clear(HttpResponseMessage);
        Clear(ResponseJson);
        Clear(ResponseToken);
        HttpContent.WriteFrom(Content);
        HttpContent.GetHeaders(HttpHeaders);
        HttpHeaders.Remove('Content-Type');
        HttpHeaders.Add('Content-Type', 'application/json');
        HttpClient.SetBaseAddress(Url);
        HttpClient.Post(Url, HttpContent, HttpResponseMessage);

        if (HttpResponseMessage.IsSuccessStatusCode) then begin
            HttpResponseMessage.Content.ReadAs(ResponseText);
            Message(ResponseText);
            ResponseJson.ReadFrom(ResponseText);
            ResponseJson.Get('accountName', AcctName);
            if NOT (AcctName.AsValue().IsNull) then begin
                EXIT(AcctName.AsValue().AsText());
            end
        end;
    end;


    //Send Payload As request and get response
    /// <summary>
    /// SendTransferFunds.
    /// </summary>
    /// <param name="Content">Text.</param>
    /// <param name="Url">Text.</param>
    /// <returns>Return value of type Boolean.</returns>
    procedure SendTransferFunds(Content: Text; Url: Text): JsonObject
    var
        ResponseText: Text;
        ResponseJson: JsonObject;
        ResponseToken, SessionToken, TxnRef : JsonToken;
    begin
        Clear(HttpClient);
        Clear(HttpContent);
        Clear(HttpHeaders);
        Clear(HttpRequestMessage);
        Clear(HttpResponseMessage);
        Clear(ResponseJson);
        Clear(ResponseToken);
        HttpContent.WriteFrom(Content);
        HttpContent.GetHeaders(HttpHeaders);
        HttpHeaders.Remove('Content-Type');
        HttpHeaders.Add('Content-Type', 'application/json');
        HttpClient.SetBaseAddress(Url);
        HttpClient.Post(Url, HttpContent, HttpResponseMessage);
        // Message(URL);
        if (HttpResponseMessage.IsSuccessStatusCode) then begin
            HttpResponseMessage.Content.ReadAs(ResponseText);
            Message(ResponseText);
            ResponseJson.ReadFrom(ResponseText);
            exit(ResponseJson);
        end;
    end;

    procedure ProvidusFundTransferPayload(var TreasuryTable: Record "Treasury Payment Notification"): TEXT
    var
        JsonObj: JsonObject;
        BankAPI: Record "Bank API Credentials";
        JsonContent: Text;
        TreasuryTable2:
                Record "Treasury Payment Notification";
    begin
        clear(JsonObj);
        Clear(JsonContent);
        TreasuryTable2.Reset();
        TreasuryTable2.Copy(TreasuryTable);
        BankAPI.Reset();
        BankAPI.SetRange("Providus Bank?", true);
        if BankAPI.FindFirst() then begin
            JsonObj.Add('creditAccount', TreasuryTable2."Beneficiary Acct. No.");
            JsonObj.Add('debitAccount', BankAPI."Bank Account Number");
            JsonObj.Add('transactionAmount', TreasuryTable2.Amount);
            JsonObj.Add('currencyCode', BankAPI."Currency Code");
            JsonObj.Add('narration', TreasuryTable2.Narration);
            JsonObj.Add('transactionReference', TreasuryTable2."Transaction Reference");
            JsonObj.Add('userName', GetAccessDetails(BankAPI, 'UserName'));
            JsonObj.Add('password', GetAccessDetails(BankAPI, 'Password'));
            JsonObj.WriteTo(JsonContent);
            EXIT(JsonContent);
        end;
    end;

    procedure GetProvidusAccount()
    var
        BankAPI: Record "Bank API Credentials";
        JsonObj: JsonObject;
        URLEndpoint1, JsonContent : TEXT[300];
    begin
        Clear(JsonObj);
        Clear(URLEndpoint1);
        Clear(JsonContent);
        BankAPI.Reset();
        BankAPI.SetRange("Providus Bank?", true);
        if BankAPI.FindFirst() then begin
            BankAPI.TestField("Bank Account No.");
            BankAPI.TestField("Bank Account Number");
            JsonObj.Add('accountNumber', BankAPI."Bank Account Number");
            JsonObj.Add('userName', GetAccessDetails(BankAPI, 'UserName'));
            JsonObj.Add('password', GetAccessDetails(BankAPI, 'Password'));
            JsonObj.WriteTo(JsonContent);

            URLEndpoint1 := StrSubstNo('%1/%2', GetAccessDetails(BankAPI, 'URL'), BankAPI."Intra NIP Account Endpoint");
            GetAccountName(JsonContent, URLEndpoint1);
        end
    end;

    procedure IsProvidusTransaction(BankCode: Code[50]): Boolean
    var
        BankCode2: Text;
    begin
        BankPmtCode.Reset();
        BankPmtCode.SetCurrentKey("Entry No");
        BankPmtCode.SetRange("Providus Bank Code", BankCode);
        if BankPmtCode.FindFirst() then begin
            BankCode2 := UpperCase(BankPmtCode."Bank Name");
            if BankCode2.Contains('PROVIDUS') then
                exit(True);
        end;
    end;

    procedure FirstBankSessionID(): Text[200]
    var
        AccessObj: JsonObject;
        ResponseJtoken: JsonToken;
        SessionObj: JsonObject;
        FetchResponseObj: JsonObject;
        HttpHeadersContent: HttpHeaders;
        JsonContent, ResponseText : Text;
    begin
        // Get Session ID for Transaction Authentication
        Clear(URL);
        clear(HttpResponseMessage);
        clear(HttpRequestMessage);
        clear(HttpClient);
        clear(HttpContent);
        clear(AccessObj);
        Clear(HttpHeadersContent);
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("First Bank?", true);
        if BankAPICredentials.FindFirst() then begin
            SessionObj.Add('corpcode', GetAccessDetails(BankAPICredentials, 'UserName'));
            SessionObj.Add('password', GetAccessDetails(BankAPICredentials, 'Password'));
            SessionObj.WriteTo(JsonContent);
            URL := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Session Endpoint");
            HttpContent.WriteFrom(JsonContent);
            HttpContent.GetHeaders(HttpHeaders);
            HttpHeaders.Remove('Content-Type');
            HttpHeaders.Add('Content-Type', 'application/json');
            HttpClient.SetBaseAddress(URL);
            HttpClient.Post(URL, HttpContent, HttpResponseMessage);

            // if GuiAllowed then Message('%1|%2', URL, JsonContent);

            if (HttpResponseMessage.IsSuccessStatusCode) then begin
                HttpResponseMessage.Content.ReadAs(ResponseText);
                FetchResponseObj.ReadFrom(ResponseText);
                // if GuiAllowed then
                //     Message(ResponseText);
                FetchResponseObj.get('success', ResponseJtoken);
                if not (ResponseJtoken.AsValue().IsNull) then begin
                    if ResponseJtoken.AsValue().AsText() = 'true' then begin
                        FetchResponseObj.get('session', ResponseJtoken);
                        exit(ResponseJtoken.AsValue().AsText())
                    end;
                    exit('NULL');
                end;
                exit('NULL');
            end;

        end;
    end;

    //FirstBank
    procedure ValidateFBAcctNumber(Var TreasuryTable: Record "Treasury Payment Notification"): Text
    var
        destinationBankCode, AcctNameTxt : Text;
        ResponseJtoken, SessionResponseJtoken, AcctName : JsonToken;
        FetchResponseObj, AcctObj : JsonObject;
        HttpHeadersContent: HttpHeaders;
        BankAPICredential: Record "Bank API Credentials";
        URL, contentVar, ResponseText : Text;
        TreasuryTable2: Record "Treasury Payment Notification";
        HttpClients: HttpClient;
    begin
        TreasuryTable2.Reset();
        HttpResponseMessage.Headers.Clear();
        Clear(SessionResponseJtoken);
        Clear(ResponseJtoken);
        clear(contentVar);
        clear(AcctObj);
        Clear(AcctName);
        clear(HttpResponseMessage);
        clear(HttpRequestMessage);
        clear(HttpClients);
        clear(HttpContent);
        Clear(HttpHeadersContent);
        TreasuryTable2.Copy(TreasuryTable);
        BankAPICredential.Reset();
        BankAPICredential.SetRange("First Bank?", true);
        if BankAPICredential.FindFirst() then begin
            Clear(SessionID);
            SessionID := FirstBankSessionID();
            URL := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredential, 'URL'), BankAPICredential."NIP Account Endpoint");
            // Message(URL);
            AcctObj.Add('corpcode', GetAccessDetails(BankAPICredential, 'UserName'));
            AcctObj.Add('session', SessionID);
            AcctObj.Add('destinationAccount', TreasuryTable2."Beneficiary Acct. No.");
            AcctObj.Add('destinationBankCode', TreasuryTable2."Beneficiary Bank Code");
            AcctObj.WriteTo(contentVar);

            GetAccountName(contentVar, URL);

            // HttpContent.Clear();
            // HttpContent.WriteFrom(contentvar);
            // // HttpContent.GetHeaders(HttpHeadersContent);
            // // HttpHeadersContent.Add('Content-Type', 'application/json');
            // HttpClients.SetBaseAddress(url);
            // HttpClients.Post(URL, HttpContent, HttpResponseMessage);


            // if (HttpResponseMessage.IsSuccessStatusCode) then begin
            //     HttpResponseMessage.Content.ReadAs(ResponseText);
            //     if GuiAllowed then Message(ResponseText);
            //     FetchResponseObj.ReadFrom(ResponseText);
            //     if FetchResponseObj.get('valid', ResponseJtoken) then
            //         if not (ResponseJtoken.AsValue().IsNull) then begin
            //             if ResponseJtoken.AsValue().AsText() = 'true' then begin
            //                 FetchResponseObj.Get('accountName', AcctName);
            //                 if not (AcctName.AsValue().IsNull) then begin
            //                     AcctNameTxt := AcctName.AsValue().AsText();
            //                     // TreasuryTable2."Beneficiary Acct. Name" := AcctNameTxt;
            //                     // TreasuryTable2.Modify();
            //                     // Commit();
            //                     EXIT(AcctNameTxt);
            //                 end;
            //             end;
            //         end;
            // end;
        end;
    end;

    //FirstBank
    procedure CreateBatchPaymentPayload(Var Rec: Record "Treasury Payment Notification")
    var
        VoidCount: Integer;
        totalItems: Integer;
        totalValue: Decimal;
        singleDebit: Boolean;
        IsPaymentProcessed: Boolean;
        EndPointURL, JsonContent, corpCode, BatchReference, Password, BatchNarration, PayLoadReferrence, BankCode : Text;
        TransObjArray: JsonArray;
        masterJsonObj, TransObj, ResponseObject : JsonObject;
        TreasuryTable: Record "Treasury Payment Notification";
        BankAPICredentials: Record "Bank API Credentials";
    begin
        Clear(BankCode);
        Clear(EndPointURL);
        Clear(corpCode);
        Clear(BatchReference);
        Clear(BatchNarration);
        Clear(TreasuryTable);
        Clear(masterJsonObj);
        Clear(TransObjArray);
        Clear(TransObj);
        Clear(SessionID);
        Clear(ResponseObject);
        totalItems := 0;
        totalValue := 0;
        VoidCount := 0;
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("First Bank?", true);
        if BankAPICredentials.FindFirst() then begin
            TreasuryTable.Copy(Rec);
            if not TreasuryTable.IsEmpty() then begin
                BatchReference := TreasuryTable."Payment Voucher No." + Format(TreasuryTable."Line No.") + Format(Today);
                BatchNarration := TreasuryTable."Payment Voucher No." + 'FBN Batch Payment for ' + Format(Today);

                EndPointURL := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Fund Trf. Endpoint");

                CorpCode := GetAccessDetails(BankAPICredentials, 'UserName');
                SessionID := FirstBankSessionID();

                masterJsonObj.Add('corpCode', CorpCode);
                masterJsonObj.Add('session', SessionID);
                masterJsonObj.Add('batchReference', BatchReference);
                masterJsonObj.Add('batchNarration', BatchNarration);
                if TreasuryTable.FindSet() then begin
                    repeat
                        // if ValidateFBAcctNumber(TreasuryTable) <> '' then begin
                        TransObj.Add('sourceAccount', BankAPICredentials."Bank Account Number");
                        TransObj.Add('destinationAccount', TreasuryTable."Beneficiary Acct. No.");
                        TransObj.Add('destinationBank', TreasuryTable."Beneficiary Bank Code");
                        TransObj.Add('paymentRef', TreasuryTable."Transaction Reference");
                        TransObj.Add('narration', TreasuryTable.Narration);
                        TransObj.Add('amount', TreasuryTable.Amount);
                        TransObj.Add('valueDate', Format(Today, 0, '<Day,2>-<Month,3>-<Year>'));
                        TransObjArray.Add(TransObj.Clone());
                        totalItems += 1;
                        totalValue += TreasuryTable.Amount;
                    // end;
                    until (VoidCount >= 1) or (TreasuryTable.Next() = 0);
                    singleDebit := true;
                end;

                masterJsonObj.Add('items', TransObjArray);
                masterJsonObj.Add('totalItems', totalItems);
                masterJsonObj.Add('totalValue', totalValue);
                masterJsonObj.Add('singleDebit', singleDebit);

                masterJsonObj.WriteTo(JsonContent);

                ResponseObject := SendTransferFunds(JsonContent, EndPointURL);

                //update Treasury  and Bank Notification Table
                // if IsPaymentProcessed then begin
                //     GetPaymentStatus(TransObjArray);
                // end;
            end;
        end;
    end;

    //FirstBank
    procedure CreateSinglePaymentPayload(Var Rec: Record "Treasury Payment Notification"; Requery: Boolean)
    var
        VoidCount: Integer;
        totalItems: Integer;
        totalValue: Decimal;
        singleDebit: Boolean;
        IsPaymentProcessed: Boolean;
        EndPointURL, JsonContent, corpCode, BatchReference, Password, BatchNarration, PayLoadReferrence, BankCode : Text;
        TransObjArray: JsonArray;
        masterJsonObj, TransObj, ResponseObject : JsonObject;
        TreasuryTable: Record "Treasury Payment Notification";
        BankAPICredentials: Record "Bank API Credentials";
        NewpaymentRef: Text[100];
    begin
        Clear(BankCode);
        Clear(EndPointURL);
        Clear(corpCode);
        Clear(BatchReference);
        Clear(BatchNarration);
        Clear(TreasuryTable);
        Clear(masterJsonObj);
        Clear(TransObjArray);
        Clear(TransObj);
        Clear(FBSession);
        Clear(ResponseObject);
        totalItems := 0;
        totalValue := 0;
        VoidCount := 0;
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("First Bank?", true);
        if BankAPICredentials.FindFirst() then begin
            TreasuryTable.Copy(Rec);
            if not TreasuryTable.IsEmpty() then begin
                BatchReference := TreasuryTable."Payment Voucher No." + Format(TreasuryTable."Line No.") + Format(Today);
                BatchNarration := TreasuryTable."Payment Voucher No." + 'FBN Batch Payment for ' + Format(Today);

                EndPointURL := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Fund Trf. Endpoint");

                CorpCode := GetAccessDetails(BankAPICredentials, 'UserName');
                FBSession := FirstBankSessionID();

                masterJsonObj.Add('corpCode', CorpCode);
                masterJsonObj.Add('session', FBSession);
                // if ValidateFBAcctNumber(TreasuryTable) <> '' then begin
                TransObj.Add('sourceAccount', BankAPICredentials."Bank Account Number");
                TransObj.Add('destinationAccount', TreasuryTable."Beneficiary Acct. No.");
                TransObj.Add('destinationBank', TreasuryTable."Beneficiary Bank Code");
                //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3
                IF Requery THEN BEGIN
                    NewpaymentRef := ReplaceLastValue(TreasuryTable."Transaction Reference");
                    TransObj.Add('paymentRef', NewpaymentRef);
                END ELSE
                    TransObj.Add('paymentRef', TreasuryTable."Transaction Reference");
                TransObj.Add('narration', TreasuryTable.Narration);
                TransObj.Add('amount', TreasuryTable.Amount);
                TransObj.Add('valueDate', Format(Today, 0, '<Day,2>-<Month,3>-<Year>'));
                // end;
            end;

            masterJsonObj.Add('item', TransObj);

            masterJsonObj.WriteTo(JsonContent);
            // Message(Format(JsonContent));
            ResponseObject := SendTransferFunds(JsonContent, EndPointURL);
            //update Treasury  and Bank Notification Table
            FirstBankUpdateReferenceTbl(TreasuryTable, ResponseObject, false, 0, '', NewpaymentRef);
            FirstBankNotifications(TreasuryTblCopy, ResponseObject);
        end;
    end;

    //FirstBank Get Payment Status
    procedure FBGetPaymentStatus(Rec: Record "Treasury Payment Notification"): Text
    var
        TransReference: Code[100];
        MessageJtoken, StatusCodeTkn, RemarkTkn, TransjsonTokens, pmtRefToken : JsonToken;
        FetchResponseObj, PaymentObj, transObj : JsonObject;
        HttpHeadersContent: HttpHeaders;
        URLEndPoint, contentvar, ResponseText, RemarkTxt, MessgeTxt, SessionID, CorpCode, PmtRefTxt
        : Text;
        TreasuryTable: Record "Treasury Payment Notification";
        StatusCode: Integer;

    begin
        HttpResponseMessage.Headers.Clear();
        Clear(MessageJtoken);
        Clear(StatusCodeTkn);
        Clear(RemarkTkn);
        Clear(pmtRefToken);
        clear(contentvar);
        clear(transObj);
        Clear(URLEndPoint);
        clear(PaymentObj);
        clear(HttpResponseMessage);
        clear(HttpRequestMessage);
        clear(HttpClient);
        clear(HttpContent);
        Clear(HttpHeadersContent);
        TreasuryTable.Reset();
        TreasuryTable.Copy(Rec);
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("First Bank?", true);
        if BankAPICredentials.FindFirst() then begin
            // if TreasuryTable.FindFirst() then begin
            SessionID := FirstBankSessionID();
            clear(HttpClient);
            URLEndPoint := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."NIP Txn. Status Endpoint");

            corpCode := GetAccessDetails(BankAPICredentials, 'UserName');
            transObj.Add('corpCode', corpCode);
            transObj.Add('session', SessionID);
            transObj.Add('reference', TreasuryTable."Transaction Reference");
            transObj.WriteTo(contentvar);
            HttpContent.WriteFrom(contentvar);
            FetchResponseObj := SendTransferFunds(contentvar, URLEndPoint);

            // HttpContent.GetHeaders(HttpHeaders);
            // HttpHeaders.Remove('Content-Type');
            // HttpHeaders.Add('Content-Type', 'application/json');
            // HttpClient.SetBaseAddress(URLEndPoint);
            // HttpClient.Post(URLEndPoint, HttpContent, HttpResponseMessage);
            // Message('URL-%1...Content-%2', URLEndPoint, contentvar);
            // if (HttpResponseMessage.IsSuccessStatusCode) then begin
            //     HttpResponseMessage.Content.ReadAs(ResponseText);
            // FetchResponseObj.ReadFrom(ResponseText);

            FetchResponseObj.Get('paymentRef', pmtRefToken);
            if not (pmtRefToken.AsValue().IsNull) then begin
                PmtRefTxt := pmtRefToken.AsValue().AsText();
            end;

            FetchResponseObj.Get('remark', RemarkTkn);
            if not (RemarkTkn.AsValue().IsNull) then begin
                RemarkTxt := RemarkTkn.AsValue().AsText();
            end;

            FetchResponseObj.get('message', MessageJtoken);
            if NOT (MessageJtoken.AsValue().IsNull) then begin
                MessgeTxt := MessageJtoken.AsValue().AsText();
            end;

            FetchResponseObj.Get('status', StatusCodeTkn);
            if NOT (StatusCodeTkn.AsValue().IsNull) then begin
                StatusCode := StatusCodeTkn.AsValue().AsInteger();
            end;
            // TreasuryTable."DateTime Sent to Bank" := CurrentDateTime;
            // TreasuryTable."Date Sent to Bank" := Today();
            // TreasuryTable.Modify(true);
            // Commit();
            FirstBankUpdateReferenceTbl(TreasuryTable, FetchResponseObj, True, StatusCode, MessgeTxt, '');// >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
            UpdateBANKNotification(True, PmtRefTxt, FORMAT(StatusCode), RemarkTxt)
            // end;
        end;
        // end;
    END;

    /// <summary>
    /// GetAccountDetails.
    /// </summary>
    /// <param name="BankPaySetup">VAR Record "Bank API Credentials".</param>
    /// <param name="field">VAR Text[10].</param>
    /// <returns>Return value of type Text.</returns>
    procedure GetAccessDetails(var BankPaySetup: Record "Bank API Credentials"; field: Text[10]): Text
    var
        MyInStream: InStream;
        Token: Text[2048];

    begin
        Token := '';

        case field of
            'Password':
                begin
                    BankPaySetup.Calcfields(Password);
                    If BankPaySetup.Password.HasValue() then begin
                        BankPaySetup.Password.CreateInStream(MyInStream);
                        MyInStream.Read(Token);
                        exit(Token);
                    end;
                end;

            'URL':
                begin
                    BankPaySetup.Calcfields("Base Url");
                    If BankPaySetup."Base Url".HasValue() then begin
                        BankPaySetup."Base Url".CreateInStream(MyInStream);
                        MyInStream.Read(Token);
                        exit(Token);
                    end;
                end;

            'UserName':
                begin
                    BankPaySetup.Calcfields(Username);
                    If BankPaySetup.Username.HasValue() then begin
                        BankPaySetup.Username.CreateInStream(MyInStream);
                        MyInStream.Read(Token);
                        exit(Token);
                    end;
                end;
        end;
    end;

    local procedure UpdateReferenceTbl(var TreasuryNotification: Record "Treasury Payment Notification"; var SessionID: Text[50]; TxnStatus: Boolean; responseCode: Text[50]; ResponseDesc: Text[500])
    var
        myInt: Integer;
        VoucherHdr: Record "Voucher Header";
    begin
        VoucherHdr.Reset();
        VoucherHdr.SetRange("Document No.", TreasuryNotification."Payment Voucher No.");
        TreasuryTblCopy.Copy(TreasuryNotification);
        TreasuryTblCopy."DateTime Sent to Bank" := CurrentDateTime;
        TreasuryTblCopy."Date Sent to Bank" := Today();
        if TxnStatus then begin
            if responseCode = '00' then begin
                //Modify TreasuryTable 
                TreasuryTblCopy."Bank Transaction Reference" := SessionID;
                TreasuryTblCopy.Status := TreasuryTblCopy.Status::Successful;
                TreasuryTblCopy."Transaction Date" := Today();
                TreasuryTblCopy."Transaction DateTime" := CurrentDateTime;
                TreasuryTblCopy."Bank Status Code" := responseCode;
                TreasuryTblCopy."Bank Status Code Desc." := ResponseDesc;
                if VoucherHdr.FindFirst() then begin
                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Sent For Payment";
                    VoucherHdr."Transaction Date" := Today;
                    VoucherHdr."Transaction DateTime" := CurrentDateTime;
                end;
            end else begin
                TreasuryTblCopy.Status := TreasuryTblCopy.Status::Failed;
                if VoucherHdr.FindFirst() then begin
                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::Failed;
                    VoucherHdr."Transaction Date" := Today;
                    VoucherHdr."Transaction DateTime" := CurrentDateTime;
                end;
            end;
            //deji 010824
            TreasuryTblCopy."Updated from Bank" := true;
            TreasuryTblCopy."Date Updated From Bank" := Today;
            TreasuryTblCopy."DateTime Updated From Bank" := CurrentDateTime;
            VoucherHdr.Modify();
            TreasuryTblCopy.Modify();
        end else begin
            if ((responseCode = '00') OR (responseCode = '7709')) then begin
                //Modify TreasuryTable 
                TreasuryTblCopy."Session ID" := SessionID;
                TreasuryTblCopy.Status := TreasuryTblCopy.Status::"Sent For Payment";
                TreasuryTblCopy."Transaction Date" := Today();
                TreasuryTblCopy."Transaction DateTime" := CurrentDateTime;
                TreasuryTblCopy."Bank Status Code" := responseCode;
                TreasuryTblCopy."Bank Status Code Desc." := ResponseDesc;
                if VoucherHdr.FindFirst() then begin
                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Sent For Payment";
                    VoucherHdr."Transaction Date" := Today;
                    VoucherHdr."Transaction DateTime" := CurrentDateTime;
                end;
            end else begin
                TreasuryTblCopy.Status := TreasuryTblCopy.Status::"Pending Update";
                if VoucherHdr.FindFirst() then begin
                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Pending Update";
                    VoucherHdr."Transaction Date" := Today;
                    VoucherHdr."Transaction DateTime" := CurrentDateTime;
                end;
            end;
            //deji 010824
            TreasuryTblCopy."Updated from Bank" := true;
            TreasuryTblCopy."Date Updated From Bank" := Today;
            TreasuryTblCopy."DateTime Updated From Bank" := CurrentDateTime;
            VoucherHdr.Modify();
            TreasuryTblCopy.Modify();
            Commit();
        end;
        IF CheckTransationsPerVoucher(VoucherHdr."Document No.") THEN BEGIN
            VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::Successful;//Check all lines before this
            VoucherHdr.Modify();
            Commit();
        END;
    end;


    procedure FirstBankUpdateReferenceTbl(var TreasuryNotification: Record "Treasury Payment Notification"; ResponseObject: JsonObject; FBPmtStatus: Boolean; PmtStatus: Integer; PmtMessage: Text; NewpaymentRef: Text)
    var
        myInt: Integer;
        VoucherHdr: Record "Voucher Header";
        BankPmtStatus: Record "Bank Payment Status";
        success, UniqueBulkPaymentId, PaymentItemToken, StatusTkn, PaymentIDTkn, MessageTkn, RemarkTkn, PaymentItemTkn
         : JsonToken;
        PaymentID, MessageTxt, RemarkTxt, UniqueBulkID : Text;
        PaymentItemObj: JsonObject;
        PaymentItemArray: JsonArray;
        status: Integer;

    begin
        TreasuryTblCopy.RESET();
        TreasuryTblCopy.Copy(TreasuryNotification);
        IF NOT FBPmtStatus THEN BEGIN
            if ResponseObject.GET('success', success) THEN
                if not (success.AsValue().IsNull) then
                    if success.AsValue().AsBoolean() then BEGIN
                        // Get UniqueBulkPaymentId
                        ResponseObject.GET('uniqueBulkPaymentId', UniqueBulkPaymentId);
                        if Not (UniqueBulkPaymentId.AsValue().IsNull) THEN
                            UniqueBulkID := UniqueBulkPaymentId.AsValue().AsCode();
                        // Get List of Payments
                        if ResponseObject.GET('paymentItem', PaymentItemToken) THEN BEGIN
                            PaymentItemArray := PaymentItemToken.AsArray();
                            //Loop Through Each Item List
                            FOREACH PaymentItemTkn in PaymentItemArray DO BEGIN
                                BankPmtStatus.RESET();
                                Clear(PaymentItemObj);
                                Clear(StatusTkn);
                                Clear(status);
                                Clear(MessageTkn);
                                Clear(RemarkTkn);
                                PaymentItemObj := PaymentItemTkn.AsObject();
                                if GuiAllowed then Message(Format(PaymentItemTkn));
                                //Retrive Status Code TEST and INTEGER fmt
                                PaymentItemObj.GET('status', StatusTkn);
                                status := StatusTkn.AsValue().AsInteger();
                                //Retrive unique Payment Id
                                PaymentItemObj.GET('uniquePaymentId', PaymentIDTkn);
                                PaymentID := PaymentIDTkn.AsValue().AsText();
                                //Retrive Message
                                PaymentItemObj.GET('message', MessageTkn);
                                MessageTxt := MessageTkn.AsValue().AsText();
                                //Modify Treasury Table Accordilly if Vendor Exist
                                VoucherHdr.Reset();
                                VoucherHdr.SetRange("Document No.", TreasuryNotification."Payment Voucher No.");
                                if VoucherHdr.FindFirst() then begin
                                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Sent For Payment";
                                    VoucherHdr."Transaction Date" := Today;
                                    VoucherHdr."Transaction DateTime" := CurrentDateTime;

                                    TreasuryTblCopy."DateTime Sent to Bank" := CurrentDateTime;
                                    TreasuryTblCopy."Date Sent to Bank" := Today();
                                    TreasuryTblCopy."Session ID" := FORMAT(UniqueBulkID);
                                    TreasuryTblCopy."Bank Transaction Reference" := PaymentID;
                                    TreasuryTblCopy."Sent to Bank" := true;
                                    // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
                                    IF NewpaymentRef <> '' then
                                        TreasuryTblCopy."Transaction Reference" := NewpaymentRef;
                                    // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
                                    TreasuryTblCopy.Status := TreasuryTblCopy.Status::"Sent For Payment";
                                    BankPmtStatus.SetRange("Payment Status", status);
                                    BankPmtStatus.SetRange("Bank Code", TreasuryTblCopy."Source Bank Code");
                                    if BankPmtStatus.FINDFIRST() THEN BEGIN
                                        TreasuryTblCopy."Bank Status Code Desc." := BankPmtStatus."Status Description";
                                        TreasuryTblCopy."Bank Status Code" := FORMAT(BankPmtStatus."Payment Status");

                                    END ELSE BEGIN
                                        TreasuryTblCopy."Bank Status Code Desc." := MessageTxt;
                                        TreasuryTblCopy."Bank Status Code" := FORMAT(status);
                                    END;
                                    TreasuryTblCopy.Modify();
                                    VoucherHdr.Modify();
                                    Commit();
                                end;
                            END;
                        END;
                    END;
        END ELSE BEGIN
            VoucherHdr.Reset();
            VoucherHdr.SetRange("Document No.", TreasuryNotification."Payment Voucher No.");
            if VoucherHdr.FindFirst() then begin
                VoucherHdr."Transaction Date" := Today;
                VoucherHdr."Transaction DateTime" := CurrentDateTime;

                TreasuryTblCopy."Transaction DateTime" := CurrentDateTime;
                TreasuryTblCopy."Transaction Date" := Today();
                // TreasuryTblCopy."Session ID" := FORMAT(UniqueBulkID);
                // TreasuryTblCopy."Bank Transaction Reference" := PaymentID;
                BankPmtStatus.SetRange("Bank Code", TreasuryTblCopy."Source Bank Code");
                BankPmtStatus.SetRange("Payment Status", PmtStatus);
                if BankPmtStatus.FINDFIRST() THEN BEGIN
                    TreasuryTblCopy."Bank Status Code" := FORMAT(BankPmtStatus."Payment Status");
                    TreasuryTblCopy."Bank Status Code Desc." := BankPmtStatus."Status Description";
                    //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3
                    IF NewpaymentRef <> '' then
                        TreasuryTblCopy."Transaction Reference" := NewpaymentRef;
                    //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3 
                    //Check if all transaction are successfull Before changing Header status
                    IF BankPmtStatus.Successfull THEN BEGIN
                        TreasuryTblCopy.Status := TreasuryTblCopy.Status::Successful;
                    END ELSE
                        IF BankPmtStatus.Failed THEN BEGIN
                            VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::Failed;//Check all lines before this
                            TreasuryTblCopy.Status := TreasuryTblCopy.Status::Failed;
                        END ELSE BEGIN
                            VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Pending Update";//Pending UPDATE
                            TreasuryTblCopy.Status := TreasuryTblCopy.Status::"Pending Update";
                        END;

                END ELSE BEGIN
                    TreasuryTblCopy."Bank Status Code Desc." := PmtMessage;
                    TreasuryTblCopy."Bank Status Code" := FORMAT(PmtStatus);
                END;
                TreasuryTblCopy.Modify();
                VoucherHdr.Modify();
                Commit();
                IF CheckTransationsPerVoucher(VoucherHdr."Document No.") THEN BEGIN
                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::Successful;//Check all lines before this
                    VoucherHdr.Modify();
                    Commit();
                END;
            END;
        end;
    END;

    procedure BankNotifications(var TreasuryNotification: Record "Treasury Payment Notification"; ResponseCode: Text[20]; ResponseDesc: Text[500]; PmtStatus: Boolean)
    var
        BankNotifications: Record "Bank Notifications";
        TreasuryNotif: Record "Treasury Payment Notification";
    begin
        TreasuryNotif.Reset();
        TreasuryNotif.Copy(TreasuryNotification);
        BankNotifications.SetCurrentKey("Entry No");
        BankNotifications.SetRange("voucher No.", TreasuryNotif."Payment Voucher No.");
        BankNotifications.SetRange("Line No.", TreasuryNotif."Line No.");
        BankNotifications.SetRange("Bank Code", TreasuryNotif."Source Bank Code");
        IF PmtStatus THEN
            BankNotifications.SETRANGE("Payment Status", True) //Payment status True for Payment
        ELSE
            BankNotifications.SETRANGE("Payment Status", false); //Payment status false for Request
        IF NOT BankNotifications.FINDFIRST() THEN BEGIN
            BankNotifications.Init();
            BankNotifications."voucher No." := TreasuryNotif."Payment Voucher No.";
            BankNotifications."Bank Code" := TreasuryNotif."Source Bank Code";
            BankNotifications."Bank Name" := TreasuryNotif."Source Acct Name";
            BankNotifications."Line No." := TreasuryNotif."Line No.";
            BankNotifications.Amount := TreasuryNotif.Amount;
            BankNotifications."Txn Ref" := TreasuryNotif."Transaction Reference";
            BankNotifications."Date Time" := CurrentDateTime;
            BankNotifications."Response Code" := ResponseCode;
            BankNotifications."Response Desc." := ResponseDesc;
            IF PmtStatus THEN
                BankNotifications."Payment Status" := true
            ELSE
                BankNotifications."Payment Status" := FALSE;

            BankNotifications."Beneficiary Bank Code" := TreasuryNotif."Beneficiary Bank Code";
            BankNotifications."Beneficiary Bank Name" := TreasuryNotif."Beneficiary Bank Name";
            BankNotifications."Beneficiary Acct. No." := TreasuryNotif."Beneficiary Acct. No.";
        END ELSE BEGIN
            BankNotifications."Response Code" := ResponseCode;
            BankNotifications."Response Desc." := ResponseDesc;
            BankNotifications."Date Time" := CurrentDateTime;
        END;
        IF NOT BankNotifications.INSERT() THEN BankNotifications.MODIFY();
    end;

    procedure FirstBankNotifications(var TreasuryNotification: Record "Treasury Payment Notification"; ResponseObject: JsonObject)
    var
        BankNotifications: Record "Bank Notifications";
        TreasuryNotif: Record "Treasury Payment Notification";
        UniqueBulkPaymentId, PaymentItemToken, StatusTkn, PaymentIDTkn, PmtRefTkn, MessageTkn, RemarkTkn, PaymentItemTkn
        : JsonToken;
        UniqueBulkID, PaymentID, StatusTxt, MessageTxt, RemarkTxt, PmtRefTxt : Text;

        PaymentItemObj: JsonObject;
        PaymentItemArray: JsonArray;
    begin
        Clear(PaymentItemToken);
        Clear(PaymentItemArray);
        ResponseObject.GET('paymentItem', PaymentItemToken);
        if (PaymentItemToken.IsArray) THEN BEGIN
            PaymentItemArray := PaymentItemToken.AsArray();
            FOREACH PaymentItemTkn in PaymentItemArray DO BEGIN
                Clear(PaymentItemObj);
                Clear(StatusTkn);
                Clear(StatusTxt);
                Clear(MessageTkn);
                Clear(RemarkTkn);
                Clear(RemarkTxt);
                Clear(PaymentIDTkn);
                Clear(PaymentID);
                Clear(MessageTxt);
                PaymentItemObj := PaymentItemTkn.AsObject();
                //Retrive Status Code
                PaymentItemObj.GET('status', StatusTkn);
                // Message(FORMAT(StatusTkn.AsValue().AsInteger()));
                StatusTxt := FORMAT(StatusTkn.AsValue().AsInteger());

                //Retrive unique Payment Id
                PaymentItemObj.GET('uniquePaymentId', PaymentIDTkn);
                // Message(PaymentIDTkn.AsValue().AsCode());
                PaymentID := PaymentIDTkn.AsValue().AsText();

                //Retrive Message
                PaymentItemObj.GET('message', MessageTkn);
                MessageTxt := MessageTkn.AsValue().AsText();

                //Retrive Payment Ref / Transaction Reference
                PaymentItemObj.GET('paymentRef', PmtRefTkn);
                // Message(PmtRefTkn.AsValue().AsCode());
                PmtRefTxt := PmtRefTkn.AsValue().AsText();

                //Retrive Response It's more detailed than Message
                PaymentItemObj.GET('responseMessage', RemarkTkn);
                IF NOT (RemarkTkn.AsValue().IsNull) THEN
                    RemarkTxt := RemarkTkn.AsValue().AsText();

                //Special Function to Update Notification When response is OBJECT
                UpdateBANKNotification(false, PmtRefTxt, StatusTxt, MessageTxt)

                // TreasuryNotif.RESET();
                // TreasuryNotif.SetCurrentKey("Transaction Reference");
                // TreasuryNotif.SETRANGE("Transaction Reference", PmtRefTxt);
                // IF TreasuryNotif.FindFirst() then begin
                //     BankNotifications.Reset();
                //     BankNotifications.SetCurrentKey("Txn Ref");
                //     BankNotifications.SETRANGE("Txn Ref", PmtRefTxt);
                //     BankNotifications.SETRANGE("Payment Status", false); //Payment status false for Request
                //     IF NOT BankNotifications.FINDFIRST() THEN BEGIN
                //         BankNotifications.Init();
                //         BankNotifications."voucher No." := TreasuryNotif."Payment Voucher No.";
                //         BankNotifications."Bank Code" := TreasuryNotif."Source Bank Code";
                //         BankNotifications."Bank Name" := TreasuryNotif."Source Acct Name";
                //         BankNotifications."Line No." := TreasuryNotif."Line No.";
                //         BankNotifications.Amount := TreasuryNotif.Amount;
                //         BankNotifications."Txn Ref" := TreasuryNotif."Transaction Reference";
                //         BankNotifications."Date Time" := CurrentDateTime;
                //         BankNotifications."Response Code" := StatusTxt;
                //         BankNotifications."Response Desc." := RemarkTxt;
                //         BankNotifications."Beneficiary Bank Code" := TreasuryNotif."Beneficiary Bank Code";
                //         BankNotifications."Beneficiary Bank Name" := TreasuryNotif."Beneficiary Bank Name";
                //         BankNotifications."Beneficiary Acct. No." := TreasuryNotif."Beneficiary Acct. No.";
                //     END ELSE BEGIN
                //         BankNotifications."Response Code" := StatusTxt;
                //         BankNotifications."Response Desc." := RemarkTxt;
                //         BankNotifications."Date Time" := CurrentDateTime;
                //     END;
                //     IF NOT BankNotifications.INSERT() THEN BankNotifications.MODIFY();
                // end;
            END;
        END ELSE BEGIN //What to do when request Response is null ?
                       //Get Failed Response Code
            ResponseObject.GET('response', StatusTkn);
            // Message(FORMAT(StatusTkn.AsValue().AsInteger()));
            StatusTxt := FORMAT(StatusTkn.AsValue().AsInteger());
            //Get Failed Response Message
            ResponseObject.GET('responseMessage', MessageTkn);
            MessageTxt := MessageTkn.AsValue().AsText();
            // Message(MessageTxt);
            TreasuryNotif.Reset();
            TreasuryNotif.Copy(TreasuryNotification);
            BankNotifications.SetCurrentKey("Entry No");
            BankNotifications.SetRange("voucher No.", TreasuryNotif."Payment Voucher No.");
            BankNotifications.SetRange("Line No.", TreasuryNotif."Line No.");
            BankNotifications.SetRange("Bank Code", TreasuryNotif."Source Bank Code");
            BankNotifications.SETRANGE("Payment Status", false); //Payment status false for Request
            IF NOT BankNotifications.FINDFIRST() THEN BEGIN
                BankNotifications.Init();
                BankNotifications."voucher No." := TreasuryNotif."Payment Voucher No.";
                BankNotifications."Bank Code" := TreasuryNotif."Source Bank Code";
                BankNotifications."Bank Name" := TreasuryNotif."Source Acct Name";
                BankNotifications."Line No." := TreasuryNotif."Line No.";
                BankNotifications.Amount := TreasuryNotif.Amount;
                BankNotifications."Txn Ref" := TreasuryNotif."Transaction Reference";
                BankNotifications."Date Time" := CurrentDateTime;
                BankNotifications."Response Code" := StatusTxt;
                BankNotifications."Response Desc." := MessageTxt;
                BankNotifications."Beneficiary Bank Code" := TreasuryNotif."Beneficiary Bank Code";
                BankNotifications."Beneficiary Bank Name" := TreasuryNotif."Beneficiary Bank Name";
                BankNotifications."Beneficiary Acct. No." := TreasuryNotif."Beneficiary Acct. No.";
            END ELSE BEGIN
                BankNotifications."Response Code" := StatusTxt;
                BankNotifications."Response Desc." := MessageTxt;
                BankNotifications."Date Time" := CurrentDateTime;
            END;
            IF NOT BankNotifications.INSERT() THEN BankNotifications.MODIFY();
        END;
    end;

    Procedure UpdateBANKNotification(GetPmtStatus: Boolean; PmtRefTxt: Text; StatusTxt: Text; MessageDesc: Text)
    VAR
        TreasuryNotif: Record "Treasury Payment Notification";
        BankNotifications: Record "Bank Notifications";
        MyOutStream: OutStream;
        DataEncryption: Text[1500];
    BEGIN
        Clear(MyOutStream);
        TreasuryNotif.RESET();
        TreasuryNotif.SetCurrentKey("Transaction Reference");
        TreasuryNotif.SETRANGE("Transaction Reference", PmtRefTxt);
        IF TreasuryNotif.FindFirst() then begin
            BankNotifications.Reset();
            BankNotifications.SetCurrentKey("Txn Ref");
            BankNotifications.SETRANGE("Txn Ref", PmtRefTxt);
            IF GetPmtStatus THEN
                BankNotifications.SETRANGE("Payment Status", True) //Payment status True for Payment
            ELSE
                BankNotifications.SETRANGE("Payment Status", false); //Payment status false for Request
            IF NOT BankNotifications.FINDFIRST() THEN BEGIN
                BankNotifications.Init();
                BankNotifications."voucher No." := TreasuryNotif."Payment Voucher No.";
                BankNotifications."Bank Code" := TreasuryNotif."Source Bank Code";
                BankNotifications."Bank Name" := TreasuryNotif."Source Acct Name";
                BankNotifications."Line No." := TreasuryNotif."Line No.";
                BankNotifications.Amount := TreasuryNotif.Amount;
                BankNotifications."Txn Ref" := TreasuryNotif."Transaction Reference";
                BankNotifications."Date Time" := CurrentDateTime;
                BankNotifications."Response Code" := StatusTxt;

                IF GetPmtStatus THEN BEGIN
                    BankNotifications."Payment Status" := TRUE;
                    DataEncryption := TreasuryNotif."Payment Voucher No." + Format(CurrentDateTime) + TreasuryNotif."Beneficiary Acct. No." + Format(TreasuryNotif.Amount);
                END ELSE BEGIN
                    BankNotifications."Payment Status" := FALSE;
                    DataEncryption := TreasuryNotif.SystemId + TreasuryNotif."Transaction Reference" + TreasuryNotif."Bank Transaction Reference";
                END;
                BankNotifications."Response Desc." := MessageDesc;
                BankNotifications."Beneficiary Bank Code" := TreasuryNotif."Beneficiary Bank Code";
                BankNotifications."Beneficiary Bank Name" := TreasuryNotif."Beneficiary Bank Name";
                BankNotifications."Beneficiary Acct. No." := TreasuryNotif."Beneficiary Acct. No.";
            END ELSE BEGIN
                BankNotifications."Response Code" := StatusTxt;
                BankNotifications."Response Desc." := MessageDesc;
                BankNotifications."Date Time" := CurrentDateTime;
            END;
            BankNotifications."Data Encryption".CreateOutStream(MyOutStream);
            MyOutStream.WriteText(DataEncryption);
            BankNotifications.Validate("Data Encryption");
            IF NOT BankNotifications.INSERT() THEN BankNotifications.MODIFY();
        end;

    END;

    // procedure CheckTransationsPerVoucher(VoucherNo: code[20]): Boolean
    // var
    //     TreasuryPmtNotif: Record "Treasury Payment Notification";
    //     FBPaymentStatus: Record "Bank Payment Status";
    //     IsHandeled: Boolean;
    // begin
    //     IsHandeled := False;
    //     TreasuryPmtNotif.Reset();
    //     TreasuryPmtNotif.SetRange("Payment Voucher No.", VoucherNo);
    //     IF TreasuryPmtNotif.FINDSET() THEN BEGIN
    //         REPEAT
    //             FBPaymentStatus.RESET();
    //             FBPaymentStatus.SetRange("Bank Code", TreasuryPmtNotif."Bank Status Code");
    //             FBPaymentStatus.SETRANGE(Successfull, TRUE);
    //             IF FBPaymentStatus.FindFirst() THEN BEGIN
    //                 IsHandeled := TRUE;
    //             END ELSE BEGIN
    //                 IsHandeled := FALSE;
    //                 EXIT(IsHandeled);
    //             END;
    //         UNTIL TreasuryPmtNotif.NEXT() = 0;
    //         EXIT(IsHandeled);
    //     END;
    // end;

    procedure CheckTransationsPerVoucher(VoucherNo: code[20]): Boolean
    var
        TreasuryPmtNotif: Record "Treasury Payment Notification";
        FBPaymentStatus: Record "Bank Payment Status";
        IsHandeled: Boolean;
    begin
        IsHandeled := False;
        TreasuryPmtNotif.Reset();
        TreasuryPmtNotif.SetRange("Payment Voucher No.", VoucherNo);
        IF TreasuryPmtNotif.FINDSET() THEN BEGIN
            REPEAT
                IF (TreasuryPmtNotif.Status = TreasuryPmtNotif.Status::Successful) THEN BEGIN
                    IsHandeled := TRUE;
                END ELSE BEGIN
                    IsHandeled := FALSE;
                    EXIT(IsHandeled);
                END;
            UNTIL TreasuryPmtNotif.NEXT() = 0;
            EXIT(IsHandeled);
        END;
    end;

    local procedure VendorExist(VoucherHdr: Record "Voucher Header"): Boolean
    var
        GenJnlLine: Record "Gen. Journal Line 2";
        IsVendor: Boolean;
    begin
        GenJnlLine.Reset();
        GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", VoucherHdr."Journal Template Code");
        GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", VoucherHdr."Journal Batch Name");
        GenJnlLine.SETRANGE(GenJnlLine."Document No.", VoucherHdr."Document No.");
        if GenJnlLine.FindSet() then begin
            repeat
                if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then
                    IsVendor := true;
            until (GenJnlLine.Next() = 0) OR IsVendor = true;
            if IsVendor then EXIT(TRUE) else Exit(False);
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Voucher Header", 'OnAfterModifyEvent', '', false, false)]
    procedure InsertTreasuryNotificationRecord(var Rec: Record "Voucher Header")
    var
        TreasuryPmtNotif, TreasuryPmtNotif2 : Record "Treasury Payment Notification";
        BankPmtCode: Record "Bank Payment Code";
        GenJnlLn: Record "Gen. Journal Line 2";
        vendor: Record Vendor;
        VoucherHdr: Record "Voucher Header";
        IsVendor, NewData : Boolean;
        vendorRout: Record "Vendor Routing";
        VendRoutNo: Code[20];
    begin
        NewData := false;
        VoucherHdr.Reset();
        VoucherHdr.Copy(Rec);
        IsVendor := VendorExist(VoucherHdr);
        if VoucherHdr."Voucher Type" = VoucherHdr."Voucher Type"::BPV then begin
            if (VoucherHdr.Status = VoucherHdr.Status::Released) AND IsVendor AND (VoucherHdr."Bank Payment Status" = VoucherHdr."Bank Payment Status"::" ") OR
            (VoucherHdr."Bank Payment Status" = VoucherHdr."Bank Payment Status"::"Pending Update") OR
            (VoucherHdr."Bank Payment Status" = VoucherHdr."Bank Payment Status"::Failed) then begin
                TreasuryPmtNotif.Reset();
                BankAPICredentials.Reset();
                GenJnlLn.Reset();
                TreasuryPmtNotif.SetCurrentKey("Payment Voucher No.");
                TreasuryPmtNotif.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                // if TreasuryPmtNotif.FindFirst() then Error(VoucherHeaderExistErr, VoucherHdr."Document No.");
                If VoucherHdr."Account Type" = VoucherHdr."Account Type"::"Bank Account" then begin
                    BankAPICredentials.SetRange("Bank Account No.", VoucherHdr."Account No.");
                    if BankAPICredentials.FindFirst() then begin
                        //Checking if both Banks are enabled before populating Treasury Notification 
                        IF (NOT BankAPICredentials."Providus Bank?") AND (NOT BankAPICredentials."First Bank?") AND (NOT BankAPICredentials."Zenith Bank?") THEN
                            EXIT;
                        //Check voucher lines
                        GenJnlLn.SetRange("Document No.", VoucherHdr."Document No.");
                        GenJnlLn.SetRange("Journal Template Name", VoucherHdr."Journal Template Code");
                        GenJnlLn.SetRange("Journal Batch Name", VoucherHdr."Journal Batch Name");
                        if GenJnlLn.FindSet() then
                            repeat
                                TreasuryPmtNotif2.Reset();
                                TreasuryPmtNotif2.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                                TreasuryPmtNotif2.SetRange("Line No.", GenJnlLn."Line No.");
                                TreasuryPmtNotif2.SetRange("Source Bank Code", VoucherHdr."Account No.");
                                // TreasuryPmtNotif2.SetRange(Status, TreasuryPmtNotif2.Status::Successful);
                                if not TreasuryPmtNotif2.FindFirst() then begin
                                    if GenJnlLn."Account Type" = GenJnlLn."Account Type"::Vendor then begin
                                        vendor.SetRange("No.", GenJnlLn."Account No.");
                                        if vendor.FindFirst() then begin
                                            // vendor.TestField("Bank Routing Code"); //G2S>>> 07/05/25>>> CAS-01435-X5M1P2
                                            BankPmtCode.Reset();
                                            BankPmtCode.SetRange("Bank Routing Code", GenJnlLn."Vendor Rounting Code"); //G2S>>> 07/05/25>>> CAS-01435-X5M1P2
                                            if BankPmtCode.FindFirst() then begin
                                                if BankAPICredentials."Providus Bank?" then
                                                    BankPmtCode.TestField("Providus Bank Code")
                                                else
                                                    if BankAPICredentials."First Bank?" then
                                                        BankPmtCode.TestField("First Bank Code")
                                                    else
                                                        if BankAPICredentials."Zenith Bank?" then
                                                            BankPmtCode.TestField("Zenith Bank Code");
                                                //Insert to Treasury Notification Table
                                                TreasuryPmtNotif.Init();
                                                TreasuryPmtNotif."Payment Voucher No." := VoucherHdr."Document No.";
                                                TreasuryPmtNotif."Source Bank Code" := VoucherHdr."Account No.";
                                                TreasuryPmtNotif."Currency Code" := BankAPICredentials."Currency Code";
                                                TreasuryPmtNotif.Narration := VoucherHdr.Narration;
                                                TreasuryPmtNotif."Source Acct Name" := VoucherHdr."Account Name";
                                                TreasuryPmtNotif."Transaction Reference" := StrSubstNo('%1-%2', VoucherHdr."Document No.", GenJnlLn."Line No.");
                                                TreasuryPmtNotif.Amount := (GenJnlLn."Amount (LCY)" - GenJnlLn."WHT Amount"); //Amount minus WHT-Actual
                                                TreasuryPmtNotif."Beneficiary Acct. No." := vendor."Bank No.";
                                                TreasuryPmtNotif."Beneficiary Acct. Name" := GenJnlLn.Description;
                                                TreasuryPmtNotif."Line No." := GenJnlLn."Line No.";
                                                TreasuryPmtNotif.Status := TreasuryPmtNotif.Status::"Released For Payment";
                                                TreasuryPmtNotif."Temp Omit" := false;
                                                if BankAPICredentials."Providus Bank?" then
                                                    TreasuryPmtNotif."Beneficiary Bank Code" := BankPmtCode."Providus Bank Code"
                                                else
                                                    if BankAPICredentials."First Bank?" then
                                                        TreasuryPmtNotif."Beneficiary Bank Code" := BankPmtCode."First Bank Code"
                                                    else
                                                        if BankAPICredentials."Zenith Bank?" then
                                                            TreasuryPmtNotif."Beneficiary Bank Code" := BankPmtCode."Zenith Bank Code";
                                                TreasuryPmtNotif."Beneficiary Bank Name" := BankPmtCode."Bank Name";
                                                NewData := true;
                                            end;
                                        end;
                                    end;
                                    if Not TreasuryPmtNotif.Insert() then TreasuryPmtNotif.Modify();
                                end;
                            until GenJnlLn.Next() = 0;
                        IF NewData THEN BEGIN
                            VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Released For Payment";
                            VoucherHdr.Modify();
                        END;
                    end ELSE BEGIN
                        TreasuryPmtNotif.SetRange("Payment Voucher No.", VoucherHdr."Document No.");
                        IF TreasuryPmtNotif.FINDSET() THEN
                            REPEAT
                                TreasuryPmtNotif."Temp Omit" := TRUE;
                                TreasuryPmtNotif.Modify();
                            UNTIL TreasuryPmtNotif.NEXT() = 0;
                    END;
                end;


                // BankPmtCode.SetRange(, Rec."Account No.");
                // if BankPmtCode.FindFirst() then begin
                //     BankPmtCode.TestField("Bank Code");
                //     TreasuryPmtNotif."Beneficiary Bank Code" := BankPmtCode."Bank Code";
                // end else
                //     Error(BankCodeErr);
                // TreasuryPmtNotif.Insert();
            end

        end;
    end;



    procedure ClearValues(VoucherHdr: Record "Voucher Header")
    var
        GenJnlLineLvar: Record "Gen. Journal Line 2";
    begin
        GenJnlLineLvar.Reset();
        GenJnlLineLvar.SetRange("Journal Template Name", VoucherHdr."Journal Template Code");
        GenJnlLineLvar.SetRange("Journal Batch Name", VoucherHdr."Journal Batch Name");
        GenJnlLineLvar.SetRange("Document No.", VoucherHdr."Document No.");
        //GenJnlLineLvar.SetRange("Voucher type", "Voucher Type");
        if GenJnlLineLvar.FindSet() then
            repeat
                GenJnlLineLvar.Validate("Gen. Bus. Posting Group", '');
                GenJnlLineLvar.Validate("Gen. Prod. Posting Group", '');
                GenJnlLineLvar.Validate("Gen. Posting Type", GenJnlLineLvar."Gen. Posting Type"::" ");
                GenJnlLineLvar.Validate("VAT Prod. Posting Group", '');
                GenJnlLineLvar.Validate("VAT Bus. Posting Group", '');
                GenJnlLineLvar.Modify();
                Commit();
            until GenJnlLineLvar.Next() = 0;
    end;

    procedure CheckAppliesAmounts(VoucherHdr: Record "Voucher Header")
    var
        CustLedEntr: Record "Cust. Ledger Entry";
        VendLedEntr: Record "Vendor Ledger Entry";
        GenJournalLine: Record "Gen. Journal Line 2";
        GenJournalLine2: Record "Gen. Journal Line 2";
        PayableAmount: Decimal;
        GLE: record "G/L Entry";
    Begin
        GLE.Reset();
        GLE.SetRange("Old Document No.", VoucherHdr."Document No.");
        if not GLE.FindFirst() then begin

            GenJournalLine.Reset();
            GenJournalLine.SetRange("Journal Template Name", VoucherHdr."Journal Template Code");
            GenJournalLine.SetRange("Journal Batch Name", VoucherHdr."Journal Batch Name");
            GenJournalLine.SetRange("Document No.", VoucherHdr."Document No.");
            GenJournalLine.SetRange("Account Type", GenJournalLine."Account Type"::Vendor);
            if GenJournalLine.FindFirst() then
                repeat

                    IF GenJournalLine."Applies-to Doc. No." <> '' THEN BEGIN
                        VendLedEntr.Reset();
                        VendLedEntr.setrange("Vendor No.", GenJournalLine."Account No.");
                        VendLedEntr.SetRange("Document Type", VendLedEntr."Document Type"::Invoice);
                        VendLedEntr.SetRange("Document No.", GenJournalLine."Applies-to Doc. No.");
                        IF VendLedEntr.FindFirst() then begin
                            VendLedEntr.CalcFields("Remaining Amt. (LCY)");
                            if Abs(VendLedEntr."Remaining Amt. (LCY)") < abs(GenJournalLine."Amount (LCY)") then
                                Error('Journal amount and applied amount must be same');
                        end;
                    end else
                        IF GenJournalLine."Applies-to ID" <> '' THEN BEGIN
                            Clear(PayableAmount);
                            VendLedEntr.Reset();
                            VendLedEntr.setrange("Vendor No.", GenJournalLine."Account No.");
                            VendLedEntr.setrange("Applies-to ID", VoucherHdr."Document No.");
                            VendLedEntr.SetRange("Document Type", VendLedEntr."Document Type"::Invoice);
                            //VendLedEntr.SetRange("Document No.", "Applies-to Doc. No.");
                            IF VendLedEntr.findset then
                                repeat
                                    VendLedEntr.CalcFields("Remaining Amt. (LCY)");
                                    PayableAmount += VendLedEntr."Remaining Amt. (LCY)";
                                until VendLedEntr.Next = 0;
                            GenJournalLine2.Reset();
                            GenJournalLine2.SetRange("Journal Template Name", VoucherHdr."Journal Template Code");
                            GenJournalLine2.SetRange("Journal Batch Name", VoucherHdr."Journal Batch Name");
                            GenJournalLine2.SetRange("Document No.", VoucherHdr."Document No.");
                            GenJournalLine2.SetRange("Account Type", GenJournalLine."Account Type"::Vendor);
                            GenJournalLine2.SetRange("Account No.", GenJournalLine."Account No.");
                            GenJournalLine2.SetRange("Applies-to ID", GenJournalLine."Applies-to ID");
                            if GenJournalLine2.FindSet() then
                                GenJournalLine2.CalcSums("Amount (LCY)");
                            if Abs(PayableAmount) < abs(GenJournalLine2."Amount (LCY)") then
                                Error('Journal amount and applied amount must be same');
                        end;
                until GenJournalLine.Next() = 0;
        end;
    end;


    procedure MoveAttachment(var VoucHdr: Record "Voucher Header")
    var
        DocumentAttachment, DocumentAttachment2 : Record "Document Attachment";
        PostedVoucherHdr: Record "Posted Voucher Header";
        VoucHdr2: Record "Voucher Header";
    begin
        VoucHdr2.Reset();
        PostedVoucherHdr.Reset();
        VoucHdr2.Copy(VoucHdr);
        PostedVoucherHdr.SetRange("Voucher No.", VoucHdr2."Document No.");
        if PostedVoucherHdr.FindFirst() then begin
            DocumentAttachment.Reset();
            DocumentAttachment.SetRange("No.", PostedVoucherHdr."Voucher No.");
            If DocumentAttachment.FindFirst() then begin
                repeat
                    DocumentAttachment2.Init();
                    DocumentAttachment2.TransferFields(DocumentAttachment);
                    DocumentAttachment2."Table ID" := 50118;
                    DocumentAttachment2."No." := PostedVoucherHdr."Document No.";
                    DocumentAttachment2.Insert(true);
                    Commit();
                until DocumentAttachment.Next() = 0;
            end;
        end;
    end;


    procedure CopyLinksAndNotes(var VoucherHeader: Record "Voucher Header")
    var
        rec_JournalApprovedPage: Page 50353;
        RecordLink, RecordLink2 : Record "Record Link";
        RecRef: RecordRef;
        NoteText: BigText;
        PostedVoucherRecID: RecordId;
        Stream: InStream;
        PostedVoucherHdr: Record "Posted Voucher Header";
        postedVHrNo: Code[20];
        VoucherHdr: Record "Voucher Header";
    begin
        VoucherHdr.Reset();
        VoucherHdr.Copy(VoucherHeader);
        // VoucherHdr.get("Document No.");
        //postedVHrNo := VoucherHdr."Document No.";
        VoucherHdr.SetRecFilter();
        clear(RecRef);
        RecRef.GetTable(VoucherHdr);
        // RecRef.FindFirst();
        PostedVoucherHdr.Reset();
        PostedVoucherHdr.SetRange("Voucher No.", VoucherHdr."Document No.");
        if PostedVoucherHdr.FindFirst() then begin
            PostedVoucherRecID := PostedVoucherHdr.RecordId;
        end;
        RecordLink.Reset();
        RecordLink.SetCurrentKey("Record ID");
        RecordLink.SetRange("Record ID", RecRef.RecordId);
        If RecordLink.FindSet() then begin
            repeat
                RecordLink2.Reset();
                RecordLink2.Copy(RecordLink);
                RecordLink2."Record ID" := PostedVoucherRecID;
                RecordLink2.Modify();
                Commit();
            until RecordLink.Next() = 0;
        end;


    end;
    //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3 Enhancement
    //Increment the Reference for Requery
    procedure ReplaceLastValue(var TransactionReference: Text[100]): Text[100]
    var
        NewText, ReplaceText : Text[100];
        LastInt: Integer;
        LastChar: Text[1];
    begin
        // Find the position of the last space in the TransactionReference
        LastChar := CopyStr(TransactionReference, StrLen(TransactionReference), 1);
        Evaluate(LastInt, LastChar);
        LastInt += 1;
        clear(LastChar);
        //replace the entire text
        NewText := DelStr(TransactionReference, StrLen(TransactionReference), 1);
        NewText := NewText + Format(LastInt);  // If there are no spaces, replace the entire text

        exit(NewText);
    end;
    //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3 Enchancement


    //Zenith Bank Integration
    //SendPayment Payload for Zenith
    procedure CreateZenithPaymentPayload(Var Rec: Record "Treasury Payment Notification"; Requery: Boolean)
    var
        VoidCount: Integer;
        totalItems: Integer;
        totalValue: Decimal;
        singleDebit: Boolean;
        IsPaymentProcessed: Boolean;
        EndPointURL, JsonContent, UserName, BatchReference, Password, BankName, PayLoadReferrence, BankCode : Text;
        TransRequestArray: JsonArray;
        masterJsonObj, ClientInfoObj, TransactionObj, ResponseObject : JsonObject;
        TreasuryTable: Record "Treasury Payment Notification";
        BankAPICredentials: Record "Bank API Credentials";
        NewpaymentRef: Text[100];
    // BeneficiaryAccount, DebitAccount : Integer;
    begin
        Clear(BankCode);
        Clear(EndPointURL);
        Clear(UserName);
        Clear(BatchReference);
        Clear(BankName);
        Clear(TreasuryTable);
        Clear(masterJsonObj);
        Clear(TransRequestArray);
        Clear(ClientInfoObj);
        Clear(TransactionObj);
        Clear(ResponseObject);
        totalItems := 0;
        totalValue := 0;
        VoidCount := 0;
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("Zenith Bank?", true);
        if BankAPICredentials.FindFirst() then begin
            TreasuryTable.Copy(Rec);
            if not TreasuryTable.IsEmpty() then begin
                // BatchReference := TreasuryTable."Payment Voucher No." + Format(TreasuryTable."Line No.") + Format(Today);
                // BatchNarration := TreasuryTable."Payment Voucher No." + 'FBN Batch Payment for ' + Format(Today);

                EndPointURL := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."Fund Trf. Endpoint");

                UserName := GetAccessDetails(BankAPICredentials, 'UserName');
                Password := GetAccessDetails(BankAPICredentials, 'Password');
                // Evaluate(BeneficiaryAccount, TreasuryTable."Beneficiary Acct. No.");
                // Evaluate(DebitAccount, BankAPICredentials."Bank Account Number");


                ClientInfoObj.Add('CompanyCode', BankAPICredentials."Company Code");
                ClientInfoObj.Add('Password', Password);
                ClientInfoObj.Add('UserID', UserName);

                masterJsonObj.Add('ClientInfo', ClientInfoObj);
                masterJsonObj.Add('MAC', '');
                masterJsonObj.Add('UseSingleDebitMultipleCredit', false);//Not Compulsory for single transactions
                // if ValidateFBAcctNumber(TreasuryTable) <> '' then begin

                TransactionObj.Add('Amount', TreasuryTable.Amount);
                TransactionObj.Add('BeneficiaryAccount', TreasuryTable."Beneficiary Acct. No.");
                TransactionObj.Add('BeneficiaryBankCode', TreasuryTable."Beneficiary Bank Code");
                TransactionObj.Add('BeneficiaryCode', TreasuryTable."Beneficiary Bank Code"); //Optional
                TransactionObj.Add('BeneficiaryName', TreasuryTable."Beneficiary Acct. Name");
                TransactionObj.Add('DebitAccount', BankAPICredentials."Bank Account Number");
                TransactionObj.Add('DebitAccountName', TreasuryTable."Source Acct Name");
                TransactionObj.Add('DebitCurrency', BankAPICredentials."Currency Code");
                TransactionObj.Add('PaymentCurrency', BankAPICredentials."Currency Code");
                // TransactionObj.Add('PaymentMethod', ''); //Not Compulsory
                BankName := UpperCase(TreasuryTable."Beneficiary Bank Name");
                //Write a condition statement for zenith and Interbank
                IF BankName.Contains('ZENITH') THEN //might not hardcode not sure
                    TransactionObj.Add('PaymentType', BankAPICredentials."Intra Bank Payment Type") //OR 
                ELSE
                    TransactionObj.Add('PaymentType', BankAPICredentials."Inter Bank Payment Type");

                TransactionObj.Add('Payment_Due_Date', Format(Today, 0, '<Day,2>/<Month,2>/<Year4>'));

                IF Requery THEN BEGIN
                    NewpaymentRef := ReplaceLastValue(TreasuryTable."Transaction Reference");
                    TransactionObj.Add('TransactionRef', NewpaymentRef);
                END ELSE
                    TransactionObj.Add('TransactionRef', TreasuryTable."Transaction Reference");
            end;

            TransRequestArray.Add(TransactionObj);
            masterJsonObj.Add('TransactionRequest', TransRequestArray);
            masterJsonObj.WriteTo(JsonContent);
            // Message(Format(JsonContent)); //To Confirm just response
            ResponseObject := SendTransferFunds(JsonContent, EndPointURL); //Same procedure for multiple need to confirm if it won't affect

            //update Treasury  and Bank Notification Table
            ZenithBankUpdateReferenceTbl(TreasuryTable, ResponseObject, false, '', '', NewpaymentRef);
            ZenithBankNotifications(TreasuryTable, ResponseObject);
        end;
    end;

    //FetchRequestPayload
    procedure FetchZenithPaymentRequest(Rec: Record "Treasury Payment Notification"): Text
    var
        TransReference: Code[100];
        MessageJtoken, StatusCodeTkn, DescripTkn, TransactionsToken, TransRefToken : JsonToken;
        EndPointURL, JsonContent, UserName, Password, BankCode : Text;
        FetchResponseObj, PaymentObj, ClientInfoObj, TransactionObj, masterJsonObj : JsonObject;
        HttpHeadersContent: HttpHeaders;
        URLEndPoint, contentvar, ResponseText, DescripTxt, MessgeTxt, SessionID, CorpCode, TransRefTxt
        : Text;
        TreasuryTable: Record "Treasury Payment Notification";
        StatusCode: Code[20];
        TransactionObjArray, TransRefArray : JsonArray;
        HttpClient: HttpClient;
        HttpContent: HttpContent;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        HttpHeaders: HttpHeaders;

    begin
        HttpResponseMessage.Headers.Clear();
        Clear(MessageJtoken);
        Clear(StatusCodeTkn);
        Clear(DescripTkn);
        Clear(TransRefToken);
        clear(TransactionObjArray);
        clear(ClientInfoObj);
        Clear(URLEndPoint);
        clear(TransactionObj);
        Clear(FetchResponseObj);
        clear(HttpResponseMessage);
        clear(HttpRequestMessage);
        clear(HttpClient);
        clear(HttpContent);
        Clear(HttpHeadersContent);
        TreasuryTable.Reset();
        TreasuryTable.Copy(Rec);
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("Zenith Bank?", true);

        if BankAPICredentials.FindFirst() then begin
            TreasuryTable.Copy(Rec);
            if NOT TreasuryTable.IsEmpty() then begin
                EndPointURL := StrSubstNo('%1/%2', GetAccessDetails(BankAPICredentials, 'URL'), BankAPICredentials."NIP Txn. Status Endpoint");
                UserName := GetAccessDetails(BankAPICredentials, 'UserName');
                Password := GetAccessDetails(BankAPICredentials, 'Password');

                ClientInfoObj.Add('CompanyCode', BankAPICredentials."Company Code");
                ClientInfoObj.Add('Password', Password);
                ClientInfoObj.Add('UserID', UserName);

                masterJsonObj.Add('ClientInfo', ClientInfoObj);

                TransactionObj.Add('TransactionRef', TreasuryTable."Transaction Reference");
            end;
            TransRefArray.Add(TransactionObj);
            masterJsonObj.Add('TransactionReference', TransRefArray);

            masterJsonObj.WriteTo(JsonContent);
            // Message(Format(JsonContent)); //
            FetchResponseObj := SendTransferFunds(JsonContent, EndPointURL);

            if FetchResponseObj.GET('Transactions', TransactionsToken) THEN
                IF TransactionsToken.IsArray Then begin
                    TransactionObjArray := TransactionsToken.AsArray();
                    IF TransactionObjArray.Count >= 1 THEN BEGIN
                        FOREACH TransactionsToken in TransactionObjArray DO BEGIN
                            Clear(TransactionObj);
                            TransactionObj := TransactionsToken.AsObject();
                            IF TransactionObj.Get('Description', DescripTkn) THEN BEGIN
                                if not (DescripTkn.AsValue().IsNull) then begin
                                    DescripTxt := DescripTkn.AsValue().AsText();
                                end;
                            END;
                            TransactionObj.get('PaymentStatus', MessageJtoken);
                            if NOT (MessageJtoken.AsValue().IsNull) then begin
                                MessgeTxt := MessageJtoken.AsValue().AsText();
                            end;

                            TransactionObj.Get('ResponseCode', StatusCodeTkn);
                            if NOT (StatusCodeTkn.AsValue().IsNull) then begin
                                StatusCode := StatusCodeTkn.AsValue().AsCode();
                            END;

                            TransactionObj.Get('TransactionRef', TransRefToken);
                            if not (TransRefToken.AsValue().IsNull) then begin
                                TransRefTxt := TransRefToken.AsValue().AsText();
                            end;
                        END
                    end ELSE BEGIN
                        FetchResponseObj.Get('ResponseCode', StatusCodeTkn);
                        if NOT (StatusCodeTkn.AsValue().IsNull) then
                            StatusCode := StatusCodeTkn.AsValue().AsCode();


                        FetchResponseObj.Get('Description', DescripTkn);
                        if not (DescripTkn.AsValue().IsNull) then
                            DescripTxt := DescripTkn.AsValue().AsText();

                        TransRefTxt := TreasuryTable."Transaction Reference";
                    END;
                end;
            ZenithBankUpdateReferenceTbl(TreasuryTable, FetchResponseObj, True, StatusCode, MessgeTxt, '');
            UpdateBANKNotification(True, TransRefTxt, FORMAT(StatusCode), MessgeTxt)
        END;
    END;



    procedure ZenithBankUpdateReferenceTbl(var TreasuryNotification: Record "Treasury Payment Notification"; ResponseObject: JsonObject; FetchStatus: Boolean; PmtStatus: Code[20]; PmtMessage: Text; NewpaymentRef: Text)
    var
        myInt: Integer;
        VoucherHdr: Record "Voucher Header";
        BankPmtStatus: Record "Bank Payment Status";
        ResponseToken, UniqueBulkPaymentId, TransactionsToken, StatusTkn, PaymentIDTkn, DescriptionTkn, MessageTkn, RemarkTkn, PaymentItemTkn
         : JsonToken;
        MessageTxt, RemarkTxt, ResponseCodeValue : Text;
        PaymentItemObj: JsonObject;
        TransactionsArray: JsonArray;
        statusCode: code[10];
        TreasuryCopy: Record "Treasury Payment Notification";
        PmtMessageUpperCase: Text;

    begin
        TreasuryCopy.RESET();
        TreasuryCopy.Copy(TreasuryNotification);
        //Send Payment Status Response
        IF NOT FetchStatus THEN BEGIN
            if ResponseObject.GET('ResponseCode', ResponseToken) THEN
                if Not (ResponseToken.AsValue().IsNull) THEN BEGIN
                    //Get Header value
                    if ResponseObject.GET('ResponseCode', StatusTkn) then
                        statusCode := StatusTkn.AsValue().AsCode();

                    if ResponseObject.GET('Description', MessageTkn) then
                        MessageTxt := MessageTkn.AsValue().AsText();

                    // Get List of Transaction
                    if ResponseObject.GET('Transactions', TransactionsToken) THEN
                        if (TransactionsToken.IsArray) then BEGIN
                            TransactionsArray := TransactionsToken.AsArray();
                            IF TransactionsArray.Count >= 1 then
                                //Get the Array Object
                                FOREACH PaymentItemTkn in TransactionsArray DO BEGIN
                                    BankPmtStatus.RESET();
                                    Clear(PaymentItemObj);
                                    Clear(StatusTkn);
                                    Clear(statusCode);
                                    Clear(MessageTkn);
                                    Clear(RemarkTkn);
                                    PaymentItemObj := PaymentItemTkn.AsObject();
                                    if GuiAllowed then Message(Format(PaymentItemTkn));
                                    //Retrive Status Code TEST and INTEGER fmt
                                    PaymentItemObj.GET('ResponseCode', StatusTkn);
                                    statusCode := StatusTkn.AsValue().AsCode();
                                    //Retrive unique Payment Id
                                    // PaymentItemObj.GET('TransactionRef', PaymentIDTkn);
                                    // PaymentID := PaymentIDTkn.AsValue().AsText();
                                    //Retrive Message
                                    PaymentItemObj.GET('Description', MessageTkn);
                                    iF Not MessageTkn.AsValue().IsNull THEN
                                        MessageTxt := MessageTkn.AsValue().AsText();
                                END;
                        END;

                    //Modify Treasury Table Accordilly if Vendor Exist
                    VoucherHdr.Reset();
                    VoucherHdr.SetRange("Document No.", TreasuryNotification."Payment Voucher No.");
                    if VoucherHdr.FindFirst() then begin
                        VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Sent For Payment";
                        VoucherHdr."Transaction Date" := Today;
                        VoucherHdr."Transaction DateTime" := CurrentDateTime;

                        TreasuryCopy."DateTime Sent to Bank" := CurrentDateTime;
                        TreasuryCopy."Date Sent to Bank" := Today();

                        // TreasuryCopy."Bank Transaction Reference" := PaymentID;
                        TreasuryCopy."Sent to Bank" := true;

                        IF NewpaymentRef <> '' then
                            TreasuryCopy."Transaction Reference" := NewpaymentRef;
                        // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
                        TreasuryCopy.Status := TreasuryCopy.Status::"Sent For Payment";
                        BankPmtStatus.SetRange("Payment StatusT", statusCode);
                        BankPmtStatus.SetRange("Bank Code", TreasuryCopy."Source Bank Code");
                        if BankPmtStatus.FINDFIRST() THEN BEGIN
                            TreasuryCopy."Bank Status Code Desc." := BankPmtStatus."Status Description";
                            TreasuryCopy."Bank Status Code" := FORMAT(BankPmtStatus."Payment StatusT");

                        END ELSE BEGIN
                            TreasuryCopy."Bank Status Code Desc." := MessageTxt;
                            TreasuryCopy."Bank Status Code" := statusCode;
                        END;
                        TreasuryCopy.Modify();
                        VoucherHdr.Modify();
                        Commit();
                    end;
                END;
        END ELSE BEGIN
            //FetchPayment Response
            VoucherHdr.Reset();
            VoucherHdr.SetRange("Document No.", TreasuryNotification."Payment Voucher No.");
            if VoucherHdr.FindFirst() then begin
                VoucherHdr."Transaction Date" := Today;
                VoucherHdr."Transaction DateTime" := CurrentDateTime;

                TreasuryCopy."Transaction DateTime" := CurrentDateTime;
                TreasuryCopy."Transaction Date" := Today();
                // TreasuryCopy."Session ID" := FORMAT(UniqueBulkID);
                // TreasuryCopy."Bank Transaction Reference" := PaymentID;
                BankPmtStatus.SetRange("Bank Code", TreasuryCopy."Source Bank Code");
                BankPmtStatus.SetRange("Payment StatusT", Format(PmtStatus)); //
                if BankPmtStatus.FINDFIRST() THEN BEGIN
                    TreasuryCopy."Bank Status Code" := FORMAT(BankPmtStatus."Payment StatusT");
                    TreasuryCopy."Bank Status Code Desc." := BankPmtStatus."Status Description";
                    //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3
                    IF NewpaymentRef <> '' then
                        TreasuryCopy."Transaction Reference" := NewpaymentRef;
                    //>>>>>> G2S 13/01/2025 CAS-01386-K2V6T3 
                    //Check if all transaction are successfull Before changing Header status
                    IF BankPmtStatus.Successfull THEN
                        PmtMessageUpperCase := PmtMessage.ToUpper();
                    IF PmtMessageUpperCase.Contains(BankPmtStatus."Status Description") THEN BEGIN
                        TreasuryCopy.Status := TreasuryCopy.Status::Successful;
                    END ELSE
                        IF BankPmtStatus.Failed THEN BEGIN
                            VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::Failed;//Check all lines before this
                            TreasuryCopy.Status := TreasuryCopy.Status::Failed;
                        END ELSE BEGIN
                            VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::"Pending Update";//Pending UPDATE
                            TreasuryCopy."Bank Status Code Desc." := PmtMessage;
                            TreasuryCopy.Status := TreasuryCopy.Status::"Pending Update";
                        END;

                END ELSE BEGIN
                    TreasuryCopy."Bank Status Code Desc." := PmtMessage;
                    TreasuryCopy."Bank Status Code" := PmtStatus;
                END;
                TreasuryCopy.Modify();
                VoucherHdr.Modify();
                // Commit();
                IF CheckTransationsPerVoucher(VoucherHdr."Document No.") THEN BEGIN
                    VoucherHdr."Bank Payment Status" := VoucherHdr."Bank Payment Status"::Successful;//Check all lines before this
                    VoucherHdr.Modify();
                    Commit();
                END;
            END;
        end;
    END;

    procedure ZenithBankNotifications(var TreasuryNotification: Record "Treasury Payment Notification"; ResponseObject: JsonObject)
    var
        BankNotifications: Record "Bank Notifications";
        TreasuryNotif: Record "Treasury Payment Notification";
        UniqueBulkPaymentId, PaymentItemToken, StatusTkn, PaymentIDTkn, PmtRefTkn, MessageTkn, RemarkTkn, PaymentItemTkn
        : JsonToken;
        UniqueBulkID, PaymentID, StatusTxt, MessageTxt, RemarkTxt, PmtRefTxt : Text;

        PaymentItemObj: JsonObject;
        PaymentItemArray: JsonArray;
    begin
        Clear(PaymentItemToken);
        Clear(PaymentItemArray);
        ResponseObject.GET('Transactions', PaymentItemToken);
        if (PaymentItemToken.IsArray) THEN BEGIN
            PaymentItemArray := PaymentItemToken.AsArray();
            IF PaymentItemArray.Count > 1 THEN BEGIN
                FOREACH PaymentItemTkn in PaymentItemArray DO BEGIN
                    Clear(PaymentItemObj);
                    Clear(StatusTkn);
                    Clear(StatusTxt);
                    Clear(MessageTkn);
                    Clear(RemarkTkn);
                    Clear(RemarkTxt);
                    Clear(PaymentIDTkn);
                    Clear(PaymentID);
                    Clear(MessageTxt);
                    PaymentItemObj := PaymentItemTkn.AsObject();
                    //Retrive Status Code
                    PaymentItemObj.GET('ResponseCode', StatusTkn);
                    StatusTxt := StatusTkn.AsValue().AsText();


                    //Retrive Message
                    PaymentItemObj.GET('Description', MessageTkn);
                    MessageTxt := MessageTkn.AsValue().AsText();

                    //Retrive Payment Ref / Transaction Reference
                    PaymentItemObj.GET('TransactionRef', PmtRefTkn);
                    // Message(PmtRefTkn.AsValue().AsCode());
                    PmtRefTxt := PmtRefTkn.AsValue().AsText();

                    //Retrive Response It's more detailed than Message
                    PaymentItemObj.GET('PaymentStatus', RemarkTkn);
                    IF NOT (RemarkTkn.AsValue().IsNull) THEN
                        RemarkTxt := RemarkTkn.AsValue().AsText();

                    //Special Function to Update Notification When response is OBJECT
                    UpdateBANKNotification(false, PmtRefTxt, StatusTxt, MessageTxt)

                END;
            END ELSE BEGIN //What to do when request Response is null ?
                           //Get Failed Response Code
                ResponseObject.GET('ResponseCode', StatusTkn);
                // Message(FORMAT(StatusTkn.AsValue().AsInteger()));
                StatusTxt := FORMAT(StatusTkn.AsValue().AsCode());
                //Get Failed Response Message
                ResponseObject.GET('Description', MessageTkn);
                MessageTxt := MessageTkn.AsValue().AsText();
                // Message(MessageTxt);
                TreasuryNotif.Reset();
                TreasuryNotif.Copy(TreasuryNotification);
                BankNotifications.SetCurrentKey("Entry No");
                BankNotifications.SetRange("voucher No.", TreasuryNotif."Payment Voucher No.");
                BankNotifications.SetRange("Line No.", TreasuryNotif."Line No.");
                BankNotifications.SetRange("Bank Code", TreasuryNotif."Source Bank Code");
                BankNotifications.SETRANGE("Payment Status", false); //Payment status false for Request
                IF NOT BankNotifications.FINDFIRST() THEN BEGIN
                    BankNotifications.Init();
                    BankNotifications."voucher No." := TreasuryNotif."Payment Voucher No.";
                    BankNotifications."Bank Code" := TreasuryNotif."Source Bank Code";
                    BankNotifications."Bank Name" := TreasuryNotif."Source Acct Name";
                    BankNotifications."Line No." := TreasuryNotif."Line No.";
                    BankNotifications.Amount := TreasuryNotif.Amount;
                    BankNotifications."Txn Ref" := TreasuryNotif."Transaction Reference";
                    BankNotifications."Date Time" := CurrentDateTime;
                    BankNotifications."Response Code" := StatusTxt;
                    BankNotifications."Response Desc." := MessageTxt;
                    BankNotifications."Beneficiary Bank Code" := TreasuryNotif."Beneficiary Bank Code";
                    BankNotifications."Beneficiary Bank Name" := TreasuryNotif."Beneficiary Bank Name";
                    BankNotifications."Beneficiary Acct. No." := TreasuryNotif."Beneficiary Acct. No.";
                END ELSE BEGIN
                    BankNotifications."Response Code" := StatusTxt;
                    BankNotifications."Response Desc." := MessageTxt;
                    BankNotifications."Date Time" := CurrentDateTime;
                END;
            END;
            IF NOT BankNotifications.INSERT() THEN BankNotifications.MODIFY();
        END;
    end;
    //Zenith Bank Integration

    var
        HttpClient: HttpClient;
        HttpContent: HttpContent;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        HttpHeaders: HttpHeaders;
        URL: Text;
        SessionID, TxnReference : Text[200];
        IsSuccessful: Boolean;
        BankAPICredentials: Record "Bank API Credentials";
        BankDoesNtExistErr: Label 'The Bank access setup for %1 does not exist';
        VoucherHeaderExistErr: Label 'Treasury Payment already exist %1';
        BankCodeErr: Label 'Bank Code does not exits';
        TreasuryTblCopy: Record "Treasury Payment Notification";
        BankPmtCode: Record "Bank Payment Code";

        URLEndpoint, FBSession : Text[300];

}
//Providus Integration 7th Aug 2024 