
pageextension 50103 ItemLedEntryExt extends "Item Ledger Entries"
{
    layout
    {
        // Add changes to page layout here
        addafter("Applied Entry to Adjust")
        {
            field("Import File No."; "Import File No.")
            {
                ApplicationArea = ALL;
            }
            field("External Document No."; "External Document No.")
            {
                ApplicationArea = ALL;
            }
            //Balu 05172021>>
            field("Transfer Order No."; "Transfer Order No.")
            {
                ApplicationArea = all;
            }
            field("From Location"; "From Location")
            {
                ApplicationArea = all;
            }
            field("To Location"; "To Location")
            {
                ApplicationArea = all;
            }
            field("User ID"; "User ID")
            {
                ApplicationArea = all;//B2BPKON210521
            }
            field("Source Type"; "Source Type")
            {
                ApplicationArea = all;
            }
            field("Source No."; "Source No.")
            {
                ApplicationArea = all;
            }
            field("Unit of Measure Code"; "Unit of Measure Code")
            {
                ApplicationArea = all;
            }
            //Balu 05172021>>

        }
        modify("Cost Amount (Expected)")
        {
            Visible = true;
        }
        modify("Sales Amount (Expected)")
        {
            Visible = true;
        }
    }

    actions
    {
    }
}
