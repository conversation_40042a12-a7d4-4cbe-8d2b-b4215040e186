page 50520 PurchaseTempLinesList
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = PurchaseTempLines;

    layout
    {
        area(Content)
        {
            repeater(Control10000000002)
            {
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = All;

                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;

                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;
                }
                field("Buy-from Vendor No."; "Buy-from Vendor No.")
                {
                    ApplicationArea = All;
                }
                field(Type; Type)
                {
                    ApplicationArea = All;
                }
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = All;
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    ApplicationArea = All;
                }
                field("Qty. to Receive"; "Qty. to Receive")
                {
                    ApplicationArea = All;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = All;
                }
                field("Amount Including VAT"; "Amount Including VAT")
                {
                    ApplicationArea = All;
                }
                field("Unit Price (LCY)"; "Unit Price (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = all;
                }
                field("Line Amount"; "Line Amount")
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = All;
                }
                field("Qty. to Invoice (Base)"; "Qty. to Invoice (Base)")
                {
                    ApplicationArea = All;
                }
                field("Qty. to Receive (Base)"; "Qty. to Receive (Base)")
                {
                    ApplicationArea = All;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = All;
                }
                field("Order Date"; "Order Date")
                {
                    ApplicationArea = All;
                }/*
                field("No"; "No. Line")
                {
                    //Caption = 'No.';
                    ApplicationArea = All;
                }*/
                field("Ship-to Code"; "Ship-to Code")
                {
                    ApplicationArea = All;
                }
                field("Sell-to Customer No."; "Sell-to Customer No.")
                {
                    ApplicationArea = All;
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                    ApplicationArea = All;
                }
                field("Due Date"; "Due Date")
                {
                    Caption = 'Due Date';
                    ApplicationArea = All;
                }
                field("Shipment Method Code"; "Shipment Method Code")
                {
                    ApplicationArea = All;
                }
                field("Location Codes"; "Location Codes")
                {
                    ApplicationArea = All;
                }
                field("Shortcut Dimension 1 Codes"; "Shortcut Dimension 1 Codes")
                {
                    ApplicationArea = All;
                }
                field("Shortcut Dimension 2 Codes"; "Shortcut Dimension 2 Codes")
                {
                    ApplicationArea = All;
                }
                field("Vendor Posting Group"; "Vendor Posting Group")
                {
                    ApplicationArea = All;
                }
                field("Prices Including VAT"; "Prices Including VAT")
                {
                    ApplicationArea = All;
                }
                field("Invoice Disc. Code"; "Invoice Disc. Code")
                {
                    ApplicationArea = All;
                }
                field("Applies-to Doc. Type"; "Applies-to Doc. Type")
                {
                    ApplicationArea = All;
                }
                field("Applies-to Doc. No."; "Applies-to Doc. No.")
                {
                    ApplicationArea = All;
                }
                field("Posting No."; "Posting No.")
                {
                    Caption = 'Posting No.';
                    ApplicationArea = All;
                }
                field("Document Date"; "Document Date")
                {
                    Caption = 'Document Date';
                    ApplicationArea = All;
                }

                field("Payment Method Code"; "Payment Method Code")
                {
                    ApplicationArea = All;
                }
                field("Applies-to ID"; "Applies-to ID")
                {
                    ApplicationArea = All;
                }
                field("Responsibility Centers"; "Responsibility Centers")
                {
                    ApplicationArea = All;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    ApplicationArea = All;
                }
                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = All;
                    //B2B.P.K.T
                }
                field("Clearing File No."; "Clearing File No.")
                {
                    ApplicationArea = All;
                    //B2B.P.K.T
                }
                field("FA Tagging Not Required"; "FA Tagging Not Required")
                {
                    ApplicationArea = All;
                }
                field("New Order No."; "New Order No.")
                {
                    ApplicationArea = All;
                }




            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Create Purchase Documents")
            {
                ApplicationArea = All;

                trigger OnAction()
                begin
                    createOrders();
                end;
            }
            action("Release current Doc")
            {
                ApplicationArea = All;

                trigger OnAction()
                begin
                    ReleaseStatus2();
                end;
            }
            action("Release All Doc")
            {
                ApplicationArea = All;

                trigger OnAction()
                begin
                    ReleaseStatus();
                end;
            }
        }
    }

    var
        myInt: Integer;

    procedure createOrders()
    Var
        Templi: Record PurchaseTempLines;
        TempliR: Record PurchaseTempLines;
        PurchHder: Record "Purchase Header";
        Purchines: record "Purchase Line";
        PrevOrd: Text;
        WindPa: Dialog;
        PurchHder2: Record "Purchase Header";
        Purchines2: record "Purchase Line";
    begin
        IF Confirm('Do you want to Create Purchase Documents ?', true, false) then begin
            Templi.RESET;
            Templi.SetCurrentKey("Document No.");
            Templi.SETFILTER("New Order No.", '=%1', '');
            IF Templi.FindSet() then BEGIN
                WindPa.Open('Processing #1###############');
                repeat
                    IF PrevOrd <> Templi."Document No." THEN BEGIN
                        TempliR.RESET;
                        TempliR.SetCurrentKey("Document No.");
                        TempliR.SETRANGE("Document No.", Templi."Document No.");
                        TempliR.SETFILTER("New Order No.", '=%1', '');
                        IF TempliR.FindSet() then begin
                            WindPa.Update(1, TempliR."Document No.");
                            if not PurchHder2.get(TempliR."Document Type", TempliR."Document No.") then begin
                                PurchHder.INIT;
                                PurchHder."Document Type" := TempliR."Document Type";
                                PurchHder."No." := TempliR."Document No.";
                                PurchHder."Purchase Type" := Templi."Purchase Type";
                                PurchHder.Insert(true);
                                PurchHder.Validate("Responsibility Center", TempliR."Responsibility Center");
                                PurchHder.Validate("Shortcut Dimension 1 Code", TempliR."Shortcut Dimension 1 Code");
                                PurchHder.Validate("Shortcut Dimension 2 Code", TempliR."Shortcut Dimension 2 Code");
                                PurchHder.Validate("Buy-from Vendor No.", TempliR."Buy-from Vendor No.");
                                PurchHder.Validate("Location Code", TempliR."Location Code");
                                PurchHder.Validate("Currency Code", TempliR."Currency Code");
                                PurchHder.Validate("Invoice Disc. Code", TempliR."Invoice Disc. Code");
                                PurchHder.Validate("Payment Method Code", TempliR."Payment Method Code");
                                PurchHder.Validate("Payment Terms Code", TempliR."Payment Terms Code");
                                PurchHder.Modify();
                            end;
                            repeat
                                if not Purchines2.get(TempliR."Document Type", TempliR."Document No.", TempliR."Line No.") then begin
                                    Purchines.INIT;
                                    Purchines."Document Type" := PurchHder."Document Type";
                                    Purchines."Document No." := PurchHder."No.";
                                    Purchines."Line No." := TempliR."Line No.";
                                    Purchines.Insert(true);
                                    Purchines.VALIDATE(Type, TempliR.Type);
                                    Purchines.Validate("No.", TempliR."No.");
                                    Purchines.Validate(Description, TempliR.Description);
                                    Purchines.Validate("Description 2", TempliR."Description 2");
                                    Purchines.Validate("Location Code", TempliR."Location Code");
                                    Purchines.Validate(Quantity, TempliR."Qty. to Receive");
                                    Purchines.Validate("Unit of Measure Code", TempliR."Unit of Measure Code");
                                    Purchines.Validate("Direct Unit Cost", TempliR."Unit Cost");
                                    Purchines.Validate("Shortcut Dimension 1 Code", TempliR."Shortcut Dimension 1 Code");
                                    Purchines.Validate("Shortcut Dimension 2 Code", TempliR."Shortcut Dimension 2 Code");
                                    //Purchines.Validate("Gen. Bus. Posting Group" , TempliR."Shortcut Dimension 1 Code");
                                    //Purchines.Validate("Gen. Prod. Posting Group" , TempliR."Shortcut Dimension 2 Code");
                                    Purchines.Modify();
                                    TempliR."New Order No." := PurchHder."No.";
                                    TempliR.Modify();
                                    Commit();
                                end;
                            until TempliR.next = 0;
                        end;
                        PrevOrd := TempliR."Document No.";
                    end;
                until Templi.next = 0;
            end;
            WindPa.Close();
        end;
        Message('Completed.');
    end;

    local procedure ReleaseStatus()
    var
        TempPurchas: Record PurchaseTempLines;
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
    begin
        TempPurchas.Reset();
        TempPurchas.SetFilter("New Order No.", '<>%1', '');
        if TempPurchas.FindSet() then
            repeat
                if PurchaseHeader.get(TempPurchas."Document Type", TempPurchas."Document No.") and (PurchaseHeader.Status = PurchaseHeader.Status::Open) then begin
                    PurchaseHeader.Status := PurchaseHeader.Status::Released;
                    PurchaseHeader.Modify();
                end;
            until TempPurchas.Next() = 0;
    end;

    local procedure ReleaseStatus2()
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
    begin

        if PurchaseHeader.get("Document Type", "Document No.") then begin
            PurchaseHeader.Status := PurchaseHeader.Status::Released;
            PurchaseHeader.Modify();
        end;
    end;
}