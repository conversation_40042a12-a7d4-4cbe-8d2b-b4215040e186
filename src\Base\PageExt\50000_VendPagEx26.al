pageextension 50000 VendPagExt26 extends "Vendor Card"
{
    layout
    {
        addafter(Blocked)
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
            }

            field("Vendor Classification"; "Vendor Classification")
            {
                ApplicationArea = all;
            }
            field("Vendor Type"; "Vendor Type")
            {
                ApplicationArea = all;
            }
            field("WHT Group"; "WHT Group")
            {
                ApplicationArea = all;
            }
            field("Main Vendor"; "Main Vendor")
            {
                ApplicationArea = all;
            }
            field("Transport Contract"; "Transport Contract")
            {
                ApplicationArea = ALL;
            }
            field("Transport Account"; "Transport Account")
            {
                ApplicationArea = ALL;
            }
            field("Registration No."; "Registration No.")
            {
                ApplicationArea = ALL;
            }
            field("Fuel Reimbursement"; "Fuel Reimbursement")//PKONJU19
            {
                ApplicationArea = ALL;
            }
            field("Fuel Reimbursement Acc."; "Fuel Reimbursement Acc.")//PKONJU19
            {
                ApplicationArea = ALL;
            }
            field("Vat On Fuel Reimbursement"; "Vat On Fuel Reimbursement")//PKONJU19
            {
                ApplicationArea = ALL;
            }
            field("Bank Account"; "Bank Account")//PKON22MA8-CR220031
            {
                ApplicationArea = ALL;
            }

        }
        addafter("Location Code")
        {
            field(Subcontractor; Subcontractor)
            {
                ApplicationArea = ALL;
            }
        }
        addafter(General)
        {
            field("Vendor Location"; "Vendor Location")
            {
                ApplicationArea = all;
            }
            field("Service Group"; "Service Group")
            {
                ApplicationArea = all;
            }
            field("Order No."; "Order No.")
            {
                ApplicationArea = all;
            }
        }
        modify(Blocked)
        {
            Editable = false;
        }
        modify("No.")
        {
            ShowMandatory = true;
        }
        addbefore("VAT Registration No.")
        {
            field("TIN No."; "TIN No.")
            {
                ApplicationArea = all;
            }
        }
        addafter("Creditor No.")
        {
            field("Bank No."; "Bank No.")
            {
                ApplicationArea = all;
            }
            field("Bank Name"; "Bank Name")
            {
                ApplicationArea = all;
            }
            field("Bank Routing Code"; "Bank Routing Code")
            {
                ApplicationArea = all;
            }
            field("WHT Classification"; "WHT Classification")
            {
                ApplicationArea = all;
            }
            field("Vendor WHT Account"; "Vendor WHT Account")
            {
                ApplicationArea = all;
            }
            //BaluAug25>>
            field("Contact Person Name"; "Contact Person Name")
            {
                ApplicationArea = all;
            }
            //BaluAug25>>
        }
        addafter(City)
        {
            field(State; State)
            {
                ApplicationArea = ToBeClassified;
                ShowMandatory = true;
            }
        }
    }

    actions
    {
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Approval Status", "Approval Status"::Open);
                VendMandatory();
            END;
        }
        modify(CancelApprovalRequest)
        {
            trigger OnBeforeAction()
            begin
                TestField("Approval Status", "Approval Status"::"Pending for Approval");
            end;
        }
        addafter(SendApprovalRequest)
        {
            action("Release")
            {
                Image = ReleaseDoc;
                ApplicationArea = All;
                trigger OnAction()
                var
                /*                     SharePointInt: Codeunit "Share Point Integration";
                                    RecRef: RecordRef; */
                begin
                    VendMandatory();
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, WorkflowEventHandling.RunWorkflowOnSendVendorForApprovalcode()) then
                        error('Workflow is enabled. You can not release manually.');
                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        Modify();
                    end;
                    /*                     //SharePoint>>
                                        RecRef.GETTABLE(Rec);
                                        SharePointInt.OnReleasedocumentDetails(RecRef, false);
                                        //SharePoint<< */
                end;
            }
            action("Open")
            {
                Image = Open;
                ApplicationArea = All;
                trigger OnAction()

                begin
                    IF "Approval Status" = "Approval Status"::"Pending for Approval" THEN
                        ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 22);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        Modify();
                    end;
                end;
            }
            action("Block")
            {
                Image = Close;
                ApplicationArea = All;
                trigger OnAction()

                begin
                    IF Blocked = Blocked::" " then BEGIN
                        IF Confirm(' to block the vendor?', True, False) then BEGIN
                            Blocked := blocked::All;
                            Modify();
                        end;
                    end;
                end;
            }
            action("UnBlock")
            {
                Image = Open;
                ApplicationArea = All;
                trigger OnAction()

                begin
                    IF Blocked <> Blocked::" " then BEGIN
                        IF Confirm(' to Unblock the vendor?', True, False) then BEGIN
                            Blocked := blocked::" ";
                            Modify();
                        end;
                    end;
                end;
            }
        }
        addafter("F&unctions")
        {
            action(TransportContractType)
            {
                Image = List;
                ApplicationArea = All;
                Caption = 'Transport Contract Type';
                RunObject = page TransportContractTypesList;
                RunPageLink = "Vendor No." = field("No.");

            }
            action(WHTLedgerEntries)
            {
                Image = List;
                Caption = 'WHT Ledger Entries';
                RunObject = Page WHTLedgerEntries;
                RunPageLink = "Party No." = FIELD("No.");
                ApplicationArea = All;
            }
            action(TransportVehicle)
            {
                Image = List;
                Caption = 'Transport Vehicle';
                RunObject = page "Transporter Vehicle List";
                RunPageLink = "Vendor No." = field("No.");
                /*
                                ApplicationArea = ALL;
                                trigger OnAction()
                                var
                                    TransVehicle: Record "Transporter Vehicle";
                                BEGIN
                                    /*TransVehicle.reset;
                                    TransVehicle.SetRange();
                                    IF TransVehicle.findfirst the 
                                END;*/
            }
            action("Res&ponsibility Centers")
            {
                RunObject = page "Vend Resp. Cent. List";
                RunPageLink = "Vendor No." = FIELD("No.");
                Image = List;
                ApplicationArea = all;
            }
            //B2BMSOn20Oct21>>
            action("Contract")
            {
                RunObject = page "Contract Procurement List";
                RunPageLink = "Vendor No." = FIELD("No.");
                Image = List;
                ApplicationArea = all;
            }
            //B2BMSOn20Oct21<<
        }
        addafter(Attachments)
        {
            //G2S>>> 07/05/25>>> CAS-01435-X5M1P2
            action("Vendor Routing")
            {
                RunObject = page "Vendor Routing";
                RunPageLink = "Vendor No." = FIELD("No.");
                Image = List;
                ApplicationArea = All;
                Caption = 'Vendor Routing';
                Promoted = true;
                PromotedCategory = Category9;
                ToolTip = 'Add multiple routing numbers or banks for each vendor.';
            }
            //G2S>>> 07/05/25>>> CAS-01435-X5M1P2
        }
    }
    Procedure VendMandatory()
    BEGIN
        TestField("Vendor Classification");
        //TestField("Vendor Type");
        TestField("Gen. Bus. Posting Group");
        TestField("Vendor Posting Group");
        IF "Vendor Type" = "Vendor Type"::Import THEN
            TestField("Currency Code");
    END;

    var
        WorkflowManagement: codeunit "Workflow Management";
        WorkflowEventHandling: Codeunit "Workflow Event Handling";
        RecordRest: Record "Restricted Record";
}