tableextension 50175 WareHouseReceiptLne extends "Warehouse Receipt Line"
{
    fields
    {
        modify("Qty. to Receive")
        {
            trigger OnBeforeValidate()
            begin
                // ValidateSourceDocumentQty(Rec."No."); // >>>>>> G2S 8399-CAS-01406-B3C2S9 05/03/2023
            end;
        }
        field(50001; "Posted Loading Slip No."; Code[20])
        {
            DataClassification = CustomerContent;
            /*TableRelation = "Posted Loading Slip Line"."Document No." where("No." = field("No."), "Document Line No." = field("Line No."), "Applied for Receipt" = const(false));*/

            trigger OnLookup()
            var
                PosLodSlip: Record "Posted Loading Slip Line";
                PostedLodSlipLne: Record "Posted Loading Slip Line";
                PostedWarSHipmentLine: Record "Posted Whse. Shipment Line";
            BEGIN
                IF "Source Document" <> "Source Document"::"Inbound Transfer" then BEGIN
                    PosLodSlip.RESET;
                    PosLodSlip.SetRange("No.", "No.");
                    //PosLodSlip.SetRange("Document Line No.", "Line No.");//PKONJ9
                    PosLodSlip.SetRange("Order No.", "Source No.");//PKONJ9
                    PosLodSlip.SetRange("Order Line No.", "Source Line No.");//PKONJ9
                    PosLodSlip.SetRange("Applied for Receipt", false);
                    IF PosLodSlip.findset then
                        repeat
                            PostedLodSlipLne.reset;
                            PostedLodSlipLne.SetRange("Document No.", PosLodSlip."Document No.");
                            PostedLodSlipLne.SetRange("Line No.", PosLodSlip."Line No.");
                            IF PostedLodSlipLne.findfirst then
                                PostedLodSlipLne.Mark(true);
                        until PosLodSlip.Next() = 0;
                    PostedLodSlipLne.MarkedOnly(true);

                    IF page.RunModal(50560, PostedLodSlipLne) = ACTION::LookupOK then
                        VALIDATE("Posted Loading Slip No.", PostedLodSlipLne."Document No.");
                end else BEGIN
                    PostedWarSHipmentLine.RESET;
                    PostedWarSHipmentLine.SetRange("Source No.", "Source No.");
                    PostedWarSHipmentLine.SetRange("Source Line No.", "Source Line No.");
                    IF PostedWarSHipmentLine.FindSet() then
                        repeat
                            PostedLodSlipLne.reset;
                            PostedLodSlipLne.SetRange("Document No.", PostedWarSHipmentLine."Posted Loading Slip No.");
                            PostedLodSlipLne.SetRange("Line No.", PostedWarSHipmentLine."Posted Loading Slip Line No.");
                            PostedLodSlipLne.SetRange(Applied, true);
                            PostedLodSlipLne.SetRange("Applied for Receipt", false);
                            IF PostedLodSlipLne.findfirst then
                                PostedLodSlipLne.Mark(true);
                        until PostedWarSHipmentLine.next = 0;
                    PostedLodSlipLne.MarkedOnly(true);

                    IF page.RunModal(50560, PostedLodSlipLne) = ACTION::LookupOK then
                        VALIDATE("Posted Loading Slip No.", PostedLodSlipLne."Document No.");
                END;

            END;

            trigger OnValidate()
            var
                PostdLoadSlpGRec: record "Posted Loading Slip Line";

            BEGIN

                IF "Posted Loading Slip No." <> '' then BEGIN
                    PostdLoadSlpGRec.reset;
                    //PostdLoadSlpGRec.SetRange("No.", "No.");
                    //PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                    PostdLoadSlpGRec.SetRange("Document No.", "Posted Loading Slip No.");
                    PostdLoadSlpGRec.SetRange("Order No.", "Source No.");//PKONJU2
                    PostdLoadSlpGRec.SetRange("Order Line No.", "Source Line No.");//PKONJU2
                    PostdLoadSlpGRec.SetRange("Item No.", "Item No.");
                    IF PostdLoadSlpGRec.findfirst then BEGIN
                        //PostdLoadSlpGRec."Applied for Receipt" := true; //Pk On 29.04.2021
                        "Posted Loading Slip Line No." := PostdLoadSlpGRec."Line No.";
                        VALIDATE("Qty. to Receive", PostdLoadSlpGRec."Qty. Loading");
                        //PostdLoadSlpGRec.Modify();//Pk On 29.04.2021
                        message('%1', "Posted Loading Slip No.");
                    end;
                end else begin
                    /*PostdLoadSlpGRec.reset;
                    // PostdLoadSlpGRec.SetRange("No.", "No.");
                    PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                    PostdLoadSlpGRec.SetRange("Document No.", Xrec."Posted Loading Slip No.");
                    IF PostdLoadSlpGRec.findfirst then BEGIN
                        PostdLoadSlpGRec."Applied for Receipt" := false;
                        PostdLoadSlpGRec.Modify();
                    end;*///Pk On 29.04.2021
                    Clear("Posted Loading Slip Line No.");
                    Validate("Qty. to Receive", 0);
                    //message('Table %1...Base %2', "Qty. to Ship", "Qty. to Ship (Base)")
                end;
            end;
        }
        field(50002; "Posted Loading Slip Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = FALSE;
        }
        field(50003; "No. 2"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = FALSE;
        }
    }

    // >>>>>> G2S 8399-CAS-01406-B3C2S9 05/03/2023
    // Funtion to check the Qty. to Receive Percentage against Prepayment Percentage on purchase document
    // If Qty. to Receive Percentage is greater than Prepayment Percentage then show error message
    procedure ValidateSourceDocumentQty(var RecieptCode: Code[20])
    var
        NoPrepmtErrMsg: Label 'No Prepayment Invoice posted for this Document.';
        PurchaseInvHeader: Record "Purch. Inv. Header";
        PurchaseOrderLine: Record "Purchase Line";
        warehouseReceiptLine: Record "Warehouse Receipt Line";
        "Prepayment %": Decimal;
        ReceivedPercentage: Decimal;
        totalQty: Decimal;
        qtytoRecievePercent: Decimal;
        remainingQtyPercentage: Decimal;
    begin
        if Rec."Source Document" = "Source Document"::"Purchase Order" then begin
            PurchaseInvHeader.SetCurrentKey("Prepayment Order No.");
            PurchaseInvHeader.SetRange("Prepayment Order No.", Rec."Source No.");

            if not PurchaseInvHeader.FindFirst() then Error(NoPrepmtErrMsg);

            PurchaseOrderLine.Reset();
            PurchaseOrderLine.SetCurrentKey("Document No.", "No.");
            purchaseOrderLine.SetRange("Document No.", Rec."Source No.");
            purchaseOrderLine.SetRange("No.", Rec."Item No.");
            purchaseOrderLine.SetRange("Line No.", Rec."Source Line No.");
            if PurchaseOrderLine.FindFirst() then begin
                totalQty := PurchaseOrderLine.Quantity;
                "Prepayment %" := PurchaseOrderLine."Prepayment %";

                if totalQty = PurchaseOrderLine."Quantity Received" then
                    exit;

                if "Prepayment %" = 0 then // Check if prepayment line
                    exit;

                if PurchaseOrderLine."Quantity Received" <> 0 then
                    ReceivedPercentage := PurchaseOrderLine."Quantity Received" / totalQty * 100;

                remainingQtyPercentage := "Prepayment %" - ReceivedPercentage;

                if remainingQtyPercentage = 0 then
                    Error('There is no prepayment available to process to Qty. to Receive: %1', rec."Qty. to Receive");

                qtytoRecievePercent := rec."Qty. to Receive" / rec.Quantity * 100;

                if Round(qtytoRecievePercent, 0.00001, '<') > Round(remainingQtyPercentage, 0.00001, '<') then
                    Error('Qty. to Recieve cannot be greater than Prepayment Percent Quantity: %1', remainingQtyPercentage);

            end;
        end;
    end;
    // <<<<<< G2S 8399-CAS-01406-B3C2S9 05/03/2023
}