
pageextension 50142 PostedWareRcpLine extends "Posted Whse. Receipt Subform"
{
    layout
    {
        addafter("Item No.")
        {
            field("Posted Loading Slip No."; "Posted Loading Slip No.")
            {
                ApplicationArea = all;
            }
            field("Posted Loading Slip Line No."; "Posted Loading Slip Line No.")
            {
                ApplicationArea = all;
            }
        }
    }

    actions
    {
        addafter("Bin Contents List")
        {
            action("Whse. Receipt Lines")
            {
                ApplicationArea = all;
                Caption = 'Posted Scrap Lines.';
                Image = ReceiptLines;
                RunObject = Page "Posted Scrap Item List";
                RunPageLink = "Warehouse Doc No." = field("Whse. Receipt No."),
                                  "Warehouse Line No." = FIELD("Whse Receipt Line No.");
            }
        }
    }


    var
        myInt: Integer;
}
