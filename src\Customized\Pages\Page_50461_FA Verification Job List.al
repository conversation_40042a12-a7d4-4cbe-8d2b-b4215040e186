page 50461 "FA Verification Job List"
{
    // version TRKIT

    Editable = false;
    PageType = List;
    SourceTable = XX_TRKIT_FA_VERIFICATION_JOB;
    UsageCategory = Administration;
    ApplicationArea = All;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(VERIFICATION_TASK_NUMBER; VERIFICATION_TASK_NUMBER)
                {
                    ApplicationArea = All;
                }
                field(VERIFICATION_TASK_DESC; VERIFICATION_TASK_DESC)
                {
                    ApplicationArea = All;
                }
                field(START_DATE; START_DATE)
                {
                    ApplicationArea = All;
                }
                field(END_DATE; END_DATE)
                {
                    ApplicationArea = All;
                }
                field(STATUS; STATUS)
                {
                    ApplicationArea = All;
                }
                field(NAVISION_SYNC_TIME; NAVISION_SYNC_TIME)
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
    }
}

