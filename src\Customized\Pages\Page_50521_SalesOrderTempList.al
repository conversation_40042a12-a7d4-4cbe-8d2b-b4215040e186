page 50521 SalesOrderTempList
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = SalesTempLines;

    layout
    {
        area(Content)
        {
            repeater(Control10000000002)
            {
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = all;
                }
                field("New Order No."; "New Order No.")
                {
                    ApplicationArea = All;
                }
                field("Payment Method Code"; "Payment Method Code")
                {
                    ApplicationArea = All;
                }
                field("Sell-to Customer No."; "Sell-to Customer No.")
                {
                    ApplicationArea = all;
                }
                field(Type; Type)
                {
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
                field("Shipment Date"; "Shipment Date")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    ApplicationArea = all;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                }
                field("Outstanding Quantity"; "Outstanding Quantity")
                {
                    ApplicationArea = all;
                }
                field("Qty. to Ship"; "Qty. to Ship")
                {
                    ApplicationArea = all;
                }
                field("Unit Price"; "Unit Price")
                {
                    ApplicationArea = all;
                }
                field("Unit Cost (LCY)"; "Unit Cost (LCY)")
                {
                    ApplicationArea = all;
                }
                field("VAT %"; "VAT %")
                {
                    ApplicationArea = all;
                }
                field("Line Discount %"; "Line Discount %")
                {
                    ApplicationArea = all;
                }
                field("Line Discount Amount"; "Line Discount Amount")
                {
                    ApplicationArea = all;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                }
                field("Gross Weight"; "Gross Weight")
                {
                    ApplicationArea = all;
                }
                field("Net Weight"; "Net Weight")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 3 Code"; "Shortcut Dimension 3 Codes")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 5 Code"; "Shortcut Dimension 5 Codes")
                {
                    ApplicationArea = all;
                }
                field("Outstanding Amount"; "Outstanding Amount")
                {
                    ApplicationArea = all;
                }
                field("VAT Calculation Type"; "VAT Calculation Type")
                {
                    ApplicationArea = all;
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                    ApplicationArea = all;
                }
                field("VAT Prod. Posting Group"; "VAT Prod. Posting Group")
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                }
                field("Outstanding Amount (LCY)"; "Outstanding Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("VAT Base Amount"; "VAT Base Amount")
                {
                    ApplicationArea = all;
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = all;
                }
                field("Prepayment %"; "Prepayment %")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. Line Amount"; "Prepmt. Line Amount")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. Amt. Incl. VAT"; "Prepmt. Amt. Incl. VAT")
                {
                    ApplicationArea = all;
                }
                field("Prepayment Amount"; "Prepayment Amount")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. VAT Base Amt."; "Prepmt. VAT Base Amt.")
                {
                    ApplicationArea = all;
                }
                field("Prepayment VAT %"; "Prepayment VAT %")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. VAT Calc. Type"; "Prepmt. VAT Calc. Type")
                {
                    ApplicationArea = all;
                }
                field("Prepayment VAT Identifier"; "Prepayment VAT Identifier")
                {
                    ApplicationArea = all;
                }
                field("Prepayment Tax Area Code"; "Prepayment Tax Area Code")
                {
                    ApplicationArea = all;
                }
                field("Prepayment Tax Liable"; "Prepayment Tax Liable")
                {
                    ApplicationArea = all;
                }
                field("Prepayment Tax Group Code"; "Prepayment Tax Group Code")
                {
                    ApplicationArea = all;
                }
                field("Prepmt Amt to Deduct"; "Prepmt Amt to Deduct")
                {
                    ApplicationArea = all;
                }
                field("Prepmt Amt Deducted"; "Prepmt Amt Deducted")
                {
                    ApplicationArea = all;
                }
                field("Prepayment Line"; "Prepayment Line")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. Amount Inv. Incl. VAT"; "Prepmt. Amount Inv. Incl. VAT")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. Amount Inv. (LCY)"; "Prepmt. Amount Inv. (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Prepmt. VAT Amount Inv. (LCY)"; "Prepmt. VAT Amount Inv. (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Prepayment VAT Difference"; "Prepayment VAT Difference")
                {
                    ApplicationArea = all;
                }
                field("Prepmt VAT Diff. to Deduct"; "Prepmt VAT Diff. to Deduct")
                {
                    ApplicationArea = all;
                }
                field("Prepmt VAT Diff. Deducted"; "Prepmt VAT Diff. Deducted")
                {
                    ApplicationArea = all;
                }
                field("Pmt. Discount Amount"; "Pmt. Discount Amount")
                {
                    ApplicationArea = all;
                }
                field("Variant Code"; "Variant Code")
                {
                    ApplicationArea = all;
                }
                field("Bin Code"; "Bin Code")
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field("Quantity (Base)"; "Quantity (Base)")
                {
                    ApplicationArea = all;
                }
                field("Outstanding Qty. (Base)"; "Outstanding Qty. (Base)")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Purchasing Code"; "Purchasing Code")
                {
                    ApplicationArea = all;
                }
                field("Shipping Time"; "Shipping Time")
                {
                    ApplicationArea = all;
                }
                field("Planned Delivery Date"; "Planned Delivery Date")
                {
                    ApplicationArea = all;
                }
                field("Planned Shipment Date"; "Planned Shipment Date")
                {
                    ApplicationArea = all;
                }
                field("Order Status"; "Order Statuss")
                {
                    ApplicationArea = all;
                }
                field("Promo. No."; "Promo. No.")
                {
                    ApplicationArea = all;
                }
                field("Promo. Line No."; "Promo. Line No.")
                {
                    ApplicationArea = all;
                }
                field("Gift Item"; "Gift Item")
                {
                    ApplicationArea = all;
                }
                field("Gift Item Quantity"; "Gift Item Quantity")
                {
                    ApplicationArea = all;
                }
                field("Promo Qty. Util."; "Promo Qty. Util.")
                {
                    ApplicationArea = all;
                }
                field("Promo. Offer Line No."; "Promo. Offer Line No.")
                {
                    ApplicationArea = all;
                }
                field("Lot No"; "Lot No")
                {
                    ApplicationArea = all;
                }
                field("Document Types"; "Document Type")
                {
                    ApplicationArea = all;
                }/*
                field(No; No)
                {
                    ApplicationArea = all;
                }*/
                field("Ship-to Code"; "Ship-to Code")
                {
                    ApplicationArea = all;
                }
                field("Order Date"; "Order Date")
                {
                    ApplicationArea = all;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Shipment Dates"; "Shipment Dates")
                {
                    ApplicationArea = all;
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                    ApplicationArea = all;
                }
                field("Due Date"; "Due Date")
                {
                    ApplicationArea = all;
                }
                field("Shipment Method Code"; "Shipment Method Code")
                {
                    ApplicationArea = all;
                }
                field("Location Codes"; "Location Codes")
                {
                    ApplicationArea = all;
                }
                field("Currency Codes"; "Currency Codes")
                {
                    ApplicationArea = all;
                }
                field("Salesperson Code"; "Salesperson Code")
                {
                    ApplicationArea = all;
                }
                field("Transport Method"; "Transport Method")
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                }
                field("External Document No."; "External Document No.")
                {
                    ApplicationArea = all;
                }
                field("Shipping Agent Code"; "Shipping Agent Code")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Centers"; "Responsibility Centers")
                {
                    ApplicationArea = all;
                }
                field("Shipping Advice"; "Shipping Advice")
                {
                    ApplicationArea = all;
                }
                field("Shipping Times"; "Shipping Times")
                {
                    ApplicationArea = all;
                }
                field("Order Statuss"; "Order Statuss")
                {
                    ApplicationArea = all;
                }
                field("Order Tracking"; "Order Tracking")
                {
                    ApplicationArea = all;
                }
                field("Rebate Period Code"; "Rebate Period Code")
                {
                    ApplicationArea = all;
                }
                field("User Wise Locations"; "User Wise Locations")
                {
                    ApplicationArea = all;
                }
                field("User Wise Resp Centr"; "User Wise Resp Centr")
                {
                    ApplicationArea = all;
                }

                field("Reason Code"; "Reason Code")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Create Sales Documents")
            {
                ApplicationArea = All;

                trigger OnAction()
                begin
                    createOrders();
                end;
            }
        }
    }

    var
        myInt: Integer;

    procedure createOrders()
    Var
        Templi: Record SalesTempLines;
        TempliR: Record SalesTempLines;
        SalesHder: Record "Sales Header";
        SalesLines: record "Sales Line";
        PrevOrd: Text;
        WindPa: Dialog;
        ItemLvar: Record Item;
        SalesHder2: Record "Sales Header";
        Saleslines2: Record "Sales Line";
        custrec: Record Customer;
    begin
        IF Confirm('Do you want to Create Sales Documents ?', true, false) then begin
            SalesHder2.LockTable();
            Templi.RESET;
            Templi.SetCurrentKey("Document No.");
            Templi.SETFILTER("New Order No.", '%1', '');
            IF Templi.FindSet() then BEGIN
                WindPa.Open('Processing #1###############');
                repeat
                    IF (PrevOrd <> Templi."Document No.")
                     THEN BEGIN
                        TempliR.RESET;
                        TempliR.SetCurrentKey("Document No.");
                        TempliR.SETRANGE("Document No.", Templi."Document No.");
                        TempliR.SETFILTER("New Order No.", '%1', '');
                        IF TempliR.FindSet() then begin
                            WindPa.Open(TempliR."Document No.");

                            if not SalesHder2.get(TempliR."Document Type", TempliR."Document No.") then begin
                                SalesHder.INIT;
                                SalesHder."Document Type" := TempliR."Document Type";
                                SalesHder."No." := TempliR."Document No.";
                                SalesHder."No. Series" := 'SALESORDER';
                                SalesHder."Posting Date" := TempliR."Posting Date";
                                SalesHder."Sales Type" := TempliR."Sales Type";
                                if "Shortcut Dimension 1 Code" = 'RET' THEN begin
                                    SalesHder.validate("POS Window", true);
                                    SalesHder.VALIDATE("Payment Method Code", "Payment Method Code");
                                end;
                                SalesHder.Insert(true);
                                SalesHder.Validate("Responsibility Center", TempliR."Responsibility Center");
                                SalesHder.Validate("Shortcut Dimension 1 Code", TempliR."Shortcut Dimension 1 Code");
                                SalesHder.Validate("Shortcut Dimension 2 Code", TempliR."Shortcut Dimension 2 Code");
                                //SalesHder.Validate("User Wise Resp Centr", TempliR."Responsibility Center");
                                SalesHder.Validate("Sell-to Customer No.", TempliR."Sell-to Customer No.");
                                // SalesHder.Validate("User Wise Locations", TempliR."Location Code");
                                SalesHder.Validate("Location Code", TempliR."Location Code");
                                SalesHder.Validate("Currency Code", TempliR."Currency Code");
                                SalesHder.Validate("Posting Date", TempliR."Posting Date");

                                //SalesHder.Validate("Payment Method Code", TempliR."Payment Terms Code" );
                                SalesHder.Validate("Payment Terms Code", TempliR."Payment Terms Code");
                                if custrec.get(TempliR."Sell-to Customer No.") then
                                    if custrec."Ship-to Code" <> '' then
                                        SalesHder.Validate("Ship-to Code", TempliR."Ship-to Code");
                                SalesHder.Validate("Reason Code", TempliR."Reason Code");
                                SalesHder.Validate("Cr. Memo Reason Type", TempliR."Cr. Memo Reason Type");
                                SalesHder."Location Code" := TempliR."Location Code";
                                SalesHder."Loading Slip Required" := false;
                                //SalesHder.Status := SalesHder.Status::Released;
                                SalesHder.Modify();
                            end;
                            repeat
                                if not Saleslines2.get(TempliR."Document Type", TempliR."Document No.", TempliR."Line No.") then begin
                                    SalesLines.INIT;
                                    SalesLines."Document Type" := SalesHder."Document Type";
                                    SalesLines."Document No." := SalesHder."No.";
                                    SalesLines."Line No." := TempliR."Line No.";
                                    SalesLines.Insert(true);
                                    SalesLines.VALIDATE(Type, TempliR.Type);
                                    IF TempliR.Type = TempliR.Type::Item then begin
                                        ItemLvar.get(TempliR."No.");
                                        SalesLines."Item Category" := ItemLvar."Item Category Code";
                                        SalesLines."Category Wise Items" := TempliR."No.";
                                    end;
                                    SalesLines.Validate("No.", TempliR."No.");
                                    SalesLines.Validate(Description, TempliR.Description);
                                    SalesLines.Validate("Description 2", TempliR."Description 2");
                                    SalesLines.Validate("Location Code", TempliR."Location Code");
                                    SalesLines.Validate(Quantity, TempliR.Quantity);
                                    SalesLines.Validate("Unit of Measure Code", TempliR."Unit of Measure Code");
                                    SalesLines.Validate("Unit Price", TempliR."Unit Price");
                                    SalesLines.Validate("Shortcut Dimension 1 Code", TempliR."Shortcut Dimension 1 Code");
                                    SalesLines.Validate("Shortcut Dimension 2 Code", TempliR."Shortcut Dimension 2 Code");
                                    if "Shortcut Dimension 1 Code" = 'RET' THEN begin
                                        SalesLines.validate("POS Window", true);
                                    end;
                                    //GetDims(SalesLines);
                                    if TempliR."Shortcut Dimension 5 Codes" <> '' then
                                        SalesLines.ValidateShortcutDimCode(5, TempliR."Shortcut Dimension 5 Codes");
                                    if TempliR."Shortcut Dimension 3 Codes" <> '' then
                                        SalesLines.ValidateShortcutDimCode(3, TempliR."Shortcut Dimension 3 Codes");
                                    //SalesLines.Validate("VAT Prod. Posting Group", TempliR."VAT Prod. Posting Group");
                                    //SalesLines.Validate("VAT Bus. Posting Group", TempliR."VAT Bus. Posting Group");
                                    SalesLines.Validate("Gift Item", TempliR."Gift Item");
                                    SalesLines.Validate("Gift Item Quantity", TempliR."Gift Item Quantity");
                                    SalesLines.Modify();

                                    SalesLines.SetItemTracking(); //added for tracking 2/22/2023

                                    TempliR."New Order No." := SalesHder."No.";
                                    TempliR.Modify();
                                    Commit();
                                end;
                            until TempliR.next = 0;
                        end;
                        // if SalesHder2."No." <> '' then
                        // PrevOrd := 'N/A' else
                        PrevOrd := TempliR."Document No.";
                    end;
                until Templi.next = 0;
            end;
            WindPa.Close();
        end;
        Message('Completed.');
    end;

    procedure GetDims(var Salin: Record "Sales Line");
    var
        DefaltDim: record "Default Dimension";
        DimMgmt: Codeunit DimensionManagement;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
    begin
        TempDimSetEntry.DELETEALL();

        TempDimSetEntry.INIT;
        TempDimSetEntry.VALIDATE("Dimension Set ID", 0);
        TempDimSetEntry.VALIDATE("Dimension Code", 'ACCLOC');
        TempDimSetEntry.VALIDATE("Dimension Value Code", "Shortcut Dimension 1 Code");
        TempDimSetEntry.INSERT(true);
        TempDimSetEntry.INIT;
        TempDimSetEntry.VALIDATE("Dimension Set ID", 0);
        TempDimSetEntry.VALIDATE("Dimension Code", 'CC');
        TempDimSetEntry.VALIDATE("Dimension Value Code", "Shortcut Dimension 2 Code");
        TempDimSetEntry.INSERT(true);
        TempDimSetEntry.INIT;
        TempDimSetEntry.VALIDATE("Dimension Set ID", 0);
        TempDimSetEntry.VALIDATE("Dimension Code", 'BRAND');
        TempDimSetEntry.VALIDATE("Dimension Value Code", "Shortcut Dimension 3 Codes");
        TempDimSetEntry.INSERT(true);
        TempDimSetEntry.INIT;
        TempDimSetEntry.VALIDATE("Dimension Set ID", 0);
        TempDimSetEntry.VALIDATE("Dimension Code", 'SEGMENT');
        TempDimSetEntry.VALIDATE("Dimension Value Code", "Shortcut Dimension 5 Codes");
        TempDimSetEntry.INSERT(true);

        Salin."Dimension Set ID" := DimMgmt.GetDimensionSetID(TempDimSetEntry);
        DimMgmt.UpdateGlobalDimFromDimSetID(Salin."Dimension Set ID", Salin."Shortcut Dimension 1 Code",
        Salin."Shortcut Dimension 2 Code");
    end;
}