tableextension 50009 CustLedEntry21 extends "Cust. Ledger Entry"
{
    fields
    {
        field(50000; "Responsibility Center"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center";
        }
        field(50026; Narration; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50027; Narration1; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50028; "Sales Rebate Period"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Rebate Period Codes".code;
        }
        field(50029; "Description 2"; Text[100])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50030; "Teller / Cheque No."; Code[30])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50031; "Qty per branch"; Decimal)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50032; "Paid By"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        //BaluOnJu7
        field(50086; "Bank Name"; Text[40])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50087; "Teller Bank Name"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,FBNMERCHANT,RANDMERCHANT,CORONATION,PROVIDUS"';
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,FBNMERCHANT,RANDMERCHANT,CORONATION,PROVIDUS;
        }
        field(50085; "Teller / Cheque Date"; Date)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50089; "TcrLimit"; decimal)
        {
            DataClassification = CustomerContent;
        }
        //BaluOnJu7
        //BaluOnJul27>>
        field(50004; "Product Group Code"; Code[50])
        {
            //TableRelation = "Product Group";
        }
        field(50005; "business unit code"; Code[50])
        {
            TableRelation = "Business Unit";
        }
        field(50006; "POS Transaction Type"; Option)
        {
            CalcFormula = Lookup("Sales Invoice Header"."POS Transaction Type" WHERE("No." = FIELD("Document No.")));
            Description = 'GJ_CHIPOS_RKD_181113';
            Editable = false;
            FieldClass = FlowField;
            OptionCaption = ',Cash,Card,Both';
            OptionMembers = ,Cash,Card,Both;
        }
        field(50007; "POS Transaction No."; Code[50])
        {
            CalcFormula = Lookup("Sales Invoice Header"."POS Transaction No." WHERE("No." = FIELD("Document No.")));
            Description = 'GJ_CHIPOS_RKD_181113';
            Editable = false;
            FieldClass = FlowField;
        }
        //BaluOnJul27>>
        field(50100; "Bckp Applies-to ID"; code[50])//PKONAU9
        {
            DataClassification = CustomerContent;
        }

    }

}