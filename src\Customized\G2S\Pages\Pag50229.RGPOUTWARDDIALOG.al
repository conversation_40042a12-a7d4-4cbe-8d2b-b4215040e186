// >>>>>> G2S 28/05/2024 CAS-01274-L1T5K01
page 50229 "RGP-OUTWARD DIALOG"
{
    Caption = 'RGP-OUTWARD DIALOG';
    PageType = StandardDialog;

    layout
    {
        area(content)
        {
            field("RGP-OUTWARD No."; "RGP-OUTWARD No.")
            {
                Caption = 'RGP-OUTWARD NO.';
                TableRelation = "Posted Gate Entry Header"."No." where("Entry Type" = FILTER(Outward), Type = const(RGP));
                ShowMandatory = true;
                trigger OnValidate()
                begin
                    SetRGP("RGP-OUTWARD No.");
                end;
            }
        }
    }


    procedure getRGP(): Code[30]
    begin
        exit("RGP-OUTWARD No.");
    end;

    procedure SetRGP(var rgpNo: code[30])
    begin
        "RGP-OUTWARD No." := rgpNo;
    end;

    var
        "RGP-OUTWARD No.": Code[20];
}
// >>>>>> G2S 28/05/2024 CAS-01274-L1T5K01

