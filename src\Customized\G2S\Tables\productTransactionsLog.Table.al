/// <summary>
/// Table Product Transaction Log (ID 50100).
/// >>>>>>>> Go2solveMay2023
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
table 50049 "Product Transaction Log"
{
    DataClassification = CustomerContent;

    fields
    {
        field(100; ID; Integer)
        {
            DataClassification = CustomerContent;
            AutoIncrement = true;
        }
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = CustomerContent;
        }
        field(3; Description; Text[100])
        {
            Caption = 'Description';
            DataClassification = CustomerContent;
        }
        field(10; Type; Text[250])
        {
            Caption = 'Type';
            DataClassification = CustomerContent;
        }
        field(5702; "Item Category Code"; Code[20])
        {
            Caption = 'Item Category Code';
            DataClassification = CustomerContent;
        }
        field(8005; "Item Category Id"; Text[250])
        {
            Caption = 'Item Category Id';
            DataClassification = CustomerContent;
        }
        field(4; prod_custom_code; Code[50])
        {
            DataClassification = CustomerContent;
        }
        field(5; prod_short_name; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(6; prod_cbb_volume; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(7; prod_pack_volume; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(9; product_type_name; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(12; brand_code; Code[100])
        {
            DataClassification = CustomerContent;
        }
        field(13; brand_name; Text[150])
        {
            DataClassification = CustomerContent;
        }
        field(14; sub_brand_code; Code[100])
        {
            DataClassification = CustomerContent;
        }
        field(15; sub_brand_name; Text[150])
        {
            DataClassification = CustomerContent;
        }
        field(16; sku_group_code; Code[100])
        {
            DataClassification = CustomerContent;
        }
        field(17; sku_group_name; Text[150])
        {
            DataClassification = CustomerContent;
        }
        field(18; prod_hsn_code; Code[100])
        {
            DataClassification = CustomerContent;
        }
        field(19; comp_code; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(20; tax1perc; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(21; tax2perc; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(22; tax3perc; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(23; sentStatus; Boolean)
        {
            DataClassification = CustomerContent;
            InitValue = false;
        }
        field(24; Remark; Text[300])
        {
            DataClassification = CustomerContent;
        }

        field(25; prod_cat_code; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(26; prod_cat_name; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(27; product_type_code; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(28; "Temp. Omit Rec"; Boolean)
        {
            Caption = 'Temporarily omit record from batch';
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                myInt: Integer;
            begin
                if SentStatus = true then
                    Error('Record has already been successfully pushed to Outreach. You can''t check this field');
            end;
        }
        field(29; "Date Sent to Outreach"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(30; "DateTime Sent to Outreach"; DateTime)
        {
            DataClassification = CustomerContent;
        }
    }


    keys
    {
        key(PK; ID)
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    begin

    end;

    trigger OnModify()
    begin

    end;

    trigger OnDelete()
    begin

    end;

    trigger OnRename()
    begin

    end;
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<