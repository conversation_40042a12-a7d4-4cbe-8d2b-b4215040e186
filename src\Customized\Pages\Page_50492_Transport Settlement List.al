page 50492 "Transport Settlement List"
{
    PageType = List;
    CardPageId = "Transport Settlement Doc";
    SourceTable = "Transport Settlement Header";
    UsageCategory = lists;
    ApplicationArea = all;
    Editable = false;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = all;
                }
                field("Transporter Cont. Type"; "Transporter Cont. Type")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

