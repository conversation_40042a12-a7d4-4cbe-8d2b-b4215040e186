﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>db6999ab-c98e-4b58-9375-994845f17cc1</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>17.543cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>4.10271cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle2">
                          <ReportItems>
                            <Tablix Name="Tablix4">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>2.5cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>5.32685cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.411cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.77386cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.108cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.674cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox27">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!No_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox27</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox223">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox223</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox31">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitofMeasure_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox31</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox197">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>11pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox197</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox44">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox39</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox45">
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TOTALVALUE.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                      <Format>N2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox40</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox18">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!COPYSTR_LOTNO_1_70.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox2</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>6</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox98">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!COPYSTR_LOTNO_71.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox85</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>6</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox46">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!No_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox33</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox47">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox35</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox48">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitofMeasure_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox36</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox57">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>11pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox38</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox81">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox39</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox82">
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TOTALVALUE.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox40</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox58">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!No_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox33</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox113">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox35</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox114">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitofMeasure_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox36</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox115">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>11pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox38</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox116">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox39</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox117">
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TOTALVALUE.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox40</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox118">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!No_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox33</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox119">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox35</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox120">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitofMeasure_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox36</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox121">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>11pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox38</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox122">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice_SalesInvoiceLine.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox39</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox123">
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TOTALVALUE.Value</Value>
                                                    <Style>
                                                      <FontSize>9pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox40</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="DocumentNo_SalesInvoiceLine1">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!LineNo_SalesInvoiceLine.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <SortExpressions>
                                      <SortExpression>
                                        <Value>=Fields!DocumentNo_SalesInvoiceLine.Value</Value>
                                      </SortExpression>
                                    </SortExpressions>
                                    <TablixMembers>
                                      <TablixMember>
                                        <Group Name="Details2" />
                                        <TablixMembers>
                                          <TablixMember>
                                            <Visibility>
                                              <Hidden>=iif((Fields!ShowSalesInvLine.Value=2)AND(Fields!Bags.Value=0),false,true)</Hidden>
                                            </Visibility>
                                          </TablixMember>
                                          <TablixMember>
                                            <Visibility>
                                              <Hidden>true</Hidden>
                                            </Visibility>
                                          </TablixMember>
                                          <TablixMember>
                                            <Visibility>
                                              <Hidden>true</Hidden>
                                            </Visibility>
                                          </TablixMember>
                                          <TablixMember>
                                            <Visibility>
                                              <Hidden>=iif((Fields!ShowSalesInvLine.Value=2)AND(Fields!Bags.Value&lt;&gt;0),false,true)</Hidden>
                                            </Visibility>
                                          </TablixMember>
                                          <TablixMember>
                                            <Visibility>
                                              <Hidden>=IIF(Fields!ShowSalesInvLine.Value=1,FALSE,TRUE)</Hidden>
                                            </Visibility>
                                          </TablixMember>
                                          <TablixMember>
                                            <Visibility>
                                              <Hidden>=IIF(Fields!ShowSalesInvLine.Value=3,FALSE,TRUE)</Hidden>
                                            </Visibility>
                                          </TablixMember>
                                        </TablixMembers>
                                      </TablixMember>
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <Top>0.50271cm</Top>
                              <Left>0.15311cm</Left>
                              <Height>3.6cm</Height>
                              <Width>15.79371cm</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                            <Tablix Name="Tablix1">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>1.59618cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox28">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style />
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox28</rd:DefaultName>
                                            <Visibility>
                                              <Hidden>=Code.SetData(Cstr(Last(Fields!TOTALVALUE1.Value)) + Chr(177) + 
Cstr(Sum(Fields!LineDiscountAmount_SalesInvoiceLine.Value)) + Chr(177) + 
Cstr(Last(Fields!AmtInclVat.Value)) + Chr(177) + 
Cstr(Last(Fields!Qty.Value)-Last(Fields!GLQty.Value)) + Chr(177) + 
Cstr(Fields!Location_Code.Value) + Chr(177) + 
Cstr(Last(Fields!AmtInclVat.Value)-Last(Fields!TOTALVALUE1.Value)) + Chr(177) + 
Cstr(Fields!Amountinword.Value) + Chr(177) + 
Cstr(Fields!Authorisers_ID.Value)+Chr(177)+
Cstr(Fields!TotalQtyCaption.Value)+Chr(177)+
Cstr(Fields!AmountinwordsCaption.Value)+Chr(177)+
Cstr(Fields!LocationCaption.Value)+Chr(177)+
Cstr(Fields!PostedbyCaption.Value)+Chr(177)+
Cstr(Fields!DateAndTimeCaption.Value)+Chr(177)+
Cstr(Fields!TotalAmtCaption.Value)+Chr(177)+
Cstr(Fields!AmtinclvatCaption.Value)+Chr(177)+
Cstr(Fields!LineDiscAmtCaption.Value)+Chr(177)+Cstr(Fields!AmtExclVatCaption.Value),2)</Hidden>
                                            </Visibility>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <Top>2.30271cm</Top>
                              <Left>15.94682cm</Left>
                              <Height>0.6cm</Height>
                              <Width>1.59618cm</Width>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details1">
                    <GroupExpressions>
                      <GroupExpression>=Fields!No_SalesInvoiceHeader.Value</GroupExpression>
                      <GroupExpression>=Fields!CopyNo.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.36074cm</Top>
            <Left>0.0635cm</Left>
            <Height>4.10271cm</Height>
            <Width>17.543cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>4.6222cm</Height>
        <Style />
      </Body>
      <Width>17.99533cm</Width>
      <Page>
        <PageHeader>
          <Height>4.66317cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox1">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!ReprintCaption.Value</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>15pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>Underline</TextDecoration>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>0cm</Top>
              <Left>6.3cm</Left>
              <Height>1.137cm</Height>
              <Width>5.88cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox3">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!ExternalDocumentNo_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox3</rd:DefaultName>
              <Top>0cm</Top>
              <Left>14.953cm</Left>
              <Height>0.423cm</Height>
              <Width>2.419cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Bottom</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox14">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Customer_Reference_No_.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox14</rd:DefaultName>
              <Top>0.83cm</Top>
              <Left>14.544cm</Left>
              <Height>0.50238cm</Height>
              <Width>3.203cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox15">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!No_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox15</rd:DefaultName>
              <Top>1.5425cm</Top>
              <Left>14.0905cm</Left>
              <Height>0.63872cm</Height>
              <Width>3.799cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox16">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!PostingDate_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>11pt</FontSize>
                        <Format>d</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox16</rd:DefaultName>
              <Top>2.407cm</Top>
              <Left>14.556cm</Left>
              <Height>0.74475cm</Height>
              <Width>2.1cm</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox55">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!DueDate_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Format>M/d/yyyy</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox55</rd:DefaultName>
              <Top>3.709cm</Top>
              <Left>14.485cm</Left>
              <Height>0.423cm</Height>
              <Width>2.342cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox11">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Old_Delivery_Effected_By.Value</Value>
                      <Style>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox11</rd:DefaultName>
              <Top>3.23458cm</Top>
              <Left>8.9571cm</Left>
              <Height>0.423cm</Height>
              <Width>5.16733cm</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox12">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!LPOReference_Caption.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox12</rd:DefaultName>
              <Top>1.23725cm</Top>
              <Left>7.33775cm</Left>
              <Height>0.423cm</Height>
              <Width>3.339cm</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox13">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!YourReference_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox13</rd:DefaultName>
              <Top>1.23725cm</Top>
              <Left>10.75575cm</Left>
              <Height>0.423cm</Height>
              <Width>2.462cm</Width>
              <ZIndex>8</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!OrderNo_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Top>3.838cm</Top>
              <Left>1.97cm</Left>
              <Height>0.423cm</Height>
              <Width>3.68cm</Width>
              <ZIndex>9</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox9">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Addr4.Value+Fields!Addr5.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox6</rd:DefaultName>
              <Top>3.221cm</Top>
              <Left>0.47844cm</Left>
              <Height>0.423cm</Height>
              <Width>8.40105cm</Width>
              <ZIndex>10</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox8">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Addr3.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox6</rd:DefaultName>
              <Top>2.79225cm</Top>
              <Left>0.4855cm</Left>
              <Height>0.423cm</Height>
              <Width>12.4885cm</Width>
              <ZIndex>11</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox7">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Addr2.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox6</rd:DefaultName>
              <Top>2.36925cm</Top>
              <Left>0.4855cm</Left>
              <Height>0.423cm</Height>
              <Width>12.4885cm</Width>
              <ZIndex>12</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox6">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Addr1.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox6</rd:DefaultName>
              <Top>1.94225cm</Top>
              <Left>0.4855cm</Left>
              <Height>0.423cm</Height>
              <Width>12.4885cm</Width>
              <ZIndex>13</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox5">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!SelltoCustomerNo_SalesInvoiceHeader.Value</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox5</rd:DefaultName>
              <Top>1.50225cm</Top>
              <Left>0.5025cm</Left>
              <Height>0.423cm</Height>
              <Width>5.1475cm</Width>
              <ZIndex>14</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox29">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CustPh.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Normal</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox5</rd:DefaultName>
              <Top>1.01575cm</Top>
              <Left>0.5025cm</Left>
              <Height>0.423cm</Height>
              <Width>5.65639cm</Width>
              <ZIndex>15</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>5.00712cm</Height>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox4">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Code.Getdata(4,2)</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox4</rd:DefaultName>
              <Top>0.211cm</Top>
              <Left>10.141cm</Left>
              <Height>0.423cm</Height>
              <Width>2.76cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox17">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Code.Getdata(7,2)</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox17</rd:DefaultName>
              <Top>0.846cm</Top>
              <Left>1.666cm</Left>
              <Height>0.899cm</Height>
              <Width>11.308cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox24">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=User!UserID</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>4.39965cm</Top>
              <Left>0.127cm</Left>
              <Height>0.45475cm</Height>
              <Width>6.34321cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox23">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Code.Getdata(5,2)</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox23</rd:DefaultName>
              <Top>2.67839cm</Top>
              <Left>6.74879cm</Left>
              <Height>0.423cm</Height>
              <Width>4.152cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox25">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Code.Getdata(8,2)</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox25</rd:DefaultName>
              <Top>3.9869cm</Top>
              <Left>7.03204cm</Left>
              <Height>0.51825cm</Height>
              <Width>2.31cm</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox26">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Globals!ExecutionTime</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox26</rd:DefaultName>
              <Top>3.76465cm</Top>
              <Left>11.88899cm</Left>
              <Height>0.51825cm</Height>
              <Width>5.29676cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox19">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=CDEC(Code.Getdata(1,2))</Value>
                      <Style>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Format>#,0.00</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox19</rd:DefaultName>
              <Top>0.238cm</Top>
              <Left>14.8385cm</Left>
              <Height>0.423cm</Height>
              <Width>2.76cm</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Textbox>
            <Textbox Name="Textbox20">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=CDEC(Code.Getdata(2,2))</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <Format>#,0.00</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox20</rd:DefaultName>
              <Top>0.714cm</Top>
              <Left>14.8485cm</Left>
              <Height>0.423cm</Height>
              <Width>2.758cm</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox21">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=CDEC(Code.Getdata(6,2))</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <Format>#,0.00</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox21</rd:DefaultName>
              <Top>1.322cm</Top>
              <Left>14.8425cm</Left>
              <Height>0.423cm</Height>
              <Width>2.761cm</Width>
              <ZIndex>8</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox22">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=CDEC(Code.Getdata(3,2))</Value>
                      <Style>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Format>#,0.00</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox22</rd:DefaultName>
              <Top>1.963cm</Top>
              <Left>14.8475cm</Left>
              <Height>0.55529cm</Height>
              <Width>2.758cm</Width>
              <ZIndex>9</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox30">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PSLDriverCo0nt.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>3.76851cm</Top>
              <Left>0.09525cm</Left>
              <Height>0.51825cm</Height>
              <Width>6.37496cm</Width>
              <ZIndex>10</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox32">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PSLDriver.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>3.17265cm</Top>
              <Left>0.09525cm</Left>
              <Height>0.51825cm</Height>
              <Width>6.37496cm</Width>
              <ZIndex>11</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox33">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PSLvehicle.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>2.61912cm</Top>
              <Left>0.127cm</Left>
              <Height>0.51825cm</Height>
              <Width>6.34321cm</Width>
              <ZIndex>12</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox34">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PSLNo.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontStyle>Italic</FontStyle>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>2.10087cm</Top>
              <Left>0.09525cm</Left>
              <Height>0.51825cm</Height>
              <Width>6.37496cm</Width>
              <ZIndex>13</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>1.7cm</LeftMargin>
        <RightMargin>1cm</RightMargin>
        <TopMargin>4.2cm</TopMargin>
        <BottomMargin>6.4cm</BottomMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function


Shared offset as Integer
Shared newPage as Object
Shared currentgroup1 as Object
Shared currentgroup2 as Object
Public Function GetGroupPageNumber(NewPage as Boolean, pagenumber as Integer) as Object
  If NewPage
    offset = pagenumber - 1
  End If
  Return pagenumber - offset
End Function

Public Function IsNewPage(groups as Object) As Boolean
newPage = FALSE
dim group1 as Object
dim group2 as Object
group1=CStr(Choose(1,Split(CStr(groups),Chr(177))))
group2=CStr(Choose(2,Split(CStr(groups),Chr(177))))
If Not (group1 = currentgroup1)
    newPage = TRUE
    currentgroup1 = group1
    currentgroup2 = group2
    ELSE 
      If Not (group2 = currentgroup2)
        newPage = TRUE 
        currentgroup2 = group2
     End If
  End If
  Return newPage
End Function

Shared Data1 as Object
Shared Data2 as Object

Public Function GetData(Num as Integer, Group as integer) as Object
if Group = 1 then
   Return Cstr(Choose(Num, Split(Cstr(Data1),Chr(177))))
End If

if Group = 2 then
   Return Cstr(Choose(Num, Split(Cstr(Data2),Chr(177))))
End If
End Function

Public Function SetData(NewData as Object,Group as integer)
  If Group = 1 and NewData &lt;&gt; "" Then
      Data1 = NewData
  End If
  If Group = 2 and NewData &lt;&gt; "" Then
      Data2 = NewData
  End If

  Return True
End Function
Public Function EndDocument() as Boolean
    Return Me.Report.Globals!PageNumber = Me.Report.Globals!TotalPages
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="PSLNo">
          <DataField>PSLNo</DataField>
        </Field>
        <Field Name="PSLvehicle">
          <DataField>PSLvehicle</DataField>
        </Field>
        <Field Name="PSLDriver">
          <DataField>PSLDriver</DataField>
        </Field>
        <Field Name="PSLDriverCo0nt">
          <DataField>PSLDriverCo0nt</DataField>
        </Field>
        <Field Name="CustPh">
          <DataField>CustPh</DataField>
        </Field>
        <Field Name="SelltoCustomerNo_SalesInvoiceHeader">
          <DataField>SelltoCustomerNo_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="Addr1">
          <DataField>Addr1</DataField>
        </Field>
        <Field Name="Addr2">
          <DataField>Addr2</DataField>
        </Field>
        <Field Name="Addr3">
          <DataField>Addr3</DataField>
        </Field>
        <Field Name="Addr4">
          <DataField>Addr4</DataField>
        </Field>
        <Field Name="Addr5">
          <DataField>Addr5</DataField>
        </Field>
        <Field Name="YourReference_SalesInvoiceHeader">
          <DataField>YourReference_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="ExternalDocumentNo_SalesInvoiceHeader">
          <DataField>ExternalDocumentNo_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="No_SalesInvoiceHeader">
          <DataField>No_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="PostingDate_SalesInvoiceHeader">
          <DataField>PostingDate_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="DueDate_SalesInvoiceHeader">
          <DataField>DueDate_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="OrderNo_SalesInvoiceHeader">
          <DataField>OrderNo_SalesInvoiceHeader</DataField>
        </Field>
        <Field Name="ReprintCaption">
          <DataField>ReprintCaption</DataField>
        </Field>
        <Field Name="LPOReference_Caption">
          <DataField>LPOReference_Caption</DataField>
        </Field>
        <Field Name="No__Printed">
          <DataField>No__Printed</DataField>
        </Field>
        <Field Name="Customer_Reference_No_">
          <DataField>Customer_Reference_No_</DataField>
        </Field>
        <Field Name="Old_Delivery_Effected_By">
          <DataField>Old_Delivery_Effected_By</DataField>
        </Field>
        <Field Name="Authorisers_ID">
          <DataField>Authorisers_ID</DataField>
        </Field>
        <Field Name="TotalQtyCaption">
          <DataField>TotalQtyCaption</DataField>
        </Field>
        <Field Name="NoCaption">
          <DataField>NoCaption</DataField>
        </Field>
        <Field Name="DescriptionCaption">
          <DataField>DescriptionCaption</DataField>
        </Field>
        <Field Name="QuantityCaption">
          <DataField>QuantityCaption</DataField>
        </Field>
        <Field Name="UnitPriceCaption">
          <DataField>UnitPriceCaption</DataField>
        </Field>
        <Field Name="LineAmountCaption">
          <DataField>LineAmountCaption</DataField>
        </Field>
        <Field Name="UOMCaption">
          <DataField>UOMCaption</DataField>
        </Field>
        <Field Name="AmountinwordsCaption">
          <DataField>AmountinwordsCaption</DataField>
        </Field>
        <Field Name="LocationCaption">
          <DataField>LocationCaption</DataField>
        </Field>
        <Field Name="PostedbyCaption">
          <DataField>PostedbyCaption</DataField>
        </Field>
        <Field Name="DateAndTimeCaption">
          <DataField>DateAndTimeCaption</DataField>
        </Field>
        <Field Name="TotalAmtCaption">
          <DataField>TotalAmtCaption</DataField>
        </Field>
        <Field Name="AmtinclvatCaption">
          <DataField>AmtinclvatCaption</DataField>
        </Field>
        <Field Name="LineDiscAmtCaption">
          <DataField>LineDiscAmtCaption</DataField>
        </Field>
        <Field Name="AmtExclVatCaption">
          <DataField>AmtExclVatCaption</DataField>
        </Field>
        <Field Name="CurrReport_PAGENO">
          <DataField>CurrReport_PAGENO</DataField>
        </Field>
        <Field Name="CompanyInfoPic">
          <DataField>CompanyInfoPic</DataField>
        </Field>
        <Field Name="Summary">
          <DataField>Summary</DataField>
        </Field>
        <Field Name="Header">
          <DataField>Header</DataField>
        </Field>
        <Field Name="CopyNo">
          <DataField>CopyNo</DataField>
        </Field>
        <Field Name="CopyTxt">
          <DataField>CopyTxt</DataField>
        </Field>
        <Field Name="Posted_Loading_Slip_No_">
          <DataField>Posted_Loading_Slip_No_</DataField>
        </Field>
        <Field Name="No_SalesInvoiceLine">
          <DataField>No_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="Description_SalesInvoiceLine">
          <DataField>Description_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="UnitofMeasure_SalesInvoiceLine">
          <DataField>UnitofMeasure_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="Quantity_SalesInvoiceLine">
          <DataField>Quantity_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="Quantity_SalesInvoiceLineFormat">
          <DataField>Quantity_SalesInvoiceLineFormat</DataField>
        </Field>
        <Field Name="UnitPrice_SalesInvoiceLine">
          <DataField>UnitPrice_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="UnitPrice_SalesInvoiceLineFormat">
          <DataField>UnitPrice_SalesInvoiceLineFormat</DataField>
        </Field>
        <Field Name="TOTALVALUE">
          <DataField>TOTALVALUE</DataField>
        </Field>
        <Field Name="TOTALVALUEFormat">
          <DataField>TOTALVALUEFormat</DataField>
        </Field>
        <Field Name="AmtInclVat">
          <DataField>AmtInclVat</DataField>
        </Field>
        <Field Name="AmtInclVatFormat">
          <DataField>AmtInclVatFormat</DataField>
        </Field>
        <Field Name="TOTALVALUE1">
          <DataField>TOTALVALUE1</DataField>
        </Field>
        <Field Name="TOTALVALUE1Format">
          <DataField>TOTALVALUE1Format</DataField>
        </Field>
        <Field Name="LineDiscountAmount_SalesInvoiceLine">
          <DataField>LineDiscountAmount_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="LineDiscountAmount_SalesInvoiceLineFormat">
          <DataField>LineDiscountAmount_SalesInvoiceLineFormat</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_SalesInvoiceLine">
          <DataField>AmountIncludingVAT_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_SalesInvoiceLineFormat">
          <DataField>AmountIncludingVAT_SalesInvoiceLineFormat</DataField>
        </Field>
        <Field Name="DocumentNo_SalesInvoiceLine">
          <DataField>DocumentNo_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="LineNo_SalesInvoiceLine">
          <DataField>LineNo_SalesInvoiceLine</DataField>
        </Field>
        <Field Name="Quantity_GLQty">
          <DataField>Quantity_GLQty</DataField>
        </Field>
        <Field Name="Quantity_GLQtyFormat">
          <DataField>Quantity_GLQtyFormat</DataField>
        </Field>
        <Field Name="GLQty">
          <DataField>GLQty</DataField>
        </Field>
        <Field Name="GLQtyFormat">
          <DataField>GLQtyFormat</DataField>
        </Field>
        <Field Name="Qty">
          <DataField>Qty</DataField>
        </Field>
        <Field Name="QtyFormat">
          <DataField>QtyFormat</DataField>
        </Field>
        <Field Name="Location_Code">
          <DataField>Location_Code</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_Amount">
          <DataField>AmountIncludingVAT_Amount</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_AmountFormat">
          <DataField>AmountIncludingVAT_AmountFormat</DataField>
        </Field>
        <Field Name="Amountinword">
          <DataField>Amountinword</DataField>
        </Field>
        <Field Name="COPYSTR_LOTNO_1_70">
          <DataField>COPYSTR_LOTNO_1_70</DataField>
        </Field>
        <Field Name="COPYSTR_LOTNO_71">
          <DataField>COPYSTR_LOTNO_71</DataField>
        </Field>
        <Field Name="ShowSalesInvLine">
          <DataField>ShowSalesInvLine</DataField>
        </Field>
        <Field Name="Bags">
          <DataField>Bags</DataField>
        </Field>
        <Field Name="LOTNO">
          <DataField>LOTNO</DataField>
        </Field>
        <Field Name="TermsHeading">
          <DataField>TermsHeading</DataField>
        </Field>
        <Field Name="Term1">
          <DataField>Term1</DataField>
        </Field>
        <Field Name="Term2">
          <DataField>Term2</DataField>
        </Field>
        <Field Name="Term3">
          <DataField>Term3</DataField>
        </Field>
        <Field Name="Term4">
          <DataField>Term4</DataField>
        </Field>
        <Field Name="Term5">
          <DataField>Term5</DataField>
        </Field>
        <Field Name="Term6">
          <DataField>Term6</DataField>
        </Field>
        <Field Name="Term7">
          <DataField>Term7</DataField>
        </Field>
        <Field Name="Term8">
          <DataField>Term8</DataField>
        </Field>
        <Field Name="Term9">
          <DataField>Term9</DataField>
        </Field>
        <Field Name="Term10">
          <DataField>Term10</DataField>
        </Field>
        <Field Name="Term11">
          <DataField>Term11</DataField>
        </Field>
        <Field Name="Term12">
          <DataField>Term12</DataField>
        </Field>
        <Field Name="Term13">
          <DataField>Term13</DataField>
        </Field>
        <Field Name="Term14">
          <DataField>Term14</DataField>
        </Field>
        <Field Name="Term15">
          <DataField>Term15</DataField>
        </Field>
        <Field Name="Term16">
          <DataField>Term16</DataField>
        </Field>
        <Field Name="Term17">
          <DataField>Term17</DataField>
        </Field>
        <Field Name="Term18">
          <DataField>Term18</DataField>
        </Field>
        <Field Name="Term19">
          <DataField>Term19</DataField>
        </Field>
        <Field Name="Term20">
          <DataField>Term20</DataField>
        </Field>
        <Field Name="Term21">
          <DataField>Term21</DataField>
        </Field>
        <Field Name="Term22">
          <DataField>Term22</DataField>
        </Field>
        <Field Name="Term23">
          <DataField>Term23</DataField>
        </Field>
        <Field Name="Term24">
          <DataField>Term24</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>