page 50774 "13Wk Log Line"
{
    Caption = '13Wk Log Line';
    PageType = ListPart;
    SourceTable = "13Wk Log Line";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No"; "Document No")
                {
                    ApplicationArea = All;
                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("item Category"; "item Category")
                {
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    ApplicationArea = All;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;
                }
                field("Week 1"; "Week 1")
                {
                    ApplicationArea = All;
                }
                field("Week 2"; "Week 2")
                {
                    ApplicationArea = All;
                }
                field("Week 3"; "Week 3")
                {
                    ApplicationArea = All;
                }
                field("Week 4"; "Week 4")
                {
                    ApplicationArea = All;
                }
                field("Week 5"; "Week 5")
                {
                    ApplicationArea = All;
                }
                field("Week 6"; "Week 6")
                {
                    ApplicationArea = All;
                }
                field("Week 7"; "Week 7")
                {
                    ApplicationArea = All;
                }
                field("Week 8"; "Week 8")
                {
                    ApplicationArea = All;
                }
                field("Week 9"; "Week 9")
                {
                    ApplicationArea = All;
                }
                field("Week 10"; "Week 10")
                {
                    ApplicationArea = All;
                }
                field("Week 11"; "Week 11")
                {
                    ApplicationArea = All;
                }
                field("Week 12"; "Week 12")
                {
                    ApplicationArea = All;
                }
                field("Week 13"; "Week 13")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}
