pageextension 50089 PostedReturnShipment extends "Posted Return Shipment"
{
    layout
    {

    }

    actions
    {
        addafter(Statistics)
        {
            action("Attached Gate Entry")
            {
                Caption = 'Attached Gate Entry';
                ApplicationArea = all;
                Image = InwardEntry;
                RunObject = page "Outward Gate Entry Line List";
                RunPageLink = "Entry Type" = const(Outward), "Source Type" = const("Purchase Return Shipment"), "Source No." = field("No.");
            }

        }
    }
}