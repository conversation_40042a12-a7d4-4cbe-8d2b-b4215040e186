page 50970 "SKU Data List"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "SKU Data";

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("SKU Code"; "SKU Code")
                {
                    ApplicationArea = All;

                }
                field("SKU Description"; "SKU Description")
                {
                    ApplicationArea = All;

                }
                field("SKU Product"; "SKU Product")
                {
                    ApplicationArea = All;

                }
                field("Pack Size"; "SKU Pack Size")
                {
                    ApplicationArea = All;

                }
                field("SKU Group"; "SKU Group")
                {
                    ApplicationArea = All;

                }
                field("Category"; "SKU Category")
                {
                    ApplicationArea = All;

                }
            }
        }
        area(Factboxes)
        {

        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction();
                begin

                end;
            }
        }
    }
}