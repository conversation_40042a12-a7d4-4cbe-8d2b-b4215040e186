page 50959 "Delegation Setup"
{
    // version DELIGATE

    PageType = Document;
    SourceTable = "User Setup";
    UsageCategory = Documents;
    ApplicationArea = all;
    Permissions = tabledata "User Setup" = rm;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("User ID"; "User ID")
                {
                }
                field(Name; Name)
                {
                }
            }
            part(Control1000000006; "Delegation Subform")
            {
                SubPageLink = "User ID" = FIELD("User ID");
            }
        }
    }

    actions
    {
    }

    trigger OnOpenPage();
    begin
        FILTERGROUP(2);
        SETRANGE("User ID", USERID);
        FILTERGROUP(0);
    end;
}

