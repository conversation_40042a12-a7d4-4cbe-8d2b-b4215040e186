page 50463 "FA Verification Data"
{
    // version TRKIT

    Editable = false;
    PageType = List;
    SourceTable = XX_TRKIT_FA_VERIFICATION_DATA;
    UsageCategory = Administration;
    ApplicationArea = All;


    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(UID; UID)
                {
                    ApplicationArea = All;
                }
                field(VERIFICATION_TASK_NUMBER; VERIFICATION_TASK_NUMBER)
                {
                    ApplicationArea = All;
                }
                field(ASSET_NUMBER; ASSET_NUMBER)
                {
                    ApplicationArea = All;
                }
                field(TAG_NUMBER; TAG_NUMBER)
                {
                    ApplicationArea = All;
                }
                field(CURRENT_ASSET_FA_LOCATION; CURRENT_ASSET_FA_LOCATION)
                {
                    ApplicationArea = All;
                }
                field(CURRENT_ASSET_ACC_LOCATION; CURRENT_ASSET_ACC_LOCATION)
                {
                    ApplicationArea = All;
                }
                field(CURRENT_ASSET_CC_LOCATION; CURRENT_ASSET_CC_LOCATION)
                {
                    ApplicationArea = All;
                }
                field(ASSET_FOUND_FA_LOCATION; ASSET_FOUND_FA_LOCATION)
                {
                    ApplicationArea = All;
                }
                field(ASSET_FOUND_ACC_LOCATION; ASSET_FOUND_ACC_LOCATION)
                {
                    ApplicationArea = All;
                }
                field(ASSET_FOUND_CC_LOCATION; ASSET_FOUND_CC_LOCATION)
                {
                    ApplicationArea = All;
                }
                field(SCAN_DATE; SCAN_DATE)
                {
                    ApplicationArea = All;
                }
                field(SCAN_BY; SCAN_BY)
                {
                    ApplicationArea = All;
                }
                field(STATUS; STATUS)
                {
                    ApplicationArea = All;
                }
                field(NAVISION_PROCESS_TIME; NAVISION_PROCESS_TIME)
                {
                    ApplicationArea = All;
                }
                field(NAVISION_PROCESS_STATUS; NAVISION_PROCESS_STATUS)
                {
                    ApplicationArea = All;
                }
                field(NAVISION_REJECT_REASON; NAVISION_REJECT_REASON)
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
    }
}

