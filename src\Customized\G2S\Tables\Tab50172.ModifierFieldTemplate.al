//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
table 50172 "Modifier Field Template"
{
    Caption = 'Modifier Field Template';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Record Type"; Option)
        {
            Caption = 'RecordType';
            OptionMembers = " ","Purchase Header","Purchase Line";

            trigger OnValidate()
            begin
                if Rec."Record Type" = "Record Type"::"Purchase Header" then
                    Rec.Validate(TableID, 38)
                else
                    if Rec."Record Type" = "Record Type"::"Purchase Line" then Rec.Validate(TableID, 39);
            end;
        }
        field(2; TableID; Integer)
        {
            Editable = false;
            DataClassification = ToBeClassified;
        }
        field(3; "Field Name"; Text[100])
        {
            Caption = 'Field Name ';
            FieldClass = Normal;

            trigger OnLookup()
            var
                ModifierHeader: Record "Purchase Doc Modifier";
                PurchaseLineRec: Record "Purchase Line";
                PurchaseHeaderRec: Record "Purchase Header";
                LineNo: Integer;
                IsPurchaseHeader: Boolean;
                FactorValue: Decimal;
            begin
                if Rec."Record Type" = "Record Type"::" " then
                    exit;

                CLEAR(FieldList);
                FieldRec.SETRANGE(FieldRec.TableNo, Rec.TableID);
                FieldList.SETTABLEVIEW(FieldRec);
                FieldList.LOOKUPMODE := true;
                if FieldList.RUNMODAL = ACTION::LookupOK then begin
                    FieldList.GETRECORD(FieldRec);
                    VALIDATE(Rec."Field Name", FieldRec.FieldName);
                    VALIDATE(FieldID, FieldRec."No.");
                end;
            end;
        }
        field(4; FieldID; Integer)
        {
            Editable = false;
            DataClassification = ToBeClassified;
        }
        field(5; "Allow Modify"; Boolean)
        {
            Caption = 'Allow Modify';
        }
        field(6; "Line No."; Integer)
        {
            Editable = false;
            DataClassification = ToBeClassified;
            AutoIncrement = true;
        }
    }
    keys
    {
        key(PK; "Record Type", "Line No.")
        {
            Clustered = true;
        }
    }

    fieldgroups
    {
        // Add your code here.
    }

    var
        FieldList: Page "Fields Lookup";
        CurrExchRate: Record "Currency Exchange Rate";
        MasterRecID: RecordID;
        FieldRec: Record "Field";
        MasterRecRef: RecordRef;
}
// >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
