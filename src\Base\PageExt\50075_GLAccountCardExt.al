pageextension 50075 GLAccountCardExt extends "G/L Account Card"
{
    layout
    {
        addafter(Totaling)
        {
            field("Coke A/C No."; "Coke A/C No.")
            {
                ApplicationArea = all;
                trigger onvalidate()
                var
                    GLAcc: record "G/L Account";
                begin
                    GLAcc.reset;
                    GLAcc.SetRange("Coke A/C No.", "Coke A/C No.");
                    if GLAcc.FindFirst() then
                        Error('Coke A/C No. %1 already exist for G/L Acc No. %2.', GLAcc."Coke A/C No.", GLAcc."No.");
                end;
            }
            field("Balance at Date"; "Balance at Date")
            {
                ApplicationArea = ALL;
            }
        }
        addafter("Omit Default Descr. in Jnl.")
        {
            field("Branch CPV"; "Branch CPV")
            {
                ApplicationArea = all;
            }
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
                Editable = false;
            }
        }
        addafter("Gen. Prod. Posting Group")
        {
            field("Inventory Posting Setup"; "Inventory Posting Setup")
            {
                ApplicationArea = All;
            }
        }
        addafter("Coke A/C No.")
        {
            //G2S 26/11/24
            field("AC head"; "AC head")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        addafter("Apply Template")
        {
            action(WorkFlows)
            {
                ApplicationArea = All;
                Image = Action;

                trigger OnAction()
                begin
                    Message('This actions are for workflows.');
                end;
            }
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    IF allinoneCU.CheckCOAApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendCOAForApproval(Rec);
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelCOAForApproval(rec);
                end;
            }
            action("Re&lease")
            {
                ApplicationArea = all;
                Caption = 'Re&lease';
                ShortCutKey = 'Ctrl+F11';
                Image = ReleaseDoc;
                trigger OnAction()
                var
                    WorkflowManagement: Codeunit "Workflow Management";
                /*                     RecRef: RecordRef;
                                    SharePointInt: Codeunit "Share Point Integration"; */
                begin
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendCOAforApprovalCode()) then
                        error('Workflow is enabled. You can not release manually.');

                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        Modify();
                        Message('Document has been Released.');
                    end;
                    if Blocked then begin
                        Blocked := false;
                        Modify();
                    end;
                    /*                     //SharePoint>>
                                        RecRef.GETTABLE(Rec);
                                        SharePointInt.OnReleasedocumentDetails(RecRef, false);
                                        //SharePoint<< */
                end;
            }
            action("Re&open")
            {
                ApplicationArea = all;
                Caption = 'Re&open';
                Image = ReOpen;
                trigger OnAction();
                var
                    RecordRest: Record "Restricted Record";
                begin
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 15);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        Modify();
                        Message('Document has been Reopened.');
                    end;
                end;
            }

        }
        addafter("G/L Balance by &Dimension")
        {
            action("G/L Balance by &Dimension2")
            {
                ApplicationArea = Dimensions;
                Caption = 'G/L Balance by &Dimension';
                Image = GLBalanceDimension;
                Promoted = true;
                PromotedCategory = Category5;
                PromotedOnly = true;
                RunObject = Page "G/L Balance by Dimension_Copy";
                ToolTip = 'View a summary of the debit and credit balances by dimensions for the current account.';
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);
    END;

    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    BEGIN
        Blocked := TRUE;
    END;


    var
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit IJLSubEvents;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
}