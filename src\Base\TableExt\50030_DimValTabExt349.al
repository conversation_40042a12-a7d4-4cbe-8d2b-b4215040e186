tableextension 50030 DimValTabExt extends "Dimension Value"
{
    fields
    {
        field(50000; "MRS Series Code"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; company; Code[50])
        {
            DataClassification = CustomerContent;
        }
        field(50002; Active; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Branch Zones"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Branch Zones".Code;
        }
        field(50004; "KD Region"; Option)
        {
            DataClassification = CustomerContent;
            Caption = 'KD Region';
            OptionCaption = '" ,ROSW,EAST,North Central,Up North,Retail,LOS,EXPORT"';
            OptionMembers = " ",ROSW,EAST,"North Central","Up North",Retail,LOS,EXPORT;
        }
        field(50006; "Region"; Option)
        {
            DataClassification = CustomerContent;
            Caption = 'Region';
            OptionCaption = '" ,ROSW,EAST,North Central,Up North,Retail,LOS,EXPORT"';
            OptionMembers = " ",ROSW,EAST,"North Central","Up North",Retail,LOS,EXPORT;
            //b2bpksalecorr10

        }
        field(50005; "Accloc code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));
        }
        field(50020; "Branch Online"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50023; "DSR Report Grouping"; Text[30]) //PKON22M10-CR220063
        {
            DataClassification = CustomerContent;
        }
        field(50024; "DSR Report Main Grouping"; Text[30]) //PKON22M10-CR220063
        {
            DataClassification = CustomerContent;
        }
    }
}