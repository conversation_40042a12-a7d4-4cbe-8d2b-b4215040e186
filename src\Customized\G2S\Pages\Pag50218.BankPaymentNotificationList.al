//RFC#39 BANK API
//Signed on 31/10/2023
//Published on 11th November 2023
//updated on 15th November 2023
//updated on 15th September 2024
//G2S


/// <summary>
/// Page Bank Payment Notification List (ID 50218).
/// </summary>
page 50218 "Bank Payment Notification List"
{
    ApplicationArea = All;
    Caption = 'Bank Payment Notification List';
    PageType = List;
    SourceTable = "Bank Payment Notification";
    CardPageId = "Bank Payment Notification Card";
    UsageCategory = Lists;
    Editable = false;
    InsertAllowed = false;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field(ID; Rec.ID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the ID field.';
                }
                field("Customer ID"; Rec."Customer ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer ID field.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer Name field.';
                }
                field("Payment Reference"; Rec."Payment Reference")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Payment Reference field.';
                }
                field(Amount; Rec.Amount)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Amount field.';
                }
                field("Date Entered"; Rec."Date Entered")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Entered field.';
                }
                field("Session ID"; Rec."Session ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Session ID field.';
                }
                field(SourceBankCode; Rec.SourceBankCode)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the SourceBankCode field.';
                }
                field(Processed; Rec.Processed)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Processed field.';
                }

                field("Temp. Omit record from Batch"; Rec."Temp. Omit record from Batch")
                {
                    ApplicationArea = All;
                    ToolTip = 'Temporarily omit record from batch';
                }

                field("Duplicate Session ID?"; "Duplicate Session ID?")
                {
                    ApplicationArea = All;
                    ToolTip = 'Session ID';
                }
                field("Date Posted"; Rec."Date Posted")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Posted field.';
                }
                field(Narration; Rec.Narration)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Narration field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(MarkandExclude)
            {
                Caption = 'Ignore Selected records from batch';
                ApplicationArea = All;
                Image = ImportDatabase;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                trigger OnAction()
                var
                    BankPayment: Record "Bank Payment Notification";
                begin
                    RecToOmitExists := false;

                    if Confirm(Text001, false, TextConfirm) then begin
                        CurrPage.SetSelectionFilter(BankPayment);
                        BankPayment.Mark(true);
                        if BankPayment.FindFirst() then begin
                            repeat
                                if ((BankPayment.Processed <> true) and (BankPayment."Temp. Omit record from Batch" = false)) then begin
                                    BankPayment."Temp. Omit record from Batch" := true;
                                    BankPayment.Modify();
                                    RecToOmitExists := true;
                                end
                            until BankPayment.Next() = 0;
                        end;
                        Rec.ClearMarks();
                        Rec.MARKEDONLY(FALSE);
                        if RecToOmitExists then
                            Message(ProcessCompleteMsg, Text003);
                    end else
                        Message(CancelConfirmation);
                end;
            }

            action(UnMarkandInclude)
            {
                Caption = 'Include selected records from batch';
                ApplicationArea = All;
                Image = ImportDatabase;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                trigger OnAction()
                var
                    BankPayment: Record "Bank Payment Notification";
                begin
                    RecToOmitExists := false;
                    if Confirm(Text001, false, TextConfirm) then begin
                        CurrPage.SetSelectionFilter(BankPayment);
                        BankPayment.Mark(true);
                        if BankPayment.FindFirst() then begin
                            repeat
                                if ((BankPayment.Processed = false) and (BankPayment."Temp. Omit record from Batch" = true)) then begin
                                    BankPayment."Temp. Omit record from Batch" := false;
                                    BankPayment.Modify();
                                    RecToOmitExists := true;
                                end;
                            until BankPayment.Next() = 0;
                        end;
                        Rec.ClearMarks();
                        Rec.Mark(false);
                        if RecToOmitExists then
                            Message(ProcessCompleteMsg, Text002);
                    end else
                        Message(CancelConfirmation);
                end;
            }
        }
    }

    var
        Text001: Label 'Please confirm you want to %1';
        TextConfirm: Label 'temporarily ignore the selected records from the transactions';
        TextUncheck: Label 'include the selected records. These records will be added to the list of records to be processed.';
        ProcessCompleteMsg: Label 'The selected records have been successfully %1 ';
        CancelConfirmation: Label 'Confirmation canceled';
        RecToOmitExists: Boolean;
        Text002: Label 'marked and committed to the transactions to be processed';
        Text003: Label 'marked and ommitted from the transactions to be processed';
}
