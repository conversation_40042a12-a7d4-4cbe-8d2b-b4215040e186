tableextension 50007 CustTabExt18 extends Customer
{
    fields
    {
        field(50000; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
            editable = false;
        }
        field(50002; "Last Transaction Date"; Date)
        {
            CalcFormula = Max("Cust. Ledger Entry"."Posting Date" WHERE("Customer No." = FIELD("No.")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(50003; "Customer Credit type"; Enum CustomerCredittype)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "Type of Business"; Enum TypeofBusiness)
        {

        }
        field(50005; "Customer Type"; Enum SalesType)
        {
            DataClassification = CustomerContent;

        }
        field(50006; "DMS Customer"; Boolean)
        {
            Caption = 'DMS Customer';
        }
        field(50007; "KD Period Joined"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Rebate Period Codes";
        }
        field(50015; "Chivita Balance"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50016; "Chicap Balance"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50020; "Batch Assign"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = 'FEFO,LEFO';
            OptionMembers = FEFO,LEFO;
        }
        field(50021; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;
        }
        field(50022; "Interest Method"; enum "Interest Method")
        {
            DataClassification = CustomerContent;
        }
        field(50023; "No. of Installments"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Alternate Phone No."; Text[30])
        {
            Caption = 'Alternate Phone No.';
            ExtendedDatatype = PhoneNo;

            trigger OnValidate()
            var
                Char: DotNet Char;
                i: Integer;
            begin
                for i := 1 to StrLen("Phone No.") do
                    if Char.IsLetter("Phone No."[i]) then
                        FieldError("Phone No.", PhoneNoCannotContainLettersErr);
            end;
        }
        field(50025; "Alternate E-Mail"; Text[80])
        {
            Caption = 'Alternate Email id';
            ExtendedDatatype = EMail;

            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
            begin
                if "E-Mail" = '' then
                    exit;
                MailManagement.CheckValidEmailAddresses("E-Mail");
            end;
        }
        field(50027; "Channel No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Channels;
            trigger OnValidate()
            begin

                IF "Customer Posting Group" = 'LOCAL' THEN BEGIN
                    IF "Channel No." <> '' THEN
                        CreateChannelDim(Rec);
                END;
            end;
        }
        field(50028; "Sales Area"; code[20])
        {
            DataClassification = CustomerContent;
            //CaptionClass = '1,2,1';
            //Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(5));

            trigger OnValidate()
            begin
                ValidateShortcutDimCode(5, "Sales Area");
            end;
        }
        field(50029; "Credit Limit Applied (LCY)"; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = Sum("Cust. Cr. Limit Schedule"."Credit Limit(LCY)" WHERE("Customer No." = FIELD("No."), Status = FILTER(Released), "Customer Type" = FILTER("Credit Customer")));
            Editable = false;
        }
        field(50030; "Accounting Location"; Code[10])
        {
            FieldClass = FlowField;
            CalcFormula = Lookup("Customer Sales Price/Discount"."Shortcut Dimension 1 Code" WHERE("Customer No." = FIELD("No.")));

        }
        field(50031; "Sales Office"; Code[20])
        {
            Description = 'SalesArea_view';
            TableRelation = "Dimension Value".Code WHERE("Dimension Code" = FILTER('SALESAREA'));
        }
        field(50041; "Creation Date"; Date)
        {
            DataClassification = CustomerContent;
            editable = false;
            //b2bpksalecorr9
        }
        field(50042; "Customer Classification"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Customer Classification";
        }
        field(50043; "Bank Account No."; Text[50])
        {
            DataClassification = CustomerContent;
        }
        field(65000; "POS Customer"; Boolean)
        {
            Description = 'GJ_CHI_RKD_091013';
        }
        field(65016; "Direct Sales Order Allowed"; Boolean)
        {

            DataClassification = CustomerContent;
        }
        //BaluonAug18>>
        field(61026; States; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //BaluonAug18<<
        field(61027; "Customer Business Type"; enum CustomerType)//PKON22M10-CR220063
        {
            DataClassification = CustomerContent;
        }
        //RFCMTDNotificationGo2solveMay2023>>
        field(61028; "Salesperson Code 1"; Code[50])
        {
            DataClassification = ToBeClassified;
            TableRelation = "Salesperson/Purchaser";
        }
        //  //RFC #2024-006 ********
        field(61029; "Customer Location"; Code[50])
        {
            DataClassification = ToBeClassified;
            TableRelation = "Customer Regions";
            trigger OnValidate()
            var
                CustRegion: Record "Customer Regions";
            begin
                CustRegion.SetRange(Location, "Customer Location");
                if CustRegion.FindFirst() then
                    "Customer Region" := CustRegion.Region;
            end;
        }
        field(61030; "Customer Region"; Code[50])
        {
            DataClassification = ToBeClassified;
            // TableRelation = "Customer Regions";
            Editable = false;
        }
        // //RFC #2024-006 ******** */
        //RFCMTDNotificationGo2solveMay2023<<
        //rebate issue
        field(61031; "Fixed Rebate Code"; Code[50])
        {
            DataClassification = ToBeClassified;
            TableRelation = "Fixed Rebate";
            // Editable = false;
        }

        field(61032; "Commencement Date"; Date)
        {
            DataClassification = ToBeClassified;

            // Editable = false;
        }
        field(61040; "Clearwox Outlet ID"; Integer)
        {
            DataClassification = ToBeClassified;

            // Editable = false;
        }
        field(61041; "Outlet Area"; Code[50])
        {
            DataClassification = ToBeClassified;
            TableRelation = "Retail Outlet Areas";

            // Editable = false;
        }
        // <<<
    }
    trigger OnModify()
    begin
        /*         if "Approval Status" <> "Approval Status"::Open then
                    error('you cannot modify record while in approval/release stage'); */
    end;

    trigger OnInsert()
    begin
        Blocked := Blocked::All;
        "Creation Date" := workdate();//b2bpksalecorr9
    end;

    procedure ValidateShortcutDimCode(FieldNumber: Integer; var ShortcutDimCode: Code[20]);
    begin
        DimMgt.ValidateDimValueCode(FieldNumber, ShortcutDimCode);
    end;


    Procedure CreateChannelDim(VAR CustLLRec: Record Customer)
    var
        DefDim: Record "Default Dimension";

    begin

        WITH CustLLRec DO BEGIN
            IF DefDim.GET(18, "No.", 'CHANNEL') THEN
                DefDim.DELETE;//BEGIN
            DefDim.INIT;
            DefDim."Table ID" := 18;
            DefDim."No." := "No.";
            DefDim."Value Posting" := DefDim."Value Posting"::"Same Code";
            DefDim."Dimension Code" := 'CHANNEL';
            DefDim."Dimension Value Code" := "Channel No.";
            DefDim.INSERT;
            //END;
            ChannelRec.SETRANGE(Code, "Channel No.");
            IF ChannelRec.FINDSET THEN BEGIN
                IF ChannelRec."Sales Area" <> '' THEN BEGIN
                    IF DefDim.GET(18, "No.", 'SALESAREA') THEN
                        DefDim.DELETE;
                    DefDim.INIT;
                    DefDim."Table ID" := 18;
                    DefDim."No." := "No.";
                    DefDim."Value Posting" := DefDim."Value Posting"::"Same Code";
                    DefDim."Dimension Code" := 'SALESAREA';
                    DefDim."Dimension Value Code" := ChannelRec."Sales Area";
                    DefDim.INSERT;
                END;
            END;
        END;
    end;

    var
        PhoneNoCannotContainLettersErr: Label 'must not contain letters';
        DimMgt: Codeunit DimensionManagement;
        ChannelRec: Record Channels;
}