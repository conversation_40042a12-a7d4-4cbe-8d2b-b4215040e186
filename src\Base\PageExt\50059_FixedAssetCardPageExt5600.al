pageextension 50059 FixedAssetPageExt5600 extends "Fixed Asset Card"
{
    layout
    {
        modify(Blocked)
        {
            Editable = false;
        }
        addafter(Description)
        {
            field("Description 1"; "Description 1")
            {
                ApplicationArea = all;
            }
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
        }

        addafter(General)
        {
            group("FA Item Details")
            {
                Caption = 'FA Item Details';
                field("PMS No."; "PMS No.")
                {
                    ApplicationArea = all;
                }
                field("PMS Card No."; "PMS Card No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                }
                field("Fuel Type"; "Fuel Type")
                {
                    ApplicationArea = all;
                }

                field("PMS Vehicle"; "PMS Vehicle")
                {
                    ApplicationArea = all;
                }

                field("Vehicle Tank Capacity"; "Vehicle Tank Capacity")
                {
                    ApplicationArea = all;
                }
                field("PMS Card Issued"; "PMS Card Issued")
                {
                    ApplicationArea = all;
                }
            }

        }
        addafter("Last Date Modified")
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = ALL;
            }

            field("FA Tagging Required"; "FA Tagging Required")
            {
                ApplicationArea = ALL;
            }
            field("FA QR"; FAQRData)
            {
                Editable = false;
                ApplicationArea = ALL;
            }
            field("FA Tagging Code"; "FA Tagging Code")
            {
                ApplicationArea = ALL;
            }
            field("Version No."; "Version No.")
            {
                ApplicationArea = all;
            }
            field("Vehicle Type"; "Vehicle Type")
            {
                ApplicationArea = ALL;
            }
            field("Vehicle Capacity (In Tonns)"; "Vehicle Capacity (In Tonns)")
            {
                ApplicationArea = ALL;
            }
            field("Vehicle Reg No."; "Vehicle Reg No.")
            {
                ApplicationArea = ALL;
            }
        }

    }

    actions
    {
        addafter("Fixed &Asset")
        {
            action(WorkFlows)
            {
                ApplicationArea = All;
                Image = Action;

                trigger OnAction()
                begin
                    Message('This actions are for workflows.');
                end;
            }
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    CheckMandFields();
                    IF allinoneCU.CheckFAApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendFAForApproval(Rec);
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelFAForApproval(rec);
                end;
            }

            action("UnBlock")
            {
                Image = Open;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF Blocked = true then BEGIN
                        IF Confirm('Do you want to Unblock the Fixed Asset?', True, False) then BEGIN
                            Blocked := false;
                            Modify();
                        end;
                    end;
                end;
            }



            action("Re&lease")
            {
                ApplicationArea = all;
                Caption = 'Re&lease';
                ShortCutKey = 'Ctrl+F11';
                Image = ReleaseDoc;
                trigger OnAction()
                var
                    WorkflowManagement: Codeunit "Workflow Management";
                /*                     RecRef: RecordRef;
                                    SharePointInt: Codeunit "Share Point Integration"; */
                begin
                    CheckMandFields();
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendFAforApprovalCode()) then
                        error('Workflow is enabled. You can not release manually.');

                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        Blocked := false;
                        Modify();
                        Message('Document has been Released.');
                    end;
                    /*                     //SharePoint>>
                                        RecRef.GETTABLE(Rec);
                                        SharePointInt.OnReleasedocumentDetails(RecRef, false);
                                        //SharePoint<< */
                end;
            }
            action("Re&open")
            {
                ApplicationArea = all;
                Caption = 'Re&open';
                Image = ReOpen;
                trigger OnAction();
                var
                    RecordRest: Record "Restricted Record";
                begin
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 5600);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        Blocked := true;
                        Modify();
                        Message('Document has been Reopened.');
                    end;
                end;
            }
            action(TransfertoVehicleHirePurchase)
            {
                Caption = 'Transfer to Vehicle Hire Purchase';
                Image = TransferFunds;
                trigger OnAction()
                Var
                    LoanB2B: record Loan_B2B;
                    FADepreciationBook: Record "FA Depreciation Book";
                    CustLRec: Record customer;
                    CustList: Page "Customer List";
                    BookValue: Decimal;
                    TransferConfirm: Label 'Do you want to transfer Book value to Loan for Installaments?';
                    LoanCalculation: Codeunit "Loan Calculations B2B";
                    FA: Record "Fixed Asset";
                    LReport: Report 50470;

                BEGIN
                    IF Confirm(TransferConfirm, false) then BEGIN
                        CurrPage.SaveRecord();
                        /* PK Commented Phani Code
                        Report.RunModal(50470, true, false, Rec);*/
                        FA.GET("No.");
                        FA.SETRECFILTER();
                        //Message('%1', FA."No.");
                        Report.RunModal(Report::"Loan Process", true, false, FA);


                        /*clear(BookValue);
                        FADepreciationBook.reset;
                        FADepreciationBook.SetRange("FA No.", "No.");
                        IF FADepreciationBook.FindSET() then
                            repeat
                                FADepreciationBook.CalcFields("Book Value");
                                BookValue += FADepreciationBook."Book Value";
                            until FADepreciationBook.next = 0;


                        LoanB2B.INIT;
                        LoanB2B.id := '';
                        LoanB2B.INSERT(true);
                        Commit();
                        CustLRec.RESET;
                        IF CustLRec.FINDSET THEN BEGIN
                            IF PAGE.RUNMODAL(PAGE::"Customer List", CustLRec) = ACTION::LookupOK THEN BEGIN
                                CustLRec.TestField("No. of Installments");
                                CustLRec.TestField("Interest Method");
                                LoanB2B.VALIDATE("Customer No.", CustLRec."No.");
                            END;

                            LoanB2B."Actual Amount" := BookValue;
                            LoanB2B."Loan Amount" := BookValue;
                            LoanB2B."Loan Start Date" := Today;
                            LoanB2B."Loan End Date" := Today;
                            LoanB2B."Effective Amount" := (FADepreciationBook."Book Value" / CustLRec."No. of Installments");
                            LoanB2B.Modify();
                        END;
                        LoanCalculation.LoanInstallments(LoanB2B);*/
                        Blocked := true;
                        "Approval Status" := "Approval Status"::Open;
                        //Modify();
                    end;
                end;
            }


        }
        addafter(Attachments)
        {
            action("Generate QR")
            {
                Image = GetEntries;
                ApplicationArea = all;
                trigger OnAction()
                var
                    FAQRC: Codeunit FAQR;
                begin
                    FAQRC.run(rec);
                    Message('QR generated succesfully.');
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;


    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField("Approval Status", "Approval Status"::Open);
    END;

    local procedure CheckMandFields()
    begin
        TestField("FA Location Code");
    end;

    var
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit IJLSubEvents;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
}