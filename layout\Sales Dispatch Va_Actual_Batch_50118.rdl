﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="Document_No_">
          <DataField>Document_No_</DataField>
        </Field>
        <Field Name="Item_No_">
          <DataField>Item_No_</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="Document_Date">
          <DataField>Document_Date</DataField>
        </Field>
        <Field Name="Posting_Date">
          <DataField>Posting_Date</DataField>
        </Field>
        <Field Name="Lot_No_">
          <DataField>Lot_No_</DataField>
        </Field>
        <Field Name="Expiration_Date">
          <DataField>Expiration_Date</DataField>
        </Field>
        <Field Name="Quantity">
          <DataField>Quantity</DataField>
        </Field>
        <Field Name="QuantityFormat">
          <DataField>QuantityFormat</DataField>
        </Field>
        <Field Name="Orderno">
          <DataField>Orderno</DataField>
        </Field>
        <Field Name="OrderlineNo">
          <DataField>OrderlineNo</DataField>
        </Field>
        <Field Name="OrderQnty">
          <DataField>OrderQnty</DataField>
        </Field>
        <Field Name="OrderQntyFormat">
          <DataField>OrderQntyFormat</DataField>
        </Field>
        <Field Name="TrackingNo">
          <DataField>TrackingNo</DataField>
        </Field>
        <Field Name="PostedDocNo">
          <DataField>PostedDocNo</DataField>
        </Field>
        <Field Name="PostedTrackingNo">
          <DataField>PostedTrackingNo</DataField>
        </Field>
        <Field Name="PostedQnty">
          <DataField>PostedQnty</DataField>
        </Field>
        <Field Name="PostedQntyFormat">
          <DataField>PostedQntyFormat</DataField>
        </Field>
        <Field Name="PostedExpdate">
          <DataField>PostedExpdate</DataField>
        </Field>
        <Field Name="CompanyInfoPic">
          <DataField>CompanyInfoPic</DataField>
        </Field>
        <Field Name="CompanyInfoName">
          <DataField>CompanyInfoName</DataField>
        </Field>
        <Field Name="User_ID">
          <DataField>User_ID</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>