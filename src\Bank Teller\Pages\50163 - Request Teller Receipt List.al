page 50163 "Request Teller Receipt List"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      HO       18-Jun-13    -> Form created for Request Teller Receipt functionality.

    Caption = 'Request Teller Receipt List';
    CardPageID = "Request Teller Receipt";
    Editable = false;
    PageType = List;
    SourceTable = "Request Teller Receipt";
    SourceTableView = where("Released for Confirmation" = const(false));
    UsageCategory = Lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnOpenPage();
    begin
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);

        /*
        RespCentCount :=0;
        
        UserIDRespCent.SETCURRENTKEY("User ID","Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
         REPEAT
          RespCentCount +=1;
          //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
          IF RespCentCount = 1 THEN
           BuildFilter :=UserIDRespCent."Resp. Center Code"
          ELSE
           BuildFilter+='|' + UserIDRespCent."Resp. Center Code";
         UNTIL UserIDRespCent.NEXT=0;
        
        IF RespCentCount > 0 THEN
         //SETRANGE("Responsibility Center",TempResp[1],TempResp[RespCentCount]);
         SETFILTER("Responsibility Center",BuildFilter);
         */

    end;

    var
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";//B2BSB.1.0
        RespCentCount: Integer;
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

