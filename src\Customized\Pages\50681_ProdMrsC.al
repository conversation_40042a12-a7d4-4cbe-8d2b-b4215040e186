page 50681 "Prodcution MRS"
{
    PageType = Document;
    SourceTable = "mrsheader";
    SourceTableView = SORTING("MRS No.")
                      ORDER(Ascending)
                      WHERE(Status = FILTER(<> Released), Closed = filter(false), "Prod. Order Ref. No." = filter(<> ''));
    UsageCategory = Documents;
    ApplicationArea = all;
    DelayedInsert = true;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MRS No."; "MRS No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE();
                    end;
                }
                field("Manual MRS No."; "Manual MRS No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Prod. Order Ref. No."; "Prod. Order Ref. No.")
                {
                    ApplicationArea = all;
                }
                field("Production Batch No."; "Production Batch No.")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        MrsLne: Record MRSLine;
                    BEGIN
                        MrsLne.reset;
                        MrsLne.SetRange("Document No.", "MRS No.");
                        IF MrsLne.findset then BEGIN
                            MrsLne."Shortcut Dimension 1 Code" := "Shortcut Dimension 1 Code";
                            MrsLne.modify;
                        END;
                    END;

                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        MrsLne: Record MRSLine;
                    BEGIN
                        MrsLne.reset;
                        MrsLne.SetRange("Document No.", "MRS No.");
                        IF MrsLne.findset then BEGIN
                            MrsLne."Shortcut Dimension 2 Code" := "Shortcut Dimension 2 Code";
                            MrsLne.modify;
                        END;
                    END;


                }
                field("MRS Type"; "MRS Type")
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Closed; Closed)
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    ApplicationArea = ALL;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
            }
            part(MatReqLinesSubform; "Material Req. Lines Subform.")
            {
                ApplicationArea = all;
                SubPageLink = "Document No." = FIELD("MRS No.");
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50004),
                                "No." = FIELD("MRS No.");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(Dimensions)
            {
                AccessByPermission = TableData Dimension = R;
                ApplicationArea = Dimensions;
                Caption = 'Dimensions';
                Enabled = "MRS No." <> '';
                Image = Dimensions;
                Promoted = true;
                PromotedIsBig = true;
                ShortCutKey = 'Alt+D';
                ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                trigger OnAction()
                begin
                    ShowDocDim();
                    CurrPage.SaveRecord();
                end;
            }
            action("Calculate Qty. On Purchase")
            {
                ApplicationArea = all;
                Caption = 'Calculate Qty. On Purchase';
                Visible = false;
                Image = Calculate;

                trigger OnAction();
                var
                    MrsLineRec: Record MRSLine;
                begin
                    MrsLineRec.SETRANGE("Document No.", "MRS No.");
                    if MrsLineRec.FINDFIRST() then
                        repeat
                            MrsLineRec.CALCFIELDS("Available Stock");
                            if MrsLineRec.Quantity > MrsLineRec."Available Stock" then begin
                                MrsLineRec.VALIDATE(Quantity);
                                MrsLineRec.MODIFY();
                            end;
                        until MrsLineRec.NEXT() = 0;
                end;
            }
            action("Requisition Slip")
            {
                ApplicationArea = all;
                Caption = 'Requisition Slip';
                Ellipsis = true;
                image = Report;
                trigger OnAction();
                var
                    MRSHeader: record MRSHeader;
                begin
                    MRSHeader.RESET();
                    MRSHeader.SETRANGE("MRS No.", "MRS No.");
                    REPORT.RUNMODAL(50096, true, false, MRSHeader);
                end;
            }

            separator(Separator1102152034)
            {
            }
            action("Re&lease")
            {
                ApplicationArea = all;
                Caption = 'Re&lease';
                ShortCutKey = 'Ctrl+F11';
                Image = ReleaseDoc;
                trigger OnAction()

                begin
                    //PhaniFeb112021 >>
                    IF "Purchase Type" = "Purchase Type"::" " then
                        error('Purchase Type cannot be blank.');
                    //PhaniFeb112021 <<                    
                    if Closed then
                        ERROR(Text001Lbl);
                    CheckMandFields();
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendMRSHeaderforApprovalCode()) then
                        error('Workflow is enabled. You can not release manually.');

                    IF Status <> Status::Released then BEGIN
                        VALIDATE(Status, Status::Released);

                        Validate(Rejected, false); //project leap

                        Modify();
                        Message('Document has been Released.');
                    end;
                end;
            }
            action("Re&open")
            {
                ApplicationArea = all;
                Caption = 'Re&open';
                Image = ReOpen;
                trigger OnAction();

                begin
                    if Closed then
                        ERROR(Text001Lbl)
                    else begin
                        IF Status = Status::"Pending for Approval" THEN
                            ERROR('You can not reopen the document when approval status is in %1', Status);
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50004);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Validate(Rejected, true); //project leap
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                end;
            }
            separator("-")
            {
                Caption = '-';
            }
            action("Cancel MRS")
            {
                ApplicationArea = all;
                Caption = 'Cancel MRS';
                Image = Cancel;

                trigger OnAction();
                begin
                    Closed := true;
                    MODIFY();
                    Message('Document has been Cancelled.');
                end;
            }
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    CheckMandFields();
                    IF allinoneCU.CheckmaterialrequestApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendMaterialRequestForApproval(Rec);
                    if Status = Status::Released then
                        Validate(Rejected, false); //project leap
                    if Status = Status::Open then
                        Validate(Rejected, True); //project leap
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelMaterialRequestForApproval(Rec);
                    Validate(Rejected, true); //project leap
                end;
            }
        }
    }



    var
        RecordRest: Record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit POAutomationWorkFlow;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        WorkflowManagement: Codeunit "Workflow Management";
        Text001Lbl: label 'Document is already closed.';
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;

    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    local procedure ShortcutDimension1CodeOnAfterV()
    begin
        CurrPage.Update();
    end;

    local procedure ShortcutDimension2CodeOnAfterV()
    begin
        CurrPage.Update();
    end;

    local procedure CheckMandFields()
    var
        mrsLineLr: record MRSLine;
    begin
        TestField("Expected Delivery Date");
        TestField("Location Code");
        mrsLineLr.reset();
        mrsLineLr.SetRange("Document No.", "MRS No.");
        IF mrsLineLr.findset() then BEGIN
            repeat
                mrsLineLr.TestField("Location Code");
                mrsLineLr.TestField(Quantity);
            until mrsLineLr.next() = 0;
        end else
            error('No lines are existing for this document.')
    end;
}