table 50378 "Rebate Transaction Log"
{
    Caption = 'Rebate Transaction Log';
    DataClassification = ToBeClassified;
    DrillDownPageId = 50659;

    fields
    {
        field(1; "Order No."; Code[20])
        {
            Caption = 'Order No.';
        }
        field(2; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
        }
        field(3; "Customer Name "; Text[100])
        {
            Caption = 'Customer Name ';
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Customer No.")));
        }
        field(4; "Reabate Amount"; Decimal)
        {
            Caption = 'Reabate Amount';
        }
        field(5; "Total Reabate Amount"; Decimal)
        {
            Caption = 'Total Reabate Amount';
        }
        field(6; "Rebate Balance Amount"; Decimal)
        {
            Caption = 'Rebate Balance Amount';
        }
        field(7; "Entry Type"; Option)
        {
            Caption = 'Entry Type';
            OptionMembers = "Application Entry","Return Entry";
            OptionCaption = 'Application Entry,Return Entry';
        }
        field(8; "Rebate Period"; Code[20])
        {
            Caption = 'Rebate Period ';
        }
        field(9; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            DataClassification = ToBeClassified;
        }
        field(10; "Applied By"; Text[100])
        {
            Caption = 'Applied By';
            DataClassification = ToBeClassified;
        }
        field(11; "Rebate Discount Status"; Option)
        {
            Caption = 'Rebate Discount Status';
            OptionCaption = 'Initial,Partial,Full';
            OptionMembers = Initial,Partial,Full;
            DataClassification = ToBeClassified;
        }
        field(12; "Return Count"; Integer)
        {
            Caption = 'Return Count';
        }
        field(13; "No. of Transaction in a Month"; Integer)
        {
            Caption = 'No. of Transaction in a Month';

        }
        field(14; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            AutoIncrement = true;
        }
    }
    keys
    {
        key(PK; "Entry No.", "Order No.")
        {
            Clustered = true;
        }
    }

    fieldgroups
    {
        fieldgroup(General; "Order No.", "Customer No.", "Customer Name ", "Reabate Amount", "Rebate Balance Amount", "Entry Type", "Rebate Period", "Entry No.")
        {
            Caption = 'Rebate Transaction Log';
        }
        fieldgroup(Dropdown; "Order No.", "Customer No.", "Customer Name ", "Reabate Amount", "Rebate Balance Amount", "Entry Type", "Rebate Period", "Entry No.")
        {
            Caption = 'Rebate Transaction Log';
        }
        fieldgroup(lookup; "Order No.", "Customer No.", "Customer Name ", "Reabate Amount", "Rebate Balance Amount", "Entry Type", "Rebate Period", "Entry No.")
        {
            Caption = 'Rebate Transaction Log';
        }
        fieldgroup(Brick; "Order No.", "Customer No.", "Customer Name ", "Reabate Amount", "Rebate Balance Amount", "Entry Type", "Rebate Period", "Entry No.")
        {
            Caption = 'Rebate Transaction Log';
        }
    }
}
