// >>>>>> G2S 28/06/2024  CAS-01310-Q4B3G1 Cost Error
report 50063 "Insert value Entries"
{
    ApplicationArea = All;
    Caption = 'Insert value Entries';
    UsageCategory = Tasks;
    ProcessingOnly = true;
    Permissions = tabledata "Value Entry" = MI, tabledata "Post Value Entry to G/L" = mi, tabledata "G/L Entry" = MD, tabledata "G/L - Item Ledger Relation" = D,
        tabledata "Posted Whse. Receipt Line" = RI;


    dataset
    {
        dataitem(Integer;
        Integer)
        {
            DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
            trigger OnPostDataItem()
            var
            begin
                //>>>>>> CAS-01325-Y3D6V2_PostedWhseReceipt
                InsertPostedWhseLine();
                //>>>>>> CAS-01325-Y3D6V2_PostedWhseReceipt

                if not CheckGlEntries() then begin
                    InsertValueEntries();
                    //>>>>>>  22/7/24 CAS-01317-R7G4M3_G/L_Adj
                    <PERSON>GenLedger();
                    DelGLItemLedgRel();
                    PostValEntrytoGL();
                    //>>>>>>  22/7/24 CAS-01317-R7G4M3_G/L_Adj

                    GLEntry.Reset();
                    GLEntry.SetRange("Document No.", 'MTR551133');
                    GLEntry.SetRange("Posting Date", 20240613D);
                    if GLEntry.FindLast() then begin
                        RecCount := 0;
                        repeat
                            RecCount := RecCount + 1;
                            GLEntry."Global Dimension 2 Code" := 'PRQM';
                            if GLEntry."ShortCut Dimension code 3" = 'EXOTIC' then
                                GLEntry.Description := 'Chivita Exotic Pineapple and Coconut 315 ml x 12 Pcs';
                            GLEntry.Modify();
                            if RecCount = 4 then exit
                        until GLEntry.Next(-1) = 0;
                    end;
                end;

            end;

        }
    }
    requestpage
    {
        layout
        {
            area(content)
            {
                group(GroupName)
                {
                }
            }
        }
        actions
        {
            area(processing)
            {
            }
        }
    }

    var
        ValueEntry: Record "Value Entry";
        ValueEntry2: Record "Value Entry";
        LastEntryNo, ValueEntryNo, NewEntryNo, RecCount : Integer;
        Item: Record Item;
        ItemNo: Code[20];
        Windows: Dialog;
        DimensionID: Integer;
        windowsTest: label 'Inserting #1';
        GLItemLedgRel: Record "G/L - Item Ledger Relation";
        GLEntry: Record "G/L Entry";
        PstdWhseRcptLn: Record "Posted Whse. Receipt Line";


    //>>>>>> CAS-01325-Y3D6V2_PostedWhseReceipt
    procedure InsertPostedWhseLine()
    begin
        PstdWhseRcptLn.Reset();
        PstdWhseRcptLn.SetCurrentKey("Posted Source No.");
        PstdWhseRcptLn.SetRange("Posted Source No.", 'MTR551133');
        PstdWhseRcptLn.SetRange("Posting Date", 20240613D);
        PstdWhseRcptLn.SetRange("Line No.", 60000);
        if not PstdWhseRcptLn.FindFirst() then begin
            PstdWhseRcptLn.Init();
            PstdWhseRcptLn."No." := 'BBAPWR03462';
            PstdWhseRcptLn.Validate("Line No.", 60000);
            PstdWhseRcptLn.Validate("Source Type", 5741);
            PstdWhseRcptLn.Validate("Source Subtype", 1);
            PstdWhseRcptLn.Validate("Source No.", 'MT748954');
            PstdWhseRcptLn.Validate("Source Line No.", 60000);
            PstdWhseRcptLn.Validate("Source Document", PstdWhseRcptLn."Source Document"::"Inbound Transfer");
            PstdWhseRcptLn.Validate("Location Code", 'SMBBAD');
            PstdWhseRcptLn.Validate("Item No.", 'YFG0083');
            PstdWhseRcptLn.Validate(Quantity, 81);
            PstdWhseRcptLn.Validate("Qty. (Base)", 81);
            PstdWhseRcptLn.Validate("Qty. Put Away", 81);
            PstdWhseRcptLn.Validate("Qty. Put Away (Base)", 81);
            PstdWhseRcptLn.Validate("Unit of Measure Code", 'TRAY');
            PstdWhseRcptLn.Validate("Qty. per Unit of Measure", 1);
            PstdWhseRcptLn.Validate(Description, 'YOGHURT STRAWBERRY 315ML X 12 PCS VIS');
            PstdWhseRcptLn.Validate("Due Date", 20240613D);
            PstdWhseRcptLn.Validate("Starting Date", 20240613D);
            PstdWhseRcptLn.Validate("Qty. Cross-Docked", 0);
            PstdWhseRcptLn.Validate("Qty. Cross-Docked (Base)", 0);
            PstdWhseRcptLn.Validate("Posted Source Document", PstdWhseRcptLn."Posted Source Document"::"Posted Transfer Receipt");
            PstdWhseRcptLn.Validate("Posted Source No.", 'MTR551133');
            PstdWhseRcptLn.Validate("Posting Date", 20240613D);
            PstdWhseRcptLn.Validate("Whse. Receipt No.", 'BBAWR25065');
            PstdWhseRcptLn.Validate("Whse Receipt Line No.", 60000);
            PstdWhseRcptLn.Validate(Status, PstdWhseRcptLn.Status::"Completely Put Away");
            PstdWhseRcptLn.Validate("Posted Loading Slip Line No.", 0);
            PstdWhseRcptLn.Insert();
        end;

        PstdWhseRcptLn.Reset();
        PstdWhseRcptLn.SetCurrentKey("Posted Source No.");
        PstdWhseRcptLn.SetRange("Line No.", 70000);
        PstdWhseRcptLn.SetRange("Posted Source No.", 'MTR551133');
        PstdWhseRcptLn.SetRange("Posting Date", 20240613D);
        if not PstdWhseRcptLn.FindFirst() then begin
            PstdWhseRcptLn.Init();
            PstdWhseRcptLn."No." := 'BBAPWR03462';
            PstdWhseRcptLn.Validate("Line No.", 70000);
            PstdWhseRcptLn.Validate("Source Type", 5741);
            PstdWhseRcptLn.Validate("Source Subtype", 1);
            PstdWhseRcptLn.Validate("Source No.", 'MT748954');
            PstdWhseRcptLn.Validate("Source Line No.", 70000);
            PstdWhseRcptLn.Validate("Source Document", PstdWhseRcptLn."Source Document"::"Inbound Transfer");
            PstdWhseRcptLn.Validate("Location Code", 'SMBBAD');
            PstdWhseRcptLn.Validate("Item No.", 'VFG0310');
            PstdWhseRcptLn.Validate(Quantity, 24);
            PstdWhseRcptLn.Validate("Qty. (Base)", 24);
            PstdWhseRcptLn.Validate("Qty. Put Away", 24);
            PstdWhseRcptLn.Validate("Qty. Put Away (Base)", 24);
            PstdWhseRcptLn.Validate("Unit of Measure Code", 'TRAY');
            PstdWhseRcptLn.Validate("Qty. per Unit of Measure", 1);
            PstdWhseRcptLn.Validate(Description, 'Chivita Exotic Pineapple and Coconut 315 ml x 12 Pcs');
            PstdWhseRcptLn.Validate("Due Date", 20240613D);
            PstdWhseRcptLn.Validate("Starting Date", 20240613D);
            PstdWhseRcptLn.Validate("Qty. Cross-Docked", 0);
            PstdWhseRcptLn.Validate("Qty. Cross-Docked (Base)", 0);
            PstdWhseRcptLn.Validate("Posted Source Document", PstdWhseRcptLn."Posted Source Document"::"Posted Transfer Receipt");
            PstdWhseRcptLn.Validate("Posted Source No.", 'MTR551133');
            PstdWhseRcptLn.Validate("Posting Date", 20240613D);
            PstdWhseRcptLn.Validate("Whse. Receipt No.", 'BBAWR25065');
            PstdWhseRcptLn.Validate("Whse Receipt Line No.", 70000);
            PstdWhseRcptLn.Validate(Status, PstdWhseRcptLn.Status::"Completely Put Away");
            PstdWhseRcptLn.Validate("Posted Loading Slip Line No.", 0);
            PstdWhseRcptLn.Insert();
        end;
    end;
    //>>>>>> CAS-01325-Y3D6V2_PostedWhseReceipt


    procedure InsertValueEntries()
    begin
        ItemNo := 'VFG0310';
        Item.SetRange("No.", ItemNo);
        if item.FindFirst() then begin
            DimensionID := 0;
            ValueEntryNo := 8708788;
            ValueEntry.SetCurrentKey("Item Ledger Entry No.");
            ValueEntry.SetRange("Item Ledger Entry No.", ValueEntryNo);
            // ValueEntry.SetRange("Entry No.",);
            ValueEntry.LockTable();
            // if ValueEntry.FindLast() then
            //     LastEntryNo := ValueEntry."Entry No.";
            if ValueEntry.FindFirst() then begin
                DimensionID := GetDimension(ItemNo);
                windows.Open(windowsTest, ValueEntryNo);
                ValueEntry."Posting Date" := 20240613D;
                ValueEntry.Validate("Item Ledger Entry No.", ValueEntryNo);
                ValueEntry."Item Ledger Entry Type" := ValueEntry."Item Ledger Entry Type"::Transfer;
                ValueEntry."Entry Type" := ValueEntry."Entry Type"::"Direct Cost";
                ValueEntry.Adjustment := false;
                ValueEntry."Document Type" := ValueEntry."Document Type"::"Transfer Receipt";
                ValueEntry."Document No." := 'MTR551133';
                ValueEntry.Validate("Item No.", ItemNo);
                ValueEntry.Validate(Description, 'Chivita Exotic Pineapple and Coconut 315 ml x 12 Pcs');
                ValueEntry.Validate("External Document No.", 'REPROCESSING');
                ValueEntry.Validate("Order Type", ValueEntry."Order Type"::Transfer);
                ValueEntry.Validate("Cost per Unit", 2675.34);
                ValueEntry.Validate("Cost Amount (Actual)", -64208.19);
                ValueEntry.Validate("Item Ledger Entry Quantity", -24);
                ValueEntry.Validate("Valued Quantity", -24);
                ValueEntry.Validate("Invoiced Quantity", -24);
                ValueEntry.Validate("Location Code", 'INTRANSIT');
                ValueEntry.Validate("Inventory Posting Group", item."Inventory Posting Group");
                ValueEntry.Validate("User ID", 'CHILIMITED\FLORENCE.GABRIEL');
                ValueEntry.Validate("Source Code", 'TRANSFER');
                ValueEntry.Validate("Global Dimension 1 Code", 'LOS');
                ValueEntry.Validate("Global Dimension 2 Code", 'PRQM');
                ValueEntry.Validate("Document Date", 20240613D);
                ValueEntry.Validate("Document Line No.", 70000);
                ValueEntry.Validate("Order Line No.", 70000);
                ValueEntry.Validate("Order No.", 'MT748954');
                ValueEntry.Validate("Dimension Set ID", DimensionID);
                ValueEntry.Inventoriable := true;
                ValueEntry."Valuation Date" := 20240613D;

                ValueEntry.Validate("Gen. Prod. Posting Group", Item."Gen. Prod. Posting Group");
                if ValueEntry.Modify() = true then
                    Commit();
            end;

            ValueEntry.Reset();
            ValueEntryNo := 8708789;
            ValueEntry.SetCurrentKey("Item Ledger Entry No.");
            ValueEntry.SetRange("Item Ledger Entry No.", ValueEntryNo);
            ValueEntry.LockTable();
            if ValueEntry.FindFirst() then begin
                // if ValueEntry.FindLast() then
                //     LastEntryNo := ValueEntry."Entry No.";
                // ValueEntry.Init();
                windows.Open(windowsTest, ValueEntryNo);
                ValueEntry."Posting Date" := 20240613D;
                ValueEntry.Validate("Item Ledger Entry No.", ValueEntryNo);
                ValueEntry."Item Ledger Entry Type" := ValueEntry."Item Ledger Entry Type"::Transfer;
                ValueEntry."Entry Type" := ValueEntry."Entry Type"::"Direct Cost";
                ValueEntry.Adjustment := false;
                ValueEntry."Document Type" := ValueEntry."Document Type"::"Transfer Receipt";
                ValueEntry."Document No." := 'MTR551133';
                ValueEntry.Validate("Item No.", ItemNo);
                ValueEntry.Validate(Description, 'Chivita Exotic Pineapple and Coconut 315 ml x 12 Pcs');
                ValueEntry.Validate("External Document No.", 'REPROCESSING');
                ValueEntry.Validate("Order Type", ValueEntry."Order Type"::Transfer);
                ValueEntry.Validate("Cost per Unit", 2675.34);
                ValueEntry.Validate("Cost Amount (Actual)", 64208.19);
                ValueEntry.Validate("Item Ledger Entry Quantity", 24);
                ValueEntry.Validate("Valued Quantity", 24);
                ValueEntry.Validate("Invoiced Quantity", 24);
                ValueEntry.Validate("Location Code", 'SMBBAD');
                ValueEntry.Validate("Inventory Posting Group", Item."Inventory Posting Group");
                ValueEntry.Validate("User ID", 'CHILIMITED\FLORENCE.GABRIEL');
                ValueEntry.Validate("Source Code", 'TRANSFER');
                ValueEntry.Validate("Global Dimension 1 Code", 'LOS');
                ValueEntry.Validate("Global Dimension 2 Code", 'PRQM');
                ValueEntry.Validate("Document Date", 20240613D);
                ValueEntry.Validate("Document Line No.", 70000);
                ValueEntry.Validate("Order Line No.", 70000);
                ValueEntry.Validate("Order No.", 'MT748954');
                ValueEntry.Validate("Dimension Set ID", DimensionID);
                ValueEntry.Inventoriable := true;
                ValueEntry."Valuation Date" := 20240613D;
                ValueEntry.Validate("Gen. Prod. Posting Group", Item."Gen. Prod. Posting Group");
                if ValueEntry.Modify() = true then
                    commit();
                Windows.Close();
            end;

        end;
        EXIT;
    end;

    //>>>>>>  22/7/24 CAS-01317-R7G4M3_G/L_Adj
    procedure DelGenLedger()
    begin
        GLEntry.SetCurrentKey("Entry No.");
        GLEntry.SetFilter("Entry No.", '%1|%2|%3', 107287642, 107287643, 107287644);
        if GLEntry.FindSet() then
            GLEntry.DeleteAll();
    end;


    procedure DelGLItemLedgRel()
    begin
        GLItemLedgRel.Reset();
        GLItemLedgRel.SetRange("G/L Entry No.", 107287642);
        GLItemLedgRel.SetRange("Value Entry No.", 46554327);
        if GLItemLedgRel.FindFirst() then
            GLItemLedgRel.Delete();
    end;
    //Life
    procedure PostValEntrytoGL()
    var
        PostValEntrytoGL: Record "Post Value Entry to G/L";
    begin
        PostValEntrytoGL.Init();
        PostValEntrytoGL."Value Entry No." := 47080952;
        PostValEntrytoGL."Item No." := 'VFG0310';
        PostValEntrytoGL."Posting Date" := 20240613D;
        PostValEntrytoGL.Insert();

        PostValEntrytoGL.Init();
        PostValEntrytoGL."Value Entry No." := 47080953;
        PostValEntrytoGL."Item No." := 'VFG0310';
        PostValEntrytoGL."Posting Date" := 20240613D;
        PostValEntrytoGL.Insert();
        Commit();

        Report.Run(REPORT::"Post Inventory Cost to G/L", True, false, PostValEntrytoGL);
    end;

    // procedure ValueEntryN(int: Integer): Integer
    // begin
    //     ValueEntry.Reset();
    //     ValueEntry.SetCurrentKey("Item Ledger Entry No.");
    //     ValueEntry.SetRange("Item Ledger Entry No.", int);
    //     if ValueEntry.FindFirst() then
    //         EXIT(ValueEntry."Entry No.");
    // end;

    procedure CheckGlEntries(): Boolean
    begin
        GLEntry.Reset();
        GLEntry.SetRange("Document No.", 'MTR551133');
        GLEntry.SetRange("Posting Date", 20240613D);
        if GLEntry.FindSet() then
            if GLEntry.Count >= 28 then
                exit(True);
    end;
    //>>>>>>  22/7/24 CAS-01317-R7G4M3_G/L_Adj
    procedure GetDimension(ItemNo: Code[20]): Integer
    var
        DefDim: Record "Default Dimension";
        ItemCopy: Record Item;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimValue: Record "Dimension Value";
        DimensionManagement: Codeunit DimensionManagement;

    begin
        ItemCopy.Reset();
        ItemCopy.SetRange("No.", ItemNo);
        if ItemCopy.FindFirst() then begin
            DefDim.Reset();
            DefDim.SetRange("No.", ItemNo);
            DefDim.SetFilter("Dimension Value Code", '<>%1', '');
            //DefDim.SetRange("Value Posting", DefDim."Value Posting"::"Code Mandatory");
            if DefDim.FindFirst() then begin
                repeat
                    TempDimSetEntry.Init();
                    TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                    TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                    DimValue.Reset();
                    DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                    DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                    if DimValue.FindFirst() then begin
                        TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                    end;
                    // TempDimSetEntry."Dimension Set ID" := -1;
                    IF NOT TempDimSetEntry.INSERT
                    THEN
                        TempDimSetEntry.MODIFY;
                //ItemJnlLine."Dimension Set ID" := 

                until DefDim.Next() = 0;

                EXIT(DimensionManagement.GetDimensionSetID(TempDimSetEntry));
            end;
        end;
    end;
}
// >>>>>> G2S 28/06/2024  CAS-01310-Q4B3G1 Cost Error