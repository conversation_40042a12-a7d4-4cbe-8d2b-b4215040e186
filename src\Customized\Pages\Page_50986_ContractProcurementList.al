page 50986 "Contract Procurement List"
{
    //Created by - B2BMSOn18Oct21
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Contract Procurement";

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("S.No."; "S.No.")
                {
                    ApplicationArea = All;
                }
                field("Type"; "Type")
                {
                    ApplicationArea = All;
                }
                //B2BMSOn20Oct21>>
                field("Contract Type"; "Contract Type")
                {
                    ApplicationArea = all;
                }
                //B2BMSOn20Oct21<<
                field("Name of Agreement"; "Name of Agreement")
                {
                    ApplicationArea = All;
                }
                field("Duration"; "Duration")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Address of Supplier"; "Address of Supplier")
                {
                    ApplicationArea = All;
                }
                field(Nature; Nature)
                {
                    ApplicationArea = All;
                }
                field("Contract Start Date"; "Contract Start Date")
                {
                    ApplicationArea = All;
                }
                field("Contract Expiry Date"; "Contract Expiry Date")
                {
                    ApplicationArea = All;
                }
                field("Contract Renewal Date"; "Contract Renewal Date")
                {
                    ApplicationArea = All;
                }
                //B2BMSOn20Oct21>>
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = all;
                }
                field("Vendor Name"; "Vendor Name")
                {
                    ApplicationArea = all;
                }
                //B2BMSOn20Oct21>>
                field(Status; Status)
                {
                    ApplicationArea = All;
                }
                field("KPI/Deliverables"; "KPI/Deliverables")
                {
                    ApplicationArea = All;
                }
                field("Contact Person CHI"; "Contact Person CHI")
                {
                    ApplicationArea = All;
                }
                field("Contact Person Supplier"; "Contact Person Supplier")
                {
                    ApplicationArea = All;
                }
                field(Locality; Locality)
                {
                    ApplicationArea = All;
                }
                field(Email; Email)
                {
                    ApplicationArea = All;
                }
                field("Phone No."; "Phone No.")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Image = "Action";
                action("Send Email")
                {
                    Caption = 'Send Email';
                    Image = SendMail;
                    Promoted = true;
                    PromotedIsBig = true;
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        ConRenewal.Run();
                    end;
                }
            }
        }
    }
    var
        ConRenewal: Codeunit "Contract Renewal Mail Alert";

}