codeunit 50147 Validations
{
    trigger OnRun()
    begin

    end;

    procedure CheckQtyValidations(VariantRec: Variant)
    var
        RecRef: RecordRef;
        SalesHeader: Record "Sales Header";
        TraansferHeader: Record "Transfer Header";
        ItemJournalLine: Record "Item Journal Line";
        SalesLine: Record "Sales Line";
        TransferLines: Record "Transfer Line";
        SalesLine2: Record "Sales Line";
        TransferLine2: Record "Transfer Line";
        SalesQty: Decimal;
        TransferQty: Decimal;
        Inventory: Decimal;
        ItemJournalQty: Decimal;
        ItemLedgerEntry: Record "Item Ledger Entry";
        AvaliableQty: Decimal;
        ItemJournalLine2: Record "Item Journal Line";
        SalesRecvsetup: Record "Sales & Receivables Setup";
        UnderinsQty: Decimal;
    begin
        SalesRecvsetup.Get();
        if not SalesRecvsetup."Preventive Negative Quantity" then
            exit;
        Clear(TransferQty);
        Clear(SalesQty);
        Clear(Inventory);
        Clear(ItemJournalQty);
        Clear(AvaliableQty);
        RecRef.GETTABLE(VariantRec);
        CASE RecRef.NUMBER OF
            37:
                begin
                    SalesLine := VariantRec;
                    IF (SalesLine.Type = SalesLine.Type::Item) and (SalesLine."Outstanding Quantity" <> 0) then begin
                        GetQCDetails(SalesLine."No.", SalesLine."Location Code", UnderinsQty);
                        //PKONDE18
                        ItemLedgerEntry.Reset();
                        ItemLedgerEntry.SetRange("Item No.", SalesLine."No.");
                        ItemLedgerEntry.SetRange("Location Code", SalesLine."Location Code");
                        if ItemLedgerEntry.FindSet() then begin
                            ItemLedgerEntry.CalcSums(Quantity);
                            Inventory := ItemLedgerEntry.Quantity;
                        end;
                        SalesLine2.Reset();
                        SalesLine2.SetRange(Type, SalesLine.Type);
                        SalesLine2.SetRange("No.", SalesLine."No.");
                        SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                        //SalesLine2.SetFilter("Document No.", '<>%1', SalesLine."Document No.");//PKON290521
                        //SalesLine2.SetFilter("Line No.", '<>%1', SalesLine."Line No.");//PKON290521
                        SalesLine2.SetRange("Location Code", SalesLine."Location Code");
                        if SalesLine2.FindSet() then begin
                            SalesLine2.CalcSums("Outstanding Qty. (Base)");
                            SalesQty := SalesLine2."Outstanding Qty. (Base)";
                        end;
                        SalesLine2.Reset();
                        SalesLine2.SetRange(Type, SalesLine.Type);
                        SalesLine2.SetRange("No.", SalesLine."No.");
                        SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                        SalesLine2.SetRange("Document No.", SalesLine."Document No.");//PKON290521
                        SalesLine2.SetRange("Line No.", SalesLine."Line No.");//PKON290521
                        SalesLine2.SetRange("Location Code", SalesLine."Location Code");
                        if SalesLine2.FindSet() then begin
                            SalesLine2.CalcSums("Outstanding Qty. (Base)");
                            SalesQty -= SalesLine2."Outstanding Qty. (Base)";
                        end;

                        TransferLines.Reset();
                        TransferLines.SetRange("Item No.", SalesLine."No.");
                        TransferLines.SetRange("Transfer-from Code", SalesLine."Location Code");
                        if TransferLines.FindSet() then begin
                            TransferLines.CalcSums("Quantity (Base)");
                            TransferLines.CalcSums("Qty. Shipped (Base)");
                            TransferQty := TransferLines."Quantity (Base)" - TransferLines."Qty. Shipped (Base)";
                        end;
                        ItemJournalLine.Reset();
                        ItemJournalLine.SetFilter("Entry Type", '%1|%2', ItemJournalLine."Entry Type"::"Negative Adjmt.", ItemJournalLine."Entry Type"::Transfer);
                        ItemJournalLine.SetRange("Item No.", SalesLine."No.");
                        ItemJournalLine.SetRange("Location Code", SalesLine."Location Code");
                        if ItemJournalLine.FindSet() then begin
                            ItemJournalLine.CalcSums("Quantity (Base)");
                            ItemJournalQty := ItemJournalLine."Quantity (Base)";
                        end;
                        AvaliableQty := Inventory - (SalesQty + TransferQty + ItemJournalQty + UnderinsQty);//PKONDE18
                        if AvaliableQty < SalesLine."Quantity (Base)" then
                            Error('Stock is not sufficcent for item no %1 Location %2 to process the order , Requirment for sales Order %3 ,Requirment for transfer order %4,Requirment for Item Journals %5,Avaliable Qty %6,Shortage Qty %7 Under Inspection QC Qty %8'
                            , SalesLine."No.", SalesLine."Location Code", SalesQty, TransferQty, ItemJournalQty, Inventory, AvaliableQty - SalesLine."Quantity (Base)", UnderinsQty);//PKONDE18
                    end;
                end;
            Database::"Transfer Line":
                begin
                    TransferLines := VariantRec;
                    GetQCDetails(TransferLines."Item No.", TransferLines."Transfer-from Code", UnderinsQty);
                    //PKONDE18 <<
                    ItemLedgerEntry.Reset();
                    ItemLedgerEntry.SetRange("Item No.", TransferLines."Item No.");
                    ItemLedgerEntry.SetRange("Location Code", TransferLines."Transfer-from Code");
                    if ItemLedgerEntry.FindSet() then begin
                        ItemLedgerEntry.CalcSums(Quantity);
                        Inventory := ItemLedgerEntry.Quantity;
                    end;
                    SalesLine2.Reset();
                    SalesLine2.SetRange(Type, SalesLine.Type::Item);
                    SalesLine2.SetRange("No.", TransferLines."Item No.");
                    SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                    SalesLine2.SetFilter("Outstanding Qty. (Base)", '<>%1', 0);
                    SalesLine2.SetRange("Location Code", TransferLines."Transfer-from Code");
                    if SalesLine2.FindSet() then begin
                        SalesLine2.CalcSums("Outstanding Qty. (Base)");
                        SalesQty := SalesLine2."Outstanding Qty. (Base)";
                    end;
                    TransferLine2.Reset();
                    TransferLine2.SetRange("Item No.", TransferLines."Item No.");
                    TransferLine2.SetRange("Transfer-from Code", TransferLines."Transfer-from Code");
                    //TransferLine2.SetFilter("Document No.", '<>%1', TransferLines."Document No.");//PKON290521
                    //TransferLine2.SetFilter("Line No.", '<>%1', TransferLines."Line No.");//PKON290521
                    if TransferLine2.FindSet() then begin
                        TransferLine2.CalcSums("Quantity (Base)");
                        TransferLine2.CalcSums("Qty. Shipped (Base)");
                        TransferQty := TransferLine2."Quantity (Base)" - TransferLine2."Qty. Shipped (Base)";
                    end;
                    TransferLine2.Reset();
                    TransferLine2.SetRange("Item No.", TransferLines."Item No.");
                    TransferLine2.SetRange("Transfer-from Code", TransferLines."Transfer-from Code");
                    TransferLine2.SetRANGE("Document No.", TransferLines."Document No.");//PKON290521
                    TransferLine2.SetRANGE("Line No.", TransferLines."Line No.");//PKON290521
                    if TransferLine2.FindSet() then begin
                        TransferLine2.CalcSums("Quantity (Base)");
                        TransferLine2.CalcSums("Qty. Shipped (Base)");
                        TransferQty -= (TransferLine2."Quantity (Base)" - TransferLine2."Qty. Shipped (Base)");
                    end;
                    ItemJournalLine.Reset();
                    ItemJournalLine.SetFilter("Entry Type", '%1|%2', ItemJournalLine."Entry Type"::"Negative Adjmt.", ItemJournalLine."Entry Type"::Transfer);
                    ItemJournalLine.SetRange("Item No.", TransferLines."Item No.");
                    ItemJournalLine.SetRange("Location Code", TransferLines."Transfer-from Code");
                    if ItemJournalLine.FindSet() then begin
                        ItemJournalLine.CalcSums("Quantity (Base)");
                        ItemJournalQty := ItemJournalLine."Quantity (Base)";
                    end;
                    AvaliableQty := Inventory - (SalesQty + TransferQty + ItemJournalQty + UnderinsQty);//PKONDE18
                    if AvaliableQty < TransferLines."Quantity (Base)" - TransferLines."Qty. Shipped (Base)" then
                        Error('Stock is not sufficent for item no %1 Location %2 to process the order , Requirment for sales Order %3 ,Requirment for transfer order %4,Requirment for Item Journals %5,Avaliable Qty %6,Shortage Qty %7 Under Inspection QC Qty %8'
                        , TransferLines."Item No.", TransferLines."Transfer-from Code", SalesQty, TransferQty, ItemJournalQty, Inventory, AvaliableQty - TransferLines."Quantity (Base)", UnderinsQty);//PKONDE18
                end;
            Database::"Item Journal Line":
                begin
                    ItemJournalLine := VariantRec;
                    GetQCDetails(ItemJournalLine."Item No.", ItemJournalLine."Location Code", UnderinsQty);
                    //PKONDE18 <<
                    ItemLedgerEntry.Reset();
                    ItemLedgerEntry.SetRange("Item No.", ItemJournalLine."Item No.");
                    ItemLedgerEntry.SetRange("Location Code", ItemJournalLine."Location Code");
                    if ItemLedgerEntry.FindSet() then begin
                        ItemLedgerEntry.CalcSums(Quantity);
                        Inventory := ItemLedgerEntry.Quantity;
                    end;
                    SalesLine2.Reset();
                    SalesLine2.SetRange(Type, SalesLine.Type::Item);
                    SalesLine2.SetRange("No.", ItemJournalLine."Item No.");
                    SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                    SalesLine2.SetFilter("Outstanding Qty. (Base)", '<>%1', 0);
                    SalesLine2.SetRange("Location Code", ItemJournalLine."Location Code");
                    if SalesLine2.FindSet() then begin
                        SalesLine2.CalcSums("Outstanding Qty. (Base)");
                        SalesQty := SalesLine2."Outstanding Qty. (Base)";
                    end;
                    TransferLine2.Reset();
                    TransferLine2.SetRange("Item No.", ItemJournalLine."Item No.");
                    TransferLine2.SetRange("Transfer-from Code", ItemJournalLine."Location Code");
                    if TransferLine2.FindSet() then begin
                        TransferLine2.CalcSums("Quantity (Base)");
                        TransferLine2.CalcSums("Qty. Shipped (Base)");
                        TransferQty := TransferLine2."Quantity (Base)" - TransferLine2."Qty. Shipped (Base)";
                    end;
                    ItemJournalLine2.Reset();
                    ItemJournalLine2.SetFilter("Entry Type", '%1|%2', ItemJournalLine."Entry Type"::"Negative Adjmt.", ItemJournalLine."Entry Type"::Transfer);
                    ItemJournalLine2.SetRange("Item No.", ItemJournalLine."Item No.");
                    ItemJournalLine2.SetRange("Location Code", ItemJournalLine."Location Code");
                    if ItemJournalLine2.FindSet() then begin
                        ItemJournalLine2.CalcSums("Quantity (Base)");
                        ItemJournalQty := ItemJournalLine2."Quantity (Base)";
                    end;
                    AvaliableQty := Inventory - (SalesQty + TransferQty + ItemJournalQty + UnderinsQty);//PKONDE18
                    if AvaliableQty < ItemJournalLine2."Quantity (Base)" then
                        Error('Stock is not sufficcent for item no %1 Location %2 to process the order , Requirment for sales Order %3 ,Requirment for transfer order %4,Requirment for Item Journals %5,Avaliable Qty %6,Shortage Qty %7 Under Inspection QC Qty %8'
                        , ItemJournalLine2."Item No.", ItemJournalLine2."Location Code", SalesQty, TransferQty, ItemJournalQty, Inventory, AvaliableQty - ItemJournalLine2."Quantity (Base)", UnderinsQty);//PKONDE18
                end;
        end;
    end;

    procedure CheckQtyValidationsTrueOrFalse(VariantRec: Variant): Boolean
    var
        RecRef: RecordRef;
        SalesHeader: Record "Sales Header";
        TraansferHeader: Record "Transfer Header";
        ItemJournalLine: Record "Item Journal Line";
        SalesLine: Record "Sales Line";
        TransferLines: Record "Transfer Line";
        SalesLine2: Record "Sales Line";
        TransferLine2: Record "Transfer Line";
        SalesQty: Decimal;
        TransferQty: Decimal;
        Inventory: Decimal;
        ItemJournalQty: Decimal;
        ItemLedgerEntry: Record "Item Ledger Entry";
        AvaliableQty: Decimal;
        ItemJournalLine2: Record "Item Journal Line";
        SalesRecvsetup: Record "Sales & Receivables Setup";
        UnderinsQty: Decimal;
    begin
        SalesRecvsetup.Get();
        if not SalesRecvsetup."Preventive Negative Quantity" then
            exit;
        Clear(TransferQty);
        Clear(SalesQty);
        Clear(Inventory);
        Clear(ItemJournalQty);
        Clear(AvaliableQty);
        RecRef.GETTABLE(VariantRec);
        CASE RecRef.NUMBER OF
            37:
                begin
                    SalesLine := VariantRec;
                    IF (SalesLine.Type = SalesLine.Type::Item) and (SalesLine."Outstanding Quantity" <> 0) then begin
                        GetQCDetails(SalesLine."No.", SalesLine."Location Code", UnderinsQty);//PKONDE18 
                        ItemLedgerEntry.Reset();
                        ItemLedgerEntry.SetRange("Item No.", SalesLine."No.");
                        ItemLedgerEntry.SetRange("Location Code", SalesLine."Location Code");
                        if ItemLedgerEntry.FindSet() then begin
                            ItemLedgerEntry.CalcSums(Quantity);
                            Inventory := ItemLedgerEntry.Quantity;
                        end;
                        SalesLine2.Reset();
                        SalesLine2.SetRange(Type, SalesLine.Type);
                        SalesLine2.SetRange("No.", SalesLine."No.");
                        SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                        //SalesLine2.SetFilter("Document No.", '<>%1', SalesLine."Document No.");//PKON290521
                        //SalesLine2.SetFilter("Line No.", '<>%1', SalesLine."Line No.");//PKON290521
                        SalesLine2.SetRange("Location Code", SalesLine."Location Code");
                        if SalesLine2.FindSet() then begin
                            SalesLine2.CalcSums("Outstanding Qty. (Base)");
                            SalesQty := SalesLine2."Outstanding Qty. (Base)";
                        end;
                        SalesLine2.Reset();
                        SalesLine2.SetRange(Type, SalesLine.Type);
                        SalesLine2.SetRange("No.", SalesLine."No.");
                        SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                        SalesLine2.SetRange("Document No.", SalesLine."Document No.");//PKON290521
                        SalesLine2.SetRange("Line No.", SalesLine."Line No.");//PKON290521
                        SalesLine2.SetRange("Location Code", SalesLine."Location Code");
                        if SalesLine2.FindSet() then begin
                            SalesLine2.CalcSums("Outstanding Qty. (Base)");
                            SalesQty -= SalesLine2."Outstanding Qty. (Base)";
                        end;

                        TransferLines.Reset();
                        TransferLines.SetRange("Item No.", SalesLine."No.");
                        TransferLines.SetRange("Transfer-from Code", SalesLine."Location Code");
                        if TransferLines.FindSet() then begin
                            TransferLines.CalcSums("Quantity (Base)");
                            TransferLines.CalcSums("Qty. Shipped (Base)");
                            TransferQty := TransferLines."Quantity (Base)" - TransferLines."Qty. Shipped (Base)";
                        end;
                        ItemJournalLine.Reset();
                        ItemJournalLine.SetFilter("Entry Type", '%1|%2', ItemJournalLine."Entry Type"::"Negative Adjmt.", ItemJournalLine."Entry Type"::Transfer);
                        ItemJournalLine.SetRange("Item No.", SalesLine."No.");
                        ItemJournalLine.SetRange("Location Code", SalesLine."Location Code");
                        if ItemJournalLine.FindSet() then begin
                            ItemJournalLine.CalcSums("Quantity (Base)");
                            ItemJournalQty := ItemJournalLine."Quantity (Base)";
                        end;
                        AvaliableQty := Inventory - (SalesQty + TransferQty + ItemJournalQty);
                        if AvaliableQty < SalesLine."Quantity (Base)" then
                            exit(true)
                    end;
                end;
            Database::"Transfer Line":
                begin
                    TransferLines := VariantRec;
                    GetQCDetails(TransferLines."Item No.", TransferLines."Transfer-from Code", UnderinsQty);//PKONDE18 
                    ItemLedgerEntry.Reset();
                    ItemLedgerEntry.SetRange("Item No.", TransferLines."Item No.");
                    ItemLedgerEntry.SetRange("Location Code", TransferLines."Transfer-from Code");
                    if ItemLedgerEntry.FindSet() then begin
                        ItemLedgerEntry.CalcSums(Quantity);
                        Inventory := ItemLedgerEntry.Quantity;
                    end;
                    SalesLine2.Reset();
                    SalesLine2.SetRange(Type, SalesLine.Type::Item);
                    SalesLine2.SetRange("No.", TransferLines."Item No.");
                    SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                    SalesLine2.SetFilter("Outstanding Qty. (Base)", '<>%1', 0);
                    SalesLine2.SetRange("Location Code", TransferLines."Transfer-from Code");
                    if SalesLine2.FindSet() then begin
                        SalesLine2.CalcSums("Outstanding Qty. (Base)");
                        SalesQty := SalesLine2."Outstanding Qty. (Base)";
                    end;
                    TransferLine2.Reset();
                    TransferLine2.SetRange("Item No.", TransferLines."Item No.");
                    TransferLine2.SetRange("Transfer-from Code", TransferLines."Transfer-from Code");
                    //TransferLine2.SetFilter("Document No.", '<>%1', TransferLines."Document No.");//PKON290521
                    //TransferLine2.SetFilter("Line No.", '<>%1', TransferLines."Line No.");//PKON290521
                    if TransferLine2.FindSet() then begin
                        TransferLine2.CalcSums("Quantity (Base)");
                        TransferLine2.CalcSums("Qty. Shipped (Base)");
                        TransferQty := TransferLine2."Quantity (Base)" - TransferLine2."Qty. Shipped (Base)";
                    end;
                    TransferLine2.Reset();
                    TransferLine2.SetRange("Item No.", TransferLines."Item No.");
                    TransferLine2.SetRange("Transfer-from Code", TransferLines."Transfer-from Code");
                    TransferLine2.SetRANGE("Document No.", TransferLines."Document No.");//PKON290521
                    TransferLine2.SetRANGE("Line No.", TransferLines."Line No.");//PKON290521
                    if TransferLine2.FindSet() then begin
                        TransferLine2.CalcSums("Quantity (Base)");
                        TransferLine2.CalcSums("Qty. Shipped (Base)");
                        TransferQty -= (TransferLine2."Quantity (Base)" - TransferLine2."Qty. Shipped (Base)");
                    end;
                    ItemJournalLine.Reset();
                    ItemJournalLine.SetFilter("Entry Type", '%1|%2', ItemJournalLine."Entry Type"::"Negative Adjmt.", ItemJournalLine."Entry Type"::Transfer);
                    ItemJournalLine.SetRange("Item No.", TransferLines."Item No.");
                    ItemJournalLine.SetRange("Location Code", TransferLines."Transfer-from Code");
                    if ItemJournalLine.FindSet() then begin
                        ItemJournalLine.CalcSums("Quantity (Base)");
                        ItemJournalQty := ItemJournalLine."Quantity (Base)";
                    end;
                    AvaliableQty := Inventory - (SalesQty + TransferQty + ItemJournalQty);
                    if AvaliableQty < TransferLines."Quantity (Base)" - TransferLines."Qty. Shipped (Base)" then
                        exit(true);
                end;
            Database::"Item Journal Line":
                begin
                    ItemJournalLine := VariantRec;
                    GetQCDetails(ItemJournalLine."Item No.", ItemJournalLine."Location Code", UnderinsQty);//PKONDE18 

                    ItemLedgerEntry.Reset();
                    ItemLedgerEntry.SetRange("Item No.", ItemJournalLine."Item No.");
                    ItemLedgerEntry.SetRange("Location Code", ItemJournalLine."Location Code");
                    if ItemLedgerEntry.FindSet() then begin
                        ItemLedgerEntry.CalcSums(Quantity);
                        Inventory := ItemLedgerEntry.Quantity;
                    end;
                    SalesLine2.Reset();
                    SalesLine2.SetRange(Type, SalesLine.Type::Item);
                    SalesLine2.SetRange("No.", ItemJournalLine."Item No.");
                    SalesLine2.SetRange("Document Type", SalesLine2."Document Type"::Order);
                    SalesLine2.SetFilter("Outstanding Qty. (Base)", '<>%1', 0);
                    SalesLine2.SetRange("Location Code", ItemJournalLine."Location Code");
                    if SalesLine2.FindSet() then begin
                        SalesLine2.CalcSums("Outstanding Qty. (Base)");
                        SalesQty := SalesLine2."Outstanding Qty. (Base)";
                    end;
                    TransferLine2.Reset();
                    TransferLine2.SetRange("Item No.", ItemJournalLine."Item No.");
                    TransferLine2.SetRange("Transfer-from Code", ItemJournalLine."Location Code");
                    if TransferLine2.FindSet() then begin
                        TransferLine2.CalcSums("Quantity (Base)");
                        TransferLine2.CalcSums("Qty. Shipped (Base)");
                        TransferQty := TransferLine2."Quantity (Base)" - TransferLine2."Qty. Shipped (Base)";
                    end;
                    ItemJournalLine2.Reset();
                    ItemJournalLine2.SetFilter("Entry Type", '%1|%2', ItemJournalLine."Entry Type"::"Negative Adjmt.", ItemJournalLine."Entry Type"::Transfer);
                    ItemJournalLine2.SetRange("Item No.", ItemJournalLine."Item No.");
                    ItemJournalLine2.SetRange("Location Code", ItemJournalLine."Location Code");
                    if ItemJournalLine2.FindSet() then begin
                        ItemJournalLine2.CalcSums("Quantity (Base)");
                        ItemJournalQty := ItemJournalLine2."Quantity (Base)";
                    end;
                    AvaliableQty := Inventory - (SalesQty + TransferQty + ItemJournalQty);
                    if AvaliableQty < ItemJournalLine2."Quantity (Base)" then
                        Exit(true)
                end;
        end;
    end;

    var
        myInt: Integer;

    [IntegrationEvent(false, false)]
    local procedure GetQCDetails(ItemNo: Text; Location: Text; var Qty: Decimal)
    begin
    end;
}