pageextension 50328 SaleInvLine extends "Posted Sales Invoices"
{
    layout
    {
        addafter("Sell-to Customer Name")//PKON22M31-CR220074
        {
            field("Created By"; "Created By")
            {
                ApplicationArea = all;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = all;
            }
        }
        addafter("Remaining Amount")
        {
            field("Total Qty Sold"; "Total Qty Sold")
            {
                ApplicationArea = all;
            }
        }
        addafter(Corrective)
        {
            field("POS Transaction Type"; "POS Transaction Type")
            {
                ApplicationArea = all;
            }
            field("POS Account No."; "POS Account No.")
            {
                ApplicationArea = all;
            }
            field("Bal. Account No."; "Bal. Account No.")
            {
                ApplicationArea = all;
            }
            field("POS Card Amount"; "POS Card Amount")
            {
                ApplicationArea = all;
            }
            field("POS Cash Amount"; "POS Cash Amount")
            {
                ApplicationArea = all;
            }
            field("POS Transaction No."; "POS Transaction No.")
            {
                ApplicationArea = all;
            }
            field("POSted loading slip No."; "POSTED LOADING SLIP NO.")
            {
                ApplicationArea = all;
            }

        }
        // Add changes to page layout here
        modify("Posting Date")
        {
            Visible = true;
        }
    }

    actions
    {
        modify(Print)
        {
            trigger OnBeforeAction()
            var
                uSrSet: record "User Setup";
            begin
                //b2bpksalecorr12
                // IF "No. Printed" > 1 then begin
                uSrSet.get(UserId);
                IF NOT uSrSet."Reprint Invoiced & Credm Docs" then
                    Error('You do not have permissions to reprint the document.');
                // end
            end;
        }
    }
}