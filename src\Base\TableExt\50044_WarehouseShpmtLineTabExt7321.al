tableextension 50044 WareHouseShpmtLne extends "Warehouse Shipment Line"
{
    fields
    {
        //PKONDE16>>
        modify("Location Code")
        {
            trigger OnAfterValidate()
            var
                SalHdr: Record "Sales Header";
            BEGIN
                SalHdr.reset;
                SalHdr.SetRange("No.", "Source No.");
                IF SalHdr.FindFirst() then
                    IF SalHdr."Location Code" = 'FGDESPATCH' then
                        IF SalHdr."Sales Type" = SalHdr."Sales Type"::Export then
                            "Bin Code" := 'EXPORT'
                        else
                            "Bin Code" := 'SMB' //need to add non editable field
            END;
        }
        //PKONDE16<<
        modify("Source No.")
        {
            trigger OnAfterValidate()
            var
                SalHdr: Record "Sales Header";
            BEGIN
                SalHdr.reset;
                SalHdr.SetRange("No.", "Source No.");
                IF SalHdr.FindFirst() then BEGIN
                    "Customer No." := SalHdr."Sell-to Customer No.";
                    "Customer Name" := SalHdr."Sell-to Customer Name";
                    //Fix24Feb2021>>
                    "Ship-to Code" := SalHdr."Ship-to Code";
                    "Ship to Address" := SalHdr."Ship-to Address";
                    "Ship to Address 2" := SalHdr."Ship-to Address 2";
                    //Fix24Feb2021<<
                END;
            END;
        }
        field(50001; "Posted Loading Slip No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Posted Loading Slip Line"."Document No." where("No." = field("No."), "Document Line No." = field("Line No."), Applied = const(false));

            trigger OnValidate()
            var
                PostdLoadSlpGRec: record "Posted Loading Slip Line";
                PostedWHshipLine: Record "Posted Whse. Shipment Line";
                SalesRece: Record "Sales & Receivables Setup";
            BEGIN
                /*
                                IF "Posted Loading Slip No." <> '' then BEGIN
                                    PostdLoadSlpGRec.reset;
                                    PostdLoadSlpGRec.SetRange("No.", "No.");
                                    PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                                    PostdLoadSlpGRec.SetRange("Document No.", "Posted Loading Slip No.");
                                    IF PostdLoadSlpGRec.findfirst then BEGIN
                                        "Posted Loading Slip Line No." := PostdLoadSlpGRec."Line No.";
                                        VALIDATE("Qty. to Ship", PostdLoadSlpGRec."Qty. Loading");
                                        message('Table %1...%2', "Qty. to Ship", PostdLoadSlpGRec."Qty. Loading")
                                    end;
                                end else begin
                                    Clear("Posted Loading Slip Line No.");
                                    Validate("Qty. to Ship", 0);
                                    message('Table %1...Base %2', "Qty. to Ship", "Qty. to Ship (Base)")
                                end;*/ //prasanna on 23.12.2020


                IF "Posted Loading Slip No." <> '' then BEGIN
                    PostdLoadSlpGRec.reset;
                    PostdLoadSlpGRec.SetRange("No.", "No.");
                    PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                    PostdLoadSlpGRec.SetRange("Document No.", "Posted Loading Slip No.");
                    IF PostdLoadSlpGRec.findfirst then BEGIN
                        if SalesRece.Get() AND SalesRece."Validate PSL in WHSHP" then begin
                            PostedWHshipLine.Reset();
                            PostedWHshipLine.SetRange("Posted Loading Slip No.", "Posted Loading Slip No.");
                            PostedWHshipLine.SetRange("Posted Loading Slip Line No.", PostdLoadSlpGRec."Line No.");
                            if PostedWHshipLine.FindFirst() then
                                Error('Already post in warehousesipment %1 And Line %2', PostedWHshipLine."No.", PostedWHshipLine."Line No.");
                        end;
                        //PostdLoadSlpGRec.Applied := true;//Pk On 29.04.2021
                        "Posted Loading Slip Line No." := PostdLoadSlpGRec."Line No.";
                        VALIDATE("Qty. to Ship", PostdLoadSlpGRec."Qty. Loading");
                        //PostdLoadSlpGRec.Modify();//Pk On 29.04.2021
                        //message('Table %1...%2', "Qty. to Ship", PostdLoadSlpGRec."Qty. Loading")
                    end;
                end else begin
                    /*PostdLoadSlpGRec.reset;
                    PostdLoadSlpGRec.SetRange("No.", "No.");
                    PostdLoadSlpGRec.SetRange("Document Line No.", "Line No.");
                    PostdLoadSlpGRec.SetRange("Document No.", Xrec."Posted Loading Slip No.");
                    IF PostdLoadSlpGRec.findfirst then BEGIN
                        PostdLoadSlpGRec.Applied := false;
                        PostdLoadSlpGRec.Modify();
                    end;*///Pk On 29.04.2021
                    Clear("Posted Loading Slip Line No.");
                    Validate("Qty. to Ship", 0);
                    //message('Table %1...Base %2', "Qty. to Ship", "Qty. to Ship (Base)")
                end;
            end;
        }
        field(50002; "Posted Loading Slip Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = FALSE;
        }
        field(50003; "Customer No."; code[20])
        {
            FieldClass = FlowField;
            CalcFormula = lookup ("Sales Header"."Sell-to Customer No." where("No." = field("Source No.")));
            Editable = FALSE;
        }
        field(50004; "Customer Name"; Text[50])
        {
            FieldClass = FlowField;
            CalcFormula = lookup ("Sales Header"."Sell-to Customer Name" where("No." = field("Source No.")));
            Editable = FALSE;
        }
        //Fix24Feb2021>>
        field(50005; "Ship-to Code"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Ship to Address"; Text[100])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50007; "Ship to Address 2"; Text[50])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        //Fix24Feb2021<<
    }
    //B2BMSOn09Sep21>>
    local procedure GetItemCopy()
    begin
        if Item."No." <> "Item No." then
            Item.Get("Item No.");
    end;

    procedure OpenItemTrackingLinesCopy()
    var
        PurchaseLine: Record "Purchase Line";
        SalesLine: Record "Sales Line";
        ServiceLine: Record "Service Line";
        TransferLine: Record "Transfer Line";
        ReservePurchLine: Codeunit "Purch. Line-Reserve";
        ReserveSalesLine: Codeunit "Sales Line-Reserve";
        ReserveTransferLine: Codeunit "Transfer Line-Reserve";
        ServiceLineReserve: Codeunit "Service Line-Reserve";
        SecondSourceQtyArray: array[3] of Decimal;
        Direction: Enum "Transfer Direction";
        IsHandled: Boolean;
        Codeunit50: Codeunit Codeunit50;
    begin
        IsHandled := false;
        OnBeforeOpenItemTrackingLines(Rec, IsHandled);
        if IsHandled then
            exit;

        TestField("No.");
        TestField("Qty. (Base)");

        GetItemCopy;
        Item.TestField("Item Tracking Code");

        SecondSourceQtyArray[1] := DATABASE::"Warehouse Shipment Line";
        SecondSourceQtyArray[2] := "Qty. to Ship (Base)";
        SecondSourceQtyArray[3] := 0;

        case "Source Type" of
            DATABASE::"Sales Line":
                begin
                    if SalesLine.Get("Source Subtype", "Source No.", "Source Line No.") then
                        Codeunit50.CallItemTrackingSecondSource(SalesLine, SecondSourceQtyArray, "Assemble to Order");
                end;
            DATABASE::"Service Line":
                begin
                    if ServiceLine.Get("Source Subtype", "Source No.", "Source Line No.") then
                        Codeunit50.CallItemTrackingService(ServiceLine);
                end;
            DATABASE::"Purchase Line":
                begin
                    if PurchaseLine.Get("Source Subtype", "Source No.", "Source Line No.") then
                        Codeunit50.CallItemTrackingPurchase(PurchaseLine, SecondSourceQtyArray);
                end;
            DATABASE::"Transfer Line":
                begin
                    Direction := Direction::Outbound;
                    if TransferLine.Get("Source No.", "Source Line No.") then
                        Codeunit50.CallItemTracking(TransferLine, Direction, SecondSourceQtyArray);
                end
        end;

        OnAfterOpenItemTrackingLines(Rec, SecondSourceQtyArray);
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterOpenItemTrackingLines(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; var SecondSourceQtyArray: array[3] of Decimal)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeOpenItemTrackingLines(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; var IsHandled: Boolean)
    begin
    end;

    var
        Item: Record Item;

    //B2BMSOn09Sep21


}