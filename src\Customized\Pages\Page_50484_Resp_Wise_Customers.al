page 50484 "Resp Wise Customers"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Customer Resp. Cent. Lines";
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                }
                field("Resp. Center Code"; "Resp. Center Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Sales Type"; "Sales Type")
                {
                    ApplicationArea = All;

                }
                field("Gen Bus Prod group"; "Gen Bus Prod group")
                {
                    ApplicationArea = all;
                }
                field(Address; Address)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ShowCard)
            {
                ApplicationArea = All;
                Caption = 'Show Card';
                Ellipsis = true;
                Image = List;
                RunObject = page "Customer Card";
                RunPageLink = "No." = field("Customer No.");
            }
        }
    }
    trigger OnOpenPage()
    begin
        UserSetGRec.Reset();
        UserSetGRec.SetRange("User ID", UserId);
        if UserSetGRec.FindFirst() then
            SetRange("Resp. Center Code", UserSetGRec."Sales Resp. Ctr. Filter");

    end;

    var
        myInt: Integer;
        UserSetGRec: Record "User Setup";
}