tableextension 50054 PurchaseCrHeader extends "Purch. Cr. Memo Hdr."
{
    fields
    {
        field(50001; "Purchase Type"; Enum PurchaseType)
        {
            DataClassification = CustomerContent;
        }
        field(50009; "Reason Codes"; Enum ReasonCodes)
        {
            DataClassification = CustomerContent;
        }
        field(50048; "Import File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50049; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }

        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }
        field(50051; "Customer No. For Adj"; code[20])
        {
            DataClassification = CustomerContent;
            ValidateTableRelation = False;//PKONJ29
            TableRelation = "Posted Loading Slip Line"."Customer No." where("Document No." = field("Posted Loading Slip No."));
        }
        //B2BMS
        field(50052; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50053; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50054; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50058; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
    }

    var
        myInt: Integer;
}