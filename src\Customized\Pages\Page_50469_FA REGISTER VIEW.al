page 50469 "FA REGISTER VIEW"
{
    // version TRKIT
    Editable = false;
    PageType = List;
    SourceTable = XX_TRKITVEW_FA_REGISTER;
    SourceTableView = SORTING(NAVISION_UPDATED_TIME)
                      ORDER(Descending)
                      WHERE(TAG_NUMBER = FILTER(<> ''));
    UsageCategory = Administration;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(ASSET_NUMBER; ASSET_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(ASSET_DESCRIPTION; ASSET_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(TAG_NUMBER; TAG_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(FA_LOCATION_CODE; FA_LOCATION_CODE)
                {
                    ApplicationArea = all;
                }
                field(FA_LOCATION_DESCRIPTION; FA_LOCATION_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(ACC_LOCATION_CODE; ACC_LOCATION_CODE)
                {
                    ApplicationArea = all;
                }
                field(ACC_LOCATION_DESCRIPTION; ACC_LOCATION_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(CC_LOCATION_CODE; CC_LOCATION_CODE)
                {
                    ApplicationArea = all;
                }
                field(CC_LOCATION_DESCRIPTION; CC_LOCATION_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(CATEGORY_CODE; CATEGORY_CODE)
                {
                    ApplicationArea = all;
                }
                field(CATEGORY_DESCRIPTION; CATEGORY_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(SUB_CATEGORY_CODE; SUB_CATEGORY_CODE)
                {
                    ApplicationArea = all;
                }
                field(SUB_CATEGORY_DESCRIPTION; SUB_CATEGORY_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(CUSTODIAN_ID; CUSTODIAN_ID)
                {
                    ApplicationArea = all;
                }
                field(MAIN_ASSET_COMPONENT_ASSET; MAIN_ASSET_COMPONENT_ASSET)
                {
                    ApplicationArea = all;
                }
                field(COMPONENT_OF_MAIN_ASSET_NO; COMPONENT_OF_MAIN_ASSET_NO)
                {
                    ApplicationArea = all;
                }
                field(ACQUISITION_DATE; ACQUISITION_DATE)
                {
                    ApplicationArea = all;
                }
                field(INVOICE_NUMBER; INVOICE_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(INVOICE_VALUE; INVOICE_VALUE)
                {
                    ApplicationArea = all;
                }
                field(SERIAL_NUMBER; SERIAL_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(STATUS; STATUS)
                {
                    ApplicationArea = all;
                }
                field(NAVISION_UPDATED_TIME; NAVISION_UPDATED_TIME)
                {
                    ApplicationArea = all;
                }
                field(NAVISION_UPDATED_USER; NAVISION_UPDATED_USER)
                {
                    ApplicationArea = all;
                }
                field(BLOCKED; BLOCKED)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

