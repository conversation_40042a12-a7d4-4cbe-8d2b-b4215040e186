page 50694 "CHI POS"
{
    // version ExistingErr,GJ_CHI_POS_1.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // CRF:2019-0060 NYO   06-JUL-19  -> Code added at Form - OnAfterGetCurrRecord()
    // Bug Fix       RKD   15-08-19   -> Code added in Post - OnPush()

    PageType = Document;
    SourceTable = "Sales Header";
    SourceTableView = SORTING("Document Type", "No.") WHERE("Document Type" = FILTER(Order), "POS Window" = CONST(true), Status = FILTER(Open | Released), "Order Status" = FILTER(<> Cancelled & <> "Short Closed"), "Order Tracking" = filter(<> "Completely Shipped"));
    layout
    {
        area(content)
        {
            group(General)
            {
                field("No."; "No.")
                {
                    Caption = 'Invoice No.';
                    Style = Strong;
                    StyleExpr = TRUE;

                    trigger OnAssistEdit();
                    begin
                        IF AssistEdit(xRec) THEN
                            CurrPage.UPDATE;
                    end;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                    Visible = false;
                }
                field("Sell-to Customer No."; "Sell-to Customer No.")
                {
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                }
                field("Posting Date"; "Posting Date")
                {
                    Style = Strong;
                    StyleExpr = TRUE;
                    trigger Onvalidate()

                    var
                        UserSetup: Record "User Setup";
                        CReCeL: Record "Customer Resp. Cent. Lines";
                        POSSetup: Record "UserID Resp. Cent. Lines";
                        RespCenter: Record "Responsibility Center";
                    begin
                        UserSetup.Get(UserId);
                        "User Wise Resp Centr" := UserSetup."Sales Resp. Ctr. Filter";
                        //VALIDATE("Responsibility Center", UserSetup."Sales Resp. Ctr. Filter");
                        CReCeL.RESET;
                        CReCeL.SETRANGE("Resp. Center Code", UserSetup."Sales Resp. Ctr. Filter");
                        //CReCeL.SETRANGE("Customer No.",CustRec."No.");
                        IF CReCeL.FINDFIRST THEN begin
                            REPEAT
                                CustRec.RESET;
                                CustRec.SETRANGE("No.", CReCeL."Customer No.");
                                CustRec.SETRANGE("POS Customer", TRUE);
                                IF CustRec.FINDFIRST THEN BEGIN
                                    VALIDATE("Sell-to Customer No.", CReCeL."Customer No.");
                                END;

                            UNTIL (CReCeL.NEXT = 0) or ("Sell-to Customer No." <> '');

                        end;
                        VALIDATE("Location Code", UserSetup.Location);
                        IF POSSetup.GET(UserSetup.Location) THEN;
                        //VALIDATE("Responsibility Center",POSSetup."Resp. Center Code");
                        VALIDATE("Salesperson Code", UserSetup."Salespers./Purch. Code");
                        VALIDATE("Ship-to Code", '1');
                        "Posting Date" := Today;
                        RespCenter.Get("Responsibility Center");
                        Validate("Shortcut Dimension 1 Code", RespCenter."Global Dimension 1 Code");
                        Validate("Shortcut Dimension 2 Code", RespCenter."Global Dimension 2 Code");
                    end;
                }
                field("Location Code"; "Location Code")
                {
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                }
                field("Salesperson Code"; "Salesperson Code")
                {
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                    Visible = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                    Visible = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                    Visible = false;
                }

            }
            part(SalesLines; "POS Line")
            {
                SubPageLink = "Document Type" = FIELD("Document Type"), "Document No." = FIELD("No.");
            }
            group("PayMent Details")
            {
                field("External Document No."; "External Document No.")
                {
                    Caption = 'Manual Bill No.';
                    Style = Strong;
                    StyleExpr = TRUE;
                }
                field("POS Transaction Type"; "POS Transaction Type")
                {
                    Caption = 'POS Transaction Type';
                    ValuesAllowed = Cash, Card, Both;//, "Fund Transfer";

                    trigger OnValidate();
                    begin
                        IF "POS Transaction Type" = "POS Transaction Type"::"Both" THEN
                            BothPOSTransactionTypeOnValida;
                        IF ("POS Transaction Type" = "POS Transaction Type"::"Card") THEN //or ("POS Transaction Type" = "POS Transaction Type"::"Fund Transfer") THEN
                            CardPOSTransactionTypeOnValida;
                        IF "POS Transaction Type" = "POS Transaction Type"::"Cash" THEN
                            CashPOSTransactionTypeOnValida;
                        //Message('You have selected %1', Format("POS Transaction Type"));
                        CurrPage.Update();
                    end;
                }
                field("POS Transaction No."; "POS Transaction No.")
                {
                    Editable = "POS Transaction No.Editable";
                }
                field("Pos Bank Name"; "Pos Bank Name")
                {
                    Editable = "Pos Bank NameEditable";
                    OptionCaption = '" ,ZB,GTB,,,,,,,UBA,,ACCESS,,,STANBIC",,,,,,,,,Zenith Fund Transfer,Access Fund Transfer,,GTB Fund Transfer'; //RFC#40 //G2S 8300_CAS-01400-R6C8L3 //G2S 8611-CAS-01408-Q0L2S7

                    trigger OnValidate();
                    begin
                        IF NOT CONFIRM('Are you sure you want pay with this bank?', FALSE) THEN EXIT;
                    end;
                }
                field("POS Card Amount"; "POS Card Amount")
                {
                    Editable = "POS Card AmountEditable";

                    trigger OnValidate();
                    begin
                        CLEAR(Balancedue);
                        CalcFields(Total);
                        if Total <> Round(Total, 1) then begin
                            TotalV := Total;
                            Total := 0;
                            Total := Round(TotalV, 1);
                        end;
                        IF "POS Transaction Type" = "POS Transaction Type"::Card THEN
                            Balancedue := Total - ("POS Card Amount");
                        IF Balancedue < 0 THEN
                            ERROR(Text0001, ("POS Card Amount"), Total);

                        IF "POS Transaction Type" = "POS Transaction Type"::Both THEN
                            Balancedue := Total - ("POS Card Amount" + "POS Cash Amount");
                        IF Balancedue < 0 THEN
                            ERROR(Text0001, ("POS Card Amount" + "POS Cash Amount"), Total);
                        //IF "POS Transaction Type" = "POS Transaction Type"::"Fund Transfer" THEN
                        //    Balancedue := Total - ("POS Card Amount");
                        //CRF: 2019 -0096  NYO >>
                        /*IF SalesSetup.GET THEN;
                        IF Total >= SalesSetup."POS Charge Amount Limit" THEN BEGIN
                          SalesLine.RESET;
                          SalesLine.SETRANGE(SalesLine."Document Type","Document Type");
                          SalesLine.SETRANGE(SalesLine."Document No.","No.");
                          SalesLine.SETRANGE(SalesLine.Type,SalesLine.Type::"G/L Account");
                          IF SalesLine.ISEMPTY THEN BEGIN
                            SalesLine2.RESET;
                            SalesLine2.SETRANGE(SalesLine2."Document Type","Document Type");
                            SalesLine2.SETRANGE(SalesLine2."Document No.","No.");
                            IF SalesLine2.FINDLAST THEN;
                            SalesLine.INIT;
                            //SalesLine.TRANSFERFIELDS(SalesLine2);
                            SalesLine."Document Type":=SalesLine2."Document Type";
                            SalesLine."Document No.":=SalesLine2."Document No.";
                            SalesLine."Line No.":=SalesLine2."Line No."+10000;

                            SalesLine.INSERT;
                            SalesLine.VALIDATE("Sell-to Customer No.",SalesLine2."Sell-to Customer No.");
                            SalesLine.VALIDATE(SalesLine.Type,SalesLine.Type::"G/L Account");
                            SalesLine."Location Code":=SalesLine2."Location Code";
                            SalesLine."Posting Group":=SalesLine2."Posting Group";
                            SalesLine.VALIDATE(Quantity,1);
                            SalesLine.VALIDATE(SalesLine."No.",SalesSetup."POS Charge GL Account");
                            SalesLine.VALIDATE(SalesLine."VAT %",0);
                            SalesLine.VALIDATE(SalesLine."Unit Price",SalesSetup."POS Charge Amount");
                            SalesLine.MODIFY;
                          END;
                        END;

                        IF Total < SalesSetup."POS Charge Amount Limit" THEN BEGIN
                          SalesLine.RESET;
                          SalesLine.SETRANGE(SalesLine."Document Type","Document Type");
                          SalesLine.SETRANGE(SalesLine."Document No.","No.");
                          SalesLine.SETRANGE(SalesLine.Type,SalesLine.Type::"G/L Account");
                          IF SalesLine.FINDFIRST THEN
                            SalesLine.DELETE;
                        END;
                        //CRF: 2019 -0096  NYO <<
                        */

                    end;
                }
                field("POS Cash Amount"; "POS Cash Amount")
                {
                    Caption = 'Cash Amount';
                    Editable = "POS Cash AmountEditable";

                    trigger OnValidate();
                    begin
                        CLEAR(Balancedue);
                        CalcFields(Total);
                        if Total <> Round(Total, 1) then begin
                            TotalV := Total;
                            Total := 0;
                            Total := Round(TotalV, 1);
                        end;
                        IF "POS Transaction Type" = "POS Transaction Type"::Cash THEN
                            Balancedue := Total - ("POS Cash Amount");
                        IF Balancedue < 0 THEN
                            ERROR(Text0001, ("POS Cash Amount"), Total);

                        IF "POS Transaction Type" = "POS Transaction Type"::Both THEN
                            Balancedue := Total - ("POS Card Amount" + "POS Cash Amount");
                        IF Balancedue < 0 THEN
                            ERROR(Text0001, ("POS Card Amount" + "POS Cash Amount"), Total);
                    end;
                }
                field(Total; Total)
                {
                    Caption = 'Total';
                    Editable = false;
                    Style = Strong;
                    StyleExpr = TRUE;
                }
                field(Balancedue; Balancedue)
                {
                    Caption = 'Balance Due';
                    Editable = false;
                    Style = Unfavorable;
                    StyleExpr = TRUE;
                }
            }
        }

    }

    actions
    {
        area(processing)
        {
            action(Post)
            {
                Caption = 'P&ost';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                var
                    SalesHdr4: Record "Sales Header";
                    ReservRec: Record "Reservation Entry";
                    Loccode: Code[20];
                    DocType: Option Quote,"Order",Invoice,"Credit Memo","Blanket Order","Return Order";
                    QtyBase: Decimal;
                    ItemUOM: Record "Item Unit of Measure";
                    SalesShipMent: Record "Sales Shipment Header";
                    SaleRecSetup: Record "Sales & Receivables Setup";
                    SalesInvoiceHdr: Record "Sales Invoice Header";
                    SalesInvoiceLine: Record "Sales Invoice Line";
                    SalesShipmentheader: Record "Sales Shipment Header";
                    SalesShipmentLine: Record "Sales Shipment Line";
                    GenJnlLine: Record "Gen. Journal Line";
                    GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
                    BankAccountLedgerEntries: Record "Bank Account Ledger Entry";
                begin
                    CurrPage.SalesLines.PAGE.CheckMixLot();
                    CalcFields(Total);
                    TESTFIELD("Location Code");
                    TESTFIELD("Document Type");
                    TESTFIELD("No.");
                    BillNo := "No.";
                    Loccode := "Location Code";
                    DocType := "Document Type";
                    CustN := "Sell-to Customer No.";
                    TotalV := Total;
                    Shot1 := "Shortcut Dimension 1 Code";
                    Shot2 := "Shortcut Dimension 2 Code";
                    //CODEUNIT.RUN(CODEUNIT::"Sales-Post (Yes/No)",Rec);
                    //IF ApprovalMgt.PrePostApprovalCheck(Rec,PurchaseHeader) THEN

                    //To check for item tracking before posting
                    SaleRecSetup.Get();
                    CLEAR(SalesAmt);
                    SalesLine.RESET;
                    SalesLine.SETRANGE("Document No.", "No.");
                    IF SalesLine.FINDSET THEN BEGIN
                        REPEAT
                            if Not SalesLine."Gift Item" then
                                SalesLine.TestField("Unit Price");//PKONJ17
                            SalesLine.SetItemTracking();
                            SalesAmt += SalesLine."Amount Including VAT";
                            IF ItemRec.GET(SalesLine."No.") THEN BEGIN
                                IF ItemRec."Item Tracking Code" <> '' THEN BEGIN
                                    CLEAR(QtyBase);
                                    /*ReservRec.RESET;
                                    ReservRec.SETCURRENTKEY("Item No.", "Source ID", "Source Subtype", "Source Type", "Location Code");
                                    ReservRec.SETRANGE("Location Code", Loccode);
                                    ReservRec.SETRANGE("Source Type", 37);
                                    ReservRec.SETRANGE("Source Subtype", DocType);
                                    ReservRec.SETRANGE("Source ID", BillNo);
                                    ReservRec.SETRANGE("Item No.", ItemRec."No.");
                                    ReservRec.SETRANGE("Source Ref. No.", SalesLine."Line No.");//Bug Fix RKD 15-08-19
                                    IF ReservRec.FINDSET THEN
                                        REPEAT
                                            QtyBase += ReservRec."Quantity (Base)";
                                        UNTIL ReservRec.NEXT = 0;
                                    //ReservRec.CALCSUMS("Quantity (Base)");
                                    IF ItemUOM.GET(SalesLine."No.", SalesLine."Unit of Measure") THEN
                                        IF QtyBase <> (-SalesLine.Quantity * ItemUOM."Qty. per Unit of Measure") THEN
                                            ERROR(Text0002, SalesLine."Line No.");*/

                                END;
                                //END;
                                //FIX15Jun2021>>
                                if SalesLine."Qty. to Ship" = 0 then begin
                                    SalesLine."Qty. to Ship" := SalesLine.Quantity - SalesLine."Quantity Shipped";
                                    SalesLine."Qty. to Ship (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Invoiced (Base)";
                                    if SaleRecSetup."Pos Ship & Invoice" then begin
                                        SalesLine."Qty. to Invoice" := SalesLine.Quantity - SalesLine."Quantity Invoiced";
                                        SalesLine."Qty. to Invoice (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Invoiced (Base)";
                                    end;
                                    //FIX15Jun2021<<
                                    SalesLine.Modify();
                                end;
                            END;
                        UNTIL SalesLine.NEXT = 0;
                        SalesAmt := ROUND(SalesAmt, 1);
                    END;



                    //<<< NYO

                    TESTFIELD("POS Transaction Type");
                    IF "POS Transaction Type" = "POS Transaction Type"::Cash THEN BEGIN
                        TESTFIELD("POS Cash Amount");
                        TESTFIELD("POS Card Amount", 0);
                        IF ROUND("POS Cash Amount", 1) <> SalesAmt THEN
                            ERROR(Text0001, "POS Cash Amount", SalesAmt);

                    END;

                    IF ("POS Transaction Type" = "POS Transaction Type"::Card) THEN BEGIN //or ("POS Transaction Type" = "POS Transaction Type"::"Fund Transfer") THEN BEGIN
                        TESTFIELD("POS Transaction No.");
                        TESTFIELD("Pos Bank Name");
                        TESTFIELD("POS Card Amount");
                        IF NOT CONFIRM('Are you sure you want pay with this bank?', FALSE) THEN EXIT;
                        TESTFIELD("POS Cash Amount", 0);
                        IF ROUND("POS Card Amount", 1) <> SalesAmt THEN
                            ERROR(Text0001, "POS Card Amount", SalesAmt);


                    END;

                    IF "POS Transaction Type" = "POS Transaction Type"::Both THEN BEGIN
                        TESTFIELD("POS Transaction No.");
                        TESTFIELD("POS Card Amount");
                        TESTFIELD("Pos Bank Name");
                        IF NOT CONFIRM('Are you sure you want pay with this bank?', FALSE) THEN EXIT;
                        TESTFIELD("POS Cash Amount");
                        IF ROUND(("POS Card Amount" + "POS Cash Amount"), 1) <> SalesAmt THEN
                            ERROR(Text0001, ("POS Card Amount" + "POS Cash Amount"), SalesAmt);


                    END;
                    //>> NYO
                    //>>NYO
                    Status := Status::Released;
                    Modify();
                    Ship := true;
                    if SaleRecSetup."Pos Ship & Invoice" then
                        Invoice := true;
                    SalesInvoiceHdr.Reset();
                    SalesInvoiceHdr.SetRange("Order No.", "No.");
                    if not SalesInvoiceHdr.FindFirst() then begin
                        TESTFIELD("Posting Date", TODAY);
                        CODEUNIT.RUN(CODEUNIT::"Sales-Post-Copy", Rec);


                        COMMIT;
                        //**************Delete Reservation Entry****************
                        ReservRec.RESET;
                        ReservRec.SETRANGE("Location Code", Loccode);
                        ReservRec.SETRANGE("Source Type", 37);
                        ReservRec.SETRANGE("Source Subtype", DocType);
                        ReservRec.SETRANGE("Source ID", BillNo);
                        IF ReservRec.FINDFIRST THEN
                            REPEAT
                                ReservRec.DELETE;
                            UNTIL ReservRec.NEXT = 0;
                        //**************Delete Reservation Entry****************
                        SalesShipMent.Reset();
                        SalesShipMent.SetRange("Order No.", "No.");
                        if SalesShipMent.FindFirst() then
                            Report.Run(Report::"New POS Print_L2", true, false, SalesShipMent);
                    end else begin
                        repeat
                            SalesLine.RESET;
                            SalesLine.SETRANGE("Document No.", "No.");
                            IF SalesLine.FINDSET THEN BEGIN
                                REPEAT
                                    SalesLine."Qty. to Ship" := SalesLine.Quantity;
                                    SalesLine."Qty. to Ship (Base)" := SalesLine."Quantity (Base)";
                                    SalesLine."Qty. to Invoice" := SalesLine.Quantity;
                                    SalesLine."Qty. to Invoice (Base)" := SalesLine."Quantity (Base)";
                                    SalesLine.Modify();
                                UNTIL SalesLine.NEXT = 0;
                            END;
                            SalesInvoiceLine.Reset();
                            SalesInvoiceLine.SetRange("Document No.", SalesInvoiceHdr."No.");
                            SalesInvoiceLine.SetFilter("No.", '<>%1', '');
                            SalesInvoiceLine.SetFilter(Quantity, '<>%1', 0);
                            if SalesInvoiceLine.FindSet() then
                                repeat
                                    SalesLine.Reset();
                                    SalesLine.SetRange("Document No.", SalesInvoiceLine."Order No.");
                                    SalesLine.SetRange("Line No.", SalesInvoiceLine."Order Line No.");
                                    if SalesLine.FindFirst() then begin
                                        //if SalesLine.Quantity <> SalesInvoiceLine.Quantity then begin
                                        SalesLine."Qty. to Invoice" -= SalesInvoiceLine.Quantity;
                                        SalesLine."Qty. to Invoice (Base)" -= SalesInvoiceLine."Quantity (Base)";
                                        SalesLine.Modify();
                                    end;
                                until SalesInvoiceLine.Next() = 0;
                            SalesShipmentheader.Reset();
                            SalesShipmentheader.SetRange("Order No.", "No.");
                            if SalesShipmentheader.FindSet() then
                                repeat
                                    SalesShipmentLine.Reset();
                                    SalesShipmentLine.SetRange("Document No.", SalesShipmentheader."No.");
                                    SalesShipmentLine.SetFilter("No.", '<>%1', '');
                                    SalesShipmentLine.SetFilter(Quantity, '<>%1', 0);
                                    if SalesShipmentLine.FindSet() then
                                        repeat
                                            SalesLine.Reset();
                                            SalesLine.SetRange("Document No.", SalesInvoiceLine."Order No.");
                                            SalesLine.SetRange("Line No.", SalesInvoiceLine."Order Line No.");
                                            if SalesLine.FindFirst() then begin
                                                //if SalesLine.Quantity <> SalesShipmentLine.Quantity then begin
                                                SalesLine."Qty. to Ship" -= SalesShipmentLine.Quantity;
                                                SalesLine."Qty. to Ship (Base)" -= SalesShipmentLine."Quantity (Base)";
                                                SalesLine.Modify();
                                            end;
                                        until SalesInvoiceLine.Next() = 0;
                                until SalesShipmentheader.Next() = 0;
                        until SalesInvoiceHdr.Next() = 0;
                        SalesLine.Reset();
                        SalesLine.SetRange("Document No.", "No.");
                        if SalesLine.FindSet() then begin
                            repeat
                                if SalesLine."Qty. to Ship" > 0 then
                                    SalesLine.SetItemTracking()
                                else
                                    if SalesLine."Qty. to Ship" < 0 then begin
                                        SalesLine.Validate("Qty. to Ship", 0);
                                        SalesLine.Modify();
                                    end;

                            until SalesLine.Next() = 0;
                            SalesLine.CalcSums("Qty. to Invoice");
                            if SalesLine."Qty. to Invoice" > 0 then begin
                                Ship := true;
                                if SaleRecSetup."Pos Ship & Invoice" then
                                    Invoice := true;
                                CODEUNIT.RUN(CODEUNIT::"Sales-Post-Copy", Rec);
                            end;
                        end;
                        SalesInvoiceHdr.Reset();
                        SalesInvoiceHdr.SetRange("Order No.", "No.");
                        if SalesInvoiceHdr.FindSet() then
                            repeat
                                SalesInvoiceHdr.CalcFields("Remaining Amount", "Amount Including VAT");
                                if SalesInvoiceHdr."Remaining Amount" > 1 then begin
                                    if SalesInvoiceHdr."POS Transaction Type" = SalesInvoiceHdr."POS Transaction Type"::Cash then
                                        PostCashAmount(SalesInvoiceHdr, SalesInvoiceHdr."Remaining Amount");
                                    if SalesInvoiceHdr."POS Transaction Type" = SalesInvoiceHdr."POS Transaction Type"::Card then
                                        PostCardAmount(SalesInvoiceHdr, SalesInvoiceHdr."Remaining Amount");
                                    if SalesInvoiceHdr."POS Transaction Type" = SalesInvoiceHdr."POS Transaction Type"::Both then begin
                                        if SalesInvoiceHdr."Remaining Amount" = SalesInvoiceHdr."Amount Including VAT" then begin
                                            PostCashAmount(SalesInvoiceHdr, SalesInvoiceHdr."POS Cash Amount");
                                            PostCardAmount(SalesInvoiceHdr, SalesInvoiceHdr."POS Card Amount");
                                        end else begin
                                            BankAccountLedgerEntries.Reset();
                                            BankAccountLedgerEntries.SetRange("Bank Account No.", SalesInvoiceHdr."Bal. Account No.");
                                            BankAccountLedgerEntries.SetRange("Document No.", SalesInvoiceHdr."No.");
                                            if not BankAccountLedgerEntries.FindFirst() then
                                                PostCashAmount(SalesInvoiceHdr, SalesInvoiceHdr."POS Cash Amount")
                                            else begin
                                                BankAccountLedgerEntries.Reset();
                                                BankAccountLedgerEntries.SetRange("Bank Account No.", SalesInvoiceHdr."POS Account No.");
                                                BankAccountLedgerEntries.SetRange("Document No.", SalesInvoiceHdr."No.");
                                                if not BankAccountLedgerEntries.FindFirst() then
                                                    PostCardAmount(SalesInvoiceHdr, SalesInvoiceHdr."POS Card Amount");
                                            end;
                                        end;
                                    end;
                                end;
                            until SalesInvoiceHdr.Next() = 0;
                        Commit();
                        SalesShipMent.Reset();
                        SalesShipMent.SetRange("Order No.", "No.");
                        if SalesShipMent.FindFirst() then
                            Report.Run(Report::"New POS Print_L2", true, false, SalesShipMent);
                        SalesLine.Reset();
                        SalesLine.SetRange("Document No.", "No.");
                        if SalesLine.FindSet() then
                            SalesLine.DeleteAll();
                        Rec.Delete();
                    end;
                    /*
                    IF NOT Processed  THEN BEGIN
                      IF CONFIRM('Do you want to book the Sale for bill No. %1?',FALSE,"Pos Entry No.") THEN BEGIN
                        SalesHdr.INIT;
                        SalesHdr.VALIDATE("Document Type",SalesHdr."Document Type"::Invoice);
                        SalesHdr.VALIDATE("No.","Pos Entry No.");
                        SalesHdr.VALIDATE("Sell-to Customer No.","Customer No.");
                        SalesHdr.VALIDATE("Order Date","Posting Date");
                        SalesHdr.VALIDATE("Posting Date","Posting Date");
                        SalesHdr.VALIDATE("Shipment Date","Posting Date");
                        SalesHdr.VALIDATE("Location Code",Location);
                        SalesHdr.VALIDATE("Salesperson Code","Sales Person");
                        SalesHdr.INSERT(TRUE);
                        SalesHdr2.RESET;
                        SalesHdr2.SETRANGE("Document Type",SalesHdr2."Document Type"::Invoice);
                        SalesHdr2.SETRANGE("No.","Pos Entry No.");
                        IF SalesHdr2.FINDFIRST THEN BEGIN
                          IF POSSetup.GET(Location) THEN;
                          SalesHdr2.VALIDATE("Shortcut Dimension 1 Code",POSSetup."Shortcut Dimension 1 Code");
                          SalesHdr2.VALIDATE("Shortcut Dimension 2 Code",POSSetup."Shortcut Dimension 2 Code");
                          SalesHdr2.MODIFY;
                        END;
                        POSL.RESET;
                        POSL.SETRANGE(POSL."Pos Entry No.","Pos Entry No.");
                        IF POSL.FINDFIRST THEN REPEAT
                          SalesLine.INIT;
                          SalesLine.VALIDATE("Document Type",SalesLine."Document Type"::Invoice);
                          SalesLine.VALIDATE("Document No.",POSL."Pos Entry No.");
                          SalesLine.VALIDATE("Line No.",POSL."Line No.");
                          SalesLine.VALIDATE("Sell-to Customer No.","Customer No.");
                          SalesLine.VALIDATE(Type,SalesLine.Type::Item);
                          SalesLine.VALIDATE("No.",POSL."Item No.");
                          SalesLine.VALIDATE("Location Code",Location);
                          SalesLine.VALIDATE(Quantity,POSL.Quantity);
                          SalesLine.VALIDATE("Unit Price",POSL."Unit Price");
                          SalesLine.VALIDATE("Line Discount %",POSL.Discount);
                          SalesLine.INSERT(TRUE);
                          //Lot No. Tracking >>>
                           IF ItemRec.GET(POSL."Item No.") THEN;
                             IF ItemRec."Item Tracking Code" <> '' THEN BEGIN
                               IF ItemRec."Lot Nos." <> '' THEN BEGIN
                    
                               END ELSE IF ItemRec."Serial Nos." <>'' THEN BEGIN
                               END;
                             END;
                          //Lot No. Tracking <<<
                        UNTIL POSL.NEXT = 0;
                        SalesHdr3.RESET;
                        SalesHdr3.SETRANGE("Document Type",SalesHdr3."Document Type"::Invoice);
                        SalesHdr3.SETRANGE("No.","Pos Entry No.");
                        IF SalesHdr3.FINDFIRST THEN
                    
                    
                        SalesHdr3.RESET;
                        SalesHdr3.SETRANGE("Document Type",SalesHdr3."Document Type"::Invoice);
                        SalesHdr3.SETRANGE("No.","Pos Entry No.");
                        IF NOT SalesHdr3.FINDFIRST THEN BEGIN
                            Processed := TRUE;
                            MODIFY;
                        END ELSE BEGIN
                          SalesHdr3.DELETEALL;
                        END;
                         END ELSE BEGIN
                           SalesHdr3.RESET;
                           SalesHdr3.SETRANGE("Document Type",SalesHdr3."Document Type"::Invoice);
                           SalesHdr3.SETRANGE("No.","Pos Entry No.");
                           IF SalesHdr3.FINDFIRST THEN
                             SalesHdr3.DELETEALL;
                         END;
                      END;
                    END;
                    */
                    /*CLEAR(ReturnCardNo);
                    Cashcard := STRMENU('Cash,Card',1,'Select Payment Type');
                    IF Cashcard =  Cashcard::Card THEN BEGIN
                      // New code >>
                    
                       //Selection := STRMENU(Text000);
                    
                      //New code <<
                      IF ReturnCardNo = '' THEN
                        Window.OPEN ('Please Enter Debit / Credit Card No. \'+
                        'Press the cancel button to stop the Search\\'+
                        'Credit / Debit Card No. = #1##################\'+
                        ReturnCardNo);
                    
                      //Wait for the fields to be changed or Entered
                      Window.INPUT(1,ReturnCardNo);
                    
                      //Test Values
                      IF ReturnCardNo='' THEN
                        ERROR('Please Enter Valid Card No.');
                      SalesHdr4.RESET;
                      SalesHdr4.SETRANGE("Document Type","Document Type");
                      SalesHdr4.SETRANGE("No.","No.");
                      IF SalesHdr4.FINDFIRST THEN BEGIN
                        SalesHdr4."POS Transaction Type" := Cashcard;
                        SalesHdr4."POS Transaction No." := ReturnCardNo;
                    
                        // Old code >>
                        IF PayMethod.GET("Payment Method Code") THEN
                          SalesHdr4."Bal. Account No." := PayMethod."Credit Card Account POS(Zenth)";
                          SalesHdr4.MODIFY;
                        END;
                        // Old code <<
                        // New Code >>
                        {IF PayMethod.GET("Payment Method Code")  THEN BEGIN
                            IF Selection = 1 THEN BEGIN
                               PayMethod.TESTFIELD("Credit Card Account POS(Zenth)");
                               SalesHdr4."Bal. Account No.":=PayMethod."Credit Card Account POS(Zenth)";
                            END;
                            IF Selection = 2 THEN BEGIN
                               PayMethod.TESTFIELD("Credit Card Account POS(UBA)");
                               SalesHdr4."Bal. Account No.":=PayMethod."Credit Card Account POS(UBA)";
                               END;
                            IF Selection = 3 THEN BEGIN
                               PayMethod.TESTFIELD("Credit Card Account POS(City)");
                               SalesHdr4."Bal. Account No.":=PayMethod."Credit Card Account POS(City)";
                               END;
                            SalesHdr4.MODIFY;
                            end;
                          }
                        //New Code <<
                        SalesHdr.RESET;
                        SalesHdr.SETRANGE("Document Type","Document Type");
                        SalesHdr.SETRANGE("No.","No.");
                        IF SalesHdr.FINDFIRST THEN
                          CODEUNIT.RUN(CODEUNIT::"Sales-Post + Print",SalesHdr);
                    END ELSE*/

                    /*
                    IF Val THEN BEGIN
                      GenJrnlLine.INIT;
                      GenJrnlLine.VALIDATE("Journal Template Name",'Sales');
                      GenJrnlLine.VALIDATE("Journal Batch Name",'CHI');
                      GenJrnlLine.VALIDATE("Document No.",BillNo);
                      GenJrnlLine.VALIDATE("Line No.",10000);
                      GenJrnlLine.VALIDATE("Account Type",GenJrnlLine."Account Type"::"G/L Account");
                      IF CustRec.GET(CustN) THEN;
                        IF CustPostGrp.GET(CustRec."Customer Posting Group") THEN;
                      GenJrnlLine.VALIDATE("Account No.",CustPostGrp."Receivables Account");
                      GenJrnlLine.VALIDATE("Posting Date",TODAY);
                      GenJrnlLine.VALIDATE("Document Type",GenJrnlLine."Document Type"::Payment);
                      GenJrnlLine.VALIDATE(Description,'Retail Sale');
                      GenJrnlLine.VALIDATE(Amount,TotalV);
                      GenJrnlLine.VALIDATE("Shortcut Dimension 1 Code",Shot1);
                      GenJrnlLine.VALIDATE("Shortcut Dimension 2 Code",Shot2);
                      GenJrnlLine.VALIDATE("Bal. Account Type",GenJrnlLine."Bal. Account Type"::Customer);
                      GenJrnlLine.VALIDATE("Bal. Account No.",CustN);
                      GenJrnlLine.INSERT;
                    END;
                    GenJrnl.RESET;
                    GenJrnl.SETRANGE("Journal Template Name",'Sales');
                    GenJrnl.SETRANGE("Journal Batch Name",'CHI');
                    IF GenJrnl.FINDFIRST THEN
                      CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post",GenJrnl);
                    
                    COMMIT;
                    
                    SLHdr.RESET;
                    SLHdr.SETRANGE("Pre-Assigned No.",BillNo);
                    IF SLHdr.FINDFIRST THEN
                      REPORT.RUNMODAL(50025,FALSE,FALSE,SLHdr);
                    */

                end;
            }
            action(POstPrint)
            {
                Caption = 'P&rint';
                Enabled = false;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    IF CONFIRM('Do you want to Print Bill No. %1?', FALSE, "No.") THEN
                        SalesHdr.RESET;
                    SalesHdr.SETRANGE("Document Type", "Document Type");
                    SalesHdr.SETRANGE("No.", "No.");
                    IF SalesHdr.FINDFIRST THEN
                        REPORT.RUNMODAL(50033, TRUE, FALSE, SalesHdr);
                end;
            }
            action(Refresh)
            {
                Caption = 'Refresh';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    CurrPage.Update();
                end;
            }
            action(ReOpen)
            {
                Caption = 'Re-Open';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Status := Status::Open;
                    Modify();
                end;
            }
            action(Cancel)
            {
                Caption = 'Cancel';
                Promoted = true;
                PromotedCategory = Process;
                Image = Cancel;
                trigger OnAction();
                begin
                    Status := Status::Open;
                    Modify();
                    Rec.Delete(true);
                end;
            }

            action(PostReturn)
            {
                Caption = 'R&eturn';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    CLEAR(SLForm);
                    IF CONFIRM('Do you want to process Return?', FALSE, "No.") THEN BEGIN
                        Window.OPEN('Press Enter to Accept\' +
                        'Press the cancel button to stop the Search\\' +
                        'POS Bill No. #1#########\' +
                        ReturnBillNo);

                        //Wait for the fields to be changed or Entered
                        //Window.INPUT(1,ReturnBillNo);CHI 9.0

                        //Test Values
                        IF ReturnBillNo = '' THEN
                            ERROR('Message');

                        CreditMemoChecking(ReturnBillNo);
                        // Give then a second chance to cancel
                        IF NOT CONFIRM('Are you sure you want to search Bill No. %1', FALSE, ReturnBillNo) THEN
                            ERROR('')
                        ELSE BEGIN
                            Window.CLOSE;
                            SLHdr.RESET;
                            //SLHdr.SETRANGE("Pre-Assigned No.",ReturnBillNo);
                            SLHdr.SETRANGE("No.", ReturnBillNo);
                            IF SLHdr.FINDFIRST THEN
                                SLLine.RESET;
                            SLLine.SETRANGE("Document No.", SLHdr."No.");
                            IF SLLine.FINDFIRST THEN BEGIN
                                SLForm.SFilter(ReturnBillNo);
                                SLForm.SETTABLEVIEW(SLLine);
                                SLForm.RUNMODAL;
                            END;
                        END;
                    END;
                end;
            }
            action(PostMixLot)
            {
                Caption = '&Mix Lot';
                Promoted = true;
                PromotedCategory = Process;
                Visible = false;
                trigger OnAction();
                begin
                    CurrPage.SalesLines.PAGE.CheckMixLot();
                end;
            }
            action(History)
            {
                Caption = 'Canc. &History';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Window.OPEN('Press Enter to Accept\' +
                    'Press the cancel button to stop the Search\\' +
                    'POS Bill No. #1#########\' +
                    BillNo);

                    //Wait for the fields to be changed or Entered
                    //Window.INPUT(1,BillNo);CHI 9.0

                    //Test Values
                    IF BillNo = '' THEN
                        ERROR('Message');

                    // Give then a second chance to cancel
                    IF NOT CONFIRM('Are you sure you want to search Bill No. %1', FALSE, BillNo) THEN
                        ERROR('')
                    ELSE BEGIN
                        POSArch.RESET;
                        POSArch.SETRANGE(Code, BillNo);
                        IF POSArch.FINDFIRST THEN;
                        //PAGE.RUN(PAGE::"POS Header Archive_L", POSArch);
                    END;
                    Window.CLOSE;
                end;
            }
            action("Update Promo")
            {
                Caption = '&Update Promo';
                Promoted = true;
                PromotedCategory = Process;
                trigger OnAction();
                begin
                    UpdatePromotionLines(0);
                    CurrPage.UPDATE;

                end;
            }
            action("Change Gift Flavour")
            {
                Caption = 'Chng.&Gift Flavr';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    IF CONFIRM('Do you want to change the gift flavour?', FALSE) THEN
                        CurrPage.SalesLines.PAGE.ChangeGiftFlavr;
                end;
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        "POS Window" := TRUE;
        CLEAR(TotalV);
        CalcFields(Total);
        Balancedue := 0;
        IF "POS Transaction Type" = "POS Transaction Type"::Card THEN
            Balancedue := Total - ("POS Card Amount");
        IF Balancedue < 0 THEN
            // ERROR(Text0001,("POS Card Amount"),Total);

            IF "POS Transaction Type" = "POS Transaction Type"::Cash THEN
                Balancedue := Total - ("POS Cash Amount");
        IF Balancedue < 0 THEN
            // ERROR(Text0001,("POS Cash Amount"),Total);

            IF "POS Transaction Type" = "POS Transaction Type"::Both THEN
                Balancedue := Total - ("POS Card Amount" + "POS Cash Amount");
        IF Balancedue < 0 THEN
            // ERROR(Text0001,("POS Card Amount"+"POS Cash Amount"),Total);
        if Total <> Round(Total, 1) then begin
                TotalV := Total;
                Total := 0;
                Total := Round(TotalV, 1);
            end;
        GetBalance;
        OnAfterGetCurrRecord;

    end;

    trigger OnClosePage();
    begin
        /*
        CREATE(WindowsShell);
        WindowsShell.SendKeys('%{F1}');
        CLEAR(WindowsShell);*///CHI 9.0
    end;

    trigger OnInit();
    begin
        "POS Cash AmountEditable" := TRUE;
        "POS Transaction No.Editable" := TRUE;
        "Pos Bank NameEditable" := TRUE;
        "POS Card AmountEditable" := TRUE;
    end;

    trigger OnInsertRecord(BelowxRec: Boolean): Boolean;
    var
        UserSetup: Record "User Setup";
        CReCeL: Record "Customer Resp. Cent. Lines";
        POSSetup: Record "UserID Resp. Cent. Lines";
    begin
        "POS Window" := TRUE;
        "User ID" := USERID;

        IF "POS Window" THEN BEGIN
            //"Posting Date" := WORKDATE;

            UserSetup.RESET;
            UserSetup.SETRANGE("User ID", USERID);
            IF UserSetup.FINDFIRST THEN;
            IF "No." <> '' THEN BEGIN
                "User Wise Resp Centr" := UserSetup."Sales Resp. Ctr. Filter";
                //VALIDATE("Responsibility Center", UserSetup."Sales Resp. Ctr. Filter");
                CReCeL.RESET;
                CReCeL.SETRANGE("Resp. Center Code", UserSetup."Sales Resp. Ctr. Filter");
                //CReCeL.SETRANGE("Customer No.",CustRec."No.");
                IF CReCeL.FINDFIRST THEN begin
                    REPEAT
                        CustRec.RESET;
                        CustRec.SETRANGE("No.", CReCeL."Customer No.");
                        CustRec.SETRANGE("POS Customer", TRUE);
                        IF CustRec.FINDFIRST THEN BEGIN
                            VALIDATE("Sell-to Customer No.", CReCeL."Customer No.");
                        END;

                    UNTIL (CReCeL.NEXT = 0) or ("Sell-to Customer No." <> '');
                    VALIDATE("Location Code", UserSetup.Location);
                    IF POSSetup.GET(UserSetup.Location) THEN;
                    //VALIDATE("Responsibility Center",POSSetup."Resp. Center Code");
                    VALIDATE("Salesperson Code", UserSetup."Salespers./Purch. Code");
                    VALIDATE("Ship-to Code", '1');
                end;

            END;
        END;
        //GJ_CHIPOS_RKD_111013 <<<
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    var
        NoSeriesMgt: Codeunit NoSeriesManagement;
    begin
        IF "No." = '' THEN BEGIN
            TestNoSeries;
            NoSeriesMgt.InitSeries(GetNoSeriesCode, xRec."No. Series", "Posting Date", "No.", "No. Series");
        END;
        "POS Transaction Type" := "POS Transaction Type"::Cash;

        IF "POS Transaction Type" = "POS Transaction Type"::Cash THEN BEGIN
            "POS Card AmountEditable" := FALSE;
            "Pos Bank NameEditable" := FALSE;
            "POS Transaction No.Editable" := FALSE;
            "POS Cash AmountEditable" := TRUE;
        END;
        OnAfterGetCurrRecord;
    end;

    trigger OnOpenPage();
    begin
        //SETRANGE("User ID",USERID);

        /*CREATE(WindowsShell);
        WindowsShell.SendKeys('%{F1}');
        CLEAR(WindowsShell);*///CHI 9.0
                              //Windows Password Connect
                              /*
                              Window.OPEN ('User ID :'+USERID+'\'+'Password #1#########\'+
                              PassText);
                              Window.INPUT(1,PassText);
                              Window.UPDATE(1,'***********');
                              IF USetup.GET(USERID) THEN
                                IF (PassText <> USetup.Password) THEN
                                  ERROR('Wrong Password, Kindly Enter the Right Password')
                              ELSE
                              //Wait for the fields to be changed or Entered
                              Window.CLOSE;
                              */
                              //Windows Password Connect
        "POS Window" := TRUE;
        IF Compinfo.GET THEN
            Compinfo.CALCFIELDS("Picture");

        IF "POS Transaction Type" = "POS Transaction Type"::Cash THEN BEGIN
            "POS Card AmountEditable" := FALSE;
            "Pos Bank NameEditable" := FALSE;
            "POS Transaction No.Editable" := FALSE;
        END;

        GetBalance;

    end;

    var
        POSArch: Record "SKU Units";
        //POSLArch : Record "POS Front Line Archive_L";
        SalesHdr: Record "Sales Header";
        SalesHdr2: Record "Sales Header";
        SalesHdr3: Record "Sales Header";
        SalesLine: Record "Sales Line";
        ItemRec: Record Item;
        SLHdr: Record "Sales Invoice Header";
        SLLine: Record "Sales Invoice Line";
        SLForm: Page "Posted Invoices - POS_L";
        USetup: Record "User Setup";
        ResrvRec: Record "Reservation Entry";
        LResrvRec: Record "Reservation Entry";
        LResrvRec1: Record "Reservation Entry";
        ILE: Record "Item Ledger Entry";
        //WindowsShell : Automation "'{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0:'{72C24DD5-D70A-438B-8A42-98424B88AFB8}':''{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}' 1.0'.WshShell";
        Window: Dialog;
        BillNo: Code[20];
        ReturnBillNo: Code[20];
        TotalV: Decimal;
        Val: Boolean;
        PassText: Text[100];
        PassText2: Integer;
        ApprovalMgt: Codeunit 1535;
        PurchaseHeader: Record "Purchase Header";
        GenJrnlLine: Record "Gen. Journal Line";
        GenJrnl: Record "Gen. Journal Line";
        CustRec: Record Customer;
        CustPostGrp: Record "Customer Posting Group";
        Shot1: Code[20];
        Shot2: Code[20];
        CustN: Code[20];
        //Calendar : Page "Calendar AssistEdit";
        Compinfo: Record "Company Information";
        Cashcard: Option Cash,Card;
        PayMethod: Record "Payment Method";
        ReturnCardNo: Code[20];
        UserRec: Record "User Setup";
        Text000: Label '&Zenith,&UBA,&CITY';
        Text0001: Label 'The Amount paid %1 is not equal to the total order amount %2';
        Balancedue: Decimal;
        Text0002: Label 'The Qty Base on the tracking Lines is less than the Qty on the sales line %1';
        SalesAmt: Decimal;
        SalesSetup: Record "Sales & Receivables Setup";
        SalesLine2: Record "Sales Line";
        [InDataSet]
        "POS Card AmountEditable": Boolean;
        [InDataSet]
        "Pos Bank NameEditable": Boolean;
        [InDataSet]
        "POS Transaction No.Editable": Boolean;
        [InDataSet]
        "POS Cash AmountEditable": Boolean;
        Text19065715: Label 'POS Detail';
        Text19024374: Label '2013 Q Tropical General Investment. All Rights Reserved';
        Text19038853: Label 'POS Control';

    procedure CreditMemoChecking(ExrenalDocNo: Code[20]);
    var
        lSalesHdr: Record "Sales Header";
        lPCreditMemoHdr: Record "Sales Cr.Memo Header";
    begin
        lSalesHdr.SETRANGE(lSalesHdr."Document Type", lSalesHdr."Document Type"::"Credit Memo");
        lSalesHdr.SETRANGE(lSalesHdr."External Document No.", ExrenalDocNo);
        IF lSalesHdr.FINDFIRST THEN
            ERROR('Credit Memo has already created, Credit Memo No. : ' + lSalesHdr."No.");

        lPCreditMemoHdr.SETRANGE(lPCreditMemoHdr."External Document No.", ExrenalDocNo);
        IF lPCreditMemoHdr.FINDFIRST THEN
            ERROR('Credit Memo has already posted, Posted Credit Memo No. : ' + lPCreditMemoHdr."No.");
    end;

    procedure GetBalance();
    begin
        Balancedue := 0;
        CalcFields(Total);
        if Total <> Round(Total, 1) then begin
            TotalV := Total;
            Total := 0;
            Total := Round(TotalV, 1);
        end;
        CASE "POS Transaction Type" OF
            "POS Transaction Type"::Card:
                BEGIN
                    Balancedue := Total - ("POS Card Amount");
                END;
            "POS Transaction Type"::Cash:
                BEGIN
                    Balancedue := Total - ("POS Cash Amount");
                END;
            "POS Transaction Type"::Both:
                BEGIN
                    Balancedue := Total - ("POS Card Amount" + "POS Cash Amount");
                END;
        END;
    end;

    local procedure CashPOSTransactionTypeOnAfterV();
    begin
        GetBalance;
    end;

    local procedure CardPOSTransactionTypeOnAfterV();
    begin
        GetBalance;
    end;

    local procedure BothPOSTransactionTypeOnAfterV();
    begin
        GetBalance;
    end;

    local procedure OnAfterGetCurrRecord();
    begin
        xRec := Rec;
        SalesLine.RESET;
        SalesLine.SETRANGE("Document Type", "Document Type");
        SalesLine.SETRANGE("Document No.", "No.");
        IF SalesLine.FINDFIRST THEN
            REPEAT
                TotalV += SalesLine."Amount Including VAT";
            UNTIL SalesLine.NEXT = 0;
        //Total := TotalV;
        //Total := ROUND(TotalV, 1);    //CRF":2019-0060 NYO 06-07-19
        IF UserRec.GET(USERID) THEN
            IF UserRec."Sales Resp. Ctr. Filter" <> '' THEN
                SETRANGE("Responsibility Center", UserRec."Sales Resp. Ctr. Filter");
        CalcFields(Total);
        GetBalance;
    end;

    local procedure CashPOSTransactionTypeOnValida();
    begin
        CLEAR("POS Card Amount");
        CLEAR("Pos Bank Name");
        CLEAR("POS Transaction No.");
        CLEAR(Balancedue);

        IF "POS Transaction Type" = "POS Transaction Type"::Cash THEN BEGIN
            "POS Card AmountEditable" := FALSE;
            "Pos Bank NameEditable" := FALSE;
            "POS Transaction No.Editable" := FALSE;
            "POS Cash AmountEditable" := TRUE;
        END;
        CashPOSTransactionTypeOnAfterV;
        CurrPage.Update(true);
    end;

    local procedure CardPOSTransactionTypeOnValida();
    begin
        CLEAR("POS Cash Amount");
        CLEAR("Pos Bank Name");
        CLEAR("POS Transaction No.");

        IF ("POS Transaction Type" = "POS Transaction Type"::Card) THEN BEGIN//or ("POS Transaction Type" = "POS Transaction Type"::"Fund Transfer") THEN BEGIN
            "POS Cash AmountEditable" := FALSE;
            "POS Card AmountEditable" := TRUE;
            "Pos Bank NameEditable" := TRUE;
            "POS Transaction No.Editable" := TRUE;
            //CRF: 2019 -0096  NYO >>
            /*IF SalesSetup.GET THEN;
            IF Total >= SalesSetup."POS Charge Amount Limit" THEN BEGIN
              SalesLine.RESET;
              SalesLine.SETRANGE(SalesLine."Document Type","Document Type");
              SalesLine.SETRANGE(SalesLine."Document No.","No.");
              SalesLine.SETRANGE(SalesLine.Type,SalesLine.Type::"G/L Account");
              IF SalesLine.ISEMPTY THEN BEGIN
                SalesLine2.RESET;
                SalesLine2.SETRANGE(SalesLine2."Document Type","Document Type");
                SalesLine2.SETRANGE(SalesLine2."Document No.","No.");
                IF SalesLine2.FINDLAST THEN;
                SalesLine.INIT;
                //SalesLine.TRANSFERFIELDS(SalesLine2);
                SalesLine."Document Type":=SalesLine2."Document Type";
                SalesLine."Document No.":=SalesLine2."Document No.";
                SalesLine."Line No.":=SalesLine2."Line No."+10000;

                SalesLine.INSERT;
                SalesLine.VALIDATE("Sell-to Customer No.",SalesLine2."Sell-to Customer No.");
                SalesLine.VALIDATE(SalesLine.Type,SalesLine.Type::"G/L Account");
                SalesLine."Location Code":=SalesLine2."Location Code";
                SalesLine."Posting Group":=SalesLine2."Posting Group";
                SalesLine.VALIDATE(Quantity,1);
                SalesLine.VALIDATE(SalesLine."No.",SalesSetup."POS Charge GL Account");
                SalesLine.VALIDATE(SalesLine."VAT %",0);
                SalesLine.VALIDATE(SalesLine."Unit Price",SalesSetup."POS Charge Amount");
                SalesLine.MODIFY;
              END;

              //CRF: 2019 -0096  NYO <<
            END;
              */
            //IF Total < SalesSetup."POS Charge Amount Limit" THEN BEGIN
            SalesLine.RESET;
            SalesLine.SETRANGE(SalesLine."Document Type", "Document Type");
            SalesLine.SETRANGE(SalesLine."Document No.", "No.");
            SalesLine.SETRANGE(SalesLine.Type, SalesLine.Type::"G/L Account");
            IF SalesLine.FINDFIRST THEN
                SalesLine.DELETE;
            //END;
        END;
        CardPOSTransactionTypeOnAfterV;
        CurrPage.Update(true);

    end;

    local procedure BothPOSTransactionTypeOnValida();
    begin
        IF "POS Transaction Type" = "POS Transaction Type"::Both THEN BEGIN
            "POS Card AmountEditable" := TRUE;
            "Pos Bank NameEditable" := TRUE;
            "POS Transaction No.Editable" := TRUE;
            "POS Cash AmountEditable" := TRUE;
        END;
        BothPOSTransactionTypeOnAfterV;
        CurrPage.Update(true);
    end;

    local procedure PostCashAmount(SalesInvoiceHdr: Record "Sales Invoice Header"; AMT: Decimal)
    var
        myInt: Integer;
        GenJnlLine: Record "Gen. Journal Line";
        GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
    begin
        with GenJnlLine do begin
            InitNewLine(
              Rec."Posting Date", Rec."Document Date", Rec."Posting Description",
              Rec."Shortcut Dimension 1 Code", Rec."Shortcut Dimension 2 Code",
              Rec."Dimension Set ID", Rec."Reason Code");

            CopyDocumentFields(0, SalesInvoiceHdr."No.", SalesInvoiceHdr."External Document No.", '', '');
            "Account Type" := "Account Type"::Customer;
            "Account No." := "Bill-to Customer No.";
            CopyFromSalesHeader(Rec);
            SetCurrencyFactor(Rec."Currency Code", Rec."Currency Factor");
            "Responsibility Center" := Rec."Responsibility Center";
            if IsCreditDocType then
                "Document Type" := "Document Type"::Refund
            else
                "Document Type" := "Document Type"::Payment;

            SetApplyToDocNo(Rec, GenJnlLine, GenJnlLine."Applies-to Doc. Type", SalesInvoiceHdr."No.");

            Amount := -AMT;
            "Source Currency Amount" := Amount;

            "Allow Zero-Amount Posting" := true;
            GenJnlLine."Description 2" := Rec."Responsibility Center" + '/' + Format("POS Transaction No.") + '/' + format("Pos Bank Name");
            GenJnlLine.Narration := Rec."Responsibility Center" + '/' + "POS Transaction No.";
            GenJnlPostLine.RunWithCheck(GenJnlLine);
        end;
    end;

    local procedure PostCardAmount(SalesInvoiceHdr: Record "Sales Invoice Header"; AMT: Decimal)
    var
        GenJnlLine: Record "Gen. Journal Line";
        GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
    begin
        with GenJnlLine do begin
            InitNewLine(
           Rec."Posting Date", Rec."Document Date", Rec."Posting Description",
           Rec."Shortcut Dimension 1 Code", Rec."Shortcut Dimension 2 Code",
           Rec."Dimension Set ID", Rec."Reason Code");

            CopyDocumentFields(0, SalesInvoiceHdr."No.", SalesInvoiceHdr."External Document No.", '', '');
            "Account Type" := "Account Type"::Customer;
            "Account No." := Rec."Bill-to Customer No.";
            CopyFromSalesHeader(Rec);
            SetCurrencyFactor(Rec."Currency Code", Rec."Currency Factor");

            if IsCreditDocType then
                "Document Type" := "Document Type"::Refund
            else
                "Document Type" := "Document Type"::Payment;

            SetApplyToDocNo(Rec, GenJnlLine, GenJnlLine."Applies-to Doc. Type", SalesInvoiceHdr."No.");

            //Amount := TotalSalesLine2."Amount Including VAT" + CustLedgEntry."Remaining Pmt. Disc. Possible";
            "Source Currency Amount" := Amount;
            "Allow Zero-Amount Posting" := true;
            "Bal. Account No." := "POS Account No.";//FIX22Jun2021
            Validate(Amount, -AMT);
            "Responsibility Center" := Rec."Responsibility Center";
            GenJnlLine."Description 2" := Rec."Responsibility Center" + '/' + Format("POS Transaction No.") + '/' + format("Pos Bank Name");
            GenJnlLine.Narration := Rec."Responsibility Center" + '/' + "POS Transaction No.";
            Clear(GenJnlPostLine);
            GenJnlPostLine.RunWithCheck(GenJnlLine);
        end;

    end;

    local procedure SetApplyToDocNo(SalesHeader: Record "Sales Header"; var GenJnlLine: Record "Gen. Journal Line"; DocType: Option; DocNo: Code[20])
    begin
        with GenJnlLine do begin
            if SalesHeader."Bal. Account Type" = SalesHeader."Bal. Account Type"::"Bank Account" then
                "Bal. Account Type" := "Bal. Account Type"::"Bank Account";
            "Bal. Account No." := SalesHeader."Bal. Account No.";
            "Applies-to Doc. Type" := "Applies-to Doc. Type"::Invoice;
            "Applies-to Doc. No." := DocNo;
        end;

    end;
}

