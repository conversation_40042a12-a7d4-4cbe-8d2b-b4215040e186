tableextension 50083 ProdBomLinext extends "Production BOM Line"
{
    fields
    {
        // Add changes to table fields here
    }
    trigger OnModify()
    var
        ProdBomHdr: Record "Production BOM Header";
        ProdBomHdrVer: Record "Production BOM Version";
    begin
        IF "Version Code" = '' then BEGIN
            IF ProdBomHdr.Get("Production BOM No.") THEN
                ProdBomHdr.TestField("Approval Status", ProdBomHdr."Approval Status"::Open);
        END ELSE
            IF ProdBomHdrVer.Get("Production BOM No.", "Version Code") THEN
                ProdBomHdrVer.TestField("Approval Status", ProdBomHdr."Approval Status"::Open);
    end;



}