codeunit 50048 RebateGL
{
    //SingleInstance = true;

    Permissions = tabledata "Sales Invoice Header" = M;
    //170424 KD Rebate

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Document Totals", 'OnAfterSalesDeltaUpdateTotals', '', false, false)]
    local procedure DocumentTotals_OnAfterSalesDeltaUpdateTotals(var TotalSalesLine: Record "Sales Line"; var SalesLine: Record "Sales Line"; var xSalesLine: Record "Sales Line")
    begin
        TotalSalesLine."Rebate Discount" += SalesLine."Rebate Discount" - xSalesLine."Rebate Discount";
        //rebate issue
        TotalSalesLine."Fixed rebate amount" += SalesLine."Fixed Rebate Amount" - xSalesLine."Fixed Rebate Amount";
        TotalSalesLine.AmtafterRebate += SalesLine.AmtafterRebate - xSalesLine.AmtafterRebate;
        TotalSalesLine.AmtafterRebateIncVAT += SalesLine.AmtafterRebateIncVAT - xSalesLine.AmtafterRebateIncVAT;
        TotalSalesLine."Fixed Rebate Amount to Inv." += SalesLine."Fixed Rebate Amount to Inv." - xSalesLine."Fixed Rebate Amount to Inv.";
        TotalSalesLine."Rebate Disc. Amount to Inv." += SalesLine."Rebate Disc. Amount to Inv." - xSalesLine."Rebate Disc. Amount to Inv.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Document Totals", 'OnAfterCalculateSalesSubPageTotals', '', false, false)]
    local procedure DocumentTotals_OnAfterCalculateSalesSubPageTotals(var TotalSalesLine2: Record "Sales Line")
    begin
        TotalSalesLine2.CalcSums("Rebate Discount");
        TotalSalesLine2.CalcSums("Fixed Rebate Amount"); //rebate issue
        TotalSalesLine2.CalcSums(AmtafterRebate);
        TotalSalesLine2.CalcSums(AmtafterRebateIncVAT);
        TotalSalesLine2.CalcSums("Fixed Rebate Amount to Inv.");
        TotalSalesLine2.CalcSums("Rebate Disc. Amount to Inv.");
    end;



    //170424
    var
        ItemJournalLrec: Record "Item Journal Line";

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterFinalizePosting', '', false, false)]
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterPostSalesLines', '', false, false)]
    // local procedure CreateRebateCRMemo(var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesHeader: Record "Sales Header"; var SalesLinesProcessed: Boolean; var SalesCrMemoHeader: Record "Sales Cr.Memo Header")
    local procedure CreateRebateCRMemo(var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesHeader: Record "Sales Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header")
    var
        SalesCRHeader: Record "Sales Cr.Memo Header";
        SalesCRMemoLine: record "Sales Cr.Memo Line";
        SalesLine, CheckSalesline : Record "Sales Line";
        SalesInvLine: Record "Sales Invoice Line";
        GenPostingSetup: Record "General Posting Setup";
        InvPostingSetErr: Label 'Invalid discount posting setup error';
        SalesCRMemoPage: Page "Sales Credit Memo";
        PostCodeunit: codeunit "Sales-Post (Yes/No)";
        RebateDisHdr, RebateDisHdrCopy : Record "Rebate Discount Header";
        RebateDisLine, RebateDisLineCopy : Record "Rebate Discount Line";
        GenJnlLine: Record "Gen. Journal Line";
        LineNo: Integer;
        Cust, CustCopy : Record Customer;
        GnJnlPost: Codeunit "Gen. Jnl.-Post Line";
        CustRespCentre: Record "Customer Resp. Cent. Lines";
        Batch: Record "Gen. Journal Batch";
        GLSetup: Record "General Ledger Setup";
        BankAccount: Record "Bank Account";
        GenJnlTemp: Record "Gen. Journal Template";
        NoSeriesmgt: Codeunit NoSeriesManagement;
        DocumentNo: Code[20];
        PostGL: Codeunit "Gen. Jnl.-Post Line";
        RebateAmountToPost, FRtoInv, RDtoinv : Decimal;
        DefDim: Record "Default Dimension";
        CountDim: Integer;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimensionManagement: Codeunit DimensionManagement;
        DimValue: Record "Dimension Value";
        countIteration: Integer;
    begin
        FRtoInv := 0;
        RDtoinv := 0;
        CheckSalesline.reset;
        CheckSalesline.SetRange("Document No.", SalesHeader."no.");
        CheckSalesline.SetFilter("Qty. to Invoice", '>%1', 0);
        if CheckSalesline.FindFirst() then
            repeat
                FRtoInv += CheckSalesline."Fixed Rebate Amount to Inv.";
                RDtoinv += CheckSalesline."Rebate Disc. Amount to Inv.";
            until CheckSalesline.next = 0;

        //CheckSalesline.CalcSums("Inv. Disc. Amount to Invoice", "Fixed Rebate Amount to Inv.", "Rebate Discount", "Rebate Disc. Amount to Inv.");

        //SalesHeader.CalcFields("Rebate Discount", "Inv. Disc. Amt Calc on Rebate", "Fixed Rebate Amount", "Fixed Rebate Amount to Inv.");
        SalesHeader.CalcFields("Rebate Discount", "Fixed Rebate Amount");
        // message(Format(SalesInvoiceHeader."No."));

        If (SalesHeader."Rebate Discount" = 0) and (SalesHeader."Fixed Rebate Amount" = 0) then exit;

        SalesLine.Reset();
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        //SalesLine.SetFilter("Qty. to Invoice", '<>%1', 0);
        if SalesLine.FindFirst() then begin
            countIteration := 1;
            repeat
                if SalesLine."Qty. to Invoice" <> 0 then begin
                    SalesInvLine.Reset();
                    SalesInvLine.SetRange("Document No.", SalesInvoiceHeader."No.");
                    SalesInvLine.SetRange("Order No.", SalesLine."Document No.");
                    SalesInvLine.SetRange("Order Line No.", SalesLine."Line No.");
                    SalesInvLine.SetFilter(Quantity, '<>%1', 0);
                    if SalesInvLine.FindFirst() then begin
                        //300424
                        if countIteration = 1 then begin
                            RebateDisHdr.Init();
                            RebateDisHdr."Sell-to Customer No." := SalesHeader."Sell-to Customer No.";
                            RebateDisHdr."Sales Inv. No." := SalesInvoiceHeader."No.";
                            RebateDisHdr.Validate("Sell-to Customer No.");
                            RebateDisHdr."Posting Date" := Today;
                            RebateDisHdr."Sales Header No." := SalesHeader."No.";

                            RebateDisHdr."Rebate Discount Amount" := RDtoinv; //CheckSalesline."Rebate Disc. Amount to Inv."; //SalesHeader."Inv. Disc. Amt Calc on Rebate";
                                                                              //SalesHeader."Rebate Discount";
                                                                              //rebate issue
                            RebateDisHdr."Fixed Rebate Amount" := FRtoInv; //CheckSalesline."Fixed Rebate Amount to Inv."; //SalesHeader."Fixed Rebate Amount to Inv.";

                            RebateDisHdr."Applies-to Doc. No." := SalesHeader."Applies-to Doc. No.";
                            RebateDisHdr."Sell-to Customer Name" := SalesInvoiceHeader."Sell-to Customer Name";
                            RebateDisHdr."Bill-to Customer Name" := SalesInvoiceHeader."Bill-to Name";
                            RebateDisHdr.Insert();
                        end;
                        //300424
                        GenPostingSetup.Reset();
                        GenPostingSetup.SetRange("Gen. Bus. Posting Group", SalesInvLine."Gen. Bus. Posting Group");
                        GenPostingSetup.SetRange("Gen. Prod. Posting Group", SalesInvLine."Gen. Prod. Posting Group");
                        if not GenPostingSetup.FindFirst() then Error(InvPostingSetErr);
                        RebateDisLine.Reset();
                        RebateDisLine.Init();
                        RebateDisLine."Gen. Bus. Posting Group" := SalesInvLine."Gen. Bus. Posting Group";
                        RebateDisLine."Gen. Prod. Posting Group" := SalesInvLine."Gen. Prod. Posting Group";
                        RebateDisLine."Sell-to Customer No." := SalesInvLine."Sell-to Customer No.";
                        // RebateDisLine.validate("Sell-to Customer No.");
                        RebateDisLine."Document No." := RebateDisHdr."Sales Header No.";
                        RebateDisLine."Item No." := SalesInvLine."No.";
                        // RebateDisLine.Type := SalesCRMemoLine.TYpe::"G/L Account";

                        //RebateDisLine.Validate("Gen. Bus. Posting Group");
                        // RebateDisLine.Validate("Gen. Prod. Posting Group");
                        RebateDisLine."Discount Account" := GenPostingSetup."Sales Inv. Disc. Account";
                        // RebateDisLine.Validate("Discount Account");
                        RebateDisHdr."Discount Account" := RebateDisLine."Discount Account";
                        RebateDisHdr.Modify();
                        RebateDisLine."Shortcut Dimension 1 Code" := SalesInvLine."Shortcut Dimension 1 Code";
                        RebateDisLine."Shortcut Dimension 2 Code" := SalesInvLine."Shortcut Dimension 2 Code";

                        RebateDisLine.Amount := SalesLine."Rebate Disc. Amount to Inv.";// rebate issue
                        RebateDisLine."Fixed Rebate Amount" := SalesLine."Fixed Rebate Amount to Inv.";// rebate issue
                                                                                                       //SalesInvLine.Quantity / SalesLine.Quantity * SalesLine."Rebate Discount";
                        RebateDisLine.Quantity := SalesInvLine.Quantity;
                        RebateDisLine."Shortcut Dimension 3 Code" := SalesLine."Shortcut Dimension 3 Code";
                        RebateDisLine."Sales Inv. No." := RebateDisHdr."Sales Inv. No.";
                        RebateDisLine."Sales Inv. Order Line No" := SalesInvLine."Line No.";
                        RebateDisLine."Sales Order Line No." := SalesLine."Line No.";
                        RebateDisLine."No." := RebateDisLine.GetLastRebateNo() + 1;
                        RebateDisLine.Insert();
                        countIteration += 1;
                    end;
                end;
                SalesLine.validate("Fixed Rebate Amount to Inv.", 0);
                SalesLine.validate("Rebate Disc. Amount to Inv.", 0);
                SalesLine.Modify();
            until SalesLine.Next() = 0;

        end;
    end;

    //  [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterSalesInvLineInsert', '', false, false)]
    procedure PostRebateAmounts(var SalesHeader: Record "Sales Header"; var SalesInvLine: Record "Sales Invoice Line"; SalesInvHeader: Record "Sales Invoice Header")
    var
        SalesCRHeader: Record "Sales Cr.Memo Header";
        SalesInvHeader2: Record "Sales Invoice Header";
        SalesCRMemoLine: record "Sales Cr.Memo Line";
        SalesLine: Record "Sales Line";
        // SalesInvLine2: Record "Sales Invoice Line";
        GenPostingSetup: Record "General Posting Setup";
        InvPostingSetErr: Label 'Invalid discount posting setup error';
        SalesCRMemoPage: Page "Sales Credit Memo";
        PostCodeunit: codeunit "Sales-Post (Yes/No)";
        RebateDisHdr, RebateDisHdrCopy : Record "Rebate Discount Header";
        RebateDisLine, RebateDisLineCopy : Record "Rebate Discount Line";
        GenJnlLineCopy, GenJnlLineCopyFR : Record "Gen. Journal Line";
        LineNo: Integer;
        Cust, CustCopy : Record Customer;
        GnJnlPost: Codeunit "Gen. Jnl.-Post Line";
        CustRespCentre: Record "Customer Resp. Cent. Lines";
        Batch: Record "Gen. Journal Batch";
        GLSetup: Record "General Ledger Setup";
        BankAccount: Record "Bank Account";
        GenJnlTemp: Record "Gen. Journal Template";
        NoSeriesmgt: Codeunit NoSeriesManagement;
        DocumentNo: Code[20];
        PostGL: Codeunit "Gen. Jnl.-Post Line";
        RebateAmountToPost, FixedRebatetoPost : Decimal;
        DefDim: Record "Default Dimension";
        CountDim: Integer;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimensionManagement: Codeunit DimensionManagement;
        DimValue: Record "Dimension Value";
    begin
        RebateDisHdrCopy.Reset();
        RebateDisHdrCopy.SetRange("Sales Header No.", SalesHeader."No.");
        // RebateDisHdrCopy.SetRange("Sales Inv. No.", SalesInvHeader."No.");
        RebateDisHdrCopy.SetRange(Processed, false);
        if RebateDisHdrCopy.FindFirst() then begin
            RebateAmountToPost := 0;
            FixedRebatetoPost := 0; //rebate issue
            //RebateDisLineCopy.Reset();
            /* RebateDisLineCopy.SetRange("Document No.", RebateDisHdrCopy."Sales Header No.");
            RebateDisLineCopy.SetRange("Sales Inv. No.", RebateDisHdrCopy."Sales Inv. No.");
            RebateDisLineCopy.SetRange(Processed, false);
            RebateDisLineCopy.CalcSums(Amount, "Fixed Rebate Amount"); *///rebate issue
            RebateAmountToPost := RebateDisHdrCopy."Rebate Discount Amount"; //RebateDisLineCopy.Amount;
            FixedRebatetoPost := RebateDisHdrCopy."Fixed Rebate Amount"; //rebateDisLineCopy."Fixed Rebate Amount"; //rebate issue

            // if RebateDisLineCopy.FindFirst() then begin
            RebateDisLine.Reset();
            RebateDisLine.SetRange("Document No.", RebateDisHdrCopy."Sales Header No."); //RebateDisLineCopy."Document No.");
            RebateDisLine.SetRange("Sales Inv. No.", RebateDisHdrCopy."Sales Inv. No."); //RebateDisLineCopy."Sales Inv. No.");
            RebateDisLine.SetRange(Processed, false);
            if RebateDisLine.FindFirst() then begin
                //commented to allow to use the standard discount posting
                /*       //post to G/L
                      GenJnlTemp.Reset();
                      GenJnlTemp.SetRange(Name, 'BANKAPI');
                      if GenJnlTemp.FindFirst() then
                          DocumentNo := NoSeriesmgt.GetNextNo(GenJnlTemp."No. Series", TODAY, TRUE);
                      GLSetup.Get;
                      GenJnlLineCopy.Reset();
                      GenJnlLineCopy.SetRange("Journal Batch Name", GLSetup."Notification Batch Name");
                      GenJnlLineCopy.SetRange("Journal Template Name", GLSetup."Notification Nos.");
                      GenJnlLineCopy.DeleteAll();
                      GenJnlLineCopy.Init;

                      GenJnlLineCopyFR.Reset();
                      GenJnlLineCopyFR.SetRange("Journal Batch Name", GLSetup."Notification Batch Name");
                      GenJnlLineCopyFR.SetRange("Journal Template Name", GLSetup."Notification Nos.");
                      GenJnlLineCopyFR.DeleteAll();
                      GenJnlLineCopyFR.Init;
                      //only if rebate discount is not zero
                      if RebateDisLine.Amount <> 0 then begin
                          GenJnlLineCopy."Line No." := 1000;
                          GenJnlLineCopy."Journal Template Name" := GLSetup."Notification Nos.";
                          GenJnlLineCopy."Journal Batch Name" := GLSetup."Notification Batch Name";
                          GenJnlLineCopy."Shortcut Dimension 1 Code" := RebateDisLine."Shortcut Dimension 1 Code";
                          GenJnlLineCopy."Shortcut Dimension 2 Code" := RebateDisLine."Shortcut Dimension 2 Code";
                          GenJnlLineCopy."Shortcut Dimension 3 Code" := RebateDisLine."Shortcut Dimension 3 Code";
                          //get cust resp centre
                          CustRespCentre.Reset();
                          CustRespCentre.SetRange(CustRespCentre."Customer No.", RebateDisLine."Sell-to Customer No.");
                          if CustRespCentre.FindFirst() then begin
                              GenJnlLineCopy.Validate("Responsibility Center", CustRespCentre."Resp. Center Code");
                          end else
                              CustRespCentre.TestField("Resp. Center Code");

                          GenJnlLineCopy."Posting Date" := Today;
                          GenJnlLineCopy.Narration := StrSubstNo('Variable Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                          GenJnlLineCopy.Amount := -RebateAmountToPost;

                          //update here now
                          GenJnlLineCopy."Account Type" := GenJnlLineCopy."Account Type"::Customer;
                          GenJnlLineCopy.validate("Account Type");
                          GenJnlLineCopy."Account No." := RebateDisHdrCopy."Sell-to Customer No.";
                          GenJnlLineCopy.validate("Account No.");
                          //end here


                          GenJnlLineCopy."Document Type" := GenJnlLineCopy."Document Type"::Payment;
                          GenJnlLineCopy.validate("Document Type");
                          GenJnlLineCopy."Document No." := RebateDisHdrCopy."Sales Inv. No.";
                          GenJnlLineCopy.validate(GenJnlLineCopy."Document No.");


                          GenJnlLineCopy."Bal. Account Type" := GenJnlLineCopy."Bal. Account Type"::"G/L Account";
                          GenJnlLineCopy.validate("Bal. Account Type");

                          GenJnlLineCopy."Bal. Account No." := RebateDisHdrCopy."Discount Account";
                          GenJnlLineCopy.validate("Bal. Account No.");
                          GenJnlLineCopy.Description := StrSubstNo('Variable Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                          GenJnlLineCopy."Description 2" := StrSubstNo('Variable Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");

                          DefDim.Reset();
                          // DefDim.SetRange("No.", RebateDisLine."Item No.");
                          DefDim.SetRange("No.", RebateDisLine."Sell-to Customer No.");
                          DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                          if DefDim.FindFirst() then begin
                              repeat
                                  TempDimSetEntry.Init();
                                  TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                                  TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                                  DimValue.Reset();
                                  DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                                  DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                                  if DimValue.FindFirst() then begin
                                      TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                                  end;
                                  IF NOT TempDimSetEntry.INSERT
                                  THEN
                                      TempDimSetEntry.MODIFY;
                              until DefDim.Next = 0;

                          end;

                          DefDim.Reset();
                          DefDim.SetRange("No.", RebateDisLine."Item No.");
                          DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                          if DefDim.FindFirst() then begin
                              repeat
                                  TempDimSetEntry.Init();
                                  TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                                  TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                                  DimValue.Reset();
                                  DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                                  DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                                  if DimValue.FindFirst() then begin
                                      TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                                  end;
                                  IF NOT TempDimSetEntry.INSERT
                                  THEN
                                      TempDimSetEntry.MODIFY;
                              until DefDim.Next = 0;
                          end;

                          //to insert global dimension 1
                          TempDimSetEntry.Init();
                          TempDimSetEntry.Validate("Dimension Code", GLSetup."Global Dimension 1 Code");
                          TempDimSetEntry.Validate("Dimension Value Code", RebateDisLine."Shortcut Dimension 1 Code");
                          DimValue.Reset();
                          DimValue.SetRange(DimValue."Dimension Code", GLSetup."Global Dimension 1 Code");
                          DimValue.SetRange(DimValue.Code, RebateDisLine."Shortcut Dimension 1 Code");
                          if DimValue.FindFirst() then begin
                              TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                          end;
                          IF NOT TempDimSetEntry.INSERT
                          THEN
                              TempDimSetEntry.MODIFY;

                          //to insert global dimension 2
                          TempDimSetEntry.Init();
                          TempDimSetEntry.Validate("Dimension Code", GLSetup."Global Dimension 2 Code");
                          TempDimSetEntry.Validate("Dimension Value Code", RebateDisLine."Shortcut Dimension 2 Code");
                          DimValue.Reset();
                          DimValue.SetRange(DimValue."Dimension Code", GLSetup."Global Dimension 2 Code");
                          DimValue.SetRange(DimValue.Code, RebateDisLine."Shortcut Dimension 2 Code");
                          if DimValue.FindFirst() then begin
                              TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                          end;
                          IF NOT TempDimSetEntry.INSERT
                          THEN
                              TempDimSetEntry.MODIFY;


                          GenJnlLineCopy."Dimension Set ID" := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
                      end;
                      //check for rebate discount not zero <<

                      //Create jnl for fixed rebate
                      if RebateDisLine."Fixed Rebate Amount" <> 0 then begin //check for fixed rebate discount not zero >>
                          GenJnlLineCopyFR."Line No." := 2000;
                          GenJnlLineCopyFR."Journal Template Name" := GLSetup."Notification Nos.";
                          GenJnlLineCopyFR."Journal Batch Name" := GLSetup."Notification Batch Name";
                          GenJnlLineCopyFR."Shortcut Dimension 1 Code" := RebateDisLine."Shortcut Dimension 1 Code";
                          GenJnlLineCopyFR."Shortcut Dimension 2 Code" := RebateDisLine."Shortcut Dimension 2 Code";
                          GenJnlLineCopyFR."Shortcut Dimension 3 Code" := RebateDisLine."Shortcut Dimension 3 Code";
                          //get cust resp centre
                          CustRespCentre.Reset();
                          CustRespCentre.SetRange(CustRespCentre."Customer No.", RebateDisLine."Sell-to Customer No.");
                          if CustRespCentre.FindFirst() then begin
                              GenJnlLineCopyFR.Validate("Responsibility Center", CustRespCentre."Resp. Center Code");
                          end else
                              CustRespCentre.TestField("Resp. Center Code");

                          GenJnlLineCopyFR."Posting Date" := Today;
                          GenJnlLineCopyFR.Narration := StrSubstNo('Fixed Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                          GenJnlLineCopyFR.Amount := -FixedRebatetoPost;

                          //update here now
                          GenJnlLineCopyFR."Account Type" := GenJnlLineCopyFR."Account Type"::Customer;
                          GenJnlLineCopyFR.validate("Account Type");
                          GenJnlLineCopyFR."Account No." := RebateDisHdrCopy."Sell-to Customer No.";
                          GenJnlLineCopyFR.validate("Account No.");
                          //end here


                          GenJnlLineCopyFR."Document Type" := GenJnlLineCopyFR."Document Type"::Payment;
                          GenJnlLineCopyFR.validate("Document Type");
                          GenJnlLineCopyFR."Document No." := RebateDisHdrCopy."Sales Inv. No.";
                          GenJnlLineCopyFR.validate(GenJnlLineCopyFR."Document No.");


                          GenJnlLineCopyFR."Bal. Account Type" := GenJnlLineCopyFR."Bal. Account Type"::"G/L Account";
                          GenJnlLineCopyFR.validate("Bal. Account Type");

                          GenJnlLineCopyFR."Bal. Account No." := RebateDisHdrCopy."Discount Account";
                          GenJnlLineCopyFR.validate("Bal. Account No.");
                          GenJnlLineCopyFR.Description := StrSubstNo('Fixed Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                          GenJnlLineCopyFR."Description 2" := StrSubstNo('Fixed Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");

                          DefDim.Reset();
                          // DefDim.SetRange("No.", RebateDisLine."Item No.");
                          DefDim.SetRange("No.", RebateDisLine."Sell-to Customer No.");
                          DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                          if DefDim.FindFirst() then begin
                              repeat
                                  TempDimSetEntry.Init();
                                  TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                                  TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                                  DimValue.Reset();
                                  DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                                  DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                                  if DimValue.FindFirst() then begin
                                      TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                                  end;
                                  IF NOT TempDimSetEntry.INSERT
                                  THEN
                                      TempDimSetEntry.MODIFY;
                              until DefDim.Next = 0;

                          end;
                          DefDim.Reset();
                          DefDim.SetRange("No.", RebateDisLine."Item No.");
                          DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                          if DefDim.FindFirst() then begin
                              repeat
                                  TempDimSetEntry.Init();
                                  TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                                  TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                                  DimValue.Reset();
                                  DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                                  DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                                  if DimValue.FindFirst() then begin
                                      TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                                  end;
                                  IF NOT TempDimSetEntry.INSERT
                                  THEN
                                      TempDimSetEntry.MODIFY;
                              until DefDim.Next = 0;

                          end;
                          //to insert global dimension 1
                          TempDimSetEntry.Init();
                          TempDimSetEntry.Validate("Dimension Code", GLSetup."Global Dimension 1 Code");
                          TempDimSetEntry.Validate("Dimension Value Code", RebateDisLine."Shortcut Dimension 1 Code");
                          DimValue.Reset();
                          DimValue.SetRange(DimValue."Dimension Code", GLSetup."Global Dimension 1 Code");
                          DimValue.SetRange(DimValue.Code, RebateDisLine."Shortcut Dimension 1 Code");
                          if DimValue.FindFirst() then begin
                              TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                          end;
                          IF NOT TempDimSetEntry.INSERT
                          THEN
                              TempDimSetEntry.MODIFY;

                          //to insert global dimension 2
                          TempDimSetEntry.Init();
                          TempDimSetEntry.Validate("Dimension Code", GLSetup."Global Dimension 2 Code");
                          TempDimSetEntry.Validate("Dimension Value Code", RebateDisLine."Shortcut Dimension 2 Code");
                          DimValue.Reset();
                          DimValue.SetRange(DimValue."Dimension Code", GLSetup."Global Dimension 2 Code");
                          DimValue.SetRange(DimValue.Code, RebateDisLine."Shortcut Dimension 2 Code");
                          if DimValue.FindFirst() then begin
                              TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                          end;
                          IF NOT TempDimSetEntry.INSERT
                          THEN
                              TempDimSetEntry.MODIFY;
                          GenJnlLineCopyFR."Dimension Set ID" := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
                      end;

                      //post to GL
                      if RebateDisLine.Amount <> 0 then
                          PostGL.RunWithCheck(GenJnlLineCopy);
                      //Post Fixed Rebate to GL
                      if RebateDisLine."Fixed Rebate Amount" <> 0 then
                          PostGL.RunWithCheck(GenJnlLineCopyFR); */


                RebateDisHdrCopy.Processed := true;
                RebateDisHdrCopy."DateTime Processed" := CurrentDateTime;
                RebateDisHdrCopy."Date Processed" := Today;
                RebateDisHdrCopy.Modify();

                //lines
                RebateDisLine.Reset();
                /* RebateDisLine.SetRange("Sales Inv. No.", RebateDisLineCopy."Sales Inv. No.");
                RebateDisLine.SetRange("Document No.", RebateDisLineCopy."Document No."); */
                RebateDisLine.SetRange("Document No.", RebateDisHdrCopy."Sales Header No."); //RebateDisLineCopy."Document No.");
                RebateDisLine.SetRange("Sales Inv. No.", RebateDisHdrCopy."Sales Inv. No.");
                RebateDisLine.ModifyAll(Processed, true);
                RebateDisLine.ModifyAll("Date Processed", Today);
                RebateDisLine.ModifyAll("DateTime Processed", CurrentDateTime);

                SalesInvHeader2.Reset();
                SalesInvHeader2.SetRange("No.", RebateDisHdrCopy."Sales Inv. No.");
                if SalesInvHeader2.FindFirst() then begin
                    SalesInvHeader2."Rebate Discount" := RebateAmountToPost;
                    SalesInvHeader2."Fixed Rebate amount" := FixedRebatetoPost; //rebate issue
                    SalesInvHeader2.Modify();
                end;
            end;

            //end;
        end;
    end;


    //re post Rebate discounts
    procedure RePostRebateAmounts(var DHeader: record "Rebate Discount Header")
    var
        SalesCRHeader: Record "Sales Cr.Memo Header";
        SalesInvHeader2: Record "Sales Invoice Header";
        SalesCRMemoLine: record "Sales Cr.Memo Line";
        SalesLine: Record "Sales Line";
        // SalesInvLine2: Record "Sales Invoice Line";
        GenPostingSetup: Record "General Posting Setup";
        InvPostingSetErr: Label 'Invalid discount posting setup error';
        SalesCRMemoPage: Page "Sales Credit Memo";
        PostCodeunit: codeunit "Sales-Post (Yes/No)";
        RebateDisHdr, RebateDisHdrCopy : Record "Rebate Discount Header";
        RebateDisLine, RebateDisLineCopy : Record "Rebate Discount Line";
        GenJnlLineCopy, GenJnlLineCopyFR : Record "Gen. Journal Line";
        LineNo: Integer;
        Cust, CustCopy : Record Customer;
        GnJnlPost: Codeunit "Gen. Jnl.-Post Line";
        CustRespCentre: Record "Customer Resp. Cent. Lines";
        Batch: Record "Gen. Journal Batch";
        GLSetup: Record "General Ledger Setup";
        BankAccount: Record "Bank Account";
        GenJnlTemp: Record "Gen. Journal Template";
        NoSeriesmgt: Codeunit NoSeriesManagement;
        DocumentNo: Code[20];
        PostGL: Codeunit "Gen. Jnl.-Post Line";
        RebateAmountToPost, FixedRebatetoPost : Decimal;
        DefDim: Record "Default Dimension";
        CountDim: Integer;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimensionManagement: Codeunit DimensionManagement;
        DimValue: Record "Dimension Value";
    //DocDim: Record "Document Dimen"
    begin
        RebateDisHdrCopy.Reset();
        RebateDisHdrCopy.SetRange("Sales Header No.", DHeader."Sales Header No.");
        // RebateDisHdrCopy.SetRange("Sales Inv. No.", SalesInvHeader."No.");
        RebateDisHdrCopy.SetRange(Processed, false);
        RebateDisHdrCopy.SetRange(Processed, false);
        if RebateDisHdrCopy.FindFirst() then begin
            RebateAmountToPost := 0;
            FixedRebatetoPost := 0; //rebate issue
            //RebateDisLineCopy.Reset();
            /* RebateDisLineCopy.SetRange("Document No.", RebateDisHdrCopy."Sales Header No.");
            RebateDisLineCopy.SetRange("Sales Inv. No.", RebateDisHdrCopy."Sales Inv. No.");
            RebateDisLineCopy.SetRange(Processed, false);
            RebateDisLineCopy.CalcSums(Amount, "Fixed Rebate Amount"); *///rebate issue
            RebateAmountToPost := RebateDisHdrCopy."Rebate Discount Amount"; //RebateDisLineCopy.Amount;
            FixedRebatetoPost := RebateDisHdrCopy."Fixed Rebate Amount"; //rebateDisLineCopy."Fixed Rebate Amount"; //rebate issue

            // if RebateDisLineCopy.FindFirst() then begin
            RebateDisLine.Reset();
            RebateDisLine.SetRange("Document No.", RebateDisHdrCopy."Sales Header No."); //RebateDisLineCopy."Document No.");
            RebateDisLine.SetRange("Sales Inv. No.", RebateDisHdrCopy."Sales Inv. No."); //RebateDisLineCopy."Sales Inv. No.");
            RebateDisLine.SetRange(Processed, false);
            if RebateDisLine.FindFirst() then begin
                //post to G/L
                GenJnlTemp.Reset();
                GenJnlTemp.SetRange(Name, 'BANKAPI');
                if GenJnlTemp.FindFirst() then
                    DocumentNo := NoSeriesmgt.GetNextNo(GenJnlTemp."No. Series", TODAY, TRUE);
                GLSetup.Get;
                GenJnlLineCopy.Reset();
                GenJnlLineCopy.SetRange("Journal Batch Name", GLSetup."Notification Batch Name");
                GenJnlLineCopy.SetRange("Journal Template Name", GLSetup."Notification Nos.");
                GenJnlLineCopy.DeleteAll();
                GenJnlLineCopy.Init;

                GenJnlLineCopyFR.Reset();
                GenJnlLineCopyFR.SetRange("Journal Batch Name", GLSetup."Notification Batch Name");
                GenJnlLineCopyFR.SetRange("Journal Template Name", GLSetup."Notification Nos.");
                GenJnlLineCopyFR.DeleteAll();
                GenJnlLineCopyFR.Init;
                //only if rebate discount is not zero
                if RebateDisLine.Amount <> 0 then begin
                    GenJnlLineCopy."Line No." := 1000;
                    GenJnlLineCopy."Journal Template Name" := GLSetup."Notification Nos.";
                    GenJnlLineCopy."Journal Batch Name" := GLSetup."Notification Batch Name";
                    GenJnlLineCopy."Shortcut Dimension 1 Code" := RebateDisLine."Shortcut Dimension 1 Code";
                    GenJnlLineCopy."Shortcut Dimension 2 Code" := RebateDisLine."Shortcut Dimension 2 Code";
                    GenJnlLineCopy."Shortcut Dimension 3 Code" := RebateDisLine."Shortcut Dimension 3 Code";
                    //get cust resp centre
                    CustRespCentre.Reset();
                    CustRespCentre.SetRange(CustRespCentre."Customer No.", RebateDisLine."Sell-to Customer No.");
                    if CustRespCentre.FindFirst() then begin
                        GenJnlLineCopy.Validate("Responsibility Center", CustRespCentre."Resp. Center Code");
                    end else
                        CustRespCentre.TestField("Resp. Center Code");

                    GenJnlLineCopy."Posting Date" := Today;
                    GenJnlLineCopy.Narration := StrSubstNo('Variable Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                    GenJnlLineCopy.Amount := -RebateAmountToPost;

                    //update here now
                    GenJnlLineCopy."Account Type" := GenJnlLineCopy."Account Type"::Customer;
                    GenJnlLineCopy.validate("Account Type");
                    GenJnlLineCopy."Account No." := RebateDisHdrCopy."Sell-to Customer No.";
                    GenJnlLineCopy.validate("Account No.");
                    //end here


                    GenJnlLineCopy."Document Type" := GenJnlLineCopy."Document Type"::Payment;
                    GenJnlLineCopy.validate("Document Type");
                    GenJnlLineCopy."Document No." := RebateDisHdrCopy."Sales Inv. No.";
                    GenJnlLineCopy.validate(GenJnlLineCopy."Document No.");


                    GenJnlLineCopy."Bal. Account Type" := GenJnlLineCopy."Bal. Account Type"::"G/L Account";
                    GenJnlLineCopy.validate("Bal. Account Type");

                    GenJnlLineCopy."Bal. Account No." := RebateDisHdrCopy."Discount Account";
                    GenJnlLineCopy.validate("Bal. Account No.");
                    GenJnlLineCopy.Description := StrSubstNo('Variable Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                    GenJnlLineCopy."Description 2" := StrSubstNo('Variable Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");

                    DefDim.Reset();
                    // DefDim.SetRange("No.", RebateDisLine."Item No.");
                    DefDim.SetRange("No.", RebateDisLine."Sell-to Customer No.");
                    DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                    if DefDim.FindFirst() then begin
                        repeat
                            TempDimSetEntry.Init();
                            TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                            TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                            DimValue.Reset();
                            DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                            DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                            if DimValue.FindFirst() then begin
                                TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                            end;
                            IF NOT TempDimSetEntry.INSERT
                            THEN
                                TempDimSetEntry.MODIFY;
                        until DefDim.Next = 0;

                    end;
                    DefDim.Reset();
                    DefDim.SetRange("No.", RebateDisLine."Item No.");
                    //DefDim.SetRange("No.", RebateDisLine."Sell-to Customer No.");
                    DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                    if DefDim.FindFirst() then begin
                        repeat
                            TempDimSetEntry.Init();
                            TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                            TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                            DimValue.Reset();
                            DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                            DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                            if DimValue.FindFirst() then begin
                                TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                            end;
                            IF NOT TempDimSetEntry.INSERT
                            THEN
                                TempDimSetEntry.MODIFY;
                        until DefDim.Next = 0;

                    end;
                    GenJnlLineCopy."Dimension Set ID" := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
                end;
                //check for rebate discount not zero <<

                //Create jnl for fixed rebate
                if RebateDisLine."Fixed Rebate Amount" <> 0 then begin //check for fixed rebate discount not zero >>
                    GenJnlLineCopyFR."Line No." := 2000;
                    GenJnlLineCopyFR."Journal Template Name" := GLSetup."Notification Nos.";
                    GenJnlLineCopyFR."Journal Batch Name" := GLSetup."Notification Batch Name";
                    GenJnlLineCopyFR."Shortcut Dimension 1 Code" := RebateDisLine."Shortcut Dimension 1 Code";
                    GenJnlLineCopyFR."Shortcut Dimension 2 Code" := RebateDisLine."Shortcut Dimension 2 Code";
                    GenJnlLineCopyFR."Shortcut Dimension 3 Code" := RebateDisLine."Shortcut Dimension 3 Code";
                    //get cust resp centre
                    CustRespCentre.Reset();
                    CustRespCentre.SetRange(CustRespCentre."Customer No.", RebateDisLine."Sell-to Customer No.");
                    if CustRespCentre.FindFirst() then begin
                        GenJnlLineCopyFR.Validate("Responsibility Center", CustRespCentre."Resp. Center Code");
                    end else
                        CustRespCentre.TestField("Resp. Center Code");

                    GenJnlLineCopyFR."Posting Date" := Today;
                    GenJnlLineCopyFR.Narration := StrSubstNo('Fixed Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                    GenJnlLineCopyFR.Amount := -FixedRebatetoPost;

                    //update here now
                    GenJnlLineCopyFR."Account Type" := GenJnlLineCopyFR."Account Type"::Customer;
                    GenJnlLineCopyFR.validate("Account Type");
                    GenJnlLineCopyFR."Account No." := RebateDisHdrCopy."Sell-to Customer No.";
                    GenJnlLineCopyFR.validate("Account No.");
                    //end here


                    GenJnlLineCopyFR."Document Type" := GenJnlLineCopyFR."Document Type"::Payment;
                    GenJnlLineCopyFR.validate("Document Type");
                    GenJnlLineCopyFR."Document No." := RebateDisHdrCopy."Sales Inv. No.";
                    GenJnlLineCopyFR.validate(GenJnlLineCopyFR."Document No.");


                    GenJnlLineCopyFR."Bal. Account Type" := GenJnlLineCopyFR."Bal. Account Type"::"G/L Account";
                    GenJnlLineCopyFR.validate("Bal. Account Type");

                    GenJnlLineCopyFR."Bal. Account No." := RebateDisHdrCopy."Discount Account";
                    GenJnlLineCopyFR.validate("Bal. Account No.");
                    GenJnlLineCopyFR.Description := StrSubstNo('Fixed Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");
                    GenJnlLineCopyFR."Description 2" := StrSubstNo('Fixed Discount Amount for %1', RebateDisHdrCopy."Sales Header No.");

                    DefDim.Reset();
                    //DefDim.SetRange("No.", RebateDisLine."Item No.");
                    DefDim.SetRange("No.", RebateDisLine."Sales Inv. No.");
                    DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                    if DefDim.FindFirst() then begin
                        repeat
                            TempDimSetEntry.Init();
                            TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                            TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                            DimValue.Reset();
                            DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                            DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                            if DimValue.FindFirst() then begin
                                TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                            end;
                            IF NOT TempDimSetEntry.INSERT
                            THEN
                                TempDimSetEntry.MODIFY;
                        until DefDim.Next = 0;

                    end;

                    DefDim.Reset();
                    // DefDim.SetRange("No.", RebateDisLine."Item No.");
                    DefDim.SetRange("No.", RebateDisLine."Sell-to Customer No.");
                    DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                    if DefDim.FindFirst() then begin
                        repeat
                            TempDimSetEntry.Init();
                            TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                            TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                            DimValue.Reset();
                            DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                            DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                            if DimValue.FindFirst() then begin
                                TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                            end;
                            IF NOT TempDimSetEntry.INSERT
                            THEN
                                TempDimSetEntry.MODIFY;
                        until DefDim.Next = 0;

                    end;
                    DefDim.Reset();
                    DefDim.SetRange("No.", RebateDisLine."Item No.");
                    //DefDim.SetRange("No.", RebateDisLine."Sell-to Customer No.");
                    DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                    if DefDim.FindFirst() then begin
                        repeat
                            TempDimSetEntry.Init();
                            TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                            TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                            DimValue.Reset();
                            DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                            DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                            if DimValue.FindFirst() then begin
                                TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                            end;
                            IF NOT TempDimSetEntry.INSERT
                            THEN
                                TempDimSetEntry.MODIFY;
                        until DefDim.Next = 0;

                    end;
                    GenJnlLineCopyFR."Dimension Set ID" := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
                end;

                //post to GL
                if RebateDisLine.Amount <> 0 then
                    PostGL.RunWithCheck(GenJnlLineCopy);
                //Post Fixed Rebate to GL
                if RebateDisLine."Fixed Rebate Amount" <> 0 then
                    PostGL.RunWithCheck(GenJnlLineCopyFR);
                RebateDisHdrCopy.Processed := true;
                RebateDisHdrCopy."DateTime Processed" := CurrentDateTime;
                RebateDisHdrCopy."Date Processed" := Today;
                RebateDisHdrCopy.Modify();

                //lines
                RebateDisLine.Reset();
                /* RebateDisLine.SetRange("Sales Inv. No.", RebateDisLineCopy."Sales Inv. No.");
                RebateDisLine.SetRange("Document No.", RebateDisLineCopy."Document No."); */
                RebateDisLine.SetRange("Document No.", RebateDisHdrCopy."Sales Header No."); //RebateDisLineCopy."Document No.");
                RebateDisLine.SetRange("Sales Inv. No.", RebateDisHdrCopy."Sales Inv. No.");
                RebateDisLine.ModifyAll(Processed, true);
                RebateDisLine.ModifyAll("Date Processed", Today);
                RebateDisLine.ModifyAll("DateTime Processed", CurrentDateTime);

                SalesInvHeader2.Reset();
                SalesInvHeader2.SetRange("No.", RebateDisHdrCopy."Sales Inv. No.");
                if SalesInvHeader2.FindFirst() then begin
                    SalesInvHeader2."Rebate Discount" := RebateAmountToPost;
                    SalesInvHeader2."Fixed Rebate amount" := FixedRebatetoPost; //rebate issue
                    SalesInvHeader2.Modify();
                end;
            end;

            //end;
        end;
    end;



}

