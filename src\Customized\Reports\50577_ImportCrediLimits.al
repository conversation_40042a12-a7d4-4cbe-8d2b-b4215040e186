report 50577 "Import Credit Limits"
{
    ProcessingOnly = true;
    UseRequestPage = true;
    UsageCategory = ReportsAndAnalysis;
    Caption = 'Import Credit Limits_50577';
    dataset
    {

    }

    requestpage
    {
        actions
        {

        }
        trigger OnQueryClosePage(CloseAction: Action): Boolean;
        begin
            IF CloseAction = ACTION::OK THEN BEGIN
                ServerFileNme := FileMgt.UploadFile(Text000, ExcelExtensionTok);
                IF ServerFileNme = '' THEN
                    EXIT(FALSE);

                SheetName := ExcelBuf.SelectSheetsName(ServerFileNme);
                IF SheetName = '' THEN
                    EXIT(FALSE);
            END;
        end;
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        lineNo2 := 0;
        LastLineNo := 0;
        ExcelBuf.LOCKTABLE;
        ExcelBuf.OpenBook(ServerFileNme, SheetName);
        ExcelBuf.ReadSheet;
        GetLastRowandColumn;

        // LineNo := GetLastLineNo();

        clearExistingLines();

        FOR X := 2 TO TotalRows DO
            InsertData(X);

        ExcelBuf.DELETEALL;

        MESSAGE('Import Completed');
    end;

    var

        ExcelBuf: Record "Excel Buffer";
        ServerFileNme: Text[250];
        SheetName: Text[250];
        FileName: Text[250];
        TotalColumns: Integer;
        TotalRows: Integer;
        X: Integer;
        ExcelBuf1: Record "Excel Buffer";
        FileMgt: Codeunit "File Management";
        Text000: Label 'Import Data';
        ExcelExtensionTok: Text[250];
        Text005: Label 'Imported from Excel';
        Text006: Label 'Import Excel File';
        Text00005: Label '.xlsx';
        Text029: Label 'You must enter a file name.';
        DocumentNumber: code[20];
        TemName: Code[20];
        BatcName: Code[20];

    procedure GetLastRowandColumn();
    begin
        ExcelBuf.SETRANGE(ExcelBuf."Row No.", 1);
        TotalColumns := ExcelBuf.COUNT;

        ExcelBuf.RESET;
        IF ExcelBuf.FINDLAST THEN
            TotalRows := ExcelBuf."Row No.";
    end;

    procedure InsertData(RowNo: Integer);
    var
        ContractNo: Code[20];
        CustCredLmtSchd: record "Cust. Cr. Limit Schedule";
        allinoneCU: Codeunit Codeunit1;
    begin
        LastLineNo := GetLastLineNo(GetValueAtCell(RowNo, 1));
        if lineNo2 = 0 then
            lineNo2 := LastLineNo + 1000;

        CustCredLmtSchd.Init();
        Evaluate(CustCredLmtSchd."Customer No.", GetValueAtCell(RowNo, 1));
        Evaluate(CustCredLmtSchd."Shortcut Dimension 1 Code", GetValueAtCell(RowNo, 2));
        Evaluate(CustCredLmtSchd."Responsibility Center", GetValueAtCell(RowNo, 3));
        // Evaluate(CustCredLmtSchd."Line No.", GetValueAtCell(RowNo, 4));
        CustCredLmtSchd."Line No." := lineNo2;
        CustCredLmtSchd.Insert();
        CustCredLmtSchd.VALIDATE("Customer No.");
        Evaluate(CustCredLmtSchd."Start Date", GetValueAtCell(RowNo, 5));
        Evaluate(CustCredLmtSchd.Description, GetValueAtCell(RowNo, 6));
        Evaluate(CustCredLmtSchd."Description 2", GetValueAtCell(RowNo, 7));
        Evaluate(CustCredLmtSchd."Customer Type", GetValueAtCell(RowNo, 8));
        Evaluate(CustCredLmtSchd."Shortcut Dimension 2 Code", GetValueAtCell(RowNo, 9));
        Evaluate(CustCredLmtSchd."Price Group", GetValueAtCell(RowNo, 10));
        Evaluate(CustCredLmtSchd."Customer Discount Group", GetValueAtCell(RowNo, 11));
        Evaluate(CustCredLmtSchd."Item Discount Group", GetValueAtCell(RowNo, 12));
        Evaluate(CustCredLmtSchd."Currency Date", GetValueAtCell(RowNo, 13));
        Evaluate(CustCredLmtSchd."Currency Code", GetValueAtCell(RowNo, 14));
        Evaluate(CustCredLmtSchd."Credit Limit", GetValueAtCell(RowNo, 15));
        CustCredLmtSchd.VALIDATE("Credit Limit");
        Evaluate(CustCredLmtSchd."Payment Terms Code", GetValueAtCell(RowNo, 16));
        Evaluate(CustCredLmtSchd."Schedule Expiry Period", GetValueAtCell(RowNo, 17));
        CustCredLmtSchd.VALIDATE("Schedule Expiry Period");
        CustCredLmtSchd."Created By" := UserId;
        CustCredLmtSchd."Created Date" := CurrentDateTime;
        /*
        IF allinoneCU.CheckcreditlimitApprovalsWorkflowEnabled(CustCredLmtSchd) then begin
            allinoneCU.OnSendcreditlimitForApproval(CustCredLmtSchd);
            CustCredLmtSchd.status := CustCredLmtSchd.Status::"Pending Approval";
        end else*/
        CustCredLmtSchd.status := CustCredLmtSchd.Status::Open;
        CustCredLmtSchd.Modify();
        lineNo2 += 1000;
    end;

    local procedure clearExistingLines()
    var
        CustCredLmtSchd: record "Cust. Cr. Limit Schedule";
        dateVar: Date;
    begin
        Evaluate(dateVar, GetValueAtCell(2, 5));
        CustCredLmtSchd.SetCurrentKey("Start Date");
        CustCredLmtSchd.SetFilter("Start Date", '=%1', dateVar);
        CustCredLmtSchd.SetFilter(Status, '=%1', CustCredLmtSchd.Status::Open);
        if CustCredLmtSchd.FindSet() then CustCredLmtSchd.DeleteAll();
    end;

    Local procedure GetLastLineNo(customerID: Code[20]): Integer
    var
        CustCredLmtSchd: record "Cust. Cr. Limit Schedule";
    begin
        CustCredLmtSchd.RESET;
        CustCredLmtSchd.SetFilter("Customer No.", '%1', customerID);
        CustCredLmtSchd.SetFilter("Line No.", '<>%1', 0);
        IF CustCredLmtSchd.FINDLAST THEN
            EXIT(CustCredLmtSchd."Line No.")
        ELSE
            EXIT(0);
    end;

    procedure GetValueAtCell(RowNo: Integer; ColNo: Integer): Text;
    begin
        IF ExcelBuf1.GET(RowNo, ColNo) THEN
            EXIT(ExcelBuf1."Cell Value as Text");
    end;

    procedure FileNameOnAfterValidate()
    begin
        RequestFile();
    end;

    procedure RequestFile()
    begin
        IF FileName <> '' THEN
            ServerFileNme := FileMgt.UploadFile(Text006, FileName)
        ELSE
            ServerFileNme := FileMgt.UploadFile(Text006, Text00005);

        ValidateServerFileName;
        FileName := FileMgt.GetFileName(ServerFileNme);
    end;

    procedure ValidateServerFileName()
    begin
        IF ServerFileNme = '' THEN BEGIN
            FileName := '';
            SheetName := '';
            ERROR(Text029);
        END;
    end;

    var
        LastLineNo: Integer;
        lineNo2: Integer;
        LineNo: Integer;
        CreditCode: Code[20];
        CustomerCreditApprovals: Record "Customer Credit Approvals";
        NoSeries: Codeunit NoSeriesManagement;
        RecievableSetup: Record "Sales & Receivables Setup";
}

