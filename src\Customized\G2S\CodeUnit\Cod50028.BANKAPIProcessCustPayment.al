//RFC#39 BANK API
//Signed on 31/10/2023
//Published on 11th November 2023
//updated on 15th November 2023
//updated on 20th September 2024
//G2S

/// <summary>
/// Codeunit BANKAPI-Process Cust. Payment (ID 50028).
/// </summary>
codeunit 50028 "BANKAPI-Process Cust. Payment"
{

    var
        GenJnlLineCopy, GenJnlLineCopy2, GenJnlLineCopy1 : Record "Gen. Journal Line";
        BankPymntRecCopy: Record "Bank Payment Notification";
        DocumentNo: Code[50];
        DimSetID: Integer;


    trigger OnRun()
    var
        BankPymntRec, BankPymntRecPost, BankPymtCopy : Record "Bank Payment Notification";
        ErrorLog: Record "Nibss Notifications Error Log";
        RecordExist: Boolean;
        Customer: Record Customer;
        ErrorExist: Boolean;
        Txt0001: Label 'The transaction has been successfully posted';
    begin

        ErrorLog.DeleteAll();
        BankPymntRecPost.Reset();
        BankPymntRecPost.SetCurrentKey("Date Created");
        BankPymntRecPost.SetRange("Duplicate Session ID?", false);
        //BankPymntRecPost.SetRange("Temp. Omit record from Batch", false);
        BankPymntRecPost.SetFilter(Processed, '<>%1', true);
        BankPymntRecPost.SetFilter("Date Posted", '%1', 0DT);
        if BankPymntRecPost.FindFirst() then begin
            repeat
                ErrorExist := false;
                if ValidateBankPaymentRec(BankPymntRecPost) then begin
                    InitGLLines(BankPymntRecPost);
                    ProcessCustPayment(BankPymntRecPost);
                    InsertGLJnlLine(GenJnlLineCopy1, 1);
                    //InsertGLJnlLine(GenJnlLineCopy2, 2);
                    //if PostGenJnlLine(GenJnlLineCopy) then begin
                    if PostGenJnlLine() then begin
                        UpdateBankPymntRec(BankPymntRecPost);
                        ErrorExist := false;
                        Message(Txt0001);
                    end else begin
                        ErrorExist := true;
                    end;
                end else begin
                    ErrorExist := true;
                end;

                //log error if error exist 
                if not ErrorExist then begin
                    //update payment
                    UpdateBankPymntRec(BankPymntRecPost);
                end;
                ErrorExist := false;
            //  end;
            until BankPymntRecPost.Next() = 0;
        end;
    end;



    /// <summary>
    /// ClearExistingGLLines.
    /// </summary>
    procedure InitGLLines(var BankPymnt: Record "Bank Payment Notification")
    var
        GLSetup: Record "General Ledger Setup";
        GenJnlLine: Record "Gen. Journal Line";
        Batch: Record "Gen. Journal Batch";
        GenJnlTemp: Record "Gen. Journal Template";
        NoSeriesmgt: Codeunit NoSeriesManagement;
        //020924 update dimensions
        DefDim: Record "Default Dimension";
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimensionManagement: Codeunit DimensionManagement;
        DimValue: Record "Dimension Value";
    begin
        DimSetID := 0;
        GLSetup.Get;
        // Delete Lines Present on the General Journal Line
        GenJnlLine.Reset;
        GenJnlLine.SetRange(GenJnlLine."Journal Template Name", GLSetup."Notification Nos.");
        GenJnlLine.SetRange(GenJnlLine."Journal Batch Name", GLSetup."Notification Batch Name");
        GenJnlLine.DeleteAll;

        Batch.Init;
        Batch."Journal Template Name" := GLSetup."Notification Nos.";
        Batch.Name := GLSetup."Notification Batch Name";
        Batch.Description := 'Bank API batch';
        if not Batch.Get(Batch."Journal Template Name", Batch.Name) then
            Batch.Insert;

        GenJnlTemp.Reset();
        GenJnlTemp.SetRange(Name, 'BANKAPI');
        if GenJnlTemp.FindFirst() then
            DocumentNo := NoSeriesmgt.GetNextNo(GenJnlTemp."No. Series", TODAY, TRUE);
    end;

    /// <summary>
    /// Process Payment.
    /// </summary>
    /// <param name="BankPymnt">VAR Record "Bank Payment Notification".</param>
    [TryFunction]
    procedure ProcessCustPayment(var BankPymnt: Record "Bank Payment Notification")
    var
        GenJnlLine: Record "Gen. Journal Line";
        LineNo: Integer;
        Cust, CustCopy : Record Customer;
        GnJnlPost: Codeunit "Gen. Jnl.-Post Line";
        CustRespCentre: Record "Customer Resp. Cent. Lines";
        Batch: Record "Gen. Journal Batch";
        GLSetup: Record "General Ledger Setup";
        BankAccount: Record "Bank Account";
        //020924 update dimensions
        DefDim: Record "Default Dimension";
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimensionManagement: Codeunit DimensionManagement;
        DimValue: Record "Dimension Value";
    begin
        // DocumentNo := '';
        BankPymntRecCopy.Copy(BankPymnt);
        GLSetup.Get;

        //DR Customer
        GenJnlLine.Init;
        GenJnlLine."Journal Template Name" := GLSetup."Notification Nos.";
        GenJnlLine."Journal Batch Name" := GLSetup."Notification Batch Name";
        LineNo := 10000;
        //get cust. acc location for GnlJnl dimension
        if Cust.Get(BankPymnt."Customer ID") then begin
            Cust.CalcFields("Accounting Location");
            IF GenJnlLine."Shortcut Dimension 1 Code" = '' THEN
                GenJnlLine.Validate("Shortcut Dimension 1 Code", Cust."Accounting Location");
            IF GenJnlLine."Shortcut Dimension 2 Code" = '' THEN
                GenJnlLine.Validate("Shortcut Dimension 2 Code", 'SLBR');
        end;

        //get cust resp centre
        CustRespCentre.Reset();
        CustRespCentre.SetRange(CustRespCentre."Customer No.", BankPymnt."Customer ID");
        if CustRespCentre.FindFirst() then begin
            GenJnlLine.Validate("Responsibility Center", CustRespCentre."Resp. Center Code");
        end else
            CustRespCentre.TestField("Resp. Center Code");
        //if CustRespCentre."Resp. Center Code" <> '' then
        //    GenJnlLine.Validate("Shortcut Dimension 2 Code", CustRespCentre."Resp. Center Code");
        //030424 commented out cos of a validation already in place
        // if GenJnlLine."Shortcut Dimension 1 Code" = '' then
        //     GenJnlLine.Validate("Shortcut Dimension 1 Code", 'LOS');
        //030424 end
        GenJnlLine."Line No." := LineNo;
        GenJnlLine.Validate("Account Type", GenJnlLine."Account Type"::Customer);
        //GenJnlLine."Account Type" := GenJnlLine."Account Type"::Customer;
        GenJnlLine."Document Type" := GenJnlLine."Document Type"::Payment;
        GenJnlLine."Document No." := DocumentNo;
        GenJnlLine.Validate("Account No.", BankPymnt."Customer ID");
        //GenJnlLine."Account No." := BankPymnt."Customer ID";
        // GenJnlLine.Validate("Posting Date", DT2Date(BankPymnt."Date Entered"));
        GenJnlLine.Validate("Posting Date", Today);
        GenJnlLine.Validate(Narration, BankPymnt.Narration);
        GenJnlLine.Cleared := true;
        GenJnlLine."Online Bank Entry" := true;
        GenJnlLine.Validate(Amount, -BankPymnt.Amount);
        GenJnlLine.Validate("Description 2", BankPymnt.Narration);

        //Get Dimension SetID
        DefDim.Reset();
        DefDim.SetRange("No.", BankPymnt."Customer ID");
        DefDim.SetFilter("Dimension Value Code", '<>%1', '');
        if DefDim.FindFirst() then begin
            repeat
                TempDimSetEntry.Init();
                TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                DimValue.Reset();
                DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                if DimValue.FindFirst() then begin
                    TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                end;
                IF NOT TempDimSetEntry.INSERT
                THEN
                    TempDimSetEntry.MODIFY;
            until DefDim.Next = 0;
            DimSetID := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
        end;
        // //update dimensions 020924
        GenJnlLine.Validate("Dimension Set ID", DimSetID);

        GenJnlLineCopy1.Copy(GenJnlLine);
        //CR Bank
        LineNo += 10000;
        GenJnlLine.Reset();
        GenJnlLine."Journal Template Name" := GLSetup."Notification Nos.";
        GenJnlLine."Journal Batch Name" := GLSetup."Notification Batch Name";
        GenJnlLine."Line No." := LineNo;
        GenJnlLine."Document Type" := GenJnlLine."Document Type"::Payment;
        GenJnlLine."Document No." := DocumentNo;
        GenJnlLine.Validate("Shortcut Dimension 1 Code", cust."Accounting Location");
        if GenJnlLine."Shortcut Dimension 1 Code" = '' then
            GenJnlLine.Validate("Shortcut Dimension 1 Code", 'LOS');
        GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", 'SLBR');
        // GenJnlLine.Validate("Posting Date", DT2Date(BankPymnt."Date Entered"));
        GenJnlLine.Validate("Posting Date", Today);
        //GenJnlLine.Validate("Bal. Account Type", GenJnlLine."Bal. Account Type"::"Bank Account");
        GenJnlLine.Validate(Narration, BankPymnt.Narration);
        GenJnlLine.Validate("Description 2", BankPymnt.Narration);
        GenJnlLine."Online Bank Entry" := True;
        GenJnlLine.Validate("Account Type", GenJnlLine."Account Type"::"Bank Account");
        GenJnlLine.validate(Amount, BankPymnt.Amount);
        //get bal account no
        BankAccount.Reset();
        BankAccount.SetRange("BankAPI Source Bank Code", BankPymnt.SourceBankCode);
        if BankAccount.FindFirst() then
            //GenJnlLine.Validate("Bal. Account No.", BankAccount."No.");
            GenJnlLine.Validate("Account No.", BankAccount."No.");
        // GenJnlLine.Insert();
        //InsetGLJnlLine2(GenJnlLine);
        GenJnlLineCopy2.Copy(GenJnlLine);
    end;

    /// <summary>
    /// InsetGLJnlLine.
    /// </summary>
    /// <param name="GenJnlLine">VAR Record "Gen. Journal Line".</param>
    /// <param name="i">Integer.</param>
    procedure InsertGLJnlLine(var GenJnlLine: Record "Gen. Journal Line"; i: Integer)
    var
        GenJnlLine2, GenJnlLine3 : Record "Gen. Journal Line";
        GLSetup: Record "General Ledger Setup";
    begin
        GLSetup.Get();
        // case i of
        //     1:
        //         begin
        //             //if (GenJnlLine.Count = 0) then begin
        //             GenJnlLine2.Copy(GenJnlLineCopy1);
        //             //end
        //         end;

        //     2:
        //         begin
        //             // if GenJnlLine.Count = 0 then begin
        //             GenJnlLine2.Copy(GenJnlLineCopy2);
        //             //end;
        //         end;
        // end;
        //GenJnlLine2.Insert();
        GenJnlLineCopy1.Insert();
        GenJnlLineCopy2.Insert();

        //GenJnlLine3.Reset();
        //GenJnlLine3.SetRange("Journal Template Name", GLSetup."Notification Nos.");
        //GLSetup."Notification Batch Name";
        //GenJnlLineCopy.copy(GenJnlLine2);
        // GenJnlLineCopy.CopyFilters(GenJnlLine3);
    end;

    /// <summary>
    /// PostGenJnlLine.
    /// </summary>
    // [TryFunction]

    procedure PostGenJnlLine(): Boolean
    var
        PostGL: Codeunit "Gen. Jnl.-Post Line";
        GenJnlPost: Record "Gen. Journal Line";
        PostingCompleted: Boolean;
    begin
        //CODEUNIT.Run(CODEUNIT::"Gen. Jnl.-Post", GenJnl);
        GenJnlPost.Reset();
        GenJnlPost.SetRange("Document No.", DocumentNo);
        if GenJnlPost.findset then begin
            repeat
                PostGL.RunWithCheck(GenJnlPost);
            until GenJnlPost.Next() = 0;
            PostingCompleted := true;
        end;
        if PostingCompleted then
            exit(true);
        exit(false);
    end;

    /// <summary>
    /// GetLastErrorID.
    /// </summary>
    /// <returns>Return value of type Integer.</returns>
    procedure GetLastErrorID(): Integer
    var
        ErrorLog: Record "Nibss Notifications Error Log";
    begin
        if ErrorLog.FindLast() then
            exit(ErrorLog.id + 1)
        else
            exit(1);
    end;

    /// <summary>
    /// GetLastErrorLogNo.
    /// </summary>
    /// <returns>Return value of type Integer.</returns>
    procedure GetLastErrorLogNo(): Integer
    var
        ErrorLog: Record "Nibss Notifications Error Log";
    begin
        if ErrorLog.FindLast() then
            exit(ErrorLog."Error Log No." + 1)
        else
            exit(1);
    end;

    /// <summary>
    /// UpdateBankPymntRec.
    /// </summary>
    /// <param name="BankPymntRec">VAR Record "Bank Payment Notification".</param>
    procedure UpdateBankPymntRec(var BankPymntRec: Record "Bank Payment Notification")
    var
        BankPymntRecToUpdate: Record "Bank Payment Notification";
    begin
        if BankPymntRecToUpdate.get(BankPymntRec.ID) then begin
            //update Notification record
            BankPymntRecToUpdate."Date Posted" := CurrentDateTime();
            BankPymntRecToUpdate."Document No." := DocumentNo;
            BankPymntRecToUpdate.Processed := true;
            //14th Nov 2023
            BankPymntRecToUpdate."Temp. Omit record from Batch" := false;
            BankPymntRecToUpdate.Modify();
        end;
    end;

    // [EventSubscriber(ObjectType::Table, Database::"Bank Payment Notification", 'OnAfterInsertEvent', '', true, true)]
    // local procedure CheckDuplicateSessionID(var Rec: Record "Bank Payment Notification")
    // var
    //     BankPayment: Record "Bank Payment Notification";
    // begin
    //     //check possible duplicate of Session ID from Bank
    //     BankPayment.Reset();
    //     BankPayment.SetRange("Session ID", Rec."Session ID");
    //     BankPayment.SetRange(SourceBankCode, Rec.SourceBankCode);
    //     BankPayment.SetFilter(ID, '<>%1', Rec.ID);
    //     if BankPayment.FindFirst() then begin
    //         BankPayment."Duplicate Session ID?" := true;
    //         BankPayment."Temp. Omit record from Batch" := true;
    //         BankPayment.Modify();
    //     end;

    //     Rec.SetRange("Session ID", BankPayment."Session ID");
    //     BankPayment.SetRange(SourceBankCode, Rec.SourceBankCode);
    //     if BankPayment.FindFirst() then begin
    //         BankPayment."Duplicate Session ID?" := true;
    //         BankPayment."Temp. Omit record from Batch" := true;
    //         BankPayment.Modify();
    //     end;
    // end;

    /// <summary>
    /// ValidateBankPaymentRec.
    /// </summary>
    /// <param name="BankPayment">VAR Record "Bank Payment Notification".</param>
    /// <returns>Return value of type Boolean.</returns>
    procedure ValidateBankPaymentRec(var BankPayment: Record "Bank Payment Notification"): Boolean
    var
        ErrorExist: Boolean;
        Customer: Record Customer;
        NibssRec: Record "Nibss Notifications";
        CustRespC: Record "Customer Resp. Cent. Lines";
        ErrorLog: Record "Nibss Notifications Error Log";
        ErrorTxtLog: Text;
        BankAccount: Record "Bank Account";
    begin
        //check if customer exists or not
        Customer.Reset();
        Customer.SetRange("No.", BankPayment."Customer ID");
        if not Customer.FindFirst() then begin
            InsertBankPaymentErrorLog('Customer No. ' + format(BankPayment."Customer ID") + ' does not exist', BankPayment);
            exit(false);
        end;

        //check if customer has been approved or not
        Customer.Reset();
        Customer.SetRange("No.", BankPayment."Customer ID");
        Customer.SetRange("Approval Status", Customer."Approval Status"::Released);
        if not Customer.FindFirst() then begin
            InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' is yet to be approved', BankPayment);
            exit(false);

        end;
        //gen bus. posting group
        IF Customer.GET(BankPayment."Customer ID") THEN begin
            IF Customer."Gen. Bus. Posting Group" = '' THEN BEGIN
                InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' Gen. Bus. Posting Group is not available', BankPayment);
                exit(false);

            end;
        end;
        //customer posting
        IF Customer.GET(BankPayment."Customer ID") THEN begin
            IF Customer."Customer Posting Group" = '' THEN BEGIN
                InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' customer posting group is not available', BankPayment);
                exit(false);
            end;

        end;

        //customer blocked?
        IF Customer.GET(BankPayment."Customer ID") THEN begin
            IF (Customer.Blocked = Customer.Blocked::All) then BEGIN
                InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' customer is blocked', BankPayment);
                exit(false);
            end;

        end;

        //customer accounting location?
        IF Customer.GET(BankPayment."Customer ID") THEN begin
            IF (Customer."Global Dimension 1 Code" = '') then BEGIN
                InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' accounting location is blank', BankPayment);
                exit(false);
            end;
        end;


        //customer resp center
        //031924 update to check responsibility center before posting
        CustRespC.Reset();
        CustRespC.SETRANGE(CustRespC."Customer No.", BankPayment."Customer ID");
        IF Not CustRespC.FindFirst THEN begin
            InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' responsibilisty center could not be found', BankPayment);
            exit(false);
        end else begin
            if CustRespC."Resp. Center Code" = '' then begin
                InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' responsibility center is blank', BankPayment);
                exit(false);
            end;
        end;
        //031924 end



        //bank account 
        BankAccount.Reset();
        BankAccount.SetRange("BankAPI Source Bank Code", BankPayment.SourceBankCode);
        if not BankAccount.FindFirst() then begin
            InsertBankPaymentErrorLog('Customer No. ' + BankPayment."Customer ID" + ' source bank code is  missing', BankPayment);
            exit(false);

        end;
        exit(true);

    end;

    /// <summary>
    /// InsertBankPaymentErrorLog.
    /// </summary>
    /// <param name="ErrorTxt">VAR Text[1024].</param>
    /// <param name="BankPymntRec">Record "Bank Payment Notification".</param>
    procedure InsertBankPaymentErrorLog(ErrorTxt: Text; var BankPymntRec: Record "Bank Payment Notification")
    var
        ErrorLog: Record "Nibss Notifications Error Log";
        ErrorLog2: Record "Nibss Notifications Error Log";
        BankPaymentCopy2: Record "Bank Payment Notification";
        i, LastID : Integer;

    begin
        CLEAR(i);
        Clear(LastID);
        // ErrorLog2.DeleteAll();

        ErrorLog2.Reset();
        if ErrorLog2.FindLast() then begin
            LastID := ErrorLog2.id
        end else
            LastID := 0;

        ErrorLog2.RESET;
        ErrorLog2.SETRANGE(SessionId, BankPymntRec."Session ID");
        ErrorLog2.SetRange(Customercode, BankPymntRec."Customer ID");
        IF ErrorLog2.FINDLAST THEN
            i := ErrorLog2."Error Log No."
        ELSE
            i := 0;

        ErrorLog.Init();
        ErrorLog."Error Log No." := i + 1;
        //ErrorLog."Error Log No." := 1;
        ErrorLog.Customercode := BankPymntRec."Customer ID";
        ErrorLog.id := LastID + 1;
        ErrorLog.amount := BankPymntRec.Amount;
        ErrorLog.sessionID := BankPymntRec."Session ID";
        ErrorLog.sourceBankCode := BankPymntRec.SourceBankCode;
        ErrorLog.narration := BankPymntRec.Narration;
        ErrorLog.PaymentReference := BankPymntRec."Payment Reference";
        ErrorLog.transactionInitiatedDate := Format(BankPymntRec."Date Entered");
        ErrorLog.Remark := ErrorTxt;
        ErrorLog.dateEntered := BankPymntRec."Date Entered";
        ErrorLog.transactionApprovalDate := Format(CurrentDateTime);
        ErrorLog.dateUpdated := ErrorLog.dateEntered;
        ErrorLog.Insert();

        // //update bankpayment
        // if Bankpaymentcopy2.Get(BankPymntRec.ID) then begin
        //     if not (BankPaymentCopy2."Temp. Omit record from Batch" = true) then begin
        //         BankPaymentCopy2."Temp. Omit record from Batch" := true;
        //         BankPaymentCopy2.Modify();
        //     end;
        // end;
    end;
}
