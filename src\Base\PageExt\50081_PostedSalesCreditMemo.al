pageextension 50081 PostedSalesCreditMemo extends "Posted Sales Credit Memo"
{
    layout
    {
        addafter("Tax Area Code")
        {
            field("Reason Codes";"Reason Codes")
            {
                ApplicationArea=all;
            }
        }
    }
    actions
    {
        modify("Print")
        {
            trigger OnBeforeAction()
            var
                uSrSet: record "User Setup";
            begin
                //b2bpksalecorr12
                //IF "No. Printed" > 1 then begin
                uSrSet.get(UserId);
                IF NOT uSrSet."Reprint Invoiced & Credm Docs" then
                    Error('You do not have permissions to reprint the document.');
                //end
            end;
        }
    }
}