tableextension 50011 ItemJotTabExt extends "Item Journal Line"
{

    fields
    {
        modify("Location Code")
        {
            trigger OnAfterValidate()
            var
                LocationLrec: Record Location;
            begin
                //Balu 05122021 >>
                if LocationLrec.Get("Location Code") then
                    LocationLrec.TestField(Blocked, false);
                //Balu 05122021<<
            end;
        }
        modify("New location code")
        {
            trigger OnAfterValidate()
            var
                LocationLrec: Record Location;
            begin
                //Balu 05122021 >>
                if LocationLrec.Get("New location code") then
                    LocationLrec.TestField(Blocked, false);
                //Balu 05122021<<
            end;
        }
        field(50003; "Manual MRS No."; Code[30])
        {
            DataClassification = CustomerContent;
        }
        field(50004; "MRS No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Previous Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Date PMS Availed"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50008; "FA No."; Code[10])
        {
            DataClassification = CustomerContent;
            //TableRelation=IF ("Reason Code"=CONST(ISSUE)) "Fixed Asset"."No." WHERE ("Global Dimension 1 Code"=FIELD("Shortcut Dimension 1 Code")) ELSE IF ("Reason Code"=CONST(RETURN)) "Fixed Asset".No. WHERE (Global Dimension 1 Code=FIELD(Shortcut Dimension 1 Code)) ELSE IF (Reason Code=CONST(STOCKADJ)) "Fixed Asset".No. WHERE (Global Dimension 1 Code=FIELD(Shortcut Dimension 1 Code)) ELSE IF (Reason Code=CONST()) "Fixed Asset".No. WHERE (Global Dimension 1 Code=FIELD(Shortcut Dimension 1 Code));
            trigger OnValidate()
            var
                FA: Record "Fixed Asset";
            begin
                IF ("Maintenance Code" <> '') THEN begin
                    IF MaintenanceRec.GET("Maintenance Code") THEN
                        IF MaintenanceRec."PMS Maintenance" THEN BEGIN
                            KmReading := 0;
                            //MaintenanceLedgerEntry.SETCURRENTKEY("FA No.","Current KM Reading");
                            //FA No.,Date PMS Availed  sorting order changed by RKD
                            MaintenanceLedgerEntry.SETCURRENTKEY("FA No.", "Date PMS Availed_");
                            MaintenanceLedgerEntry.SETRANGE("FA No.", "FA No.");
                            MaintenanceLedgerEntry.SETFILTER("Date PMS Availed_", '%1..%2', 0D, "Date PMS Availed");
                            MaintenanceLedgerEntry.SETFILTER("Current Meter Reading", '<>%1', 0);
                            IF MaintenanceLedgerEntry.FINDLAST THEN
                                KmReading := MaintenanceLedgerEntry."Current Meter Reading";
                            //"Previous Km Reading" := MaintenanceLedgerEntry."Current KM Reading";
                            IF FA.GET("FA No.") THEN;
                            KmReadingRec.RESET;
                            KmReadingRec.SETRANGE(KmReadingRec."Card No.", FA."PMS Card No.");
                            KmReadingRec.SETFILTER(KmReadingRec."Posting Date", '%1..%2', 20160101D, CALCDATE('-1D', "Date PMS Availed"));
                            IF KmReadingRec.FINDLAST THEN BEGIN
                                IF KmReading > KmReadingRec."Current Mileage" THEN
                                    "Previous Km Reading" := KmReading
                                ELSE
                                    "Previous Km Reading" := KmReadingRec."Current Mileage";
                            END
                            ELSE
                                "Previous Km Reading" := KmReading;
                        END;
                    ItemJnlLine.RESET;
                    ItemJnlLine.SETRANGE("Document No.", "Document No.");
                    ItemJnlLine.SETRANGE("FA No.", "FA No.");
                    IF ItemJnlLine.FINDLAST THEN BEGIN
                        IF ItemJnlLine."Km Reading" > "Previous Km Reading" THEN
                            "Previous Km Reading" := ItemJnlLine."Previous Km Reading";
                    END;

                end;
            end;

        }
        field(50009; "Maintenance Code"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = Maintenance.Code;
            trigger OnValidate()
            var
                FA: Record "Fixed Asset";
            begin
                IF ("FA No." <> '') THEN BEGIN
                    IF MaintenanceRec.GET("Maintenance Code") THEN
                        IF MaintenanceRec."PMS Maintenance" THEN BEGIN
                            TESTFIELD("Date PMS Availed");
                            KmReading := 0;
                            //MaintenanceLedgerEntry.SETCURRENTKEY("FA No.","Current KM Reading");
                            //FA No.,Date PMS Availed  sorting order changed by RKD
                            MaintenanceLedgerEntry.SETCURRENTKEY("FA No.", "Date PMS Availed_");

                            MaintenanceLedgerEntry.SETRANGE("FA No.", "FA No.");
                            MaintenanceLedgerEntry.SETFILTER("Date PMS Availed_", '%1..%2', 0D, "Date PMS Availed");
                            MaintenanceLedgerEntry.SETFILTER("Current Meter Reading", '<>%1', 0);
                            IF MaintenanceLedgerEntry.FINDLAST THEN
                                KmReading := MaintenanceLedgerEntry."Current Meter Reading";
                            //"Previous Km Reading" := MaintenanceLedgerEntry."Current KM Reading";

                            IF FA.GET("FA No.") THEN;
                            KmReadingRec.RESET;
                            KmReadingRec.SETRANGE(KmReadingRec."Card No.", FA."PMS Card No.");
                            KmReadingRec.SETFILTER(KmReadingRec."Posting Date", '%1..%2', 20160101D, CALCDATE('-1D', "Date PMS Availed"));
                            IF KmReadingRec.FINDLAST THEN BEGIN
                                IF KmReading > KmReadingRec."Current Mileage" THEN
                                    "Previous Km Reading" := KmReading
                                ELSE
                                    "Previous Km Reading" := KmReadingRec."Current Mileage";
                            END
                            ELSE
                                "Previous Km Reading" := KmReading;
                            ItemJnlLine.RESET;
                            ItemJnlLine.SETRANGE("Document No.", "Document No.");
                            ItemJnlLine.SETRANGE("FA No.", "FA No.");
                            IF ItemJnlLine.FINDLAST THEN BEGIN
                                IF ItemJnlLine."Km Reading" > "Previous Km Reading" THEN
                                    "Previous Km Reading" := ItemJnlLine."Km Reading";
                            END;
                        END;
                END;

                IF ("Maintenance Code" = '') THEN BEGIN
                    CLEAR("Km Reading");
                    CLEAR("Previous Km Reading");
                END;
            end;
        }
        field(50010; "Br. Cust. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50011; "Cust. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50012; "Disposal Type"; Option)
        {
            Description = 'SAA3.0';
            OptionMembers = " ",Disposals,"Exp-FG","Exp-RM","Exp-PM",MKTRTNS;
            DataClassification = CustomerContent;
        }
        field(50013; "FA Posting Type"; Option)
        {
            Description = 'SAA3.0';
            OptionCaption = '" ,Acquisition Cost,Depreciation,Write-Down,Appreciation,Custom 1,Custom 2,Disposal,Maintenance,Salvage Value,Diesel,Petrol,capital work in progress"';
            OptionMembers = " ","Acquisition Cost",Depreciation,"Write-Down",Appreciation,"Custom 1","Custom 2",Disposal,Maintenance,"Salvage Value",Diesel,Petrol,"capital work in progress";
            DataClassification = CustomerContent;
        }
        field(50015; "Production Batch No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //PhaniFeb182021>>
        field(50080; "Description 2"; text[50])
        {
            DataClassification = customercontent;
        }
        field(50081; "Qty on Inventory"; Decimal)
        {
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" WHERE("Item No." = field("Item No."), "Location Code" = field("Location Code")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(50083; "Last Direct Cost"; Decimal)
        {
            CalcFormula = lookup(Item."Last Direct Cost" WHERE("No." = field("Item No.")));
            Editable = false;
            FieldClass = FlowField;
        }
        //PhaniFeb182021<<
        field(50084; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50085; "Clearing File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50086; "Available Qty"; Decimal)
        {
            /*CalcFormula = sum ("Item Ledger Entry".Quantity where("Item No." = field("Item No."), "Global Dimension 1 Code" = field("Shortcut Dimension 1 Code"), "Global Dimension 2 Code" = field("Shortcut Dimension 2 Code"), "Location Code" = field("Location Code"), "Variant Code" = field("Variant Code")));*/
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Item No."), "Global Dimension 1 Code" = field("Shortcut Dimension 1 Code"), "Location Code" = field("Location Code"), "Variant Code" = field("Variant Code")));
            Editable = false;
            FieldClass = FlowField;
        }
        //Fix12Jul2021CWIP>>
        field(50087; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50088; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Header"."No." WHERE(Status = CONST(Released), "Document Type" = CONST(Capex));
        }
        field(50089; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Line"."Line No." WHERE("Document No." = FIELD("Capex No."));
            trigger OnValidate()
            var
                PurchLn: Record "Purchase Line";
            begin
                Rec.TestField("Capex No.");
                CheckCapexBudget();
            end;
        }
        //Fix12Jul2021CWIP<<
        // <<<<<< G2S 070224 Capex_Budget_Control 
        modify(Amount)
        {
            trigger OnBeforeValidate()
            begin
                CheckCapexBudget()
            end;
        }
        // <<<<<< G2S 070224 Capex_Budget_Control 
    }
    [IntegrationEvent(false, false)]
    local procedure OnMoveItemJournalLine(var Sender: record "Item Journal Line"; ToRecordID: RecordId)
    begin
    end;

    // <<<<<< G2S 070224 Capex_Budget_Control 
    procedure CheckCapexBudget()
    // >>>>>> G2S 290623 RFC#11
    var
        BudLine: Record "Budget Line";
        RemainingBudAmt: Decimal;
    begin
        RemainingBudAmt := 0;
        BudLine.Reset();
        BudLine.SetRange("Document No.", "Capex No.");
        BudLine.SetRange("Line No.", "Capex Line No.");
        // if "Line Amount" <> 0 then begin
        if BudLine.FindFirst() then begin
            BudLine.CalcFields("Budget Utilized");
            RemainingBudAmt := BudLine."Amount(LCY)" - BudLine."Budget Utilized";
            if (RemainingBudAmt > 0) then begin
                //amount greater than remaining bud amt. prompt error message
                if not (Amount <= RemainingBudAmt) then begin
                    Error(Text003);
                end;
            end else
                //budget fully utilized. prompt error message
                Error(Text002, "Capex Line No.");
        end;
        // end else
        //line amount yet to be populated. prompt error message
        // Error(Text001);
    end;
    // >>>>>> G2S 290623 RFC#11
    // <<<<<< G2S 070224 Capex_Budget_Control 

    var
        ItemJnlLine: Record "Item Journal Line";
        MaintenanceLedgerEntry: Record "Maintenance Ledger Entry";
        MaintenanceRec: Record Maintenance;
        KmReadingRec: Record "Original PMS Statement";
        KmReading: Decimal;
        Text003: Label 'Line Amount is greater than the budget balance. Kindly review the line amount value again';
        Text002: Label 'Budget for the Capex Line No.: %1 has been fully utilized.';


}