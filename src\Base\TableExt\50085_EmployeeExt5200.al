tableextension 50085 EmployeeExt extends Employee
{

    fields
    {
        field(50000; "Bank Name"; Enum BankName)
        {
            DataClassification = CustomerContent;
        }
        modify("Search Name")
        {
            trigger OnAfterValidate() //PKON22FE11-CR220021
            var
                cu1: Codeunit Codeunit1;
            begin
                cu1.UpdateStaffName(rec);
            end;
        }

    }

}