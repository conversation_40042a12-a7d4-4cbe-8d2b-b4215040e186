pageextension 50155 WareRolcenExt extends "Warehouse Manager Role Center"
{
    layout
    {

    }

    actions
    {
        //BaluonMar8 2022>>
        addafter(Group)
        {
            group("Approve Pages")
            {
                action("Request to Approve")
                {
                    ApplicationArea = all;
                    Caption = 'Request to Approves';
                    RunObject = page "Requests to Approve";
                }
                action("Approval Entries View")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries View';
                    RunObject = page "Approval Entries View Page";
                }
                action("Approval Entries")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries';
                    RunObject = page "Approval Entries";
                }
            }
        }//BaluonMar8 2022<<

        addafter("Group20")
        {
            group("Customized Pages")
            {
                action("Local Purchase Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Local Purchase Orders";
                }
                action("Import Purchase Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Import Purchase Orders";
                }
                action("Local Sales Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Local Sales Orders";
                }
                action("Direct Sales Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Direct Sales Orders";
                }
                action("Export Sales Orders")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Export Sales Orders";
                }

                group("Material Requisition")
                {
                    action("Material Requisitions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions.";
                    }
                    action("Material Requisitions-Released")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions-Released";
                    }
                    action("Production MRS List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production MRS List";
                    }
                    action("Approved Production MRS")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Approved Production MRS List";
                    }
                    action("Material Requisitions-Closed")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions- Closed";
                    }
                    action("Material Req Ack")
                    {
                        Caption = 'Material Requisation Release Ack';
                        ApplicationArea = all;
                        RunObject = page "Material Requ-Rel Ack";
                    }
                }
                group("Purchase Requisition")
                {
                    action("Purchase Requisitions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Purchase Requisitions";
                    }
                    action("Purchase Requisitions-Released")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Purchase Requisitions-Released";
                    }
                    action("Purchase Requisitions-Closed")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Purchase Requisitions-Closed";
                    }

                }
                group("Quotation Comparision")
                {
                    action("Quotation Comparisions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Quotation Comparisions";
                    }
                }
                group("Petrol Management System")
                {
                    action("PMS Cards")
                    {
                        ApplicationArea = Planning;
                        RunObject = page PMSManagement;
                    }
                    action("PMS Vouchers")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "PMS Voucher List";
                    }
                }
            }
            group("Customized Reports")
            {
                action("Standard Cost")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Standard Cost";
                }
                action("Item-Lot-Aging Report")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Item-Lot-Aging Report";
                }
                action("RM - PM Availability Plan")
                {
                    ApplicationArea = Planning;
                    RunObject = report "RM - PM Availability Plan";
                }
                action("Item QR Printing")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Item QR Printing";
                }
                action("RES. DIESEL LOADING ADVICE")
                {
                    ApplicationArea = Planning;
                    RunObject = report "RES. DIESEL LOADING ADVICE";
                }
                action("Stock Exposure Sheet-SNOP")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Stock Exposure Sheet-SNOP";
                }
                action("RSF By Variant_SNOP")
                {
                    ApplicationArea = Planning;
                    RunObject = report "RSF By Variant_SNOP";
                }
                action("Summary Item Valuation")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Summary Item Valuation";
                }
                action("item reoder list")
                {
                    ApplicationArea = Planning;
                    RunObject = report "item reoder list";
                }
                action("Partial dispatchs")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Partial dispatchs";
                }
                action("Loading Slip")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Loading Slip";
                }
                action("Dispatched Gate Pass")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Dispatched Gate Pass";
                }
                action("Non Returnable GatePass")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Non Returnable GatePass";
                }
                action("Returnable GatePass")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Returnable GatePass";
                }
                action("Container Details")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Container Details";
                }
                action("Storage capacity Planning")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Supply Chain Disp. Bud.";
                }
                action("RES. VEH. GATEPASS/WAYBILL")
                {
                    ApplicationArea = Planning;
                    RunObject = report "RES. VEH. GATEPASS/WAYBILL";
                }
                action("Supply Chain Report_SNOP")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Supply Chain Report_SNOP";
                }
                action("Upload Item Journals -Prod")
                {
                    ApplicationArea = all;
                    RunObject = xmlport "Upload Item Journals -Prod";
                }
            }
            group(PMS)
            {
                action("PMS Voucher")
                {
                    Caption = 'PMS Voucher';
                    ApplicationArea = all;
                    RunObject = page "PMS Voucher List";
                }
            }

        }
    }
    var
        myInt: Integer;
}