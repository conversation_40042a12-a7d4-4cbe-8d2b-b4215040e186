page 50416 "Channel Card"
{
    // version Channel

    PageType = Card;
    SourceTable = Channels;
    UsageCategory = Tasks;
    ApplicationArea = All;


    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Code"; Code)
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Report Serial No"; "Report Serial No")
                {
                    ApplicationArea = All;
                }
                field("Reporting Channel"; "Reporting Channel")
                {
                    ApplicationArea = All;
                }
                field("Qty. Wise Channels"; "Qty. Wise Channels")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Sales Team")
            {
                Caption = 'Sales Team';
                action(Action1000000011)
                {
                    Caption = 'Sales Team';
                    RunObject = Page "Channel Sales Team";
                    RunPageLink = "Channel No." = FIELD(Code);
                    ApplicationArea = All;
                }
            }
        }
    }
}

