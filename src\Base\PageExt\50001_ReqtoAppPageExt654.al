pageextension 50001 ReqtoAppPageExt654 extends "Requests to App<PERSON>"
{
    layout
    {
        addafter(ToApprove)
        {
            field("Document No."; "Document No.")
            {
                ApplicationArea = all;
            }
        }
    }

    actions
    {
        addafter(AllRequests)
        {
            action("ChangeLogReport")
            {
                Caption = 'Change Log Entries';
                ApplicationArea = all;
                Image = Report;
                trigger OnAction()
                var
                    CLE: Record "Change Log Setup (Table)";
                begin
                    CLE.Reset();
                    CLE.SetRange("Table No.", "Table ID");
                    if CLE.FindFirst() then
                        Report.Run(508, true, false, CLE);
                end;
            }
        }
        addafter(Delegate)
        {
            action("Digital Signature")
            {
                ApplicationArea = ALL;
                Image = Signature;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                trigger OnAction()
                var
                    UserSetup: Record "User Setup";
                    PurchSetup: Record "Share Point Interagtion Setup";
                    URL: Text;
                    PurchaseHdr: Record "Purchase Header";
                begin
                    if (("Table ID" = Database::"Purchase Header") and ("Document Type" = "Document Type"::Order) and (PurchaseHdr.get("Document Type"::Order, "Document No."))
                        and ((PurchaseHdr."Purchase Type" = PurchaseHdr."Purchase Type"::Local) or (PurchaseHdr."Purchase Type" = PurchaseHdr."Purchase Type"::Import))) or ("Table ID" = Database::"Budget Header") then begin
                        UserSetup.Get(UserId);
                        UserSetup.TestField("E-Mail");
                        PurchSetup.Get();
                        if (PurchSetup."Purchase Date" <> 0D) and (DT2Date("Date-Time Sent for Approval") < PurchSetup."Purchase Date") then//FIX01Dec2021
                            exit;
                        URL := StrSubstNo(PurchSetup.URL, "Document No.", UserSetup."E-Mail");
                        Hyperlink(URL);
                    end else
                        Error('Invalid Document for digital signature');
                end;
            }
        }
    }

}