//RFC Outreach version #2065 25th Sep., 2024
/// <summary>
/// Codeunit JsonIntegration-ProductTran (ID 50027).
/// </summary>
codeunit 50027 "Json Integration-Product Tran"
{
    trigger OnRun()
    var
        ProdTranLogList: Record "Product Transaction Log";
        ProdTranLogListCopy: Record "Product Transaction Log";
    begin

        ProdTranLogList.Reset();
        ProdTranLogList.SetRange("Temp. Omit Rec", false);
        ProdTranLogList.SetRange(sentStatus, false);
        if ProdTranLogList.FindFirst() then
            repeat
                //260723 end update for manufacturing date
                if ProdTranLogListCopy.get(ProdTranLogList.ID) then begin
                    ProdTranLogListCopy."Date Sent to Outreach" := Today();
                    ProdTranLogListCopy."DateTime Sent to Outreach" := CurrentDateTime;
                    ProdTranLogListCopy.Modify();
                end;
                sendBatchProductJSON(ProdTranLogList);
            until ProdTranLogList.Next() = 0;
    end;


    /// <summary>
    /// sendData.
    /// </summary>
    /// <param name="json">JsonObject.</param>
    /// <param name="url">Text.</param>
    /// <returns>Return value of type Boolean.</returns>
    procedure sendData(json: Text; url: Text): Text
    var
        httpClientVar: HttpClient;
        httpRequestMessageVar: HttpRequestMessage;
        httpResponseMessageVar: HttpResponseMessage;
        httpContentVar: HttpContent;
        jsonObj: JsonObject;
        jsonObj1: JsonObject;
        jsonArr: JsonArray;
        jsonTok: JsonToken;
        jsonStatusToken: JsonToken;
        myContent, responseText, status : Text;
        i: Integer;
        sent: Boolean;
        HttpHeadersContent: HttpHeaders;

    begin
        httpResponseMessageVar.Headers.Clear();
        clear(httpResponseMessageVar);
        clear(httpRequestMessageVar);
        clear(httpContentVar);
        clear(httpClientVar);
        clear(HttpHeadersContent);
        httpContentVar.WriteFrom(json);
        httpContentVar.GetHeaders(HttpHeadersContent);
        HttpHeadersContent.Remove('Content-Type');
        HttpHeadersContent.Add('Content-Type', 'application/json');
        httpClientVar.SetBaseAddress(URL);
        httpClientVar.Post(url, httpContentVar, httpResponseMessageVar);

        if (httpResponseMessageVar.IsSuccessStatusCode) then begin
            httpResponseMessageVar.Content.ReadAs(myContent);
            // Message('response: %1 with code: %2', myContent, httpResponseMessageVar.HttpStatusCode);
            exit(myContent);
        end
        else begin
            sent := false;
            httpResponseMessageVar.Content.ReadAs(myContent);
            exit(myContent);
        end;
    end;


    /// <summary>
    /// createBatchProductJSON.
    /// </summary>
    /// <param name="ProductLog">Record "Product Transaction Log".</param>
    /// <returns>Return value of type Text.</returns>
    procedure sendBatchProductJSON(ProductLog: Record "Product Transaction Log"): Text
    var
        //productLog: Record "Product Transaction Log";
        itemObj: JsonObject;
        jsonArr: JsonArray;
        content: Text;
        jsonIntegration: Codeunit "Json Integration";
        jsonObj, responseJsonObj, detailedJSONObject : JsonObject;
        responseArray: JsonArray;
        jsonTok, responseToken, statusToken, prodToken, remarkToken : JsonToken;
        data, url, response, detailedResponse : Text;
        boolStatus, TxnErrCheckStatus : Boolean;
        i: Integer;
    begin
        itemObj.Add('prod_code', '');
        itemObj.Add('prod_custom_code', '');
        itemObj.Add('prod_name', '');
        itemObj.Add('prod_short_name', '');
        itemObj.Add('prod_cbb_volume', '');
        itemObj.Add('prod_pack_volume', '');
        itemObj.Add('product_type_code', '');
        itemObj.Add('product_type_name', '');
        itemObj.Add('prod_cat_code', '');
        itemObj.Add('prod_cat_name', '');
        itemObj.Add('brand_code', '');
        itemObj.Add('brand_name', '');
        itemObj.Add('sub_brand_code', '');
        itemObj.Add('sub_brand_name', '');
        itemObj.Add('sku_group_code', '');
        itemObj.Add('sku_group_name', '');
        itemObj.Add('prod_hsn_code', '');
        itemObj.Add('comp_code', '');
        itemObj.Add('tax1perc', '');
        itemObj.Add('tax2perc', '');
        itemObj.Add('tax3perc', '');
        // productLog.Reset();
        // productLog.SetRange();
        //productLog.SetRange(sentStatus, false);
        //if (productLog.FindFirst()) then begin
        //   repeat
        itemObj.Replace('prod_code', productLog."No.");
        itemObj.Replace('prod_custom_code', '');
        itemObj.Replace('prod_name', productLog.Description);
        itemObj.Replace('prod_short_name', productLog.prod_short_name);
        itemObj.Replace('prod_cbb_volume', productLog.prod_cbb_volume);
        itemObj.Replace('prod_pack_volume', '');
        itemObj.Replace('product_type_code', productLog.product_type_code);
        itemObj.Replace('product_type_name', productLog.product_type_name);
        itemObj.Replace('prod_cat_code', productLog.prod_cat_code);
        itemObj.Replace('prod_cat_name', productLog.prod_cat_name);
        itemObj.Replace('brand_code', productLog.brand_code);
        itemObj.Replace('brand_name', productLog.brand_name);
        itemObj.Replace('sub_brand_code', productLog.sub_brand_code);
        itemObj.Replace('sub_brand_name', productLog.sub_brand_name);
        itemObj.Replace('sku_group_code', productLog.sku_group_code);
        itemObj.Replace('sku_group_name', productLog.sku_group_name);
        itemObj.Replace('prod_hsn_code', '');
        itemObj.Replace('comp_code', CompanyName);
        itemObj.Replace('tax1perc', format(productLog.tax1perc));
        itemObj.Replace('tax2perc', '0');
        itemObj.Replace('tax3perc', '0');
        jsonArr.Add(itemObj.Clone());
        //until productLog.Next() = 0;
        jsonObj.Add('PRODUCTMASTER', jsonArr);
        jsonObj.WriteTo(content);

        url := getProductEndPoint();
        if (url <> '') then
            response := jsonIntegration.sendData(content, url)
        else
            Message('You have not setup the Endpoint Url yet!');

        if (response <> '') then begin
            responseJsonObj.ReadFrom(response);
            responseJsonObj.Get('PRODUCTMASTERRESPONSE', jsonTok);
            responseArray := jsonTok.AsArray();

            for i := 0 to (responseArray.Count() - 1) do begin
                responseArray.Get(i, responseToken);
                responseToken.WriteTo(detailedResponse);
                detailedJSONObject.ReadFrom(detailedResponse);
                detailedJSONObject.Get('status', statusToken);
                detailedJSONObject.Get('prod_code', prodToken);
                detailedJSONObject.Get('remark', remarkToken);
                //31 July 2023
                if (statusToken.AsValue().AsText() = '1') then begin
                    boolStatus := true;
                    TxnErrCheckStatus := false;
                end
                else begin
                    boolStatus := false;
                    TxnErrCheckStatus := true;
                end;

                //updateProductLog(prodToken.AsValue().AsText(), boolStatus, remarkToken.AsValue().AsText(),TxnErrCheckStatus);

                //31 July 2023 resolve invalid JSON Token
                if ((not (prodToken.AsValue().IsNull)) and (not (remarkToken.AsValue().IsNull))) then
                    updateProductLog(prodToken.AsValue().AsText(), boolStatus, remarkToken.AsValue().AsText(), TxnErrCheckStatus);
                if ((prodToken.AsValue().IsNull)) and (not (remarkToken.AsValue().IsNull)) then
                    updateProductLog('', boolStatus, remarkToken.AsValue().AsText(), TxnErrCheckStatus);
                if ((not (prodToken.AsValue().IsNull)) and ((remarkToken.AsValue().IsNull))) then
                    updateProductLog(prodToken.AsValue().AsText(), boolStatus, '', TxnErrCheckStatus);
                //end invalid JSON
            end;
        end;
        // else
        //     Message('Response is empty');

        Message(content);
        exit(content);
        //end;
    end;

    /// <summary>
    /// updateShipmentLog.
    /// </summary>
    /// <param name="shipmentNo">Text.</param>
    /// <param name="status">Text.</param>
    /// <param name="remark">Text.</param>
    /// <param name="TxnErrCheck">Boolean.</param>
    // procedure updateShipmentLog(shipmentNo: Text; status: Boolean; remark: Text; TxnErrCheck: Boolean)
    // var
    //     shipmentLog: Record "Shipment Transactions Hdr Log";
    // begin
    //     shipmentLog.reset();
    //     shipmentLog.SetRange("No.", shipmentNo);
    //     if shipmentLog.FindFirst() then begin
    //         // shipmentLog.Init();
    //         shipmentLog.ModifyAll("Sent Status", status);
    //         shipmentLog.ModifyAll(Remark, remark);
    //         shipmentLog.ModifyAll("Temp. Omit Rec", TxnErrCheck);
    //         shipmentLog.ModifyAll("DateTime Sent to Outreach", CurrentDateTime);
    //         shipmentLog.ModifyAll("Date Sent to Outreach", Today());
    //     end;
    //     shipmentLog.Reset();
    // end;

    /// <summary>
    /// updateProductLog.
    /// </summary>
    /// <param name="productNo">Text.</param>
    /// <param name="status">Boolean.</param>
    /// <param name="remark">Text.</param>
    /// <param name="TxnErrCheck">Boolean.</param>
    procedure updateProductLog(productNo: Text; status: Boolean; remark: Text; TxnErrCheck: Boolean)
    var
        productLog: Record "Product Transaction Log";
    begin
        productLog.Reset();
        productLog.SetRange("No.", productNo);
        if productLog.FindFirst() then begin
            //productLog.Init();
            productLog.ModifyAll(sentStatus, status);
            productLog.ModifyAll(Remark, remark);
            productLog.ModifyAll("Temp. Omit Rec", TxnErrCheck);
            productLog.ModifyAll("DateTime Sent to Outreach", CurrentDateTime);
            productLog.ModifyAll("Date Sent to Outreach", Today());
        end;
        productLog.Reset();
    end;

    /// <summary>
    /// getProductEndPoint.
    /// </summary>
    /// <returns>Return value of type Text.</returns>
    procedure getProductEndPoint(): Text
    var
        urlEndPoint: Record "End Point URL Setup";
    begin
        urlEndPoint.SetRange("Product End Point URL");
        if (urlEndPoint.FindFirst()) then
            exit(urlEndPoint."Product End Point URL");
    end;

    /// <summary>
    /// getShipmentEndPoint.
    /// </summary>
    /// <returns>Return value of type Text.</returns>
    procedure getShipmentEndPoint(): Text
    var
        urlEndPoint: Record "End Point URL Setup";
    begin
        urlEndPoint.SetRange("Shipment End Point URL");
        if (urlEndPoint.FindFirst()) then
            exit(urlEndPoint."Shipment End Point URL");
    end;

    local procedure DeleteSpecialChars(var InitialText: Text[100]): Code[100]
    var
        AllowedChars: Text;
        FormattedChars: Text;
        OldXtr: Label 'Old Lot Value: %1';
        NewXtr: Label 'New Lot Value: %2';
    begin
        AllowedChars := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_0123456789';
        FormattedChars := DelChr(InitialText, '=', DELCHR(InitialText, '=', AllowedChars));
        if FormattedChars <> InitialText then
            Message(OldXtr + '; ' + NewXtr, InitialText, FormattedChars);
        exit(FormattedChars);
    end;

    local procedure DeleteSpecialChars2(var InitialText: Code[100]): Code[100]
    var
        AllowedChars: Text;
        FormattedChars: Text;
        OldXtr: Label 'Old Lot Value: %1';
        NewXtr: Label 'New Lot Value: %2';
    begin
        AllowedChars := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_0123456789';
        FormattedChars := DelChr(InitialText, '=', DELCHR(InitialText, '=', AllowedChars));
        if FormattedChars <> InitialText then
            Message(OldXtr + '; ' + NewXtr, InitialText, FormattedChars);
        exit(FormattedChars);
    end;


    // July 31 2023
    //Email Notification for failed Job Queue run
    // [EventSubscriber(ObjectType::Table, Database::"Job Queue Entry", 'OnAfterModifyEvent', '', true, true)]
    // local procedure CheckJobQueueNotification(var Rec: Record "Job Queue Entry")
    // var
    //     SMTPMailSetup: Record "SMTP Mail Setup";
    //     SMTPMail: Codeunit "SMTP Mail";
    //     SenderMail: Text;
    //     Recipients: List of [Text];
    //     RecipientsCC: List of [Text];
    //     Subject: text;
    //     UserSetup: Record "User Setup";
    //     CustomSetup: Record "Custom Setup";

    // begin
    // Rec.SetRange("Object Type to Run", Rec."Object Type to Run"::Codeunit);
    // Rec.SetRange("Object ID to Run", 50027);
    // Rec.SetRange(Status, Rec.Status::Error);
    // //Rec.SetFilter(Status, '%1|%2', Rec.Status::Error, Rec.Status::Finished);
    // if Rec.FindFirst() then begin
    //     UserSetup.Reset();
    //     Usersetup.SetRange("Can rec. Outreach Job Notif?", true);
    //     UserSetup.SetFilter(UserSetup."E-Mail", '<>%1', '');
    //     if UserSetup.findset then
    //         repeat
    //             Recipients.Add(UserSetup."E-Mail")
    //     until UserSetup.next() = 0;
    //     //05/08/23 dist email 
    //     CustomSetup.Reset();
    //     CustomSetup.SetRange(Category, CustomSetup.Category::Outreach);
    //     if CustomSetup.FindFirst() then begin
    //         Recipients.Add(CustomSetup."Outreach Dist. Email");
    //     end;
    //     //end
    //     SMTPMailSetup.get();
    //     SenderMail := SMTPMailSetup."User ID";
    //     Subject := 'PRODUCT Job Queue Error Notification';
    //     SMTPMail.CreateMessage('CHI ERP', SenderMail, Recipients, Subject, '', TRUE);
    //     //SMTPMail.AddBCC();
    //     SMTPMail.AppendBody('Hi Team,');
    //     SMTPMail.AppendBody('<BR><BR>');
    //     SMTPMail.AppendBody('You are registered to receive notifications related to CHI.');
    //     SMTPMail.AppendBody('<BR><BR>');
    //     SMTPMail.AppendBody('This is a message to notify you that an error occurred while the job queue for pushing transactions to Outreach occurred. Please see error description below');
    //     SMTPMail.AppendBody('<BR><BR>');
    //     SMTPMail.AppendBody(Rec."Error Message");
    //     SMTPMail.AppendBody('<BR><BR>');
    //     SMTPMail.AppendBody('Regards,');
    //     SMTPMail.AppendBody('<BR>');
    //     SMTPMail.AppendBody('CHI Limited');
    //     SMTPMail.Send();
    // end;

    // //end;

}
//RFCOutreachAPIGo2solveJuly2023<<<<<<
