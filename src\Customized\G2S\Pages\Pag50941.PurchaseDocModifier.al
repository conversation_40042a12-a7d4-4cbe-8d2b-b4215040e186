//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
page 50941 "Purchase Doc Modifier"
{
    Caption = 'Purchase Doc Modifier';
    PageType = Card;
    SourceTable = "Purchase Doc Modifier";

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Record Type"; "Record Type")
                {
                    ShowMandatory = true;
                    Editable = FieldEditable;
                    ApplicationArea = All;

                    trigger OnValidate()
                    begin
                        CurrPage."Purchase Doc Modifier Line".Page.SetVisibleField();
                    end;
                }
                field("Document type"; "Document type")
                {
                    ShowMandatory = true;
                    Editable = FieldEditable;
                    ApplicationArea = All;
                }
                field("Document No."; "Document No.")
                {
                    ShowMandatory = true;
                    Editable = FieldEditable;
                    ApplicationArea = All;
                }
                field("Vendor No."; "Vendor No.")
                {
                    Editable = FieldEditable;
                    ApplicationArea = All;
                }
                field("Vendor Name"; "Vendor Name")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    Editable = false;
                    StyleExpr = StatusColor;
                    ApplicationArea = All;

                    trigger OnValidate()
                    begin
                        if Rec.Status in [Status::"Pending Approval", Status::Released] then begin
                            CurrPage.Editable := false;
                            lineEditable := false;
                        end else
                            lineEditable := true;
                        CurrPage.Update(FALSE);
                    end;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    Visible = false;
                    ShowMandatory = true;
                    Editable = FieldEditable;
                    ApplicationArea = All;
                }
                field("Cc Code"; "Cc Code")
                {
                    ShowMandatory = true;
                    Caption = 'Cc Code';
                    Editable = FieldEditable;
                }
                field("Document Modified"; "Document Modified")
                {
                    Editable = false;
                    Caption = 'Purchase Document Modified';
                    ApplicationArea = All;

                    trigger OnValidate()
                    begin
                        if Rec."Document Modified" then begin
                            Rec.Validate(Status, Rec.Status::Archived);
                            CurrPage.Close();
                        end;
                    end;
                }
            }
            part("Purchase Doc Modifier Line"; "Purchase Doc Modifier Line")
            {
                SubPageLink = "Modifier Code" = field("No."), "Document type" = FIELD("Document type"), TableID = FIELD(TableID),
                              "Record Type" = FIELD("Record Type");
                SubPageView = SORTING("Modifier Code", TableID, "Record Type", "Line No.");
                ApplicationArea = All;
                UpdatePropagation = Both;
                Editable = lineEditable;
            }
        }
    }

    actions
    {
        area(Navigation)
        {
            group("Functions")
            {
                action("ReOpen")
                {
                    ApplicationArea = All;
                    Caption = 'ReOpen';
                    Image = ReOpen;

                    trigger OnAction()
                    begin
                        if isDocUsed then
                            exit;

                        if "Document Modified" then Error('Modifier Document already used.');

                        if Rec.Status = Status::Released then begin
                            Rec.Status := Status::Open;
                            Rec.Modify();
                        end else begin
                            if Rec.Status = Status::"Pending Approval" then Error('Use the cancel approval button to reopen this document.');
                        end;
                    end;
                }
                action("Release")
                {
                    ApplicationArea = All;
                    Caption = 'Release';
                    Image = ReleaseDoc;

                    trigger OnAction()
                    var
                        workflow: Record Workflow;
                        isWorkflowEnabled: Boolean;
                    begin
                        if isDocUsed then
                            exit;

                        if "Document Modified" then Error('Modifier document already used.');

                        if Rec."Cc Code" = '' then Error(CcCodeErrMsg, Rec."No.");

                        ChecklineData();

                        IF PDMCodeUnit.CheckPDMApprovalsWorkflowEnabled(Rec) then
                            Error('Approval workflow enabled for this Record.');

                        workflow.SetCurrentKey(Category);
                        workflow.SetFilter(Category, '%1', 'PURCHASE DOC MODIFIE');
                        if workflow.FindSet() then
                            repeat
                                if workflow.Enabled then isWorkflowEnabled := true;
                            until isWorkflowEnabled or (workflow.Next() = 0);

                        if isWorkflowEnabled then Error('No Approval workflow enabled for this Record.');

                        if Rec.Status = Status::Open then begin
                            Rec.Status := Status::Released;
                            Rec.Modify();
                        end else begin
                            if Rec.Status = Status::"Pending Approval" then Error('Purchase Modifier Document currently in an Approval process.');
                            Error('Modifier Document cannot be Released in this Status %1', Rec.Status);
                        end;
                    end;
                }
                action("Modify PO")
                {
                    ApplicationArea = All;
                    Caption = 'Modify PO';
                    Visible = ModifyVisible;
                    Promoted = true;
                    PromotedCategory = Process;
                    Image = PostDocument;

                    trigger OnAction()
                    begin
                        if isDocUsed then
                            exit;
                        TestField("Document No.");
                        if Rec."Cc Code" = '' then Error(CcCodeErrMsg, Rec."No.");

                        if "Document Modified" then Error('Modifier document already used.');
                        if Rec.Status in [Status::Open, Status::"Pending Approval"] then Error('Modifier Document must be Approved before proceeding with this Action');
                        ChecklineData();
                        ModifyPurchaseDocument("No.");
                    end;
                }
                action("Approval Entry")
                {
                    Image = Approvals;
                    Caption = 'Approvals';
                    Promoted = true;
                    PromotedCategory = Process;
                    ToolTip = 'Track Approvals for Document.';
                    ApplicationArea = All;
                    RunObject = page "Approval Entries";
                    RunPageLink = "Document No." = field("No.");
                }
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = ApprvButtonVisible;
                Enabled = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;

                trigger OnAction();
                begin
                    if isDocUsed then
                        exit;

                    if Rec."Cc Code" = '' then Error('Cc Code must have a value in Purchase Doc Modifier: No.=%1. It cannot be zero or empty.', Rec."No.");

                    ChecklineData();
                    if Rec.Status <> Status::Released then
                        IF PDMCodeUnit.CheckPDMApprovalsWorkflowEnabled(Rec) then
                            PDMCodeUnit.OnSendPDMForApproval(Rec)
                        else
                            Error('No Approval Workflow for this Record.');
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = ApprvButtonVisible;
                Enabled = OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;

                trigger OnAction();
                begin
                    if isDocUsed then
                        exit;
                    if Rec.Status = Status::"Pending Approval" then
                        PDMCodeUnit.OnCancelPDMForApproval(Rec);
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        if Rec.Status in [Status::Released, Status::"Pending Approval"] then begin
            FieldEditable := false;
            lineEditable := false;
        end else begin
            FieldEditable := true;
            lineEditable := true;
        end;
        SetFieldBehaviour();
        if Rec."No." = '' then Rec."Document type" := "Document Type"::Order;
    end;

    trigger OnAfterGetCurrRecord()
    begin
        if Rec.Status in [Status::Released, Status::"Pending Approval"] then begin
            FieldEditable := false;
            lineEditable := false;
        end else begin
            FieldEditable := true;
            lineEditable := true;
        end;
        SetFieldBehaviour();
    end;

    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    procedure ChecklineData(): Boolean
    var
        ModifierLine: Record "Purchase Doc Modifier Line";
        ErrTxt001: Label 'Reason must have a value in Purchase Doc Modifier Line: Modifier Code= %1, Line No= %2';
        ErrTxt002: Label 'New Value must have a value in Purchase Doc Modifier Line:  Modifier Code= %1, Line No= %2';
    begin
        ModifierLine.SetFilter("Modifier Code", Rec."No.");
        if ModifierLine.FindSet() then begin
            repeat
                if ModifierLine."Cc Code" = '' then Error(CcCodeErrMsgLine, ModifierLine."Line No.");

                if ModifierLine."Field Name".Contains('Currency') then begin
                    if ModifierLine.Reason = '' then Error(ErrTxt001, ModifierLine."Modifier Code", ModifierLine."Line No.");
                end else begin
                    if ModifierLine."New Value" = '' then Error(ErrTxt002, ModifierLine."Modifier Code", ModifierLine."Line No.");
                    if ModifierLine.Reason = '' then Error(ErrTxt001, ModifierLine."Modifier Code", ModifierLine."Line No.");
                end;
            until ModifierLine.Next() = 0;
        end else begin
            if ModifierLine."New Value" = '' then Error(ErrTxt002, ModifierLine."Modifier Code", ModifierLine."Line No.");
            if ModifierLine.Reason = '' then Error(ErrTxt001, ModifierLine."Modifier Code", ModifierLine."Line No.");
        end;

    end;

    procedure SetFieldBehaviour()
    begin
        case Status of
            status::Open:
                begin
                    StatusColor := 'favorable';
                end;
            status::"Pending Approval":
                begin
                    StatusColor := 'ambiguous';
                end;
            status::Released:
                begin
                    StatusColor := 'attention';
                end;
        end;
        if Rec."Document Modified" or (Rec.Status = Status::Archived) then isDocUsed := true else isDocUsed := false;
        if Rec."Document Modified" or (Rec.Status = Status::Released) then ApprvButtonVisible := false else ApprvButtonVisible := true;
        if Rec.Status = Status::Released then ModifyVisible := true else ModifyVisible := false;

        if Rec.Status = Status::Released then CurrPage.Caption := 'APPROVED PURCHASE DOC MODIFIER';
        if Rec.Status = Status::Archived then CurrPage.Caption := 'ARCHIVED PURCHASE DOC MODIFIER';
    end;


    trigger OnModifyRecord(): Boolean
    begin

    end;

    var
        lineEditable, isDocUsed, ApprvButtonVisible, ModifyVisible : Boolean;
        FieldEditable: Boolean;
        StatusColor: Text;
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        PDMCodeUnit: Codeunit "PModifier-CU";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        WorkflowManagement: Codeunit "Workflow Management";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        CcCodeErrMsg: TextConst ENU = 'Cc Code must have a value in Purchase Doc Modifier: No.=%1. It cannot be zero or empty.';
        CcCodeErrMsgLine: TextConst ENU = 'Cc Code must have a value in Purchase Doc Modifier Line: Line No.=%1. It cannot be zero or empty.';
}
//>>>>>> G2S CAS-01334-J2M7C2 8/30/2024
