page 50524 TempPrsLinesList
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = TempPRSLines;

    layout
    {
        area(Content)
        {
            repeater(Control10000000002)
            {
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Date of MRS"; "Date of MRS")
                {
                    ApplicationArea = all;
                }
                field("Manual MRS. No"; "Manual MRS. No")
                {
                    ApplicationArea = all;
                }
                field("Issue Dept"; "Issue Dept")
                {
                    ApplicationArea = all;

                }
                field("Issue Bus. Unit"; "Issue Bus. Unit")
                {
                    ApplicationArea = all;

                }
                field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    CaptionClass = '1,2,1';
                    Editable = true;

                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    CaptionClass = '1,2,2';
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
                field("MRS Type"; "MRS Type")
                {

                    ApplicationArea = all;
                    Editable = FALSE;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = all;
                }
                field(Type; Type)
                {
                    Caption = 'Type';
                    ApplicationArea = all;
                }
                field("No."; "No.")
                {
                    Caption = 'No.';
                    ApplicationArea = all;

                }
                field("Item Category Code"; "Item Category Code")
                {

                    ApplicationArea = all;
                }
                field(Description; Description)
                {

                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field("Unit Price"; "Unit Price")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Requested Quantity"; "Requested Quantity")
                {

                    ApplicationArea = all;
                }
                field("Qty. to Order"; "Qty. to Order")
                {

                    ApplicationArea = all;
                }
                field("Line Amount(LCY)"; "Line Amount(LCY)")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code."; "Shortcut Dimension 1 Code.")
                {
                    Editable = true;
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code."; "Shortcut Dimension 2 Code.")
                {
                    ApplicationArea = all;
                }
                field("Issue Depts"; "Issue Depts")
                {
                    ApplicationArea = all;
                }

                field("Issue Bus. Units"; "Issue Bus. Units")
                {
                    ApplicationArea = all;
                }
                field("Indent Depts"; "Indent Depts")
                {
                    ApplicationArea = all;
                }
                field("Indent Bus. Units"; "Indent Bus. Units")
                {
                    ApplicationArea = all;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {

                    ApplicationArea = All;
                }
                field("Quantity(Base)"; "Quantity(Base)")
                {
                    ApplicationArea = All;
                }
                field(Comments; Comments)
                {
                    ApplicationArea = All;
                }
                field("MRS No."; "MRS No.")
                {

                    Editable = false;
                    ApplicationArea = All;
                }
                field("MRS Line No."; "MRS Line No.")
                {

                    ApplicationArea = All;
                    Editable = false;
                }
                field("Purchase Types"; "Purchase Types")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = All;
                }
                field("Item Category Codes"; "Item Category Codes")
                {
                    ApplicationArea = All;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = All;
                }
                field("Capex No."; "Capex No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Capex Line No."; "Capex Line No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("CWIP No."; "CWIP No.")
                {
                    ApplicationArea = All;
                    editable = false;
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                    ApplicationArea = All;
                    //editable = false;
                }
                field("VAT Prod. Posting Group"; "VAT Prod. Posting Group")
                {
                    ApplicationArea = All;
                    //editable = false;
                }
                field("No"; "No")
                {
                    ApplicationArea = All;
                }
                field("Document Dates"; "Document Dates")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Date of MRS."; "Date of MRS.")
                {
                    ApplicationArea = All;
                }
                field("Manual MRS. No."; "Manual MRS. No.")
                {
                    ApplicationArea = All;
                }
                field("Issue Dept."; "Issue Dept.")
                {
                    ApplicationArea = All;
                }
                field("Issue Bus. Unit."; "Issue Bus. Unit.")
                {
                    ApplicationArea = All;
                }
                field("Indent Dept"; "Indent Dept")
                {
                    ApplicationArea = All;
                }
                field("Indent Bus. Unit."; "Indent Bus. Unit.")
                {
                    ApplicationArea = All;
                }
                field("Shortcut Dimension 1 Codes"; "Shortcut Dimension 1 Codes")
                {
                    ApplicationArea = All;
                }
                field("Shortcut Dimension 2 Codes"; "Shortcut Dimension 2 Codes")
                {
                    ApplicationArea = All;
                }
                field("Expected Delivery Dates"; "Expected Delivery Dates")
                {
                    Description = 'SAA3.0';
                    ApplicationArea = All;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = All;
                }
                field("Comment."; "Comment.")
                {
                    ApplicationArea = All;
                }

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}