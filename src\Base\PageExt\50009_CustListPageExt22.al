pageextension 50009 CustListPagExt22 extends "Customer List"
{
    layout
    {
        addafter("Location Code")
        {
            field("Net Change"; "Net Change")
            {
                ApplicationArea = All;
            }
            field("DMS Customer"; "DMS Customer")
            {
                ApplicationArea = All;

            }

            field("Customer Type"; "Customer Type")
            {
                ApplicationArea = All;
                //b2bpksalecorr10
            }
            field("Credit Limit Applied (LCY)"; "Credit Limit Applied (LCY)")
            {
                ApplicationArea = All;
                //b2bpksalecorr10
            }
            field("E-Mail"; "E-Mail")
            {
                ApplicationArea = All;
                //b2bpksalecorr10
            }

            field("Customer Credit type"; "Customer Credit type")
            {
                ApplicationArea = All;
            }
            field("Sales Office"; "Sales Office")
            {
                ApplicationArea = all;
            }
            field("Last Transaction Date"; "Last Transaction Date")
            {
                ApplicationArea = all;
                //b2bpksalecorr9
            }
            field("Creation Date"; "Creation Date")
            {
                ApplicationArea = all;
                //b2bpksalecorr9
            }
            //Baluon Apr 18 2022>>
            field(Address; Address)
            {
                ApplicationArea = all;
            }
            field("Address 2"; "Address 2")
            {
                ApplicationArea = all;
            }
            field(City; City)
            {
                ApplicationArea = all;
            }
            field("Accounting Location"; "Accounting Location")
            {
                ApplicationArea = all;
            }
            //Baluon Apr 18 2022<<
        }
    }
    actions
    {
        addafter("C&ontact")
        {

            action(CustomerStatement)
            {
                Image = SendMail;
                Caption = 'Customer Statement';
                ApplicationArea = ALL;
                trigger OnAction()
                var
                    Cust: Record Customer;
                    CustSel: Record Customer;
                    CustMail: Report CustomerMailProcess;
                    usersetup: Record "User Setup";
                BEGIN
                    usersetup.get(UserId);
                    if not usersetup."Send Customer History Mail" then
                        error('You do not have permissions to perform this action.');
                    CurrPage.SetSelectionFilter(CustSel);
                    Message('No. of customer selected %1', CustSel.Count);
                    IF CustSel.FINDSET then
                        Report.RunModal(Report::CustomerMailProcess, True, False, CustSel);
                    /*THEN

                        Clear(CustMail);
                //CustMail.SetValues(CustSel."No.");
                //CustMail.RunModal();
                    until CustSel.next = 0;*/
                END;
            }
        }
    }
}
