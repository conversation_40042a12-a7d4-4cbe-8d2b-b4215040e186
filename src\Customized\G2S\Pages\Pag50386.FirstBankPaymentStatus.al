//G2S Providus Integration
page 50602 "Bank Payment Status"
{
    Caption = 'Bank Payment Status';
    PageType = List;
    SourceTable = "Bank Payment Status";
    UsageCategory = Lists;
    ApplicationArea = Basic, Suite;
    Permissions = tabledata "Bank Payment Status" = RM;


    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Payment Status";
                "Payment Status")
                {
                    ApplicationArea = Basic, Suite;
                }
                field("Payment StatusT"; "Payment StatusT")
                {
                    ApplicationArea = Basic, Suite;
                }
                field("Status Description"; "Status Description")
                {
                    ApplicationArea = Basic, Suite;
                }
                field(Remark; Remark)
                {
                    ApplicationArea = Basic, Suite;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = Basic, Suite;
                }
                field(Successfull; Successfull)
                {
                    ApplicationArea = Basic, Suite;
                }
                field(Failed; Failed)
                {
                    ApplicationArea = Basic, Suite;
                }

            }
        }
    }
}
