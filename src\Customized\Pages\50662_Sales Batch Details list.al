page 50662 "Sales Batch Details " //B2BSPON22SEPT19
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Sales Batch Details";

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Doc No."; "Doc No.")
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field("Doc Type"; "Doc Type")
                {
                    ApplicationArea = All;
                    Editable = false;

                }

                field(Location; Location)
                {
                    ApplicationArea = ALL;
                    Editable = false;
                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("To Location"; "To Location")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field("Lot No."; "Lot No.")
                {
                    ApplicationArea = All;
                    Editable = false;

                }

                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                    Editable = false;


                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    Editable = false;

                }
                field("Expiry Date"; "Expiry Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released by"; "Released by")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
        area(Factboxes)
        {

        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction();
                begin

                end;
            }
        }
    }
}