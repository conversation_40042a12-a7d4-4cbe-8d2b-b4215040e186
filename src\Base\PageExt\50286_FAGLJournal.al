
/// <summary>
/// PageExtension FixedAssetGLJournal (ID 50286) extends Record Fixed Asset G/L Journal.
/// </summary>
pageextension 50286 FixedAssetGLJournal extends "Fixed Asset G/L Journal"
{
    layout
    {
        addafter("Shortcut Dimension 2 Code")
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
        }

        addafter(Amount)
        {
            field("Capex No."; "Capex No.")
            {
                ApplicationArea = All;
                trigger OnValidate()
                BEGIN
                    IF "Capex No." = '' then
                        clear("Capex Line No.");
                END;

            }
            field("Capex Line No."; "Capex Line No.")
            {
                ApplicationArea = All;
                trigger OnValidate()
                var
                    CapexLine: Record "Budget Line";
                    CapexAmountUtil: Decimal;
                    CapexAmount: decimal;
                BEGIN
                    /*IF ("Capex No." <> '') AND ("Capex Line No." <> 0) THEN BEGIN
                        CapexLine.RESET;
                        CapexLine.SETRANGE("Document No.", "Capex No.");
                        CapexLine.SETRANGE("Line No.", "Capex Line No.");
                        IF CapexLine.FINDFIRST THEN BEGIN
                            CapexLine.CALCFIELDS("Budget Utilized");
                            CapexAmountUtil := CapexLine."Budget Utilized";
                            CapexAmount := CapexLine."Amount(LCY)";
                        end;
                    End;
                    IF (Amount > (CapexAmount - CapexAmountUtil)) then
                        ERROR('Capex Budget Cannot exceed %1', (CapexAmount - CapexAmountUtil));*/

                END;
            }
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = All;
            }
        }
        //PhaniFeb172021>>
        modify("Account No.")
        {
            trigger OnBeforeValidate()
            var
                FADEpreBook: Record "FA Depreciation Book";
                FixedAsset: Record "Fixed Asset";
            BEGIN
                FADEpreBook.RESET;
                FADEpreBook.SetRange("FA No.", "Account No.");
                IF FADEpreBook.findfirst then BEGIN
                    IF FADEpreBook."Disposal Date" > 0D then
                        Error('You Cannot Select this Fixed Asset %1 it is Disposed.', "Account No.");
                    if FixedAsset.Get("Account No.") then begin
                        FixedAsset.TestField("Approval Status", FixedAsset."Approval Status"::Released);
                        FixedAsset.TestField(Blocked, false);
                    end;
                end;
            END;
        }
        //PhaniFeb172021<<
    }
    actions
    {

        //G2S 050324
        //Case ID:  CAS-01269-R7Y3W9 
        //Case Title: FA GL Journal Posting- Control Gap
        //Case Date: 5th March, 2024
        addafter("P&ost")
        {
            action("PoST-NEW")
            {
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ApplicationArea = All;
                Caption = 'Post';
                trigger OnAction()
                var
                    GenJounLine: Record "Gen. Journal Line";
                    FixedAsset: record "Fixed Asset";
                    GenJou2: Record "Gen. Journal Line";
                    FixAsset2: Record "Fixed Asset";
                    FixAsset: Record "Fixed Asset";

                BEGIN

                    Rec.SetRange("Approval Status", Rec."Approval Status"::Released);
                    if Rec.FindSet() then begin
                        IF "Journal Template Name" = 'ASSETS' then begin
                            GenJounLine.RESET;
                            GenJounLine.SetRange("Journal Template Name", "Journal Template Name");
                            GenJounLine.SetRange("Journal Batch Name", "Journal Batch Name");
                            GenJounLine.SetRange("Account Type", GenJounLine."Account Type"::"Fixed Asset");
                            GenJounLine.SetRange("FA Posting Type", GenJounLine."FA Posting Type"::"Acquisition Cost");
                            IF GenJounLine.FindSet() then
                                Repeat
                                    GenJounLine.TestField("Capex No.");
                                    GenJounLine.TestField("Capex Line No.");
                                    IF FixedAsset.GET(GenJounLine."Account No.") THEN
                                        if FixedAsset."Main Asset/Component" <> FixedAsset."Main Asset/Component"::Component then
                                            if FixedAsset."FA Tagging Required" then
                                                if not (Format(FixedAsset."FA Posting Group") IN ['COMP-SOFTW', 'F&F OFFICE', 'F&F RESIDE', 'MHEQUIP', 'ROADS', 'ROADS 2', 'BUILDING', 'LAND']) then
                                                    FixedAsset.TestField("FA Tagging Code");
                                until GenJounLine.next = 0;
                        end;
                        CheckDispCardExi();
                        clear(FANo);
                        GenJou2.reset;
                        GenJou2.SetRange("Document No.", "Document No.");
                        GenJou2.SetRange("Account Type", GenJou2."Account Type"::"Fixed Asset");
                        GenJou2.SetRange("FA Posting Type", GenJou2."FA Posting Type"::Disposal);
                        IF GenJou2.FindFirst() then BEGIN
                            FixAsset2.reset;
                            FixAsset2.SetRange("No.", GenJou2."Account No.");
                            IF FixAsset2.findfirst then
                                FANo := FixAsset2."No.";
                            //message('%1..first', FANo);
                        end;

                        CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post", Rec);
                        IF FANo <> '' then BEGIN
                            FixAsset.reset;
                            FixAsset.SetRange("No.", FANo);
                            IF FixAsset.FindFirst() then BEGIN
                                FixAsset.Blocked := true;
                                FixAsset."Approval Status" := FixAsset."Approval Status"::Open;
                                FixAsset.Inactive := true;
                                FixAsset.Modify();
                                //message('%1..second', FANo);
                            END;
                        end;
                        clear(FANo);
                    end;
                end;
            }

            action("Post & Print")
            {
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ApplicationArea = All;
                Caption = 'Post and &Print';
                trigger OnAction()
                var
                    GenJounLine: Record "Gen. Journal Line";
                    FixedAsset: record "Fixed Asset";
                    GenJou2: Record "Gen. Journal Line";
                    FixAsset2: Record "Fixed Asset";
                    FixAsset: Record "Fixed Asset";

                BEGIN

                    Rec.SetRange("Approval Status", Rec."Approval Status"::Released);
                    if Rec.FindSet() then begin
                        IF "Journal Template Name" = 'ASSETS' then begin
                            GenJounLine.RESET;
                            GenJounLine.SetRange("Journal Template Name", "Journal Template Name");
                            GenJounLine.SetRange("Journal Batch Name", "Journal Batch Name");
                            GenJounLine.SetRange("Account Type", GenJounLine."Account Type"::"Fixed Asset");
                            GenJounLine.SetRange("FA Posting Type", GenJounLine."FA Posting Type"::"Acquisition Cost");
                            IF GenJounLine.FindSet() then
                                Repeat
                                    GenJounLine.TestField("Capex No.");
                                    GenJounLine.TestField("Capex Line No.");
                                    IF FixedAsset.GET(GenJounLine."Account No.") THEN
                                        if FixedAsset."Main Asset/Component" <> FixedAsset."Main Asset/Component"::Component then
                                            if FixedAsset."FA Tagging Required" then
                                                if not (Format(FixedAsset."FA Posting Group") IN ['COMP-SOFTW', 'F&F OFFICE', 'F&F RESIDE', 'MHEQUIP', 'ROADS', 'ROADS 2', 'BUILDING', 'LAND']) then
                                                    FixedAsset.TestField("FA Tagging Code");
                                until GenJounLine.next = 0;
                        end;
                        CheckDispCardExi();
                        clear(FANo);
                        GenJou2.reset;
                        GenJou2.SetRange("Document No.", "Document No.");
                        GenJou2.SetRange("Account Type", GenJou2."Account Type"::"Fixed Asset");
                        GenJou2.SetRange("FA Posting Type", GenJou2."FA Posting Type"::Disposal);
                        IF GenJou2.FindFirst() then BEGIN
                            FixAsset2.reset;
                            FixAsset2.SetRange("No.", GenJou2."Account No.");
                            IF FixAsset2.findfirst then
                                FANo := FixAsset2."No.";
                            //message('%1..first', FANo);
                        end;

                        CODEUNIT.Run(CODEUNIT::"Gen. Jnl.-Post+Print", Rec);

                        IF FANo <> '' then BEGIN
                            FixAsset.reset;
                            FixAsset.SetRange("No.", FANo);
                            IF FixAsset.FindFirst() then BEGIN
                                FixAsset.Blocked := true;
                                FixAsset."Approval Status" := FixAsset."Approval Status"::Open;
                                FixAsset.Inactive := true;
                                FixAsset.Modify();
                                //message('%1..second', FANo);
                            END;
                        end;
                        clear(FANo);
                    end;
                end;
            }
        }
        modify("Post and &Print")
        {
            //G2S 050324
            //Case ID:  CAS-01269-R7Y3W9 
            //Case Title: FA GL Journal Posting- Control Gap
            //Case Date: 5th March, 2024
            //>>>>start
            Visible = false;
            //<<< end
            trigger OnBeforeAction()

            var
                GenJounLine: Record "Gen. Journal Line";
                FixedAsset: record "Fixed Asset";
                GenJou2: Record "Gen. Journal Line";
                FixAsset2: Record "Fixed Asset";
                GenJnlBatch: Record "Gen. Journal Batch";
                ApprovalsMgmt: Codeunit "Approvals Mgmt.";
            begin
                IF GenJnlBatch.Get("Journal Template Name", "Journal Batch Name") THEN
                    IF NOT ApprovalsMgmt.IsGeneralJournalBatchApprovalsWorkflowEnabled(GenJnlBatch) THEN
                        TestField("Approval Status", "Approval Status"::Released);//Balu On April 24

                IF "Journal Template Name" = 'ASSETS' then begin
                    GenJounLine.RESET;
                    GenJounLine.SetRange("Journal Template Name", "Journal Template Name");
                    GenJounLine.SetRange("Journal Batch Name", "Journal Batch Name");
                    GenJounLine.SetRange("Account Type", GenJounLine."Account Type"::"Fixed Asset");
                    GenJounLine.SetRange("FA Posting Type", GenJounLine."FA Posting Type"::"Acquisition Cost");
                    IF GenJounLine.FindSet() then
                        Repeat
                            GenJounLine.TestField("Capex No.");
                            GenJounLine.TestField("Capex Line No.");
                            IF FixedAsset.GET(GenJounLine."Account No.") THEN
                                if FixedAsset."Main Asset/Component" <> FixedAsset."Main Asset/Component"::Component then
                                    if FixedAsset."FA Tagging Required" then
                                        if not (Format(FixedAsset."FA Posting Group") IN ['COMP-SOFTW', 'F&F OFFICE', 'F&F RESIDE', 'MHEQUIP', 'ROADS', 'ROADS 2', 'BUILDING', 'LAND']) then
                                            FixedAsset.TestField("FA Tagging Code");
                        until GenJounLine.next = 0;
                end;
                CheckDispCardExi();
                clear(FANo);
                GenJou2.reset;
                GenJou2.SetRange("Document No.", "Document No.");
                GenJou2.SetRange("Account Type", GenJou2."Account Type"::"Fixed Asset");
                GenJou2.SetRange("FA Posting Type", GenJou2."FA Posting Type"::Disposal);
                IF GenJou2.FindFirst() then BEGIN
                    FixAsset2.reset;
                    FixAsset2.SetRange("No.", GenJou2."Account No.");
                    IF FixAsset2.findfirst then
                        FANo := FixAsset2."No.";
                    //message('%1..first', FANo);
                end;

                CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post2", Rec);

            end;

            trigger OnAfterAction()
            var
                FixAsset3: Record "Fixed Asset";
            BEGIN
                IF FANo <> '' then BEGIN
                    FixAsset3.reset;
                    FixAsset3.SetRange("No.", FANo);
                    IF FixAsset3.FindFirst() then BEGIN
                        FixAsset3.Blocked := true;
                        FixAsset3."Approval Status" := FixAsset3."Approval Status"::Open;
                        FixAsset3.Inactive := true;
                        FixAsset3.Modify();
                        //message('%1..second', FANo);
                    END;
                end;
                clear(FANo);
            END;

        }
        modify("P&ost")
        {
            //G2S 050324
            //Case ID:  CAS-01269-R7Y3W9 
            //Case Title: FA GL Journal Posting- Control Gap
            //Case Date: 5th March, 2024
            //>>> start
            Visible = false;
            //<<< end
            trigger OnBeforeAction()

            var
                GenJounLine: Record "Gen. Journal Line";
                FixedAsset: record "Fixed Asset";
                FixAsset: record "Fixed Asset";
                GenJou: Record "Gen. Journal Line";
                GenJnlBatch: Record "Gen. Journal Batch";
                ApprovalsMgmt: Codeunit "Approvals Mgmt.";
            begin
                IF GenJnlBatch.Get("Journal Template Name", "Journal Batch Name") THEN
                    IF NOT ApprovalsMgmt.IsGeneralJournalBatchApprovalsWorkflowEnabled(GenJnlBatch) THEN
                        TestField("Approval Status", "Approval Status"::Released);//Balu On April 24
                IF "Journal Template Name" = 'ASSETS' then begin
                    GenJounLine.RESET;
                    GenJounLine.SetRange("Journal Template Name", "Journal Template Name");
                    GenJounLine.SetRange("Journal Batch Name", "Journal Batch Name");
                    GenJounLine.SetRange("Account Type", GenJounLine."Account Type"::"Fixed Asset");
                    GenJounLine.SetRange("FA Posting Type", GenJounLine."FA Posting Type"::"Acquisition Cost");
                    IF GenJounLine.FindSet() then
                        Repeat
                            GenJounLine.TestField("Capex No.");//PK On 27.05.2021
                            GenJounLine.TestField("Capex Line No.");//PK On 27.05.2021
                            IF FixedAsset.GET(GenJounLine."Account No.") THEN
                                if FixedAsset."FA Tagging Required" then
                                    if FixedAsset."Main Asset/Component" <> FixedAsset."Main Asset/Component"::Component then
                                        if not (Format(FixedAsset."FA Posting Group") IN ['COMP-SOFTW', 'F&F OFFICE', 'F&F RESIDE', 'MHEQUIP', 'ROADS', 'ROADS 2', 'BUILDING', 'LAND']) then
                                            FixedAsset.TestField("FA Tagging Code");
                        until GenJounLine.next = 0;
                END;
                CheckDispCardExi();

                clear(FANo);
                GenJou.reset;
                GenJou.SetRange("Document No.", "Document No.");
                GenJou.SetRange("Account Type", GenJou."Account Type"::"Fixed Asset");
                GenJou.SetRange("FA Posting Type", GenJou."FA Posting Type"::Disposal);
                IF GenJou.FindFirst() then BEGIN
                    FixAsset.reset;
                    FixAsset.SetRange("No.", GenJou."Account No.");
                    IF FixAsset.findfirst then
                        FANo := FixAsset."No.";
                    //message('%1..first', FANo);
                end;
            end;

            trigger OnAfterAction()
            var
                FixAsset: Record "Fixed Asset";
            BEGIN
                IF FANo <> '' then BEGIN
                    FixAsset.reset;
                    FixAsset.SetRange("No.", FANo);
                    IF FixAsset.FindFirst() then BEGIN
                        FixAsset.Blocked := true;
                        FixAsset."Approval Status" := FixAsset."Approval Status"::Open;
                        FixAsset.Inactive := true;
                        FixAsset.Modify();
                        //message('%1..second', FANo);
                    END;
                end;
                clear(FANo);
            END;
        }
        addafter("P&ost")
        {
            action(Post2)
            {
                ApplicationArea = Suite;
                Caption = 'Post2';
                Ellipsis = true;
                Image = Report;
                trigger OnAction()
                var
                    GenJounLine: Record "Gen. Journal Line";
                    FixedAsset: record "Fixed Asset";
                    GenJou1: Record "Gen. Journal Line";
                    FixAsset1: Record "Fixed Asset";
                    GenJnlBatch: Record "Gen. Journal Batch";
                    ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                begin
                    IF GenJnlBatch.Get("Journal Template Name", "Journal Batch Name") THEN
                        IF NOT ApprovalsMgmt.IsGeneralJournalBatchApprovalsWorkflowEnabled(GenJnlBatch) THEN
                            TestField("Approval Status", "Approval Status"::Released);//Balu On April 24
                    IF "Journal Template Name" = 'ASSETS' then begin
                        GenJounLine.RESET;
                        GenJounLine.SetRange("Journal Template Name", "Journal Template Name");
                        GenJounLine.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenJounLine.SetRange("Account Type", GenJounLine."Account Type"::"Fixed Asset");
                        GenJounLine.SetRange("FA Posting Type", GenJounLine."FA Posting Type"::"Acquisition Cost");
                        IF GenJounLine.FindSet() then
                            Repeat
                                GenJounLine.TestField("Capex No.");
                                GenJounLine.TestField("Capex Line No.");
                                IF FixedAsset.GET(GenJounLine."Account No.") THEN
                                    if FixedAsset."Main Asset/Component" <> FixedAsset."Main Asset/Component"::Component then
                                        if FixedAsset."FA Tagging Required" then
                                            if not (Format(FixedAsset."FA Posting Group") IN ['COMP-SOFTW', 'F&F OFFICE', 'F&F RESIDE', 'MHEQUIP', 'ROADS', 'ROADS 2', 'BUILDING', 'LAND']) then
                                                FixedAsset.TestField("FA Tagging Code");
                            until GenJounLine.next = 0;
                    end;
                    CheckDispCardExi;

                    clear(FANo);
                    GenJou1.reset;
                    GenJou1.SetRange("Document No.", "Document No.");
                    GenJou1.SetRange("Account Type", GenJou1."Account Type"::"Fixed Asset");
                    GenJou1.SetRange("FA Posting Type", GenJou1."FA Posting Type"::Disposal);
                    IF GenJou1.FindFirst() then BEGIN
                        FixAsset1.reset;
                        FixAsset1.SetRange("No.", GenJou1."Account No.");
                        IF FixAsset1.findfirst then
                            FANo := FixAsset1."No.";
                        //message('%1..first', FANo);
                    end;

                    CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post2", Rec);



                    IF FANo <> '' then BEGIN
                        FixAsset1.reset;
                        FixAsset1.SetRange("No.", FANo);
                        IF FixAsset1.FindFirst() then BEGIN
                            FixAsset1.Blocked := true;
                            FixAsset1."Approval Status" := FixAsset1."Approval Status"::Open;
                            FixAsset1.Inactive := true;
                            FixAsset1.Modify();
                            //message('%1..second', FANo); 
                        END;
                    end;
                    clear(FANo);
                end;
            }

            group("Request Approval")
            {
                Caption = 'Request Approval';
                group(SendApprovalRequest)
                {
                    Caption = 'Send Approval Request';
                    Image = SendApprovalRequest;
                    action(SendApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Journal Batch';
                        //Enabled = NOT OpenApprovalEntriesOnBatchOrAnyJnlLineExist AND CanRequestFlowApprovalForBatchAndAllLines;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        var
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            ApprovalsMgmt.TrySendJournalBatchApprovalRequest(Rec);
                            SetControlAppearanceFromBatch;
                            SetControlAppearance;
                        end;
                    }
                    action(SendApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Selected Journal Lines';
                        //Enabled = NOT OpenApprovalEntriesOnBatchOrCurrJnlLineExist AND CanRequestFlowApprovalForBatchAndCurrentLine;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send selected journal lines for approval.';
                        trigger OnAction()
                        var
                            GenJournalLine: Record "Gen. Journal Line";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            GetCurrentlySelectedLines(GenJournalLine);
                            ApprovalsMgmt.TrySendJournalLineApprovalRequests(GenJournalLine);
                        end;
                    }
                    //Balu On April 24>>
                    action("Release")
                    {
                        Image = ReleaseDoc;
                        ApplicationArea = All;
                        trigger OnAction()
                        var
                            GenjurLine: Record "Gen. Journal Line";
                        begin
                            IF WorkflowManagement.CanExecuteWorkflow(Rec, WorkflowEventHandling.RunWorkflowOnSendVendorForApprovalcode()) then
                                error('Workflow is enabled. You can not release manually.');
                            GenjurLine.Reset();
                            GenjurLine.SetRange("Journal Template Name", "Journal Template Name");
                            GenjurLine.SetRange("Journal Batch Name", "Journal Batch Name");
                            GenjurLine.SetRange("Document No.", "Document No.");
                            if GenjurLine.FindSet() then
                                repeat
                                    IF GenjurLine."Approval Status" <> GenjurLine."Approval Status"::Released then BEGIN
                                        GenjurLine."Approval Status" := GenjurLine."Approval Status"::Released;
                                        GenjurLine.Modify();
                                    end;
                                until GenjurLine.Next() = 0;
                        end;
                    }
                    action("Open")
                    {
                        Image = Open;
                        ApplicationArea = All;
                        trigger OnAction()
                        var
                            GenjurLine: Record "Gen. Journal Line";
                        begin
                            IF "Approval Status" = "Approval Status"::"Pending for Approval" THEN
                                ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                            RecordRest.Reset();
                            RecordRest.SetRange(ID, 81);
                            RecordRest.SetRange("Record ID", Rec.RecordId());
                            IF RecordRest.FindFirst() THEN
                                error('This record is under in workflow process. Please cancel approval request if not required.');
                            GenjurLine.Reset();
                            GenjurLine.SetRange("Journal Template Name", "Journal Template Name");
                            GenjurLine.SetRange("Journal Batch Name", "Journal Batch Name");
                            GenjurLine.SetRange("Document No.", "Document No.");
                            if GenjurLine.FindSet() then
                                repeat
                                    GenjurLine."Approval Status" := GenjurLine."Approval Status"::Open;
                                    GenjurLine.Modify();
                                until GenjurLine.Next() = 0;
                        end;
                    }
                    //Balu On April 24<<
                }
                group(CancelApprovalRequest)
                {
                    Caption = 'Cancel Approval Request';
                    Image = Cancel;
                    action(CancelApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Journal Batch';
                        // Enabled = CanCancelApprovalForJnlBatch OR CanCancelFlowApprovalForBatch;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        var
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            ApprovalsMgmt.TryCancelJournalBatchApprovalRequest(Rec);
                            SetControlAppearanceFromBatch;
                            SetControlAppearance;
                        end;
                    }
                    action(CancelApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Selected Journal Lines';
                        Enabled = CanCancelApprovalForJnlLine OR CanCancelFlowApprovalForLine;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending selected journal lines for approval.';

                        trigger OnAction()
                        var
                            GenJournalLine: Record "Gen. Journal Line";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            GetCurrentlySelectedLines(GenJournalLine);
                            ApprovalsMgmt.TryCancelJournalLineApprovalRequests(GenJournalLine);
                        end;
                    }
                }
            }

        }

    }


    Procedure CheckDispCardExi()
    var
        GenJounLine: Record "Gen. Journal Line";
        FADisposal: record "FA Disposal";
    BEGIN
        IF "Journal Template Name" = 'ASSETS' then begin
            GenJounLine.RESET;
            GenJounLine.SetRange("Journal Template Name", "Journal Template Name");
            GenJounLine.SetRange("Journal Batch Name", "Journal Batch Name");
            //GenJounLine.SetRange("Document No.", "Document No.");
            GenJounLine.SetRange("Account Type", GenJounLine."Account Type"::"Fixed Asset");
            GenJounLine.SetRange("FA Posting Type", GenJounLine."FA Posting Type"::Disposal);
            IF GenJounLine.FindSet() then
                Repeat
                    FADisposal.Reset();
                    //FADisposal.SetRange("FA No.", GenJounLine."FA No.");//Mar102021
                    FADisposal.SetRange("FA No.", GenJounLine."Account No.");
                    FADisposal.SetRange(Status, FADisposal.Status::Released);
                    IF Not FADisposal.FindFirst() THEN
                        error('Released FA Disposal card not existing for this FA No %1', GenJounLine."FA No.");
                until GenJounLine.next = 0;
        end;
    end;

    local procedure CurrentJnlBatchNameOnAfterVali()
    var
        GenJnl: Codeunit GenJnlManagement;
    begin
        CurrPage.SaveRecord;
        GenJnl.SetName("Journal Batch Name", Rec);
        CurrPage.Update(false);
    end;

    local procedure GetCurrentlySelectedLines(var GenJournalLine: Record "Gen. Journal Line"): Boolean
    begin
        CurrPage.SetSelectionFilter(GenJournalLine);
        exit(GenJournalLine.FindSet);
    end;

    local procedure SetControlAppearance()
    var
        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
        WorkflowWebhookManagement: Codeunit "Workflow Webhook Management";
        CanRequestFlowApprovalForLine: Boolean;
    begin
        OpenApprovalEntriesExistForCurrUser :=
          OpenApprovalEntriesExistForCurrUserBatch or ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(RecordId);

        OpenApprovalEntriesOnJnlLineExist := ApprovalsMgmt.HasOpenApprovalEntries(RecordId);
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist := OpenApprovalEntriesOnJnlBatchExist or OpenApprovalEntriesOnJnlLineExist;

        CanCancelApprovalForJnlLine := ApprovalsMgmt.CanCancelApprovalForRecord(RecordId);

        WorkflowWebhookManagement.GetCanRequestAndCanCancel(RecordId, CanRequestFlowApprovalForLine, CanCancelFlowApprovalForLine);
        CanRequestFlowApprovalForBatchAndCurrentLine := CanRequestFlowApprovalForBatch and CanRequestFlowApprovalForLine;
    end;

    local procedure SetControlAppearanceFromBatch()
    var
        GenJournalBatch: Record "Gen. Journal Batch";
        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
        WorkflowWebhookManagement: Codeunit "Workflow Webhook Management";
        CanRequestFlowApprovalForAllLines: Boolean;
    begin
        if ("Journal Template Name" <> '') and ("Journal Batch Name" <> '') then
            GenJournalBatch.Get("Journal Template Name", "Journal Batch Name")
        else
            if not GenJournalBatch.Get(GetRangeMax("Journal Template Name"), "Journal Batch Name") then
                exit;

        CheckOpenApprovalEntries(GenJournalBatch.RecordId);

        CanCancelApprovalForJnlBatch := ApprovalsMgmt.CanCancelApprovalForRecord(GenJournalBatch.RecordId);

        WorkflowWebhookManagement.GetCanRequestAndCanCancelJournalBatch(
          GenJournalBatch, CanRequestFlowApprovalForBatch, CanCancelFlowApprovalForBatch, CanRequestFlowApprovalForAllLines);
        CanRequestFlowApprovalForBatchAndAllLines := CanRequestFlowApprovalForBatch and CanRequestFlowApprovalForAllLines;
    end;

    local procedure CheckOpenApprovalEntries(BatchRecordId: RecordID)
    var
        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
    begin
        OpenApprovalEntriesExistForCurrUserBatch := ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(BatchRecordId);

        OpenApprovalEntriesOnJnlBatchExist := ApprovalsMgmt.HasOpenApprovalEntries(BatchRecordId);

        OpenApprovalEntriesOnBatchOrAnyJnlLineExist :=
          OpenApprovalEntriesOnJnlBatchExist or
          ApprovalsMgmt.HasAnyOpenJournalLineApprovalEntries("Journal Template Name", "Journal Batch Name");
    end;


    var
        FANo: code[10];
        OpenApprovalEntriesExistForCurrUser: Boolean;
        OpenApprovalEntriesExistForCurrUserBatch: Boolean;
        OpenApprovalEntriesOnJnlBatchExist: Boolean;
        OpenApprovalEntriesOnJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrAnyJnlLineExist: Boolean;
        ShowWorkflowStatusOnBatch: Boolean;
        ShowWorkflowStatusOnLine: Boolean;
        CanCancelApprovalForJnlBatch: Boolean;
        CanCancelApprovalForJnlLine: Boolean;
        IsSaaSExcelAddinEnabled: Boolean;
        CanRequestFlowApprovalForBatch: Boolean;
        CanRequestFlowApprovalForBatchAndAllLines: Boolean;
        CanRequestFlowApprovalForBatchAndCurrentLine: Boolean;
        CanCancelFlowApprovalForBatch: Boolean;
        CanCancelFlowApprovalForLine: Boolean;
        WorkflowManagement: codeunit "Workflow Management";
        WorkflowEventHandling: Codeunit "Workflow Event Handling";
        RecordRest: Record "Restricted Record";



}