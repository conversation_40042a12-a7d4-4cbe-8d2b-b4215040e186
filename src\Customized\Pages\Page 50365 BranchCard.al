page 50365 BranchRequestCard
{
    Caption = 'Branch Request Card';
    PageType = Document;
    //ApplicationArea = All;//PKON1605
    //UsageCategory = Documents;//PKON1605
    SourceTable = BranchRequest;
    DelayedInsert = true;
    SourceTableView = where("Transfer Order No." = filter(''));//PKON1605

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field(No; No)
                {
                    ApplicationArea = All;
                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE();
                    end;

                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = All;
                }
                field("To Location"; "To Location")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Branch Req Mail Send"; "Branch Req Mail Send")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Transfer Order No."; "Transfer Order No.")
                {
                    ApplicationArea = all;
                }
                field("Mail Send"; "Mail Send")
                {
                    Caption = 'Mail Sent';
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(BranchRequestLine; BranchRequestSubform)
            {
                ApplicationArea = Basic, Suite;
                SubPageLink = "Document No." = field(No);
                UpdatePropagation = Both;
            }
        }
    }

    actions
    {
        area(Processing)
        {

            action("Send Br Notification")
            {
                Image = SendMail;
                ApplicationArea = all;
                trigger OnAction()
                var
                    UserSetLVar: Record "User Setup";
                begin
                    if "Mail Send" then begin
                        UserSetLVar.Get(UserId);
                        if not UserSetLVar."Resend Branch Request" then
                            Error('You are not Authorized Person to resend the Email');
                    end;
                    CountMail();
                    //TestField("Transfer Type", "Transfer Type"::"Branch Request");
                    sendmail.SendAppBrStkRequestAlertFromBR(Rec, Email);
                    "Mail Send" := true;
                    Message('Sent Successfully');
                end;
            }
            action("Create Transfer Order")
            {
                Image = Post;
                ApplicationArea = all;
                trigger OnAction()
                begin
                    CreateTransferOrder();
                end;

            }
            action(DeleteEmptyLine)//PKONJ3
            {
                Image = Delete;
                ApplicationArea = all;
                trigger OnAction()
                var
                    BranchRequestLine: Record BranchRequestLine;
                begin
                    BranchRequestLine.Reset();
                    BranchRequestLine.SETRANGE("Document No.", '');
                    IF BranchRequestLine.FindFIRST() then BEGIN
                        Message('Count %1', BranchRequestLine."Item No.");
                        IF Confirm('Do you want to delete the empty Document No.?', True, False) then
                            BranchRequestLine.Delete();
                    end;
                end;
            }

        }

    }
    procedure CreateTransferOrder()
    var
        transferHdr: Record "Transfer Header";
        transferLine: Record "Transfer Line";
        BranchRequestLine: Record BranchRequestLine;
        BranchRequest: Record BranchRequest;
        Validations: Codeunit Validations;
        BranchRqstLine: Record BranchRequestLine;
        //G2S 11/06/2024
        Text000: Label 'Branch Request,Transfer Ticket';
        Text003: Label 'Kindly Choose the Transfer Type for the process:';
        options: Text;
        selected: Integer;
        TransferTicketPass: Boolean;
        UserSetup: Record "User Setup";
    begin
        //Balu 05142021>>
        if "Transfer Order No." <> '' then
            Error('Already Transfer Order is Created.');
        BranchRqstLine.Reset();
        BranchRequestLine.SetRange("Document No.", No);
        BranchRequestLine.SetFilter("Issued Quantity", '%1', 0);
        if BranchRequestLine.FindFirst() then
            if not Confirm('There are some lines with zero Quantity Issue Do you want to skip and Continue', true, false) then
                exit;
        BranchRqstLine.Reset();
        BranchRequestLine.SetRange("Document No.", No);
        BranchRequestLine.SetFilter("Issued Quantity", '<>%1', 0);
        if NOT BranchRequestLine.FindFirst() then
            Error('There is nothing to create.');
        //Balu 05142021<<
        if not Confirm('Do you want to create transfer order', false) then
            exit;

        //G2S 11/06/2024
        options := Text000;
        selected := Dialog.StrMenu(options, 3, Text003);
        if selected = 0 then
            Error('Kindly select an Transfer Type to Continue!');

        //G2S 11/07/2024
        UserSetup.Get(UserId);
        if not UserSetup."Can Use Transfer Ticket" then
            if selected = 2 then Error('You do not have access to pick the transfer type :%1', transferHdr."Transfer Type"::"Transfer Ticket");

        transferHdr.Init();
        transferHdr."No." := '';
        transferHdr.Insert(true);
        transferHdr.Validate("Transfer-from Code", Rec."From Location");
        transferHdr.Validate("Transfer-to Code", Rec."To Location");
        transferHdr.Validate("In-Transit Code", 'INTRANSIT');

        //G2S 11/06/2024
        case selected of
            1:
                transferHdr."Transfer Type" := transferHdr."Transfer Type"::"Branch Request";

            2:
                transferHdr."Transfer Type" := transferHdr."Transfer Type"::"Transfer Ticket";
        end;
        transferHdr.FromBranchReqst := true;

        transferHdr."Branch Request No" := No;
        transferHdr.Modify();
        "Transfer Order No." := transferHdr."No.";
        TransferOrderCreated := true;
        Modify(false);
        BranchRequestLine.Reset();
        BranchRequestLine.SetRange("Document No.", No);
        //BranchRequestLine.SetRange("Issued Quantity",<> '');
        BranchRequestLine.SetFilter("Issued Quantity", '<>%1', 0);//Balu 05142021
        if BranchRequestLine.FindSet() then
            repeat
                transferLine.Init();
                transferLine."Document No." := transferHdr."No.";
                transferLine."Line No." := BranchRequestLine."Line No.";
                transferLine.Insert();
                transferLine.validate("Item No.", BranchRequestLine."Item No.");
                //transferLine.Description := BranchRequestLine.Description;
                transferLine.Validate("Unit of Measure Code", BranchRequestLine."Unit Of Measure");
                //transferLine.Validate(Quantity, BranchRequestLine."Requested Quantity");
                transferLine.Validate(Quantity, BranchRequestLine."Issued Quantity");//Balu 05142021
                Validations.CheckQtyValidations(transferLine);//FIX 10May2021
                transferLine.Modify();
            until BranchRequestLine.next = 0;
        Message('Transfer Order no. %1 is created.', transferHdr."No.");
    end;

    var

        myInt: Integer;
        sendmail: Codeunit "Send Mail";
        Email: Text[200];


    trigger OnModifyRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not modify branch request. Already transfer order is created.');
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not deleted branch request. Already transfer order is created.');
    end;


}