tableextension 50003 PurchPayExt312 extends "Purchases & Payables Setup"
{
    fields
    {
        field(50000; "Import Purchase Quote"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; "Local Purchase Quote"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50002; "Import Purchase Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50003; "Local Purchase Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50004; "Import Purchase Invoice"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50005; "Local Purchase Invoice"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50006; "Import Purchase Credit Memo"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50007; "Local Purchase Credit Memo"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50008; "Import Blanket Purchase Order"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50009; "Local Blanket Purchase Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50010; "Delivery Required"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Quality Required"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "Payment Terms Required"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "Price Weightage"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Payment Terms Weightage"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Default Delivery Rating"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Delivery Weightage"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Default Quality Rating"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50018; "Quote Comparision"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50019; "Max Fuel Availed"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50020; "PMS Purchase Invoice"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50023; "Reconsilation No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50021; "GIT To Mail Alert ID"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50022; "GIT Mail"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        /*field(50024; "SharedPoint Path"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50025; "SharePoint Path For Archive"; Text[100])
        {
            DataClassification = CustomerContent;
        }*/
        field(50026; "PO Closing Period"; DateFormula)
        {
            DataClassification = CustomerContent;
        }
        field(50027; "Transport Inv Account"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account" where(Blocked = filter(false));
            //B2B.P.K.T
        }
        field(50028; "Transport Inv Threshold Amt"; Integer)
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50029; "FA Item Template - MRS"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Config. Template Header".Code where("Table ID" = filter(27));
        }
        field(50030; "FA Card Template - MRS"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Config. Template Header".Code where("Table ID" = filter(5600));
        }
        /*field(50031; "SCD Approved Email 1"; code[100])
        {
            DataClassification = CustomerContent;
        }
        field(50032; "SCD Approved Email 2"; code[100])
        {
            DataClassification = CustomerContent;
        }
        field(50033; "SCD Approved Email 3"; code[100])
        {
            DataClassification = CustomerContent;
        }*/
        field(60002; "Import Vendor Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60003; "Import File Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60004; "Import Quote Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60005; "Import Order Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60006; "Import Invoice Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60007; "Import Credit Memo Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60008; "Import Posted Invoice Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60009; "Import Posted Credit Memo Nos."; Code[10])
        {
            Description = 'UNL1.0';
            TableRelation = "No. Series";
        }
        field(60028; "LC Value Variation %"; Decimal)
        {
            Description = 'HO1.0';
        }
        field(60026; "Clearing Nos."; Code[10])
        {
            Caption = 'Clearing Nos.';
            Description = 'HO1.0';
            TableRelation = "No. Series";
        }
        field(60029; "Import Service Orders"; Code[10])
        {
            TableRelation = "No. Series";//PKONJ8
        }
        field(50031; "Service Master No."; code[100])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";//Service07Jul2021
        }
        //B2BMSOn18Oct21>>
        field(60027; "Contract Email 1"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(60032; "Contract Email 2"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(60030; "Contract Email 3"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(60031; "Contract Duration"; Code[30])
        {
            DataClassification = CustomerContent;
        }
        //B2BMSOn18Oct21<<
        //B2BMSOn09Nov21>>
        field(60033; "Vendor Contract Check"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //B2BMSOn09Nov21<<
        // >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
        field(60034; "PModifier Nos."; Code[20])
        {
            DataClassification = ToBeClassified;
            TableRelation = "No. Series".Code;
        }
        // <<<<<< G2S CAS-01334-J2M7C2 8/30/2024
    }

}