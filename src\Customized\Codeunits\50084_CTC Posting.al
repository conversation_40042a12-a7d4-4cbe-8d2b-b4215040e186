codeunit 50084 "CTC Posting"
{
    // version CTC

    Permissions = TableData "FA Ledger Entry" = rim,
                  TableData "Maintenance Ledger Entry" = rim;

    trigger OnRun();
    begin
        CreateFAJournal;
        MESSAGE('Finished');
    end;

    procedure CreateFAJournal();
    var
        AllocationSetup: Record "Allocation Setup";
        GenJnlLine: Record "Gen. Journal Line";
        AllocationHeader: Record "Allocation Header";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        DocumentNo: Code[20];
        LineNo: Integer;
        FADepreciationBook: Record "FA Depreciation Book";
        GenJnlAllocation: Record "Gen. Jnl. Allocation";
        MainLedEntry1: Record "Maintenance Ledger Entry";
        item: Record Item temporary;
        Frequency: Code[10];
        FALedEntry: Record "FA Ledger Entry";
        MainLedgEntryAmount: Decimal;
        FAPostingGroup: Record "FA Posting Group";
    begin
        AllocationSetup.GET;
        LineNo := 0;

        AllocationHeader.RESET;
        AllocationHeader.SETRANGE("Account Type", AllocationHeader."Account Type"::"Fixed Asset");
        AllocationHeader.SETRANGE(Status, AllocationHeader.Status::Certify);
        if not AllocationHeader.FINDFIRST then
            exit;

        AllocationHeader.RESET;
        AllocationHeader.SETRANGE("Account Type", AllocationHeader."Account Type"::"Fixed Asset");
        AllocationHeader.SETRANGE(Status, AllocationHeader.Status::Certify);
        if AllocationHeader.FINDFIRST then
            repeat
                GenJnlLine.RESET;
                GenJnlLine.SETRANGE("Journal Template Name", AllocationHeader."Journal Template Name");
                GenJnlLine.SETRANGE("Journal Batch Name", AllocationHeader."Journal Batch Name");
                if GenJnlLine.FINDFIRST then begin
                    GenJnlAllocation.RESET;
                    GenJnlAllocation.SETRANGE("Journal Template Name", GenJnlLine."Journal Template Name");
                    GenJnlAllocation.SETRANGE("Journal Batch Name", GenJnlLine."Journal Batch Name");
                    if GenJnlAllocation.FINDFIRST then
                        GenJnlAllocation.DELETEALL;
                    GenJnlLine.DELETEALL;

                    /*JournalLineDim.RESET;
                    JournalLineDim.SETRANGE("Table ID", 221);
                    JournalLineDim.SETRANGE("Journal Template Name", GenJnlLine."Journal Template Name");
                    JournalLineDim.SETRANGE("Journal Batch Name", GenJnlLine."Journal Batch Name");
                    IF JournalLineDim.FINDFIRST THEN
                      JournalLineDim.DELETEALL;
                      GenJnlLine.DELETEALL;

                    JournalLineDim.RESET;
                    JournalLineDim.SETRANGE("Table ID", 81);
                    JournalLineDim.SETRANGE("Journal Template Name", GenJnlLine."Journal Template Name");
                    JournalLineDim.SETRANGE("Journal Batch Name", GenJnlLine."Journal Batch Name");
                    IF JournalLineDim.FINDFIRST THEN
                      JournalLineDim.DELETEALL;*///CHI2018

                end;
            until AllocationHeader.NEXT = 0;

        AllocationHeader.SETRANGE("Account Type", AllocationHeader."Account Type"::"Fixed Asset");
        AllocationHeader.SETRANGE(Status, AllocationHeader.Status::Certify);
        if AllocationHeader.FINDFIRST then
            repeat
                //Get AllMaintainance codes
                Frequency := '-' + FORMAT(AllocationHeader."Recurring Frequency");
                if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Maintenance then begin
                    MainLedEntry1.RESET;
                    MainLedEntry1.SETRANGE("FA No.", AllocationHeader."Account No.");
                    if AllocationHeader."Maintenance Code" <> '' then
                        MainLedEntry1.SETRANGE("Maintenance Code", AllocationHeader."Maintenance Code");
                    MainLedEntry1.SETRANGE(Allocated, false);
                    MainLedEntry1.SETFILTER("Posting Date", '%1..%2', DMY2DATE(1, DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 2),
                    DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 3)), CALCDATE('-1D', AllocationHeader."Posting Date"));
                    item.DELETEALL;
                    if MainLedEntry1.FINDFIRST then
                        repeat
                            item.INIT;
                            item."No." := MainLedEntry1."Maintenance Code";
                            item."Unit Price" := MainLedEntry1.Amount;
                            if not item.INSERT then begin
                                item.GET(MainLedEntry1."Maintenance Code");
                                item."Unit Price" += MainLedEntry1.Amount;
                                item.MODIFY;
                            end;
                        until MainLedEntry1.NEXT = 0;
                    if item.FINDSET then
                        repeat
                            LineNo += 10000;
                            GenJnlLine.INIT;
                            GenJnlLine.VALIDATE("Journal Template Name", AllocationHeader."Journal Template Name");
                            GenJnlLine.VALIDATE("Journal Batch Name", AllocationHeader."Journal Batch Name");
                            GenJnlLine.VALIDATE("Document No.", NoSeriesMgt.GetNextNo(AllocationSetup."CTC No. Series", TODAY, true));
                            GenJnlLine.VALIDATE("Line No.", LineNo);
                            GenJnlLine.INSERT; //PJ
                            GenJnlLine.VALIDATE("Recurring Method", GenJnlLine."Recurring Method"::"V  Variable");
                            GenJnlLine.VALIDATE("Recurring Frequency", AllocationHeader."Recurring Frequency");
                            GenJnlLine.VALIDATE("Posting Date", CALCDATE('-1D', AllocationHeader."Posting Date"));
                            GenJnlLine.VALIDATE("Account Type", GenJnlLine."Account Type"::"Fixed Asset");
                            GenJnlLine.VALIDATE("Account No.", AllocationHeader."Account No.");

                            GenJnlLine.VALIDATE("FA Posting Type", GenJnlLine."FA Posting Type"::Maintenance);
                            GenJnlLine.VALIDATE("Maintenance Code", item."No.");
                            GenJnlLine.VALIDATE("Expiration Date", AllocationHeader."Expiration Date");
                            GenJnlLine."Allocation Account Type" := GenJnlLine."Account Type"::"Fixed Asset";
                            GenJnlLine."Allocation Account No." := AllocationHeader."Account No.";
                            GenJnlLine."Allocation Maintenance Code" := AllocationHeader."Maintenance Code";
                            GenJnlLine.VALIDATE(Amount, -item."Unit Price");
                            //GenJnlLine.INSERT; //PJ

                            GenJnlLine.VALIDATE("Shortcut Dimension 1 Code", AllocationHeader."Shortcut Dimension 1 Code");
                            GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", AllocationHeader."Shortcut Dimension 2 Code");

                            //GenJnlLine.VALIDATE("Shortcut Dimension 9 Code", AllocationHeader."Shortcut Dimension 9 Code");
                            // GenJnlLine.VALIDATE("Shortcut Dimension 10 Code", AllocationHeader."Shortcut Dimension 10 Code");

                            GenJnlLine.MODIFY;
                            CreateGenJnlAllocation(GenJnlLine, AllocationHeader, item."No.");
                            COMMIT;
                            //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Batch",GenJnlLine);
                            modifyledgers(AllocationHeader);
                            COMMIT;
                        until item.NEXT = 0;
                    AllocationHeader."Posting Date" := CALCDATE('+1M', AllocationHeader."Posting Date");
                    AllocationHeader.MODIFY;
                end;
                if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Depreciation then begin
                    MainLedgEntryAmount := 0;
                    LineNo := 0;
                    FALedEntry.RESET;
                    FALedEntry.SETRANGE("FA No.", AllocationHeader."Account No.");
                    FALedEntry.SETRANGE("FA Posting Type", FALedEntry."FA Posting Type"::Depreciation);
                    FALedEntry.SETRANGE(Allocated, false);
                    FALedEntry.SETFILTER("Posting Date", '%1..%2', DMY2DATE(1, DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 2),
                      DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 3)), CALCDATE('-1D', AllocationHeader."Posting Date"));
                    if FALedEntry.FINDFIRST then begin
                        repeat
                            MainLedgEntryAmount += ABS(FALedEntry.Amount);
                        until FALedEntry.NEXT = 0;
                    end;
                    LineNo += 10000;
                    GenJnlLine.INIT;
                    GenJnlLine.VALIDATE("Journal Template Name", AllocationHeader."Journal Template Name");
                    GenJnlLine.VALIDATE("Journal Batch Name", AllocationHeader."Journal Batch Name");
                    GenJnlLine.VALIDATE("Document No.", NoSeriesMgt.GetNextNo(AllocationSetup."CTC No. Series", TODAY, true));
                    GenJnlLine.VALIDATE("Line No.", LineNo);
                    GenJnlLine.VALIDATE("Recurring Method", GenJnlLine."Recurring Method"::"V  Variable");
                    GenJnlLine.VALIDATE("Recurring Frequency", AllocationHeader."Recurring Frequency");
                    GenJnlLine.VALIDATE("Posting Date", CALCDATE('-1D', AllocationHeader."Posting Date"));
                    GenJnlLine.VALIDATE("Account Type", GenJnlLine."Account Type"::"G/L Account");

                    if FAPostingGroup.GET(FALedEntry."FA Posting Group") then
                        GenJnlLine.VALIDATE("Account No.", FAPostingGroup."Depreciation Expense Acc.");


                    //GenJnlLine.VALIDATE("FA Posting Type", GenJnlLine."FA Posting Type"::Depreciation);
                    //GenJnlLine.VALIDATE("Maintenance Code",item."No.");
                    GenJnlLine.VALIDATE("Expiration Date", AllocationHeader."Expiration Date");
                    GenJnlLine."Allocation Account Type" := GenJnlLine."Allocation Account Type"::"G/L Account";
                    if FAPostingGroup.GET(FALedEntry."FA Posting Group") then
                        GenJnlLine."Allocation Account No." := FAPostingGroup."Depreciation Expense Acc.";
                    GenJnlLine.VALIDATE(Amount, -MainLedgEntryAmount);
                    GenJnlLine.INSERT;

                    GenJnlLine.VALIDATE("Shortcut Dimension 1 Code", AllocationHeader."Shortcut Dimension 1 Code");
                    GenJnlLine.VALIDATE("Shortcut Dimension 2 Code", AllocationHeader."Shortcut Dimension 2 Code");
                    // GenJnlLine.VALIDATE("Shortcut Dimension 9 Code", AllocationHeader."Shortcut Dimension 9 Code");
                    // GenJnlLine.VALIDATE("Shortcut Dimension 10 Code", AllocationHeader."Shortcut Dimension 10 Code");

                    //GenJnlLine.INSERT;

                    GenJnlLine.MODIFY;
                    CreateGenJnlAllocation(GenJnlLine, AllocationHeader, '');
                    COMMIT;
                    //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Batch",GenJnlLine);
                    modifyledgers(AllocationHeader);
                    COMMIT;
                    AllocationHeader."Posting Date" := CALCDATE('+1M', AllocationHeader."Posting Date");
                    AllocationHeader.MODIFY;
                end;
            until AllocationHeader.NEXT = 0;

    end;

    procedure GetMainLedEntry(var AllocationHeader: Record "Allocation Header") MainLedgEntryAmount: Decimal;
    var
        MainLedEntry: Record "Maintenance Ledger Entry";
        Frequency: Code[10];
        FALedEntry: Record "FA Ledger Entry";
    begin
        MainLedgEntryAmount := 0;
        Frequency := '-' + FORMAT(AllocationHeader."Recurring Frequency");

        if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Maintenance then begin
            MainLedEntry.RESET;
            MainLedEntry.SETRANGE("FA No.", AllocationHeader."Account No.");
            if AllocationHeader."Maintenance Code" <> '' then
                MainLedEntry.SETRANGE("Maintenance Code", AllocationHeader."Maintenance Code");
            MainLedEntry.SETRANGE(Allocated, false);
            //MainLedEntry.SETFILTER(MainLedEntry."Posting Date",
            //STRSUBSTNO('%1..%2',CALCDATE(Frequency,AllocationHeader."Posting Date"),FORMAT(AllocationHeader."Posting Date")));
            MainLedEntry.SETFILTER("Posting Date", '%1..%2', DMY2DATE(1, DATE2DMY(AllocationHeader."Posting Date", 2),
            DATE2DMY(AllocationHeader."Posting Date", 3)), AllocationHeader."Posting Date");
            if MainLedEntry.FINDFIRST then
                repeat
                    MainLedgEntryAmount += MainLedEntry.Amount;
                //MainLedEntry.Allocated := TRUE;
                //MainLedEntry.MODIFY;
                until MainLedEntry.NEXT = 0;
            //MainLedEntry.MODIFYALL(Allocated,TRUE);
            exit(MainLedgEntryAmount);
        end;
        if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Depreciation then begin
            FALedEntry.RESET;
            FALedEntry.SETRANGE("FA No.", AllocationHeader."Account No.");
            FALedEntry.SETRANGE("FA Posting Type", FALedEntry."FA Posting Type"::Depreciation);
            FALedEntry.SETRANGE(Allocated, false);
            //FALedEntry.SETFILTER(FALedEntry."Posting Date",
            //STRSUBSTNO('%1..%2',CALCDATE(Frequency,AllocationHeader."Posting Date"),FORMAT(AllocationHeader."Posting Date")));
            FALedEntry.SETFILTER("Posting Date", '%1..%2', DMY2DATE(1, DATE2DMY(AllocationHeader."Posting Date", 2),
              DATE2DMY(AllocationHeader."Posting Date", 3)), AllocationHeader."Posting Date");

            if FALedEntry.FINDFIRST then
                repeat
                    MainLedgEntryAmount += ABS(FALedEntry.Amount);
                //FALedEntry.Allocated := TRUE;
                //FALedEntry.MODIFY;
                until FALedEntry.NEXT = 0;
            //FALedEntry.MODIFYALL(Allocated,TRUE);
            exit(MainLedgEntryAmount);
        end;
    end;

    procedure CreateGenJnlAllocation(var GenJnlLine: Record "Gen. Journal Line"; var AllocationHeader: Record "Allocation Header"; MaintainanceCode: Code[20]);
    var
        FADepreciationBook: Record "FA Depreciation Book";
        GenJnlAllocation: Record "Gen. Jnl. Allocation";
        FAPostingGroup: Record "FA Posting Group";
        AllocationLine: Record "Allocation Line";
        LineNo: Integer;
        AllocationSetup: Record "Allocation Setup";
        ICPartner: Record "IC Partner";
        Maintenance: Record Maintenance;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        DimSetID: Integer;
        DimMgmt: Codeunit DimensionManagement;
    begin
        AllocationLine.RESET;
        AllocationLine.SETRANGE("Account Type", AllocationHeader."Account Type");
        AllocationLine.SETRANGE("Account No.", AllocationHeader."Account No.");
        AllocationLine.SETRANGE("Allocation Type", AllocationHeader."Allocation Type");
        //AllocationLine.SETRANGE("Maintanance Code", MaintainanceCode); //RKD
        if AllocationLine.FINDFIRST then
            repeat
                GenJnlAllocation.INIT;
                GenJnlAllocation.VALIDATE("Journal Template Name", GenJnlLine."Journal Template Name");
                GenJnlAllocation.VALIDATE("Journal Batch Name", GenJnlLine."Journal Batch Name");
                GenJnlAllocation.VALIDATE("Journal Line No.", GenJnlLine."Line No.");
                GenJnlAllocation."JV Document No." := GenJnlLine."Document No."; //RKD
                GenJnlAllocation."Line No." := AllocationLine."Line No.";
                GenJnlAllocation.Description := AllocationLine.Remarks;  //RKD
                GenJnlAllocation."Fixed Asset No." := AllocationLine."Line Account No.";
                FADepreciationBook.RESET;
                if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Maintenance then
                    FADepreciationBook.SETRANGE("FA No.", AllocationHeader."Account No.");

                if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Depreciation then
                    FADepreciationBook.SETRANGE("FA No.", AllocationHeader."Account No.");

                if FADepreciationBook.FINDFIRST then begin
                    //IF AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Maintenance THEN
                    //  IF FAPostingGroup.GET(FADepreciationBook."FA Posting Group") THEN
                    //    GenJnlAllocation."Account No." := FAPostingGroup."Maintenance Expense Account";

                    if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Maintenance then
                        if MaintainanceCode <> '' then begin
                            if Maintenance.GET(MaintainanceCode) then begin
                                if Maintenance."Maintenance Account Code" <> '' then
                                    GenJnlAllocation."Account No." := Maintenance."Maintenance Account Code"
                                else begin
                                    if FAPostingGroup.GET(FADepreciationBook."FA Posting Group") then
                                        GenJnlAllocation."Account No." := FAPostingGroup."Maintenance Expense Account";
                                end;
                            end;
                        end else begin
                            if FAPostingGroup.GET(FADepreciationBook."FA Posting Group") then
                                GenJnlAllocation."Account No." := FAPostingGroup."Maintenance Expense Account";
                        end;

                    if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Depreciation then
                        if FAPostingGroup.GET(FADepreciationBook."FA Posting Group") then
                            GenJnlAllocation."Account No." := FAPostingGroup."Depreciation Expense Acc.";
                end;
                if AllocationLine."Line Account Type" = AllocationLine."Line Account Type"::"IC Partner" then
                    if ICPartner.GET(AllocationLine."Line Account No.") then begin
                        GenJnlAllocation."Account No." := ICPartner."Payables Account";
                        GenJnlAllocation."Line Account Type" := GenJnlAllocation."Line Account Type"::"IC Partner";
                    end;

                //IF AllocationLine."Line Account Type" = AllocationLine."Line Account Type"::"Fixed Asset" THEN
                //   GenJnlAllocation."Line Account Type" := GenJnlAllocation."Line Account Type"::"Fixed Asset";

                if (AllocationLine."Line Account Type" = AllocationLine."Line Account Type"::"Fixed Asset") then
                    if AllocationLine."Allocation Type" = AllocationLine."Allocation Type"::Depreciation then
                        GenJnlAllocation."Line Account Type" := GenJnlAllocation."Line Account Type"::"G/L Account"
                    else
                        GenJnlAllocation."Line Account Type" := GenJnlAllocation."Line Account Type"::"Fixed Asset";

                if AllocationLine."Line Account Type" = AllocationLine."Line Account Type"::"G/L Account" then
                    GenJnlAllocation."Line Account Type" := GenJnlAllocation."Line Account Type"::"G/L Account";

                if (AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Depreciation) and
                   (AllocationLine."Line Account Type" <> AllocationLine."Line Account Type"::"IC Partner") and
                   (AllocationLine."Line Account Type" <> AllocationLine."Line Account Type"::"G/L Account")
                   then
                    GenJnlAllocation."FA Account No." := FAPostingGroup."Depreciation Expense Acc."
                else
                    GenJnlAllocation."FA Account No." := AllocationLine."Line Account No.";

                if GenJnlAllocation.INSERT then; //RKD
                if AllocationLine."Allocation Quantity" <> 0 then
                    GenJnlAllocation.VALIDATE("Allocation Quantity", AllocationLine."Allocation Quantity");
                if AllocationLine."Allocation Amount" <> 0 then
                    GenJnlAllocation.VALIDATE(Amount, AllocationLine."Allocation Amount");
                if AllocationLine."Allocation %" <> 0 then
                    GenJnlAllocation.VALIDATE("Allocation %", AllocationLine."Allocation %");
                GenJnlAllocation.VALIDATE("Shortcut Dimension 1 Code", AllocationLine."Shortcut Dimension 1 Code");
                GenJnlAllocation.VALIDATE("Shortcut Dimension 2 Code", AllocationLine."Shortcut Dimension 2 Code");
                /*GenJnlAllocation.VALIDATE("Shortcut Dimension 9 Code", AllocationLine."Shortcut Dimension 9 Code");
                GenJnlAllocation.VALIDATE("Shortcut Dimension 10 Code", AllocationLine."Shortcut Dimension 10 Code");*///CHI2.0

                GenJnlAllocation.MODIFY;

                AllocationSetup.GET;
                /*IF AllocationLine."Line Account Type" <> AllocationLine."Line Account Type"::"IC Partner" THEN
                BEGIN
                  JournalLineDim.INIT;
                  JournalLineDim."Table ID" := 221;
                  JournalLineDim."Journal Template Name" := GenJnlAllocation."Journal Template Name";
                  JournalLineDim."Journal Batch Name" := GenJnlAllocation."Journal Batch Name";
                  JournalLineDim."Journal Line No." := GenJnlAllocation."Journal Line No.";
                  JournalLineDim."Allocation Line No." := GenJnlAllocation."Line No.";
                  JournalLineDim."Document No." := GenJnlAllocation."JV Document No.";  //RKD
                  JournalLineDim."Dimension Code" := AllocationSetup."Employee Dimension Code";
                  JournalLineDim."Dimension Value Code" := AllocationLine."Employee Code";
                  IF JournalLineDim.INSERT THEN ;

                  JournalLineDim.INIT;
                  JournalLineDim."Table ID" := 221;
                  JournalLineDim."Journal Template Name" := GenJnlAllocation."Journal Template Name";
                  JournalLineDim."Journal Batch Name" := GenJnlAllocation."Journal Batch Name";
                  JournalLineDim."Journal Line No." := GenJnlAllocation."Journal Line No.";
                  JournalLineDim."Allocation Line No." := GenJnlAllocation."Line No.";
                  JournalLineDim."Document No." := GenJnlAllocation."JV Document No.";  //RKD
                  JournalLineDim."Dimension Code" := AllocationSetup."CTC Dimension Code";
                  JournalLineDim."Dimension Value Code" := AllocationLine."CTC Code";
                  IF JournalLineDim.INSERT THEN;
                END;*///CHI2018
                IF AllocationLine."Line Account Type" <> AllocationLine."Line Account Type"::"IC Partner" THEN BEGIN
                    TempDimSetEntry.DELETEALL();
                    TempDimSetEntry.INIT;
                    TempDimSetEntry.VALIDATE("Dimension Set ID", DimSetID);
                    TempDimSetEntry.VALIDATE("Dimension Code", AllocationSetup."Employee Dimension Code");
                    TempDimSetEntry.VALIDATE("Dimension Value Code", AllocationLine."Employee Code");
                    TempDimSetEntry.INSERT(true);

                    TempDimSetEntry.INIT;
                    TempDimSetEntry.VALIDATE("Dimension Set ID", DimSetID);
                    TempDimSetEntry.VALIDATE("Dimension Code", AllocationSetup."CTC Dimension Code");
                    TempDimSetEntry.VALIDATE("Dimension Value Code", AllocationLine."CTC Code");
                    TempDimSetEntry.INSERT(true);

                    GenJnlAllocation."Dimension Set ID" := DimMgmt.GetDimensionSetID(TempDimSetEntry);
                    DimMgmt.UpdateGlobalDimFromDimSetID(GenJnlAllocation."Dimension Set ID", GenJnlAllocation."Shortcut Dimension 1 Code",
                    GenJnlAllocation."Shortcut Dimension 2 Code");
                    GenJnlAllocation.MODIFY(TRUE);
                End;
            until AllocationLine.NEXT = 0;

    end;

    procedure GetNoSeries(var AllocationHeader: Record "Allocation Header"): Code[20];
    var
        GenJnlBatch: Record "Gen. Journal Batch";
    begin
        if GenJnlBatch.GET(AllocationHeader."Journal Template Name", AllocationHeader."Journal Batch Name") then
            exit(GenJnlBatch."Posting No. Series");
    end;

    procedure modifyledgers(var AllocationHeader: Record "Allocation Header");
    var
        MainLedEntry: Record "Maintenance Ledger Entry";
        Frequency: Code[10];
        FALedEntry: Record "FA Ledger Entry";
    begin
        if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Maintenance then begin
            MainLedEntry.RESET;
            MainLedEntry.SETRANGE("FA No.", AllocationHeader."Account No.");
            if AllocationHeader."Maintenance Code" <> '' then
                MainLedEntry.SETRANGE("Maintenance Code", AllocationHeader."Maintenance Code");
            MainLedEntry.SETRANGE(Allocated, false);
            //MainLedEntry.SETFILTER(MainLedEntry."Posting Date",
            //STRSUBSTNO('%1..%2',CALCDATE(Frequency,AllocationHeader."Posting Date"),FORMAT(AllocationHeader."Posting Date")));
            MainLedEntry.SETFILTER("Posting Date", '%1..%2', DMY2DATE(1, DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 2),
            DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 3)), CALCDATE('-1D', AllocationHeader."Posting Date"));

            if MainLedEntry.FINDFIRST then
                repeat
                    MainLedEntry.Allocated := true;
                    MainLedEntry.MODIFY;
                until MainLedEntry.NEXT = 0;
        end;
        if AllocationHeader."Allocation Type" = AllocationHeader."Allocation Type"::Depreciation then begin
            FALedEntry.RESET;
            FALedEntry.SETRANGE("FA No.", AllocationHeader."Account No.");
            FALedEntry.SETRANGE("FA Posting Type", FALedEntry."FA Posting Type"::Depreciation);
            FALedEntry.SETRANGE(Allocated, false);
            //FALedEntry.SETFILTER(FALedEntry."Posting Date",
            //STRSUBSTNO('%1..%2',CALCDATE(Frequency,AllocationHeader."Posting Date"),FORMAT(AllocationHeader."Posting Date")));
            FALedEntry.SETFILTER("Posting Date", '%1..%2', DMY2DATE(1, DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 2),
            DATE2DMY(CALCDATE('-1D', AllocationHeader."Posting Date"), 3)), CALCDATE('-1D', AllocationHeader."Posting Date"));

            if FALedEntry.FINDFIRST then
                repeat
                    FALedEntry.Allocated := true;
                    FALedEntry.MODIFY;
                until FALedEntry.NEXT = 0;
        end;
    end;
}

