pageextension 50265 SalesQuoteExt extends "Sales Quote"
{
    layout
    {



    }
    actions
    {
        modify(MakeOrder)
        {
            trigger OnBeforeAction()
            var
                SalLine: Record "Sales Line";
                ItemLedEntry: Record "Item Ledger Entry";
                ItemQty: Decimal;
                Customer: Record Customer;
            BEGIN
                //FIX11Jun2021>>
                Customer.Get("Sell-to Customer No.");
                if (Customer."Bill-to Customer No." <> '') then
                    if Customer."Bill-to Customer No." <> "Bill-to Customer No." then
                        TestField("Bill-to Customer No.", Customer."Bill-to Customer No.");
                if Customer."Bill-to Customer No." = '' then
                    TestField("Bill-to Customer No.", "Sell-to Customer No.");
                //FIX11Jun2021<<
                CheckSalesMandValues(True);
            END;
        }
        modify(MakeInvoice)
        {
            Visible = false;
        }
    }
}
