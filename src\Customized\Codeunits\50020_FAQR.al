codeunit 50060 FAQR
{
    Permissions = TableData 5600 = rimd;
    TableNo = 5600;

    trigger OnRun()
    begin
        UpdateQRCode(Rec);
    end;

    Procedure UpdateQRCode(FALpa: Record "Fixed Asset")
    var
        FA: Record "Fixed Asset";
        ReturnDoc: BigText;
        Myfile: File;
        QRCodeFileName: Text;
        StreamIn: InStream;
        Streamout: OutStream;
        QRCodeInput: Text;
        MemoryStream: DotNet Memory;
        Convert: DotNet Convert;
    begin
        FA.get(FALpa."No.");
        //QRCodeInput := FA."No." + ',' + FA.Description + ',' + FA."FA Location Code";PK-Ravi
        QRCodeInput := FA."No." + ',' + FA.Description + ',' + FA."FA Location Code" + ',' + fa."FA Tagging Code";// PK-Ravi
        IF QRCodeInput = '' THEN
            EXIT;
        // Save a QR code image into a file in a temporary folder
        QRCodeFileName := GetQRCode(QRCodeInput);
        Myfile.Open(QRCodeFileName, TextEncoding::Windows);
        Myfile.CreateInStream(StreamIn);
        MemoryStream := MemoryStream.MemoryStream();
        COPYSTREAM(MemoryStream, StreamIn);
        ReturnDoc.ADDTEXT(Convert.ToBase64String(MemoryStream.ToArray()));
        FA.FAQRData.CreateOutStream(Streamout, TextEncoding::Windows);
        MemoryStream.WriteTo(Streamout);
        FA.MODIFY(true);
    end;

    LOCAL Procedure GetQRCode(QRCodeInput: Text) QRCodeFileName: Text
    var
        IBarCodeProvider: DotNet BarcodeProvidersIBarcodeProvider;
    BEGIN
        GetBarCodeProvider(IBarCodeProvider);
        QRCodeFileName := IBarCodeProvider.GetBarcode(QRCodeInput);
    END;

    LOCAL Procedure GetBarCodeProvider(VAR IBarCodeProvider: DotNet BarcodeProvidersIBarcodeProvider)
    var
        QRCodeProvider: DotNet BarcodeProvidersQRCodeProvide;
    begin
        QRCodeProvider := QRCodeProvider.QRCodeProvider();
        IBarCodeProvider := QRCodeProvider;
    end;

}