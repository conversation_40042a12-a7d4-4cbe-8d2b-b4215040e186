//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
page 50946 "Purchase Doc Modifier Line"
{
    Caption = 'Purchase Doc Modifier Line';
    AutoSplitKey = true;
    DelayedInsert = true;
    PageType = ListPart;
    UsageCategory = None;
    SourceTable = "Purchase Doc Modifier Line";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Modifier Code"; "Modifier Code")
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                }
                field(FieldID; FieldID)
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                }
                field(Type; Type)
                {
                    ApplicationArea = All;
                }
                field("Purchase Line No."; "Purchase Line No.")
                {
                    Editable = false;
                    // Editable = IsPurchaseLineVisible;
                    // Visible = IsPurchaseLineVisible;
                    ApplicationArea = All;

                    trigger OnDrillDown()
                    var
                        ModifierHeader: Record "Purchase Doc Modifier";
                    begin
                        ModifierHeader.Get(Rec."Modifier Code");
                        if ModifierHeader.Status = ModifierHeader.Status::Open then
                            if ModifierHeader."Record Type" = ModifierHeader."Record Type"::"Purchase Line" then
                                DrilldownPurchaseLineNo();
                    end;
                }
                field("Field Name"; "Field Name")
                {
                    Editable = false;
                    ApplicationArea = All;

                    trigger OnDrillDown()
                    var
                        ModifierHeader: Record "Purchase Doc Modifier";
                    begin
                        ModifierHeader.Get(Rec."Modifier Code");
                        if ModifierHeader.Status = ModifierHeader.Status::Open then
                            DrilldownFieldName();
                    end;
                }
                field("Default Value"; Rec."Default Value")
                {
                    ApplicationArea = all;
                    ToolTip = 'Specifies the value of the Old Value field.', Comment = '%';
                }
                field("New Value"; Rec."New Value")
                {
                    Editable = true;
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the New Value field.', Comment = '%';
                }
                field("Decimal Field"; Rec."Decimal Field")
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Decimal Field field.', Comment = '%';
                }
                field("Date Field"; "Date Field")
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date', Comment = '%';
                }
                field(TableID; TableID)
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    // Visible = IsCurrencyVisible;
                    Editable = IsCurrencyVisible;
                    ApplicationArea = Suite;
                    Importance = Promoted;
                    ToolTip = 'Specifies the value of the Currency Code field.', Comment = '%';

                    trigger OnAssistEdit()
                    var
                        PurchaseHeader: Record "Purchase Header";
                        PurchaseHeaderModifier: Record "Purchase Doc Modifier";
                    begin
                        Clear(ChangeExchangeRate);
                        PurchaseHeaderModifier.SetRange("No.", Rec."Modifier Code");
                        if PurchaseHeaderModifier.FindFirst() then begin
                            PurchaseHeaderModifier.TestField("Document No.");
                            PurchaseHeader.SetRange("No.", PurchaseHeaderModifier."Document No.");
                            if PurchaseHeader.FindFirst() then begin
                                if PurchaseHeader."Posting Date" <> 0D then
                                    ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", PurchaseHeader."Posting Date")
                                else
                                    ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", WorkDate);

                                if ChangeExchangeRate.RunModal = ACTION::OK then begin
                                    Validate("Currency Factor", ChangeExchangeRate.GetParameter);
                                end;
                                Clear(ChangeExchangeRate);
                            end;
                        end;
                    end;

                    trigger OnValidate()
                    begin
                        if TableID = 39 then Error('You can''t set currency value for Purchase Line!');
                        if "Currency Code" = '' then "Currency Factor" := 0;
                        CurrPage.SaveRecord;
                    end;
                }
                field("Currency Factor"; Rec."Currency Factor")
                {
                    Visible = false;
                    Editable = false;
                    ApplicationArea = suite;
                    Importance = Promoted;
                    ToolTip = 'Specifies the value of the Currency Factor field.', Comment = '%';
                }
                field(Reason; Reason)
                {
                    Caption = 'Reason for Modification';
                    ApplicationArea = All;
                }
                field("Line No."; "Line No.")
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                }
                field("Record Type"; Rec."Record Type")
                {
                    Editable = false;
                    Visible = false;
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Record Type field.', Comment = '%';
                }
                field("Cc Code"; "Cc Code")
                {
                    Caption = 'Cc Code';
                    Editable = false;
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        IsCurrencyVisible := true;
    end;

    trigger OnAfterGetCurrRecord();
    begin
        IsCurrencyVisible := true;
        SetVisibleField();
    end;

    procedure SetVisibleField()
    begin
        if Rec."Record Type" = "Record Type"::"Purchase Header" then IsCurrencyVisible := true else IsCurrencyVisible := false;
        if Rec."Record Type" = "Record Type"::"Purchase Line" then IsPurchaseLineVisible := true else IsPurchaseLineVisible := false;
        CurrPage.Update(FALSE);
    end;

    procedure DrilldownFieldName()
    var
        ModifierHeader: Record "Purchase Doc Modifier";
        PurchaseLineRec: Record "Purchase Line";
        PurchaseHeaderRec: Record "Purchase Header";
        PurchaseRecRef: RecordRef;
        LineNo: Integer;
        IsPurchaseHeader: Boolean;
        FactorValue: Decimal;
        FieldRec: Record Field;
        FieldList: Page "Fields Lookup";
    begin
        if Rec."Modifier Code" = '' then
            exit;

        if Rec.TableID = 39 then TestField("Purchase Line No.");

        if xRec."Field Name" <> Rec."Field Name" then Rec.InitializeLineData();

        ModifierHeader.SetRange("No.", Rec."Modifier Code");
        if ModifierHeader.FindFirst() then begin
            ModifierHeader.TestField("Document No.");
            if ModifierHeader."Record Type" = ModifierHeader."Record Type"::"Purchase Header" then
                IsPurchaseHeader := true;

            if ModifierHeader.TableID = 0 then
                exit;

            if Rec.TableID <> ModifierHeader.TableID then Rec.TableID := ModifierHeader.TableID;
        end;

        CLEAR(FieldList);
        FieldRec.SETRANGE(FieldRec.TableNo, Rec.TableID);
        FieldList.SETTABLEVIEW(FieldRec);
        FieldList.LOOKUPMODE := true;
        if FieldList.RUNMODAL = ACTION::LookupOK then begin
            FieldList.GETRECORD(FieldRec);
            Rec.VALIDATE("Field Name", FieldRec.FieldName);
            Rec.VALIDATE(FieldID, FieldRec."No.");

            case IsPurchaseHeader of
                true:
                    begin
                        ModifierHeader.TestField("Document No.");
                        PurchaseHeaderRec.SetRange("No.", ModifierHeader."Document No.");
                        if PurchaseHeaderRec.FindFirst() then
                            PurchaseRecRef.GetTable(PurchaseHeaderRec);

                        Rec."Default Value" := Format(PurchaseRecRef.Field(FieldRec."No."));

                        if Rec."Field Name".Contains('Currency') then begin
                            Rec."Currency Code" := Format(PurchaseRecRef.Field(FieldRec."No."));
                            Evaluate(FactorValue, Format(PurchaseRecRef.Field(33)));
                            Rec."Currency Factor" := FactorValue;
                        end;
                    end;

                false:
                    begin
                        PurchaseLineRec.SetRange("Document No.", ModifierHeader."Document No.");
                        PurchaseLineRec.SetRange("Line No.", Rec."Purchase Line No.");
                        if PurchaseLineRec.FindFirst() then begin
                            PurchaseRecRef.GetTable(PurchaseLineRec);
                            Rec."Default Value" := Format(PurchaseRecRef.Field(FieldRec."No."));
                        end;
                    end;
            end;
        end;

        ModifierHeader.Reset();
        ModifierHeader.SetRange("No.", Rec."Modifier Code");
        if ModifierHeader.FindFirst() then begin
            ModifierHeader."workflow Handler" := Rec."Field Name";
            ModifierHeader.Modify();
        end;
        Commit();
    end;

    procedure DrilldownPurchaseLineNo()
    var
        ModifierHeader: Record "Purchase Doc Modifier";
        PurchaseLine: Record "Purchase Line";
        PurchaseLineLookUp: Page "Purchase Lines";
    begin
        if Rec."Modifier Code" = '' then
            exit;

        if xRec."Purchase Line No." <> Rec."Purchase Line No." then InitializeLineData();

        ModifierHeader.SetRange("No.", Rec."Modifier Code");
        if ModifierHeader.FindFirst() then begin
            ModifierHeader.TestField("Document No.");
            PurchaseLine.SetRange("Document No.", ModifierHeader."Document No.");
            PurchaseLine.SetFilter("Document Type", '=%1', ModifierHeader."Document type");
            PurchaseLineLookUp.LookupMode := true;
            PurchaseLineLookUp.SetTableView(PurchaseLine);
            if PurchaseLineLookUp.RunModal() = Action::LookupOK then begin
                PurchaseLineLookUp.GetRecord(PurchaseLine);

                if PurchaseLine.Quantity <> PurchaseLine."Quantity Invoiced" then begin
                    Rec.Validate("Purchase Line No.", PurchaseLine."Line No.");
                    Rec."Purchase Line No." := PurchaseLine."Line No.";
                    Rec."Document type" := PurchaseLine."Document Type";
                    Rec."Header No." := PurchaseLine."Document No.";
                    Rec."Item No." := PurchaseLine."No.";
                    Rec.Type := PurchaseLine.Type;
                end else
                    Error('You cannot select a fully Invoiced line');
            end;
        end;
        Commit();
    end;

    var
        ChangeExchangeRate: Page "Change Exchange Rate";
        [InDataSet]
        IsCurrencyVisible, isVisible, "New ValueVisible", "Default ValueVisible", IsPurchaseLineVisible : Boolean;
}
// >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
