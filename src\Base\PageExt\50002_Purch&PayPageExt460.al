pageextension 50002 PurchPayPagExt460 extends "Purchases & Payables Setup"
{
    layout
    {
        addafter("Posted Credit Memo Nos.")
        {
            field("Local Purchase Quote"; "Local Purchase Quote")
            {
                ApplicationArea = all;
            }
            field("Import Purchase Quote"; "Import Purchase Quote")
            {
                ApplicationArea = all;
            }
            field("Local Purchase Order"; "Local Purchase Order")
            {
                ApplicationArea = all;
            }
            field("Import Purchase Order"; "Import Purchase Order")
            {
                ApplicationArea = all;
            }
            field("Import Service Orders"; "Import Service Orders")
            {
                ApplicationArea = all;//PKONJ8
            }
            field("Local Purchase Invoice"; "Local Purchase Invoice")
            {
                ApplicationArea = all;
            }
            field("Import Purchase Invoice"; "Import Purchase Invoice")
            {
                ApplicationArea = all;
            }
            field("Import Purchase Credit Memo"; "Import Purchase Credit Memo")
            {
                ApplicationArea = all;
            }
            field("Local Purchase Credit Memo"; "Local Purchase Credit Memo")
            {
                ApplicationArea = all;
            }
            field("Import Blanket Purchase Order"; "Import Blanket Purchase Order")
            {
                ApplicationArea = all;
            }
            field("Local Blanket Purchase Order"; "Local Blanket Purchase Order")
            {
                ApplicationArea = all;
            }
            field("Reconsilation No."; "Reconsilation No.")
            {
                ApplicationArea = all;
            }
            field("Delivery Required"; "Delivery Required")
            {
                ApplicationArea = all;
            }
            field("Quality Required"; "Quality Required")
            {
                ApplicationArea = all;
            }
            field("Payment Terms Required"; "Payment Terms Required")
            {
                ApplicationArea = all;
            }
            field("Price Weightage"; "Price Weightage")
            {
                ApplicationArea = all;
            }
            field("Payment Terms Weightage"; "Payment Terms Weightage")
            {
                ApplicationArea = all;
            }
            field("Default Delivery Rating"; "Default Delivery Rating")
            {
                ApplicationArea = all;
            }
            field("Delivery Weightage"; "Delivery Weightage")
            {
                ApplicationArea = all;
            }
            field("Default Quality Rating"; "Default Quality Rating")
            {
                ApplicationArea = all;
            }
            field("Quote Comparision"; "Quote Comparision")
            {
                ApplicationArea = all;
            }
            field("Max Fuel Availed"; "Max Fuel Availed")
            {
                ApplicationArea = all;
            }
            field("PMS Purchase Invoice"; "PMS Purchase Invoice")
            {
                ApplicationArea = all;
            }
            field("GIT To Mail Alert ID"; "GIT To Mail Alert ID")
            {
                ApplicationArea = All;
            }
            field("GIT Mail"; "GIT Mail")
            {
                ApplicationArea = All;
            }
            /*field("SharedPoint Path"; "SharedPoint Path")
            {
                ApplicationArea = All;
            }
            field("SharePoint Path For Archive"; "SharePoint Path For Archive")
            {
                ApplicationArea = All;
            }*/
            field("PO Closing Period"; "PO Closing Period")
            {
                ApplicationArea = ALL;
            }
            field("Transport Inv Account"; "Transport Inv Account")
            {
                ApplicationArea = ALL;
            }
            field("Transport Inv Threshold Amt"; "Transport Inv Threshold Amt")
            {
                ApplicationArea = ALL;
            }
            field("FA Card Template - MRS"; "FA Card Template - MRS")
            {
                ApplicationArea = ALL;
            }
            field("FA Item Template - MRS"; "FA Item Template - MRS")
            {
                ApplicationArea = ALL;
            }
            field("Clearing Nos."; "Clearing Nos.")
            {
                ApplicationArea = ALL;
            }
            field("Import File Nos."; "Import File Nos.")
            {
                ApplicationArea = ALL;
            }
            field("LC Value Variation %"; "LC Value Variation %")
            {
                ApplicationArea = all;
            }
            field("Service Master No."; "Service Master No.")
            {
                ApplicationArea = all;
            }
            // >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
            field("PModifier Nos."; "PModifier Nos.")
            {
                Caption = 'Purchase Doc Modifier';
                ApplicationArea = All;
            }
            // <<<<<< G2S CAS-01334-J2M7C2 8/30/2024
            //B2BMSOn18Oct21>>
            field("Contract Email 1"; "Contract Email 1")
            {
                ApplicationArea = ALL;
            }
            field("Contract Email 2"; "Contract Email 2")
            {
                ApplicationArea = ALL;
            }
            field("Contract Email 3"; "Contract Email 3")
            {
                ApplicationArea = ALL;
            }
            field("Contract Duration"; "Contract Duration")
            {
                ApplicationArea = ALL;
            }
            //B2BMSOn18Oct21<<

            //B2BMSOn09Nov21>>
            field("Vendor Contract Check"; "Vendor Contract Check")
            {
                ApplicationArea = all;
            }
            //B2BMSOn09Nov21<<

            /*
                        field("SCD Approved Email 1"; "SCD Approved Email 1")
                        {
                            ApplicationArea = ALL;
                        }
                        field("SCD Approved Email 2"; "SCD Approved Email 2")
                        {
                            ApplicationArea = ALL;
                        }
                        field("SCD Approved Email 3"; "SCD Approved Email 3")
                        {
                            ApplicationArea = ALL;
                        }*/
        }

    }
    actions
    {
        addafter("Incoming Documents Setup")
        {
            action("UpdateNoSeries In Locations")
            {
                ApplicationArea = All;
                trigger OnAction()
                var
                    locationLrec: Record Location;
                begin
                    IF Not Confirm('Do you want to update Purch related No. series in Locations ?', True, False) then
                        exit;
                    locationLrec.Reset();
                    if locationLrec.FindSet then
                        repeat
                            locationLrec."Pur. Posted Invoice Nos." := "Posted Invoice Nos.";
                            locationLrec."Pur. Posted Credit Memo Nos." := "Posted Credit Memo Nos.";
                            locationLrec."Pur. Posted Receipt Nos." := "Posted Receipt Nos.";
                            locationLrec."Pur.Posted Return Shpt. Nos." := "Posted Return Shpt. Nos.";
                            locationLrec.Modify();
                        until locationLrec.Next() = 0
                end;
            }

        }
    }



}
