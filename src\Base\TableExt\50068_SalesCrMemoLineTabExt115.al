tableextension 50068 SalesCrMemoLineTabExt115 extends "Sales Cr.Memo Line"
{
    fields
    {
        field(50020; "Product. Discount Code"; Code[20])
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Lookup("Value Entry"."Prod. Discount Code" WHERE("Document No." = FIELD("Document No."), "Item No." = FIELD("No.")));
        }
        field(50025; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50026; "Posted Loading Slip Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }

    }

}