tableextension 50055 TransferLineExt extends "Transfer Line"
{
    fields
    {
        field(50000; "Transfer Price"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50001; Amount; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Branch Request Qty"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50003; "SKU Unit"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50004; "Production Batch No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Production Order No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Production Order Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "Maintenance Job Card No"; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
        field(50011; "Created Date"; DateTime)
        {
            // DataClassification = ToBeClassified;
            Editable = false;
        }
        // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
    }

    /*trigger OnModify()
    var
        TOH: Record "Transfer Header";
    begin
        IF TOH.GET("Document No.") THEN
            TOH.TestField("Approval Status", TOH."Approval Status"::Open);
    end;*/
    //Commented code 
    trigger OnModify()
    var
        TOH: Record "Transfer Header";
    begin
        //B2BPKON260521<<
        IF TOH.GET("Document No.") THEN
            IF TOH."order status" = TOH."order status"::"Short close" then
                error('Order is already Shortclosed.');

        // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
        updateCreatedTime();
        // <<<<<< G2S 30\05\2024 CAS-01301-G2L8L9
    end;

    trigger OnInsert()
    var
        TOH: Record "Transfer Header";
    begin
        //B2BPKON260521<<
        IF TOH.GET("Document No.") THEN
            IF TOH."order status" = TOH."order status"::"Short close" then
                error('Order is already Shortclosed.');

        // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
        updateCreatedTime();
        // <<<<<< G2S 30\05\2024 CAS-01301-G2L8L9
    end;

    trigger OnDelete()
    var
        TOH: Record "Transfer Header";
    begin
        //B2BPKON260521<<
        IF TOH.GET("Document No.") THEN
            IF TOH."order status" = TOH."order status"::"Short close" then
                error('Order is already Shortclosed.')
    end;
    //B2BMSOnAug11>>
    procedure OpenItemTrackingLinesCopy(Direction: Enum "Transfer Direction")
    begin
        TestField("Item No.");
        TestField("Quantity (Base)");

        //ReserveTransferLine.CallItemTracking(Rec, Direction);
        Codeunit50.CallItemTracking(Rec, Direction);
    end;

    // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
    procedure updateCreatedTime()
    var
        currDateTime: DateTime;
    begin
        TransferHeader.SetRange("No.", "Document No.");
        if TransferHeader.FindFirst() then begin
            if TransferHeader."Created Date" = 0DT then begin
                currDateTime := CreateDateTime(WorkDate(), Time);
                TransferHeader.Validate("Created Date", currDateTime);
                TransferHeader.Modify();
                Commit();
                "Created Date" := currDateTime;
            end else
                "Created Date" := TransferHeader."Created Date";
        end;
    end;
    // <<<<<< G2S 30\05\2024 CAS-01301-G2L8L9

    var
        Codeunit50: Codeunit Codeunit50;
        TransferHeader: Record "Transfer Header";

    //B2BMSOnAug11<<
}