// >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
codeunit 50096 "PModifier-CU"
{
    Permissions = tabledata "Purchase Header" = RIMD;

    trigger OnRun()
    begin
        // Add your code here 
    end;

    var
        ModifierHeader: Record "Purchase Doc Modifier";
        PurchaseHeader: Record "Purchase Header";
        TenantLicenseStateImpl: Codeunit "Tenant License State";
        DeleteDocErrorMsg: Label 'You cannot delete a Modifer Doc that has already been used to modify a Purchase Document';
        workflowsetup: codeunit "Workflow Setup";
        WorkflowevenHandling: Codeunit "Workflow Event Handling";
        WorkflowManagement: Codeunit "Workflow Management";
        ApprvMgt: Codeunit "Approvals Mgmt.";
        RecordRest: Codeunit "Record Restriction Mgt.";

    [EventSubscriber(ObjectType::Table, Database::"Purchase Doc Modifier Line", 'OnBeforeDeleteEvent', '', false, false)]
    local procedure OnBeforeDeleteEventModifierLine(var Rec: Record "Purchase Doc Modifier Line"; RunTrigger: Boolean)
    begin
        if Rec."Modifier Code" <> '' then
            ModifierHeader.Get(Rec."Modifier Code");

        if ModifierHeader."Document No." <> '' then
            PurchaseHeader.SetRange("Document Type", ModifierHeader."Document type");

        ModifierHeader.TestField(ModifierHeader.Status, ModifierHeader.Status::Open);
        PurchaseHeader.SetRange("No.", ModifierHeader."Document No.");
        if PurchaseHeader.FindFirst() then
            if PurchaseHeader.isModifed and ModifierHeader."Document Modified" then Error(DeleteDocErrorMsg);
        RunTrigger := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Doc Modifier", 'OnBeforeDeleteEvent', '', false, false)]
    local procedure OnBeforeDeleteEventModifierHeader(RunTrigger: Boolean; Rec: Record "Purchase Doc Modifier")
    begin
        Rec.TestField(Rec.Status, Rec.Status::Open);
        PurchaseHeader.Reset();
        if Rec."Document No." <> '' then begin
            PurchaseHeader.SetRange("Document Type", Rec."Document type");
            PurchaseHeader.SetRange("No.", Rec."Document No.");
            if PurchaseHeader.FindFirst() then
                if PurchaseHeader.isModifed and ModifierHeader."Document Modified" then Error(DeleteDocErrorMsg);
        end;
        RunTrigger := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", 'OnBeforeModifyEvent', '', false, false)]
    local procedure OnBeforeModifyEvent(var Rec: Record "Purchase Header"; var xRec: Record "Purchase Header")
    begin
        if Rec."From Purchase Modifier" then
            Rec.SetHideValidationDialog(true);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", 'OnBeforeUpdateCurrencyFactor', '', false, false)]
    local procedure OnBeforeUpdateCurrencyFactor(var PurchaseHeader: Record "Purchase Header"; var Updated: Boolean)
    begin
        if PurchaseHeader."From Purchase Modifier" then
            Updated := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", 'OnBeforeModifyEvent', '', false, false)]
    local procedure OnBeforeTestStatusOpenHEader(var Rec: Record "Purchase Header"; var xRec: Record "Purchase Header")
    begin
        if Rec."Purchase Order Tracking" = Rec."Purchase Order Tracking"::"Completely Received" then
            Rec."Completely Received" := true;
    end;

    procedure IsPaidMode(): Boolean
    begin
        exit(TenantLicenseStateImpl.IsPaidMode());
    end;

    procedure LicenseState()
    begin
        if not IsPaidMode() then
            if GetEndDate() < CurrentDateTime then Error('');
    end;

    procedure GetEndDate(): DateTime
    begin
        exit(TenantLicenseStateImpl.GetEndDate());
    end;

    procedure GetPeriod(TenantLicenseState: Enum "Tenant License State"): Integer
    begin
        exit(TenantLicenseStateImpl.GetPeriod(TenantLicenseState));
    end;

    // Purchase Doc Modifer Approval Workflow start

    //Action intgeration Event
    [IntegrationEvent(false, false)]
    Procedure OnSendPDMForApproval(var PDM: Record "Purchase Doc Modifier")
    begin
    end;

    [IntegrationEvent(false, false)]
    Procedure OnCancelPDMForApproval(var PDM: Record "Purchase Doc Modifier")
    begin
    end;

    //Create events for workflow
    procedure RunworkflowOnSendPDMforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('RunworkflowOnSendPDMforApproval'), 1, 128));
    end;


    [EventSubscriber(ObjectType::Codeunit, codeunit::"PModifier-CU", 'OnSendPDMForApproval', '', true, true)]
    local procedure RunworkflowonsendPDMForApproval(var PDM: Record "Purchase Doc Modifier")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOnSendPDMforApprovalCode(), PDM);
    end;

    procedure RunworkflowOnCancelPDMforApprovalCode(): code[128]
    begin
        exit(CopyStr(UpperCase('OnCancelPDMForApproval'), 1, 128));
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"PModifier-CU", 'OncancelPDMForApproval', '', true, true)]

    local procedure RunworkflowonCancelPDMForApproval(var PDM: Record "Purchase Doc Modifier")
    begin
        WorkflowManagement.HandleEvent(RunworkflowOncancelPDMforApprovalCode(), PDM);
    end;

    //Workflow Table Relation
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowTableRelationsToLibrary', '', false, false)]
    local procedure AddWorkflowTableRelationsToLibrary()
    var
        WorkflowSetup: Codeunit "Workflow Setup";
    begin
        WorkflowSetup.InsertTableRelation(Database::"Purchase Doc Modifier", 1, Database::"Approval Entry", 22);
        WorkflowSetup.InsertTableRelation(Database::"Purchase Doc Modifier", 1, Database::"Purchase Doc Modifier Line", 1);
        WorkflowSetup.InsertTableRelation(Database::"Purchase Doc Modifier", 23, Database::"Purchase Doc Modifier Line", 7);
        //Handle Workflow Page Request
        "Workflow Request Page Handling".CreateEntitiesAndFields();
    end;

    //Add events to library

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventsToLibrary', '', false, false)]
    local procedure OnAddWorkflowEventsToLibrary12();
    begin
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnSendPDMforApprovalCode(), DATABASE::"Purchase Doc Modifier",
          CopyStr(CheckPDMSendforapprovaleventdesctxt, 1, 250), 0, FALSE);
        WorkflowevenHandling.AddEventToLibrary(RunworkflowOnCancelPDMforApprovalCode(), DATABASE::"Purchase Doc Modifier",
          CopyStr(PDMrequestcanceleventdesctxt, 1, 250), 0, FALSE);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Event Handling", 'OnAddWorkflowEventPredecessorsToLibrary', '', true, true)]
    local procedure OnAddworkfloweventprodecessorstolibrary11(EventFunctionName: code[128]);
    begin
        case EventFunctionName of
            RunworkflowOnCancelPDMforApprovalCode():
                WorkflowevenHandling.AddEventPredecessor(RunworkflowOnCancelPDMforApprovalCode(), RunworkflowOnSendPDMforApprovalCode());
            WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnApproveApprovalRequestCode(), RunWorkflowOnSendPDMForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnRejectApprovalRequestCode(), RunWorkflowOnSendPDMForApprovalCode());
            WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode():
                WorkflowevenHandling.AddEventPredecessor(WorkflowevenHandling.RunWorkflowOnDelegateApprovalRequestCode(), RunWorkflowOnSendPDMForApprovalCode());
        end;
    end;

    procedure ISPDMworkflowenabled(var PDM: Record "Purchase Doc Modifier"): Boolean
    begin
        if PDM.Status <> PDM.Status::open then
            exit(false);
        exit(WorkflowManagement.CanExecuteWorkflow(PDM, RunworkflowOnSendPDMforApprovalCode()));
    end;

    Procedure CheckPDMApprovalsWorkflowEnabled(VAR PDM: Record "Purchase Doc Modifier"): Boolean // 1
    begin
        IF not ISPDMworkflowenabled(PDM) then
            exit(false) else
            exit(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnpopulateApprovalEntryArgument', '', true, true)]
    local procedure OnpopulateApprovalEntriesArgument12(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        PDM: Record "Purchase Doc Modifier";
    begin
        case RecRef.Number() of
            Database::"Purchase Doc Modifier":
                begin
                    RecRef.SetTable(PDM);
                    ApprovalEntryArgument."Document No." := FORMAT(PDM."No.");
                end;
        end;
    end;

    //Handling workflow response

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onopendocument', '', true, true)]
    local procedure Onopendocument11(RecRef: RecordRef; var Handled: boolean)
    var
        PDM: Record "Purchase Doc Modifier";
    begin
        case RecRef.Number() of
            Database::"Purchase Doc Modifier":
                begin
                    RecRef.SetTable(PDM);
                    PDM.Status := PDM.Status::Open;
                    PDM.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'OnreleaseDocument', '', true, true)]
    local procedure OnReleasedocument11(RecRef: RecordRef; var Handled: boolean)
    var
        PDM: Record "Purchase Doc Modifier";
    begin
        case RecRef.Number() of
            Database::"Purchase Doc Modifier":
                begin
                    RecRef.SetTable(PDM);
                    PDM.Status := PDM.Status::Released;
                    PDM.Modify();
                    Handled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'Onsetstatustopendingapproval', '', true, true)]
    local procedure OnSetstatusToPendingApproval11(RecRef: RecordRef; var IsHandled: boolean)
    var
        PDM: Record "Purchase Doc Modifier";
    begin
        case RecRef.Number() of
            Database::"Purchase Doc Modifier":
                begin
                    RecRef.SetTable(PDM);
                    PDM.Status := PDM.Status::"Pending Approval";
                    PDM.Modify();
                    IsHandled := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Response Handling", 'Onaddworkflowresponsepredecessorstolibrary', '', true, true)]
    local procedure Onaddworkflowresponseprodecessorstolibrary11(ResponseFunctionName: Code[128])
    var
        workflowresponsehandling: Codeunit "Workflow Response Handling";
    begin
        case ResponseFunctionName of
            workflowresponsehandling.SetStatusToPendingApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SetStatusToPendingApprovalCode(), RunworkflowOnSendPDMforApprovalCode());
            workflowresponsehandling.SendApprovalRequestForApprovalCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.SendApprovalRequestForApprovalCode(), RunworkflowOnSendPDMforApprovalCode());
            workflowresponsehandling.CancelAllApprovalRequestsCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.CancelAllApprovalRequestsCode(), RunworkflowOnCancelPDMforApprovalCode());
            workflowresponsehandling.OpenDocumentCode():
                workflowresponsehandling.AddResponsePredecessor(workflowresponsehandling.OpenDocumentCode(), RunworkflowOnCancelPDMforApprovalCode());
        end;
    end;


    //Setup claim workflow

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'OnAddworkflowcategoriestolibrary', '', true, true)]
    local procedure OnaddworkflowCategoryTolibrary11()
    begin
        workflowsetup.InsertWorkflowCategory(CopyStr(PDMCategoryTxt, 1, 20), CopyStr(PDMCategoryDescTxt, 1, 100));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Onafterinsertapprovalstablerelations', '', true, true)]
    local procedure OnInsertApprovaltablerelations11()
    Var
        ApprovalEntry: record "Approval Entry";
    begin
        workflowsetup.InsertTableRelation(Database::"Purchase Doc Modifier", 0, Database::"Approval Entry", ApprovalEntry.FieldNo("Record ID to Approve"));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Workflow Setup", 'Oninsertworkflowtemplates', '', true, true)]
    local procedure OnInsertworkflowtemplate11()
    begin
        InsertPDMApprovalworkflowtemplate();
    end;

    local procedure InsertPDMApprovalworkflowtemplate();
    var
        workflow: record Workflow;
    begin
        workflowsetup.InsertWorkflowTemplate(workflow, CopyStr(IncDocOCRWorkflowCodeTxt13, 1, 17), CopyStr(PDMApprWorkflowDescTxt, 1, 100), CopyStr(PDMCategoryTxt, 1, 20));
        InsertPDMApprovalworkflowDetails(workflow);
        workflowsetup.MarkWorkflowAsTemplate(workflow);
    end;

    local procedure InsertPDMApprovalworkflowDetails(var workflow: record Workflow);
    var
        PDM: Record "Purchase Doc Modifier";
        workflowstepargument: record "Workflow Step Argument";
        Blankdateformula: DateFormula;
    begin
        workflowsetup.PopulateWorkflowStepArgument(workflowstepargument, workflowstepargument."Approver Type"::Approver, workflowstepargument."Approver Limit Type"::"Direct Approver", 0, '', Blankdateformula, true);

        workflowsetup.InsertDocApprovalWorkflowSteps(workflow, BuildPDMtypecondition(PDM.Status::open), RunworkflowOnSendPDMforApprovalCode(), BuildPDMtypecondition(PDM.Status::"Pending Approval"), RunworkflowOnCancelPDMforApprovalCode(), workflowstepargument, true);
    end;

    local procedure BuildPDMtypecondition(status: integer): Text
    var
        PDM: Record "Purchase Doc Modifier";
    Begin
        PDM.SetRange(status, status);
        exit(StrSubstNo(PDMTypeCondnTxt, workflowsetup.Encode(PDM.GetView(false))));
    End;

    var
        PDMrequestcanceleventdescTxt: Label 'Approval of a Purchase document Modifier is Cancelled';
        CheckPDMSendforapprovaleventdesctxt: Label 'Approval of a Purchase document Modifier is requested';
        NoworkfloweableErr: Label 'No Approval workflow for this record type is enabled.';
        PDMCategoryDescTxt: Label 'Purchase Document Modifier';
        PDMCategoryTxt: Label 'Purchase Doc Modifier';
        PDMApprWorkflowDescTxt: Label 'Purchase Doc Modifier Approval Workflow';
        IncDocOCRWorkflowCodeTxt13: Label 'INCDOC-PDM';
        PDMTypeCondnTxt: Label '<?xml version="1.0" encoding="utf-8" standalone="yes"?><ReportParameters><DataItems><DataItem name="PDM">%1</DataItem></DataItems></ReportParameters>';
        "Workflow Request Page Handling": Codeunit "PMOD Workflow Rqst Pg Handling";


    //Access record from the approval request page

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Page Management", 'Onaftergetpageid', '', true, true)]
    local procedure Onaftergetpageid12(RecordRef: RecordRef; var PageID: Integer)
    begin
        Case RecordRef.Number() of
            database::"Purchase Doc Modifier":
                PageID := GetcardPageid(RecordRef)
        end;
    end;

    local procedure GetcardPageid(RecordRef: RecordRef): Integer
    var
        MasterDataTemp: Record "Purchase Doc Modifier";
    begin
        Case RecordRef.Number() of
            database::"Purchase Doc Modifier":
                begin
                    RecordRef.SetTable(MasterDataTemp);
                    exit(page::"Purchase Doc Modifier");
                end;
        end;
    end;

    procedure GetModifierHeader(var ModifierHeader: Record "Purchase Doc Modifier")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseDocModifier: TestPage "Purchase Doc Modifier";
    begin
        PurchaseHeader.SetRange("No.", ModifierHeader."Document No.");
        if PurchaseHeader.FindFirst() then begin
            PurchaseHeader.TestField("Document Type", ModifierHeader."Document type");
            PurchaseHeader.TestField(Status, PurchaseHeader.Status::Released);
        end;

        PurchaseDocModifier."Modify PO".Invoke();
    end;

    // [EventSubscriber(ObjectType::Table, Database::"Rebate Records", 'OnAfterModifyEvent', '', false, false)]
    // local procedure MyProcedure(var Rec: Record "Rebate Records"; var xRec: Record "Rebate Records")
    // var
    //     Test: Text[20];
    // begin
    //     Test := 'Test';
    // end;
}
// >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
