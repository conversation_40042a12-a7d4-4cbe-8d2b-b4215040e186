page 50902 "Post. Imp. Purch. Rcpt Subform"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created for Posted Import Purchase Rcpts.

    AutoSplitKey = true;
    Caption = 'Post. Imp. Purch. Rcpt Subform';
    Editable = false;
    LinksAllowed = false;
    PageType = ListPart;
    SourceTable = "Purch. Rcpt. Line";

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field(Type; Type)
                {
                }
                field("No."; "No.")
                {
                }
                field("Cross-Reference No."; "Cross-Reference No.")
                {
                    Visible = false;
                }
                field("Variant Code"; "Variant Code")
                {
                    Visible = false;
                }
                field(Description; Description)
                {
                }
                field("Return Reason Code"; "Return Reason Code")
                {
                    Visible = false;
                }
                field("Location Code"; "Location Code")
                {
                }
                field("Bin Code"; "Bin Code")
                {
                    Visible = false;
                }
                field(Quantity; Quantity)
                {
                    BlankZero = true;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    Visible = false;
                }
                field("Quantity Invoiced"; "Quantity Invoiced")
                {
                    BlankZero = true;
                }
                field("Qty. Rcd. Not Invoiced"; "Qty. Rcd. Not Invoiced")
                {
                    Editable = false;
                    Visible = false;
                }
                field("Requested Receipt Date"; "Requested Receipt Date")
                {
                    Visible = false;
                }
                field("Promised Receipt Date"; "Promised Receipt Date")
                {
                    Visible = false;
                }
                field("Planned Receipt Date"; "Planned Receipt Date")
                {
                }
                field("Expected Receipt Date"; "Expected Receipt Date")
                {
                }
                field("Order Date"; "Order Date")
                {
                }
                field("Lead Time Calculation"; "Lead Time Calculation")
                {
                    Visible = false;
                }
                field("Job No."; "Job No.")
                {
                    Visible = false;
                }
                field("Prod. Order No."; "Prod. Order No.")
                {
                    Visible = false;
                }
                field("Inbound Whse. Handling Time"; "Inbound Whse. Handling Time")
                {
                    Visible = false;
                }
                field("Appl.-to Item Entry"; "Appl.-to Item Entry")
                {
                    Visible = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    Visible = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    Visible = false;
                }
                field(Correction; Correction)
                {
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Order &Tracking")
                {
                    Caption = 'Order &Tracking';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        ShowTracking;

                    end;
                }
                action("&Undo Receipt")
                {
                    Caption = '&Undo Receipt';

                    trigger OnAction();
                    begin
                        //CurrForm.PurchReceiptLines.PAGE.UndoReceiptLine;
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        ApproveUnDoreceipt;

                    end;
                }
                action("Undo &Receipt History")
                {
                    Caption = 'Undo &Receipt History';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        UndoReceiptHistory;

                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        _ShowDimensions;

                    end;
                }
                action("Co&mments")
                {
                    Caption = 'Co&mments';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        _ShowLineComments;

                    end;
                }
                action("Item &Tracking Entries")
                {
                    Caption = 'Item &Tracking Entries';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        _ShowItemTrackingLines;

                    end;
                }
                action("Item Invoice &Lines")
                {
                    Caption = 'Item Invoice &Lines';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50052. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchReceiptLines.PAGE.*/
                        _ShowItemPurchInvLines;

                    end;
                }
            }
        }
    }

    procedure ShowTracking();
    var
        ItemLedgEntry: Record "Item Ledger Entry";
        TempItemLedgEntry: Record "Item Ledger Entry" temporary;
        TrackingForm: Page "Order Tracking";
    begin
        TESTFIELD(Type, Type::Item);
        if "Item Rcpt. Entry No." <> 0 then begin
            ItemLedgEntry.GET("Item Rcpt. Entry No.");
            TrackingForm.SetItemLedgEntry(ItemLedgEntry);
        end else
            TrackingForm.SetMultipleItemLedgEntries(TempItemLedgEntry,
              DATABASE::"Purch. Rcpt. Line", 0, "Document No.", '', 0, "Line No.");

        TrackingForm.RUNMODAL;
    end;

    procedure _ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;

    procedure ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;

    procedure _ShowItemTrackingLines();
    begin
        Rec.ShowItemTrackingLines;
    end;

    procedure ShowItemTrackingLines();
    begin
        Rec.ShowItemTrackingLines;
    end;

    procedure UndoReceiptLine();
    var
        PurchRcptLine: Record "Purch. Rcpt. Line";
    begin
        PurchRcptLine.COPY(Rec);
        CurrPage.SETSELECTIONFILTER(PurchRcptLine);
        CODEUNIT.RUN(CODEUNIT::"Undo Purchase Receipt Line", PurchRcptLine);
    end;

    procedure _ShowItemPurchInvLines();
    begin
        TESTFIELD(Type, Type::Item);
        Rec.ShowItemPurchInvLines;
    end;

    procedure ShowItemPurchInvLines();
    begin
        TESTFIELD(Type, Type::Item);
        Rec.ShowItemPurchInvLines;
    end;

    procedure _ShowLineComments();
    begin
        Rec.ShowLineComments;
    end;

    procedure ShowLineComments();
    begin
        Rec.ShowLineComments;
    end;

    procedure ApproveUnDoreceipt();
    var
        //ApproveUndoRec: Record "Approve Undo Receipts";
        PurchRcptLine: Record "Purch. Rcpt. Line";
    begin
        /*
        TESTFIELD("Quantity Invoiced", 0);
        TESTFIELD(Quantity);
        PurchRcptLine.COPY(Rec);
        CurrPage.SETSELECTIONFILTER(PurchRcptLine);
        //insert into Approve Undo rec
        ApproveUndoRec.SETRANGE(ApproveUndoRec."GRN No.", PurchRcptLine."Document No.");
        ApproveUndoRec.SETRANGE("Line No.", PurchRcptLine."Line No.");
        ApproveUndoRec.SETRANGE("Item No.", PurchRcptLine."No.");
        if ApproveUndoRec.ISEMPTY then begin
            ApproveUndoRec.INIT;
            ApproveUndoRec."GRN No." := PurchRcptLine."Document No.";
            ApproveUndoRec."Line No." := PurchRcptLine."Line No.";
            ApproveUndoRec."Item No." := PurchRcptLine."No.";
            ApproveUndoRec.Description := PurchRcptLine.Description;
            ApproveUndoRec.Quantity := PurchRcptLine.Quantity;
            ApproveUndoRec."Location Code" := PurchRcptLine."Location Code";
            ApproveUndoRec."Order Date" := PurchRcptLine."Order Date";
            ApproveUndoRec."Posting Date" := PurchRcptLine."Posting Date";
            ApproveUndoRec."Unit of Measure Code" := PurchRcptLine."Unit of Measure Code";
            ApproveUndoRec.INSERT;
        end;
        FILTERGROUP(1);
        ApproveUndoRec.SETRANGE(ApproveUndoRec."GRN No.", PurchRcptLine."Document No.");
        ApproveUndoRec.SETRANGE("Line No.", PurchRcptLine."Line No.");
        ApproveUndoRec.SETRANGE("Item No.", PurchRcptLine."No.");
        FILTERGROUP(0);
        PAGE.RUN(PAGE::"Approve Undo Receipt Matrix", ApproveUndoRec);
        */
    end;

    procedure UndoReceiptHistory();
    var
        //ApproveUndoRec: Record "Approve Undo Receipts";
        PurchRcptLine: Record "Purch. Rcpt. Line";
    begin
        /*
        //TESTFIELD(Quantity);
        PurchRcptLine.COPY(Rec);
        CurrPage.SETSELECTIONFILTER(PurchRcptLine);
        FILTERGROUP(0);
        ApproveUndoRec.SETRANGE(ApproveUndoRec."GRN No.", PurchRcptLine."Document No.");
        ApproveUndoRec.SETRANGE("Line No.", PurchRcptLine."Line No.");
        ApproveUndoRec.SETRANGE(Status, 1);
        //ApproveUndoRec.SETRANGE("Item No.",PurchRcptLine."No.");
        FILTERGROUP(1);
        PAGE.RUN(PAGE::"Approve Undo Receipt Matrix", ApproveUndoRec);
        */
    end;
}

