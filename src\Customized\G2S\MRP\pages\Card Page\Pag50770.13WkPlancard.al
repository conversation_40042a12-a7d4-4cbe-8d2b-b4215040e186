page 50770 "13Wk Plan Card"
{
    Caption = '13Wk Plan Card';
    PageType = Card;
    SourceTable = "13Wk Header";
    UsageCategory = Documents;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;
                }
                field("Cc Code"; "Cc Code")
                {
                    ApplicationArea = All;
                }
                field("Created by"; "Created by")
                {
                    ApplicationArea = All;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = All;
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = All;
                }
                field("Active Version"; "Active Version")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
            }

            part("Plan Lines"; "13Wk Plan Line")
            {
                ApplicationArea = All;
                SubPageLink = "Document No" = FIELD("No.");
            }
        }
    }

    actions
    {
        area(Navigation)
        {
            action("Update Safety Stock")
            {
                Image = UpdateXML;
                ApplicationArea = all;
                Caption = 'Update Safety Stock';
                ToolTip = 'Executes the Update Safety Stock action.';
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;

                trigger OnAction()
                begin
                    Rec.TestField(Description);
                    Rec.TestField("Created By");

                end;
            }
            group(Approval)
            {
                action("Send Aprvl Rqst")
                {
                    Image = SendApprovalRequest;
                    ApplicationArea = all;
                    // Enabled = not OpenApprovalEntriesExist and CanRequestApprovalWorkFlow;
                    ToolTip = 'Executes the Send Approval Request action.';
                    Caption = 'Send Approval Request';
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;

                    trigger OnAction()
                    begin

                    end;
                }
                action("Cancel Aprvl Rqst")
                {
                    Image = CancelApprovalRequest;
                    ApplicationArea = all;
                    // Enabled = CanCelApprovalForRecord or CanCancelApprovalForWorkFlow;
                    Caption = 'Cancel Approval Request';
                    ToolTip = 'Executes the Cancel Approval Request action.';
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;

                    trigger OnAction()
                    begin

                    end;
                }
                action("Approval Entry")
                {
                    Image = Approvals;
                    ApplicationArea = all;
                    Caption = 'Approval Entry';
                    RunObject = page "Approval Entries";
                    RunPageLink = "Document No." = field("No.");
                }
            }
            action("Import 13 Weeks Plan")
            {
                Image = ImportExcel;
                ApplicationArea = all;
                Caption = 'Import 13 Weeks Plan';
                ToolTip = 'Import the 13 Weeks FG Supply Plan.';
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;

                trigger OnAction()
                var
                    importReport: Report "13Wk import Report";
                begin
                    TestField("No.");
                    TestField("Start Date");
                    TestField("End Date");
                    if not Confirm('Do you want to import 13 Weeks Plan ?', True, False) then
                        exit;
                    importReport.SetParameters(Rec."No.", Rec."Start Date", Rec."End Date");
                    importReport.Run();
                end;
            }
            group(Function)
            {
                action("Archive Project")
                {
                    Image = Archive;
                    ApplicationArea = all;
                    Caption = 'Archive Project';
                    ToolTip = 'Executes the Archive action.';

                    trigger OnAction()
                    begin

                    end;
                }
                action("Re&lease")
                {
                    Image = ReleaseDoc;
                    ApplicationArea = all;
                    Caption = 'Release';
                    ToolTip = 'Executes the Release action.';
                    trigger OnAction()
                    begin

                    end;
                }
                action("Re&open")
                {
                    Image = ReOpen;
                    ApplicationArea = all;
                    Caption = 'Reopen';
                    ToolTip = 'Executes the ReOpen action.';
                    trigger OnAction()
                    begin

                    end;
                }
            }
            group(Plan)
            {
                Caption = 'Plan';
                action(Attachments)
                {
                    ApplicationArea = All;
                    Caption = 'Attachments';
                    Image = Attach;
                    ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                    trigger OnAction()
                    var
                        DocumentAttachmentDetails: Page "Document Attachment Details";
                        RecRef: RecordRef;
                    begin
                        RecRef.GetTable(Rec);
                        DocumentAttachmentDetails.OpenForRecRef(RecRef);
                        DocumentAttachmentDetails.RunModal();
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        SetCurrentVersion();
    end;

    procedure SetCurrentVersion()
    begin
        PlanLog.SetFilter("No.", '=%1', Rec."No.");
        if PlanLog.FindSet() then begin
            PlanLog.SetCurrentKey(Version);
            PlanLog.SetAscending(Version, true);
            if PlanLog.FindFirst() then begin
                "Active Version" := PlanLog.Version;
                Message('Active Version is %1', "Active Version");
            end;
        end;
    end;

    var
        "Active Version": Code[50];
        PlanLog: Record "13Wk Log Header";
}