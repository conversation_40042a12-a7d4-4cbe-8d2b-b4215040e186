page 50769 "Archived Purchase Modidiers"
{
    ApplicationArea = All;
    Caption = 'Archived Purchase Modidiers';
    PageType = List;
    SourceTable = "Purchase Doc Modifier";
    CardPageId = "Purchase Doc Modifier";
    UsageCategory = History;
    SourceTableView = where(Status = filter(Archived));
    Editable = false;
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the No. field.', Comment = '%';
                }
                field("Record Type"; Rec."Record Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Record Type field.', Comment = '%';
                }
                field("Document No."; Rec."Document No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Document No. field.', Comment = '%';
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Vendor No. field.', Comment = '%';
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Status field.', Comment = '%';
                }
                field("Date Modified"; Rec."Date Modified")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Date Modified field.', Comment = '%';
                }
            }
        }
    }
}
