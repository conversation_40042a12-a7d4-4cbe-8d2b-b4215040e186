page 50951 "Approval Entries View Page" //PKONAU11 Whole Object
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Approval Entry";
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;
    Permissions = tabledata "Approval Entry" = rm;

    layout
    {
        area(Content)
        {
            repeater(Details)
            {
                field("Table ID"; "Table ID")
                {
                    ApplicationArea = All;

                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = All;

                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;

                }
                field("Sequence No."; "Sequence No.")
                {
                    ApplicationArea = All;

                }
                field("Approval Code"; "Approval Code")
                {
                    Caption = 'Approval Code';
                }
                field("Sender ID"; "Sender ID")
                {
                    ApplicationArea = All;

                }
                field("Salespers./Purch. Code"; "Salespers./Purch. Code")
                {
                    ApplicationArea = All;

                }
                field("Approver ID"; "Approver ID")
                {
                    ApplicationArea = All;

                }
                field(Status; Status)
                {
                    ApplicationArea = All;

                }
                field("Date-Time Sent for Approval"; "Date-Time Sent for Approval")
                {
                    ApplicationArea = All;

                }
                field("Last Date-Time Modified"; "Last Date-Time Modified")
                {
                    ApplicationArea = All;

                }
                field("Last Modified By User ID"; "Last Modified By User ID")
                {
                    ApplicationArea = All;

                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Delete Entries")
            {
                Caption = 'Cancel Request.';
                trigger OnAction()
                var
                    Userset: Record "User Setup";
                    Appr: Record "Approval Entry";
                    ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                    ApprovalEntry: Record "Approval Entry";
                begin
                    Userset.get(UserId);
                    IF Not Userset."Delete Approval Entries" then
                        error('You dont have permissions to perform this action.');
                    CurrPage.SetSelectionFilter(ApprovalEntry);
                    ApprovalsMgmt.RejectApprovalRequests(ApprovalEntry);
                    /*Appr.Reset();
                    Appr.SetRange("Document Type", "Document Type");
                    Appr.SetRange("Document No.", "Document No.");
                    IF Appr.findset THEN begin
                        IF Confirm(StrSubstNo('The Number of lines for this document is %1', Appr.Count), True, False) THEN BEGIn
                            Appr.Modifyall(Status, Status::Canceled);
                            Message('Cancelled.');
                        end;
                    end;*/
                end;
            }
        }
    }

}