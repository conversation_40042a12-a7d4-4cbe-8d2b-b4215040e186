table 50044 "Confirmed Teller Receipt"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // *********************************************************************************************
    // SIGN
    // *********************************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry Osinigben
    // *********************************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // *********************************************************************************************
    // 1.0       HO       18-06-2013    -> Table created for Confirmed Tellers Receipts functionality.
    // CRF:2019-0090 SAA 28-10-19    -> Code added in CreateBankReceiptVoucher()
    // CRF:2019-100  NYO   21-12-19   ->Code added in CreateBankReceiptVoucher(),
    // New Option added in "Credit Account Type" field as Vendor

    Permissions = TableData "Approval Entry" = ri,
                  TableData "Posted Approval Entry" = ri;

    fields
    {
        field(1; "Branch Code"; Code[20])
        {
        }
        field(2; "Bank Deposited"; Option)
        {
            //OptionMembers = ,ZIB,FBN,UBA,CITI,GTB,SKYE,FBNCITI,SCB,STR,WEMA,DMB,REBATE;//PKONDE16
            OptionMembers = ,ZIB,FBN,UBA,CITI,GTB,SKYE,FBNCITI,SCB,STR,WEMA,DMB,REBATE,CORONATION;//PKONDE16
        }
        field(3; "Teller No."; Code[30])
        {
            trigger OnValidate()
            var
                ConfirmedTellerReceipt: record "Confirmed Teller Receipt";
                TellerErr: Label 'You cannot repeat Teller No. %1 already Exist.';
            BEGIN
                ConfirmedTellerReceipt.reset;
                ConfirmedTellerReceipt.SetRange("Teller No.", "Teller No.");
                IF ConfirmedTellerReceipt.findfirst then
                    Error(TellerErr, "Teller No.");
            END;
        }
        field(4; "Teller Date"; Date)
        {
        }
        field(5; "Teller Amount"; Decimal)
        {
        }
        field(6; "Cheque No."; Code[20])
        {
        }
        field(7; "Cheque Date"; Date)
        {
        }
        field(8; "Bank Issued"; Code[10])
        {
        }
        field(9; "Customer Name"; Code[100])
        {
        }
        field(10; "Bank Code"; Option)
        {
            //OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE"';//PKONDE16
            //OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE;//PKONDE16
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE",CORONATION,PROVIDUS';//PKONDE16
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,CORONATION,PROVIDUS;//PKONDE16

        }
        field(11; "Confirmation No."; Code[10])
        {
        }
        field(12; "Confirmation Date"; Date)
        {
            Editable = false;
        }
        field(13; "Confirmation Time"; Time)
        {
            Editable = false;
        }
        field(14; "Confirmed By"; Code[50])
        {
            Editable = false;
        }
        field(15; TellerIsConfirmed; Boolean)
        {
        }
        field(16; "Line No."; Integer)
        {
        }
        field(17; "Last Modified By"; Code[50])
        {
        }
        field(18; "Last Modified Date"; DateTime)
        {
        }
        field(19; "Unposted Teller No."; Code[20])
        {
        }
        field(21; "Global Dimension 1 Code"; Code[20])
        {
            Caption = 'Branch Name';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));
        }
        field(23; Company; Option)
        {
            OptionMembers = CHI,AJANLA,"TGI-NIG";
        }
        field(24; "Chq Value Date"; Date)
        {
        }
        field(25; "Branch names"; Option)
        {
            OptionMembers = ,ABA,ABUJA,AJANLA,BENIN,GOMBE,IBADAN,KADUNA,KANO,MAIDUGURI,MAKURDI,ONITSHA,"PORT-HARCOURT",SOKOTO,YOLA,AJAO,IKEJA,IKOYI,"S\LERE",APAPA,"TGI-KAN","TGI-ABJ",ENUGU;
        }
        field(26; "Teller Type"; Option)
        {
            OptionCaption = '" ,Teller Cash,Teller Cheque,Post Dated Cheque,E-Payment"';
            OptionMembers = " ","Teller Cash","Teller Cheque",PDC,"E-Payment";
        }
        field(27; "Bank Location"; Code[50])
        {
        }
        field(28; "No."; Integer)
        {
        }
        field(29; "Posted By"; Code[50])
        {
            Editable = false;
        }
        field(30; "Posted Date"; DateTime)
        {
            Editable = false;
        }
        field(31; "Return Confirmed Bank Teller"; Boolean)
        {
            Editable = true;

            trigger OnValidate();
            begin
                if UserSetup.GET(USERID) then
                    if not UserSetup."Teller/Cheque Awaiting BRV" then
                        ERROR(Text50200);

                if "Return Confirmed Bank Teller" then
                    TESTFIELD("Reason for Return");

                if not "Return Confirmed Bank Teller" then
                    CLEAR("Reason for Return");
            end;
        }
        field(32; Reversed; Boolean)
        {
        }
        field(33; "Reversed Date"; DateTime)
        {
            Editable = false;
        }
        field(34; "Reversed By"; Code[50])
        {
            Editable = false;
        }
        field(35; "Customer No."; Code[20])
        {
            TableRelation = "Customer Resp. Cent. Lines"."Customer No." WHERE("Resp. Center Code" = FIELD("Responsibility Center"));
        }
        field(36; "Global Dimension 2 Code"; Code[20])
        {
            Caption = 'CC';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = FILTER(2));
        }
        field(37; "Responsibility Center"; Code[20])
        {
            TableRelation = "Responsibility Center";
        }
        field(38; "Bank Name"; Text[100])
        {
        }
        field(39; "Created by"; Code[50])
        {
            Editable = false;
        }
        field(40; "Created Date"; Date)
        {
            Editable = false;
        }
        field(41; "Created Time"; Time)
        {
            Editable = false;
        }
        field(42; "Released for Confirmation"; Boolean)
        {
            Editable = true;
        }
        field(43; "Bank Receipt Created"; Boolean)
        {
        }
        field(44; "Paid By"; Text[200])
        {
        }
        field(45; "Released By"; Code[50])
        {
        }
        field(46; "Released Date"; Date)
        {
        }
        field(47; "Released time"; Time)
        {
        }
        field(49; "Created BRV No."; Code[20])
        {
            //TableRelation = "Voucher Header"."Document No." WHERE("Document No." = FIELD("Created BRV No."),
            // "Voucher Type" = FILTER(BRV));//B2BSB.1.0
        }
        field(50; "Reason for Return"; Text[200])
        {
        }
        field(51; "Create BRV"; Boolean)
        {
        }
        field(52; "Original Teller Recpt."; Boolean)
        {

            trigger OnValidate();
            begin
                if "Original Teller Recpt." then begin
                    "Original Teller Recvd. by" := USERID;
                    "Original Teller Recpt. Date" := TODAY;
                    "Original Teller Recpt. Time" := TIME;
                end;

                if not "Original Teller Recpt." then begin
                    CLEAR("Original Teller Recvd. by");
                    "Original Teller Recpt. Date" := 0D;
                    "Original Teller Recpt. Time" := 000000T;
                end;
            end;
        }
        field(53; "Original Teller Recpt. Date"; Date)
        {
        }
        field(54; "Original Teller Recpt. Time"; Time)
        {
        }
        field(55; "Original Teller Recvd. by"; Code[50])
        {
        }
        field(56; "Original Teller Posted by"; Code[50])
        {
        }
        field(57; "Original Teller Posted Date"; Date)
        {
        }
        field(58; "Original Teller Posted Time"; Time)
        {
        }
        field(59; "Original Teller Posted"; Boolean)
        {
        }
        field(65; "Credit Account Type"; Option)
        {
            Description = '//NYO 12/12/19 0100, Vendor added to the options';
            OptionCaption = '" ,Customer,G/L Account,Vendor"';
            OptionMembers = " ",Customer,"G/L Account",Vendor;
        }
        field(66; "G/L Account No."; Code[20])
        {
            TableRelation = IF ("Credit Account Type" = FILTER("G/L Account")) "G/L Account"."No."
            ELSE
            IF ("Credit Account Type" = FILTER(Vendor)) Vendor."No.";
        }
        field(67; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            Editable = false;
            TableRelation = Currency;

            trigger OnValidate();
            begin
                //TestStatusOpen; // UNL 1.0
            end;
        }
        field(68; "Teller Amount(LCY)"; Decimal)
        {
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Teller Amount (LCY)';
            Editable = false;

            trigger OnValidate();
            begin
                //TestStatusOpen; // UNL 1.0
            end;
        }
        field(69; "Currency Factor"; Decimal)
        {
            Caption = 'Currency Factor';
            DecimalPlaces = 0 : 15;
            Editable = false;
            MinValue = 0;
        }
        field(70; "G/L Account Name"; Text[100])
        {
            Editable = false;
        }
        field(71; Narration; Text[100])
        {
        }
    }

    keys
    {
        key(Key1; "No.")
        {
        }
        key(Key2; "Branch names")
        {
        }
    }

    fieldgroups
    {
    }

    trigger OnInsert();
    begin

        if BankConfirmRec.FINDLAST then
            "No." := BankConfirmRec."No." + 1
        else
            "No." := 1;
    end;

    trigger OnModify();
    begin
        //"Last Modified By":=USERID;
        //"Last Modified Date":=CURRENTDATETIME;
    end;

    var
        Text50200: Label 'You do not have permission to reverse confirmed bank tellers';
        UserSetup: Record "User Setup";
        BankConfirmRec: Record "Confirmed Teller Receipt";
        //VoucherHeader: Record "Voucher Header";//B2BSB.1.0
        BankTellerConfReg: Record "Confirmed Teller Receipt";
        Text50201: Label 'Payment by';
        VoucherLine: Record "Gen. Journal Line";
        RecordInsert: Boolean;
        Text50202: Label 'Bank receipt voucher %1 created sucessfuly.';
        Text50203: Label 'There is nothing to create.';
        CustRec: Record Customer;
        ApprEntryRec: Record "Approval Entry";
        Text50204: Label 'There is nothing to Post.';
        NoSeriesMgt: Codeunit NoSeriesManagement;
        LineNo: Integer;
        GLSetup: Record "General Ledger Setup";

    procedure CreateBankReceiptVoucher();
    var
        DocNo: Code[20];
    begin
        RecordInsert := false;

        BankTellerConfReg.RESET;
        BankTellerConfReg.SETRANGE("No.", "No.");
        BankTellerConfReg.SETRANGE("Return Confirmed Bank Teller", false);
        BankTellerConfReg.SETRANGE("Bank Receipt Created", false);
        BankTellerConfReg.SETRANGE("Create BRV", true);
        //NYO 26-09-2019 for G/L and Customer Request >>
        if "Credit Account Type" = "Credit Account Type"::Customer then
            BankTellerConfReg.SETRANGE("Customer No.", "Customer No.");
        if "Credit Account Type" = "Credit Account Type"::"G/L Account" then
            BankTellerConfReg.SETRANGE("G/L Account No.", "G/L Account No.");
        //NYO 26-09-2019 for G/L and Customer Request <<
        //NYO 12/12/19 0100, Vendor added to the options
        if "Credit Account Type" = "Credit Account Type"::Vendor then
            BankTellerConfReg.SETRANGE("G/L Account No.", "G/L Account No.");
        //NYO 12/12/19 0100, Vendor added to the options


        //BankTellerConfReg.SETFILTER("Customer No.", '<>%1','');
        if BankTellerConfReg.FINDFIRST then begin
            //REPEAT

            //IF BankTellerConfReg."Created BRV No." = '' THEN BEGIN
            /*VoucherHeader.INIT;
            VoucherHeader."Voucher Type" := VoucherHeader."Voucher Type"::BRV;
            VoucherHeader."Dim. Document Type" := VoucherHeader."Dim. Document Type"::BRV;
            //VoucherHeader.Narration :=Text50201 + ' '+ BankTellerConfReg."Customer Name";
            //NYO 26-09-2019 for G/L and Customer Request >>
            if "Credit Account Type" <> "Credit Account Type"::"G/L Account" then
                VoucherHeader.Narration := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No.";
            if "Credit Account Type" = "Credit Account Type"::"G/L Account" then
                VoucherHeader.Narration := BankTellerConfReg."Teller No.";
            //NYO 26-09-2019 for G/L and Customer Request <<

            if BankTellerConfReg."Teller Type" = BankTellerConfReg."Teller Type"::"Teller Cash" then
                VoucherHeader."Payment Mode" := VoucherLine."Payment Mode"::Teller;
            if BankTellerConfReg."Teller Type" = BankTellerConfReg."Teller Type"::"Teller Cheque" then
                VoucherHeader."Payment Mode" := VoucherHeader."Payment Mode"::Cheque;
            if BankTellerConfReg."Teller Type" = BankTellerConfReg."Teller Type"::PDC then
                VoucherHeader."Payment Mode" := VoucherHeader."Payment Mode"::PDC;
            if BankTellerConfReg."Teller Type" = BankTellerConfReg."Teller Type"::"E-Payment" then
                VoucherHeader."Payment Mode" := VoucherHeader."Payment Mode"::"E-Payment";
            VoucherHeader."Teller / Cheque Date" := BankTellerConfReg."Teller Date";
            VoucherHeader."Teller / Cheque No." := BankTellerConfReg."Unposted Teller No.";
            VoucherHeader."Bank Name" := BankTellerConfReg."Bank Name";
            VoucherHeader."Bank Receipt Type" := VoucherHeader."Bank Receipt Type"::Indirect;
            VoucherHeader."Teller Bank Name" := BankTellerConfReg."Bank Code";
            VoucherHeader."Bank Teller Paid By" := BankTellerConfReg."Paid By";
            VoucherHeader."Document No." := ''; //SAA3.0 to prevent document no. already exists error
            VoucherHeader.INSERT(true);

            VoucherHeader.VALIDATE("Shortcut Dimension 1 Code", BankTellerConfReg."Global Dimension 1 Code");
            VoucherHeader.VALIDATE("Shortcut Dimension 2 Code", BankTellerConfReg."Global Dimension 2 Code");


            VoucherHeader.VALIDATE("Account Type", VoucherHeader."Account Type"::"Bank Account");
            VoucherHeader.VALIDATE(VoucherHeader."Account No.", BankTellerConfReg."Bank Issued");
            VoucherHeader.VALIDATE("Responsibility Center", BankTellerConfReg."Responsibility Center");
            //NYO 26-09-2019 for G/L and Customer Request >>
            if "Credit Account Type" = "Credit Account Type"::Customer then begin
                VoucherHeader.VALIDATE("Receiving Type", VoucherHeader."Receiving Type"::Customer);
                VoucherHeader.VALIDATE("Receiving Code", BankTellerConfReg."Customer No.");
            end;
            if "Credit Account Type" = "Credit Account Type"::"G/L Account" then begin
                VoucherHeader.VALIDATE("Receiving Type", VoucherHeader."Receiving Type"::Others);
                //VoucherHeader.VALIDATE("Receiving Code",BankTellerConfReg."Customer No.");
            end;
            //NYO 12/12/19 0100, Vendor added to the options
            if "Credit Account Type" = "Credit Account Type"::Vendor then
                VoucherHeader.VALIDATE("Receiving Type", VoucherHeader."Receiving Type"::Vendor);
            //NYO 12/12/19 0100, Vendor added to the options

            //NYO 26-09-2019 for G/L and Customer Request >>

            //CRF: 2019 -0090 SAA >>
            if "Currency Code" <> '' then begin
                VoucherHeader.VALIDATE("Currency Code", "Currency Code");
                VoucherHeader.VALIDATE("Currency Factor", "Currency Factor");
            end;
            //CRF: 2019 -0090 SAA <<

            VoucherHeader."Created By" := BankTellerConfReg."Created by";
            VoucherHeader."Created Date" := BankTellerConfReg."Created Date";
            VoucherHeader."Created Time" := BankTellerConfReg."Created Time";

            VoucherHeader."Confirmed BY" := BankTellerConfReg."Confirmed By";
            VoucherHeader."Confirmed Date" := BankTellerConfReg."Confirmation Date";
            VoucherHeader."Confirmed Time" := BankTellerConfReg."Confirmation Time";
            VoucherHeader.Status := VoucherHeader.Status::Released;
            //VoucherHeader."Posting Doc. No." :=  NoSeriesMgt.GetNextNo(VoucherHeader."Posting No. Series",VoucherHeader."Posting Date",TRUE);
            VoucherHeader.MODIFY;*/
            Clear(LineNo);
            VoucherLine.reset;
            //VoucherLine.setrange("Voucher Type", VoucherLine."Voucher Type"::BRV);
            VoucherLine.setrange("Journal Template Name", 'BRV');
            VoucherLine.setrange("Journal Batch Name", 'BRV');
            IF VoucherLine.FindLast() then
                LineNo := VoucherLine."Line No." + 10000;

            VoucherLine.INIT;
            VoucherLine."Line No." := LineNo;
            VoucherLine."Voucher Type" := VoucherLine."Voucher Type"::BRV;
            VoucherLine."Journal Template Name" := 'BRV';
            VoucherLine."Journal Batch Name" := 'BRV';
            VoucherLine."Source Code" := 'BRV';

            VoucherLine.insert(true);
            GLSetup.get;
            GLSetup.Testfield(GLSetup."Bank Receipt Voucher No");
            voucherline."Document No." := NoSeriesMgt.GetNextNo(GLSetup."Bank Receipt Voucher No", WORKDATE, true);
            DocNo := voucherline."Document No.";
            //VoucherLine.VALIDATE("Document No.", VoucherHeader."Document No.");Prasanna
            //VoucherLine."Document Type" := VoucherLine."Document Type"::Payment;//added New
            //PKONNO15>>
            IF BankTellerConfReg."Customer No." <> '' THEN
                VoucherLine."Document Type" := VoucherLine."Document Type"::Payment
            else
                VoucherLine."Document Type" := VoucherLine."Document Type"::" ";
            //PKONNO15<<

            VoucherLine.VALIDATE("Posting Date", TODAY);
            //CRF: 2019 -0090 SAA >>
            if "Currency Code" <> '' then begin
                VoucherLine.VALIDATE("Currency Code", "Currency Code");
                VoucherLine.VALIDATE("Currency Factor", "Currency Factor");
            end;
            //CRF: 2019 -0090 SAA <<

            //NYO 26-09-2019 for G/L and Customer Request >>
            if "Credit Account Type" = "Credit Account Type"::Customer then begin
                VoucherLine.VALIDATE("Account Type", VoucherLine."Account Type"::Customer);
                VoucherLine.VALIDATE("Account No.", BankTellerConfReg."Customer No.");
            end;
            if "Credit Account Type" = "Credit Account Type"::"G/L Account" then begin
                VoucherLine.VALIDATE("Account Type", VoucherLine."Account Type"::"G/L Account");
                VoucherLine.VALIDATE("Account No.", BankTellerConfReg."G/L Account No.");
            end;
            //NYO 12/12/19 0100, Vendor added to the options
            if "Credit Account Type" = "Credit Account Type"::Vendor then begin
                VoucherLine.VALIDATE("Account Type", VoucherLine."Account Type"::Vendor);
                VoucherLine.VALIDATE("Account No.", BankTellerConfReg."G/L Account No.");
            end;
            VoucherLine."Bal. Account Type" := VoucherLine."Bal. Account Type"::"Bank Account";
            VoucherLine."Bal. Account No." := BankTellerConfReg."Bank Issued";
            //NYO 12/12/19 .0100, Vendor added to the options

            //NYO 26-09-2019 for G/L and Customer Request <<

            VoucherLine.VALIDATE("Credit Amount", BankTellerConfReg."Teller Amount");
            if VoucherLine."Shortcut Dimension 1 Code" = '' then
                VoucherLine.VALIDATE("Shortcut Dimension 1 Code", BankTellerConfReg."Global Dimension 1 Code");
            if VoucherLine."Shortcut Dimension 2 Code" = '' then
                VoucherLine.VALIDATE("Shortcut Dimension 2 Code", BankTellerConfReg."Global Dimension 2 Code");
            VoucherLine."Responsibility Center" := BankTellerConfReg."Responsibility Center";
            //if VoucherHeader.Narration <> '' then  //CRF:2019-0090 SAA//B2BSB.1.0
            if "Credit Account Type" = "Credit Account Type"::"G/L Account" then
                VoucherLine.Narration := BankTellerConfReg.Narration; //CRF:2019-0090 SAA

            //VoucherLine."Description 2" :=VoucherHeader.Narration + '/'+BankTellerConfReg."Teller No.";
            if "Credit Account Type" <> "Credit Account Type"::"G/L Account" then  //CRF:2019-0090 SAA
                                                                                   //VoucherLine."Description 2" := VoucherHeader.Narration + '/' + BankTellerConfReg."Teller No." else//B2BSB.1.0
                VoucherLine."Description 2" := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No." else
                VoucherLine."Description 2" := BankTellerConfReg."Teller No.";//CRF:2019-0090 SAA

            VoucherLine.VALIDATE(Cleared, true);

            //if VoucherLine.INSERT(true) then
            RecordInsert := true;
            VoucherLine.Modify;
            if RecordInsert then begin
                BankTellerConfReg."Bank Receipt Created" := true;
                //BankTellerConfReg."Created BRV No." := VoucherHeader."Document No.";//B2BSB.1.0
                BankTellerConfReg."Created BRV No." := voucherline."Document No.";
                BankTellerConfReg.MODIFY;

                //Create Approval Entry
                /* ApprEntryRec.SetRange("Document No.", VoucherHeader."Document No.");
                 if not ApprEntryRec.FindFirst() then begin
                     ApprEntryRec.INIT;
                     ApprEntryRec."Table ID" := DATABASE::"Voucher Header";
                     //ApprEntryRec."Document Type" :=ApprEntryRec."Document Type" ::BRV;//CHI1.0
                     ApprEntryRec."Document No." := VoucherHeader."Document No.";
                     ApprEntryRec."Sequence No." := 1;
                     ApprEntryRec."Approval Code" := USERID;
                     ApprEntryRec."Sender ID" := USERID;
                     ApprEntryRec."Approver ID" := USERID;
                     ApprEntryRec.Status := ApprEntryRec.Status::Approved;
                     ApprEntryRec."Date-Time Sent for Approval" := CURRENTDATETIME;
                     ApprEntryRec."Last Date-Time Modified" := CURRENTDATETIME;
                     ApprEntryRec."Last Modified By User ID" := USERID;
                     ApprEntryRec."Due Date" := TODAY;
                     ApprEntryRec.Amount := BankTellerConfReg."Teller Amount";
                     ApprEntryRec."Amount (LCY)" := BankTellerConfReg."Teller Amount";
                     ApprEntryRec."Approval Type" := ApprEntryRec."Approval Type"::Approver;
                     ApprEntryRec."Limit Type" := ApprEntryRec."Limit Type"::"Approval Limits";
                     if ApprEntryRec.INSERT then;
                 end;*/
            end;
            //UNTIL BankTellerConfReg.NEXT =0;
        end;


        if RecordInsert then
            MESSAGE(Text50202, voucherline."Document No.")
        else
            MESSAGE(Text50203);
    end;

    procedure CreateBankReceiptVoucherNPK();
    var
        DocNo: Code[20];
        RespCenter: Record "Responsibility Center";
    begin
        RecordInsert := false;

        BankTellerConfReg.RESET;
        //BankTellerConfReg.SETRANGE("No.", "No.");
        BankTellerConfReg.SETRANGE("Return Confirmed Bank Teller", false);
        BankTellerConfReg.SETRANGE("Bank Receipt Created", false);
        BankTellerConfReg.SETRANGE("Create BRV", true);
        /*
        if "Credit Account Type" = "Credit Account Type"::Customer then
            BankTellerConfReg.SETRANGE("Customer No.", "Customer No.");
        if "Credit Account Type" = "Credit Account Type"::"G/L Account" then
            BankTellerConfReg.SETRANGE("G/L Account No.", "G/L Account No.");
        if "Credit Account Type" = "Credit Account Type"::Vendor then
            BankTellerConfReg.SETRANGE("G/L Account No.", "G/L Account No.");*/
        if BankTellerConfReg.FINDSET then begin
            REPEAT
                Clear(LineNo);
                VoucherLine.reset;
                VoucherLine.setrange("Journal Template Name", 'BRV');
                VoucherLine.setrange("Journal Batch Name", 'BRV');
                IF VoucherLine.FindLast() then
                    LineNo := VoucherLine."Line No." + 10000;
                Message('Line no %1', LineNo);
                VoucherLine.INIT;
                VoucherLine."Line No." := LineNo;
                VoucherLine."Voucher Type" := VoucherLine."Voucher Type"::BRV;
                VoucherLine."Journal Template Name" := 'BRV';
                VoucherLine."Journal Batch Name" := 'BRV';
                VoucherLine."Source Code" := 'BRV';

                VoucherLine.insert(true);
                GLSetup.get;
                GLSetup.Testfield(GLSetup."Bank Receipt Voucher No");
                voucherline."Document No." := NoSeriesMgt.GetNextNo(GLSetup."Bank Receipt Voucher No", WORKDATE, true);
                DocNo := voucherline."Document No.";
                //if "Credit Account Type" <> "Credit Account Type"::"G/L Account" then//FIX28Oct2021
                //PKONNO15>>
                IF BankTellerConfReg."Customer No." <> '' THEN
                    VoucherLine."Document Type" := VoucherLine."Document Type"::Payment
                else
                    VoucherLine."Document Type" := VoucherLine."Document Type"::" ";
                //PKONNO15<<
                //PK On 05.10.2021
                GLSetup.TESTFIELD(GLSetup."Posted Bank Receipt Voucher No");
                VoucherLine."Posting No. Series" := GLSetup."Posted Bank Receipt Voucher No";
                if "Responsibility Center" <> '' then begin
                    RespCenter.SETRANGE(Code, "Responsibility Center");
                    RespCenter.SETFILTER("Posting Bank Recpts No.", '<>%1', '');
                    if RespCenter.FINDFIRST then
                        VoucherLine."Posting No. Series" := RespCenter."Posting Bank Recpts No." else
                        ERROR('The %1 for %2 - %3 must not be blank in the Responsibility Center table',
                        RespCenter.FIELDCAPTION("Posting Bank Recpts No."), FIELDCAPTION("Responsibility Center"), "Responsibility Center");
                end;
                //PK On 05.10.2021
                VoucherLine.VALIDATE("Posting Date", TODAY);
                if BankTellerConfReg."Currency Code" <> '' then begin
                    VoucherLine.VALIDATE("Currency Code", BankTellerConfReg."Currency Code");
                    VoucherLine.VALIDATE("Currency Factor", BankTellerConfReg."Currency Factor");
                end;
                //PKON22AP27>>
                IF "Credit Account Type" = "Credit Account Type"::Customer THEN
                    VoucherLine.Validate("Document Type", VoucherLine."Document Type"::Payment)
                else
                    VoucherLine.Validate("Document Type", VoucherLine."Document Type"::" ");
                //PKON22AP27<<
                if BankTellerConfReg."Credit Account Type" = BankTellerConfReg."Credit Account Type"::Customer then begin
                    VoucherLine.VALIDATE("Account Type", VoucherLine."Account Type"::Customer);
                    VoucherLine.VALIDATE("Account No.", BankTellerConfReg."Customer No.");
                end;
                if BankTellerConfReg."Credit Account Type" = BankTellerConfReg."Credit Account Type"::"G/L Account" then begin
                    VoucherLine.VALIDATE("Account Type", VoucherLine."Account Type"::"G/L Account");
                    VoucherLine.VALIDATE("Account No.", BankTellerConfReg."G/L Account No.");
                end;
                if BankTellerConfReg."Credit Account Type" = BankTellerConfReg."Credit Account Type"::Vendor then begin
                    VoucherLine.VALIDATE("Account Type", VoucherLine."Account Type"::Vendor);
                    VoucherLine.VALIDATE("Account No.", BankTellerConfReg."G/L Account No.");
                end;
                /*VoucherLine."Bal. Account Type" := VoucherLine."Bal. Account Type"::"Bank Account";
                VoucherLine."Bal. Account No." := BankTellerConfReg."Bank Issued";*/
                VoucherLine.VALIDATE("Credit Amount", BankTellerConfReg."Teller Amount");
                if VoucherLine."Shortcut Dimension 1 Code" = '' then
                    VoucherLine.VALIDATE("Shortcut Dimension 1 Code", BankTellerConfReg."Global Dimension 1 Code");
                if VoucherLine."Shortcut Dimension 2 Code" = '' then
                    VoucherLine.VALIDATE("Shortcut Dimension 2 Code", BankTellerConfReg."Global Dimension 2 Code");
                VoucherLine."Responsibility Center" := BankTellerConfReg."Responsibility Center";
                //if BankTellerConfReg."Credit Account Type" = BankTellerConfReg."Credit Account Type"::"G/L Account" then
                //    VoucherLine.Narration := BankTellerConfReg.Narration;
                //if BankTellerConfReg."Credit Account Type" <> BankTellerConfReg."Credit Account Type"::"G/L Account" then
                //    VoucherLine."Description 2" := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No." else
                //    VoucherLine."Description 2" := BankTellerConfReg."Teller No.";
                //VoucherLine."Description 2" := BankTellerConfReg.Narration;//PKON22AP5-CR220047
                VoucherLine.VALIDATE(Cleared, true);
                VoucherLine."Teller / Cheque No." := BankTellerConfReg."Teller No.";//Pk On 04.24.2021
                VoucherLine."Teller / Cheque Date" := BankTellerConfReg."Teller Date";//Pk On 04.24.2021
                VoucherLine."Paid By" := BankTellerConfReg."Paid By";//Fix05Jul2021
                RecordInsert := true;

                if BankTellerConfReg."Currency Code" <> '' then begin //PKONDE6
                    VoucherLine.VALIDATE("Currency Code", BankTellerConfReg."Currency Code");//PKONDE6
                    VoucherLine.VALIDATE("Currency Factor", BankTellerConfReg."Currency Factor");//PKONDE6
                end;
                //Message('%1...%2...%3', BankTellerConfReg."Currency Code", BankTellerConfReg."Currency Factor", VoucherLine."Currency Code");
                //BaluonMar17 2022>>
                VoucherLine."Gen. Prod. Posting Group" := '';
                VoucherLine."Gen. Bus. Posting Group" := '';
                VoucherLine."Gen. Posting Type" := VoucherLine."Gen. Posting Type"::" ";
                //VoucherLine."Document Type" := VoucherLine."Document Type"::" ";//PKON22AP27
                //BaluonMar17 2022<<
                //PKON22AP28 BUG FIX >>
                if BankTellerConfReg."Credit Account Type" = BankTellerConfReg."Credit Account Type"::"G/L Account" then BEGIN
                    IF BankTellerConfReg.Narration <> '' THEN BEGIN
                        VoucherLine.Narration := BankTellerConfReg.Narration;
                        VoucherLine."Description 2" := BankTellerConfReg.Narration + '/' + BankTellerConfReg."Teller No.";
                    END ELSE begin
                        VoucherLine.Narration := BankTellerConfReg."Teller No.";
                        VoucherLine."Description 2" := BankTellerConfReg."Teller No.";
                    end;
                END;
                if BankTellerConfReg."Credit Account Type" <> BankTellerConfReg."Credit Account Type"::"G/L Account" then BEGIN
                    VoucherLine."Description 2" := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No.";
                    VoucherLine.Narration := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No.";
                END;
                //PKON22AP28 BUG FIX <<
                VoucherLine.Modify;
                //Insert Balance Line
                VoucherLine.INIT;
                VoucherLine."Line No." := LineNo + 10010;
                Message('Line no %1', VoucherLine."Line No.");
                VoucherLine."Voucher Type" := VoucherLine."Voucher Type"::BRV;
                VoucherLine."Journal Template Name" := 'BRV';
                VoucherLine."Journal Batch Name" := 'BRV';
                VoucherLine."Source Code" := 'BRV';

                VoucherLine.insert(true);
                //PK On 05.10.2021
                GLSetup.TESTFIELD(GLSetup."Posted Bank Receipt Voucher No");
                VoucherLine."Posting No. Series" := GLSetup."Posted Bank Receipt Voucher No";
                if "Responsibility Center" <> '' then begin
                    RespCenter.SETRANGE(Code, "Responsibility Center");
                    RespCenter.SETFILTER("Posting Bank Recpts No.", '<>%1', '');
                    if RespCenter.FINDFIRST then
                        VoucherLine."Posting No. Series" := RespCenter."Posting Bank Recpts No." else
                        ERROR('The %1 for %2 - %3 must not be blank in the Responsibility Center table',
                        RespCenter.FIELDCAPTION("Posting Bank Recpts No."), FIELDCAPTION("Responsibility Center"), "Responsibility Center");
                end;
                //PK On 05.10.2021
                GLSetup.get;
                GLSetup.Testfield(GLSetup."Bank Receipt Voucher No");
                voucherline."Document No." := DocNo;//NoSeriesMgt.GetNextNo(GLSetup."Bank Receipt Voucher No", WORKDATE, true);
                //VoucherLine."Document Type" := VoucherLine."Document Type"::Payment;
                //PKON22AP27>>
                IF "Credit Account Type" = "Credit Account Type"::Customer THEN
                    VoucherLine.Validate("Document Type", VoucherLine."Document Type"::Payment)
                else
                    VoucherLine.Validate("Document Type", VoucherLine."Document Type"::" ");
                //PKON22AP27<<
                VoucherLine.VALIDATE("Posting Date", TODAY);
                if "Currency Code" <> '' then begin
                    VoucherLine.VALIDATE("Currency Code", "Currency Code");
                    VoucherLine.VALIDATE("Currency Factor", "Currency Factor");
                end;
                VoucherLine."Account Type" := VoucherLine."Bal. Account Type"::"Bank Account";
                VoucherLine.VALIDATE("Account No.", BankTellerConfReg."Bank Issued");
                VoucherLine.VALIDATE("Debit Amount", BankTellerConfReg."Teller Amount");
                if VoucherLine."Shortcut Dimension 1 Code" = '' then
                    VoucherLine.VALIDATE("Shortcut Dimension 1 Code", BankTellerConfReg."Global Dimension 1 Code");
                if VoucherLine."Shortcut Dimension 2 Code" = '' then
                    VoucherLine.VALIDATE("Shortcut Dimension 2 Code", BankTellerConfReg."Global Dimension 2 Code");
                VoucherLine."Responsibility Center" := BankTellerConfReg."Responsibility Center";
                //PKON22AP28 BUG FIX >>
                if BankTellerConfReg."Credit Account Type" = BankTellerConfReg."Credit Account Type"::"G/L Account" then BEGIN
                    IF BankTellerConfReg.Narration <> '' THEN BEGIN
                        VoucherLine.Narration := BankTellerConfReg.Narration;
                        VoucherLine."Description 2" := BankTellerConfReg.Narration + '/' + BankTellerConfReg."Teller No.";
                    END ELSE begin
                        VoucherLine.Narration := BankTellerConfReg."Teller No.";
                        VoucherLine."Description 2" := BankTellerConfReg."Teller No.";
                    end;
                END;
                if BankTellerConfReg."Credit Account Type" <> BankTellerConfReg."Credit Account Type"::"G/L Account" then BEGIN
                    VoucherLine."Description 2" := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No.";
                    VoucherLine.Narration := BankTellerConfReg."Customer Name" + '/' + BankTellerConfReg."Teller No.";
                END;
                //IF BankTellerConfReg.Narration <> '' THEN
                //VoucherLine."Description 2" := BankTellerConfReg.Narration;//PKON22AP5-CR220047  //PKON22AP28
                //PKON22AP28 BUG FIX <<
                VoucherLine.VALIDATE(Cleared, true);
                VoucherLine."Teller / Cheque No." := BankTellerConfReg."Teller No.";//Pk On 04.24.2021
                VoucherLine."Teller / Cheque Date" := BankTellerConfReg."Teller Date";//Pk On 04.24.2021
                VoucherLine."Paid By" := BankTellerConfReg."Paid By";//Fix05Jul2021
                RecordInsert := true;
                if BankTellerConfReg."Currency Code" <> '' then begin //PKONDE6
                    VoucherLine.VALIDATE("Currency Code", BankTellerConfReg."Currency Code");//PKONDE6
                    VoucherLine.VALIDATE("Currency Factor", BankTellerConfReg."Currency Factor");//PKONDE6 
                end;
                //Message('%1...%2...%3', BankTellerConfReg."Currency Code", BankTellerConfReg."Currency Factor", VoucherLine."Currency Code");
                //BaluonMar17 2022>>
                VoucherLine."Gen. Prod. Posting Group" := '';
                VoucherLine."Gen. Bus. Posting Group" := '';
                VoucherLine."Gen. Posting Type" := VoucherLine."Gen. Posting Type"::" ";
                //VoucherLine."Document Type" := VoucherLine."Document Type"::" ";//PKON22AP27<
                //BaluonMar17 2022<<
                VoucherLine.Modify;
                if RecordInsert then begin
                    BankTellerConfReg."Bank Receipt Created" := true;
                    BankTellerConfReg."Created BRV No." := voucherline."Document No.";
                    BankTellerConfReg.MODIFY;
                end;
            until BankTellerConfReg.next = 0;
        end;

        if RecordInsert then
            MESSAGE('Vouchers Created.')
        else
            MESSAGE(Text50203);
    end;

    procedure OriginalTellerReceiptAndPosted();
    begin
        BankTellerConfReg.RESET;
        BankTellerConfReg.SETRANGE("No.", "No.");
        BankTellerConfReg.SETRANGE("Bank Receipt Created", true);
        BankTellerConfReg.SETRANGE("Original Teller Recpt.", true);
        if BankTellerConfReg.FINDSET then begin
            REPEAT
                BankTellerConfReg."Original Teller Posted" := true;
                BankTellerConfReg."Original Teller Posted by" := USERID;
                BankTellerConfReg."Original Teller Posted Date" := TODAY;
                BankTellerConfReg."Original Teller Posted Time" := TIME;
                BankTellerConfReg.MODIFY;
            UNTIL BankTellerConfReg.NEXT = 0;
        end else
            ERROR(Text50204);
    end;

    //end;
    //end;
}
