page 50657 States
{
    ApplicationArea = All;
    Caption = 'States';
    PageType = List;
    SourceTable = States;
    UsageCategory = Lists;
    Permissions = tabledata 50370 = RIMD;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(State; State)
                {
                    ApplicationArea = ToBeClassified;
                }
                field("Capital"; "Capital")
                {
                    ApplicationArea = ToBeClassified;
                }
            }
        }
    }
}
