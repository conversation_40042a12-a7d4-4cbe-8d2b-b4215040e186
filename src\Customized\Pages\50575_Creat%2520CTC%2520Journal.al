page 50575 "Creat CTC Journal"
{
    // version CTC
    PageType = Card;
    UsageCategory = Tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            field(PostDate; PostDate)
            {
                ApplicationArea = all;
                Caption = 'Please Enter Posting Date';
            }
            field(AllType; AllType)
            {
                ApplicationArea = all;
                Caption = 'Enter Allocation Type';
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Creat CTC Journal")
            {
                ApplicationArea = all;
                Caption = 'Creat CTC Journal';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    if (PostDate = 0D) or (AllType = AllType::" ") then
                        ERROR('Pls Enter Posting Date and Allocation Type');

                    //CTCPosting.CreateFAJournal(AllType,PostDate);//CHI2018
                    CTCPosting.CreateFAJournal();
                    Message(Text000);
                end;
            }
        }
    }

    var
        CTCPosting: Codeunit "CTC Posting";
        PostDate: Date;
        AllType: Option " ",Maintenance,Depreciation;
        Text000: Label 'CTC Journals successfully created';
}

