page 50169 "Apprved Bank Pmt Vouchers Stf"
{
    // version CHI6.0

    // This page saved as from Apprved Bank Payment Vouchers (50353, Document) by B2BMSOn17Jan2022

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE   DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11  -> Form Created to display Bank Pmt. Document Details.
    // 
    // 3.0      SAA      08-Mar-12  -> Added codes to "Form-OnOpenForm" to filter out Responsibility Centres in Vouchers.
    //                              -> Added the "Responsibility Centre" field to the PAGE.
    //                   21-May-12  -> Added codes to assign and unassign Balance Account Type and Balance Account No. to
    //                                 enable check printing.
    // 
    // 1.0      HO       07-Sep-12  -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Bank Payment Voucher Document No.
    // B2BMS    B2BMS    17-Jan-22  -> Added code in Trigger OnNewRecord() to make Staff Voucher as True.

    Caption = 'Approved Bank Payment Vouchers - Staff';
    Editable = false;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(BPV),
                            Status = CONST(Released));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Transaction Type"; "Transaction Type")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        if "Transaction Type" <> "Transaction Type"::Import then
                            "Import File No." := '';
                        if "Transaction Type" <> "Transaction Type"::" " then
                            TESTFIELD("Account No.", '');
                    end;
                }
                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        if "Import File No." <> '' then
                            TESTFIELD("Transaction Type", "Transaction Type"::Import);
                    end;
                }
                field("Clearing File No."; "Clearing File No.")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Credit Account No.';

                    trigger OnValidate();
                    begin
                        if "Account No." <> '' then begin
                            TESTFIELD("Transaction Type");
                            if "Transaction Type" = "Transaction Type"::Import then
                                TESTFIELD("Import File No.");
                        end;
                    end;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Payment Type"; "Bank Payment Type")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Multiple Batch"; "Multiple Batch")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Create Zenith File"; "Create Zenith File")
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Bank Payment Voucher Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Payment Details")
            {
                Caption = 'Payment Details';
                field("Payable To"; "Payable To")
                {
                    ApplicationArea = all;
                }
                field("Payable Code"; "Payable Code")
                {
                    ApplicationArea = all;
                }
                field("Payable Name"; "Payable Name")
                {
                    ApplicationArea = all;
                }
                field(ToBeCollectedBy; ToBeCollectedBy)
                {
                    ApplicationArea = all;
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                    ApplicationArea = all;
                }
                field("Teller / Cheque Date"; "Teller / Cheque Date")
                {
                    ApplicationArea = all;
                }
                field(PaymentSettlementOf; PaymentSettlementOf)
                {
                    ApplicationArea = all;
                }
            }
            group("Inter-Company")
            {
                Caption = 'Inter-Company';
                field("IC Partner G/L Acc. No."; "IC Partner G/L Acc. No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Voucher Header", 24, "Document No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                action(Action1000000062)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        CurrPage.VoucherLines.PAGE.ShowLineDimensions;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send A&pproval Request")
                {
                    ApplicationArea = all;
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        //IF ApprovalMgt.SendVoucherApprovalRequest(Rec) THEN;
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    ApplicationArea = all;
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.CancelVoucherApprovalRequest(Rec,TRUE,TRUE) THEN;
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        CheckHeaderLines(Rec);
                        PerformManualRelease;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';

                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator(Separator1102152025)
                {
                }
                action("P&review Check")
                {
                    ApplicationArea = all;
                    Caption = 'P&review Check';
                    RunObject = Page "Check Preview";
                    Visible = false;
                }
                action("Print Check")
                {
                    ApplicationArea = all;
                    Caption = 'Print Check';

                    trigger OnAction();
                    var
                        GenJnlLine: Record "Gen. Journal Line" temporary;
                        GenJnlLine1: Record "Gen. Journal Line";
                        DocPrint: Codeunit "Document-Print";
                    begin
                        //Print Check
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        TESTFIELD(Status, Status::Released);
                        AssignBalAcctNo := false;
                        CLEAR(GenJnlLine1);
                        GenJnlLine1.SETRANGE("Journal Template Name", "Journal Template Code");
                        GenJnlLine1.SETRANGE("Journal Batch Name", "Journal Batch Name");
                        GenJnlLine1.SETRANGE(GenJnlLine1."Document No.", "Document No.");
                        if GenJnlLine1.FINDSET then
                            repeat
                                //GenJnlLine.INIT;
                                //GenJnlLine.TRANSFERFIELDS(GenJnlLine1);
                                GenJnlLine1."Bal. Account Type" := GenJnlLine1."Bal. Account Type"::"Bank Account";
                                GenJnlLine1."Bal. Account No." := Rec."Account No.";
                                GenJnlLine1.MODIFY;
                                COMMIT;
                                AssignBalAcctNo := true;
                            until GenJnlLine1.NEXT = 0;

                        // SAA 3.0 <<

                        GenJnlLine.RESET;
                        GenJnlLine.COPY(GenJnlLine1);
                        GenJnlLine.SETRANGE("Journal Template Name", "Journal Template Code");
                        GenJnlLine.SETRANGE("Journal Batch Name", "Journal Batch Name");
                        GenJnlLine.SETRANGE("Document No.", "Document No.");
                        DocPrint.PrintCheck(GenJnlLine);
                        //CODEUNIT.RUN(CODEUNIT::"Adjust Gen. Journal Balance",GenJnlLine);
                        //END;

                        // SAA3.0 >>
                        if AssignBalAcctNo then begin
                            CLEAR(GenJnlLine1);
                            GenJnlLine1.SETRANGE("Journal Template Name", "Journal Template Code");
                            GenJnlLine1.SETRANGE("Journal Batch Name", "Journal Batch Name");
                            GenJnlLine1.SETRANGE(GenJnlLine1."Document No.", "Document No.");
                            if GenJnlLine1.FINDSET then
                                repeat
                                    if GenJnlLine1."Check Printed" then begin
                                        GenJnlLine1."Check Printed" := false;
                                        GenJnlLine1.MODIFY;
                                        GenJnlLine1."Bal. Account Type" := GenJnlLine1."Bal. Account Type"::"G/L Account";
                                        GenJnlLine1."Bal. Account No." := ''; //Rec."Account No.";
                                        GenJnlLine1."Bank Payment Type" := GenJnlLine1."Bank Payment Type"::"Computer Check";
                                        GenJnlLine1."Check Printed" := true;
                                        GenJnlLine1.MODIFY;
                                        COMMIT;
                                    end;
                                until GenJnlLine1.NEXT = 0;
                        end;
                        // SAA3.0 <<
                    end;
                }
                action("Void Check")
                {
                    ApplicationArea = all;
                    Caption = 'Void Check';

                    trigger OnAction();
                    begin
                        CLEAR(Remarks);
                        d.OPEN('Enter Reason for Void and Press ENTER KEY \ Reason : #1#####################################');
                        //d.INPUT(1,Remarks);CHI 9.0
                        d.CLOSE;

                        if Remarks = '' then
                            repeat
                                MESSAGE('Please enter Reason again');
                                d.OPEN('Enter Reason for Void and Press ENTER KEY \ Reason : #1#####################################');
                                //d.INPUT(1,Remarks);CHI 9.0
                                d.CLOSE;
                            until Remarks <> '';
                        TESTFIELD(Status, Status::Released);
                        CurrPage.VoucherLines.PAGE.VoidCheque(Remarks);
                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    var
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        GenJrnlLine2.SETRANGE(GenJrnlLine2."Document No.", "Document No.");
                        if GenJrnlLine2.FINDSET then
                            repeat
                                if GenJrnlLine2."Bank Payment Type" = GenJrnlLine2."Bank Payment Type"::"Computer Check" then
                                    if (GenJrnlLine2."Check Printed" = false) or (GenJrnlLine2."Computer Check No." = '') then
                                        ERROR('You must Print a Cheque');
                            until GenJrnlLine2.NEXT = 0;
                        ClearValues();
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachmentPage.MoveAttachment(Rec);

                            MoveAttachmentPage.CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        Clear(DocumentNo);
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'BPV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'BPV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';
                    Enabled = BankPaymentStatus;


                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        Genjnlin2: Record "Gen. Journal Line 2";
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        Genjnlin2.Reset();
                        Genjnlin2.SetRange("Journal Batch Name", "Journal Batch Name");
                        Genjnlin2.SetRange("Journal Template Name", "Journal Template Code");
                        Genjnlin2.SetRange("Document No.", "Document No.");
                        Genjnlin2.SetFilter("WHT Amount", '=%1', 0);
                        If Genjnlin2.FindFirst() then
                            if not Confirm('WHT is not calculated in some lines, Do you want to continue ?', True, false) then
                                exit;


                        Clear(DocumentNo);
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'BPV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'BPV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        DocumentNo := "Document No.";
                        ClearValues();
                        CheckAppliesAmounts();
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachmentPage.MoveAttachment(Rec);

                            MoveAttachmentPage.CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action("Bank Payment Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }
                //Balu ********>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu ********<<
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50083, true, false, VoucherHeader);
                    //REPORT.RUN(50195, true, false, VoucherHeader);
                end;
            }

        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 20, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::BPV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<

        "Staff Voucher" := true; //B2BMSOn17Jan2022
    end;

    trigger OnAfterGetCurrRecord()
    begin
        //Providus Integration G2S 7th Aug 2024
        BankPaymentStatus := CheckBankpaymentStatus();
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        if UserMg.GetVoucherFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetVoucherFilter());
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<

        //B2BMSOn17Jan2022>>
        UserSetupGRec.Get(UserId);
        if not UserSetupGRec."Open Staff Bank Pmt Voucher" then
            Error('You donot have permissions to access this page. Please Enable it in User Setup.');
        //B2BMSOn17Jan2022<<
    end;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        GenJrnlLine2: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        Text50201: Label 'To be collected by must not be Blank';
        Text50202: Label 'Cheque No must not be blank';
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        AssignBalAcctNo: Boolean;
        //Text50000 : ;
        d: Dialog;
        Remarks: Text[80];
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        RecordRest: record "Restricted Record";
        UserSetupGRec: Record "User Setup"; //B2BMSOn17Jan2022
        BankPaymentStatus: Boolean;  //Providus Integration G2S 7th Aug 2024

    //Providus Integration G2S 7th Aug 2024
    local procedure CheckBankpaymentStatus(): Boolean
    var
        BankAPICredentials: Record "Bank API Credentials";
        GenJnlLine: Record "Gen. Journal Line 2";
        IsVendor: Boolean;
    begin
        BankAPICredentials.Reset();
        BankAPICredentials.SetRange("Bank Account No.", Rec."Account No.");
        if BankAPICredentials.FindFirst() then begin
            GenJnlLine.Reset();
            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FindSet() then begin
                repeat
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then
                        IsVendor := true;
                until (GenJnlLine.Next() = 0) OR IsVendor = true;
            end;
            if (Rec."Bank Payment Status" = Rec."Bank Payment Status"::Successful) AND (Rec.Status = Rec.Status::Released) AND IsVendor then
                EXIT(true) else
                if ("Bank Payment Status" = "Bank Payment Status"::" ") then
                    Exit(True) else
                    exit(false);
        end else
            Exit(true);
    end;

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        Vendor: Record Vendor;
    begin
        with VoucherHeaderRec do begin

            TESTFIELD("Transaction Type");
            TESTFIELD("Account No.");
            if "Transaction Type" = "Transaction Type"::Import then
                TESTFIELD("Import File No.");
            TESTFIELD(Narration);
            TESTFIELD("Responsibility Center");

            if ToBeCollectedBy = '' then
                ERROR(Text50201);

            //IF "Teller / Cheque No." = '' THEN
            //ERROR(Text50202);

            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            TESTFIELD("Posting Date");
            TESTFIELD(Narration);
            TESTFIELD(ToBeCollectedBy);
            TESTFIELD(PaymentSettlementOf);
            TESTFIELD("Payable To");
            if "Payable To" <> "Payable To"::Others then
                TESTFIELD("Payable Code");

            /*
            IF VoucherHeader."Payment Mode" = VoucherHeader."Payment Mode"::Teller THEN
              VoucherHeader.TESTFIELD("Teller Bank Name") ELSE
              VoucherHeader.TESTFIELD("Bank Name");
            */

            //TESTFIELD("Teller / Cheque No.");

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat


                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Description 2");

                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then begin

                        Vendor.GET(GenJnlLine."Account No.");
                        GenJnlLine.TESTFIELD("External Document No.");

                        if (Vendor."Service Group" = Vendor."Service Group"::Supplier) or
                          (Vendor."Service Group" = Vendor."Service Group"::Contractor) then begin
                            GenJnlLine.TESTFIELD("Vendor Payment Type");

                            if GenJnlLine."Vendor Payment Type" = GenJnlLine."Vendor Payment Type"::Advance then
                                GenJnlLine.TESTFIELD("LPO No.");

                            //GenJnlLine.TESTFIELD("Applies-to Doc. Type");
                            //GenJnlLine.TESTFIELD("Applies-to Doc. No.");

                            if GenJnlLine."Applies-to Doc. Type" = 0 then
                                ERROR('Applies-to Doc. Type must not be blank for this %1 Vendor', Vendor."Service Group");

                            if GenJnlLine."Applies-to Doc. No." = '' then
                                ERROR('Applies-to Doc. No. must not be blank for this Vendor', Vendor."Service Group");

                        end;

                    end;

                    //GenJnlLine."Posting Date" := TODAY;
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then begin
                        GenJnlLine.TESTFIELD("Fixed Asset Type");
                        GenJnlLine.TESTFIELD("Maintenance Code");
                    end;


                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"G/L Account" then begin
                        GenJnlLine.TESTFIELD("Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Posting Type", 0);
                        GenJnlLine.TESTFIELD("VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("VAT Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Prod. Posting Group", '');
                    end;

                    if GenJnlLine."IC Partner Code" <> '' then
                        GenJnlLine.TESTFIELD("IC Partner G/L Acc. No.");

                    GenJnlLine.MODIFY;

                until GenJnlLine.NEXT = 0;
            end;
        end;

    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    var
        MoveAttachmentPage: Page "Approved General JVs";

}

