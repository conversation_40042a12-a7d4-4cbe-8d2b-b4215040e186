//>>>>>> 14/8/24 CAS-01340-V7R3W7 Posted Journal Voucher
report 50165 "posted Journal Vouchers report"
{
    ApplicationArea = All;
    Caption = 'posted Journal Vouchers report_50165';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = RDLC;
    Permissions = tabledata "G/L Entry" = R;
    RDLCLayout = './src/Customized/G2S/Reports/Layouts/posted Journal Vouchers report.rdlc';

    dataset
    {

        dataitem("G/L Entry"; "G/L Entry")
        {
            DataItemTableView = sorting("Posting Date", "Document No.");
            RequestFilterFields = "Posting Date", "Document No.", "Global Dimension 1 Code", "Global Dimension 2 Code";
            column(Posting_Date; "Posting Date")
            {

            }
            column(Document_No_; "Document No.")
            {

            }
            column(G_L_Account_No_; "G/L Account No.")
            {

            }
            column(G_L_Account_Name; "G/L Account Name")
            {

            }
            column(Narration; Narration)
            {

            }
            column(Global_Dimension_1_Code; "Global Dimension 1 Code")
            {

            }
            column(Global_Dimension_2_Code; "Global Dimension 2 Code")
            {

            }
            column(Debit_Amount; "Debit Amount")
            {

            }
            column(Credit_Amount; "Credit Amount")
            {

            }
            column(Entry_No_; "Entry No.")
            {

            }
            column(COMPANYNAME; COMPANYNAME)
            {
            }
            column(COMPANYPICTURE; CompanyInfo.Picture)
            {
            }
            column(Amount; Amount)
            {
            }
            column(Source_Code; "Source Code")
            {
            }
            trigger OnPreDataItem()
            begin
                "G/L Entry".SetCurrentKey("Source Code");
                if SourceCode = '' then
                    "G/L Entry".SetFilter("Source Code", sourceCodeFilter)
                else
                    "G/L Entry".SetFilter("Source Code", SourceCode);


                if PrintToExcel then begin
                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 1);
                    TempExcelBuffer.VALIDATE("Column No.", 1);
                    TempExcelBuffer."Cell Value as Text" := 'CHI LIMITED';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 2);
                    TempExcelBuffer.VALIDATE("Column No.", 1);
                    TempExcelBuffer."Cell Value as Text" := 'Posted Journal Voucher Report';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 1);
                    TempExcelBuffer."Cell Value as Text" := 'Posting Date';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 2);
                    TempExcelBuffer."Cell Value as Text" := 'Document No.';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 3);
                    TempExcelBuffer."Cell Value as Text" := 'G/L Account No.';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;


                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 4);
                    TempExcelBuffer."Cell Value as Text" := 'G/L Account Name';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 5);
                    TempExcelBuffer."Cell Value as Text" := 'Naration';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;


                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 6);
                    TempExcelBuffer."Cell Value as Text" := 'Accounting  Location';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 7);
                    TempExcelBuffer."Cell Value as Text" := 'Cc Code';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 8);
                    TempExcelBuffer."Cell Value as Text" := 'Source Code';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 9);
                    TempExcelBuffer."Cell Value as Text" := 'Debit Amount';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 10);
                    TempExcelBuffer."Cell Value as Text" := 'Credit Amount';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", 4);
                    TempExcelBuffer.VALIDATE("Column No.", 11);
                    TempExcelBuffer."Cell Value as Text" := 'Entry No.';
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                end;
            end;

            trigger OnAfterGetRecord()
            begin
                IF PrintToExcel THEN BEGIN
                    rowno += 1;
                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 1);
                    TempExcelBuffer."Cell Value as Text" := FORMAT("G/L Entry"."Posting Date");
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 2);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry"."Document No.";
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 3);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry"."G/L Account No.";
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;


                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 4);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry"."G/L Account Name";
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 5);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry".Narration;
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;


                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 6);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry"."Global Dimension 1 Code";
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 7);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry"."Global Dimension 2 Code";
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 8);
                    TempExcelBuffer."Cell Value as Text" := "G/L Entry"."Source Code";
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 9);
                    TempExcelBuffer."Cell Value as Text" := Format("G/L Entry"."Debit Amount");
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                    //rowno:=rowno+1;
                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 10);
                    TempExcelBuffer."Cell Value as Text" := FORMAT("G/L Entry"."Credit Amount");
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;


                    TempExcelBuffer.INIT;
                    TempExcelBuffer.VALIDATE("Row No.", rowno);
                    TempExcelBuffer.VALIDATE("Column No.", 11);
                    TempExcelBuffer."Cell Value as Text" := FORMAT("G/L Entry"."Entry No.");
                    TempExcelBuffer.Formula := '';
                    TempExcelBuffer.INSERT;

                END;

            end;

            trigger OnPostDataItem()
            begin
                if PrintToExcel then begin

                end;
            end;
        }
    }
    requestpage
    {
        layout
        {
            area(Content)
            {
                field(PrintToExcel; PrintToExcel)
                {

                }
                field("Source Code"; SourceCode)
                {
                    TableRelation = "Source Code" where(Code = filter('BANKJV|BANKREC|BPV|BRV|CAJOUR|CAPACITJNL|CASHRECJNL|CONSUMPJNL|CPV|CRV|FAGLJNL|FAJNL|GENJNL|INSJNL|ITEMJNL|JOBGLJNL|JOBJNL|PAYMENTJNL|PAYMTRECON|PHYSINVJNL|POINOUTJNL|PRODORDER|PURCHJNL|RECLASSJNL|RESJNL|REVALJNL|SALESJNL|WHITEM|WHPHYSINVT|WHRCLSSJNL'));
                }

            }
        }
        actions
        {
            area(Processing)
            {
            }
        }
    }

    labels
    {
        Report_Name = 'POSTED JOURNAL VOUCHER';
        Report_ID = 'Report_ID : 50165';

    }

    trigger OnPreReport();
    begin
        CompanyInfo.GET();
        CompanyInfo.CalcFields(Picture);
        sourceCodeFilter := 'BANKJV|BANKREC|BPV|BRV|CAJOUR|CAPACITJNL|CASHRECJNL|CONSUMPJNL|CPV|CRV|FAGLJNL|FAJNL|GENJNL|INSJNL|ITEMJNL|JOBGLJNL|JOBJNL|PAYMENTJNL|PAYMTRECON|PHYSINVJNL|POINOUTJNL|PRODORDER|PURCHJNL|RECLASSJNL|RESJNL|REVALJNL|SALESJNL|WHITEM|WHPHYSINVT|WHRCLSSJNL';


        IF PrintToExcel THEN BEGIN
            rowno := 4;
            TempExcelBuffer.DELETEALL;
            CLEAR(TempExcelBuffer);
        END;
    end;

    trigger OnPostReport()
    begin
        IF PrintToExcel THEN BEGIN
            TempExcelBuffer.CreateNewBook('Posted Journal Voucher');
            TempExcelBuffer.WriteSheet('Posted Journal Voucher', CompanyName(), UserId());
            TempExcelBuffer.CloseBook();
            TempExcelBuffer.OpenExcel();
        END;
    end;

    var
        PrintToExcel: Boolean;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        rowno: Integer;
        CompanyInfo: Record "Company Information";
        SourceCode: Code[10];
        sourceCodeFilter: Text[1000];
}
//>>>>>> 14/8/24 CAS-01340-V7R3W7 Posted Journal Voucher

