pageextension 50112 ProdBOMVerVerExt extends "Production BOM Version"
{
    layout
    {
        modify("Version Code")
        {
            Editable = false;
        }
        modify(Status)
        {
            Editable = FALSE;
        }
        modify("Unit of Measure Code")
        {
            Editable = FALSE;
        }
        addafter(Status)
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = All;
                Editable = false;
            }

        }
    }

    actions
    {
        addafter("Copy BOM &Version")
        {
            action(InsertVersion)
            {
                ApplicationArea = ALL;
                image = Versions;
                Caption = 'Insert BOM Version';
                trigger OnAction()
                var
                    incrIntVar: Integer;
                    incrTxtVar: Text;
                    newVersionCode: Code[20];
                    ProductionBOM: Record "Production BOM Version";
                    ProductionBOMCopy: Record "Production BOM Version";
                    ProdBOM: Record "Production BOM Header";
                BEGIN
                    ProductionBOM.RESEt;
                    //G2S 05/11/24
                    ProductionBOM.SetCurrentKey("Starting Date");
                    ProductionBOM.SetAscending("Starting Date", true);
                    //G2S 05/11/24
                    ProductionBOM.SetRange("Production BOM No.", "Production BOM No.");
                    ProductionBOM.Setfilter("Version Code", '<>%1', '');
                    IF ProductionBOM.FindLast() THEN begin
                        "Version Code" := INCSTR(FORMAT(ProductionBOM."Version Code"));
                        //G2S 04/11/24
                        ProductionBOMCopy.Reset();
                        ProductionBOMCopy.SetRange("Production BOM No.", "Production BOM No.");
                        ProductionBOMCopy.Setfilter("Version Code", '<>%1', '');
                        if ProductionBOMCopy.FindSet() then
                            repeat
                                if ProductionBOMCopy."Version Code" = INCSTR(FORMAT(ProductionBOM."Version Code")) then begin
                                    incrTxtVar := format(ProductionBOMCopy."Version Code").Split('-').Get(2);
                                    Evaluate(incrIntVar, incrTxtVar);
                                    newVersionCode := format(ProductionBOMCopy."Version Code").Split('-').Get(1) + '-' + Format(incrIntVar + 1);
                                    "Version Code" := newVersionCode;
                                end
                            until ProductionBOMCopy.Next() = 0;
                        //G2S 04/11/24
                    end else
                        "Version Code" := FORMAT("Production BOM No.") + '-1';

                    IF ProdBOM.get("Production BOM No.") then begin
                        Description := ProdBOM.Description;
                        "Unit of Measure Code" := ProdBOM."Unit of Measure Code";
                    end;
                END;
            }
            action(WorkFlows)
            {
                ApplicationArea = All;
                Image = Action;

                trigger OnAction()
                begin
                    Message('This actions are for workflows.');
                end;
            }
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Approval Entries")
            {
                ApplicationArea = All;
                Image = Entries;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                var
                    ApprovalEntries: Page "Approval Entries";
                    ApprovalEntry: Record "Approval Entry";
                begin
                    ApprovalEntry.Reset();
                    ApprovalEntry.SetRange("Table ID", DATABASE::"Production BOM Version");
                    ApprovalEntry.SetRange("Document No.", "Production BOM No.");
                    ApprovalEntries.SetTableView(ApprovalEntry);
                    ApprovalEntries.RUN;
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    IF allinoneCU.CheckProdBOMVerApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendProdBOMVerForApproval(Rec);
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelProdBOMVerForApproval(rec);
                end;
            }

            action("Re&lease")
            {
                ApplicationArea = all;
                Caption = 'Re&lease';
                ShortCutKey = 'Ctrl+F11';
                Image = ReleaseDoc;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                var
                    WorkflowManagement: Codeunit "Workflow Management";
                begin
                    IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendProdBOMVerforApprovalCode()) then
                        error('Workflow is enabled. You can not release manually.');

                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        VALIDATE(Status, Status::Certified);
                        Modify();
                        Message('Document has been Released.');
                    end;
                end;
            }
            action("Re&open")
            {
                ApplicationArea = all;
                Caption = 'Re&open';
                Image = ReOpen;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction();
                var
                    RecordRest: Record "Restricted Record";
                begin
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 99000779);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        VALIDATE(Status, Status::"Under Development");
                        Modify();
                        Message('Document has been Reopened.');
                    end;
                end;
            }

            action(XMLPort)
            {
                ApplicationArea = All;
                Image = XMLFile;
                RunObject = xmlport 50001;
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;
    // B2BMS08022021>>
    trigger OnModifyRecord(): Boolean
    begin
        TestField("Approval Status", "Approval Status"::Open);
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        TestField("Approval Status", "Approval Status"::Open);
    end;
    // B2BMS08022021<<

    var
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit IJLSubEvents;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
}