pageextension 50086 PurchaseInvoiceExt extends "Purchase Invoice"
{
    layout
    {
        addafter(Status)
        {
            field("Arrival Date"; "Arrival Date")
            {
                ApplicationArea = all;
            }
        }

    }

    actions
    {
        addafter(Dimensions)
        {
            action("Get Gate Entry Lines")
            {
                Caption = 'Get Gate Entry Lines';
                trigger OnAction()
                begin
                    GetGateEntryLines;
                end;
            }
            action("Attached Gate Entry")
            {
                Caption = 'Attached Gate Entry';
                Image = InwardEntry;
                RunObject = page "Gate Entry Attachment List";
                RunPageLink = "Entry Type" = const(Inward), "Purchase Invoice No." = field("No.");
            }

        }
        modify(PostAndNew)
        {
            trigger OnBeforeAction()
            var
                PurchLinLvar: Record "Purchase Line";
            BEGIN

                TestField("Shortcut Dimension 1 Code");
                TestField("Shortcut Dimension 2 Code");
                PurchLinLvar.Reset();
                PurchLinLvar.SetRange("Document Type", "Document Type"::Invoice);
                PurchLinLvar.SetRange("Document No.", "No.");
                PurchLinLvar.Setfilter(Quantity, '<>%1', 0);
                PurchLinLvar.Setrange("WHT Applicable", false);
                IF PurchLinLvar.FindFirst() then
                    IF NOT Confirm('There is no WHT Applicable for Lines in this Order', false) then
                        error('Please give WHT Applicable for for order %1', PurchLinLvar."Document No.");//FIX19Jun2021


            END;
        }
    }
}