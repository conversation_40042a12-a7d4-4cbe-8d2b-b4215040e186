/// <summary>
/// Enum ShiftCodEnum (ID 50001).
/// </summary>
enum 50001 ShiftCodeEnum
{
    Extensible = true;

    value(0; A)
    {
        Caption = 'A';
    }
    value(1; B)
    {
        Caption = 'B';
    }
    value(2; C)
    {
        Caption = 'C';
    }
}

///Providus Integration 7th Aug 2024 
enum 50002 "Payment Status"
{
    Extensible = true;
    value(0; " ")
    {

    }
    value(1; New)
    {

    }
    value(2; Successful)
    {

    }
    value(3; "Pending Update")
    {

    }
    value(4; Failed)
    {

    }
    value(5; "Sent To Another Bank")
    {

    }
    value(6; "Sent For Payment")
    {

    }
    value(7; "Released For Payment")
    {

    }

}
///Providus Integration 7th Aug 2024 
