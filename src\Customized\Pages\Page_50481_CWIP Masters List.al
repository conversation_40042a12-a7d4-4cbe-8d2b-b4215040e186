page 50481 "CWIP List"
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "CWIP Masters";
    CardPageId = "CWIP Card";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(CWIP)
            {
                field("CWIP No."; "CWIP No.")
                {
                    ApplicationArea = All;

                }
                field("CWIP Name"; "CWIP Name")
                {
                    ApplicationArea = all;
                }
                field("GL Account No."; "GL Account No.")
                {
                    ApplicationArea = all;
                }
                field("FA No."; "FA No.")
                {
                    ApplicationArea = all;
                }
                field(Certified; Certified)
                {
                    ApplicationArea = all;
                }
                field("Certified Date"; "Certified Date")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Capex No."; "Capex No.")
                {
                    ApplicationArea = all;
                }
                field("Capex Line No."; "Capex Line No.")
                {
                    ApplicationArea = all;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                }

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}