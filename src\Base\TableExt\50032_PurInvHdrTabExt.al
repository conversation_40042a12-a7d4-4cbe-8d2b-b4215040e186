tableextension 50032 PurInvHdrTabbExt extends "Purch. Inv. Header"
{
    fields
    {
        field(50001; "Purchase Type"; Enum PurchaseType)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Purch Req. Ref. No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Purch. Req Header";
            Editable = false;
        }
        field(50009; "Reason Codes"; Enum ReasonCodes)
        {
            DataClassification = CustomerContent;

        }
        modify("Buy-from Vendor No.")
        {
            trigger OnBeforeValidate()
            Var
                VendGRec: Record Vendor;
            BEGIN
                IF VendGRec.GET("Buy-from Vendor No.") THEN
                    VendGRec.TestField("Approval Status", VendGRec."Approval Status"::Released);
            END;

        }
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            //B2B.P.K.T
        }
        field(50048; "Import File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }

        field(50049; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        //Balu 
        field(50050; "Arrival Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        //B2BMS
        field(50052; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50053; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50054; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50058; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
    }

    var
        myInt: Integer;
}