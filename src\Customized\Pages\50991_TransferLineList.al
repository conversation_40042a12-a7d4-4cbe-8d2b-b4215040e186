page 50991 TransferLineList
{
    Caption = 'Transfer Line List View';//PKONJ10.2 full object
    PageType = List;
    SourceTable = "Transfer Line";
    Insertallowed = false;
    ModifyAllowed = false;
    DeleteAllowed = false;
    ApplicationArea = all;
    UsageCategory = Lists;
    PromotedActionCategories = 'Update List';

    layout
    {
        area(Content)
        {
            repeater(Control)
            {
                field(OrStatus; OrStatus)
                {
                    ApplicationArea = All;
                    Caption = 'Order Status';
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;
                    Caption = 'Document No.';
                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                    Caption = 'Item No.';
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                    Caption = 'Description';
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    ApplicationArea = All;
                    Caption = 'Unit of Measure';
                }

                field("Transfer-from Code"; "Transfer-from Code")
                {
                    ApplicationArea = All;
                }
                field("Transfer-to Code"; "Transfer-to Code")
                {
                    ApplicationArea = All;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                    Caption = 'Quantity';
                }
                // >>>>>> G2S CAS-01301-G2L8L9
                field("Created Date"; Rec."Created Date")
                {
                    ApplicationArea = all;
                    ToolTip = 'Specifies the value of the Created Date field.', Comment = '%';
                }
                // >>>>>> G2S CAS-01301-G2L8L9
                field("Quantity Shipped"; "Quantity Shipped")
                {
                    ApplicationArea = All;
                }
                field("Quantity Received"; "Quantity Received")
                {
                    ApplicationArea = All;
                }
                field("Outstanding Quantity"; "Outstanding Quantity")
                {
                    ApplicationArea = All;
                }
            }

        }
    }

    actions
    {
        area(Processing)
        {
            // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
            action("Update List")
            {
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                Caption = 'Update List';

                trigger OnAction()
                begin
                    updateCreatedDate();
                end;
            }
            // <<<<<< G2S 30\05\2024 CAS-01301-G2L8L9
        }
    }
    trigger OnAfterGetRecord()
    begin
        OrStatus := 'Open';
        IF (Quantity <> 0) and ("Quantity Shipped" <> 0) and ("Quantity Received" = 0) then
            OrStatus := 'Only Shipped'
        else
            IF (Quantity <> 0) and ("Quantity Shipped" <> 0) and (Quantity = "Quantity Shipped") And (Quantity = "Quantity Received") then
                OrStatus := 'Completely Received'
            else
                IF (Quantity <> 0) and ("Quantity Shipped" <> "Quantity Received") then
                    OrStatus := 'Not Completely Received';

    end;

    // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
    procedure updateCreatedDate()
    begin
        TransferHeader.SetFilter("Created Date", '<>%1', 0DT);
        if TransferHeader.FindSet() then
            repeat
                TransferLine.SetFilter("Document No.", TransferHeader."No.");
                if TransferLine.FindSet() then
                    repeat
                        TransferLine."Created Date" := TransferHeader."Created Date";
                        TransferLine.Modify();
                    until TransferLine.Next() = 0;
                Commit();
            until TransferHeader.Next() = 0;
        Commit();
        CurrPage.Update();
    end;
    // <<<<<< G2S 30\05\2024 CAS-01301-G2L8L9

    var
        Orstatus: Text;
        // >>>>>> 30\05\2024 G2S CAS-01301-G2L8L9
        TransferHeader: Record "Transfer Header";
        TransferLine: Record "Transfer Line";
    // >>>>>> G2S 30\05\2024 CAS-01301-G2L8L9
}