tableextension 50137 WareSHPHdrExt extends "Warehouse Shipment Header"
{
    trigger OnInsert()
    var
        Loca: Record Location;
        Ware: Record "Warehouse Setup";
    begin
        /*
        Loca.GET("Location Code");
        Loca.TestField("Whse. Ship Nos.");
        Loca.TestField("Posted Whse. Ship Nos.");

        Ware.get();
        Ware."Whse. Ship Nos." := Loca."Whse. Ship Nos.";
        Ware."Posted Whse. Shipment Nos." := Loca."Posted Whse. Ship Nos.";
        Ware.Modify();
        Message('%1...%2', Ware."Whse. Ship Nos.", Ware."Posted Whse. Shipment Nos.");
        Commit();*/
    end;
}