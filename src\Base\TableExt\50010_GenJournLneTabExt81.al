tableextension 50010 GenJournLne81 extends "Gen. Journal Line"
{
    fields
    {
        modify(Amount)
        {
            trigger OnBeforeValidate()
            var
                DimenCodeBuffer: Record "Dimension Code Buffer";
                AmtErr: Label 'Amount Cannot Exceed Budget Amount still you want to continue?';
                GLEntry: Record "G/L Entry";
                GLAmt: Decimal;
                GLBudgetEntry: Record "G/L Budget Entry";
                GLBudgAmt: Decimal;
                LoaDetails: record "Loan Details B2B";
                TotalAmt: Decimal;
                AmountErr: Label 'You cannot assign morethan Loan Amount %1';
            BEGIN
                IF ("Loan ID" <> '') THEN BEGIN
                    LoaDetails.reset;
                    LoaDetails.SetRange("Loan Id", "Loan ID");
                    IF LoaDetails.findset then
                        repeat
                            TotalAmt += (LoaDetails."EMI Amount" - LoaDetails."EMI Deducted");
                        until LoaDetails.next = 0;
                END;
                /* IF TotalAmt < Amount THEN
                     error(AmountErr, TotalAmt);
                     *///Commented on 11.24.2020

                /*IF NOT "System-Created Entry" THEN BEGIN
                    clear(GLAmt);
                    GLEntry.reset;
                    GLEntry.SetRange("G/L Account No.", "Account No.");
                    IF GLEntry.findset then
                        repeat
                            GLAmt += GLEntry.Amount;
                        until GLENtry.Next = 0;
                    DimenCodeBuffer.reset;
                    DimenCodeBuffer.SetRange(code, "Account No.");
                    IF DimenCodeBuffer.FindFirst() then BEGIN
                        IF (Amount + GLAmt) > DimenCodeBuffer.Amount THEN
                            ERROR(AmtErr, DimenCodeBuffer.Amount);
                    END;

                    GLBudgetEntry.reset;
                    GLBudgetEntry.SetRange(Date, "Posting Date");
                    IF GLBudgetEntry.findFIRST then BEGIN
                        GLBudgAmt += GLBudgetEntry.Amount;
                    end;
                    IF (Amount + GLAmt) > GLBudgAmt THEN BEGIN
                        If Confirm(AmtErr, False) then
                            exit
                        else
                            clear(amount);
                    end;


                end;  */
            END;

            trigger OnAfterValidate()
            begin
                "WHT Amount" := Round((Amount * ("WHT %" / 100)), 1);
                if "Currency Factor" <> 0 then
                    "WHT Amount(LCY)" := Round(("Currency Factor" * (Amount * ("WHT %" / 100))), 1)
                else
                    "WHT Amount(LCY)" := Round((Amount * ("WHT %" / 100)), 1);
            end;
        }
        field(50000; "Responsibility Center"; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Responsibility Center";
        }
        field(50001; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;
            trigger OnValidate()
            var
                whtgrp: record WHTSetUp;
            begin
                CLEAR("WHT %");
                clear("WHT Account");
                CLEAR("WHT Amount");
                clear("WHT Amount(LCY)");
            end;
        }
        field(50013; "WHT %"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50014; "WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50029; "WHT Amount(LCY)"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(50017; "WHT Account"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50018; "Remaining Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Remaining Amount(LCY)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        modify("Account No.")
        {
            trigger OnAfterValidate()
            var
                FixedAsset: Record "Fixed Asset";
                MaintenanceLedgerEntry: Record "Maintenance Ledger Entry";
            begin
                Validate("WHT Group", '');
                IF ("Document Type" = "Document Type"::Payment) AND ("Account Type" = "Account Type"::Vendor) THEN BEGIN
                    IF VendorGRec.GET("Account No.") THEN BEGIN
                        Validate("WHT Group", VendorGRec."WHT Group");
                    END;
                END;
                //UserSetupGRec.GET(USERID);
                //UserSetupGRec.TestField("Gen. Jouranl Line Resp. Centre");
                //"Responsibility Center" := UserSetupGRec."Gen. Jouranl Line Resp. Centre";
                //TestField("Document No.");Balu 9 Feb
                "Voucher No." := "Document No.";
                if "Account Type" = "Account Type"::"Fixed Asset" then begin
                    if FixedAsset.Get("Account No.") then begin
                        FixedAsset.TestField("Approval Status", FixedAsset."Approval Status"::Released);
                        FixedAsset.TestField(Blocked, false);
                    end;
                    MaintenanceLedgerEntry.Reset();
                    MaintenanceLedgerEntry.SetRange("FA No.", FixedAsset."No.");
                    if MaintenanceLedgerEntry.FindLast() then
                        "Last Meter Reading" := MaintenanceLedgerEntry."Current Meter Reading";
                end;
            end;
        }
        modify("Bal. Account No.")
        {
            trigger OnAfterValidate()
            var
                FixedAsset: Record "Fixed Asset";
            begin
                if "Bal. Account Type" = "Bal. Account Type"::"Fixed Asset" then
                    if FixedAsset.Get("Bal. Account No.") then begin
                        FixedAsset.TestField("Approval Status", FixedAsset."Approval Status"::Released);
                        FixedAsset.TestField(Blocked, false);
                    end;
            end;
        }
        field(50019; "Date PMS Availed"; Date)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                IF "Date PMS Availed" <> 0D THEN BEGIN
                    PurchLine.RESET;
                    PurchLine.SETRANGE("No.", "Account No.");
                    PurchLine.SETRANGE("Date PMS Availed", "Date PMS Availed");
                    IF PurchLine.FINDFIRST THEN
                        ERROR('This Vehicle already have a pending Voucher on %1 not posted, Voucher No. %2',
                               "Date PMS Availed", PurchLine."Document No.");
                    GenRec.SETRANGE(GenRec."Account No.", "Account No.");
                    GenRec.SETRANGE(GenRec."Date PMS Availed", "Date PMS Availed");
                    GenRec.SETFILTER(GenRec."Document No.", '<>%1', "Document No.");
                    IF GenRec.FINDFIRST THEN
                        ERROR('This Vehicle already have a pending Voucher on %1 not posted, Voucher No. %2', "Date PMS Availed", GenRec."Document No.");
                    ItemJlLine.SETRANGE(ItemJlLine."FA No.", "Account No.");
                    ItemJlLine.SETRANGE(ItemJlLine."Date PMS Availed", "Date PMS Availed");
                    IF ItemJlLine.FINDFIRST THEN
                        ERROR('This Vehicle already have a pending Issue Journal on %1 not posted, Journal No. %2',
                              "Date PMS Availed", ItemJlLine."Document No.");
                    CLEAR("Current Km Reading");
                    CLEAR("Last Km Reading");
                    IF "Maintenance Code" <> '' THEN
                        VALIDATE("Maintenance Code");
                END;
                IF "Date PMS Availed" = 0D THEN BEGIN
                    CLEAR("Current Km Reading");
                    CLEAR("Last Km Reading");
                END;
            end;
        }
        field(50022; "Last Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50023; "Fuel Availed"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Current Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Old_PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50010; "Last Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Current Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "Cheque No."; Code[10])
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                BankChequeDetailsLRec: Record "Bank Cheque Details";
                BankAccLedEntryLRec: Record "Bank Account Ledger Entry";
                GenJournalLineLRec: Record "Gen. Journal Line";
                DuplicateChequeErr: Label 'Cheque No. %1 already entered', comment = '%1=Cheque No.';
                ValidChequeErr: Label 'Please enter a valid cheque No.';
                ChequeIssuedErr: Label 'cheque No. %1 is already issued', comment = '%1=Cheque No.';
            begin
                GenJournalLineLRec.Reset;
                GenJournalLineLRec.SetRange("Journal Template Name", "Journal Template Name");
                GenJournalLineLRec.SetRange("Journal Batch Name", "Journal Batch Name");
                GenJournalLineLRec.SetRange("Cheque No.", "Cheque No.");
                If GenJournalLineLRec.FindFirst() then
                    Error(DuplicateChequeErr, "Cheque No.");
                IF "Bal. Account Type" = "Bal. Account Type"::"Bank Account" then begin
                    BankChequeDetailsLRec.Reset();
                    BankChequeDetailsLRec.SetRange("Bank No.", "Bal. Account No.");
                    BankChequeDetailsLRec.SetRange("Cheque No.", "Cheque No.");
                    IF Not BankChequeDetailsLRec.FindFirst() then
                        Error(ValidChequeErr)
                    Else
                        BankChequeDetailsLRec.TestField("Approval Status", "Approval Status"::Released);
                    BankAccLedEntryLRec.Reset();
                    BankAccLedEntryLRec.SetRange("Bank Account No.", "Bal. Account No.");
                    BankAccLedEntryLRec.SetRange("Cheque No.", "Cheque No.");
                    IF BankAccLedEntryLRec.Findfirst() then
                        error(ChequeIssuedErr, "Cheque No.");
                end;
                IF "Account Type" = "Account Type"::"Bank Account" then begin
                    BankChequeDetailsLRec.Reset();
                    BankChequeDetailsLRec.SetRange("Bank No.", "Account No.");
                    BankChequeDetailsLRec.SetRange("Cheque No.", "Cheque No.");
                    IF Not BankChequeDetailsLRec.FindFirst() then
                        Error(ValidChequeErr)
                    Else
                        BankChequeDetailsLRec.TestField("Approval Status", "Approval Status"::Released);
                    BankAccLedEntryLRec.Reset();
                    BankAccLedEntryLRec.SetRange("Bank Account No.", "Account No.");
                    BankAccLedEntryLRec.SetRange("Cheque No.", "Cheque No.");
                    IF BankAccLedEntryLRec.Findfirst() then
                        error(ChequeIssuedErr);
                end;
            end;
        }
        field(50025; "Cheque Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50123; "WHT Amount (Base)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50124; "WHT Amount(LCY) (Base)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        //PMS
        modify("Maintenance Code")
        {
            trigger OnAfterValidate()
            var
                FA: Record "Fixed Asset";
            BEGIN
                IF MaintenanceRec.GET("Maintenance Code") THEN
                    IF MaintenanceRec."PMS Maintenance" OR MaintenanceRec."Diesel Maintenance" THEN BEGIN
                        KmReading := 0;
                        TESTFIELD("Date PMS Availed");
                        TESTFIELD("Fuel Availed");
                        MaintenanceLedgerEntry.SetCurrentKey("FA No.", "Date PMS Availed_");
                        MaintenanceLedgerEntry.SETRANGE("FA No.", "Account No.");
                        MaintenanceLedgerEntry.SETFILTER("Date PMS Availed_", '<>%1', 0D);
                        MaintenanceLedgerEntry.SETRANGE("Date PMS Availed_", 20000101D, "Date PMS Availed");
                        MaintenanceLedgerEntry.SETFILTER("Current Meter Reading", '<>%1', 0);
                        IF MaintenanceLedgerEntry.FINDLAST THEN BEGIN
                            KmReading := MaintenanceLedgerEntry."Current Meter Reading";
                            IF FA.GET("Account No.") THEN;
                            KmReadingRec.RESET;
                            KmReadingRec.SETRANGE(KmReadingRec."Card No.", FA."PMS Card No.");
                            KmReadingRec.SETFILTER(KmReadingRec."Posting Date", '%1..%2', 20160101D, CALCDATE('-1D', "Date PMS Availed"));
                            IF KmReadingRec.FINDLAST THEN BEGIN
                                IF KmReading > KmReadingRec."Current Mileage" THEN
                                    "Last Km Reading" := KmReading
                                ELSE
                                    "Last Km Reading" := KmReadingRec."Current Mileage";
                            END
                            ELSE
                                "Last Km Reading" := MaintenanceLedgerEntry."Current Meter Reading";
                        END;
                        GenJnlLine.SETRANGE("Document No.", "Document No.");
                        GenJnlLine.SETRANGE("Account No.", "Account No.");
                        GenJnlLine.SETFILTER("Current Km Reading", '<>%1', 0);
                        IF "Line No." <> 0 THEN
                            GenJnlLine.SETFILTER("Line No.", '<%1', "Line No.");
                        IF GenJnlLine.FINDLAST THEN BEGIN
                            IF GenJnlLine."Current Km Reading" > "Last Km Reading" THEN
                                "Last Km Reading" := GenJnlLine."Current Km Reading";
                        END;
                    end;
            end;
        }
        field(50026; Narration; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50027; Narration1; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Payment Mode"; enum PaymentMode)
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Voucher type"; Enum VoucherType)
        {
            DataClassification = CustomerContent;
        }
        field(50033; Cleared; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //PhaniFeb122021>> Here increased side from 50 to 100 for Description 2 field
        field(50034; "Description 2"; text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50035; "Sales Rebate Period"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50036; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Header"."No." WHERE(Status = CONST(Released), "Document Type" = CONST(Capex));
        }
        field(50037; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Line"."Line No." WHERE("Document No." = FIELD("Capex No."));
            trigger OnValidate()
            begin
                TestField("Capex No.");
                if "CWIP No." <> '' THEN BEGIN //PKONNO5
                    IF "Capex Line No." <> 0 THEN BEGIN
                        IF "Credit Amount" = 0 THEN
                            TESTFIELD("Debit Amount");
                        CheckCapexValue();
                        IF "Debit Amount" > 0 THEN BEGIN
                            // IF ("Debit Amount" + FAJnlAmount + CapexAmtUtil) > CapexAmt THEN
                            IF ("Amount (LCY)" + FAJnlAmount + CapexAmtUtil) > CapexAmt THEN //CapexBudget
                                ERROR(Text50200, ("Debit Amount" + FAJnlAmount + CapexAmtUtil), CapexAmt);
                        END;
                    end;//PKONNO5
                END//SAA3.0
                   //Capex Budget
                ELSE begin
                    IF "Capex Line No." <> 0 THEN BEGIN
                        IF "Credit Amount" = 0 THEN
                            TESTFIELD("Debit Amount");
                        CheckCapexValue();
                        IF "Debit Amount" > 0 THEN BEGIN
                            IF ("Amount (LCY)" + FAJnlAmount + CapexAmtUtil) > CapexAmt THEN //CapexBudget
                                ERROR(Text50200, ("Debit Amount" + FAJnlAmount + CapexAmtUtil), CapexAmt);
                        END;
                    end;
                end;
                //Capex Budget
            end;
        }
        field(50038; "Voucher No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50039; "Loan ID"; code[30])
        {
            DataClassification = CustomerContent;
            TableRelation = Loan_B2B.Id where(Closed = filter(false));
            trigger OnValidate()
            VAR
                LoanPostGrp: Record "Loan Posting Groups";
                LoanGRec: Record Loan_B2B;
            BEGIN
                IF ("Journal Template Name" <> 'SALES') THEN BEGIN
                    LoanGRec.RESET;
                    LoanGRec.SetRange(Id, "Loan ID");
                    IF LoanGRec.FindFirst() THEN BEGIN
                        IF LoanPostGrp.Get(LoanGRec."Loan Posting Group") THEN BEGIN
                            //"Account Type" := "Account Type"::"Bank Account";
                            //"Account No." := LoanGRec."Customer No.";
                            "Bal. Account Type" := "Bal. Account Type"::"G/L Account";
                            "Bal. Account No." := LoanPostGrp."Loan Refundable Acc.";
                        end;
                    END;
                end;

            END;
        }

        field(50040; "Provision Entry"; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'PROV1.0';
        }
        field(50041; "Fixed Asset Type"; Option)
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
            OptionCaption = '" ,Automobile,Funiture,Electrical,Rent,PLant-Machinery,Generators,Residential,Road,Building,Consumables,Rates-Taxes"';
            OptionMembers = " ",Automobile,Funiture,Electrical,Rent,"PLant-Machinery",Generators,Residential,Road,Building,Consumables,"Rates-Taxes";
        }
        field(50042; "Shortcut Dimension 3 Code"; Code[20])
        {
            CaptionClass = '1,2,3';
            Caption = 'Shortcut Dimension 3 Code';
            Description = 'CHI6.0';
            //Enabled = false;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(3));
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                ValidateShortcutDimCode(3, "Shortcut Dimension 3 Code");
            end;
        }
        field(50043; "Shortcut Dimension 4 Code"; Code[20])
        {
            CaptionClass = '1,2,4';
            Caption = 'Shortcut Dimension 4 Code';
            Description = 'CHI6.0';
            //Enabled = false; //PJ
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(4));
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                ValidateShortcutDimCode(4, "Shortcut Dimension 4 Code");
            end;
        }
        field(50044; "Shortcut Dimension 5 Code"; Code[20])
        {
            CaptionClass = '1,2,5';
            Caption = 'Shortcut Dimension 5 Code';
            Description = 'CHI6.0';
            //Enabled = false;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(5));
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                ValidateShortcutDimCode(5, "Shortcut Dimension 5 Code");
            end;
        }
        field(50045; "Shortcut Dimension 6 Code"; Code[20])
        {
            CaptionClass = '1,2,6';
            Caption = 'Shortcut Dimension 6 Code';
            Description = 'CHI6.0';
            //Enabled = false;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(6));
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                ValidateShortcutDimCode(6, "Shortcut Dimension 6 Code");
            end;
        }
        field(50046; "Shortcut Dimension 7 Code"; Code[20])
        {
            CaptionClass = '1,2,7';
            Caption = 'Shortcut Dimension 7 Code';
            Description = 'CHI6.0';
            //Enabled = false;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(7));
            DataClassification = CustomerContent;
            trigger OnValidate();
            var

                GLSetup: Record "General Ledger Setup";
                EmpMaster: Record employee;
            begin
                ValidateShortcutDimCode(7, "Shortcut Dimension 7 Code");
                //PhaniFeb152021>>
                GLSetup.GET;
                IF ("Shortcut Dimension 7 Code" <> '') AND EmpMaster.GET("Shortcut Dimension 7 Code") THEN BEGIN
                    VALIDATE("Shortcut Dimension 1 Code", EmpMaster."Global Dimension 1 Code");
                    VALIDATE("Shortcut Dimension 2 Code", EmpMaster."Global Dimension 2 Code");
                END;
                //PhaniFeb152021>>
            end;
        }
        field(50047; "Shortcut Dimension 8 Code"; Code[20])
        {
            CaptionClass = '1,2,8';
            Caption = 'Shortcut Dimension 8 Code';
            Description = 'CHI6.0';
            //Enabled = false;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(8));
            DataClassification = CustomerContent;
            trigger OnValidate();
            begin
                ValidateShortcutDimCode(8, "Shortcut Dimension 8 Code");
            end;
        }
        field(50048; "Process Document Type"; Option)
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
            OptionCaption = '" ,Medical Bill,LPO,LC Opened,Importation Cost Related Bill,IPO Facility,Facility,Clearing,Demurage,Duty,Non Recov. Vat,Recoverable Vat,CISS,Insurance,Lodgement,NAFDAC,Container"';
            OptionMembers = " ","Medical Bill",LPO,"LC Opened","Importation Cost Related Bill","IPO Facility",Facility,Clearing,Demurage,Duty,"Non Recov. Vat","Recoverable Vat",CISS,Insurance,Lodgement,NAFDAC,Container;
        }
        field(50049; "Ship to Code"; Code[10])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = "Ship-to Address".Code WHERE("Customer No." = FIELD("Account No."));
        }
        field(50050; "Description 3"; Text[80])
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
            Enabled = false;
        }
        field(50051; "Receipt Document No."; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50052; "JV Type"; Option)
        {
            DataClassification = CustomerContent;
            Caption = 'JV Type';
            Description = 'SAA3.0_added options after option 7_220517';
            OptionCaption = '" ,General,Sales,Purchase,IC Partner,Recurring,Lodgement,IC Purchase,Bk-Cr-Memo,Bk-Dr-Memo,BRJNL,BRJV,Bank"';
            OptionMembers = " ",General,Sales,Purchase,"IC Partner",Recurring,Lodgement,"IC Purchase","Bk-Cr-Memo","Bk-Dr-Memo",BRJNL,BRJV,Bank;
        }
        field(50053; "Created By"; Code[50])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50054; "Created By Name"; Text[50])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50055; "Created Date"; Date)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50056; "Created Time"; Time)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50057; "Modified By"; Code[50])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50058; "Modified By Name"; Text[25])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50059; "Modified Date"; Date)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50060; "Modified Time"; Time)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50061; "Posted By"; Code[20])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50062; "Posted By Name"; Text[50])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50063; "Posted Date"; Date)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50064; "Posted Time"; Time)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50065; "Authorised By"; Code[50])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50066; "Batched By"; Code[50])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50067; "Payable to"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,Staff,Vendor,Customer,Bank"';
            OptionMembers = " ",Staff,Vendor,Customer,Bank;
        }
        field(50068; "Payable Code"; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50069; "Payable Name"; Text[80])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50070; "Cash Payments Options"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,Advance Payment,Settlement,Cash Payment(Direct)"';
            OptionMembers = " ","Advance Payment",Settlement,"Cash Payment(Direct)";
        }
        field(50071; "Cash Paid"; Boolean)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50072; "Cash Paid By"; Code[50])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50073; "Cash Paid On"; DateTime)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50074; "Reprint CPV Slip"; Boolean)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50075; "Reprinted By"; Code[50])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50076; "Reprinted On"; DateTime)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50077; "Collected By Name"; Code[100])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50078; "Credited On"; DateTime)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50079; "Credited By"; Code[50])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50080; "Credit Bank"; Code[10])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50081; "Paid To / Received By"; Text[50])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50082; "Receiving Type"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,Vendor,Customer"';
            OptionMembers = " ",Vendor,Customer;
        }
        field(50083; "Receiving Code"; code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50084; "Teller / Cheque No."; Code[30])
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50085; "Teller / Cheque Date"; Date)
        {
            Description = 'UNL1.0';
            DataClassification = CustomerContent;
        }
        field(50086; "Bank Name"; Text[40])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50087; "Teller Bank Name"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,FBNMERCHANT,RANDMERCHANT,CORONATION,PROVIDUS"';
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,FBNMERCHANT,RANDMERCHANT,CORONATION,PROVIDUS;

        }
        field(50088; "Bank Doc. Type"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,Lodgement,Payment,Facility,BID/IFEM,Intra Bank,RTN Cheques,Stale Cheque,Cheque RVSAL,COT,Import Related,Int. Mgt Fees,Interest"';
            OptionMembers = " ",Lodgement,Payment,Facility,"BID/IFEM","Intra Bank","RTN Cheques","Stale Cheque","Cheque RVSAL",COT,"Import Related","Int. Mgt Fees",Interest;
        }
        field(50089; "Reason for RTN Cheque"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            OptionCaption = '" ,DAR,REP,DCR"';
            OptionMembers = " ",DAR,REP,DCR;
        }
        field(50090; PaymentSettlementOf; Text[30])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50091; "Return Cheque"; Boolean)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50092; "Import File No."; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = "Vendor 2"."No." WHERE("Vendor Type" = FILTER("Import File"));//Balu on 19April
        }
        field(50093; "Clearing File No."; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50094; "Charge Code"; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = "Item Charge";
        }
        field(50095; "Multiple Batch"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50096; "Maturity Date"; Date)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50097; "Employee Band"; Code[10])
        {
            Description = 'HO1.0';
            DataClassification = CustomerContent;
        }
        field(50098; "Employee Grade"; Code[10])
        {
            Description = 'HO1.0';
            DataClassification = CustomerContent;
        }
        field(50099; "Employee Step"; Code[10])
        {
            Description = 'HO1.0';
            DataClassification = CustomerContent;
        }
        field(50100; "FA No."; Code[10])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = "Fixed Asset"."No." WHERE("Global Dimension 1 Code" = FIELD("Shortcut Dimension 1 Code"));
        }
        field(50101; "Vendor Payment Type"; Option)
        {
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
            OptionCaption = '" ,Advance,Settlement"';
            OptionMembers = " ",Advance,Settlement;
        }
        field(50102; "LPO No."; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = IF ("Account Type" = CONST(Vendor)) "Purchase Header"."No." WHERE("Buy-from Vendor No." = FIELD("Account No."))
            ELSE
            IF ("Bal. Account Type" = CONST(Vendor)) "Purchase Header"."No." WHERE("Buy-from Vendor No." = FIELD("Bal. Account No."));
        }
        field(50103; "Staff Code"; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = Employee;

            trigger OnValidate();
            var
                EmpMaster: Record Employee;
            begin
                if ("Staff Code" <> '') and EmpMaster.GET("Staff Code") then begin
                    //"Employee Band" := EmpMaster."Employee Band";
                    //"Employee Grade" := EmpMaster."Employee Grade";
                    //"Employee Step" := EmpMaster."Step / Level Code";
                    VALIDATE("Shortcut Dimension 1 Code", EmpMaster."Global Dimension 1 Code");
                    VALIDATE("Shortcut Dimension 2 Code", EmpMaster."Global Dimension 2 Code");

                    /*if DefDim.GET(50325, EmpMaster."No.", GLSetup."Shortcut Dimension 9 Code") then
                        VALIDATE("Shortcut Dimension 9 Code", DefDim."Dimension Value Code");
                    //ValidateShortcutDimCode(9,DefDim."Dimension Value Code");

                    if DefDim.GET(50325, EmpMaster."No.", GLSetup."Shortcut Dimension 10 Code") then
                        VALIDATE("Shortcut Dimension 10 Code", DefDim."Dimension Value Code");*///CHI2.0
                    //ValidateShortcutDimCode(10,DefDim."Dimension Value Code");
                end;
            end;
        }
        field(50104; "FA Posting Group"; Code[20])
        {
            Caption = 'FA Posting Group';
            DataClassification = CustomerContent;
            Description = 'SAA3.0';
            TableRelation = "FA Posting Group";
        }
        field(50105; "Branch CPV"; Boolean)
        {
            Description = 'GJ_CHI_RKD_181213';
            DataClassification = CustomerContent;
        }
        field(50106; "Computer Check No."; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50107; "PDS No."; Code[20])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            TableRelation = IF ("Account Type" = CONST(Vendor)) "Purch. Inv. Header"."No." WHERE("Buy-from Vendor No." = FIELD("Account No."))
            ELSE
            IF ("Bal. Account Type" = CONST(Vendor)) "Purch. Inv. Header"."No." WHERE("Buy-from Vendor No." = FIELD("Bal. Account No."));

            trigger OnValidate();
            var
                PurchInvHeader: Record "Purch. Inv. Header";
            begin
                if PurchInvHeader.GET("PDS No.") then
                    "LPO No." := PurchInvHeader."Order No.";
            end;
        }
        field(50108; "Branch GRN No."; Code[10])
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50109; "Qty per branch"; Decimal)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
        }
        field(50110; "Reversal For Prov Entry No."; Integer)
        {
            DataClassification = CustomerContent;
            Description = 'PROV1.0';
        }
        field(50111; "Online Bank Entry"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50112; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "CWIP Masters"."CWIP No." where(Status = const(Release));
            trigger onvalidate()
            var
                CWIPMaster: record "CWIP Masters";
            begin
                "Account Type" := "Account Type"::"G/L Account";
                CWIPMaster.Get("CWIP No.");
                "Account No." := CWIPMaster."GL Account No.";
                //PhaniFeb192021>>
                VALIDATE("Shortcut Dimension 1 Code", CWIPMaster."Global Dimension 1 Code");
                VALIDATE("Shortcut Dimension 2 Code", CWIPMaster."Global Dimension 2 Code");
                //PhaniFeb192021<<

            end;
        }
        field(50116; "Line Account No."; Code[20])
        {
            DataClassification = CustomerContent;
            Description = 'CTC';
        }
        field(50117; "Allocation Account Type"; Option)
        {
            DataClassification = CustomerContent;
            Description = 'CTC';
            OptionCaption = 'Fixed Asset,G/L Account';
            OptionMembers = "Fixed Asset","G/L Account";
        }
        field(50118; "Allocation Account No."; Code[20])
        {
            DataClassification = CustomerContent;
            Description = 'CTC';
            TableRelation = IF ("Allocation Account Type" = FILTER("G/L Account")) "G/L Account"."No."
            ELSE
            IF ("Allocation Account Type" = FILTER("Fixed Asset")) "Fixed Asset"."No.";
        }
        field(50119; "Line Account Type"; Option)
        {
            DataClassification = CustomerContent;
            Description = 'CTC';
            OptionCaption = 'Fixed Asset,G/L Account';
            OptionMembers = "Fixed Asset","G/L Account";
        }
        field(50120; "Allocation Maintenance Code"; Code[20])
        {
            DataClassification = CustomerContent;
            Caption = 'Maintenance Code';
            Description = 'CTC';
            TableRelation = Maintenance;
        }
        field(50121; "Fixed Asset No."; Code[20])
        {
            DataClassification = CustomerContent;
            Description = 'CTC';
        }
        //Balu On April 24>>
        field(50122; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
        }
        //Balu On April 24<<
        //Fix28May2021>>
        field(50125; "WHT Account Holder Type"; Enum "Gen. Journal Account Type")
        {
            DataClassification = CustomerContent;
        }
        field(50126; "WHT Account Holder"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //Fix28May2021<<
        //Fix05Jul2021>>
        field(50127; "Paid By"; Code[200])
        {
            DataClassification = CustomerContent;
        }
        //Fix05Jul2021<<
        //BaluonSep8>>
        field(50128; "Printable Comment 1"; Text[50])
        {
            DataClassification = CustomerContent;
            Description = 'Additional information to print on the Sales Order';
        }
        field(50129; "Printable Comment 2"; Text[50])
        {
            DataClassification = CustomerContent;
        }
        //BaluonSep8>>

    }

    trigger OnInsert()
    begin
        //UserSetupGRec.GET(USERID);
        //UserSetupGRec.TestField("Gen. Jouranl Line Resp. Centre");
        //"Responsibility Center" := UserSetupGRec."Gen. Jouranl Line Resp. Centre";
        //message('%1..%2', "Dimension Set ID", "PMS Card No.");
        /*IF "PMS Card No." <> '' then BEGIN
            message('%1', "Dimension Set ID");
            DimMgt.GetShortcutDimensions("Dimension Set ID", ShortcutDimCode);
        END;*/
    end;

    trigger OnModify()

    BEGIN
        IF "PMS Card No." <> '' THEN BEGIN
            VALIDATE("Dimension Set ID", xRec."Dimension Set ID");
        end;
    END;

    procedure CheckCapexValue()
    begin

        CLEAR(FAJnlAmount);
        CLEAR(CapexAmtUtil);
        CLEAR(CapexAmt);

        FAJnlLineGRec.RESET;
        FAJnlLineGRec.SETFILTER("Journal Template Name", '<>%1', "Journal Template Name");
        FAJnlLineGRec.SETFILTER("Journal Batch Name", '<>%1', "Journal Batch Name");
        //FAJnlLineGRec.SETFILTER(Amount, '>=%1', 0);//Fix29Jun2021
        FAJnlLineGRec.SETRANGE("Capex No.", "Capex No.");
        FAJnlLineGRec.SETRANGE("Capex Line No.", "Capex Line No.");
        IF FAJnlLineGRec.FINDSET THEN
            REPEAT
                if FAJnlLineGRec."Debit Amount" <> 0 then    //Capex Budget
                    FAJnlAmount += FAJnlLineGRec."Amount (LCY)";
            UNTIL FAJnlLineGRec.NEXT = 0;

        FAJnlLineGRec.RESET;
        FAJnlLineGRec.SETFILTER("Journal Template Name", '=%1', "Journal Template Name");
        FAJnlLineGRec.SETFILTER("Journal Batch Name", '=%1', "Journal Batch Name");
        //FAJnlLineGRec.SETFILTER("Document No.", '=%1', "Document No.");//NYO ...08/03/2016//Fix29Jun2021
        FAJnlLineGRec.SETFILTER("Line No.", '<>%1', "Line No.");
        //FAJnlLineGRec.SETFILTER(Amount, '>=%1', 0);//Fix29Jun2021
        FAJnlLineGRec.SETRANGE("Capex No.", "Capex No.");
        FAJnlLineGRec.SETRANGE("Capex Line No.", "Capex Line No.");
        IF FAJnlLineGRec.FINDSET THEN
            REPEAT
                if FAJnlLineGRec."Debit Amount" <> 0 then //Capex Budget
                    FAJnlAmount += FAJnlLineGRec."Amount (LCY)";
            UNTIL FAJnlLineGRec.NEXT = 0;


        CapexLine.RESET;
        //CapexLine.SETRANGE("Document Type",CapexLine."Document Type" :: Capex);
        CapexLine.SETRANGE("Document No.", "Capex No.");
        CapexLine.SETRANGE("Line No.", "Capex Line No.");
        IF CapexLine.FINDFIRST THEN BEGIN
            CapexLine.CALCFIELDS(CapexLine."Budget Utilized");
            // CapexLine.CALCFIELDS("Amt. Utilized(LCY)");
            //  CapexAmtUtil := CapexLine."Amt. Utilized(LCY)";
            CapexAmtUtil := CapexLine."Budget Utilized";
            CapexAmt := CapexLine."Amount(LCY)";
        END;

    end;

    procedure CalculateWHT()
    var
        whtgrp: record WHTSetUp;
        WHTAmntB: Decimal;
    begin
        //TestField("Document Type", "Document Type"::Payment);//PK on 01.21.2021 VjedLin
        TestField("Account Type", "Account Type"::Vendor);
        if not ("Bal. Account Type" = "Bal. Account Type"::"Bank Account") AND not ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") then
            Error('Balanace Acoount Type should be Either GLAccount or BankAccount ');
        //TestField("Bal. Account No.");//PK on 01.21.2021 VjedLin
        TestField(Amount);
        TestField("WHT Group");
        TestField("Account No.");

        IF whtgrp.get("WHT Group") THEN begin
            whtgrp.TestField(Percentage);
            "WHT %" := whtgrp.Percentage;
            IF "Account Type" = "Account Type"::Customer then BEGIN
                whtgrp.TestField("Receivable Account No.");
                "WHT Account" := whtgrp."Receivable Account No."
            END else
                IF "Account Type" = "Account Type"::Vendor then BEGIN
                    whtgrp.TestField("Payable Account No");
                    "WHT Account" := whtgrp."Payable Account No";
                END;
            /*"WHT Amount" := Round((Amount * ("WHT %" / 100)), 1);
            if "Currency Factor" <> 0 then
                "WHT Amount(LCY)" := Round(("Currency Factor" * (Amount * ("WHT %" / 100))), 1)
            else
                "WHT Amount(LCY)" := Round((Amount * ("WHT %" / 100)), 1);*///Prasanna on 12.27.2020

            WHTAmntB := GetWHTAmount();

            IF WHTAmntB <> 0 then begin
                "WHT Amount" := Round((WHTAmntB * ("WHT %" / 100)), 0.01);
                //Message('%1...%2....%3', (WHTAmntB * ("WHT %" / 100)), "WHT Amount", WHTAmntB);
                if "Currency Factor" <> 0 then
                    "WHT Amount(LCY)" := Round(((WHTAmntB / "Currency Factor") * ("WHT %" / 100)), 0.01)
                else
                    "WHT Amount(LCY)" := Round((WHTAmntB * ("WHT %" / 100)), 0.01);
            end;
        end else Begin
            CLEAR("WHT %");
            clear("WHT Account");
            CLEAR("WHT Amount");
            clear("WHT Amount(LCY)");
        end;
    end;

    procedure GetWHTAmount() Amnt: Decimal
    var
        CustLedEntr: Record "Cust. Ledger Entry";
        VendLedEntr: Record "Vendor Ledger Entry";
        PostPurchIn: Record "Purch. Inv. Header";
        PostSaleIn: Record "Sales Invoice Header";
        PostPurchInLine: Record "Purch. Inv. Line";
    Begin
        Clear(Amnt);
        if "Account Type" = "Account Type"::Vendor then begin
            VendLedEntr.Reset();
            VendLedEntr.setrange("Applies-to ID", "Document No.");
            VendLedEntr.SetRange("Document Type", VendLedEntr."Document Type"::Invoice);
            IF VendLedEntr.findset then
                repeat
                    PostPurchInLine.RESET;
                    PostPurchInLine.setrange("Document No.", VendLedEntr."Document No.");
                    PostPurchInLine.SetRange("WHT Applicable", True);
                    IF PostPurchInLine.findSET then begin
                        PostPurchInLine.CalcSums(Amount);
                        Amnt += ROUND(PostPurchInLine.Amount, 0.01);
                    end;
                until VendLedEntr.Next = 0
        end else
            if "Account Type" = "Account Type"::Customer then begin
                CustLedEntr.Reset();
                CustLedEntr.setrange("Applies-to ID", "Document No.");
                CustLedEntr.SetRange("Document Type", CustLedEntr."Document Type"::Invoice);
                IF CustLedEntr.findset then
                    repeat
                        PostSaleIn.RESET;
                        PostSaleIn.setrange("No.", CustLedEntr."Document No.");
                        IF PostSaleIn.findSET then begin
                            PostSaleIn.CalcFields(Amount);
                            Amnt += Round(PostSaleIn.Amount, 0.01);
                        end;
                    until CustLedEntr.Next = 0
            End;
        exit(Amnt);
    end;

    var
        CapexAmtUtil: Decimal;
        CapexAmt: Decimal;
        CapexLine: Record "Budget Line";
        FAJnlAmount: Decimal;
        FAJnlLineGRec: Record "Gen. Journal Line";
        GenJnlLine: Record "Gen. Journal Line";
        ItemJlLine: Record "Item Journal Line";
        GenRec: Record "Gen. Journal Line";
        PurchLine: Record "Purchase Line";
        MaintenanceRec: Record Maintenance;
        MaintenanceLedgerEntry: Record "Maintenance Ledger Entry";
        VendorGRec: Record Vendor;
        UserSetupGRec: Record "User Setup";
        WhtGRec: Record WHTSetUp;
        KmReadingRec: Record "Original PMS Statement";
        KmReading: Decimal;
        Text50200: Label 'Total Acquisiton amount %1 must not exceed capex line amount %2.';
        DimMgt: Codeunit DimensionManagement;
        ShortcutDimCode: array[8] of Code[20];
        CapexAmountUtil: Decimal;
        CapexAmount: decimal;

}