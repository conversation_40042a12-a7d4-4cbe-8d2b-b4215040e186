tableextension 50076 ProdOrdLineExt extends "Prod. Order Line"
{
    fields
    {
        // Add changes to table fields here
        modify("Location Code")
        {
            trigger OnAfterValidate()
            var
                Location: Record Location;
            begin
                if Location.Get("Location Code") then
                    Location.TestField(Blocked, false);//Balu 05122021
            end;
        }
        field(50010; "Subcontracting Order No."; code[20])
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
            TableRelation = "Purchase Header"."No." WHERE("Document Type" = CONST(Order), "No." = FIELD("Subcontracting Order No."), Subcontracting = CONST(true));
        }
        field(50011; "Subcontractor Code"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor."No." WHERE(Subcontractor = CONST(true));
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50012; "Production Batch No."; Code[20])
        {
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50007; "Warehouse Location"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Location;
            trigger OnValidate()
            var
                Location: Record Location;
            begin
                if Location.Get("Warehouse Location") then
                    Location.TestField(Blocked, false);//Balu 05122021
            end;
        }
        field(50008; "Warehouse Bin Code"; Code[20])
        {
            Caption = 'Warehouse Bin Code';
            DataClassification = CustomerContent;
            TableRelation = "Bin Content"."Bin Code" WHERE("Location Code" = FIELD("Warehouse Location"), "Item No." = FIELD("Item No."), "Variant Code" = FIELD("Variant Code"));
        }
        field(50015; "Qty. To Transfer"; Decimal)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                IF "Qty. To Transfer" + "Qty Transfered" > "Finished Quantity" then
                    Error('Transfer Quantity should be less than finished quantity.')
            end;
        }
        field(50016; "Qty Transfered"; Decimal)
        {

            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50017; "Transfer Ticket No."; Integer)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count ("Transfer Line" where("Production Order No." = field("Prod. Order No."), "Production Order Line No." = field("Line No.")));
        }
        field(50018; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            //PkOnEXT
        }

        //<<<<<< G2S 090923


        field(50019; "Posted Consumption Qty."; Decimal)
        {
            // DataClassification = ToBeClassified;
            Editable = false;

        }

        field(50026; "Item Consumption Qty."; Decimal)
        {
            // DataClassification = ToBeClassified;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Document No." = field("Prod. Order No."), "Item No." = field("Prod Order Comp. Item"), "Entry Type" = filter(Consumption)));
            trigger OnValidate()
            var
                ProdOrderComp: Record "Prod. Order Component";
            begin
                //Message('Before ');
                ProdOrderComp.Reset();
                ProdOrderComp.SetRange("Prod. Order No.", "Prod. Order No.");
                ProdOrderComp.SetRange("Item No.", "Prod Order Comp. Item");
                ProdOrderComp.CalcSums("Quantity per");
                //if ProdOrderComp.FindFirst() then begin
                "Posted Consumption Qty." := ABS(ROUND(("Item Consumption Qty." / ProdOrderComp."Quantity per"), 1, '>'));
                Modify();
                // end;

                //Message('Hi');
            end;


        }
        field(50020; "Fraction Quantity"; Decimal)
        {
            DataClassification = ToBeClassified;
        }

        field(50021; "Date Modified"; DateTime)
        {
            DataClassification = ToBeClassified;
        }

        field(50022; "Date Created"; DateTime)
        {
            DataClassification = ToBeClassified;
        }
        field(50023; "Created By"; Code[50])
        {
            DataClassification = ToBeClassified;
        }
        field(50024; "Modified By"; Code[50])
        {
            DataClassification = ToBeClassified;
        }
        field(50025; "Prod Order Comp. Item"; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        //>>>>>> G2S 090923

    }
    //G2S >>>>> 03 Nov 2023
    /// <summary>
    /// GetRelProdOrderBarcodeStatus.
    /// </summary>
    /// <param name="DocNo">Code[20].</param>
    /// <returns>Return value of type Boolean.</returns>
    procedure GetRelProdOrderBarcodeStatus(DocNo: Code[20]): Boolean
    var
        ProdOrder: Record "Production Order";
    begin
        ProdOrder.Reset();
        ProdOrder.SetRange("No.", DocNo);
        if ProdOrder.FindFirst() then begin
            exit(ProdOrder."Barcode Printed?");
        end else
            exit(false);
    end;

    var
        myInt: Integer;
        DeleteAllowed: Boolean;
        Txt001: Label 'You cannot delete this production order.';

    trigger OnModify()
    var
        myInt: Integer;
    begin
        "Modified By" := UserId;
        "Date Modified" := CurrentDateTime;
    end;

    trigger OnInsert()
    var
        myInt: Integer;
    begin
        "Created By" := UserId;
        "Date Created" := CurrentDateTime;
    end;

    trigger OnDelete()
    var
        ProductionOrder: Record "Production Order";
        UserSetup: Record "User Setup";
    begin
        ProductionOrder.Reset();
        ProductionOrder.SetRange("No.", Rec."Prod. Order No.");
        ProductionOrder.SetRange("Barcode Printed?", true);
        IF ProductionOrder.FindFirst() then begin
            UserSetup.Reset();
            UserSetup.SetRange("User ID", USERID);
            if UserSetup.FindFirst() then begin
                if not UserSetup."Can Modify Prod. Order" then
                    Error(Txt001);
            end;
        end;
    end;
    //G2S >>>>> 03 Nov 2023


}