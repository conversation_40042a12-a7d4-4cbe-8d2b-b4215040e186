page 50501 "Clearing Lines"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO       15-Feb-12    -> Form created for IPO Clearing.

    Editable = false;
    PageType = List;
    SourceTable = "Clearing Line";
    UsageCategory = Lists;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Document No."; "Document No.")
                {
                }
                field("Line No."; "Line No.")
                {
                }
                field("Document Type"; "Document Type")
                {
                }
                field("Order No."; "Order No.")
                {
                }
                field("Order Line No."; "Order Line No.")
                {
                }
                field(Type; Type)
                {
                }
                field("No."; "No.")
                {
                }
                field(Description; Description)
                {
                }
                field("Description 2"; "Description 2")
                {
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                }
                field(Quantity; Quantity)
                {
                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                }
                field("Amount (FCY)"; "Amount (FCY)")
                {
                }
                field("Item Category Code"; "Item Category Code")
                {
                }
                field("Product Group Code"; "Product Group Code")
                {
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Show Document")
                {
                    Caption = 'Show Document';

                    trigger OnAction();
                    begin
                        ClearingRec.GET("Document No.");
                        PAGE.RUN(PAGE::Clearing, ClearingRec);
                    end;
                }
            }
        }
    }

    var
        ClearingRec: Record "Clearing Header";
}

