/// <summary>
/// Report Barcode Report Custom-LP (ID 50049).
/// </summary>
report 50049 "Barcode Report Custom-LP"
{
    ApplicationArea = All;
    Caption = 'Barcode  label reprint';
    UsageCategory = ReportsAndAnalysis;
    EnableExternalAssemblies = true;
    DefaultLayout = RDLC;
    // RDLCLayout = './Production Order Barcode-LP filtering.rdl';
    RDLCLayout = './layout/Production Order Barcode-LP filtering.rdl';
    dataset
    {
        dataitem("IWX LP Header"; "IWX LP Header")
        {
            DataItemTableView = sorting("No.");
            RequestFilterFields = "No.";
            column("LPNo"; "No.")
            {

            }
            column(Production_Order_No_; "Production Order No.")
            {
            }

            column(Description; Description)
            {

            }
            column(Source_No_; "Source No.")
            {

            }
            column(BUOM; BUOM)
            {

            }
            column(Due_Date; "Due Date")
            {

            }
            column(Production_Batch_No_; "Production Batch No.")
            {

            }
            column(Logo; CompInfo.Picture)
            {
            }
            dataitem("Tracking Specification"; "Tracking Specification")
            {
                DataItemLink = "Source ID" = FIELD("Production Order No.");
                DataItemTableView = sorting("Entry No.");
                column(Item_No_; "Item No.") { }
                column(Lot_No_; "Lot No.") { }
                column(Expiration_Date; "Expiration Date") { }
            }
            dataitem("Production Order"; "Production Order")
            {
                DataItemLink = "No." = FIELD("Production Order No.");
                DataItemTableView = sorting("No.");
                // RequestFilterFields = "No.";
                // column(Source_No_; "Source No.")
                // {

                // }
                // column(Description; Description)
                // {

                // }
                // column(BUOM; BUOM)
                // {

                // }
                // column(Due_Date; "Due Date")
                // {

                // }
                // column(Production_Batch_No_; "Production Batch No.")
                // {

                // }
                // column(Logo; CompInfo.Picture)
                // {
                // }
            }

            trigger OnAfterGetRecord()
            var
                Item: Record Item;
                ProdOrderLine: Record "Prod. Order Line";
                ProdOrder: Record "Production Order";
            begin
                BUOM := '';
                "Production Batch No." := '';
                "Due Date" := 0D;
                "Source No." := '';
                Description := '';
                ProdOrder.Reset();
                ProdOrder.SetRange("No.", "Production Order No.");
                if ProdOrder.FindFirst() then begin
                    "Production Batch No." := ProdOrder."Production Batch No.";
                    "Source No." := ProdOrder."Source No.";
                    Description := ProdOrder.Description;
                    "Due Date" := ProdOrder."Due Date";
                    //7th Nove 2023
                    ProdOrderLine.Reset();
                    ProdOrderLine.SetRange("Prod. Order No.", ProdOrder."No.");
                    if ProdOrderLine.FindFirst() then begin

                        Item.Reset();
                        Item.SetRange("No.", ProdOrder."Source No.");
                        Item.SetRange("License Plate Enabled?", true);
                        if Item.FindFirst() then begin
                            //BUOM := Item."Base Unit of Measure";
                            BUOM := 'PALLETS';
                        end else begin
                            Error(Txt001, "No.");
                        end;
                    end else begin
                        Error(Txt001, "No.");
                    end;
                end;
            end;
        }
    }
    requestpage
    {
        layout
        {
            area(content)
            {
                group(GroupName)
                {
                }
            }
        }
        actions
        {
            area(processing)
            {
            }
        }
    }

    trigger OnPreReport()
    var
    begin
        CompInfo.GET;
        CompInfo.CALCFIELDS(CompInfo.Picture);
    end;

    var
        CompInfo: Record "Company Information";
        BUOM: Code[10];
        Txt001: Label 'Barcode label for order %1 cannot be generated.';
        Txt002: Label 'The Lic. Plate record %1 does not exist for production order %2';
        "Production Batch No.": Code[20];
        "Due Date": Date;

}
