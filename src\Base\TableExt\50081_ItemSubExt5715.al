tableextension 50081 ItemSubExt extends "Item Substitution"
{
    fields
    {
        field(50001; "Sub. Item Blocked"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Period End"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Period Start"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "Last Modified Date Time"; DateTime)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50005; "Last Modified By"; Text[50])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
    }
    trigger Onmodify()
    begin
        "Last Modified By" := UserId();
        "Last Modified Date Time" := CurrentDateTime();
    end;
}