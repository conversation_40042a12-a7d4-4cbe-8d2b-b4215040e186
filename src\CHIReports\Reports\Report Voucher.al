report 50078 Voucher
{

    DefaultLayout = RDLC;
    RDLCLayout = 'Reports\Layout\Voucher.rdl';
    ApplicationArea = all;
    UsageCategory = ReportsAndAnalysis;
    Caption = 'Voucher_50078';

    dataset
    {

        dataitem("Voucher Header"; "Voucher Header")
        {
            column(Document_No_; "Document No.")
            {

            }
            column(PostedNarration1_Narration; Narration)
            {
            }
            column(Voucher_Type; SourceDesc)
            {

            }
            column(CompanyInformation_Picture; CompanyInformation.Picture)
            {

            }
            column(CompanyInformation_Address_____CompanyInformation__Address_2___________CompanyInformation_City; CompanyInformation.Address + ' ' + CompanyInformation."Address 2" + '  ' + CompanyInformation.City)
            {
            }
            column(CompanyInformation_Name; CompanyInformation.Name)
            {
            }
            column(Account_Name; "Account Name")
            {

            }
            column(Amount__LCY_; "Amount (LCY)")
            {

            }
            column(Narration; Narration)
            {

            }
            // <<<<<< G2S 07/06/2024 CAS-01305-L2G1R9
            column(Date______FORMAT__Posting_Date; 'Date: ' + FORMAT("Posting Date"))
            {
            }
            // <<<<<< G2S 07/06/2024 CAS-01305-L2G1R9
            dataitem("Gen. Journal Line"; "Gen. Journal Line 2")
            {

                /*DataItemTableView = SORTING("Document No.", "Posting Date", "Amount (LCY)") Order(Descending) WHERE("Project Code" = FILTER(1), "Voucher Type" = FILTER(JV), "JV Type" = FILTER(Purchase));*/
                DataItemLinkReference = "Voucher Header";
                DataItemLink = "Document No." = field("Document No.");

                //WHERE("Voucher Type" = FILTER(JV));
                //"JV Type" = FILTER(Purchase),"Project Code" = FILTER(1),
                RequestFilterFields = "Document No.", "Posting Date";

                column(DocType; "Document Type")
                {

                }
                column(NarrationLn; Narration)
                {

                }
                column(SourceDesc_____Voucher_; SourceDesc)
                {
                }
                column(Date______FORMAT__Posting_Date__; 'Date: ' + FORMAT("Document Date"))
                {
                }
                column(G_L_Entry__Document_No__; "Document No.")
                {
                }
                column(DebitAmountTotal; DebitAmountTotal)
                {
                }
                column(CreditAmountTotal; CreditAmountTotal)
                {
                }
                column(G_L_Entry__Debit_Amount_; "Debit Amount")
                {
                }
                column(GLAccName; GLAccName)
                {
                }
                column(CrText; CrText)
                {
                }
                column(G_L_Entry__Credit_Amount_; ABS("Credit Amount"))
                {
                }
                column(Gen__Journal_Line__Gen__Journal_Line__Description; "Gen. Journal Line".Description)
                {
                }
                column(DebitAmountTotal_Control1000000028; DebitAmountTotal)
                {
                }
                column(CreditAmountTotal_Control1000000029; CreditAmountTotal)
                {
                }
                column(DrText; DrText)
                {
                }
                column(Gen__Journal_Line__Account_No__; "Account No.")
                {
                }
                column(ABS__Credit_Amount__; ABS("Credit Amount"))
                {
                }
                column(ABS__Debit_Amount__; ABS("Debit Amount"))
                {
                }
                column(Rs____NumberText_1_______NumberText_2_; NoText[1] + NoText[2])
                {
                }
                column(Voucher_No___Caption; Voucher_No___CaptionLbl)
                {
                }
                column(Credit_AmountCaption; Credit_AmountCaptionLbl)
                {
                }
                column(Debit_AmountCaption; Debit_AmountCaptionLbl)
                {
                }
                column(ParticularsCaption; ParticularsCaptionLbl)
                {
                }
                column(Narration__Caption; Narration__CaptionLbl)
                {
                }
                column(Approved_by_Caption; Approved_by_CaptionLbl)
                {
                }
                column(Checked_by_Caption; Checked_by_CaptionLbl)
                {
                }
                column(Prepared_by_Caption; Prepared_by_CaptionLbl)
                {
                }
                column(Amount__in_words__Caption; Amount__in_words__CaptionLbl)
                {
                }
                column(Continued______Caption; Continued______CaptionLbl)
                {
                }
                column(Gen__Journal_Line_Journal_Template_Name; "Journal Template Name")
                {
                }
                column(Gen__Journal_Line_Journal_Batch_Name; "Journal Batch Name")
                {
                }
                column(Gen__Journal_Line_Line_No_; "Line No.")
                {
                }
                column(Gen__Journal_Line_Amount__LCY_; Abs("Amount (LCY)"))
                {
                }
                column(TotalDebitAmt; ABS(TotalDebitAmt)) { }
                column(credittotal; ABS(CreditAmountTotal)) { }

                dataitem(Integer; Integer)
                {
                    DataItemTableView = SORTING(Number);

                    column(IntegerOccurcesCaption; IntegerOccurcesCaptionLbl)
                    {
                    }
                    column(Integer_Number; Number)
                    {
                    }

                    trigger OnAfterGetRecord();
                    begin

                    end;

                    trigger OnPreDataItem();
                    begin
                        GenJrnlLine.SETCURRENTKEY("Document No.", "Posting Date", "Amount (LCY)");
                        GenJrnlLine.ASCENDING(FALSE);
                        GenJrnlLine.SETRANGE("Posting Date", "Gen. Journal Line"."Posting Date");
                        GenJrnlLine.SETRANGE("Document No.", "Gen. Journal Line"."Document No.");
                        GenJrnlLine.FINDLAST;
                        IF NOT (GenJrnlLine."Line No." = "Gen. Journal Line"."Line No.") THEN
                            CurrReport.BREAK;

                        SETRANGE(Number, 1, PageLoop);

                    end;

                }
                trigger OnAfterGetRecord()
                var
                    GLAccount: Record "G/L Account";
                    Customer: Record Customer;
                    Vendor: Record Vendor;
                    Check: Report Check;
                begin
                    SlNo += 1;
                    DrText := 'Dr';
                    CrText := 'To';
                    //SourceDesc := '';
                    NUMLines := 13;
                    PageLoop := NUMLines;
                    LinesPrinted := 0;

                    PageLoop := PageLoop - 1;
                    LinesPrinted := LinesPrinted + 1;



                    CLEAR(GLAccName);
                    if "Gen. Journal Line"."Account Type" = "Gen. Journal Line"."Account Type"::"G/L Account" then
                        if GLAccount.Get("Gen. Journal Line"."Account No.") then
                            GLAccName := GLAccount.Name;
                    if "Gen. Journal Line"."Account Type" = "Gen. Journal Line"."Account Type"::Customer then
                        if Customer.Get("Gen. Journal Line"."Account No.") then
                            GLAccName := Customer.Name;
                    if "Gen. Journal Line"."Account Type" = "Gen. Journal Line"."Account Type"::Vendor then
                        if Vendor.Get("Gen. Journal Line"."Account No.") then
                            GLAccName := Vendor.Name;
                    if "Gen. Journal Line"."Account Type" = "Gen. Journal Line"."Account Type"::"Bank Account" then
                        if BankAcc.Get("Gen. Journal Line"."Account No.") then
                            GLAccName := BankAcc.Name;
                    if "Debit Amount" <> 0 then
                        TotalDebitAmt += "Amount (LCY)";
                    if "Credit Amount" <> 0 then
                        CreditAmountTotal += "Amount (LCY)";
                    CLEAR(Check);

                    //message('%1', NoText[1]);
                    //Check.InitTextVariable;
                    //Check.FormatNoText(NoText, ROUND(TotalDebitAmt, 0.01, '='), "Currency Code");
                end;

                trigger OnPreDataItem()
                begin

                end;
            }
            trigger OnAfterGetRecord()
            BEGIN
                clear(SourceDesc);
                IF "Source Code" <> '' THEN BEGIN
                    SourceCode.GET("Source Code");
                    SourceDesc := SourceCode.Description;
                END;
                clear(TotAmt);
                VouHdr.RESEt;
                VouHdr.SetRange("Document No.", "Document No.");
                IF VouHdr.findfirst then BEGIN
                    GenJnLin2.RESET;
                    GenJnLin2.SetRange("Document No.", VouHdr."Document No.");
                    GenJnLin2.SetFilter("Debit Amount", '<>%1', 0);
                    IF GenJnLin2.findset then
                        repeat
                            TotAmt += GenJnLin2."Amount (LCY)";
                        until GenJnLin2.next = 0;
                END;
                //message('%1', TotAmt);
                NoText[1] := CheckRep.figure(ROUND(TotAmt, 0.01, '='), 'NAIRA', 'KOBO');
                NoText[1] := NoText[1] + ' ONLY';
            END;

            trigger OnPostDataItem()
            BEGIN
                //message('%1', NoText[1]);
            END;

        }

    }

    requestpage
    {

        layout
        {
        }

        actions
        {
        }
    }

    labels
    {
    }

    var
        CompanyInformation: Record 79;
        SourceCode: Record 230;
        RemBlankRows: Decimal;
        SlNo: Integer;
        GenJrnlLine: Record "Gen. Journal Line 2";
        TotAmt: Decimal;
        GLAccName: Text[100];
        VouHdr: Record "Voucher Header";
        GenJnLin2: Record "Gen. Journal Line 2";
        SourceDesc: Text[50];
        CrText: Text[2];
        DrText: Text[2];
        PageLoop: Integer;
        LinesPrinted: Integer;
        NUMLines: Integer;
        ChequeNo: Code[50];
        ChequeDate: Date;
        OnesText: array[20] of Text[30];
        TensText: array[10] of Text[30];
        ExponentText: array[6] of Text[30];
        PrintLineNarration: Boolean;
        PostingDate: Date;
        TotalDebitAmt: Decimal;
        DocumentNo: Code[20];
        DebitAmountTotal: Decimal;
        CreditAmountTotal: Decimal;
        BankAcc: Record "Bank Account";
        PrePostingDate: Date;
        PreDocumentNo: Code[50];
        ToWords: Codeunit 50001;
        NoText: array[20] of Text;
        Voucher_No___CaptionLbl: Label 'Voucher No. :';
        Credit_AmountCaptionLbl: Label 'Credit Amount';
        Debit_AmountCaptionLbl: Label 'Debit Amount';
        ParticularsCaptionLbl: Label 'Particulars';
        Narration__CaptionLbl: Label 'Narration :';
        Approved_by_CaptionLbl: Label 'Approved by:';
        Checked_by_CaptionLbl: Label 'Checked by:';
        Prepared_by_CaptionLbl: Label 'Prepared by:';
        Amount__in_words__CaptionLbl: Label 'Amount (in words):';
        Continued______CaptionLbl: Label 'Continued......';
        IntegerOccurcesCaptionLbl: Label 'IntegerOccurces';
        CheckRep: Codeunit NumbertoText;



    trigger OnPreReport();
    begin
        CompanyInformation.GET;
        CompanyInformation.CalcFields(Picture);
        DebitAmountTotal := 0;
        CreditAmountTotal := 0;

    end;
}

