tableextension 50282 WarehouesExt extends "Warehouse Employee"
//B2BSPON22AU29>>
{
    fields
    {
        // Add changes to table fields here   field(65016; "Final Approval"; Option)
        field(50001; "State"; Option)
        {
            CalcFormula = Lookup(User.State WHERE("User Name" = FIELD("User ID")));
            Editable = false;
            FieldClass = FlowField;
            OptionCaption = 'Enable,Disable';
            OptionMembers = Enable,Disable;
        }
    }

    var
        myInt: Integer;
}