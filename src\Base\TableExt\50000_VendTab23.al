tableextension 50000 VendTab23 extends Vendor
{
    fields
    {
        field(50000; "Approval Status"; Enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
            editable = false;
        }
        field(50002; "Last Transaction Date"; Date)
        {
            CalcFormula = Max("Vendor Ledger Entry"."Posting Date" WHERE("Vendor No." = FIELD("No.")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(50003; "Vendor Classification"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Vendor Classification";

            trigger OnValidate()
            var
                venclasLRec: Record "Vendor Classification";
                VendLedgerEntryLVar: Record "Vendor Ledger Entry";
                UsrSet: record "User Setup";
            BEGIN
                IF venclasLRec.GET("Vendor Classification") then
                    venclasLRec.TestField("Blocking Period");
                IF UsrSet.GET(UserId) then begin
                    IF NOT UsrSet."Validate Vendor Classification" then begin
                        VendLedgerEntryLVar.Reset();
                        VendLedgerEntryLVar.SetRange("Vendor No.", "No.");
                        if VendLedgerEntryLVar.FindFirst() then
                            Error('Already Ledger entries Exist For This Vendor');
                    end;
                end else
                    error('You can not modify this field');
            END;
        }
        field(50004; "Vendor Type"; Enum PurchaseType)
        {
            DataClassification = CustomerContent;
        }
        field(50005; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;
        }
        field(50006; "Transport Contract"; enum ContractType)
        {
            DataClassification = CustomerContent;

            //TableRelation = "Transport Contract Types";
        }
        field(50007; "Main Vendor"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor;
        }
        field(50010; "Vendor Location"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = Location;
            Description = 'SUBCON1.0';
        }
        field(50011; Subcontractor; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
            trigger OnValidate()
            begin
                TestField("Vendor Location");
            end;
        }
        field(50012; Select; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "ORP No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Service Group"; Option)
        {
            Description = 'SAA3.0';
            DataClassification = CustomerContent;
            InitValue = Supplier;
            OptionCaption = 'Supplier,Overseas Suppliers,Contractor,Consultant,Hospital,Agent,Staff,Insurance,Transporter,Services,Inter Comp.,Expatriate Staff,PMS,Loader,SupplierLC';
            OptionMembers = Supplier,"Overseas Suppliers",Contractor,Consultant,Hospital,Agent,Staff,Insurance,Transporter,Services,"Inter Comp.","Expatriate Staff",PMS,Loader,SupplierLC;
        }
        field(50015; "Order No."; Code[20])
        {
            DataClassification = CustomerContent;
            Caption = 'Order No.';
            Description = 'UNL1.0';
            Editable = false;
            TableRelation = "Purchase Header"."No." WHERE("Document Type" = CONST(Order),
                                                           "Import File No." = FIELD("No."));

        }
        field(50016; "TIN No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Bank No."; Code[20])
        {
            Description = 'UNL1.0';

            trigger OnValidate();
            Var
                BankGRec: Record "Bank Account";
            begin
                BankGRec.RESET;
                if BankGRec.GET("Bank No.") then
                    "Bank Name" := BankGRec.Name;

                if "Bank No." = '' then
                    CLEAR("Bank Name");
            end;
        }
        field(50018; "Bank Name"; Text[50])
        {
            Description = 'UNL1.0';
        }
        field(50019; "Bank Routing Code"; Code[20])
        {
            TableRelation = "Bank Routing Code";

            trigger OnValidate();
            Var
                BankRC: Record "Bank Routing Code";
            begin
                if BankRC.GET("Bank Routing Code") then
                    "Bank Name" := BankRC."Bank Name";
            end;
        }
        field(50020; "Registration No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50021; "Staff ID"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Employee;
        }
        field(50023; "Vendor Payment Type"; Option)
        {
            OptionMembers = "",Cash,Credit;
            DataClassification = CustomerContent;
        }
        //Balu ********>>
        field(50024; "Vendor WHT Account"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor;
        }
        field(50025; "WHT Classification"; Enum WHTCalculation)
        {
            DataClassification = CustomerContent;
        }
        field(50027; "Transport Account"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        //Balu ********<<
        field(50029; "Fuel Reimbursement"; Boolean)//PKONJU19
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Vat On Fuel Reimbursement"; Boolean)//PKONJU19
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Fuel Reimbursement Acc."; Code[20])//PKONJU19
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50032; "Contact Person Name"; Text[50])//BaluAug25
        {
            DataClassification = CustomerContent;

        }
        field(50033; "Bank Account"; Text[50])//PKON22MA8-CR220031
        {
            DataClassification = CustomerContent;

        }
        field(50034; "State"; Code[20])
        {
            DataClassification = ToBeClassified;
            TableRelation = States.State;
        }
    }
    trigger OnModify()
    begin
        /*         if NOT (Select <> xrec.Select) then begin
                    if ("Approval Status" <> "Approval Status"::Open) then
                        error('you cannot modify record while in approval/release stage');
                end; */
    end;

    trigger OnInsert()
    begin
        //Blocked := Blocked::All;
    end;

}