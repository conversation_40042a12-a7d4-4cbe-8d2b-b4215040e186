tableextension 50275 "VAT Entires Ext" extends "VAT Entry"
{
    fields
    {
        field(50000; "Global Dimension 1 Code"; Code[20])
        {
            DataClassification = ToBeClassified;
            CaptionClass = '1,1,1';
            Caption = 'Global Dimension 1 Code';
        }
        field(50001; "Global Dimension 2 Code"; Code[20])
        {
            DataClassification = ToBeClassified;
            CaptionClass = '1,1,2';
            Caption = 'Global Dimension 1 Code';
        }
    }

    var
        myInt: Integer;
}