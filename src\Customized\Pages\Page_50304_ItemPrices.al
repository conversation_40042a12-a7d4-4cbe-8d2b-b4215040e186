page 50304 "Purchase Price List"
{
    PageType = ListPart;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Purchase Price";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = All;

                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;

                }
                field("Minimum Quantity"; "Minimum Quantity")
                {
                    ApplicationArea = All;

                }
                field("Starting Date"; "Starting Date")
                {
                    ApplicationArea = All;

                }
                field("Ending Date"; "Ending Date")
                {
                    ApplicationArea = All;

                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = All;

                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                    ApplicationArea = All;

                }

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
        
}