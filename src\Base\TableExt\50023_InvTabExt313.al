tableextension 50023 InvTabExt extends "Inventory Setup"
{
    fields
    {
        field(50000; "MRS Nos."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; "Purch. Requisition No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50002; "Create Purch Req On"; Option)
        {
            DataClassification = CustomerContent;
            OptionMembers = "Document Wise","Item Category Wise";
        }
        field(50003; "RFQ Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50004; "Trip ID NoSeries"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50005; "Inward Gate Entry Nos._NRGP"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50006; "Outward Gate Entry Nos.-NRGP"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }

        field(50007; "Scrap Journal Template Name"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Gen. Journal Template";
        }

        field(50008; "Scrap Journal Batch Name"; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "Gen. Journal Batch".Name WHERE("Journal Template Name" = field("Scrap Journal Template Name"));
        }
        field(50009; "Inward Gate Entry Nos. - RGP"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50010; "Outward Gate Entry Nos.-RGP"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50011; "SNOP FG Item Category"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Category";
        }
        field(50012; "MDV Nos."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50013; "TT InTransit Location"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Location.Code where("Use As In-Transit" = const(true));
        }
        field(50014; "CCD Approval No. series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50015; "SCD Approval No. series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50016; "Mail ID If Loadslp Nt Ack"; text[200])
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Requisition Nos."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50018; "Scrap Disposal Customer ID"; Code[10])
        {
            Description = 'SAA3.0';
            TableRelation = Customer;
        }
        field(50019; "Scrap Disposal GL Account No."; Code[10])
        {
            Description = 'SAA3.0';
            TableRelation = "G/L Account";
        }
        field(50021; "Allowed Date for Inv. BackDate"; date)
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Allowed Date for Backdate To"; date)
        {
            DataClassification = CustomerContent;
        }
        field(50023; "No Backdating Allowed"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Bin Reclass Mail Alert ID"; text[200])
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Shortg & Damg Mail Alert ID"; text[200])
        {
            DataClassification = CustomerContent;
        }
        field(50026; "Inward RGP No. Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series".Code;
        }
        field(50027; "Inward NRGP No. Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series".Code;
        }
        field(50028; "Outward RGP No. Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series".Code;
        }
        field(50029; "Outward NRGP No. Series"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series".Code;
        }
        field(50030; "Check Item Tracking in lSP"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Bin Reclass Mail Alert ID2"; text[200])
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Update Exp Date"; Boolean)
        {
            DataClassification = CustomerContent;
            Caption = 'Update Exp. Date In Whr Recp.';
        }
        field(50033; "POS Central Location"; Code[20])
        {
            Caption = 'POS Central Warehouse';
            DataClassification = CustomerContent;
            TableRelation = Location;

        }
        field(50034; "Retail Price Group"; Code[20])
        {
            Caption = 'Retail Price Group';
            DataClassification = CustomerContent;
            TableRelation = "Customer Price Group";

        }
        field(50035; "Retail Acct Location"; Code[20])
        {
            Caption = 'Retail Accounting Location';
            DataClassification = CustomerContent;
            TableRelation = "Dimension Value".CODE where("Global Dimension No." = filter(1));

        }
        field(50036; "Retail Stock outletID"; Code[20])
        {
            Caption = 'Retail Stock outletID';
            DataClassification = CustomerContent;
            //TableRelation = "Dimension Value".CODE where("Global Dimension No." = filter(1));

        }
        field(50037; "Retail Stock SupplierID"; Code[20])
        {
            Caption = 'Retail Stock SupplierID';
            DataClassification = CustomerContent;
            //TableRelation = "Dimension Value".CODE where("Global Dimension No." = filter(1));

        }
    }

}