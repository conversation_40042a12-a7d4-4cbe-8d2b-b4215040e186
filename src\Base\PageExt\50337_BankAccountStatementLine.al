pageextension 50337 BankStatemntStatmtLinePageExt extends "Bank Account Statement Lines"
{
    layout
    {
        addafter("Document No.")
        {
            field("Statement Line No."; "Statement Line No.")
            {
                ApplicationArea = all;
            }
            field("Matching Status"; "Matching Status")
            {
                ApplicationArea = all;
            }
            field("MatchLine No"; "MatchLine No")
            {
                ApplicationArea = all;


            }
            field("Cancel No"; "Cancel No")
            {
                ApplicationArea = all;
            }
            field(Description2; Description2)
            {
                ApplicationArea = all;
            }
        }
    }
    procedure ShowMatchLines()
    Var
        OriBnkSmnt: Record "Original Bank Statement";
    begin
        OriBnkSmnt.SETRANGE(OriBnkSmnt.Code, "Bank Account No.");
        OriBnkSmnt.SETRANGE(OriBnkSmnt."Statement No.", "Statement No.");
        OriBnkSmnt.SETRANGE(OriBnkSmnt."MatchLine No", "MatchLine No");
        if OriBnkSmnt.FINDSET then begin
            if PAGE.RUNMODAL(0, OriBnkSmnt) = ACTION::LookupOK then;
        end;
    end;

}