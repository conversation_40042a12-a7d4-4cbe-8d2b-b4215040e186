page 50995 "SMTP E-Mail Log" //PKONJU2
{
    Editable = false;
    PageType = List;
    SourceTable = "E-mail Log";
    ApplicationArea = all;
    UsageCategory = lists;
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Entry No"; "Entry No")
                {
                    ApplicationArea = all;
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = all;//PKONJU5
                }
                field(Details; Details)
                {
                    ApplicationArea = all;
                }
                field(Result; Result)
                {
                    ApplicationArea = all;
                }
                field("Error text1"; "Error text1")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Error text2"; "Error text2")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Error text3"; "Error text3")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("User Id"; "User Id")
                {
                    ApplicationArea = all;
                }
                field("Date Time"; "Date Time")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

