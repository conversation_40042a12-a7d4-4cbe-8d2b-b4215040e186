pageextension 50003 SalesRecPageExt459 extends "Sales & Receivables Setup"
{
    layout
    {
        addafter("Direct Debit Mandate Nos.")
        {
            field("Direct Sales Order"; "Direct Sales Order")
            {
                ApplicationArea = all;
            }
            field("Local Sales Order"; "Local Sales Order")
            {
                ApplicationArea = all;
            }
            field("Export Sales Order"; "Export Sales Order")
            {
                ApplicationArea = all;
            }
            field("Direct Sales Quote"; "Direct Sales Quote")
            {
                ApplicationArea = all;
            }
            field("Local Sales Quote"; "Local Sales Quote")
            {
                ApplicationArea = all;
            }
            field("Export Sales Quote"; "Export Sales Quote")
            {
                ApplicationArea = all;
            }
            field("Validate Cust. Credit/Cash"; "Validate Cust. Credit/Cash")
            {
                ApplicationArea = All;
            }
            field("Loading SLip No."; "Loading SLip No.")
            {
                ApplicationArea = ALL;
            }
            field("Posted Loading SLip No."; "Posted Loading SLip No.")
            {
                ApplicationArea = ALL;
            }
            field("Grace Period for Loading Slip"; "Grace Period for Loading Slip")
            {
                ApplicationArea = ALL;
            }
            field("Grace Period for Vehicle No."; "Grace Period for Vehicle No.")
            {
                ApplicationArea = ALL;
            }
            field("KD All Inv Rebate %"; "KD All Inv Rebate %")
            {
                ApplicationArea = all;
            }
            field("KD Big Vol. Taget Price"; "KD Big Vol. Taget Price")
            {
                ApplicationArea = all;
            }
            field("Calculate Promo On Cust. Grp."; "Calculate Promo On Cust. Grp.")
            {
                ApplicationArea = all;
            }
            field("Promo Schedule No"; "Promo Schedule No")
            {
                ApplicationArea = all;
            }
            field("KD Big Vol. Taget Rebate %"; "KD Big Vol. Taget Rebate %")
            {
                ApplicationArea = all;
            }
            field("KD Small Vol. Taget Rebate%"; "KD Small Vol. Taget Rebate%")
            {
                ApplicationArea = all;
            }
            field("KD KBD Rebate %"; "KD KBD Rebate %")
            {
                ApplicationArea = all;
            }
            field("Quartarly Vol. Incentive %"; "Quartarly Vol. Incentive %")
            {
                ApplicationArea = all;
            }
            field("KBD RED Rebate Weight%"; "KBD RED Rebate Weight%")
            {
                ApplicationArea = all;
            }
            field("KBD DMS Rebate Weight%"; "KBD DMS Rebate Weight%")
            {
                ApplicationArea = all;
            }
            field("KBD Veh. Run Day RebateW%"; "KBD Veh. Run Day RebateW%")
            {
                ApplicationArea = all;
            }
            field("KBD DMS Usage Days"; "KBD DMS Usage Days")
            {
                ApplicationArea = all;
            }
            field("KBD Average OOS Target"; "KBD Average OOS Target")
            {
                ApplicationArea = all;
            }
            field("KBD Veh. Run. Min Days"; "KBD Veh. Run. Min Days")
            {
                ApplicationArea = all;
            }
            field("Cr. Limit Blocking Days"; "Cr. Limit Blocking Days")
            {
                ApplicationArea = all;
            }
            field("Redistribution GL Account"; "Redistribution GL Account")
            {
                ApplicationArea = all;
            }
            field("Preventive Negative Quantity"; "Preventive Negative Quantity")
            {
                ApplicationArea = all;
            }
            field("XML File Path"; "XML File Path")
            {
                ApplicationArea = all;
            }
            field("Self Lifting Credit Memo"; "Self Lifting Credit Memo")
            {
                ApplicationArea = all;
            }
            field("Customer Statement Mail CC1"; "Customer Statement Mail CC1")
            {
                ApplicationArea = all;
            }
            field("Customer Statement Mail CC2"; "Customer Statement Mail CC2")
            {
                ApplicationArea = all;
            }
            field("Min Productivity Days"; "Min Productivity Days")
            {
                ApplicationArea = all;
            }
            field("Max Productivity Days"; "Max Productivity Days")
            {
                ApplicationArea = all;
            }
            field("Sales By Revenue"; "Sales By Revenue")
            {
                ApplicationArea = all;
            }
            field("Sales By Volume"; "Sales By Volume")
            {
                ApplicationArea = all;
            }
            field("Group Email Sales Notify"; "Group Email Sales Notify")
            {
                ApplicationArea = all;
            }
            field("Delete WareHouse Entry"; "Delete WareHouse Entry")
            {
                ApplicationArea = all;
            }
            field("Apply Promotions"; "Apply Promotions")
            {
                ApplicationArea = all;//Balu 05092021
            }
            field("Pos Ship & Invoice"; "Pos Ship & Invoice")
            {
                ApplicationArea = all;
            }
            field("Validate PSL in WHSHP"; "Validate PSL in WHSHP")
            {
                ApplicationArea = all;
            }
            field("Mail On Invoice"; "Mail On Invoice")
            {
                ApplicationArea = all;//B2BPKON260521
            }
            field("Enable InterSwitch"; "Enable InterSwitch")
            {
                ApplicationArea = all;
            }
            field("Sales Complaints No."; "Sales Complaints No.")//PKON22J2-CR220070
            {
                ApplicationArea = all;
            }
            field("Shrinkage %"; "Shrinkage %")//B2BSPON22AU17
            {
                ApplicationArea = all;
            }
            //>>>>>> G2S CAS-01356-T3R1X0
            field("SalesVolume Report Checker"; "SalesVolume Report Checker")
            {
                ApplicationArea = All;
            }
            //<<<<<< G2S CAS-01356-T3R1X0
            field("Cust. Cr. Limit No Series"; "Cust. Cr. Limit No Series")
            {
                ApplicationArea = All;
            }
            //Test
            field("Item filter"; "Item filter")
            {
                ApplicationArea = All;
                MultiLine = true;
            }
            field("Location filter"; "Location filter")
            {
                ApplicationArea = All;
                MultiLine = true;
            }
        }

    }

    actions
    {
        addafter("Payment Methods")
        {
            action("Get Record Count")
            {
                ApplicationArea = All;
                Caption = 'Get Record Count';
                Image = Import;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    if Rec."Ext. Doc. No. Mandatory" then
                        cu.clearTransferLine(true) else
                        cu.clearSalesreturnLine(true);
                end;
            }

            action("Delete Trans Line")
            {
                ApplicationArea = All;
                Caption = 'Delete Transaction Line';
                Image = Import;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    if Rec."Ext. Doc. No. Mandatory" then
                        cu.clearTransferLine(false) else
                        cu.clearSalesreturnLine(false);
                end;

            }
        }
    }

    var
        CU: Codeunit 50098;

    trigger OnOpenPage()
    begin
        // cu.clearComponentLine();
    end;

}