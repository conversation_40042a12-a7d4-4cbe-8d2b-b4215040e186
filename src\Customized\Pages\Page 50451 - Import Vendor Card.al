page 50451 "Import Vendor Card"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>ben
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Import Vendors.
    // 
    // 1.0      CHI      21-Oct-11      -> New function "UnblockVendor" and is trggered from the new Menu Item button
    //                                     "Unblock Vendor" from the menu button "Functions".
    // 
    // 1.0      HO        08-Sep-12    -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Import Vendor No.

    Caption = 'Import Vendor Card';
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Card;
    RefreshOnActivate = true;
    SourceTable = "Vendor 2";
    SourceTableView = WHERE("Vendor Type" = FILTER(Import));


    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {

                    trigger OnAssistEdit();
                    begin
                        /*IF AssistEdit(xRec) THEN
                          CurrPage.UPDATE;
                          */

                    end;
                }
                field(Name; Name)
                {
                }
                field(Address; Address)
                {
                }
                field("Address 2"; "Address 2")
                {
                }
                field("Post Code"; "Post Code")
                {
                    Caption = 'Post Code/City';
                }
                field(City; City)
                {
                }
                field("Country/Region Code"; "Country/Region Code")
                {
                }
                field("Phone No."; "Phone No.")
                {
                }
                field("Primary Contact No."; "Primary Contact No.")
                {
                }
                field(Contact; Contact)
                {
                    Editable = ContactEditable;

                    trigger OnValidate();
                    begin
                        ContactOnAfterValidate;
                    end;
                }
                /*field("No. of Revision"; "No. of Revision")
                {
                }*/
                field("Search Name"; "Search Name")
                {
                }
                field("Balance (LCY)"; "Balance (LCY)")
                {

                    trigger OnDrillDown();
                    var
                        VendLedgEntry: Record "Vendor Ledger Entry";
                        DtldVendLedgEntry: Record "Detailed Vendor Ledg. Entry";
                    begin
                        DtldVendLedgEntry.SETRANGE("Vendor No.", "No.");
                        COPYFILTER("Global Dimension 1 Filter", DtldVendLedgEntry."Initial Entry Global Dim. 1");
                        COPYFILTER("Global Dimension 2 Filter", DtldVendLedgEntry."Initial Entry Global Dim. 2");
                        COPYFILTER("Currency Filter", DtldVendLedgEntry."Currency Code");
                        VendLedgEntry.DrillDownOnEntries(DtldVendLedgEntry);
                    end;
                }
                field("Purchaser Code"; "Purchaser Code")
                {
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                }
                field(Blocked; Blocked)
                {
                }
                field("Last Date Modified"; "Last Date Modified")
                {
                }
                field("Vendor Type"; "Vendor Type")
                {
                }
                /*field(Status; Status)
                {
                }*/
                field("Outstanding Orders"; "Outstanding Orders")
                {
                }
                field("Service Group"; "Service Group")
                {
                }
            }
            group(Communication)
            {
                Caption = 'Communication';
                field("Fax No."; "Fax No.")
                {
                }
                field("E-Mail"; "E-Mail")
                {
                }
                field("Home Page"; "Home Page")
                {
                }
                field("IC Partner Code"; "IC Partner Code")
                {

                    trigger OnValidate();
                    begin
                        ICPartnerCodeOnAfterValidate;
                    end;
                }
            }
            group(Invoicing)
            {
                Caption = 'Invoicing';
                field("Pay-to Vendor No."; "Pay-to Vendor No.")
                {
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                }
                field("Vendor Posting Group"; "Vendor Posting Group")
                {
                }
                field("Invoice Disc. Code"; "Invoice Disc. Code")
                {
                }
                field("Prices Including VAT"; "Prices Including VAT")
                {
                }
                field("Prepayment %"; "Prepayment %")
                {
                }
            }
            group(Payments)
            {
                Caption = 'Payments';
                field("Application Method"; "Application Method")
                {
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                }
                field("Payment Method Code"; "Payment Method Code")
                {
                }
                field(Priority; Priority)
                {
                }
                /*field("Vendor Payment Type"; "Vendor Payment Type")
                {
                }*/
                field("Bank No."; "Bank No.")
                {
                }
                field("Bank Name"; "Bank Name")
                {
                }
                field("Our Account No."; "Our Account No.")
                {
                }
                field("Block Payment Tolerance"; "Block Payment Tolerance")
                {

                    trigger OnValidate();
                    begin
                        /*if "Block Payment Tolerance" then begin
                            if CONFIRM(Text002, false) then
                                PaymentToleranceMgt.DelTolVendLedgEntry(Rec);
                        end else begin
                            if CONFIRM(Text001, false) then
                                PaymentToleranceMgt.CalcTolVendLedgEntry(Rec);
                        end;*/
                    end;
                }
            }
            group(Receiving)
            {
                Caption = 'Receiving';
                field("Location Code"; "Location Code")
                {
                }
                field("Shipment Method Code"; "Shipment Method Code")
                {
                }
                field("Lead Time Calculation"; "Lead Time Calculation")
                {
                }
                field("Base Calendar Code"; "Base Calendar Code")
                {
                    DrillDown = false;
                }
                /*field("Customized Calendar"; CalendarMg.CustomizedCalendarExistText(CustomizedCalendar."Source Type"::Vendor, "No.", '', "Base Calendar Code"))
                {
                    Caption = 'Customized Calendar';
                    Editable = false;

                    trigger OnDrillDown();
                    begin
                        CurrPage.SAVERECORD;
                        TESTFIELD("Base Calendar Code");
                        CalendarMg.ShowCustomizedCalendar(CustomizedCalEntry."Source Type"::Vendor, "No.", '', "Base Calendar Code");
                    end;
                }*/
            }
            group("Foreign Trade")
            {
                Caption = 'Foreign Trade';
                field("Currency Code"; "Currency Code")
                {
                }
                field("Language Code"; "Language Code")
                {
                }
                field("VAT Registration No."; "VAT Registration No.")
                {
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Ven&dor")
            {
                Caption = 'Ven&dor';
                action("Ledger E&ntries")
                {
                    Caption = 'Ledger E&ntries';
                    RunObject = Page "Vendor Ledger Entries";
                    RunPageLink = "Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Vendor No.");
                    ShortCutKey = 'Ctrl+F5';
                }
                group("Purchase &History")
                {
                    Caption = 'Purchase &History';
                    action("Used as &Buy-from Vendor")
                    {
                        Caption = 'Used as &Buy-from Vendor';

                        trigger OnAction();
                        begin
                            //PurchInfoPaneMgt.LookupVendHistory("No.",FALSE);
                        end;
                    }
                    action("Used as &Pay-to Vendor")
                    {
                        Caption = 'Used as &Pay-to Vendor';

                        trigger OnAction();
                        begin
                            //PurchInfoPaneMgt.LookupVendHistory("No.",TRUE);}//CHI2018
                        end;
                    }
                }
                action("Co&mments")
                {
                    Caption = 'Co&mments';
                    RunObject = Page 124;
                    RunPageLink = "Table Name" = CONST(Vendor),
                                  "No." = FIELD("No.");
                }
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    RunObject = Page "Default Dimensions";
                    RunPageLink = "Table ID" = CONST(23),
                                  "No." = FIELD("No.");
                    ShortCutKey = 'Shift+Ctrl+D';
                }
                action("Bank Accounts")
                {
                    Caption = 'Bank Accounts';
                    RunObject = Page "Vendor Bank Account Card";
                    RunPageLink = "Vendor No." = FIELD("No.");
                }
                action("Order &Addresses")
                {
                    Caption = 'Order &Addresses';
                    RunObject = Page "Order Address";
                    RunPageLink = "Vendor No." = FIELD("No.");
                }
                action("C&ontact")
                {
                    Caption = 'C&ontact';

                    trigger OnAction();
                    begin
                        ShowContact;
                    end;
                }
                separator(Separator11)
                {
                }
                action(Statistics)
                {
                    Caption = 'Statistics';
                    RunObject = Page "Vendor Statistics";
                    RunPageLink = "No." = FIELD("No."),
                                  "Global Dimension 1 Filter" = FIELD("Global Dimension 1 Filter"),
                                  "Global Dimension 2 Filter" = FIELD("Global Dimension 2 Filter");
                    ShortCutKey = 'F9';
                }
                action("Entry Statistics")
                {
                    Caption = 'Entry Statistics';
                    RunObject = Page "Vendor Entry Statistics";
                    RunPageLink = "No." = FIELD("No."),
                                  "Date Filter" = FIELD("Date Filter"),
                                  "Global Dimension 1 Filter" = FIELD("Global Dimension 1 Filter"),
                                  "Global Dimension 2 Filter" = FIELD("Global Dimension 2 Filter");
                }
                action(Purchases)
                {
                    Caption = 'Purchases';
                    RunObject = Page "Vendor Purchases";
                    RunPageLink = "No." = FIELD("No."),
                                  "Global Dimension 1 Filter" = FIELD("Global Dimension 1 Filter"),
                                  "Global Dimension 2 Filter" = FIELD("Global Dimension 2 Filter");
                }
                separator(Separator83)
                {
                    Caption = '""';
                }
                action("Cross References")
                {
                    Caption = 'Cross References';
                    RunObject = Page "Cross References";
                    RunPageLink = "Cross-Reference Type" = CONST(Vendor),
                                  "Cross-Reference Type No." = FIELD("No.");
                    RunPageView = SORTING("Cross-Reference Type", "Cross-Reference Type No.");
                }
                separator(Separator108)
                {
                }
                action("Online Map")
                {
                    Caption = 'Online Map';

                    trigger OnAction();
                    begin
                        DisplayMap;
                    end;
                }
                action("&Approvals")
                {
                    Caption = '&Approvals';

                    trigger OnAction();
                    var
                        ApprovalEntries: Page 658;
                    begin
                        /*
                        ApprovalEntries.Setfilters(DATABASE::Vendor,27,"No.");
                        ApprovalEntries.RUN;*///CHI WF
                    end;
                }
            }
            group("&Purchases")
            {
                Caption = '&Purchases';
                action(Items)
                {
                    Caption = 'Items';
                    RunObject = Page "Vendor Item Catalog";
                    RunPageLink = "Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Vendor No.", "Item No.");
                }
                action("Invoice &Discounts")
                {
                    Caption = 'Invoice &Discounts';
                    RunObject = Page "Vend. Invoice Discounts";
                    RunPageLink = Code = FIELD("Invoice Disc. Code");
                }
                action(Prices)
                {
                    Caption = 'Prices';
                    RunObject = Page "Purchase Prices";
                    RunPageLink = "Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Vendor No.");
                }
                action("Line Discounts")
                {
                    Caption = 'Line Discounts';
                    RunObject = Page "Purchase Line Discounts";
                    RunPageLink = "Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Vendor No.");
                }
                action("Prepa&yment Percentages")
                {
                    Caption = 'Prepa&yment Percentages';
                    RunObject = Page "Purchase Prepmt. Percentages";
                    RunPageLink = "Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Vendor No.");
                }
                action("S&td. Vend. Purchase Codes")
                {
                    Caption = 'S&td. Vend. Purchase Codes';
                    RunObject = Page "Standard Vendor Purchase Codes";
                    RunPageLink = "Vendor No." = FIELD("No.");
                }
                separator(Separator117)
                {
                }
                action(Quotes)
                {
                    Caption = 'Quotes';
                    RunObject = Page "Purchase Quote";
                    RunPageLink = "Buy-from Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Document Type", "Buy-from Vendor No.");
                }
                action("Blanket Orders")
                {
                    Caption = 'Blanket Orders';
                    RunObject = Page "Blanket Purchase Order";
                    RunPageLink = "Buy-from Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Document Type", "Buy-from Vendor No.");
                }
                action(Orders)
                {
                    Caption = 'Orders';
                    RunObject = Page "Purchase Order";
                    RunPageLink = "Buy-from Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Document Type", "Buy-from Vendor No.", "No.");
                }
                action("Return Orders")
                {
                    Caption = 'Return Orders';
                    RunObject = Page "Purchase Return Order";
                    RunPageLink = "Buy-from Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Document Type", "Buy-from Vendor No.", "No.");
                }
                action("Item &Tracking Entries")
                {
                    Caption = 'Item &Tracking Entries';

                    trigger OnAction();
                    var
                        ItemTrackingMgt: Codeunit "Item Tracking Management";
                    begin

                        //ItemTrackingMgt.CallItemTrackingEntryForm(2,"No.",'','','','','');
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Apply Template")
                {
                    Caption = 'Apply Template';
                    Ellipsis = true;

                    trigger OnAction();
                    var
                        TemplateMgt: Codeunit "Config. Template Management";
                        RecRef: RecordRef;
                    begin
                        RecRef.GETTABLE(Rec);
                        TemplateMgt.UpdateFromTemplateSelection(RecRef);
                    end;
                }
                separator("----------------")
                {
                    Caption = '----------------';
                }
                action("Send A&pproval Request")
                {
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    begin

                        //if Block then
                        //ERROR('Vendor %1 is Blocked', "No.");
                        //IF ApprovalMgt.SendVendorApprovalRequest(Rec) THEN;
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin
                        //if Status = Status::Blocked then
                        //ERROR('Vendor %1 is Blocked closed', "No.");

                        //IF ApprovalMgt.CancelVendorApprovalRequest(Rec,TRUE,TRUE) THEN;
                    end;
                }
                separator(Separator1102152006)
                {
                    Caption = '----------------';
                }
                action("Re&lease")
                {
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        /*if Status = Status::Blocked then
                            ERROR('Vendor %1 is Blocked', "No.");

                        PerformManualRelease;*/
                    end;
                }
                action("Amend Vendor")
                {
                    Caption = 'Amend Vendor';

                    trigger OnAction();
                    begin
                        /*IF Status = Status :: Blocked THEN
                          ERROR('Vendor %1 is Blocked',"No.");
                        */
                        //PerformManualRelease;

                    end;
                }
                separator(Separator1102152009)
                {
                    Caption = '----------------';
                }
                action("Unblock Vendor")
                {
                    Caption = 'Unblock Vendor';
                    Visible = false;

                    trigger OnAction();
                    begin
                        //UnblockVendor; // CHI1.0
                    end;
                }
                action("Block Vendor")
                {
                    Caption = 'Block Vendor';

                    trigger OnAction();
                    begin

                        //BlockVendor; // CHI1.0
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        ActivateFields;

        OnAfterGetCurrRecord;
    end;

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("No.", 23, TODAY, TIME, USERID, DATABASE::Vendor); //HO1.0
    end;

    trigger OnFindRecord(Which: Text): Boolean;
    var
        RecordFound: Boolean;
    begin
        RecordFound := FIND(Which);
        if not RecordFound and (GETFILTER("No.") <> '') then begin
            MESSAGE(Text003, GETFILTER("No."));
            SETRANGE("No.");
            RecordFound := FIND(Which);
        end;
        exit(RecordFound);
    end;

    trigger OnInit();
    begin
        ContactEditable := true;
        MapPointVisible := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Vendor Type" := "Vendor Type"::Import;
        OnAfterGetCurrRecord;
    end;

    trigger OnOpenPage();
    var
        MapMgt: Codeunit "Online Map Management";
    begin
        ActivateFields;
        if not MapMgt.TestSetup then
            MapPointVisible := false;

        SETRANGE("Vendor Type", "Vendor Type"::Import);
    end;

    var
        CalendarMgmt: Codeunit "Calendar Management";
        //CalendarMg: Codeunit "Calendar Management Ext";
        PaymentToleranceMgt: Codeunit "Payment Tolerance Management";
        CustomizedCalEntry: Record "Customized Calendar Entry";
        CustomizedCalendar: Record "Customized Calendar Change";
        Text001: Label 'Do you want to allow payment tolerance for entries that are currently open?';
        Text002: Label 'Do you want to remove payment tolerance from entries that are currently open?';
        PurchInfoPaneMgt: Codeunit "Purchases Info-Pane Management";
        Text003: Label 'The vendor %1 does not exist.';
        ApprovalMgt: Codeunit 1535;
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        [InDataSet]
        MapPointVisible: Boolean;
        [InDataSet]
        ContactEditable: Boolean;

    procedure ActivateFields();
    begin
        ContactEditable := "Primary Contact No." = '';
    end;

    local procedure ContactOnAfterValidate();
    begin
        ActivateFields;
    end;

    local procedure ICPartnerCodeOnAfterValidate();
    begin
        CurrPage.UPDATE;
    end;

    local procedure OnAfterGetCurrRecord();
    begin
        xRec := Rec;
        ActivateFields;
    end;
}

