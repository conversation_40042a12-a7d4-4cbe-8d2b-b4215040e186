﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>d2ff38cd-8c04-4cb8-bae8-c2197505f394</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.54167in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.84167in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.66667in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle1">
                          <ReportItems>
                            <Textbox Name="No">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!No.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>No</rd:DefaultName>
                              <Top>0in</Top>
                              <Left>0in</Left>
                              <Height>0.25in</Height>
                              <Width>1.54167in</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Image Name="Image3">
                              <Source>Database</Source>
                              <Value>=Fields!CPicture.Value</Value>
                              <MIMEType>image/jpeg</MIMEType>
                              <Sizing>FitProportional</Sizing>
                              <Top>0.25in</Top>
                              <Left>0in</Left>
                              <Height>1.35833in</Height>
                              <Width>1.51389in</Width>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Image>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle2">
                          <ReportItems>
                            <Image Name="Image2">
                              <Source>Database</Source>
                              <Value>=Fields!QR.Value</Value>
                              <MIMEType>image/bmp</MIMEType>
                              <Sizing>FitProportional</Sizing>
                              <Top>0in</Top>
                              <Left>0.04167in</Left>
                              <Height>1.60833in</Height>
                              <Width>1.75834in</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Image>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details">
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                    </PageBreak>
                  </Group>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.005in</Top>
            <Left>0in</Left>
            <Height>1.66667in</Height>
            <Width>3.38334in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>1.69999in</Height>
        <Style>
          <Border>
            <Style>Solid</Style>
          </Border>
          <TopBorder>
            <Color>Black</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </TopBorder>
          <BottomBorder>
            <Color>Black</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </BottomBorder>
          <LeftBorder>
            <Color>Black</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </LeftBorder>
          <RightBorder>
            <Color>Black</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </RightBorder>
        </Style>
      </Body>
      <Width>3.39667in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="No">
          <DataField>No</DataField>
        </Field>
        <Field Name="QR">
          <DataField>QR</DataField>
        </Field>
        <Field Name="CPicture">
          <DataField>CPicture</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>