codeunit 50019 "Job Queues"
{
    TableNo = "Job Queue Entry";
    trigger OnRun()
    begin
        case Rec."Parameter String" of
            'InsuranceMail':
                SendMail();
            'Due Date for Cr. Limit':
                CheckDueDateCrLimit();
        end;
    end;

    local procedure SendMail()
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        FASetup: Record "FA Setup";
        Insurance: Record Insurance;
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
    begin
        IF IsInsuranceExists() then begin
            FASetup.get();
            SMTPMailSetup.get();
            SenderAddr := SMTPMailSetup."User ID";
            RecepientAddr.Add(FASetup."E-Mail");
            SubjectTxt := 'Insurance Renewal';
            SMTPMAil.CreateMessage('', SenderAddr, RecepientAddr, SubjectTxt, '', true);
            SMTPMail.AppendBody(CreateEmailBody());
            SMTPMAil.send;
        end;
    end;

    Local procedure IsInsuranceExists(): Boolean
    var
        InsuranceLRec: Record Insurance;
        FASetup: Record "FA Setup";
        CheckDate: Date;
    begin
        FASetup.get();
        CheckDate := CalcDate(FASetup."Insurance Period", Today);
        InsuranceLRec.Reset();
        InsuranceLRec.SetRange(Blocked, false);
        InsuranceLRec.SetRange("Expiration Date", CheckDate);
        exit(InsuranceLRec.FindFirst());
    end;

    Local procedure CreateEmailBody() EmailBodyText: Text
    var
        FASetup: Record "FA Setup";
        Insurance: Record Insurance;
        CheckDate: Date;
    begin
        EmailBodyText += '<table border="1">';
        EmailBodyText += '<tr>';
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Insurance No.');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Policy No.');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Description');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Expiration Date');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'FA Location Code');
        EmailBodyText += '</tr>';
        FASetup.get();
        CheckDate := CalcDate(FASetup."Insurance Period", Today);
        Insurance.Reset();
        Insurance.SetRange(Blocked, false);
        Insurance.SetRange("Expiration Date", CheckDate);
        IF Insurance.FindSet() then
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', Insurance."No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', Insurance."Policy No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', Insurance.Description);
                EmailBodyText += StrSubstNo('<td>%1</td>', Insurance."Expiration Date");
                EmailBodyText += StrSubstNo('<td>%1</td>', Insurance."FA Location Code");
                EmailBodyText += '</tr>';
            until Insurance.Next = 0;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    local procedure CheckDueDateCrLimit()
    var
        Customer: Record Customer;
        CustLedgEntry: Record "Cust. Ledger Entry";
        SRSetup: Record "Sales & Receivables Setup";
        CustCrLimitSchd: Record "Cust. Cr. Limit Schedule";
        CheckDate: Date;
    begin
        SRSetup.Get();
        CheckDate := CalcDate('-' + format(SRSetup."Cr. Limit Blocking Days"), Today);
        Customer.Reset();
        Customer.SetFilter("Customer Credit type", '<>%1', Customer."Customer Credit type"::"Cash n Carry");
        IF Customer.FindSet() then
            repeat
                CustLedgEntry.Reset();
                CustLedgEntry.Setrange("Customer No.", Customer."No.");
                CustLedgEntry.Setfilter("Remaining Amount", '<>%1', 0);
                CustLedgEntry.SetFilter("Due Date", '<%1', CheckDate);
                IF CustLedgEntry.FindFirst() then begin
                    CustCrLimitSchd.Reset();
                    CustCrLimitSchd.SetRange("Customer No.", CustLedgEntry."Customer No.");
                    CustCrLimitSchd.SetRange("Customer Type", CustCrLimitSchd."Customer Type"::"Credit Customer");
                    IF CustCrLimitSchd.FindFirst() then begin
                        CustCrLimitSchd.Status := CustCrLimitSchd.Status::Expired;
                        CustCrLimitSchd.Modify();
                        Customer."Approval Status" := Customer."Approval Status"::Open;
                        Customer.Blocked := Customer.Blocked::All;
                        Customer.Modify();
                    end;
                end;
            until Customer.Next() = 0;
    end;
}