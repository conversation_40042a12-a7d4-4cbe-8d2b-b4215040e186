table 50400 CHIERP_SalesItemRestriction
{
    DataClassification = CustomerContent;
    Caption = 'Sales Item Restriction';
    DrillDownPageId = CHIERP_SalesItemRestrictions;
    LookupPageId = CHIERP_SalesItemRestrictions;

    fields
    {
        field(1; CHIERP_ParentItemNo; Code[20])
        {
            Caption = 'Parent Item No.';
            TableRelation = Item;
        }
        field(2; CHIERP_ItemNo; code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item;
        }
        field(3; CHIERP_StartingDate; Date)
        {
            Caption = 'Starting Date';

        }
        field(4; CHIERP_EndingDate; Date)
        {
            Caption = 'Ending Date';
        }
        field(5; CHIERP_QuantityRestriction; Option)
        {
            Caption = 'Quantity Restriction';
            OptionMembers = "Same Quantity","Minimum Quantity","No Quantity Restriction";
            OptionCaption = 'Same Quantity, Minimum Quantity, NoQuantityRestriction';
        }
        field(6; CHIERP_MinimumQuantity; Decimal)
        {
            Caption = 'Minimum Quantity';
        }
    }

    keys
    {
        key(PK; CHIERP_ParentItemNo, CHIERP_ItemNo, CHIERP_StartingDate)
        {
            Clustered = true;
        }
    }
}