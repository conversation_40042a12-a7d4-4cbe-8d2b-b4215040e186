page 50119 "Promo. Schedule"
{
    DelayedInsert = false;
    PageType = Document;
    SourceTable = "Promo Schedule";
    SourceTableView = SORTING("Document Type", "No.")
                      ORDER(Ascending)
                      WHERE("Document Type" = CONST(Promo));
    UsageCategory = Tasks;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    trigger OnAssistEdit()
                    begin
                        TestField(Status, Status::Open);
                        if AssistEdit(Rec) then
                            CurrPage.Update(false);
                    end;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                    ApplicationArea = all;
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    begin
                        TestField(Status, Status::Open);
                    end;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("Export Cust Offers"; "Export Cust Offers")
                {
                    ApplicationArea = all;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = all;
                }
                field("Scheme Applicable"; "Scheme Applicable")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Application Type"; "Application Type")
                {
                    ApplicationArea = all;

                    trigger onvalidate()
                    BEGIN
                        IF (xRec."Application Type" <> rec."Application Type") then
                            clear("Customer Filter");
                    END;
                }
                field("Customer Filter"; "Customer Filter")
                {
                    ApplicationArea = all;
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        CustList: Page "Customer List";
                        CustPriceGrList: Page "Customer Posting Groups";
                        Cust: Record Customer;
                        CustPost: Record "Customer Posting Group";
                    begin
                        if "Application Type" = "Application Type"::"All Customers" then
                            exit;
                        case "Application Type" of
                            "Application Type"::Customer:
                                begin
                                    Cust.Reset();
                                    IF Cust.FindSet() then;
                                    CustList.SetTableView(Cust);
                                    CustList.LookupMode := true;
                                    if CustList.RunModal = ACTION::LookupOK then
                                        "Customer Filter" := Cust."No.";
                                end;
                            "Application Type"::"Customer Posting Group":
                                begin
                                    CustPost.Reset();
                                    IF CustPost.FindSet() then;
                                    CustPriceGrList.SetTableView(CustPost);
                                    CustPriceGrList.LookupMode := true;
                                    if CustPriceGrList.RunModal = ACTION::LookupOK then
                                        "Customer Filter" := CustPost.Code;
                                end;
                        end;
                    end;
                }

            }
            part(PromoSchdLines; "Promo. Schedule Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Document Type" = FIELD("Document Type"),
                              "Document No." = FIELD("No.");
            }
            group(Retail)
            {
                Caption = 'Retail';
                field("Retail Promo"; "Retail Promo")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("P&romo. Schd.")
            {
                Caption = 'P&romo. Schd.';
                separator(Separator1102152037)
                {
                }
                action("&Approvals")
                {
                    ApplicationArea = All;
                    Image = Action;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }

            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Scheme Restrictions")
                {
                    ApplicationArea = all;
                    Image = BreakRulesList;
                    Caption = 'Scheme Restrictions';

                    RunObject = Page "Promo Schd. restrictions";
                    RunPageLink = "Document Type" = FIELD("Document Type"),
                                  "Promo Schd. Code" = FIELD("No.");
                }
                separator("---")
                {
                    Caption = '---';
                }
                action("Send A&pproval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    //Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin

                        IF allinoneCU.CheckPRSApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendPRSForApproval(Rec);
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    //Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    var
                        SalesLine: Record "Sales Line";
                    begin

                        allinoneCU.OnCancelPRSForApproval(Rec);
                    end;
                }
                separator(Separator1102152024)
                {
                }

                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    var
                        SalesLine: Record "Sales Line";
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50038);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()

                    begin
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendPRSforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                separator(Separator1102152014)
                {
                }
                action("&Close Scheme")
                {
                    ApplicationArea = all;
                    image = close;
                    Caption = '&Close Scheme';
                    trigger OnAction();
                    begin
                        CloseDocument;
                    end;
                }
            }
        }
    }
    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Document Type" := "Document Type"::Promo;
        "Dim. Document Type" := "Dim. Document Type"::Promo;
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;




    var
        ApprovalMgt: Codeunit 1535;
        RecordRest: record "Restricted Record";
        WorkflowManagement: Codeunit "Workflow Management";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        allinoneCU: codeunit Codeunit1;
}

