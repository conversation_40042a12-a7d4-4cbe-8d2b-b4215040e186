/// <summary>
/// Page CustomSetup (ID 50204).
/// </summary>
page 50204 CustomSetup
{
    Caption = 'Custom Setup';
    PageType = List;
    SourceTable = "Custom Setup";
    ApplicationArea = All;
    UsageCategory = Lists;


    layout
    {
        area(content)
        {
            repeater(General)
            {
                Caption = 'General';
                field(ID; Rec.ID)
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field(Category; Rec.Category)
                {
                    ApplicationArea = All;
                }
                field("Posting Group"; "Posting Group")
                {
                    ApplicationArea = All;
                }
                field("Validity Period"; "Validity Period")
                {
                    ApplicationArea = All;
                }
                field("Outreach Dist. Email"; "Outreach Dist. Email")
                {
                    ApplicationArea = All;
                }
                //KD REBATE 280424
                field("100% Disc. VAT Prod. Post Grp"; Rec."100% Disc. VAT Prod. Post Grp")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var
                        myInt: Integer;
                    begin
                        if Rec.Category <> Rec.Category::"KD Rebate" then begin
                            Rec."100% Disc. VAT Prod. Post Grp" := '';
                            Rec.Modify();
                        end;
                    end;
                }

                field("100% Disc. VAT Bus. Post Grp"; Rec."100% Disc. VAT Bus. Post Grp")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var
                        myInt: Integer;
                    begin
                        if Rec.Category <> Rec.Category::"KD Rebate" then begin
                            Rec."100% Disc. VAT Bus. Post Grp" := '';
                            Rec.Modify();
                        end;
                    end;
                }
                //KD Rebate 280424
                field("Created By"; Rec."Created By")
                {
                    ApplicationArea = All;
                }
                //KD Rebate 280424
                field("Date Created"; Rec."Date Created")
                {
                    ApplicationArea = All;
                }
                //scratchpad posting 260424
                //scratchpad 050724
                field("Scratchpad Journal Batch"; Rec."Scratchpad Journal Batch")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var
                        myInt: Integer;
                    begin
                        if Rec.Category <> Rec.Category::"Project LEAP" then begin
                            Rec."Scratchpad Journal Batch" := '';
                            Rec.Modify();
                        end;
                    end;
                }
                field("Scratchpad Jnl. Template"; Rec."Scratchpad Jnl. Template")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    var
                        myInt: Integer;
                    begin
                        if Rec.Category <> Rec.Category::"Project LEAP" then begin
                            Rec."Scratchpad Jnl. Template" := '';
                            Rec.Modify();
                        end;
                    end;
                }
            }
        }
    }
}
