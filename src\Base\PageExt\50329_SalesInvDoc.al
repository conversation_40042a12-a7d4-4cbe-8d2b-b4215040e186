pageextension 50329 SaleInvLined extends "Posted Sales Invoice"
{
    layout
    {
        // Add changes to page layout here
        addafter("Sell-to Customer Name")//PKON22M31-CR220074
        {
            field("Created By"; "Created By")
            {
                ApplicationArea = all;
            }
            field("Created Date"; "Created Date")
            {
                ApplicationArea = all;
            }
            //270324
            field("Rebate Discount"; "Rebate Discount")
            {
                ApplicationArea = All;
                Visible = false;
            }
            //270324
        }
    }

    actions
    {
        modify(Print)
        {
            trigger OnBeforeAction()
            var
                uSrSet: record "User Setup";
            begin
                //b2bpksalecorr12
                // IF "No. Printed" > 1 then begin
                uSrSet.get(UserId);
                IF NOT uSrSet."Reprint Invoiced & Credm Docs" then
                    Error('You do not have permissions to reprint the document.');
                // end
            end;
        }
    }
}