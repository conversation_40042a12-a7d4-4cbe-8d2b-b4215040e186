page 50055 "Service Vendor Rate List"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Service Vendor Rate";
    CardPageId = "Service Vendor Rate";
    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Service Code"; "Service Code")
                {
                    ApplicationArea = All;

                }
                field("Service Description"; "Service Description")
                {
                    ApplicationArea = All;

                }
                field("Vendor Code"; "Vendor Code")
                {
                    ApplicationArea = All;

                }
                field("Vendor Name"; "Vendor Name")
                {
                    ApplicationArea = All;

                }
                field("Service Rate"; "Service Rate")
                {
                    ApplicationArea = All;

                }
            }
        }
        area(Factboxes)
        {

        }
    }

    actions
    {
        area(Processing)
        {

        }
    }
}