tableextension 50124 RelativeTabExt extends Relative
{
    fields
    {
        field(50100; Capacity; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = Sum ("Machine Center".Capacity WHERE("No." = FIELD(Code)));
            Editable = false;
        }
        field(50101; Used; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = Sum ("Supply Chain Disp. Bud."."Prod. Budget" WHERE(Indentation = FILTER(<> 0), "Prod. Line No." = FIELD(Code), Month = FIELD("Used for Month"), Year = FIELD("Used for Year"), "Week No." = FIELD("Used for Week")));
            Editable = false;
        }
        field(50102; Balance; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50103; "Used for Month"; integer)
        {
            DataClassification = CustomerContent;
        }
        field(50104; "Used for Year"; integer)
        {
            DataClassification = CustomerContent;
            trigger onvalidate()
            begin
                CALCFIELDS(Capacity, Used);
                Balance := Capacity - Used;
            end;
        }
        field(50105; "Used for Week"; integer)
        {
            DataClassification = CustomerContent;
        }
    }
}