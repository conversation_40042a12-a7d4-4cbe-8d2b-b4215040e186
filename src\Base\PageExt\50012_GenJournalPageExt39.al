pageextension 50012 GenJournalPageExt extends "General Journal"
{
    layout
    {
        addbefore("Bal. Account No.")
        {

            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = All;
            }
            field("WHT Group"; "WHT Group")
            {
                ApplicationArea = all;

            }
            field("WHT %"; "WHT %")
            {
                ApplicationArea = all;
            }

            field("WHT Amount"; "WHT Amount")
            {
                ApplicationArea = all;
            }
            field("WHT Amount(LCY)"; "WHT Amount(LCY)")
            {
                ApplicationArea = all;
            }
            field("WHT Account"; "WHT Account")
            {
                ApplicationArea = all;
                Editable = false;
            }
            field("Date PMS Availed"; "Date PMS Availed")
            {
                ApplicationArea = all;
            }
            field("Cheque No."; "Cheque No.")
            {
                ApplicationArea = all;
            }
            field("Cheque Date"; "Cheque Date")
            {
                ApplicationArea = all;
            }
            field("Sales Rebate Period"; "Sales Rebate Period")
            {
                ApplicationArea = all;
            }
            field("Voucher No."; "Voucher No.")
            {

            }
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = all;
            }
        }
    }
    actions
    {
        addafter(Post)
        {
            action(Post2)
            {
                ApplicationArea = all;
                Caption = 'Post-WHT';
                Image = Post;
                trigger OnAction()
                begin
                    CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post2", Rec);
                    CurrPage.UPDATE(FALSE);
                end;
            }
            action("Preview Posting")
            {
                ApplicationArea = all;
                Caption = 'Preview Posting-WHT';
                Image = ViewPostedOrder;
                trigger OnAction()
                var
                    GenJnlPost: Codeunit "Gen. Jnl.-Post2";
                begin
                    GenJnlPost.Preview(Rec);
                end;
            }
            action("Import From Excel")
            {
                ApplicationArea = all;
                Image = Import;
                RunObject = report "Import Sales JVS";
            }
            //B2BSPONSEPT12>>
            action("jndimension")
            {
                ApplicationArea = all;
                Caption = 'Update Dimensions';
                Image = UpdateDescription;
                trigger OnAction()
                var
                    JnlDime: Report JnDimension;
                begin
                    JnlDime.GetDefalValue("Journal Batch Name", "Journal Template Name", "Document No.");
                    JnlDime.run();

                end;

            }
            //B2BSPONSEPT12<<

        }
    }

    trigger OnNewRecord(BelowxRec: Boolean)
    BEGIN
        UserSetupGRec.GET(USERID);
        UserSetupGRec.TestField("Gen. Jouranl Line Resp. Centre");
        "Responsibility Center" := UserSetupGRec."Gen. Jouranl Line Resp. Centre";
    END;

    var
        UserSetupGRec: Record "User Setup";

}

