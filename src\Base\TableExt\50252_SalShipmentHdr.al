tableextension 50252 SalesShipHeaderExt extends "Sales Shipment Header"
{
    fields
    {
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            //B2B.P.K.T
        }
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;

        }
        field(50063; "Approval Status"; enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "POS Window"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //Fix06Jul2021>>
        field(50064; "Sales Area"; Code[20])
        {
            DataClassification = CustomerContent;
            Caption = 'Sales Office';//PKONJU7
            Editable = false;
        }
        //Fix06Jul2021<<
        field(65000; Total; Decimal)
        {
            Description = 'GJ_CHIPOS_RKD_141013';
        }

        field(65004; "POS Transaction Type"; Option)
        {
            Description = 'GJ_CHIPOS_RKD_181113';
            OptionCaption = ',Cash,Card,Both';
            OptionMembers = ,Cash,Card,Both;
        }
        field(65005; "POS Transaction No."; Code[18])
        {
            Description = 'GJ_CHIPOS_RKD_181113';
        }
        field(65006; "POS User ID"; Code[50])
        {
            Description = 'GJ_CHIPOS_RKD_181113,CHIUPG';
        }
        field(65007; "POS Card Amount"; Decimal)
        {
        }
        field(65008; "POS Cash Amount"; Decimal)
        {
        }
        field(65009; "Pos Bank Name"; Option)
        {
            //OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB"';//PKONDE16
            //OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB;//PKONDE16
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB",CORONATION,PROVIDUS';//PKONDE16
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB,CORONATION,PROVIDUS;//PKONDE16;

        }
        field(65011; "POS Account No."; Code[10])
        {

        }
        //B2BMS
        field(65012; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(65013; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(65014; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(65015; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
        field(65019; "Applied By"; code[50]) //PKON22AP28.2
        {
            Editable = false;
            DataClassification = CustomerContent;

        }

    }

    var

}