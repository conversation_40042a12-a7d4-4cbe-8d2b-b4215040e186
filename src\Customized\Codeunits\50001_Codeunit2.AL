codeunit 50001 Codeunit2
{
    trigger OnRun()
    begin
        //BlockVendors();
        BlockCustomers();
        CloseRetailSales()//PKON22AP4-CR220045
        /* GITExpiryMailAlert();
         BlockItems();*/
        //AutoShortClosePO();
        //CompExpiry();
    end;

    // AutoExpiry of CompanyInformation Credit Limit

    Procedure CompExpiry()
    var
        CompCreLimitSchedule: Record "Company Cr. Limit Schedule";
        CompCreLimitBudget: Record "Company Cr. Limit Budget";
    BEGIN
        CompCreLimitSchedule.reset;
        CompCreLimitSchedule.SetFilter(Status, '<>%1', CompCreLimitSchedule.Status::Expired);
        IF CompCreLimitSchedule.findset then
            repeat
                IF (CompCreLimitSchedule."Schedule Limit Expiry Date" <= today) then begin
                    CompCreLimitSchedule.Status := CompCreLimitSchedule.status::Expired;
                    CompCreLimitSchedule.modify();
                end;
            until CompCreLimitSchedule.next = 0;


        CompCreLimitBudget.reset;
        CompCreLimitBudget.SetFilter(Status, '<>%1', CompCreLimitBudget.Status::Expired);
        IF CompCreLimitBudget.findset then
            repeat
                IF (CompCreLimitBudget."Schedule Limit Expiry Date" <= today) then begin
                    CompCreLimitBudget.Status := CompCreLimitBudget.status::Expired;
                    CompCreLimitBudget.modify();
                end;
            until CompCreLimitBudget.next = 0;
    END;
    // AutoExpiry of CompanyInformation Credit Limit

    //COA
    procedure BlockCOA();
    var
        GLAccount: Record "G/L Account";
        GLEntry: Record "G/L Entry";
        GLSetup: Record "General Ledger Setup";

    begin
        GLAccount.Reset();
        GLAccount.SETRANGE(Blocked, false);
        GLSetup.Get();
        if GLAccount.FINDSET() then
            repeat
                IF FORMAT(GLSetup."COA Blocking Period") <> '' THEN BEGIN
                    GLEntry.RESET;
                    GLEntry.SetRange("G/L Account No.", GLAccount."No.");
                    IF GLEntry.FINDLAST THEN BEGIN
                        if (GLEntry."Posting Date" <> 0D) and
                           (GLAccount."Last Date Modified" < CALCDATE('-1M', TODAY)) and
                           (GLEntry."Posting Date" < CALCDATE(GLSetup."COA Blocking Period", TODAY)) then begin
                            GLAccount.Blocked := true;
                            GLAccount."Approval Status" := GLAccount."Approval Status"::Open;
                            GLAccount.MODIFY;
                        end;
                    end else begin
                        IF (GLAccount."Last Date Modified" < CALCDATE(GLSetup."COA Blocking Period", TODAY)) then begin
                            GLAccount.Blocked := true;
                            GLAccount."Approval Status" := GLAccount."Approval Status"::Open;
                            GLAccount.MODIFY;
                        end;
                    end;
                end;
            until GLAccount.NEXT = 0;
    end;



    //Vendor
    procedure BlockVendors();
    var
        Vendor: Record Vendor;
        VendLedEntry: Record "Vendor Ledger Entry";
        VendClass: Record "Vendor Classification";

    begin
        Vendor.SETRANGE(Blocked, Vendor.Blocked::" ");
        Vendor.SetFilter("Vendor Classification", '<>%1', '');
        if Vendor.FINDSET then
            repeat
                VendClass.GET(Vendor."Vendor Classification");
                IF FORMAT(VendClass."Blocking Period") <> '' THEN BEGIN
                    Vendor.CALCFIELDS("Last Transaction Date");
                    VendLedEntry.RESET;
                    VendLedEntry.SetCurrentKey("Vendor No.", "Posting Date");
                    VendLedEntry.SetRange("Vendor No.", Vendor."No.");
                    IF VendLedEntry.FINDLAST THEN BEGIN
                        if (VendLedEntry."Posting Date" <> 0D) and
                           (Vendor."Last Date Modified" < CALCDATE('-1M', TODAY)) and
                           (VendLedEntry."Posting Date" < CALCDATE(VendClass."Blocking Period", TODAY)) then begin
                            Vendor.Blocked := Vendor.Blocked::All;
                            vendor."Approval Status" := vendor."Approval Status"::Open;
                            Vendor.MODIFY;
                        end;
                    end else begin
                        IF (Vendor."Last Date Modified" < CALCDATE(VendClass."Blocking Period", TODAY)) then begin
                            Vendor.Blocked := Vendor.Blocked::All;
                            vendor."Approval Status" := vendor."Approval Status"::Open;
                            Vendor.MODIFY;
                        end;
                    end;
                end;
            until Vendor.NEXT = 0;
    end;
    ///Customer
    procedure BlockCustomers()
    var
        custposGrp: Record "Customer Posting Group";
        Customer: Record Customer;
        custLedEntry: Record "Cust. Ledger Entry";
    begin
        Customer.SETRANGE(Customer.Blocked, Customer.Blocked::" ");
        Customer.SETFILTER(Customer."Customer Posting Group", 'BRANCH|BRCUST|LOCAL|VANSALES|" "');
        IF Customer.FINDSET THEN
            REPEAT
                Customer.CALCFIELDS("Last Transaction Date");
                IF (Customer."Last Transaction Date" <> 0D) AND (Customer."Last Transaction Date" < CALCDATE('-18M', TODAY))
                   AND (Customer."Last Date Modified" < CALCDATE('-1M', TODAY)) THEN BEGIN
                    Customer.Blocked := Customer.Blocked::All;
                    Customer."Approval Status" := Customer."Approval Status"::Open;
                    Customer.MODIFY;
                END;
            UNTIL Customer.NEXT = 0;
    end;
    //Block purchase Request
    procedure BlockPurchaseRequests()
    var
        PurchReqHdr: Record "Purch. Req Header";
    begin
        PurchReqHdr.reset;
        PurchReqHdr.SetRange("Document Date", CALCDATE('-1W', TODAY));
        PurchReqHdr.SetRange("RFQ Number", '');
        IF PurchReqHdr.FINDSET then BEGIN
            PurchReqHdr.ModifyAll(Closed, true);
            PurchReqHdr.ModifyAll("Closed By", UserId);
            PurchReqHdr.ModifyAll("Closed Date", CurrentDateTime);
        end;
    end;

    procedure GITExpiryMailAlert()
    var
        Recipients: List of [Text];
        UsersLRec: Record User;
        LocationLRec: Record Location;
        SMTPMail: Codeunit "SMTP Mail";
        FromMail: Text[100];
        ToMail: Text[100];
        Subject: Text;
        PurchPaybleLRec: Record "Purchases & Payables Setup";
        SMTPSetup: Record "SMTP Mail Setup";
        GITInsLRec: Record "GIT Insurance";
        VendLRec: Record Vendor;
    BEGIN
        Subject := 'Reg: GIT Expiry Details';
        PurchPaybleLRec.get;
        SMTPSetup.get;
        FromMail := SMTPSetup."GIT Expiry Mail Alert ID";
        ToMail := PurchPaybleLRec."GIT To Mail Alert ID";
        Recipients.ADD(ToMail);
        SMTPMail.CreateMessage('ERP', FromMail, Recipients, Subject, '', TRUE);
        SMTPMail.AppendBody('Respected Sir/Madam,');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('Following List shown is GIT Expiry Vehicle No.s With in 7 days.');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('<table border="1" width="600" cellspacing="0" cellpadding="0">');
        SMTPMail.AppendBody('<tr>');
        SMTPMail.AppendBody('<th width="200">GIT No.</th>');
        SMTPMail.AppendBody('<th width="200">Vehicle No.</th>');
        SMTPMail.AppendBody('<th width="200">Expiry Date</th>');
        SMTPMail.AppendBody('<th width="200">Vendor No.</th>');
        SMTPMail.AppendBody('<th width="200">Vendor Name</th>');
        SMTPMail.AppendBody('</tr>');
        SMTPMail.AppendBody('<tr>');
        GITInsLRec.Reset();
        GITInsLRec.SetRange("Expiration Date", CalcDate('<-7D>', Today), Today);
        IF GITInsLRec.findset then
            repeat
                IF VendLRec.get(GITInsLRec."Vendor No.") THEN;
                SMTPMail.AppendBody('<td>' + GITInsLRec."GIT No." + '</td>');
                SMTPMail.AppendBody('<td>' + GITInsLRec."Vendor No." + '</td>');
                SMTPMail.AppendBody('<td>' + FORMAT(GITInsLRec."Expiration Date") + '</td>');
                SMTPMail.AppendBody('<td>' + GITInsLRec."Vendor No." + '</td>');
                SMTPMail.AppendBody('<td>' + VendLRec.Name + '</td>');
            until GITInsLRec.next = 0;
        SMTPMail.AppendBody('</tr>');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('Thanking You.');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('Regards,');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.AppendBody('CHI Team');
        SMTPMail.AppendBody('<BR><BR>');
        SMTPMail.Send;
        MESSAGE('Mail Send.');
    END;

    procedure BlockItems();
    var
        Item: Record Item;
        ILE: Record "Item Ledger Entry";
    begin
        Item.SETRANGE(Blocked, false);
        if Item.FINDSET then
            repeat
                ILE.Reset();
                ILE.SetCurrentKey("Item No.", "Posting Date");
                ILE.SetRange("Item No.", Item."No.");
                IF ILE.FindLast() THEN;
                Item.CALCFIELDS("Qty. on Purch. Order");
                if (Item."Item Category Code" in ['AUTOELEC', 'AUTOMECH', 'BLDMAT', 'CARPENTARY', 'CONS STORE', 'ELECTRIC', 'ENGSPARE', 'FILTERS']) or
                   (Item."Item Category Code" in ['FORKLIFT', 'FUEL', 'GASSPARES', 'GEARS', 'IT STOCK', 'LUBRICANT', 'PLUMBING', 'PROMO', 'SPARES']) or
                   (Item."Item Category Code" in ['TELCARD', 'WELDING']) then
                    if (ILE."Posting Date" <> 0D) and (ILE."Posting Date" < CALCDATE('-2Y', TODAY)) and
                       (Item."Last Date Modified" < CALCDATE('-1M', TODAY)) then begin
                        Item.Blocked := true;
                        Item.MODIFY;
                    end;
                if Item."Item Category Code" in ['FA'] then
                    if (ILE."Posting Date" <> 0D) and (ILE."Posting Date" < CALCDATE('-6M', TODAY))
                       and (Item."Last Date Modified" < CALCDATE('-1M', TODAY)) then begin
                        Item.Blocked := true;
                        Item.MODIFY;
                    end;
                if Item."Item Category Code" in ['FG', 'PM', 'RM'] then
                    if (ILE."Posting Date" <> 0D) and (ILE."Posting Date" < CALCDATE('-1Y', TODAY))
                       and (Item."Last Date Modified" < CALCDATE('-1M', TODAY)) then begin
                        Item.Blocked := true;
                        Item.MODIFY;
                    end;

                if Item."Item Category Code" in ['CARTONS', 'CONCENTRAT', 'FLAVOURS', 'TOOLS', 'TPMS', 'TYRES', 'WARSPARES'] then
                    if (ILE."Posting Date" <> 0D) and (ILE."Posting Date" < CALCDATE('-2Y', TODAY))
                       and (Item."Last Date Modified" < CALCDATE('-1M', TODAY)) then begin
                        Item.Blocked := true;
                        Item.MODIFY;
                    end;
            until Item.NEXT = 0;
    end;

    procedure AutoShortClosePO()
    Var
        PurchHdr: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        VendRec: Record Vendor;
        VendorType: Integer;
        WarehouseGRec: Record "Warehouse Receipt Header";
        WareHouseLineGrec: Record "Warehouse Receipt Line";
        WarehouseNo: Code[20];
        ShortClose: Boolean;
        PurchPayable: Record "Purchases & Payables Setup";
    BEGIN
        clear(ShortClose);
        PurchPayable.GET;
        PurchHdr.RESET;
        PurchHdr.Setfilter("Document Date", '<%1', CALCDATE(PurchPayable."PO Closing Period", TODAY));
        //PurchHdr.SetFilter("Expected Receipt Date", '<%1', Today);
        PurchHdr.SETRANGE("Document Type", PurchHdr."Document Type"::Order);
        PurchHdr.SETFILTER("Order Status", '<>%1|<>%2', PurchHdr."Order Status"::Cancelled, PurchHdr."Order Status"::"Short Closed");
        //PurchHdr.SetRange("No.", 'FLP000038');
        IF PurchHdr.FINDSET THEN
            REPEAT
                PurchaseLine.RESET;
                PurchaseLine.SETRANGE(PurchaseLine."Document Type", PurchaseLine."Document Type"::Order);
                PurchaseLine.SETFILTER(PurchaseLine."Document No.", PurchHdr."No.");
                IF PurchaseLine.FINDSET THEN
                    REPEAT

                        /*WareHouseLineGrec.Reset();
                        WareHouseLineGrec.SetRange("Source No.", PurchHdr."No.");
                        if WareHouseLineGrec.FindSet then begin
                            WarehouseNo := WareHouseLineGrec."No.";
                            WareHouseLineGrec.DeleteAll();
                            if WarehouseGRec.Get(WarehouseNo) then
                                WarehouseGRec.Delete();
                        end;*/

                        IF (PurchaseLine."Quantity Received" < PurchaseLine.Quantity) AND
                           (PurchaseLine."Quantity Received" <> 0) THEN BEGIN
                            PurchaseLine."Order Status" := PurchaseLine."Order Status"::"Short Closed";
                            PurchaseLine.MODIFY;
                            ShortClose := TRUE;
                        end;

                        IF (PurchaseLine."Quantity Received" < PurchaseLine.Quantity) AND
                            (PurchaseLine."Quantity Received" = 0) THEN BEGIN
                            PurchaseLine."Order Status" := PurchaseLine."Order Status"::Cancelled;
                            PurchaseLine.MODIFY;
                        end;
                    UNTIL PurchaseLine.NEXT = 0;
                IF ShortClose THEN
                    PurchHdr.VALIDATE("Order Status", PurchHdr."Order Status"::"Short Closed")
                ELSE
                    PurchHdr.VALIDATE("Order Status", PurchHdr."Order Status"::Cancelled);
                PurchHdr.MODIFY;

            UNTIL PurchHdr.NEXT = 0;

    END;

    Procedure CloseRetailSales()//PKON22AP4-CR220045
    var
        SalesHdr: Record "Sales Header";
        SalesLine: Record "Sales Line";
        ReservationEntries: Record "Reservation Entry";
    begin
        SalesHdr.Reset();
        SalesHdr.SetRange("POS Window", true);
        SalesHdr.SetFilter("Document Date", '<%1', WorkDate());
        SalesHdr.SetRange("Document Type", SalesHdr."Document Type"::Order);
        IF SalesHdr.Findset then BEGIN
            repeat
                SalesLine.Reset();
                SalesLine.SetRange("Document Type", SalesHdr."Document Type");
                SalesLine.SetRange("Document No.", saleshdr."No.");
                IF SalesLine.FINDSET then BEGIN
                    SalesLine.CalcSums("Quantity Shipped", "Quantity Invoiced");
                    IF (SalesLine."Quantity Shipped" = SalesLine."Quantity Invoiced") then BEGIN
                        SalesLine.Modifyall("Order Status", SalesLine."Order Status"::"Short Closed");
                        SalesLine.Modifyall(Quantity, SalesLine."Quantity Shipped");

                        ReservationEntries.Reset();
                        ReservationEntries.SetRange("Source Type", 37);
                        ReservationEntries.SetRange("Source Subtype", 1);
                        ReservationEntries.SetRange("Source ID", SalesLine."Document No.");
                        IF ReservationEntries.FindSet() then
                            ReservationEntries.DeleteAll();

                        SalesHdr."Order Status" := SalesHdr."Order Status"::"Short Closed";
                        SalesHdr.Modify;
                    end;
                end;
            until SalesHdr.Next = 0;
        end;
    end;
}
