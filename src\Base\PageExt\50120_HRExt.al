pageextension 50169 "HR Ext" extends "Human Resources Manager RC"
{
    // This page extension is created by B2BMSOn17Jan2022
    layout
    {
        // Add changes to page layout here
    }

    actions
    {
        //BaluonMar8 2022>>
        addafter(Group)
        {
            group("Approve Pages")
            {
                action("Request to Approve")
                {
                    ApplicationArea = all;
                    Caption = 'Request to Approves';
                    RunObject = page "Requests to Approve";
                }
                action("Approval Entries View")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries View';
                    RunObject = page "Approval Entries View Page";
                }
                action("Approval Entries")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries';
                    RunObject = page "Approval Entries";
                }
            }
        }//BaluonMar8 2022<<
        addafter(Group2)
        {
            group("Staff Vouchers")
            {
                action("Bank Payment Voucher List - Staff")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Bank Payment Voucher List Stf";
                }
                action("Approved Bank Payment Voucher List - Staff")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Apprved Bank Pmt Vchr List Stf";
                }
                action("Posted Bank Payment Voucher List - Staff")
                {
                    ApplicationArea = Planning;
                    RunObject = page "Posted Bank Pmt Vouch List Stf";
                }

            }
            group("Customized xml ports")
            {
                action("BPV Staff")
                {
                    ApplicationArea = planning;
                    RunObject = xmlport "BPV Staff";
                }
                action("BPV-GD12")//PKON22JA21
                {
                    ApplicationArea = planning;
                    RunObject = xmlport "BPV Staff GD12";
                }
            }
            group("Customized Reports") //PKON22JA24
            {
                action("Bank Confirmation FileCtion 2")
                {
                    ApplicationArea = planning;
                    RunObject = report "Bank Confirmation FileCtion 2";
                }
            }

        }
    }

    var
        myInt: Integer;
}