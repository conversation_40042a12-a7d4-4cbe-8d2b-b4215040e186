/// <summary>
/// Page Material Requisitions. (ID 50058).
/// </summary>
page 50996 "Material Requisitions List2"
{
    CardPageID = "Material Requisition2";
    Editable = false;
    PageType = List;
    SourceTable = MRSHeader;
    SourceTableView = SORTING("MRS No.")
                      ORDER(Ascending);
    //  WHERE(Status = FILTER(<> Released), Closed = filter(false));
    UsageCategory = Lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("MRS No."; "MRS No.")
                {
                    ApplicationArea = all;
                }
                field("Date of MRS"; "Date of MRS")
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    ApplicationArea = ALL;
                }
                field("Issued Date"; "Issued Date")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Total Amout(LCY)"; "Total Amout(LCY)")
                {
                    ApplicationArea = all;
                }
                field("Manual MRS No."; "Manual MRS No.")
                {
                    ApplicationArea = all;
                }/*
                field("Issue Dept."; "Issue Dept.")
                {
                    ApplicationArea = all;
                }
                field("Issue Bus. Unit"; "Issue Bus. Unit")
                {
                    ApplicationArea = all;
                }
                field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                }*/
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
            }

        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50004),
                                "No." = FIELD("MRS No.");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

}