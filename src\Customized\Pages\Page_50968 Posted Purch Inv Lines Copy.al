page 50968 "Posted Purch.Inv Lines-copy"
{
    Caption = 'Posted Purchase Inv Lines Copy';
    Editable = true;
    PageType = List;
    SourceTable = "Purch. Inv. Line";
    ApplicationArea = all;
    UsageCategory = Administration;
    Permissions = tabledata "Purch. Inv. Line" = RM;
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(Control1)
            {
                ShowCaption = false;
                field("Buy-from Vendor No."; "Buy-from Vendor No.")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = all;
                }

                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = all;
                }
                field("Posting Group"; "Posting Group")
                {
                    ApplicationArea = all;
                }
                field("Expected Receipt Date"; "Expected Receipt Date")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = all;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = all;
                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                    ApplicationArea = all;
                }
                field("Unit Cost (LCY)"; "Unit Cost (LCY)")
                {
                    ApplicationArea = all;
                }

                field(Amount; Amount)
                {
                    ApplicationArea = all;
                }
                field("Amount Including VAT"; "Amount Including VAT")
                {
                    ApplicationArea = all;
                }
                field("Actual Order Qty."; "Actual Order Qty.")
                {
                    ApplicationArea = all;
                }
                field("Sub Document Type"; "Sub Document Type")
                {
                    ApplicationArea = all;
                }
                field("Sub Document No."; "Sub Document No.")
                {
                    ApplicationArea = all;
                }

                field("PMS No."; "PMS No.")
                {
                    ApplicationArea = all;
                }
                field("Last Meter Reading"; "Last Meter Reading")
                {
                    ApplicationArea = all;
                }
                field("Current Meter Reading"; "Current Meter Reading")
                {
                    ApplicationArea = all;
                }
                field("Km Covered"; "Km Covered")
                {
                    ApplicationArea = all;
                }
                field("PMS Unit Price"; "PMS Unit Price")
                {
                    ApplicationArea = all;
                }
                field("Km per Ltr"; "Km per Ltr")
                {
                    ApplicationArea = all;
                }
                field("PMS Card No."; "PMS Card No.")
                {
                    ApplicationArea = all;
                }
                field("Old_PMS Card No."; "Old_PMS Card No.")
                {
                    ApplicationArea = all;
                }
                field("Date PMS Availed"; "Date PMS Availed")
                {
                    ApplicationArea = all;
                }
                field("Fuel Avail"; "Fuel Avail")
                {
                    ApplicationArea = all;
                }

                field("Capex No."; "Capex No.")
                {
                    ApplicationArea = all;
                }
                field("Capex Line No."; "Capex Line No.")
                {
                    ApplicationArea = all;
                }
                field("Budget Name"; "Budget Name")
                {
                    ApplicationArea = all;
                }
                field("FA Posting Group"; "FA Posting Group")
                {
                    ApplicationArea = all;
                }

                field("CWIP No."; "CWIP No.")
                {
                    ApplicationArea = all;
                }

                field("WHT Applicable"; "WHT Applicable")
                {
                    ApplicationArea = all;
                }
                //Service08Jul2021>>
                field("Service Code"; "Service Code")
                {
                    ApplicationArea = all;
                }
                //Service08Jul2021<<

                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = all;
                }
                field("Clearing File No."; "Clearing File No.")
                {
                    ApplicationArea = all;
                }
                field("No.2"; "No.2")
                {
                    ApplicationArea = all;
                }
                field("WHT Group"; "WHT Group")
                {
                    ApplicationArea = all;
                }
                field("WHT %"; "WHT %")
                {
                    ApplicationArea = all;

                }
                //FIX05Jun2021>>
                field("WHT Amount"; "WHT Amount")
                {
                    ApplicationArea = all;

                }
                field("WHT Amount 2"; "WHT Amount 2")
                {
                    ApplicationArea = all;

                }
                field("Posted Loading Slip No."; "Posted Loading Slip No.")
                {
                    ApplicationArea = all;

                }
                //FIX05Jun2021<<
                field("Pms Mapped"; "Pms Mapped")
                {
                    ApplicationArea = all;
                }
                //FIX05Jul2021>>
                field("Orginal WHT Amount"; "Orginal WHT Amount")
                {
                    ApplicationArea = all;


                }
                //FIX05Jul2021<<

            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}