page 50910 "Import File Subform2 Archive"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      HO      10-Sep-12      -> Form Created to Import File Archive Management.

    Caption = 'Import File subform Archive';
    //Editable = false;
    PageType = ListPart;
    SourceTable = "Purch. Rcpt. Line";
    SourceTableView = SORTING("Document No.", "Import File No.", Type)
                      WHERE(Type = CONST(Item));

    layout
    {
    }

    actions
    {
    }

    var
        ItemInvCost: Decimal;
        ValueEntry: Record "Value Entry";
        ItemChargeCost: Decimal;
        ItemChargeNo: Code[20];
        ItemTotalCharges: Decimal;
        TotalItemInvCost: Decimal;

    procedure UpdateMatrix();
    begin
    end;

    procedure DrillDownCharge();
    begin
    end;

    procedure DrillDownInvoiceCost();
    begin
    end;

    procedure DrillDownChargeTotal();
    begin
    end;
}

