report 50315 "13Wk import Report"
{
    ProcessingOnly = true;
    UsageCategory = None;
    Caption = '13Wk import Report';
    dataset
    {
    }
    requestpage
    {
        layout
        {
        }
        actions
        {
            area(Processing)
            {
            }
        }
        trigger OnQueryClosePage(CloseAction: Action): Boolean;
        begin
            IF CloseAction = ACTION::OK THEN BEGIN
                ServerFileNme := FileMgt.UploadFile(Text000, ExcelExtensionTok);
                IF ServerFileNme = '' THEN
                    EXIT(FALSE);

                SheetName := ExcelBuf.SelectSheetsName(ServerFileNme);
                IF SheetName = '' THEN
                    EXIT(FALSE);
            END;
        end;
    }

    trigger OnPreReport();
    begin
        lineNo := 1000;
        ExcelBuf.LOCKTABLE;
        ExcelBuf.OpenBook(ServerFileNme, SheetName);
        ExcelBuf.ReadSheet;
        GetLastRowandColumn;

        clearExistingLines();

        FOR X := 2 TO TotalRows DO
            InsertData(X);

        ExcelBuf.DELETEALL;
        MESSAGE('Import Completed');
    end;

    procedure GetLastRowandColumn();
    begin
        ExcelBuf.SETRANGE(ExcelBuf."Row No.", 1);
        TotalColumns := ExcelBuf.COUNT;

        ExcelBuf.RESET;
        IF ExcelBuf.FINDLAST THEN
            TotalRows := ExcelBuf."Row No.";
    end;

    procedure InsertData(RowNo: Integer);
    var
        PlanLine: Record "13Wk Plan Line";
    begin
        PlanLine.Init();
        PlanLine.Validate("Document No", PlanCode);
        PlanLine."Item No." := GetValueAtCell(RowNo, 1);
        PlanLine.Validate("Start Date", StartDate);
        PlanLine.Validate("End Date", EndDate);
        PlanLine."Line No." := lineNo;
        PlanLine.Insert();
        Evaluate(PlanLine."Week 1", GetValueAtCell(RowNo, 2));
        Evaluate(PlanLine."Week 2", GetValueAtCell(RowNo, 3));
        Evaluate(PlanLine."Week 3", GetValueAtCell(RowNo, 4));
        Evaluate(PlanLine."Week 4", GetValueAtCell(RowNo, 5));
        Evaluate(PlanLine."Week 5", GetValueAtCell(RowNo, 6));
        Evaluate(PlanLine."Week 6", GetValueAtCell(RowNo, 7));
        Evaluate(PlanLine."Week 7", GetValueAtCell(RowNo, 8));
        Evaluate(PlanLine."Week 8", GetValueAtCell(RowNo, 9));
        Evaluate(PlanLine."Week 9", GetValueAtCell(RowNo, 10));
        Evaluate(PlanLine."Week 10", GetValueAtCell(RowNo, 11));
        Evaluate(PlanLine."Week 11", GetValueAtCell(RowNo, 12));
        Evaluate(PlanLine."Week 12", GetValueAtCell(RowNo, 13));
        Evaluate(PlanLine."Week 13", GetValueAtCell(RowNo, 14));
        PlanLine.Modify();
        lineNo += 1000;
    end;

    local procedure clearExistingLines()
    var
        PlanLine: record "13Wk Plan Line";
        PlanHeader: Record "13Wk Header";
    begin
        PlanHeader.Get(PlanCode);
        PlanHeader.TestStatusOpen();

        PlanLine.SetRange("Start Date", StartDate);
        PlanLine.SetFilter("Document No", '=%1', PlanCode);
        if PlanLine.FindFirst() then PlanLine.DeleteAll();
    end;

    procedure SetParameters(PLCode: Code[20]; SDate: Date; EDate: Date)
    begin
        PlanCode := PLCode;
        StartDate := SDate;
        EndDate := EDate;
    end;

    procedure GetValueAtCell(RowNo: Integer; ColNo: Integer): Text;
    begin
        IF ExcelBuf1.GET(RowNo, ColNo) THEN
            EXIT(ExcelBuf1."Cell Value as Text");
    end;

    var
        lineNo: Integer;
        StartDate: Date;
        EndDate: Date;
        PlanCode: Code[20];
        ExcelBuf: Record "Excel Buffer";
        ServerFileNme: Text[250];
        SheetName: Text[250];
        TotalColumns: Integer;
        TotalRows: Integer;
        X: Integer;
        ExcelBuf1: Record "Excel Buffer";
        FileMgt: Codeunit "File Management";
        Text000: Label 'Import Data';
        ExcelExtensionTok: Text[250];
        DocumentNumber: code[20];
}
