
tableextension 50040 MaintanenceLedger extends "Maintenance Ledger Entry"
{
    fields
    {
        field(50011; "Current Meter Reading"; decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50010; "Last Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Old_PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50019; "Date PMS Availed_"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Last Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50023; Allocated; Boolean)
        {
            Description = 'CTC';
            DataClassification = CustomerContent;
        }
        field(50024; "Description 2"; Text[250])
        {
            Description = 'CTC';
            DataClassification = CustomerContent;
        }
        field(50027; "Item No."; Code[20])
        {
            CalcFormula = Lookup ("Value Entry"."Item No." WHERE("Entry No." = FIELD("Value Entry No.")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(50028; "Value Entry No."; Integer)
        {
            CalcFormula = Lookup ("G/L - Item Ledger Relation"."Value Entry No." WHERE("G/L Entry No." = FIELD("G/L Entry No.")));
            FieldClass = FlowField;
        }
        field(50030; "New Last KM Reading"; Decimal)//PKONAU27
        {
            DataClassification = CustomerContent;
        }
        field(50031; "New Current KM Reading"; Decimal)//PKONAU27
        {
            DataClassification = CustomerContent;
        }

    }

    var
        myInt: Integer;
}