//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
page 50743 "Purchase Document Modifiers"
{
    Caption = 'Purchase Document Modifiers';
    ApplicationArea = All;
    SourceTable = "Purchase Doc Modifier";
    CardPageId = "Purchase Doc Modifier";
    SourceTableView = where(Status = filter(Open | "Pending Approval"));
    PageType = List;
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field("Record Type"; "Record Type")
                {
                    ApplicationArea = All;
                }
                field("Document type"; "Document type")
                {
                    ApplicationArea = All;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;
                }
                field("Vendor No."; "Vendor No.")
                {
                    ApplicationArea = All;
                }
                field("Vendor Name"; "Vendor Name")
                {
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    ApplicationArea = All;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    Visible = false;
                    ApplicationArea = All;
                }
            }
        }
    }

    trigger OnOpenPage()
    var
        "Workflow Request Page Handling": Codeunit "PMOD Workflow Rqst Pg Handling";
    begin
        "Workflow Request Page Handling".CreateEntitiesAndFields();
    end;
}
//>>>>>> G2S CAS-01334-J2M7C2 8/30/2024
