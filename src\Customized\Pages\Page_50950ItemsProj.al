page 50949 "Item Projection List"
{
    PageType = List;
    //ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = Item;
    CardPageId = "Item Card";
    Editable = false;
    SourceTableView = where("Item Category Code" = filter('FG'));

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                    ApplicationArea = All;


                }
                field(Description; Description)
                {
                    ApplicationArea = All;

                }
                field("Base Unit of Measure"; "Base Unit of Measure")
                {
                    ApplicationArea = All;

                }
                field(ItemInventory; itemAv.Inventory)
                {
                    ApplicationArea = Location;
                    Caption = 'Inventory';
                    DecimalPlaces = 0 : 5;
                    DrillDown = true;
                    Editable = false;
                    ToolTip = 'Specifies the inventory level of an item.';

                    trigger OnDrillDown()
                    begin
                        SetItemFilter;
                        ItemAvailFormsMgt.ShowItemLedgerEntries(itemAv, false);
                    end;
                }
                field(ProjAvailableBalance; ProjAvailableBalance)
                {
                    ApplicationArea = Location;
                    Caption = 'Projected Available Balance';
                    DecimalPlaces = 0 : 5;
                    ToolTip = 'Specifies the item''s availability. This quantity includes all known supply and demand but does not include anticipated demand from demand forecasts or blanket sales orders or suggested supplies from planning or requisition worksheets.';
                    Editable = false;
                    trigger OnDrillDown()
                    begin
                        ShowItemAvailLineList(4);
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetItemFilter();
        CalcAvailQuantities(
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, QtyAvailable);
    end;

    local procedure CalcAvailQuantities(var GrossRequirement: Decimal; var PlannedOrderRcpt: Decimal; var ScheduledRcpt: Decimal; var PlannedOrderReleases: Decimal; var ProjAvailableBalance: Decimal; var ExpectedInventory: Decimal; var AvailableInventory: Decimal)
    var
        DummyQtyAvailable: Decimal;
    begin
        SetItemFilter;
        ItemAvailFormsMgt.CalcAvailQuantities(
          itemAv, AmountType = AmountType::"Balance at Date",
          GrossRequirement, PlannedOrderRcpt, ScheduledRcpt,
          PlannedOrderReleases, ProjAvailableBalance, ExpectedInventory, DummyQtyAvailable, AvailableInventory);
    end;

    local procedure SetItemFilter()
    begin
        itemAv.RESEt;
        itemAv.SetRange("No.", "No.");
        itemAv.SetRange("Date Filter", 0D, WorkDate());
        IF Locf <> '' then
            itemAv.SetRange("Location Filter", Locf);
        IF itemAv.findfirst then
            itemAv.CalcFields(Inventory);
    end;

    local procedure ShowItemAvailLineList(What: Integer)
    begin
        SetItemFilter;
        ItemAvailFormsMgt.ShowItemAvailLineList(itemAv, What);
    end;

    procedure getloc(loc: Text[200])
    begin
        Locf := loc;
    end;

    var
        itemAv: Record Item;
        ItemAvailFormsMgt: Codeunit "Item Availability Forms Mgt";
        PlannedOrderReleases: Decimal;
        GrossRequirement: Decimal;
        PlannedOrderRcpt: Decimal;
        ScheduledRcpt: Decimal;
        ProjAvailableBalance: Decimal;
        PeriodStart: Date;
        PeriodEnd: Date;
        AmountType: Option "Net Change","Balance at Date";
        ExpectedInventory: Decimal;
        QtyAvailable: Decimal;
        Locf: Text[200];
}