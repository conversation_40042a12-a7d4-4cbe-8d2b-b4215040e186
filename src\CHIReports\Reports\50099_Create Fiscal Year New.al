Report 50099 "Create Fiscal Year New"
{
    Caption = 'Create Fiscal Year New_50099';
    ProcessingOnly = true;
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    dataset
    {
    }

    requestpage
    {
        SaveValues = true;

        layout
        {
            area(content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(StartingDate; FiscalYearStartDate)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Starting Date';
                        ToolTip = 'Specifies the date from which the report or batch job processes information.';
                    }
                    field(NoOfPeriods; NoOfPeriods)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'No. of Periods';
                        ToolTip = 'Specifies how many accounting periods to include.';
                    }
                    field(PeriodLength; PeriodLength)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Period Length';
                        ToolTip = 'Specifies the period for which data is shown in the report. For example, enter "1M" for one month, "30D" for thirty days, "3Q" for three quarters, or "5Y" for five years.';
                    }
                }
            }
        }

        actions
        {
        }

        trigger OnOpenPage()
        begin
            if NoOfPeriods = 0 then begin
                NoOfPeriods := 12;
                Evaluate(PeriodLength, '<1M>');
            end;
            if AccountingPeriod.Find('+') then
                FiscalYearStartDate := AccountingPeriod."Starting Date";
        end;
    }

    labels
    {
    }

    trigger OnPreReport()
    var
        ConfirmManagement: Codeunit "Confirm Management";
    begin
        //CRF:2020-0041  NYO  31-12-2020 >>
        CurrYr := DATE2DMY(FiscalYearStartDate, 3);
        //CRF:2020-0041  NYO  31-12-2020 <<
        AccountingPeriod."Starting Date" := FiscalYearStartDate;
        AccountingPeriod.TestField("Starting Date");

        AccountingPeriod.SetRange(Closed, false);
        if AccountingPeriod.Find('-') then begin
            FirstPeriodStartDate := AccountingPeriod."Starting Date";
            FirstPeriodLocked := AccountingPeriod."Date Locked";
            if (not HideDialog) and (FiscalYearStartDate < FirstPeriodStartDate) and FirstPeriodLocked then
                if not ConfirmManagement.GetResponseOrDefault(CreateAndCloseQst, false) then
                    exit;
        end else
            if not HideDialog then
                if not ConfirmManagement.GetResponseOrDefault(CreateQst, false) then
                    exit;

        AccountingPeriod.SetRange(Closed);
        FiscalYearStartDate2 := FiscalYearStartDate;
        Create445acctperiods2();

        AccountingPeriod.Get(FiscalYearStartDate2);
        AccountingPeriod.UpdateAvgItems;
    end;

    var
        CreateAndCloseQst: Label 'The new fiscal year begins before an existing fiscal year, so the new year will be closed automatically.\\Do you want to create and close the fiscal year?';
        CreateQst: Label 'After you create the new fiscal year, you cannot change its starting date.\\Do you want to create the fiscal year?';
        AccountingPeriod: Record "Accounting Period";
        InvtSetup: Record "Inventory Setup";
        NoOfPeriods: Integer;
        PeriodLength: DateFormula;
        FiscalYearStartDate: Date;
        FiscalYearStartDate2: Date;
        FirstPeriodStartDate: Date;
        FirstPeriodLocked: Boolean;
        i: Integer;
        HideDialog: Boolean;
        CurrYr: Integer;

    procedure InitializeRequest(NewNoOfPeriods: Integer; NewPeriodLength: DateFormula; StartingDate: Date)
    begin
        NoOfPeriods := NewNoOfPeriods;
        PeriodLength := NewPeriodLength;
        if AccountingPeriod.FindLast then
            FiscalYearStartDate := AccountingPeriod."Starting Date"
        else
            FiscalYearStartDate := StartingDate;
    end;

    procedure HideConfirmationDialog(NewHideDialog: Boolean)
    begin
        HideDialog := NewHideDialog;
    end;

    PROCEDURE Create445acctperiods2();
    VAR
        DateRec: Record Date;
        N: Integer;
        NoofFridays: Integer;
        FiscalYearStartDate3: Date;
        InvPeriods: Record "Inventory Period";
    BEGIN
        FOR i := 1 TO NoOfPeriods + 1 DO BEGIN
            IF (FiscalYearStartDate <= FirstPeriodStartDate) AND (i = NoOfPeriods + 1) THEN
                EXIT;

            AccountingPeriod.INIT;
            AccountingPeriod."Starting Date" := FiscalYearStartDate;
            AccountingPeriod.VALIDATE("Starting Date");

            CASE i OF
                1:
                    AccountingPeriod.Name := 'January';
                2:
                    AccountingPeriod.Name := 'February';
                3:
                    AccountingPeriod.Name := 'March';
                4:
                    AccountingPeriod.Name := 'April';
                5:
                    AccountingPeriod.Name := 'May';
                6:
                    AccountingPeriod.Name := 'June';
                7:
                    AccountingPeriod.Name := 'July';
                8:
                    AccountingPeriod.Name := 'August';
                9:
                    AccountingPeriod.Name := 'September';
                10:
                    AccountingPeriod.Name := 'October';
                11:
                    AccountingPeriod.Name := 'November';
                12:
                    AccountingPeriod.Name := 'December';
                13:
                    AccountingPeriod.Name := 'January';
            END;

            IF (i = 1) OR (i = NoOfPeriods + 1) THEN BEGIN
                AccountingPeriod."New Fiscal Year" := TRUE;
                InvtSetup.GET;
                AccountingPeriod."Average Cost Calc. Type" := InvtSetup."Average Cost Calc. Type";
                AccountingPeriod."Average Cost Period" := InvtSetup."Average Cost Period";
            END;
            IF (FirstPeriodStartDate = 0D) AND (i = 1) THEN
                AccountingPeriod."Date Locked" := TRUE;
            IF (AccountingPeriod."Starting Date" < FirstPeriodStartDate) AND FirstPeriodLocked THEN BEGIN
                AccountingPeriod.Closed := TRUE;
                AccountingPeriod."Date Locked" := TRUE;
            END;
            IF NOT AccountingPeriod.FIND('=') THEN
                AccountingPeriod.INSERT;
            //comment baser
            //FiscalYearStartDate := CALCDATE(PeriodLength,FiscalYearStartDate);

            IF AccountingPeriod.Name = 'January' THEN BEGIN
                DateRec.RESET;
                DateRec.SETFILTER("Period Type", '%1', 0);
                DateRec.SETFILTER(DateRec."Period Start", '>=%1', FiscalYearStartDate);
                DateRec.SETFILTER("Period No.", '%1', 5);
                IF DateRec.FINDFIRST THEN
                    IF DateRec."Period Start" <= DMY2DATE(2, 1, (DATE2DMY(FiscalYearStartDate, 3))) THEN
                        FiscalYearStartDate := CALCDATE('+1W', DateRec."Period Start");
            END;


            //based on starting date count 4 / 5 fridays and add 1 day to find next start date
            IF i <= 13 THEN BEGIN
                IF i IN [1, 2, 4, 5, 7, 8, 10, 11, 13] THEN
                    NoofFridays := 4 ELSE
                    NoofFridays := 5;
                N := 0;
                DateRec.RESET;
                DateRec.SETFILTER("Period Type", '%1', 0);
                DateRec.SETFILTER(DateRec."Period Start", '>=%1', FiscalYearStartDate);
                DateRec.SETFILTER("Period No.", '%1', 5);
                IF DateRec.FINDFIRST THEN
                    REPEAT
                        N += 1;
                        IF N = NoofFridays THEN BEGIN
                            FiscalYearStartDate3 := DateRec."Period Start" + 1;
                            //assigning month ending date to inventory period
                            InvPeriods.INIT;
                            InvPeriods.VALIDATE("Ending Date", DateRec."Period Start");
                            IF AccountingPeriod.Name = 'December' THEN BEGIN
                                InvPeriods.VALIDATE("Ending Date", DMY2DATE(31, 12, CurrYr));
                                FiscalYearStartDate3 := InvPeriods."Ending Date" + 1;
                            END;
                            InvPeriods.Name := AccountingPeriod.Name;
                            IF InvPeriods.INSERT THEN;
                            DateRec.FINDLAST;
                        END;
                    UNTIL DateRec.NEXT = 0; // NoofFridays;
                FiscalYearStartDate := FiscalYearStartDate3;
            END ELSE
                FiscalYearStartDate := DMY2DATE(1, 1, (DATE2DMY(FiscalYearStartDate3, 3) + 1));
        END;
    END;

}

