page 50414 "Bank Acc. Reconciliation (C)"
{

    Caption = 'Bank Acc. Reconciliation (C)';
    PageType = Document;
    PromotedActionCategories = 'New,Process,Report,Bank,Matching,Posting';
    SaveValues = false;
    SourceTable = "Bank Acc. Reconciliation";
    SourceTableView = WHERE("Statement Type" = CONST("Bank Reconciliation"));
    UsageCategory = Administration;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field(BankAccountNo; "Bank Account No.")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Bank Account No.';
                    ToolTip = 'Specifies the number of the bank account that you want to reconcile with the bank''s statement.';
                }
                field(StatementNo; "Statement No.")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Statement No.';
                    ToolTip = 'Specifies the number of the bank account statement.';
                }
                field(StatementDate; "Statement Date")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Statement Date';
                    ToolTip = 'Specifies the date on the bank account statement.';
                }
                field(BalanceLastStatement; "Balance Last Statement")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Balance Last Statement';
                    ToolTip = 'Specifies the ending balance shown on the last bank statement, which was used in the last posted bank reconciliation for this bank account.';
                }
                field(StatementEndingBalance; "Statement Ending Balance")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Statement Ending Balance';
                    ToolTip = 'Specifies the ending balance shown on the bank''s statement that you want to reconcile with the bank account.';
                }
            }
            group(OutstandingAmount)
            {
                field("Debit Balance Navision"; "Out. Debit Balance Navision")
                {
                    ApplicationArea = ALL;
                }
                field("Credit Balance Navision"; "Out. Credit Balance Navision")
                {
                    ApplicationArea = ALL;
                }

                field("Debit Balance Nav (LCY)"; "Out. Debit Balance Nav (LCY)")
                {
                    ApplicationArea = ALL;
                }
                field("Credit Balance Nav (LCY)"; "Out. Credit Balance Nav (LCY)")
                {
                    ApplicationArea = ALL;
                }

                field("Debit Balance Statement"; "Out. Debit Balance Statement")
                {
                    ApplicationArea = ALL;
                }

                field("Credit Balance Statement"; "Out. Credit Balance Statement")
                {
                    ApplicationArea = ALL;
                }

                field("Opening Balance"; "Opening Balance")
                {
                    ApplicationArea = ALL;
                }
                field("Closing Balance"; "Closing Balance")
                {
                    ApplicationArea = ALL;
                }
            }
            group(Control8)
            {
                ShowCaption = false;
                part(StmtLine; "Bank Acc. Reconciliation Lines")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Bank Statement Lines';
                    SubPageLink = "Bank Account No." = FIELD("Bank Account No."),
                                  "Statement No." = FIELD("Statement No.");
                }
                part(ApplyBankLedgerEntries; "Apply Bank Acc. Ledger Entries")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Bank Account Ledger Entries';
                    SubPageLink = "Bank Account No." = FIELD("Bank Account No."),
                                  Open = CONST(true),
                                  "Statement Status" = FILTER(Open | "Bank Acc. Entry Applied" | "Check Entry Applied"),
                                  Reversed = FILTER(false);
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
                Visible = false;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
                Visible = false;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Recon.")
            {
                Caption = '&Recon.';
                Image = BankAccountRec;
                action("&Card")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = '&Card';
                    Image = EditLines;
                    RunObject = Page "Bank Account Card";
                    RunPageLink = "No." = FIELD("Bank Account No.");
                    ShortCutKey = 'Shift+F7';
                    ToolTip = 'View or change detailed information about the record that is being processed on the journal line.';
                }
            }
        }
        area(processing)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Image = "Action";
                action("Delete Right Side All data")
                {
                    trigger OnAction()
                    var
                        UserSetup: Record "User Setup";
                        recOriginalBnk: Record "Original Bank Statement";
                    BEGIN
                        UserSetup.GET(USERID);
                        IF NOT UserSetup."Delete RBS Right side data" THEN
                            ERROR('You dont have the permission to delete the data from right side ! Contact you system administrator');


                        IF CONFIRM('Do you want to delete all the entries from right side', FALSE) THEN BEGIN
                            recOriginalBnk.SETRANGE(recOriginalBnk.Code, "Bank Account No.");
                            recOriginalBnk.SETRANGE(recOriginalBnk."Statement No.", "Statement No.");
                            IF recOriginalBnk.FINDFIRST THEN
                                REPEAT
                                    recOriginalBnk.DELETE;
                                UNTIL recOriginalBnk.NEXT = 0;
                        END;

                    END;
                }
                action(SuggestLines)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Suggest Lines';
                    Ellipsis = true;
                    //PhaniFeb182021>>
                    Visible = FALSE;
                    //PhaniFeb182021<<
                    Image = SuggestLines;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Create bank account ledger entries suggestions and enter them automatically.';

                    trigger OnAction()
                    begin
                        SuggestBankAccStatement.SetStmt(Rec);
                        SuggestBankAccStatement.RunModal;
                        Clear(SuggestBankAccStatement);
                    end;
                }
                action(CancelNavisionData)
                {
                    trigger OnAction()
                    var
                        NavBnkStatement: Record "Bank Acc. Reconciliation Line";
                        Total: Decimal;
                        NavBnkStatementMaxCancelNo: Record "Bank Acc. Reconciliation Line";
                        LastNoInternal: Integer;
                    BEGIN
                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        CurrPage.StmtLine.Page.SetTableView(NavBnkStatement);
                        IF NavBnkStatement.FINDSET THEN
                            REPEAT
                                Total += NavBnkStatement."Statement Amount";
                            UNTIL NavBnkStatement.NEXT = 0;

                        IF Total = 0 THEN BEGIN
                            NavBnkStatementMaxCancelNo.RESET;
                            NavBnkStatementMaxCancelNo.SETCURRENTKEY("Cancel No");
                            NavBnkStatementMaxCancelNo.SETRANGE("Bank Account No.", "Bank Account No.");
                            NavBnkStatementMaxCancelNo.SETRANGE("Statement No.", "Statement No.");
                            IF NavBnkStatementMaxCancelNo.FINDLAST THEN
                                LastNoInternal := NavBnkStatementMaxCancelNo."Cancel No";
                        END ELSE BEGIN
                            ERROR('Debit amount and Credit amount is not matching for the selected lines');
                        END;

                        NavBnkStatement.MODIFYALL(NavBnkStatement."Cancel No", LastNoInternal + 1);
                        NavBnkStatement.MODIFYALL("Matching Status", NavBnkStatement."Matching Status"::Cancel)

                    END;
                }
                action(MapManually)
                {
                    trigger OnAction()
                    var
                        MatchLineNo: Integer;
                        LeftSideTotal: Integer;
                        BnkReconLine: Record "Bank Acc. Reconciliation Line";
                        OgnlBnkStatementUpdate: REcord "Bank Acc. Reconciliation Line";
                        OgnlBnkStatement: Record "Original Bank Statement";
                        BnkReconLineUpdate: Record "Bank Acc. Reconciliation Line";
                        "RightSideDr.Total": Decimal;
                        "RightSideCr.Total": Decimal;
                    BEGIN
                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        /*CurrForm.StmtLine.FORM.SelectedEntries(BnkReconLine);
                        CurrForm.BankStatementLine.FORM.SelectedEntries(OgnlBnkStatement);*/
                        //As PEr 2009
                        MatchLineNo := LastMatchLine();
                        MatchLineNo := MatchLineNo + 1;
                        BnkReconLineUpdate.COPY(BnkReconLine);
                        OgnlBnkStatementUpdate.COPY(OgnlBnkStatement);
                        // Total for the left side
                        CLEAR(LeftSideTotal);
                        IF BnkReconLine.FINDFIRST THEN
                            REPEAT
                                LeftSideTotal += BnkReconLine."Statement Amount";
                                IF BnkReconLine."Matching Status" <> BnkReconLine."Matching Status"::"Not Matching" THEN  // exact Line checks
                                    ERROR('There is EXACT or PROBABAL or Cancel Line exists, Please unmatch that line and do manual matching ');
                            UNTIL BnkReconLine.NEXT = 0;

                        // Total for the right side
                        CLEAR("RightSideDr.Total");
                        CLEAR("RightSideCr.Total");
                        //OgnlBnkStatement.SETCURRENTKEY(Code);
                        IF OgnlBnkStatement.FINDFIRST THEN
                            REPEAT
                                "RightSideDr.Total" += OgnlBnkStatement."Bank Debit Amount";
                                "RightSideCr.Total" += OgnlBnkStatement."Bank Credit Amount";
                                IF OgnlBnkStatement."Matching Status" <> BnkReconLine."Matching Status"::"Not Matching" THEN  // exact Line checks
                                    ERROR('There is EXACT or PROBABAL or Cancel Line exists, Please unmatch that line and do manual matching ');
                            UNTIL OgnlBnkStatement.NEXT = 0;

                        IF LeftSideTotal = "RightSideCr.Total" - "RightSideDr.Total" THEN BEGIN
                            BnkReconLineUpdate.MODIFYALL("MatchLine No", MatchLineNo);
                            BnkReconLineUpdate.MODIFYALL("Cross Matching", TRUE);
                            BnkReconLineUpdate.MODIFYALL("Matching Status", BnkReconLineUpdate."Matching Status"::Exact);
                            BnkReconLineUpdate.MODIFYALL("MatchLine No", MatchLineNo);

                            //BnkReconLineUpdate."MatchLine No":=MatchLineNo;
                            // BnkReconLineUpdate.MODIFY;
                            //Update original Original Bank statement Table
                            OgnlBnkStatement.MODIFYALL("MatchLine No", MatchLineNo);
                            OgnlBnkStatement.MODIFYALL(Matching, TRUE);
                            OgnlBnkStatement.MODIFYALL("Matching Status", OgnlBnkStatementUpdate."Matching Status"::Exact);
                            OgnlBnkStatement.MODIFYALL("Manually Mapped", TRUE);
                            OgnlBnkStatement.MODIFYALL("Manually Mapped By", USERID);
                            OgnlBnkStatement.MODIFYALL("Manually Mapped on", CURRENTDATETIME);
                        END ELSE
                            ERROR('Amount is not matching for both the sides, please check amount and select manual match.');
                    END;
                }
                action(CancelBankData)
                {
                    trigger OnAction()
                    var
                        OgnlBnkStatement: Record "Original Bank Statement";
                        DebitAmount: Decimal;
                        CreditAmount: Decimal;
                        OgnlBnkStatementMaxCancelNo: Record "Original Bank Statement";
                        LastNoInternal: integer;
                    BEGIN

                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        //CurrPage.StmtLine.PAGE.GetSelectedRecords(OgnlBnkStatement);//as per 2009
                        IF OgnlBnkStatement.FINDSET THEN
                            REPEAT
                                DebitAmount += OgnlBnkStatement."Bank Debit Amount";
                                CreditAmount += OgnlBnkStatement."Bank Credit Amount";
                            UNTIL OgnlBnkStatement.NEXT = 0;

                        IF DebitAmount = CreditAmount THEN BEGIN
                            OgnlBnkStatementMaxCancelNo.RESET;
                            OgnlBnkStatementMaxCancelNo.SETCURRENTKEY("Cancel No");
                            OgnlBnkStatementMaxCancelNo.SETRANGE(Code, "Bank Account No.");
                            OgnlBnkStatementMaxCancelNo.SETRANGE("Statement No.", "Statement No.");
                            IF OgnlBnkStatementMaxCancelNo.FINDLAST THEN
                                LastNoInternal := OgnlBnkStatementMaxCancelNo."Cancel No";
                        END ELSE BEGIN
                            ERROR('Debit amount and Credit amount is not matching for the selected lines');
                        END;

                        OgnlBnkStatement.MODIFYALL(OgnlBnkStatement."Cancel No", LastNoInternal + 1);
                        OgnlBnkStatement.MODIFYALL("Matching Status", OgnlBnkStatement."Matching Status"::Cancel)
                    END;

                }
                action("Transfer to General Journal")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Transfer to General Journal';
                    Ellipsis = true;
                    Image = TransferToGeneralJournal;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Transfer the lines from the current window to the general journal.';

                    trigger OnAction()
                    begin
                        TransferToGLJnl.SetBankAccRecon(Rec);
                        TransferToGLJnl.Run;
                    end;
                }
                action(BankReconSummary)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Bank Reconciliation';
                    Ellipsis = true;
                    Image = BankAccountRec;

                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;

                    trigger OnAction()
                    Var
                        ReportBank: Report "Bank Recon Summary";
                    begin
                        ReportBank.SetData("Bank Account No.", "Statement No.", "Statement Date", "Statement Ending Balance");
                        ReportBank.RUNMODAL;
                    end;

                }
            }
            group("Ba&nk")
            {
                Caption = 'Ba&nk';

                action(ImportBankStatement)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Import Bank Statement';
                    Image = Import;
                    Promoted = true;
                    PromotedCategory = Category4;
                    PromotedIsBig = true;
                    ToolTip = 'Import electronic bank statements from your bank to populate with data about actual bank transactions.';

                    trigger OnAction()
                    begin
                        CurrPage.Update;
                        ImportBankStatement;
                    end;
                }


            }
            group("M&atching")
            {
                Caption = 'M&atching';
                action(MatchAutomatically)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Match Automatically';
                    Image = MapAccounts;
                    Promoted = true;
                    PromotedCategory = Category5;
                    PromotedIsBig = true;
                    ToolTip = 'Automatically search for and match bank statement lines.';

                    trigger OnAction()
                    begin
                        SetRange("Statement Type", "Statement Type");
                        SetRange("Bank Account No.", "Bank Account No.");
                        SetRange("Statement No.", "Statement No.");
                        REPORT.Run(REPORT::"Match Bank Entries", true, true, Rec);
                    end;
                }

                action(MatchSheets)
                {

                    trigger OnAction()
                    var
                        ReconLine: record "Bank Acc. Reconciliation Line";
                        Dialog1: Dialog;
                        Text01: label 'Checking for Matches!!!';
                        CountV: integer;
                        t: integer;
                        MatchLineNo: integer;
                        Flag: integer;
                        AreaRec: Record "Original Bank Statement";
                        CheqAndDcoNoMath: Boolean;
                    BEGIN

                        TESTFIELD("Outstanding Entry Downloaded", TRUE);
                        IF CONFIRM('Do you want to run automatic match ', FALSE) THEN BEGIN
                            ReconLine.RESET;
                            ReconLine.SETCURRENTKEY("Bank Account No.", "Statement No.", "Statement Line No.");
                            ReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
                            ReconLine.SETRANGE("Cross Matching", FALSE);
                            ReconLine.SETRANGE("Statement No.", "Statement No.");
                            IF ReconLine.FINDFIRST THEN BEGIN
                                Dialog1.OPEN(Text01 +
                                            '@1@@@@@@@@@@@@@@@@@@@@@@@');
                                CountV := ReconLine.COUNT;
                                t := 0;
                                MatchLineNo := LastMatchLine();
                                MatchLineNo := MatchLineNo + 1;

                                REPEAT

                                    t := t + 1;
                                    Flag := 2; //added by sanjoy ********
                                    Dialog1.UPDATE(1, ROUND(t * 10000 / CountV, 1));
                                    AreaRec.RESET;
                                    AreaRec.SETCURRENTKEY(Code, "Statement No.", "Line No.");
                                    AreaRec.SETRANGE(Code, ReconLine."Bank Account No.");
                                    AreaRec.SETRANGE("Statement No.", ReconLine."Statement No.");
                                    AreaRec.SETRANGE(Matching, FALSE);
                                    AreaRec.SETRANGE(AreaRec."Cancel No", 0);
                                    IF AreaRec.FINDFIRST THEN
                                        REPEAT
                                            // Added Later sanjoy >>
                                            IF (CheqAndDcoNoMath) AND ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                            (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) THEN BEGIN
                                                IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                    AreaRec.Matching := TRUE;
                                                    AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                    ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                    AreaRec.MODIFY;
                                                    ReconLine."Cross Matching" := TRUE;
                                                    ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                    ReconLine.MODIFY;
                                                    MatchLineNo := MatchLineNo + 1;
                                                    Flag := 0;                                                     // added sanjoy ********
                                                END;                                                          // added sanjoy ********
                                            END
                                            // Added later Sanjoy <<


                                            ELSE
                                                IF (AreaRec."Bank Doc. No." = ReconLine."Document No.") AND
                                               (AreaRec."Bank Posting Date" = ReconLine."Transaction Date") AND
                                               ((AreaRec."Bank Narration") = (ReconLine.Description + ReconLine.Description2 + ReconLine.Description3)) AND
                                               ((AreaRec."Bank Deposit Slip No.") = (ReconLine."Deposit Slip No.")) AND
                                               ((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR
                                               (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount")) THEN BEGIN
                                                    IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                        AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                        AreaRec.Matching := TRUE;
                                                        AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                        ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                        AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                        AreaRec.MODIFY;
                                                        ReconLine."Cross Matching" := TRUE;
                                                        ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                        ReconLine.MODIFY;
                                                        MatchLineNo := MatchLineNo + 1;
                                                        Flag := 0;                                                     // added sanjoy ********
                                                    END;                                                          // added sanjoy ********
                                                END ELSE
                                                    IF ((STRSUBSTNO(AreaRec."Bank Doc. No.") = STRSUBSTNO(ReconLine."Document No.")) OR
                                                       ((AreaRec."Bank Posting Date" = ReconLine."Transaction Date")) AND
                                                       (((AreaRec."Bank Debit Amount" = -ReconLine."Statement Amount") OR (
                                                       (AreaRec."Bank Credit Amount" = ReconLine."Statement Amount"))) OR
                                                       ((STRPOS(AreaRec."Bank Narration", ReconLine.Description + '' + ReconLine.Description2 + '' + ReconLine.Description3) <> 0))))
                                                       THEN BEGIN
                                                        IF ReconLine."Deposit Slip No." <> '' THEN BEGIN
                                                            IF ((STRPOS(AreaRec."Bank Narration", ReconLine."Deposit Slip No.") <> 0)) OR
                                                               ((AreaRec."Bank Deposit Slip No.") = (ReconLine."Deposit Slip No.")) THEN BEGIN
                                                                IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::Exact;
                                                                    AreaRec.Matching := TRUE;
                                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                    AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                                    ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                                    AreaRec.MODIFY;
                                                                    ReconLine."Cross Matching" := TRUE;
                                                                    ReconLine."Matching Status" := ReconLine."Matching Status"::Exact; // Added sanjoy paul ********
                                                                    ReconLine.MODIFY;
                                                                    MatchLineNo := MatchLineNo + 1;
                                                                    Flag := 0;                                                     // added sanjoy ********
                                                                END;                                                          // added sanjoy ********

                                                            END

                                                            ELSE BEGIN
                                                                IF ((STRPOS(AreaRec."Bank Narration", ReconLine."Deposit Slip No.") <> 0)) AND
                                                                (((AreaRec."Bank Debit Amount" = ABS(ReconLine."Statement Amount")) OR (
                                                                (AreaRec."Bank Credit Amount" = ABS(ReconLine."Statement Amount"))))
                                                                AND ((STRPOS(AreaRec."Bank Narration", ReconLine.Description) <> 0))) THEN BEGIN
                                                                    AreaRec."Matching Status" := AreaRec."Matching Status"::Probable;
                                                                    AreaRec.Matching := TRUE;
                                                                    AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                    AreaRec.MODIFY;
                                                                    ReconLine."Cross Matching" := TRUE;
                                                                    ReconLine.MODIFY;
                                                                END;
                                                            END;
                                                        END ELSE BEGIN
                                                            IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                                AreaRec."Matching Status" := AreaRec."Matching Status"::Probable;
                                                                AreaRec.Matching := TRUE;
                                                                AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                                AreaRec."MatchLine No" := MatchLineNo;                          // sanjoy Paul ********
                                                                ReconLine."MatchLine No" := MatchLineNo;                        // sanjoy Paul ********
                                                                AreaRec.MODIFY;
                                                                ReconLine."Cross Matching" := TRUE;
                                                                ReconLine."Matching Status" := ReconLine."Matching Status"::Probable;   // Added sanjoy paul ********
                                                                ReconLine.MODIFY;
                                                                MatchLineNo := MatchLineNo + 1;
                                                                Flag := 1;                                                     // added sanjoy ********
                                                            END;                                                          // added sanjoy ********

                                                        END;
                                                    END ELSE BEGIN
                                                        IF Flag = 2 THEN BEGIN                                            // added sanjoy ********
                                                            AreaRec."Matching Status" := AreaRec."Matching Status"::"Not Matching";
                                                            ReconLine."Matching Status" := ReconLine."Matching Status"::"Not Matching";   // Added sanjoy paul ********
                                                                                                                                          //ReconLine.MODIFY;
                                                            AreaRec.Matching := FALSE;
                                                            //AreaRec."Matched Line No." := ReconLine."Statement Line No.";
                                                            AreaRec.MODIFY;
                                                        END;                                                            // added sanjoy ********

                                                    END;
                                        UNTIL AreaRec.NEXT = 0;
                                UNTIL ReconLine.NEXT = 0;
                                Dialog1.CLOSE;
                            END;
                        END;

                    END;
                }

                action(UploadBankStatement)
                {
                    trigger OnAction()
                    var
                        Bankstatement: XmlPort "Bank Statement Import";
                    BEGIN
                        CLEAR(Bankstatement); // added sanjoy paul ********
                        Bankstatement.GetBankNo("Bank Account No.", "Statement No.");
                        Bankstatement.Run();
                    END;
                }
                action(MatchManually)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Match Manually';
                    Image = CheckRulesSyntax;
                    Promoted = true;
                    PromotedCategory = Category5;
                    PromotedIsBig = true;
                    ToolTip = 'Manually match selected lines in both panes to link each bank statement line to one or more related bank account ledger entries.';

                    trigger OnAction()
                    var
                        TempBankAccReconciliationLine: Record "Bank Acc. Reconciliation Line" temporary;
                        TempBankAccountLedgerEntry: Record "Bank Account Ledger Entry" temporary;
                        MatchBankRecLines: Codeunit "Match Bank Rec. Lines";
                    begin
                        CurrPage.StmtLine.PAGE.GetSelectedRecords(TempBankAccReconciliationLine);
                        CurrPage.ApplyBankLedgerEntries.PAGE.GetSelectedRecords(TempBankAccountLedgerEntry);
                        MatchBankRecLines.MatchManually(TempBankAccReconciliationLine, TempBankAccountLedgerEntry);
                    end;
                }
                action(RemoveMatch)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Remove Match';
                    Image = RemoveContacts;
                    Promoted = true;
                    PromotedCategory = Category5;
                    PromotedIsBig = true;
                    ToolTip = 'Remove selection of matched bank statement lines.';

                    trigger OnAction()
                    var
                        TempBankAccReconciliationLine: Record "Bank Acc. Reconciliation Line" temporary;
                        TempBankAccountLedgerEntry: Record "Bank Account Ledger Entry" temporary;
                        MatchBankRecLines: Codeunit "Match Bank Rec. Lines";
                    begin
                        CurrPage.StmtLine.PAGE.GetSelectedRecords(TempBankAccReconciliationLine);
                        CurrPage.ApplyBankLedgerEntries.PAGE.GetSelectedRecords(TempBankAccountLedgerEntry);
                        MatchBankRecLines.RemoveMatch(TempBankAccReconciliationLine, TempBankAccountLedgerEntry);
                    end;
                }
                action(All)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Show All';
                    Image = AddWatch;
                    ToolTip = 'Show all bank statement lines.';

                    trigger OnAction()
                    begin
                        CurrPage.StmtLine.PAGE.ToggleMatchedFilter(false);
                        CurrPage.ApplyBankLedgerEntries.PAGE.ToggleMatchedFilter(false);
                    end;
                }
                action(NotMatched)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Show Nonmatched';
                    Image = AddWatch;
                    ToolTip = 'Show all bank statement lines that have not yet been matched.';

                    trigger OnAction()
                    begin
                        CurrPage.StmtLine.PAGE.ToggleMatchedFilter(true);
                        CurrPage.ApplyBankLedgerEntries.PAGE.ToggleMatchedFilter(true);
                    end;
                }
                action(MatchedEntries)
                {
                    ApplicationArea = all;
                    Caption = 'Matched Entries';
                    trigger OnAction()
                    begin
                        CurrPage.StmtLine.Page.ToggleMatchedFilterforBankRec;
                        //CurrPage.ApplyBankLedgerEntries.PAGE.ToggleMatchedFilterforBankAccLedge;//PK
                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                Image = Post;
                action("&Test Report")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = '&Test Report';
                    Ellipsis = true;
                    Image = TestReport;
                    ToolTip = 'Preview the resulting bank account reconciliations to see the consequences before you perform the actual posting.';

                    trigger OnAction()
                    begin
                        ReportPrint.PrintBankAccRecon(Rec);
                    end;
                }
                action(Post)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'P&ost';
                    Image = PostOrder;
                    Promoted = true;
                    PromotedCategory = Category6;
                    PromotedIsBig = true;
                    RunObject = Codeunit "Bank Acc. Recon Post Y/N Cust";
                    ShortCutKey = 'F9';
                    ToolTip = 'Finalize the document or journal by posting the amounts and quantities to the related accounts in your company books.';
                }
                action(PostAndPrint)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Post and &Print';
                    Image = PostPrint;
                    Promoted = true;
                    PromotedCategory = Category6;
                    PromotedIsBig = true;
                    RunObject = Codeunit "Bank Acc. Recon. Post+Print";
                    ShortCutKey = 'Shift+F9';
                    ToolTip = 'Finalize and prepare to print the document or journal. The values and quantities are posted to the related accounts. A report request window where you can specify what to include on the print-out.';
                }
                action("Calculate Statement Ending Balance")
                {
                    trigger OnAction()
                    var
                        ClosingBal: Decimal;
                        recOriginalBnk: Record "Original Bank Statement";
                    BEGIN
                        ClosingBal := 0;
                        recOriginalBnk.SETRANGE(recOriginalBnk.Code, "Bank Account No.");
                        recOriginalBnk.SETRANGE(recOriginalBnk."Statement No.", "Statement No.");
                        recOriginalBnk.SETRANGE(recOriginalBnk."Outstanding Entry", FALSE);
                        IF recOriginalBnk.FINDFIRST THEN
                            REPEAT
                                ClosingBal += -recOriginalBnk."Bank Debit Amount" + recOriginalBnk."Bank Credit Amount"
                            UNTIL recOriginalBnk.NEXT = 0;
                        "Closing Balance BANK" := ClosingBal + "Opening Balance BANK";
                        MODIFY;
                    END;
                }
                action("Download Outstanding entries")
                {
                    trigger OnAction()
                    BEGIN
                        TESTFIELD("Outstanding Entry Downloaded", FALSE);
                        DownloadOutstandingEntries;
                        "Outstanding Entry Downloaded" := TRUE;
                        MODIFY;
                    END;

                }
                action(CalculateClosingBankBalance)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Calculate Closing Balance';
                    Image = CalculateBalanceAccount;
                    promoted = true;
                    //promotedcategory = Category7;
                    PromotedIsBig = true;
                    trigger onaction()
                    Var
                        BankStatement: Record "Bank Account Statement";
                        BankAcc: Record "Bank Account";
                        BankAccLedEnt: Record "Bank Account Ledger Entry";
                    BEGIN
                        //PhaniFeb182021>>
                        TestField("Statement Date");
                        clear("Closing Balance");
                        BankAccLedEnt.RESET;
                        BankAccLedEnt.SetRange("Bank Account No.", "Bank Account No.");
                        IF "Statement Date" <> 0D then
                            BankAccLedEnt.SetRange("Posting Date", 0D, "Statement Date");
                        BankAccLedEnt.SetRange(Open, true);
                        IF BankAccLedEnt.findset then
                            repeat
                                "Closing Balance" += BankAccLedEnt.Amount;
                            until BankAccLedEnt.next = 0;

                        /*if BankAcc.Get("Bank Account No.") then begin
                            BankAcc.CalcFields(Balance, "Total on Checks");
                            "Closing Balance" := BankAcc.Balance;
                        end;*/
                        //PhaniFeb182021<<


                        /*BankStatement.Reset;
                        BankStatement.SetRange("Bank Account No.", "Bank Account No.");
                        IF BankStatement.findset then
                            repeat
                                "Closing Balance" := BankStatement."Statement Ending Balance" + "Opening Balance";
                            until BankStatement.next = 0;*/
                    END;
                }
            }
        }
    }
    procedure DownloadOutstandingentries()
    var
        lBankAccReconLine: Record "Bank Acc. Reconciliation Line";
        lOrgBnkStatement: REcord "Original Bank Statement";
        lLastLineNo: integer;
        lBRSOutstandingData: Record "BRS Outstanding Data";
        lBankLedgerEntry: Record "Bank Account Ledger Entry";
        BankAccSetStmtNo: Codeunit "Bank Acc. Entry Set Recon.-No.";
    BEGIN

        lBankAccReconLine.RESET;
        lBankAccReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
        lBankAccReconLine.SETRANGE("Statement No.", "Statement No.");
        lBankAccReconLine.SETRANGE(lBankAccReconLine."Outstanding Entry", TRUE);
        lBankAccReconLine.DELETEALL(TRUE);

        lOrgBnkStatement.RESET;
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement.Code, "Bank Account No.");
        lOrgBnkStatement.SETRANGE("Statement No.", "Statement No.");
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement."Outstanding Entry", TRUE);
        lOrgBnkStatement.DELETEALL(TRUE);

        lBankAccReconLine.RESET;
        lOrgBnkStatement.RESET;
        // Dowanloading Navision Data  >>
        //LBankReconciliationLine.SETCURRENTKEY("MatchLine No");
        lBankAccReconLine.SETRANGE("Bank Account No.", "Bank Account No.");
        lBankAccReconLine.SETRANGE("Statement No.", "Statement No.");
        IF lBankAccReconLine.FINDLAST THEN
            lLastLineNo := lBankAccReconLine."Statement Line No.";
        lBRSOutstandingData.RESET;
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Type, lBRSOutstandingData.Type::Navision);
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Reconciled, FALSE);
        lBRSOutstandingData.SETRANGE("Bank No", "Bank Account No.");
        IF lBRSOutstandingData.FINDSET THEN
            REPEAT
                IF NOT ((lBRSOutstandingData."Debit Amount" = 0) AND (lBRSOutstandingData."Credit Amount" = 0)) THEN BEGIN
                    lBankLedgerEntry.RESET;
                    lBankLedgerEntry.SETCURRENTKEY("Bank Account No.", "Posting Date");
                    lBankLedgerEntry.SETRANGE("Bank Account No.", lBRSOutstandingData."Bank No");
                    lBankLedgerEntry.SETRANGE(lBankLedgerEntry."Document No.", lBRSOutstandingData."Document No");
                    lBankLedgerEntry.SETRANGE(Open, TRUE);
                    lBankLedgerEntry.SETRANGE("Statement Status", lBankLedgerEntry."Statement Status"::Open);
                    IF lBRSOutstandingData."Debit Amount" <> 0 THEN
                        lBankLedgerEntry.SETRANGE(lBankLedgerEntry.Amount, lBRSOutstandingData."Debit Amount");
                    IF lBRSOutstandingData."Credit Amount" <> 0 THEN
                        lBankLedgerEntry.SETRANGE(lBankLedgerEntry.Amount, -lBRSOutstandingData."Credit Amount");
                    lBankLedgerEntry.SETRANGE(lBankLedgerEntry."Statement No.", '');

                    IF lBankLedgerEntry.FINDFIRST THEN BEGIN
                        lBankAccReconLine.INIT;
                        lBankAccReconLine."Statement Line No." := lLastLineNo + 10000;
                        lBankAccReconLine."Transaction Date" := lBankLedgerEntry."Posting Date";
                        lBankAccReconLine.Description := lBankLedgerEntry.Description;
                        lBankAccReconLine."Document No." := lBankLedgerEntry."Document No.";
                        lBankAccReconLine."Bank Account No." := "Bank Account No.";
                        lBankAccReconLine."Statement No." := "Statement No.";
                        lBankAccReconLine."Statement Amount" := lBankLedgerEntry."Remaining Amount";
                        lBankAccReconLine."External Document No." := lBankLedgerEntry."External Document No.";
                        lBankAccReconLine."Applied Amount" := lBankAccReconLine."Statement Amount";
                        lBankAccReconLine.Type := lBankAccReconLine.Type::"Bank Account Ledger Entry";
                        IF lBankLedgerEntry."Old_Description 2" <> '' THEN
                            lBankAccReconLine.Description2 := lBankLedgerEntry."Old_Description 2"
                        ELSE
                            lBankAccReconLine.Description2 := lBankLedgerEntry."Description 2";
                        IF lBankLedgerEntry."Old_Description 3" <> '' THEN
                            lBankAccReconLine.Description3 := lBankLedgerEntry."Old_Description 3"
                        ELSE
                            lBankAccReconLine.Description3 := lBankLedgerEntry."Old_Description 3";
                        lBankAccReconLine."Deposit Slip No." := lBankLedgerEntry."Deposit Slip No.";
                        lBankAccReconLine."Applied Entries" := 1;
                        //lBankAccReconLine."Global Dimension 9 Code" := lBankLedgerEntry."Shortcut Dimension 9 Code";//Commented as per CHI
                        lBankAccReconLine."Outstanding Entry" := TRUE;
                        lBankAccReconLine."Matching Status" := lBankAccReconLine."Matching Status"::"Not Matching";
                        BankAccSetStmtNo.SetReconNo(lBankLedgerEntry, lBankAccReconLine);
                        lBankAccReconLine.INSERT;
                        lLastLineNo += 10000;
                    END;
                END;
            UNTIL lBRSOutstandingData.NEXT = 0;
        //<<


        // Downloading Statement data
        CLEAR(lLastLineNo);
        lOrgBnkStatement.RESET;
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement.Code, "Bank Account No.");
        lOrgBnkStatement.SETRANGE(lOrgBnkStatement."Statement No.", "Statement No.");
        IF lOrgBnkStatement.FINDLAST THEN
            lLastLineNo := lOrgBnkStatement."Line No." + 10000;

        lBRSOutstandingData.RESET;
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Type, lBRSOutstandingData.Type::Statement);
        lBRSOutstandingData.SETRANGE(lBRSOutstandingData.Reconciled, FALSE);
        lBRSOutstandingData.SETRANGE("Bank No", "Bank Account No.");
        IF lBRSOutstandingData.FINDSET THEN
            REPEAT
                IF NOT ((lBRSOutstandingData."Debit Amount" = 0) AND (lBRSOutstandingData."Credit Amount" = 0)) THEN BEGIN
                    lOrgBnkStatement.INIT;
                    lOrgBnkStatement.Code := "Bank Account No.";
                    lOrgBnkStatement."Statement No." := "Statement No.";
                    lOrgBnkStatement."Line No." := lLastLineNo;
                    lOrgBnkStatement."Bank Posting Date" := lBRSOutstandingData."Posting date";
                    lOrgBnkStatement."Bank Narration" := lBRSOutstandingData.Narrations;
                    lOrgBnkStatement."Bank Debit Amount" := lBRSOutstandingData."Debit Amount";
                    lOrgBnkStatement."Bank Credit Amount" := lBRSOutstandingData."Credit Amount";
                    lOrgBnkStatement."Matching Status" := lOrgBnkStatement."Matching Status"::"Not Matching";
                    lOrgBnkStatement."Outstanding Entry" := TRUE;
                    lOrgBnkStatement."Outstanding Entry Line" := lBRSOutstandingData."Line No";
                    lOrgBnkStatement.INSERT;
                    lLastLineNo += 1;
                END;
            UNTIL lBRSOutstandingData.NEXT = 0;
    END;

    Procedure LastMatchLine(): Integer
    var
        LOriginalBankStatement: Record "Original Bank Statement";
        LastNoInternal: integer;
        LastNoExternat:
                        integer;
        LBankReconciliationLine: Record "Bank Acc. Reconciliation Line";
    begin

        // Checking Matching line for both the Tables
        LOriginalBankStatement.RESET;
        LOriginalBankStatement.SETCURRENTKEY("MatchLine No");
        LOriginalBankStatement.SETRANGE(Code, "Bank Account No.");
        LOriginalBankStatement.SETRANGE("Statement No.", "Statement No.");
        IF LOriginalBankStatement.FINDLAST THEN
            LastNoInternal := LOriginalBankStatement."MatchLine No";
        LBankReconciliationLine.RESET;
        LBankReconciliationLine.SETCURRENTKEY("MatchLine No");
        LBankReconciliationLine.SETRANGE("Bank Account No.", "Bank Account No.");
        LBankReconciliationLine.SETRANGE("Statement No.", "Statement No.");
        IF LBankReconciliationLine.FINDLAST THEN
            LastNoExternat := LBankReconciliationLine."MatchLine No";

        IF LastNoInternal = LastNoExternat THEN
            EXIT(LastNoInternal)
        ELSE
            IF LastNoInternal > LastNoExternat THEN BEGIN
                MESSAGE('Please press the Refresh button and start from the fresh');
                EXIT(LastNoInternal);
            END
            ELSE
                IF LastNoInternal < LastNoExternat THEN BEGIN
                    MESSAGE('Please press the Refresh button and start from the fresh');
                    EXIT(LastNoExternat);
                END
    end;

    var
        SuggestBankAccStatement: Report "Suggest Bank Acc. Recon. Lines";
        TransferToGLJnl: Report "Trans. Bank Rec. to Gen. Jnl.";
        ReportPrint: Codeunit "Test Report-Print";
        jj: page 379;

}

