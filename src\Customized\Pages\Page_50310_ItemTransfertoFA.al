page 50310 "ItemTransfertoFA"
{
    PageType = List;
    //ApplicationArea = All;
    SourceTable = "Item Approval For Transfer FA";
    //Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Item No."; "Item No.")
                {
                    ApplicationArea = ALL;
                }

                field(Description; Description)
                {
                    ApplicationArea = ALL;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = ALL;
                }
                field(Status; Status)
                {
                    ApplicationArea = ALL;
                }


            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                //Visible = openapp;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            action("Send Approval Request")
            {
                ApplicationArea = All;
                Image = SendApprovalRequest;
                Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin

                    IF allinoneCU.CheckITMFAApprovalsWorkflowEnabled(Rec) then
                        allinoneCU.OnSendITMFAForApproval(Rec);
                end;
            }
            action("Cancel Approval Request")
            {
                ApplicationArea = All;
                Image = CancelApprovalRequest;
                Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    allinoneCU.OnCancelITMFAForApproval(Rec);
                end;
            }

        }
    }

    trigger OnAfterGetRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    var

        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        ApprovalMgt: Codeunit 1535;
        ApprovalEntries: Page 658;
        allinoneCU: Codeunit Codeunit1;
        RecordRest: Record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        WorkflowManagement: Codeunit "Workflow Management";
        myInt: Integer;
}