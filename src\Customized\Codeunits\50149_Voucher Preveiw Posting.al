codeunit 50149 "Voucher Preview Posting"
{
    // version ReportError,CHI6.0

    // 
    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL       06-Dec-11     -> Codeunit created to post voucher documents.
    // 3.0      SAA       26-Jan-12     -> Code Added to OnRun() to assign new fields for postings to ledgers.
    //                    02-Jan-13     -> Code added to OnRun() to create a contra line for lines
    //                                     having shortcut dimension 1 codes different from the Bank account one.
    //                                  -> Created a new function "CreateContrasAcctLine" to create contra account lines.
    //                    22-May-17     -> Added '"JV Type"::Bank' to the "JV Type" options for Voucher Type::JV
    //                    20-Sept-17    -> Added new functions 'movedimensionstotemp' and 'movedimensionstoactual' to ensure dimension code
    //                                     with dimension no 16 and above are captured in journal line dimensions.
    //                                  -> added codes ton OnRun() to call the new functions.
    // 4.0            RKD 07-JUN-16     -> Change the order of Fa posting type validation after Capex No. and Capex Line No. validation
    // 2019-05        NYO 02-May-19     -> cODE ADDED TO INCLUDE "Applies-to-ID"
    // CRF:2019-0042  NYO 15-May-19     -> CODE ADDED OnRun()

    Permissions = //TableData 356 =rimd, //chi9.0
                  TableData "Voucher Header" = rmd;
    TableNo = "Voucher Header";

    trigger OnRun();
    var
        TmpGenJrnlLine: Record "Gen. Journal Line" temporary;
        TmpGenJrnlLine2: Record "Gen. Journal Line" temporary;
        VendRec: Record Vendor;
        AmountVal: Decimal;
        DelLineGenJrnlLine: Record "Gen. Journal Line";
    begin
        //To delete previous lines which are not posted due to error.
        DelLineGenJrnlLine.RESET;
        DelLineGenJrnlLine.SetRange("Journal Template Name", "Journal Template Code");
        DelLineGenJrnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        DelLineGenJrnlLine.SetRange("Document No.", "Document No.");//PKONJ11
        If DelLineGenJrnlLine.FINDSET then
            DelLineGenJrnlLine.DeleteAll();
        //To delete previous lines which are not posted due to error.

        CLEAR(LineNo);


        if "Voucher Type" <> "Voucher Type"::JV then begin
            TESTFIELD("Account No.");
            TESTFIELD("Posting Date");
        end;
        TESTFIELD(Status, Status::Released);

        //DocNo := NoSeriesMgt.TryGetNextNo("Posting No. Series","Posting Date");
        /*if "Posting Doc. No." = '' then begin
            DocNo := NoSeriesMgt.GetNextNo("Posting No. Series", "Posting Date", false);
            "Posting Doc. No." := DocNo; //To prevent skiping of posting No. Series
            MODIFY;
        end else
            DocNo := "Posting Doc. No.";*/
        DocNo := NoSeriesMgt.GetNextNo("Posting No. Series", "Posting Date", false);
        //To delete previous lines which are not posted due to error.
        DelLineGenJrnlLine.RESET;
        DelLineGenJrnlLine.SetRange("Journal Template Name", "Journal Template Code");
        DelLineGenJrnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        DelLineGenJrnlLine.SetRange("Document No.", DocNo);
        If DelLineGenJrnlLine.FINDSET then
            DelLineGenJrnlLine.DeleteAll();
        //To delete previous lines which are not posted due to error.
        GenJrnlLine.RESET;
        GenJrnlLine.SETRANGE("Journal Template Name", "Journal Template Code");
        GenJrnlLine.SETRANGE("Journal Batch Name", "Journal Batch Name");
        if "Voucher Type" = "Voucher Type"::JV then
            GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV)
        else
            if "Voucher Type" = "Voucher Type"::CPV then
                GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CPV)
            else
                if "Voucher Type" = "Voucher Type"::CRV then
                    GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CRV)
                else
                    if "Voucher Type" = "Voucher Type"::BPV then
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BPV)
                    else
                        if "Voucher Type" = "Voucher Type"::BRV then
                            GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BRV);
        GenJrnlLine.SETRANGE("Document No.", DocNo);
        GenJrnlLine.DELETEALL(true);
        //SAA3.0 >>
        ApprovalEntry.SETRANGE("Document No.", "Document No.");
        if ApprovalEntry.FINDLAST then;
        //Check if voucher has been posted already
        //GLEntryRec.SETCURRENTKEY("Document No.","Posting Date",Amount,"Source Type","Source No.");
        //GLEntryRec.SETRANGE("Old Document No.","Document No.");
        //IF NOT GLEntryRec.ISEMPTY THEN
        //ERROR(TEXT50000,GLEntryRec."Document No.");
        //SAA3.0 <<

        PostedVoucherHeader.LOCKTABLE;
        PostedVoucherHeader.INIT;
        PostedVoucherHeader.TRANSFERFIELDS(Rec);
        PostedVoucherHeader."Dimension Set ID" := "Dimension Set ID";//B2BDim
        PostedVoucherHeader."Posted By" := Getuserid(USERID);
        PostedVoucherHeader."Posted Date" := WORKDATE;
        PostedVoucherHeader."Posted Time" := TIME;
        PostedVoucherHeader."Posting Date" := TODAY; //"Posting Date"; SAA3.0 20/10/2017 <<
        PostedVoucherHeader."Voucher No." := "Document No.";//SAA3.0
        //TESTFIELD("Posting No. Series");
        //PostedVoucherHeader."Document No." := NoSeriesMgt.GetNextNo("Posting No. Series","Posting Date",TRUE);
        //PostedVoucherHeader."Document No." := NoSeriesMgt.TryGetNextNo("Posting No. Series","Posting Date");
        PostedVoucherHeader."Document No." := DocNo;
        //PostedVoucherHeader.INSERT;


        case "Voucher Type" of
            "Voucher Type"::JV:
                begin
                    if "JV Type" in ["JV Type"::General, "JV Type"::Sales, "JV Type"::Purchase, "JV Type"::"IC Purchase",
                      "JV Type"::Lodgement, "JV Type"::"IC Partner", "JV Type"::" ", "JV Type"::BRJV, "JV Type"::Bank, "JV Type"::LBSV] then begin
                        //CRF:2019-0042  NYO 15-05-19  "JV Type"::LBSV addedd
                        //added "JV Type"::Bank among the options for Bank JV postings
                        clear(LineNo);
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        if GenJrnlLine.FIND('-') then
                            repeat
                                LineNo += 100;
                                Clear(GenJrnlLine2);
                                //move dimensions to temp file 20/09/17- Saheed
                                movedimensionstotemp;

                                GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No." + LineNo);//PK
                                //LineNo :=GenJrnlLine."Line No.";
                                GenJrnlLine.CALCFIELDS("Allocated Amt. (LCY)");
                                if GenJrnlLine."Allocated Amt. (LCY)" = 0 then  //SAA3.0
                                    GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.")
                                else
                                    GenJrnlLine2.VALIDATE("Document No.", GenJrnlLine."Document No."); //SAA3.0
                                                                                                       // GenJrnlLine2.VALIDATE("Line No.",LineNo);
                                GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::JV);
                                GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                                GenJrnlLine2.VALIDATE("Bal. Account Type", GenJrnlLine."Bal. Account Type");
                                //GenJrnlLine2.VALIDATE("Responsibility Center",GenJrnlLine."Responsibility Center");
                                GenJrnlLine2.VALIDATE("Posting Date", GenJrnlLine."Posting Date");
                                GenJrnlLine2.VALIDATE("Fixed Asset Type", GenJrnlLine."Fixed Asset Type");
                                GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");
                                // SAA 3.0 <<
                                GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                                GenJrnlLine2.VALIDATE("Bal. Account No.", GenJrnlLine."Bal. Account No.");
                                GenJrnlLine2.VALIDATE("Bal. Gen. Posting Type", GenJrnlLine."Bal. Gen. Posting Type");
                                GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                GenJrnlLine2.VALIDATE(Amount, GenJrnlLine.Amount);
                                GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                                GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                                //GenJrnlLine2.VALIDATE("Amount (LCY)", GenJrnlLine."Amount (LCY)"); //PJ
                                //SAA3.0
                                //IF GenJrnlLine."Debit Amount" <> 0 THEN
                                //  GenJrnlLine2.VALIDATE("Debit Amount",GenJrnlLine."Debit Amount");
                                //IF GenJrnlLine."Credit Amount" <> 0 THEN
                                //  GenJrnlLine2.VALIDATE("Credit Amount",GenJrnlLine."Credit Amount");
                                //GenJrnlLine2.Correction:=TRUE;

                                GenJrnlLine2.VALIDATE("Allocated Amt. (LCY)", GenJrnlLine."Allocated Amt. (LCY)");
                                if GenJrnlLine."Allocated Amt. (LCY)" <> 0 then  //SAA3.0
                                                                                 //GenJrnlLine2.INSERT; ELSE
                                  begin
                                    GenJrnlLine2.VALIDATE("Recurring Method", GenJrnlLine."Recurring Method");
                                    GenJrnlLine2.VALIDATE("Expiration Date", GenJrnlLine."Expiration Date");
                                    GenJrnlLine2.VALIDATE("Recurring Frequency", GenJrnlLine."Recurring Frequency");
                                    //GenJrnlLine2.VALIDATE("Gen. Prod. Posting Group",GenJrnlLine."Gen. Prod. Posting Group");
                                    //GenJrnlLine2.MODIFY;
                                end;
                                GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");
                                GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");
                                GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //NYO 02/05/2019
                                GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                                GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                                GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                                GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                                GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");
                                GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                                GenJrnlLine2.VALIDATE("IC Direction", GenJrnlLine."IC Direction");
                                GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");
                                GenJrnlLine2.VALIDATE("Document Type", GenJrnlLine."Document Type");

                                // SAA 3.0 >>
                                GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                                GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                                GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                                GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                                GenJrnlLine2.VALIDATE("Process Document Type", GenJrnlLine."Process Document Type");
                                //GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code",GenJrnlLine."Shortcut Dimension 1 Code");
                                //GenJrnlLine2."Responsibility Center":=GenJrnlLine."Responsibility Center";
                                GenJrnlLine2."Date PMS Availed" := GenJrnlLine."Date PMS Availed";//NYO

                                if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then begin
                                    GenJrnlLine2."Date PMS Availed" := GenJrnlLine."Date PMS Availed";
                                    //GenJrnlLine2.VALIDATE("FA Posting Type",GenJrnlLine."FA Posting Type");  //Coomented By RKD
                                    GenJrnlLine2."Fuel Availed" := GenJrnlLine."Fuel Availed";
                                    if GenJrnlLine."Maintenance Code" <> '' then begin
                                        GenJrnlLine2.VALIDATE("FA Posting Type", GenJrnlLine."FA Posting Type");
                                        GenJrnlLine2.VALIDATE("Maintenance Code", GenJrnlLine."Maintenance Code");
                                    end;
                                    //RKD >>  to copy Fuel Kilometer perameters
                                    //IF MaintenanceRec.GET(GenJrnlLine."Maintenance Code") THEN BEGIN
                                    // IF MaintenanceRec."PMS Maintenance" THEN BEGIN
                                    GenJrnlLine2."Last Km Reading" := GenJrnlLine."Last Km Reading";
                                    GenJrnlLine2."Date PMS Availed" := GenJrnlLine."Date PMS Availed";
                                    GenJrnlLine2."Current Km Reading" := GenJrnlLine."Current Km Reading";
                                    GenJrnlLine2."Last Meter Reading" := GenJrnlLine."Last Meter Reading";
                                    GenJrnlLine2."Current Meter Reading" := GenJrnlLine."Current Meter Reading";

                                    //END;
                                    //END;


                                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then begin
                                        //GenJrnlLine2.VALIDATE("FA Posting Type",GenJrnlLine."FA Posting Type");
                                        GenJrnlLine2."FA Posting Type" := GenJrnlLine."FA Posting Type"; //RKD
                                        if GenJrnlLine."Maintenance Code" <> '' then
                                            GenJrnlLine2.VALIDATE("Maintenance Code", GenJrnlLine."Maintenance Code");
                                        /*if GenJrnlLine."Capex No." <> '' then
                                            GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                        if GenJrnlLine."Capex Line No." <> 0 then
                                            GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0*/
                                        GenJrnlLine2.VALIDATE("FA Posting Type", GenJrnlLine."FA Posting Type"); //RKD
                                    end;
                                end;
                                //RKD <<
                                //CWIP>>
                                GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                //CWIP<<
                                //Feb192021>>
                                if GenJrnlLine."Capex No." <> '' then
                                    GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                if GenJrnlLine."Capex Line No." <> 0 then
                                    GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No.");
                                //Message('1..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");
                                //SAA3.0
                                //Feb192021<<

                                // nyo
                                d1 := GenJrnlLine."Shortcut Dimension 1 Code";
                                d2 := GenJrnlLine."Shortcut Dimension 2 Code";
                                //nyo not used

                                GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                GenJrnlLine2.Validate("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                                /*GenJrnlLine2.VALIDATE("Shortcut Dimension 9 Code", GenJrnlLine."Shortcut Dimension 9 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 10 Code", GenJrnlLine."Shortcut Dimension 10 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 11 Code", GenJrnlLine."Shortcut Dimension 11 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code", GenJrnlLine."Shortcut Dimension 12 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 13 Code", GenJrnlLine."Shortcut Dimension 13 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 14 Code", GenJrnlLine."Shortcut Dimension 14 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 15 Code", GenJrnlLine."Shortcut Dimension 15 Code");
                                */
                                GenJrnlLine2."External Document No." := GenJrnlLine."External Document No.";
                                GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                                                                                                             //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");

                                GenJrnlLine2."Receipt Document No." := GenJrnlLine."Receipt Document No.";
                                GenJrnlLine2."JV Type" := PostedVoucherHeader."JV Type";
                                GenJrnlLine2."FA Posting Type" := GenJrnlLine."FA Posting Type";
                                GenJrnlLine2."Maintenance Code" := GenJrnlLine."Maintenance Code";
                                GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                                GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                //GenJrnlLine2."Posting No. Series" := GenJrnlLine."Posting No. Series"; //17Feb2021
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                                GenJrnlLine2.Validate("Gen. Bus. Posting Group", '');
                                GenJrnlLine2.Validate("Gen. Prod. Posting Group", '');
                                GenJrnlLine2.Validate("Gen. Posting Type", 0);
                                GenJrnlLine2.Validate("VAT Bus. Posting Group", '');
                                GenJrnlLine2.Validate("VAT Prod. Posting Group", '');
                                GenJrnlLine2.Validate("Bal. Gen. Bus. Posting Group", '');
                                GenJrnlLine2.Validate("Bal. Gen. Prod. Posting Group", '');
                                GenJrnlLine2.Validate("Bal. VAT Bus. Posting Group", '');
                                GenJrnlLine2.Validate("Bal. VAT Prod. Posting Group", '');
                                if GenJrnlLine."Allocated Amt. (LCY)" = 0 then  //SAA3.0
                                    GenJrnlLine2.INSERT
                                else
                                    GenJrnlLine2.MODIFY;

                                //move dimensions from temp file to actual 20/09/17- Saheed
                                movedimensionstoactual;

                            // GenJrnlLine2.VALIDATE("Responsibility Center",GenJrnlLine."Responsibility Center");
                            // GenJrnlLine2.MODIFY;
                            until GenJrnlLine.NEXT = 0;

                        if GenJrnlLine."Allocated Amt. (LCY)" <> 0 then begin //SAA3.0
                            GenJrnlLine2.RESET;
                            GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV);
                            GenJrnlLine2.SETRANGE("Document No.", "Document No.");
                            //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post",GenJrnlLine2);
                            //RKD >>
                            //GenJnlPost.CopyDocumentNo("Document No."); //PJ
                            GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK                            
                            /*GenJnlPost.RUN(GenJrnlLine2);
                            CLEAR(GenJnlPost);*///Feb15
                            //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Line",GenJrnlLine2);
                            //RKD <<
                        end else begin
                            GenJrnlLine2.RESET;
                            GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV);
                            GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");
                            //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post",GenJrnlLine2);
                            //RKD >>
                            //GenJnlPost.CopyDocumentNo(PostedVoucherHeader."Document No."); //PJ
                            GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                            Commit();
                            GenJnlPost.Preview(GenJrnlLine2);
                            //Message('%1', GenJrnlLine2."Line No.");
                            CLEAR(GenJnlPost);
                            //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Line",GenJrnlLine2);
                            //RKD <<
                            Commit();

                        end;
                    end;
                    if "JV Type" in ["JV Type"::"Bk-Dr-Memo", "JV Type"::"Bk-Cr-Memo", "JV Type"::BRJNL] then begin
                        if "Branch Voucher Type" = "Branch Voucher Type"::Debit then begin
                            GenJrnlLine.RESET;
                            GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV);
                            GenJrnlLine.SETRANGE("Document No.", "Document No.");
                            if GenJrnlLine.FIND('-') then
                                repeat
                                    //LineNo += 10000;
                                    //GenJrnlLine2.VALIDATE("Line No.",LineNo);
                                    // SAA3.0 >>

                                    //move dimensions to temp file 20/09/17  - Saheed
                                    movedimensionstotemp;

                                    GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No.");
                                    LineNo := GenJrnlLine."Line No.";
                                    // SAA3.0 <<
                                    GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::JV);
                                    GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                    GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                    GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                                    //GenJrnlLine2.VALIDATE("Posting Date",GenJrnlLine."Posting Date");
                                    GenJrnlLine2.VALIDATE("Posting Date", "Posting Date");  //RKD MODIFIED

                                    GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                                    //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                                    // SAA 3.0 <<
                                    //GenJrnlLine2.VALIDATE("Responsibility Center",PostedVoucherHeader."Responsibility Center");
                                    GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                                    GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                                    GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                                    GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                                    GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                                    GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                                    GenJrnlLine2.VALIDATE("Debit Amount", GenJrnlLine."Debit Amount");
                                    GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                                    GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                                    GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");
                                    GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");
                                    GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //NYO 02/05/2019
                                    GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                                    GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                                    GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");
                                    GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                                    //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");
                                    // SAA 3.0 >>
                                    GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                                    GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                                    GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                                    GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                    GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                                    GenJrnlLine2.VALIDATE("Document Type", GenJrnlLine."Document Type");
                                    //HO1.0 >>
                                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then begin
                                        GenJrnlLine2.VALIDATE("FA Posting Type", GenJrnlLine."FA Posting Type");
                                        GenJrnlLine2.VALIDATE("Maintenance Code", GenJrnlLine."Maintenance Code");
                                        if (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type"::"Acquisition Cost")
                                         /*or (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type" ::"capital work in progress") */then begin
                                            if GenJrnlLine."Capex No." <> '' then
                                                GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                            if GenJrnlLine."Capex Line No." <> 0 then
                                                GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                        end;
                                    end;
                                    ///HO1.0 <<
                                    /// 
                                    //SAA 3.0 >>
                                    //Feb192021>>
                                    if GenJrnlLine."Capex No." <> '' then
                                        GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                    if GenJrnlLine."Capex Line No." <> 0 then
                                        GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                    //Message('2..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");
                                    //Feb192021<<


                                    //CWIP>>
                                    GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                    //CWIP<<
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                                    GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                                    /*
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 9 Code", GenJrnlLine."Shortcut Dimension 9 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 10 Code", GenJrnlLine."Shortcut Dimension 10 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 11 Code", GenJrnlLine."Shortcut Dimension 11 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code", GenJrnlLine."Shortcut Dimension 12 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 13 Code", GenJrnlLine."Shortcut Dimension 13 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 14 Code", GenJrnlLine."Shortcut Dimension 14 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 15 Code", GenJrnlLine."Shortcut Dimension 15 Code");
                                    */
                                    // SAA 3.0 <<
                                    GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                    GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                    GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                    GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                    GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                                    GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                    GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                    GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                    GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                    GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                    GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                    GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                    GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                                    GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                                    GenJrnlLine2."Payable Name" := PostedVoucherHeader."Payable Name";
                                    GenJrnlLine2."Cash Payments Options" := PostedVoucherHeader."Cash Payments Options";
                                    GenJrnlLine2."Cash Paid" := PostedVoucherHeader."Cash Paid";
                                    GenJrnlLine2."Cash Paid By" := PostedVoucherHeader."Cash Paid By";
                                    GenJrnlLine2."Cash Paid On" := PostedVoucherHeader."Cash Paid On";
                                    GenJrnlLine2."Reprint CPV Slip" := PostedVoucherHeader."Reprint CPV Slip";
                                    GenJrnlLine2."Reprinted By" := PostedVoucherHeader."Reprinted By";
                                    GenJrnlLine2."Reprinted On" := PostedVoucherHeader."Reprinted On";
                                    GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                    GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                    GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                                    GenJrnlLine2."Collected By Name" := ToBeCollectedBy;
                                    // SAA 3.0 <<
                                    GenJrnlLine2.INSERT;

                                    //move dimensions from temp file to actual 20/09/17 - Saheed
                                    movedimensionstoactual;

                                until GenJrnlLine.NEXT = 0;

                            LineNo += 10000;
                            GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::JV);
                            GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                            GenJrnlLine2.VALIDATE("Line No.", LineNo);
                            GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                            //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                            GenJrnlLine2.VALIDATE("Posting Date", "Posting Date");  //RKD Modified

                            GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                            GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                            if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                                GenJrnlLine2.VALIDATE("Responsibility Center", "Responsibility Center");
                            GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                            GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                            GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                            CALCFIELDS("Amount (LCY)");
                            CALCFIELDS(Amount);
                            if "Currency Code" = '' then
                                GenJrnlLine2.VALIDATE("Credit Amount", ABS("Amount (LCY)"))
                            else
                                GenJrnlLine2.VALIDATE("Credit Amount", ABS(Amount));
                            //GenJrnlLine2.VALIDATE("Credit Amount",("Amount (LCY)"));
                            GenJrnlLine2.VALIDATE(Narration, Narration);
                            GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                            GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                            GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                            GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                            GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                            GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                            GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                            GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                            GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                            GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                            GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                            GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                            GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                            GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                            GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                            // SAA 3.0 >>
                            GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                            GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                            GenJrnlLine2."Payable Name" := PostedVoucherHeader."Payable Name";
                            GenJrnlLine2."Cash Payments Options" := PostedVoucherHeader."Cash Payments Options";
                            GenJrnlLine2."Cash Paid" := PostedVoucherHeader."Cash Paid";
                            GenJrnlLine2."Cash Paid By" := PostedVoucherHeader."Cash Paid By";
                            GenJrnlLine2."Cash Paid On" := PostedVoucherHeader."Cash Paid On";
                            GenJrnlLine2."Reprint CPV Slip" := PostedVoucherHeader."Reprint CPV Slip";
                            GenJrnlLine2."Reprinted By" := PostedVoucherHeader."Reprinted By";
                            GenJrnlLine2."Reprinted On" := PostedVoucherHeader."Reprinted On";
                            // SAA 3.0 >>
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                            GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                            GenJrnlLine2.VALIDATE("Document Type", 0);
                            //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                            //GenJrnlLine2.VALIDATE("IC Partner Code",GenJrnlLine."IC Partner Code");
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");
                            //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);

                            GenJrnlLine2.VALIDATE(Narration, Narration);
                            GenJrnlLine2.VALIDATE(Description, "Account Name");
                            GenJrnlLine2."Responsibility Center" := "Responsibility Center"; //saa3.0
                            GenJrnlLine2.VALIDATE("Description 2", Narration);
                            GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                            GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                            GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                            GenJrnlLine2."Collected By Name" := ToBeCollectedBy;

                            // SAA 3.0 <<
                            GenJrnlLine2.INSERT;

                        end;
                        if "Branch Voucher Type" = "Branch Voucher Type"::"Credit " then begin
                            GenJrnlLine.RESET;
                            GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV);
                            GenJrnlLine.SETRANGE("Document No.", "Document No.");
                            if GenJrnlLine.FIND('-') then
                                repeat
                                    //LineNo += 10000;
                                    //GenJrnlLine2.VALIDATE("Line No.",LineNo);
                                    // SAA3.0 >>

                                    //move dimensions to temp file 20/09/17  - Saheed
                                    movedimensionstotemp;

                                    GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No.");
                                    LineNo := GenJrnlLine."Line No.";
                                    // SAA3.0 <<
                                    GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::JV);
                                    GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                    GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                    GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                                    //GenJrnlLine2.VALIDATE("Posting Date",GenJrnlLine."Posting Date");
                                    GenJrnlLine2.VALIDATE("Posting Date", "Posting Date");  //RKD MODIFIED

                                    GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                                    //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                                    // SAA 3.0 <<
                                    //GenJrnlLine2.VALIDATE("Responsibility Center",PostedVoucherHeader."Responsibility Center");
                                    GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                                    GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                                    GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                                    GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                                    GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                                    GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                                    GenJrnlLine2.VALIDATE("Credit Amount", GenJrnlLine."Credit Amount");
                                    GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                                    GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                                    GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");
                                    GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");
                                    GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID");//NYO 02/05/2019
                                    GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                                    GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                                    GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");
                                    GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                                    //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");
                                    // SAA 3.0 >>
                                    GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                                    GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                                    GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                                    GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                    GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                                    GenJrnlLine2.VALIDATE("Document Type", GenJrnlLine."Document Type");
                                    //HO1.0 >>
                                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then begin
                                        GenJrnlLine2.VALIDATE("FA Posting Type", GenJrnlLine."FA Posting Type");
                                        GenJrnlLine2.VALIDATE("Maintenance Code", GenJrnlLine."Maintenance Code");
                                        if (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type"::"Acquisition Cost")
                                       /* or (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type" ::"capital work in progress")*/  then begin
                                            if GenJrnlLine."Capex No." <> '' then
                                                GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                            if GenJrnlLine."Capex Line No." <> 0 then
                                                GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                        end;
                                    end;
                                    ///HO1.0 <<
                                    //Feb192021>>
                                    if GenJrnlLine."Capex No." <> '' then
                                        GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                    if GenJrnlLine."Capex Line No." <> 0 then
                                        GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                    //Message('3..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");
                                    //Feb192021<<

                                    /// 
                                    //SAA 3.0 >>
                                    //CWIP>>
                                    GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                    //CWIP<<
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                                    GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                                    /*
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 9 Code", GenJrnlLine."Shortcut Dimension 9 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 10 Code", GenJrnlLine."Shortcut Dimension 10 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 11 Code", GenJrnlLine."Shortcut Dimension 11 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code", GenJrnlLine."Shortcut Dimension 12 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 13 Code", GenJrnlLine."Shortcut Dimension 13 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 14 Code", GenJrnlLine."Shortcut Dimension 14 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 15 Code", GenJrnlLine."Shortcut Dimension 15 Code");
                                    */
                                    // SAA 3.0 <<
                                    GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                    GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                    GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                    GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                    GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                                    GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                    GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                    GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                    GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                    GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                    GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                    GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                    GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                                    GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                                    GenJrnlLine2."Payable Name" := PostedVoucherHeader."Payable Name";
                                    GenJrnlLine2."Cash Payments Options" := PostedVoucherHeader."Cash Payments Options";
                                    GenJrnlLine2."Cash Paid" := PostedVoucherHeader."Cash Paid";
                                    GenJrnlLine2."Cash Paid By" := PostedVoucherHeader."Cash Paid By";
                                    GenJrnlLine2."Cash Paid On" := PostedVoucherHeader."Cash Paid On";
                                    GenJrnlLine2."Reprint CPV Slip" := PostedVoucherHeader."Reprint CPV Slip";
                                    GenJrnlLine2."Reprinted By" := PostedVoucherHeader."Reprinted By";
                                    GenJrnlLine2."Reprinted On" := PostedVoucherHeader."Reprinted On";
                                    GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                    GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                    GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                                    GenJrnlLine2."Collected By Name" := ToBeCollectedBy;
                                    // SAA 3.0 <<
                                    GenJrnlLine2.INSERT;

                                    //move dimensions from temp file to actual 20/09/17- Saheed
                                    movedimensionstoactual;

                                until GenJrnlLine.NEXT = 0;

                            LineNo += 10000;
                            GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::JV);
                            GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                            GenJrnlLine2.VALIDATE("Line No.", LineNo);
                            GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                            //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                            GenJrnlLine2.VALIDATE("Posting Date", "Posting Date");  //RKD Modified

                            GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                            GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                            if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                                GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                            GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                            GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                            GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                            CALCFIELDS("Amount (LCY)");
                            CALCFIELDS(Amount);
                            if "Currency Code" = '' then
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS("Amount (LCY)"))
                            else
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS(Amount));
                            //GenJrnlLine2.VALIDATE("Credit Amount",("Amount (LCY)"));
                            GenJrnlLine2.VALIDATE(Narration, Narration);
                            GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                            GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                            GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                            GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                            GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                            GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                            GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                            GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                            GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                            GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                            GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                            GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                            GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                            GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                            GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                            // SAA 3.0 >>
                            GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                            GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                            GenJrnlLine2."Payable Name" := PostedVoucherHeader."Payable Name";
                            GenJrnlLine2."Cash Payments Options" := PostedVoucherHeader."Cash Payments Options";
                            GenJrnlLine2."Cash Paid" := PostedVoucherHeader."Cash Paid";
                            GenJrnlLine2."Cash Paid By" := PostedVoucherHeader."Cash Paid By";
                            GenJrnlLine2."Cash Paid On" := PostedVoucherHeader."Cash Paid On";
                            GenJrnlLine2."Reprint CPV Slip" := PostedVoucherHeader."Reprint CPV Slip";
                            GenJrnlLine2."Reprinted By" := PostedVoucherHeader."Reprinted By";
                            GenJrnlLine2."Reprinted On" := PostedVoucherHeader."Reprinted On";
                            // SAA 3.0 >>
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                            GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                            GenJrnlLine2.VALIDATE("Document Type", 0);
                            //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                            //GenJrnlLine2.VALIDATE("IC Partner Code",GenJrnlLine."IC Partner Code");
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");
                            //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);

                            GenJrnlLine2.VALIDATE(Narration, Narration);
                            GenJrnlLine2.VALIDATE(Description, "Account Name");
                            GenJrnlLine2.VALIDATE("Description 2", Narration);
                            GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                            GenJrnlLine2."Responsibility Center" := "Responsibility Center"; //saa3.0
                            GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                            GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                            GenJrnlLine2."Collected By Name" := ToBeCollectedBy;

                            // SAA 3.0 <<
                            GenJrnlLine2.INSERT;
                        end;
                        GenJrnlLine2.RESET;
                        GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::JV);
                        GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");

                        //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post",GenJrnlLine2);
                        //RKD >>
                        //GenJnlPost.CopyDocumentNo(PostedVoucherHeader."Document No."); //PJ
                        GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                        //GenJnlPost.RUN(GenJrnlLine2); //PK test on 23.02.2021
                        CLEAR(GenJnlPost);
                        //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Line",GenJrnlLine2);
                        //RKD <<
                    end;
                end;
            "Voucher Type"::CPV:
                begin
                    /*
                     GenJrnlLine.RESET;
                     GenJrnlLine.SETRANGE("Journal Template Name","Journal Template Code");
                     GenJrnlLine.SETRANGE("Journal Batch Name","Journal Batch Name");
                     IF GenJrnlLine.FINDLAST THEN
                       LineNo := GenJrnlLine."Line No.";
                     */
                    IF Not "WHT Applicable" then begin
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CPV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        if GenJrnlLine.FIND('-') then
                            repeat
                                //LineNo += 10000;
                                //GenJrnlLine2.VALIDATE("Line No.",LineNo);
                                // SAA3.0 >>
                                //move dimensions to temp file 20/09/17 - Saheed
                                movedimensionstotemp;
                                GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No." + 100);//PK
                                                                                                //GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No.");
                                                                                                //LineNo := GenJrnlLine."Line No.";
                                                                                                // SAA3.0 <<

                                GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::CPV);
                                GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                                //GenJrnlLine2.VALIDATE("Posting Date",GenJrnlLine."Posting Date");
                                GenJrnlLine2.VALIDATE("Posting Date", TODAY);  //RKD MODIFIED

                                GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                                //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                                // SAA 3.0 <<
                                //GenJrnlLine2.VALIDATE("Responsibility Center",PostedVoucherHeader."Responsibility Center");
                                GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                                GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                                GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                                GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                                GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                                GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                                GenJrnlLine2.VALIDATE("Debit Amount", GenJrnlLine."Debit Amount");
                                GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                                GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                                GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");
                                GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");
                                GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //NYO 02/05/2019
                                GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                                GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                                GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");
                                GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                                //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");
                                // SAA 3.0 >>
                                GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                                GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                                GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                                GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                                GenJrnlLine2.VALIDATE("Document Type", GenJrnlLine."Document Type");
                                //HO1.0 >>
                                if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then begin
                                    GenJrnlLine2."Date PMS Availed" := GenJrnlLine."Date PMS Availed";
                                    //RKD >> Moved the code from below commented
                                    if (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type"::"Acquisition Cost")
                                    /*or (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type" ::"capital work in progress") */then begin
                                        if GenJrnlLine."Capex No." <> '' then
                                            GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                        if GenJrnlLine."Capex Line No." <> 0 then
                                            GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                    end;
                                    //RKD <<
                                    GenJrnlLine2.VALIDATE("FA Posting Type", GenJrnlLine."FA Posting Type");
                                    if GenJrnlLine."Maintenance Code" <> '' then      //RKD added code
                                        GenJrnlLine2.VALIDATE("Maintenance Code", GenJrnlLine."Maintenance Code");

                                    if MaintenanceRec.GET(GenJrnlLine."Maintenance Code") then begin
                                        if MaintenanceRec."PMS Maintenance" then begin
                                            GenJrnlLine2."Last Km Reading" := GenJrnlLine."Last Km Reading";
                                            GenJrnlLine2."Date PMS Availed" := GenJrnlLine."Date PMS Availed";
                                            GenJrnlLine2.VALIDATE("Current Km Reading", GenJrnlLine."Current Km Reading");
                                        end;
                                    end;


                                    //GenJrnlLine2.VALIDATE("Current Km Reading",GenJrnlLine."Current Km Reading");
                                    //GenJrnlLine2."Last Km Reading":=GenJrnlLine."Last Km Reading";
                                    GenJrnlLine2."Fuel Availed" := GenJrnlLine."Fuel Availed";
                                    //RKD << Code moved to Up
                                    //IF (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type" ::"Acquisition Cost") OR
                                    //  (GenJrnlLine."FA Posting Type" = GenJrnlLine."FA Posting Type" ::"capital work in progress") THEN BEGIN
                                    //  IF GenJrnlLine."Capex No." <>'' THEN
                                    //    GenJrnlLine2.VALIDATE("Capex No.",GenJrnlLine."Capex No."); //SAA3.0
                                    //  IF GenJrnlLine."Capex Line No." <>0 THEN
                                    //    GenJrnlLine2.VALIDATE("Capex Line No.",GenJrnlLine."Capex Line No."); //SAA3.0
                                    //END;
                                    //RKD <<
                                end;
                                ///HO1.0 <<
                                /// 
                                IF GenJrnlLine."Capex No." <> '' THEN
                                    GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                IF GenJrnlLine."Capex Line No." <> 0 THEN
                                    GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                //Message('4..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");



                                //SAA 3.0 >>
                                //CWIP>>
                                GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                //CWIP<<
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                                GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");
                                /*
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 9 Code", GenJrnlLine."Shortcut Dimension 9 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 10 Code", GenJrnlLine."Shortcut Dimension 10 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 11 Code", GenJrnlLine."Shortcut Dimension 11 Code");
                                */
                                //IF GenJrnlLine."Shortcut Dimension 12 Code" <>'' THEN
                                //  GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code",GenJrnlLine."Shortcut Dimension 12 Code");
                                //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                                /*
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code", GenJrnlLine."Shortcut Dimension 12 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 13 Code", GenJrnlLine."Shortcut Dimension 13 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 14 Code", GenJrnlLine."Shortcut Dimension 14 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 15 Code", GenJrnlLine."Shortcut Dimension 15 Code");
                                */
                                // SAA 3.0 <<
                                GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                                GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                                GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                                GenJrnlLine2."Payable Name" := PostedVoucherHeader."Payable Name";
                                GenJrnlLine2."Cash Payments Options" := PostedVoucherHeader."Cash Payments Options";
                                GenJrnlLine2."Cash Paid" := PostedVoucherHeader."Cash Paid";
                                GenJrnlLine2."Cash Paid By" := PostedVoucherHeader."Cash Paid By";
                                GenJrnlLine2."Cash Paid On" := PostedVoucherHeader."Cash Paid On";
                                GenJrnlLine2."Reprint CPV Slip" := PostedVoucherHeader."Reprint CPV Slip";
                                GenJrnlLine2."Reprinted By" := PostedVoucherHeader."Reprinted By";
                                GenJrnlLine2."Reprinted On" := PostedVoucherHeader."Reprinted On";
                                GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                                GenJrnlLine2."Collected By Name" := ToBeCollectedBy;

                                // SAA 3.0 <<
                                GenJrnlLine2.INSERT;

                                //move dimensions from temp file to actual  20/09/17 - Saheed
                                movedimensionstoactual;


                            until GenJrnlLine.NEXT = 0;

                        //LineNo += 10000; //PK
                        LineNo += 10200;//PK
                        GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::CPV);
                        GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                        GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                        GenJrnlLine2.VALIDATE("Line No.", LineNo);
                        GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                        GenJrnlLine2.VALIDATE("Document Type", GenJrnlLine."Document Type"); //PJ
                                                                                             //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                        GenJrnlLine2.VALIDATE("Posting Date", TODAY);  //RKD Modified

                        GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                        GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                        if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                            GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                        GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                        GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                        GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                        GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                        CALCFIELDS("Amount (LCY)");
                        CALCFIELDS(Amount);
                        //GenJrnlLine2.VALIDATE("Credit Amount",("Amount (LCY)"));
                        if "Currency Code" = '' then
                            GenJrnlLine2.VALIDATE("Credit Amount", ABS("Amount (LCY)"))
                        else
                            GenJrnlLine2.VALIDATE("Credit Amount", ABS(Amount));
                        //GenJrnlLine2.VALIDATE("Credit Amount",(Amount));
                        GenJrnlLine2.VALIDATE(Narration, Narration);
                        GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";

                        IF GenJrnlLine."Capex No." <> '' THEN
                            GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                        IF GenJrnlLine."Capex Line No." <> 0 THEN
                            GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                        //Message('5..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                        GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                        GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                        GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                        GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                        GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                        GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                        GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                        GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                        GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                        GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                        GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                        GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                        GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                        GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                        GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                        // SAA 3.0 >>
                        GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                        GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                        GenJrnlLine2."Payable Name" := PostedVoucherHeader."Payable Name";
                        GenJrnlLine2."Cash Payments Options" := PostedVoucherHeader."Cash Payments Options";
                        GenJrnlLine2."Cash Paid" := PostedVoucherHeader."Cash Paid";
                        GenJrnlLine2."Cash Paid By" := PostedVoucherHeader."Cash Paid By";
                        GenJrnlLine2."Cash Paid On" := PostedVoucherHeader."Cash Paid On";
                        GenJrnlLine2."Reprint CPV Slip" := PostedVoucherHeader."Reprint CPV Slip";
                        GenJrnlLine2."Reprinted By" := PostedVoucherHeader."Reprinted By";
                        GenJrnlLine2."Reprinted On" := PostedVoucherHeader."Reprinted On";
                        // SAA 3.0 >>
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                        //GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");B2BDim
                        GenJrnlLine2.VALIDATE("Document Type", 1);
                        //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                        //GenJrnlLine2.VALIDATE("IC Partner Code",GenJrnlLine."IC Partner Code");
                        GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");
                        //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);

                        GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                        GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                        GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                        GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                        IF GenJrnlLine."Capex No." <> '' THEN
                            GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                        IF GenJrnlLine."Capex Line No." <> 0 THEN
                            GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                        //Message('6..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                        GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";

                        //GenJrnlLine2."Description 2" :=
                        //  COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                        //GenJrnlLine2."Description 3" :=
                        //COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                        //    MAXSTRLEN(GenJrnlLine2."Description 3"));


                        GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                        GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                        GenJrnlLine2."Collected By Name" := ToBeCollectedBy;

                        // SAA 3.0 <<
                        GenJrnlLine2.INSERT;

                        GenJrnlLine2.RESET;
                        GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CPV);
                        GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");

                        //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post",GenJrnlLine2);
                        //RKD >>
                        //GenJnlPost.CopyDocumentNo(PostedVoucherHeader."Document No."); //PJ
                        GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                        Commit();
                        GenJnlPost.Preview(GenJrnlLine2);
                        //Message('%1', GenJrnlLine2."Line No.");
                        CLEAR(GenJnlPost);
                    end else begin
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CPV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        //GenJrnlLine.SetRange("WHT Amount", 0);//PkintoSline
                        if GenJrnlLine.FIND('-') then BEGIN
                            LineNo += 10000 + 100;
                            GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                            GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                            GenJrnlLine2.VALIDATE("Line No.", LineNo);
                            GenJrnlLine2.Insert();
                            //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                            GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                            GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::CPV);
                            GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                            if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                                GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                            GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                            GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");
                            GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                            GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                            CALCFIELDS("Amount (LCY)");
                            CALCFIELDS(Amount);
                            if "Currency Code" = '' then
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS("Amount (LCY)"))
                            else
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS(Amount));
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //CRF:2019-0042 NYO 01/08/2019
                            GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                            GenJrnlLine2."Import File No." := PostedVoucherHeader."Import File No.";
                            GenJrnlLine2."Clearing File No." := PostedVoucherHeader."Clearing File No.";
                            //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", "IC Partner G/L Acc. No.");//SAA3.0
                                                                                                        //GenJrnlLine2.VALIDATE("Voucher Type",GenJrnlLine2."Voucher Type" ::BPV);
                            GenJrnlLine2.VALIDATE("Bal. Account Type", "Account Type");
                            GenJrnlLine2.VALIDATE("Bal. Account No.", "Account No.");
                            //Message('%1...%2', GenJrnlLine2."Bal. Account Type", GenJrnlLine2."Bal. Account No.");

                            IF GenJrnlLine."Capex No." <> '' THEN
                                GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                            IF GenJrnlLine."Capex Line No." <> 0 THEN
                                GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                            //Message('7..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                            GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                            GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                            GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                            GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                            GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                            GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                            GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                            GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                            GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                            GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                            GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                            GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                            GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                            // SAA 3.0 >>
                            GenJrnlLine2."Teller / Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                            GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                            GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                            GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                            GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                            GenJrnlLine2."Collected By Name" := PostedVoucherHeader.ToBeCollectedBy;
                            //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                            GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                            GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                            GenJrnlLine2.VALIDATE("Document Type", 1);
                            //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                            //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);
                            /*
                            GenJrnlLine2."Description 2" :=
                              COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                            GenJrnlLine2."Description 3" :=
                              COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                                MAXSTRLEN(GenJrnlLine2."Description 3"));
                            */
                            //CWIP>>
                            GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                            //CWIP<<
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                            GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                            GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                            GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                            GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                            GenJrnlLine2."Collected By Name" := ToBeCollectedBy;
                            // SAA 3.0 <<
                            //Prasanna for WHT For Single Line
                            GenJrnlLine2."WHT %" := GenJrnlLine."WHT %";
                            GenJrnlLine2."WHT Account" := GenJrnlLine."WHT Account";
                            GenJrnlLine2."WHT Group" := GenJrnlLine."WHT Group";
                            GenJrnlLine2."WHT Amount" := GenJrnlLine."WHT Amount";
                            GenJrnlLine2."WHT Amount(LCY)" := GenJrnlLine."WHT Amount(LCY)";
                            GenJrnlLine2."WHT Amount (Base)" := GenJrnlLine."WHT Amount (Base)";
                            GenJrnlLine2."WHT Amount(LCY) (Base)" := GenJrnlLine."WHT Amount(LCY) (Base)";
                            GenJrnlLine2."Loan ID" := GenJrnlLine."Loan ID";
                            //Prasanna for WHT For Single Line
                            GenJrnlLine2.Modify();
                            GenJrnlLine2.RESET;
                            GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CPV);
                            GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");
                            GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                            //Message('post also');
                            Commit();
                            GenJnlPost.Preview(GenJrnlLine2);//PK on 01.20.21
                            //Message('%1', GenJrnlLine2."Line No.");
                            CLEAR(GenJnlPost);
                        END;
                    end;
                    //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Line",GenJrnlLine2);
                    //RKD <<

                    /*
                    CLEAR(GLPosted);
                    GLEntry.RESET;
                    GLEntry.SETRANGE("Voucher Type",GLEntry."Voucher Type" :: CPV);
                    GLEntry.SETRANGE("Document No.",PostedVoucherHeader."Document No.");
                    IF GLEntry.FINDFIRST THEN
                      GLPosted := TRUE
                    ELSE
                      GenJrnlLine2.DELETEALL;

                    IF GLPosted THEN BEGIN
                      //NoSeriesMgt.GetNextNo("Posting No. Series","Posting Date",TRUE);
                      PostedVoucherHeader.INSERT;

                      TempApprovalEntry.RESET;
                      TempApprovalEntry.DELETEALL;
                      ApprovalEntry.SETRANGE("Table ID",DATABASE::"Voucher Header");
                      ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: CPV);
                      ApprovalEntry.SETRANGE("Document No.","Document No.");
                      IF ApprovalEntry.FINDSET THEN BEGIN
                        REPEAT
                          TempApprovalEntry.INIT;
                          TempApprovalEntry := ApprovalEntry;
                          TempApprovalEntry.INSERT;
                        UNTIL ApprovalEntry.NEXT = 0;
                      END;
                      ApprovalMgt.MoveApprvalEntryToPosted(TempApprovalEntry,DATABASE::"Posted Voucher Header",
                                                           PostedVoucherHeader."Document No.");
                      ApprovalMgt.DeleteApprovalEntry(DATABASE::"Voucher Header",22,"Document No.");

                      DimMgt.MoveOneDocDimToPostedDocDim(
                        TempDocDim,DATABASE::"Voucher Header",16,"Document No.",0,
                        DATABASE::"Posted Voucher Header",PostedVoucherHeader."Document No.");
                    END;*/
                end;

            "Voucher Type"::CRV:
                begin

                    /* GenJrnlLine.RESET;
                     GenJrnlLine.SETRANGE("Journal Template Name","Journal Template Code");
                     GenJrnlLine.SETRANGE("Journal Batch Name","Journal Batch Name");
                     IF GenJrnlLine.FINDLAST THEN
                       LineNo := GenJrnlLine."Line No.";
                     */
                    //MESSAGE("Document No."); //RKD
                    IF Not "WHT Applicable" then begin
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CRV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        if GenJrnlLine.FIND('-') then
                            repeat
                                //LineNo += 10000;
                                //move dimensions to temp file 20/09/17  - Saheed
                                movedimensionstotemp;
                                GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No." + 100);//PK
                                GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                                //GenJrnlLine2.VALIDATE("Line No.",LineNo);
                                // SAA3.0 >>
                                //GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No.");
                                //LineNo := GenJrnlLine."Line No.";
                                // SAA3.0 <<
                                //GenJrnlLine2.VALIDATE("Posting Date",GenJrnlLine."Posting Date");
                                GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD MODIFIED
                                                                              //GenJrnlLine2.VALIDATE("Document Type",GenJrnlLine2."Document Type" ::Payment);
                                GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::CRV);
                                GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                                // SAA 3.0 >>
                                //GenJrnlLine2.VALIDATE("Responsibility Center",PostedVoucherHeader."Responsibility Center");
                                GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                                GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                                GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");//SAA3.0
                                GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");  //SAA3.0
                                GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID");  //NYO 02/05/2019
                                GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                                GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                                GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                                GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                                GenJrnlLine2.VALIDATE("Credit Amount", GenJrnlLine."Credit Amount");
                                GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                                GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                                GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                                GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                                GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");

                                // SAA 3.0 >>
                                GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                                GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                                GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                                // SAA3.0 >>
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                // SAA3.0 <<

                                GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                                GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                                /*
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 9 Code", GenJrnlLine."Shortcut Dimension 9 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 10 Code", GenJrnlLine."Shortcut Dimension 10 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 11 Code", GenJrnlLine."Shortcut Dimension 11 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code", GenJrnlLine."Shortcut Dimension 12 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 13 Code", GenJrnlLine."Shortcut Dimension 13 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 14 Code", GenJrnlLine."Shortcut Dimension 14 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 15 Code", GenJrnlLine."Shortcut Dimension 15 Code");
                                */
                                //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                                //GenJrnlLine2.VALIDATE("Ship to Code",GenJrnlLine."Ship to Code");

                                IF GenJrnlLine."Capex No." <> '' THEN
                                    GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                IF GenJrnlLine."Capex Line No." <> 0 THEN
                                    GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                //Message('8..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                                //CWIP>>
                                GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                //CWIP<<
                                GenJrnlLine2.Cleared := GenJrnlLine.Cleared;
                                GenJrnlLine2.VALIDATE("Credited On", GenJrnlLine."Credited On");
                                GenJrnlLine2.VALIDATE("Credited By", GenJrnlLine."Credited By");
                                GenJrnlLine2.VALIDATE("Credit Bank", GenJrnlLine."Credit Bank");
                                GenJrnlLine2."Collected By Name" := PostedVoucherHeader."Bank Teller Paid By";
                                // SAA 3.0 >>
                                GenJrnlLine2."Paid To / Received By" := PostedVoucherHeader."Received From";
                                GenJrnlLine2."Receiving Type" := PostedVoucherHeader."Receiving Type";
                                GenJrnlLine2."Receiving Code" := PostedVoucherHeader."Receiving Code";
                                GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                                GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                //GenJrnlLine2.PaymentSettlementOf:=PostedVoucherHeader.PaymentSettlementOf;
                                GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                                GenJrnlLine2.INSERT;

                                //move dimensions from temp file to actual 20/09/17 - Saheed
                                movedimensionstoactual;

                            until GenJrnlLine.NEXT = 0;

                        //LineNo += 10000;//PK
                        LineNo += 10200;//PK
                        GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                        GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                        GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                        GenJrnlLine2.VALIDATE("Line No.", LineNo);
                        //GenJrnlLine2.VALIDATE("Document Type",GenJrnlLine2."Document Type" ::Payment);
                        GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::CRV);
                        //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                        GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                        GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                        if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                            GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                        GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                        GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                        GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                        GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                        CALCFIELDS("Amount (LCY)");
                        CALCFIELDS(Amount);
                        if "Currency Code" = '' then
                            GenJrnlLine2.VALIDATE("Debit Amount", ABS("Amount (LCY)"))
                        else
                            GenJrnlLine2.VALIDATE("Debit Amount", ABS(Amount));
                        GenJrnlLine2.VALIDATE(Narration, Narration);
                        GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                        GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                        GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                        GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                        GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                        GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                        GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                        GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                        GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                        GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                        GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                        GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                        GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                        GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                        GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                        GenJrnlLine2."Paid To / Received By" := PostedVoucherHeader."Received From";
                        GenJrnlLine2."Receiving Type" := PostedVoucherHeader."Receiving Type";
                        GenJrnlLine2."Receiving Code" := PostedVoucherHeader."Receiving Code";
                        GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                        GenJrnlLine2."Collected By Name" := PostedVoucherHeader."Bank Teller Paid By";//HO1.0
                                                                                                      // SAA 3.0 >>
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                        GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                        //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                        //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);

                        GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                        GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                        GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                        GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                        IF GenJrnlLine."Capex No." <> '' THEN
                            GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                        IF GenJrnlLine."Capex Line No." <> 0 THEN
                            GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                        //Message('9..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                        //CWIP>>
                        GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                        //CWIP<<
                        /*
                        GenJrnlLine2."Description 2" :=
                          COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                        GenJrnlLine2."Description 3" :=
                          COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                            MAXSTRLEN(GenJrnlLine2."Description 3"));
                         */
                        GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                        GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";

                        // SAA 3.0 <<
                        GenJrnlLine2.INSERT;

                        GenJrnlLine2.RESET;
                        GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CRV);
                        GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");
                        //RKD >>
                        //GenJnlPost.CopyDocumentNo(PostedVoucherHeader."Document No."); //PJ
                        GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                        Commit();
                        GenJnlPost.Preview(GenJrnlLine2);
                        //Message('%1', GenJrnlLine2."Line No.");
                        CLEAR(GenJnlPost);
                    end else begin
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CRV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        //GenJrnlLine.SetRange("WHT Amount", 0);//PkintoSline
                        if GenJrnlLine.FIND('-') then BEGIN
                            LineNo += 10000 + 100;
                            GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                            GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                            GenJrnlLine2.VALIDATE("Line No.", LineNo);
                            GenJrnlLine2.Insert();
                            //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                            GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                            GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::CRV);
                            GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                            if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                                GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                            GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                            GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");
                            GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                            GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                            CALCFIELDS("Amount (LCY)");
                            CALCFIELDS(Amount);
                            if "Currency Code" = '' then
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS("Amount (LCY)"))
                            else
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS(Amount));
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //CRF:2019-0042 NYO 01/08/2019
                            GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                            GenJrnlLine2."Import File No." := PostedVoucherHeader."Import File No.";
                            GenJrnlLine2."Clearing File No." := PostedVoucherHeader."Clearing File No.";
                            //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", "IC Partner G/L Acc. No.");//SAA3.0
                                                                                                        //GenJrnlLine2.VALIDATE("Voucher Type",GenJrnlLine2."Voucher Type" ::BPV);
                            GenJrnlLine2.VALIDATE("Bal. Account Type", "Account Type");
                            GenJrnlLine2.VALIDATE("Bal. Account No.", "Account No.");
                            //Message('%1...%2', GenJrnlLine2."Bal. Account Type", GenJrnlLine2."Bal. Account No.");
                            GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                            GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                            GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                            GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                            GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                            GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                            GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                            GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                            GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                            GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                            GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                            GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                            // SAA 3.0 >>
                            GenJrnlLine2."Teller / Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                            GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                            GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                            GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                            GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                            GenJrnlLine2."Collected By Name" := PostedVoucherHeader.ToBeCollectedBy;
                            //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                            GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                            GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                            GenJrnlLine2.VALIDATE("Document Type", 1);
                            //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                            //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);
                            /*
                            GenJrnlLine2."Description 2" :=
                              COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                            GenJrnlLine2."Description 3" :=
                              COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                                MAXSTRLEN(GenJrnlLine2."Description 3"));
                            */
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                            GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                            GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                            GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                            GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                            GenJrnlLine2."Collected By Name" := ToBeCollectedBy;
                            // SAA 3.0 <<
                            //Prasanna for WHT For Single Line
                            GenJrnlLine2."WHT %" := GenJrnlLine."WHT %";
                            GenJrnlLine2."WHT Account" := GenJrnlLine."WHT Account";
                            GenJrnlLine2."WHT Group" := GenJrnlLine."WHT Group";
                            GenJrnlLine2."WHT Amount" := GenJrnlLine."WHT Amount";
                            GenJrnlLine2."WHT Amount(LCY)" := GenJrnlLine."WHT Amount(LCY)";
                            GenJrnlLine2."WHT Amount (Base)" := GenJrnlLine."WHT Amount (Base)";
                            GenJrnlLine2."WHT Amount(LCY) (Base)" := GenJrnlLine."WHT Amount(LCY) (Base)";
                            GenJrnlLine2."Loan ID" := GenJrnlLine."Loan ID";
                            //Prasanna for WHT For Single Line
                            GenJrnlLine2.Modify();
                            GenJrnlLine2.RESET;
                            GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::CRV);
                            GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");
                            GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                            //Message('post also');
                            Commit();
                            GenJnlPost.Preview(GenJrnlLine2);//PK on 01.20.21
                            //Message('%1', GenJrnlLine2."Line No.");
                            CLEAR(GenJnlPost);
                        END;
                    end;

                    //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Line",GenJrnlLine2);
                    //RKD <<

                    /*
                    CLEAR(GLPosted);
                    GLEntry.RESET;
                    GLEntry.SETRANGE("Voucher Type",GLEntry."Voucher Type" :: CRV);
                    GLEntry.SETRANGE("Document No.",PostedVoucherHeader."Document No.");
                    IF GLEntry.FINDFIRST THEN
                      GLPosted := TRUE
                    ELSE
                      GenJrnlLine2.DELETEALL;


                    IF GLPosted THEN BEGIN
                      //NoSeriesMgt.GetNextNo("Posting No. Series","Posting Date",TRUE);
                      PostedVoucherHeader.INSERT;

                      TempApprovalEntry.RESET;
                      TempApprovalEntry.DELETEALL;
                      ApprovalEntry.SETRANGE("Table ID",DATABASE::"Voucher Header");
                      ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: CRV);
                      ApprovalEntry.SETRANGE("Document No.","Document No.");
                      IF ApprovalEntry.FINDSET THEN BEGIN
                        REPEAT
                          TempApprovalEntry.INIT;
                          TempApprovalEntry := ApprovalEntry;
                          TempApprovalEntry.INSERT;
                        UNTIL ApprovalEntry.NEXT = 0;
                      END;
                      ApprovalMgt.MoveApprvalEntryToPosted(TempApprovalEntry,DATABASE::"Posted Voucher Header",
                                                           PostedVoucherHeader."Document No.");
                      ApprovalMgt.DeleteApprovalEntry(DATABASE::"Voucher Header",23,"Document No.");


                      DimMgt.MoveOneDocDimToPostedDocDim(
                        TempDocDim,DATABASE::"Voucher Header",17,"Document No.",0,
                        DATABASE::"Posted Voucher Header",PostedVoucherHeader."Document No.");

                    END;*/
                end;


            "Voucher Type"::BRV:
                begin
                    IF Not "WHT Applicable" then begin
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BRV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        if GenJrnlLine.FIND('-') then
                            repeat
                                //LineNo += 10000;
                                //move dimensions to temp file 20/09/17 - Saheed
                                movedimensionstotemp;
                                //GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No." + 100);//PK-GJ2
                                GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                //GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No.");
                                //LineNo := GenJrnlLine."Line No.";
                                //GenJrnlLine2.VALIDATE("Line No.",LineNo);
                                //GenJrnlLine2.VALIDATE("Posting Date",GenJrnlLine."Posting Date");
                                GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                                GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                                //GenJrnlLine2.VALIDATE("Document Type",GenJrnlLine2."Document Type" ::Payment);
                                GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::BRV);
                                GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                                // SAA 3.0 >>
                                //GenJrnlLine2.VALIDATE("Responsibility Center",PostedVoucherHeader."Responsibility Center");
                                // SAA 3.0 <<
                                GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                                GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");//SAA3.0
                                GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");  //SAA3.0
                                GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID");  //NYO 02/05/2019
                                GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                                GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                                GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                                GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                                GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                                GenJrnlLine2.VALIDATE("Credit Amount", GenJrnlLine."Credit Amount");
                                GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                                GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                                GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                                GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                                GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");
                                GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");
                                GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                                GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                // SAA 3.0 >>
                                GenJrnlLine2."Collected By Name" := PostedVoucherHeader."Bank Teller Paid By";//HO1.0
                                GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                                /*
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 9 Code", GenJrnlLine."Shortcut Dimension 9 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 10 Code", GenJrnlLine."Shortcut Dimension 10 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 11 Code", GenJrnlLine."Shortcut Dimension 11 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 12 Code", GenJrnlLine."Shortcut Dimension 12 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 13 Code", GenJrnlLine."Shortcut Dimension 13 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 14 Code", GenJrnlLine."Shortcut Dimension 14 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 15 Code", GenJrnlLine."Shortcut Dimension 15 Code");
                                */
                                //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");

                                IF GenJrnlLine."Capex No." <> '' THEN
                                    GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                IF GenJrnlLine."Capex Line No." <> 0 THEN
                                    GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                //Message('10..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                                //CWIP>>
                                GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                //CWIP<<
                                GenJrnlLine2.Cleared := GenJrnlLine.Cleared;
                                GenJrnlLine2.VALIDATE("Credited On", GenJrnlLine."Credited On");
                                GenJrnlLine2.VALIDATE("Credited By", GenJrnlLine."Credited By");
                                GenJrnlLine2.VALIDATE("Credit Bank", GenJrnlLine."Credit Bank");
                                GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                                GenJrnlLine2."Teller / Cheque No." := FORMAT(PostedVoucherHeader."Teller Bank Name")
                                + PostedVoucherHeader."Teller / Cheque No.";
                                GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                                GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                                GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                                GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                                GenJrnlLine2."Paid To / Received By" := PostedVoucherHeader."Received From";
                                GenJrnlLine2."Receiving Type" := PostedVoucherHeader."Receiving Type";
                                GenJrnlLine2."Receiving Code" := PostedVoucherHeader."Receiving Code";
                                GenJrnlLine2.VALIDATE("Bank Doc. Type", GenJrnlLine."Bank Doc. Type");
                                GenJrnlLine2.VALIDATE("Reason for RTN Cheque", GenJrnlLine."Reason for RTN Cheque");
                                GenJrnlLine2.VALIDATE("Return Cheque", GenJrnlLine."Return Cheque");
                                GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                                GenJrnlLine2.VALIDATE("Process Document Type", GenJrnlLine."Process Document Type");
                                GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                                //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                                GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                                GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                                GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                                GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                                                                                                             // SAA 3.0 <<
                                GenJrnlLine2.INSERT;

                                //move dimensions from temp file to actual 20/09/17 - Saheed
                                movedimensionstoactual;


                            until GenJrnlLine.NEXT = 0;

                        //LineNo += 10000; //PK
                        LineNo += 10100; //PK
                        GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                        GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                        GenJrnlLine2.VALIDATE("Line No.", LineNo);
                        GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                        //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                        GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified

                        //GenJrnlLine2.VALIDATE("Document Type",GenJrnlLine2."Document Type" ::Payment);
                        GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::BRV);
                        GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                        if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                            GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                        GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                        GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");  //SAA3.0
                        GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                        GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                        CALCFIELDS("Amount (LCY)");
                        CALCFIELDS(Amount);
                        if "Currency Code" = '' then
                            GenJrnlLine2.VALIDATE("Debit Amount", ABS("Amount (LCY)"))
                        else
                            GenJrnlLine2.VALIDATE("Debit Amount", ABS(Amount));
                        GenJrnlLine2.VALIDATE(Narration, Narration);
                        GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                        GenJrnlLine2.VALIDATE("Import File No.", PostedVoucherHeader."Import File No.");
                        GenJrnlLine2.VALIDATE("Clearing File No.", PostedVoucherHeader."Clearing File No.");

                        GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                        GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                        GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                        GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                        GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                        GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                        GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                        GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                        GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                        GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                        GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                        GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                        // SAA 3.0 >>
                        GenJrnlLine2."Collected By Name" := PostedVoucherHeader."Bank Teller Paid By";//HO1.0
                        GenJrnlLine2."Teller / Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                        GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                        GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                        GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                        GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                        GenJrnlLine2."Paid To / Received By" := PostedVoucherHeader."Received From";
                        GenJrnlLine2."Receiving Type" := PostedVoucherHeader."Receiving Type";
                        GenJrnlLine2."Receiving Code" := PostedVoucherHeader."Receiving Code";
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                        GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                        GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                        //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                        //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);
                        GenJrnlLine2.VALIDATE(Narration, GenJrnlLine.Narration);
                        GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                        GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                        GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                        /*
                        GenJrnlLine2."Description 2" :=
                          COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                        GenJrnlLine2."Description 3" :=
                          COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                            MAXSTRLEN(GenJrnlLine2."Description 3"));
                            */
                        GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                        GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";

                        // SAA 3.0 <<
                        GenJrnlLine2.INSERT;

                        GenJrnlLine2.RESET;
                        GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BRV);
                        GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");

                        //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post",GenJrnlLine2);
                        //RKD >>
                        //GenJnlPost.CopyDocumentNo(PostedVoucherHeader."Document No."); //PJ
                        GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                        Commit();
                        GenJnlPost.Preview(GenJrnlLine2);
                        //Message('%1', GenJrnlLine2."Line No.");
                        CLEAR(GenJnlPost);
                    end else begin
                        GenJrnlLine.RESET;
                        GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BRV);
                        GenJrnlLine.SETRANGE("Document No.", "Document No.");
                        //GenJrnlLine.SetRange("WHT Amount", 0);//PkintoSline
                        if GenJrnlLine.FIND('-') then BEGIN
                            LineNo += 10000 + 100;
                            GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                            GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                            GenJrnlLine2.VALIDATE("Line No.", LineNo);
                            GenJrnlLine2.Insert();
                            //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                            GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                            GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::BRV);
                            GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                            if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                                GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                            GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                            GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");
                            GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                            GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                            CALCFIELDS("Amount (LCY)");
                            CALCFIELDS(Amount);
                            if "Currency Code" = '' then
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS("Amount (LCY)"))
                            else
                                GenJrnlLine2.VALIDATE("Debit Amount", ABS(Amount));
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //CRF:2019-0042 NYO 01/08/2019
                            GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                            GenJrnlLine2."Import File No." := PostedVoucherHeader."Import File No.";
                            GenJrnlLine2."Clearing File No." := PostedVoucherHeader."Clearing File No.";
                            //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", "IC Partner G/L Acc. No.");//SAA3.0
                                                                                                        //GenJrnlLine2.VALIDATE("Voucher Type",GenJrnlLine2."Voucher Type" ::BPV);
                            GenJrnlLine2.VALIDATE("Bal. Account Type", "Account Type");
                            GenJrnlLine2.VALIDATE("Bal. Account No.", "Account No.");
                            //Message('%1...%2', GenJrnlLine2."Bal. Account Type", GenJrnlLine2."Bal. Account No.");
                            GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                            GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                            GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                            GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                            GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                            GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                            GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                            GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                            GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                            GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                            GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                            GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                            // SAA 3.0 >>
                            GenJrnlLine2."Teller / Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                            GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                            GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                            GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                            GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                            GenJrnlLine2."Collected By Name" := PostedVoucherHeader.ToBeCollectedBy;
                            //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                            GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                            GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                            GenJrnlLine2.VALIDATE("Document Type", 1);
                            //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                            //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);
                            /*
                            GenJrnlLine2."Description 2" :=
                              COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                            GenJrnlLine2."Description 3" :=
                              COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                                MAXSTRLEN(GenJrnlLine2."Description 3"));
                            */
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                            GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                            GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                            GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                            GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                            GenJrnlLine2."Collected By Name" := ToBeCollectedBy;
                            // SAA 3.0 <<
                            //Prasanna for WHT For Single Line
                            GenJrnlLine2."WHT %" := GenJrnlLine."WHT %";
                            GenJrnlLine2."WHT Account" := GenJrnlLine."WHT Account";
                            GenJrnlLine2."WHT Group" := GenJrnlLine."WHT Group";
                            GenJrnlLine2."WHT Amount" := GenJrnlLine."WHT Amount";
                            GenJrnlLine2."WHT Amount(LCY)" := GenJrnlLine."WHT Amount(LCY)";
                            GenJrnlLine2."WHT Amount (Base)" := GenJrnlLine."WHT Amount (Base)";
                            GenJrnlLine2."WHT Amount(LCY) (Base)" := GenJrnlLine."WHT Amount(LCY) (Base)";
                            GenJrnlLine2."Loan ID" := GenJrnlLine."Loan ID";
                            //Prasanna for WHT For Single Line

                            IF GenJrnlLine."Capex No." <> '' THEN
                                GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                            IF GenJrnlLine."Capex Line No." <> 0 THEN
                                GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                            //Message('12..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                            //CWIP>>
                            GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                            //CWIP<<
                            GenJrnlLine2.Modify();
                            GenJrnlLine2.RESET;
                            GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BRV);
                            GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");
                            GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK

                            ////Message('post also');
                            Commit();
                            GenJnlPost.Preview(GenJrnlLine2);//PK on 01.20.21
                            //Message('%1', GenJrnlLine2."Line No.");
                            CLEAR(GenJnlPost);
                        END;
                    end;
                    //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post Line",GenJrnlLine2);
                    //RKD <<


                    /*
                    CLEAR(GLPosted);
                    GLEntry.RESET;
                    GLEntry.SETRANGE("Voucher Type",GLEntry."Voucher Type" :: BRV);
                    GLEntry.SETRANGE("Document No.",PostedVoucherHeader."Document No.");
                    IF GLEntry.FINDFIRST THEN
                      GLPosted := TRUE
                    ELSE
                      GenJrnlLine2.DELETEALL;


                    IF GLPosted THEN BEGIN
                      //NoSeriesMgt.GetNextNo("Posting No. Series","Posting Date",TRUE);
                      PostedVoucherHeader.INSERT;

                      TempApprovalEntry.RESET;
                      TempApprovalEntry.DELETEALL;
                      ApprovalEntry.SETRANGE("Table ID",DATABASE::"Voucher Header");
                      ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: BRV);
                      ApprovalEntry.SETRANGE("Document No.","Document No.");
                      IF ApprovalEntry.FINDSET THEN BEGIN
                        REPEAT
                          TempApprovalEntry.INIT;
                          TempApprovalEntry := ApprovalEntry;
                          TempApprovalEntry.INSERT;
                        UNTIL ApprovalEntry.NEXT = 0;
                      END;
                      ApprovalMgt.MoveApprvalEntryToPosted(TempApprovalEntry,DATABASE::"Posted Voucher Header",
                                                           PostedVoucherHeader."Document No.");
                      ApprovalMgt.DeleteApprovalEntry(DATABASE::"Voucher Header",25,"Document No.");

                      DimMgt.MoveOneDocDimToPostedDocDim(
                        TempDocDim,DATABASE::"Voucher Header",19,"Document No.",0,
                        DATABASE::"Posted Voucher Header",PostedVoucherHeader."Document No.");
                    END;*/
                end;

            "Voucher Type"::BPV:
                begin
                    /*
                    GenJrnlLine.RESET;
                    GenJrnlLine.SETRANGE("Journal Template Name","Journal Template Code");
                    GenJrnlLine.SETRANGE("Journal Batch Name","Journal Batch Name");
                    IF GenJrnlLine.FINDLAST THEN
                      LineNo := GenJrnlLine."Line No.";
                     */
                    GenJrnlLine.RESET;
                    GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BPV);
                    GenJrnlLine.SETRANGE("Document No.", "Document No.");
                    //GenJrnlLine.SetRange("WHT Amount", 0);//PkintoSline
                    if GenJrnlLine.FIND('-') then
                        repeat
                            //LineNo += 10000;
                            //move dimensions to temp file  20/09/17
                            movedimensionstotemp;
                            GenJrnlLine2.Reset();
                            GenJrnlLine2.SETRANGE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.SETRANGE("Journal Batch Name", "Journal Batch Name");
                            IF GenJrnlLine2.FindLast() THEN
                                LineNo := GenJrnlLine2."Line No." + 100;
                            //PK On 02.05.2021<<
                            GenJrnlLine2.VALIDATE("Line No.", LineNo);//PK
                            GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                            GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                            //GenJrnlLine2.VALIDATE("Line No.",LineNo);
                            //GenJrnlLine2.VALIDATE("Line No.", GenJrnlLine."Line No.");
                            //LineNo := GenJrnlLine."Line No.";
                            GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                            GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::BPV);
                            //GenJrnlLine2.VALIDATE("Posting Date",GenJrnlLine."Posting Date");
                            GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified

                            GenJrnlLine2.VALIDATE("Account Type", GenJrnlLine."Account Type");
                            // SAA 3.0 >>
                            //GenJrnlLine2.VALIDATE("Responsibility Center",PostedVoucherHeader."Responsibility Center");

                            // SAA 3.0 <<
                            GenJrnlLine2.VALIDATE("Account No.", GenJrnlLine."Account No.");
                            GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("Currency Code", GenJrnlLine."Currency Code");
                            GenJrnlLine2.VALIDATE("Currency Factor", GenJrnlLine."Currency Factor");
                            GenJrnlLine2.VALIDATE("Debit Amount", GenJrnlLine."Debit Amount");
                            GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                            GenJrnlLine2.VALIDATE("Source Code", GenJrnlLine."Source Code");
                            GenJrnlLine2."Import File No." := PostedVoucherHeader."Import File No.";
                            GenJrnlLine2."Clearing File No." := PostedVoucherHeader."Clearing File No.";
                            GenJrnlLine2.VALIDATE("Reason Code", GenJrnlLine."Reason Code");
                            GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                            GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                            GenJrnlLine2.VALIDATE("Applies-to Doc. Type", GenJrnlLine."Applies-to Doc. Type");
                            GenJrnlLine2.VALIDATE("Applies-to Doc. No.", GenJrnlLine."Applies-to Doc. No.");
                            GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //NYO 02/05/2019
                            //GenJrnlLine2.VALIDATE("VAT Bus. Posting Group", GenJrnlLine."VAT Bus. Posting Group");
                            //GenJrnlLine2.VALIDATE("VAT Prod. Posting Group", GenJrnlLine."VAT Prod. Posting Group");
                            // SAA 3.0 >>
                            GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                            GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");
                            GenJrnlLine2.VALIDATE("Loan ID", GenJrnlLine."Loan ID");
                            GenJrnlLine2."WHT %" := GenJrnlLine."WHT %";
                            GenJrnlLine2."WHT Account" := GenJrnlLine."WHT Account";
                            GenJrnlLine2."WHT Group" := GenJrnlLine."WHT Group";
                            GenJrnlLine2."WHT Amount" := GenJrnlLine."WHT Amount";
                            GenJrnlLine2."WHT Amount(LCY)" := GenJrnlLine."WHT Amount(LCY)";
                            GenJrnlLine2."WHT Amount (Base)" := GenJrnlLine."WHT Amount (Base)";
                            GenJrnlLine2."WHT Amount(LCY) (Base)" := GenJrnlLine."WHT Amount(LCY) (Base)";

                            GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                            GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                            GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                            GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                            GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //CRF:2019-0042 NYO 01/08/2019
                            GenJrnlLine2."Payable to" := PostedVoucherHeader."Payable to";
                            GenJrnlLine2."Payable Code" := PostedVoucherHeader."Payable Code";
                            GenJrnlLine2.VALIDATE("Bank Doc. Type", GenJrnlLine."Bank Doc. Type");
                            GenJrnlLine2.VALIDATE("Reason for RTN Cheque", GenJrnlLine."Reason for RTN Cheque");
                            GenJrnlLine2.VALIDATE("Return Cheque", GenJrnlLine."Return Cheque");
                            GenJrnlLine2.VALIDATE("Ship to Code", GenJrnlLine."Ship to Code");
                            GenJrnlLine2.VALIDATE("Process Document Type", GenJrnlLine."Process Document Type");
                            GenJrnlLine2."Collected By Name" := PostedVoucherHeader.ToBeCollectedBy;
                            GenJrnlLine2.VALIDATE("External Document No.", GenJrnlLine."External Document No.");

                            GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", GenJrnlLine."Shortcut Dimension 1 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", GenJrnlLine."Shortcut Dimension 2 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 3 Code", GenJrnlLine."Shortcut Dimension 3 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 4 Code", GenJrnlLine."Shortcut Dimension 4 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 5 Code", GenJrnlLine."Shortcut Dimension 5 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 6 Code", GenJrnlLine."Shortcut Dimension 6 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 7 Code", GenJrnlLine."Shortcut Dimension 7 Code");
                            GenJrnlLine2.VALIDATE("Shortcut Dimension 8 Code", GenJrnlLine."Shortcut Dimension 8 Code");
                            GenJrnlLine2.VALIDATE("Dimension Set ID", GenJrnlLine."Dimension Set ID");//B2BDim
                            GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                            // SAA 3.0 <<
                            //HO1.0 >>
                            if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then begin
                                if GenJrnlLine."Capex No." <> '' then
                                    GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                if GenJrnlLine."Capex Line No." <> 0 then
                                    GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0

                                GenJrnlLine2.VALIDATE("FA Posting Type", GenJrnlLine."FA Posting Type");
                                if GenJrnlLine."Maintenance Code" <> '' then
                                    GenJrnlLine2.VALIDATE("Maintenance Code", GenJrnlLine."Maintenance Code");
                            end;
                            ///HO1.0 <<
                            /// 
                            IF GenJrnlLine."Capex No." <> '' THEN
                                GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                            IF GenJrnlLine."Capex Line No." <> 0 THEN
                                GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                                                                                       //Message('13..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                            //CWIP>>
                            GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                            //CWIP<<
                            GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                            GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                            GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                            GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                            GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                            GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                            GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                            GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                            GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                            GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                            GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                            GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                            GenJrnlLine2.VALIDATE("Document Type", 1);
                            GenJrnlLine2."Responsibility Center" := GenJrnlLine."Responsibility Center"; //saa3.0
                            GenJrnlLine2.INSERT;
                            GenJrnlLine2.VALIDATE("Charge Code", GenJrnlLine."Charge Code");
                            GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                            GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                            GenJrnlLine2."Collected By Name" := ToBeCollectedBy;

                            //move dimensions from temp file to actual 20/09/17
                            movedimensionstoactual;

                            //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                            GenJrnlLine2.MODIFY;
                        until GenJrnlLine.NEXT = 0;
                    //GJ_CHI_RBM_121413 >>>>
                    if not GenJrnlLine."Multiple Batch" then begin
                        //PK On 02.05.2021>>
                        GenJrnlLine2.Reset();
                        GenJrnlLine2.SETRANGE("Journal Template Name", "Journal Template Code");
                        GenJrnlLine2.SETRANGE("Journal Batch Name", "Journal Batch Name");
                        IF GenJrnlLine2.FindLast() THEN
                            LineNo := GenJrnlLine2."Line No." + 100;
                        //PK On 02.05.2021<<
                        GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                        GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                        GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                        GenJrnlLine2.VALIDATE("Line No.", LineNo);
                        //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                        GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                        GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::BPV);
                        GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                        if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                            GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                        GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                        GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                        GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                        CALCFIELDS("Amount (LCY)");
                        CALCFIELDS(Amount);
                        if "Currency Code" = '' then
                            GenJrnlLine2.VALIDATE("Credit Amount", ABS("Amount (LCY)"))
                        else
                            GenJrnlLine2.VALIDATE("Credit Amount", ABS(Amount));
                        GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                        GenJrnlLine2.VALIDATE("Applies-to ID", GenJrnlLine."Applies-to ID"); //CRF:2019-0042 NYO 01/08/2019
                        GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                        GenJrnlLine2."Import File No." := PostedVoucherHeader."Import File No.";
                        GenJrnlLine2."Clearing File No." := PostedVoucherHeader."Clearing File No.";
                        //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                        GenJrnlLine2.VALIDATE("IC Partner Code", GenJrnlLine."IC Partner Code");
                        GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", "IC Partner G/L Acc. No.");//SAA3.0

                        IF GenJrnlLine."Capex No." <> '' THEN
                            GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                        IF GenJrnlLine."Capex Line No." <> 0 THEN
                            GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                                                                                   //Message('14..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                        //GenJrnlLine2.VALIDATE("Voucher Type",GenJrnlLine2."Voucher Type" ::BPV);
                        GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                        GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                        GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                        GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                        GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                        GenJrnlLine2."Modified By" := Getuserid(PostedVoucherHeader."Modified By");
                        GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                        GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                        GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                        GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                        GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                        GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                        GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                        // SAA 3.0 >>
                        GenJrnlLine2."Teller / Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                        GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                        GenJrnlLine2."WHT %" := GenJrnlLine."WHT %";
                        GenJrnlLine2."WHT Account" := GenJrnlLine."WHT Account";
                        GenJrnlLine2."WHT Group" := GenJrnlLine."WHT Group";
                        GenJrnlLine2."WHT Amount" := GenJrnlLine."WHT Amount";
                        GenJrnlLine2."WHT Amount(LCY)" := GenJrnlLine."WHT Amount(LCY)";
                        GenJrnlLine2."WHT Amount (Base)" := GenJrnlLine."WHT Amount (Base)";
                        GenJrnlLine2."WHT Amount(LCY) (Base)" := GenJrnlLine."WHT Amount(LCY) (Base)";
                        GenJrnlLine2."Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                        GenJrnlLine2."Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                        GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                        GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                        GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                        GenJrnlLine2."Collected By Name" := PostedVoucherHeader.ToBeCollectedBy;
                        //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                        GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                        GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                        GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                        GenJrnlLine2.VALIDATE("Document Type", 1);
                        //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                        //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);
                        /*
                        GenJrnlLine2."Description 2" :=
                          COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                        GenJrnlLine2."Description 3" :=
                          COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                            MAXSTRLEN(GenJrnlLine2."Description 3"));
                        */
                        GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                        GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                        GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                        GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                        GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                        GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                        GenJrnlLine2."Collected By Name" := ToBeCollectedBy;

                        GenJrnlLine2.INSERT;
                        //GenJrnlLine2.VALIDATE("Charge Code",GenJrnlLine."Charge Code");
                        //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                        //GenJrnlLine2.MODIFY;
                    end else begin
                        //TESTING MULTIPLE ENTRY
                        //GJ_CHI_RBM_111213>>>>
                        VendRec.RESET;
                        if VendRec.FINDFIRST then
                            repeat
                                CLEAR(AmountVal);
                                GenJrnlLine.RESET;
                                GenJrnlLine.SETCURRENTKEY("Voucher Type", "Document No.", "Account No.");
                                GenJrnlLine.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BPV);
                                GenJrnlLine.SETRANGE("Document No.", "Document No.");
                                GenJrnlLine.SETRANGE("Account No.", VendRec."No.");
                                if GenJrnlLine.FIND('-') then begin
                                    repeat
                                        if GenJrnlLine."Currency Code" <> '' then
                                            AmountVal += ABS(GenJrnlLine.Amount)
                                        else
                                            AmountVal += ABS(GenJrnlLine."Amount (LCY)");
                                    until GenJrnlLine.NEXT = 0;
                                    GenJrnlLine2.Reset();
                                    GenJrnlLine2.SETRANGE("Journal Template Name", "Journal Template Code");
                                    GenJrnlLine2.SETRANGE("Journal Batch Name", "Journal Batch Name");
                                    IF GenJrnlLine2.FindLast() THEN
                                        LineNo := GenJrnlLine2."Line No." + 100;
                                    //PK On 02.05.2021<<
                                    GenJrnlLine2.VALIDATE("Journal Template Name", "Journal Template Code");
                                    GenJrnlLine2.VALIDATE("Journal Batch Name", "Journal Batch Name");
                                    GenJrnlLine2.VALIDATE("Document No.", PostedVoucherHeader."Document No.");
                                    GenJrnlLine2.VALIDATE("Line No.", LineNo);
                                    //GenJrnlLine2.VALIDATE("Posting Date","Posting Date");
                                    GenJrnlLine2.VALIDATE("Posting Date", TODAY); //RKD Modified
                                    GenJrnlLine2.VALIDATE("Voucher Type", GenJrnlLine2."Voucher Type"::BPV);
                                    GenJrnlLine2.VALIDATE("Account Type", "Account Type");
                                    if GenJrnlLine2."Account Type" = GenJrnlLine2."Account Type"::Customer then
                                        GenJrnlLine2.VALIDATE("Responsibility Center", PostedVoucherHeader."Responsibility Center");
                                    GenJrnlLine2.VALIDATE("Account No.", "Account No.");
                                    GenJrnlLine2.VALIDATE("Voucher No.", "Document No.");//SAA3.0
                                    GenJrnlLine2.VALIDATE("Currency Code", "Currency Code");
                                    GenJrnlLine2.VALIDATE("Currency Factor", "Currency Factor");
                                    CALCFIELDS("Amount (LCY)");
                                    CALCFIELDS(Amount);
                                    GenJrnlLine2.VALIDATE("Credit Amount", ABS(AmountVal));
                                    GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                                    GenJrnlLine2.VALIDATE("Source Code", "Source Code");
                                    GenJrnlLine2."Import File No." := PostedVoucherHeader."Import File No.";
                                    GenJrnlLine2."Clearing File No." := PostedVoucherHeader."Clearing File No.";

                                    IF GenJrnlLine."Capex No." <> '' THEN
                                        GenJrnlLine2.VALIDATE("Capex No.", GenJrnlLine."Capex No."); //SAA3.0
                                    IF GenJrnlLine."Capex Line No." <> 0 THEN
                                        GenJrnlLine2.VALIDATE("Capex Line No.", GenJrnlLine."Capex Line No."); //SAA3.0
                                                                                                               //Message('15..%1..%2', GenJrnlLine."Capex No.", GenJrnlLine."CWIP No.");


                                    GenJrnlLine2."CWIP No." := GenJrnlLine."CWIP No.";
                                    //GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.",GenJrnlLine."IC Partner G/L Acc. No.");//SAA3.0
                                    GenJrnlLine2.VALIDATE("IC Partner Code", "IC Partner Code");
                                    GenJrnlLine2.VALIDATE("IC Partner G/L Acc. No.", "IC Partner G/L Acc. No.");//SAA3.0
                                                                                                                //GenJrnlLine2.VALIDATE("Voucher Type",GenJrnlLine2."Voucher Type" ::BPV);
                                    GenJrnlLine2."Created By" := PostedVoucherHeader."Created By";
                                    GenJrnlLine2."Created By Name" := PostedVoucherHeader."Created By Name";
                                    GenJrnlLine2."Created Date" := PostedVoucherHeader."Created Date";
                                    GenJrnlLine2."Created Time" := PostedVoucherHeader."Created Time";
                                    GenJrnlLine2."Modified By" := PostedVoucherHeader."Modified By";
                                    GenJrnlLine2."Modified By Name" := PostedVoucherHeader."Modified By Name";
                                    GenJrnlLine2."Modified Date" := PostedVoucherHeader."Modified Date";
                                    GenJrnlLine2."Modified Time" := PostedVoucherHeader."Modified Time";
                                    GenJrnlLine2."Posted By" := PostedVoucherHeader."Posted By";
                                    GenJrnlLine2."Posted By Name" := PostedVoucherHeader."Posted By Name";
                                    GenJrnlLine2."Posted Date" := PostedVoucherHeader."Posted Date";
                                    GenJrnlLine2."Posted Time" := PostedVoucherHeader."Posted Time";
                                    // SAA 3.0 >>
                                    GenJrnlLine2."Teller / Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                                    GenJrnlLine2."Teller / Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                                    GenJrnlLine2."WHT %" := GenJrnlLine."WHT %";
                                    GenJrnlLine2."WHT Account" := GenJrnlLine."WHT Account";
                                    GenJrnlLine2."WHT Group" := GenJrnlLine."WHT Group";
                                    GenJrnlLine2."WHT Amount" := GenJrnlLine."WHT Amount";
                                    GenJrnlLine2."WHT Amount(LCY)" := GenJrnlLine."WHT Amount(LCY)";
                                    GenJrnlLine2."WHT Amount (Base)" := GenJrnlLine."WHT Amount (Base)";
                                    GenJrnlLine2."WHT Amount(LCY) (Base)" := GenJrnlLine."WHT Amount(LCY) (Base)";
                                    GenJrnlLine2."Cheque No." := PostedVoucherHeader."Teller / Cheque No.";
                                    GenJrnlLine2."Cheque Date" := PostedVoucherHeader."Teller / Cheque Date";
                                    GenJrnlLine2."Bank Name" := PostedVoucherHeader."Bank Name";
                                    GenJrnlLine2."Teller Bank Name" := PostedVoucherHeader."Teller Bank Name";
                                    GenJrnlLine2."Payment Mode" := PostedVoucherHeader."Payment Mode";
                                    GenJrnlLine2."Collected By Name" := PostedVoucherHeader.ToBeCollectedBy;
                                    //GenJrnlLine2.VALIDATE("Staff Code",GenJrnlLine."Staff Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 1 Code", PostedVoucherHeader."Shortcut Dimension 1 Code");
                                    GenJrnlLine2.VALIDATE("Shortcut Dimension 2 Code", PostedVoucherHeader."Shortcut Dimension 2 Code");
                                    GenJrnlLine2.VALIDATE("Dimension Set ID", PostedVoucherHeader."Dimension Set ID");//B2BDim
                                    GenJrnlLine2.PaymentSettlementOf := PostedVoucherHeader.PaymentSettlementOf;
                                    GenJrnlLine2.VALIDATE("Document Type", 1);
                                    //GenJrnlLine2.VALIDATE("Fixed Asset Type",GenJrnlLine."Fixed Asset Type");
                                    //GenJrnlLine2.VALIDATE("Description 2",PostedVoucherHeader.Narration);
                                    /*
                                   GenJrnlLine2."Description 2" :=
                                     COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),1,MAXSTRLEN(GenJrnlLine2."Description 2"));
                                   GenJrnlLine2."Description 3" :=
                                     COPYSTR(STRSUBSTNO(PostedVoucherHeader.Narration),MAXSTRLEN(GenJrnlLine2."Description 2")+1,
                                       MAXSTRLEN(GenJrnlLine2."Description 3"));
                                    */
                                    GenJrnlLine2.VALIDATE(Narration, PostedVoucherHeader.Narration);
                                    GenJrnlLine2.VALIDATE(Description, GenJrnlLine.Description);
                                    GenJrnlLine2.VALIDATE("Description 2", GenJrnlLine."Description 2");
                                    GenJrnlLine2.VALIDATE("Description 3", GenJrnlLine."Description 3");

                                    GenJrnlLine2."Authorised By" := ApprovalEntry."Approver ID";
                                    GenJrnlLine2."Batched By" := ApprovalEntry."Sender ID";
                                    GenJrnlLine2."Collected By Name" := ToBeCollectedBy;
                                    GenJrnlLine2."Multiple Batch" := GenJrnlLine."Multiple Batch";//SAA3.0
                                    //FIX28May2021>>
                                    GenJrnlLine2."WHT Account Holder Type" := GenJrnlLine2."WHT Account Holder Type"::Vendor;
                                    GenJrnlLine2."WHT Account Holder" := VendRec."No.";
                                    //FIX28May2021<<
                                    GenJrnlLine2.INSERT;
                                end;
                            until VendRec.NEXT = 0;
                        //GJ_CHI_RBM_111213<<<<
                        //TESTING MULTIPLE ENTRY

                    end;

                    GenJrnlLine2.RESET;
                    GenJrnlLine2.SETRANGE("Voucher Type", GenJrnlLine."Voucher Type"::BPV);
                    GenJrnlLine2.SETRANGE("Document No.", PostedVoucherHeader."Document No.");
                    GenJnlPost.CopyDocumentNo(GenJrnlLine."Document No.", DocNo);//PJ-PK
                    Commit();
                    GenJnlPost.Preview(GenJrnlLine2);//PK on 01.20.21 
                    //Message('%1', GenJrnlLine2."Line No.");
                    CLEAR(GenJnlPost);
                    //end;


                end;
        end;
        DelLineGenJrnlLine.RESET;
        //DelLineGenJrnlLine.SetRange("Journal Template Name", "Journal Template Code");
        //DelLineGenJrnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        DelLineGenJrnlLine.SetRange("Voucher No.", Rec."Document No.");
        If DelLineGenJrnlLine.FINDSET then
            DelLineGenJrnlLine.DeleteAll();
    end;

    var
        PostedVoucherHeader: Record "Posted Voucher Header" temporary;
        PostVouchHd: Record "Posted Voucher Header" temporary;
        XmlFile: File;
        OutputStream: OutStream;
        VendLed: Record "Vendor Ledger Entry";
        BankAccount: Record "Bank Account";
        ShortCutDimCode: Array[8] of Code[20];

        GLSetup: Record "General Ledger Setup";
        GenJrnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        NoSeriesMgt: Codeunit NoSeriesManagement;
        LineNo: Integer;
        GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
        GenJrnlLine2: Record "Gen. Journal Line";//PK-GJ2
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        TempApprovalEntry: Record "Approval Entry" temporary;
        DimMgt: Codeunit DimensionManagement;
        DocNo: Code[20];
        GLEntry: Record "G/L Entry";
        GLPosted: Boolean;
        //GenJnlPost: Codeunit "Gen. Jnl.-Post+Print_Custimize"; //PJ
        GenJnlPost: Codeunit "Gen. Jnl.-Post2"; //PJ
        GenJnlPostBatch2: Codeunit "Gen. Jnl.-Post Batch2"; //PJ
        GLEntryRec: Record "G/L Entry";
        TEXT50000: Label 'This voucher has been already posted with posted Document No. %1.';
        d1: Code[20];
        d2: Code[20];
        MaintenanceRec: Record Maintenance;

    procedure movedimensionstotemp();
    begin

        //Move dimensions to temp file
        /*FromJnlLineDim.SETRANGE(FromJnlLineDim."Table ID",81);
        FromJnlLineDim.SETRANGE(FromJnlLineDim."Journal Template Name",GenJrnlLine."Journal Template Name");
        FromJnlLineDim.SETRANGE(FromJnlLineDim."Journal Batch Name",GenJrnlLine."Journal Batch Name");
        FromJnlLineDim.SETRANGE(FromJnlLineDim."Journal Line No.",GenJrnlLine."Line No.");
        FromJnlLineDim.SETRANGE(FromJnlLineDim."Document No.",GenJrnlLine."Document No.");
        IF FromJnlLineDim.FINDSET THEN BEGIN
          REPEAT
           tempJournalLineDimension.INIT;
           tempJournalLineDimension.TRANSFERFIELDS(FromJnlLineDim);
           IF GenJrnlLine."Allocated Amt. (LCY)" = 0 THEN
             tempJournalLineDimension."Document No.":=PostedVoucherHeader."Document No." ELSE
           tempJournalLineDimension."Document No." := GenJrnlLine."Document No.";
           tempJournalLineDimension.INSERT;
         UNTIL FromJnlLineDim.NEXT = 0;
        END;
        *///CHI2018

    end;

    procedure movedimensionstoactual();
    begin

        //Move dimensions in temp to new journal line dimensions
        /*tempJournalLineDimension.SETRANGE(tempJournalLineDimension."Table ID",81);
        tempJournalLineDimension.SETRANGE(tempJournalLineDimension."Journal Template Name",GenJrnlLine."Journal Template Name");
        tempJournalLineDimension.SETRANGE(tempJournalLineDimension."Journal Batch Name",GenJrnlLine."Journal Batch Name");
        tempJournalLineDimension.SETRANGE(tempJournalLineDimension."Journal Line No.",GenJrnlLine."Line No.");
        tempJournalLineDimension.SETRANGE(tempJournalLineDimension."Document No.",PostedVoucherHeader."Document No.");
        IF tempJournalLineDimension.FINDSET THEN BEGIN
          REPEAT
           JournalLineDimension.INIT;
           JournalLineDimension.TRANSFERFIELDS(tempJournalLineDimension);
           //JournalLineDimension."Document No.":=PostedVoucherHeader."Document No.";
           IF JournalLineDimension.INSERT THEN;
         UNTIL tempJournalLineDimension.NEXT = 0;
        END;
        *///CHI2018

    end;





    procedure Getuserid(parUSERID: Code[30]): Code[10];
    var
        winIDtext: Text[50];
        CompanyInfo: Record "Company Information";
    begin
        if STRLEN(parUSERID) > 10 then begin
            //CompanyInfo.GET;
            //winIDtext := CompanyInfo."Domain ID"+'\'+parUSERID;
            //winlogin.SETFILTER(winlogin.ID,winIDtext);
            //IF winlogin.FINDFIRST THEN
            //BEGIN
            if (STRPOS(parUSERID, '.') > 0) and (STRLEN(COPYSTR(parUSERID, 1, STRPOS(parUSERID, '.'))) > 10) then
                exit(COPYSTR(parUSERID, 1, 8) + '.' + COPYSTR(parUSERID, STRPOS(parUSERID, '.') + 1, 1))
            else
                if (STRPOS(parUSERID, '.') > 0) and (STRLEN(COPYSTR(parUSERID, 1, STRPOS(parUSERID, '.'))) < 10) then
                    exit(COPYSTR(parUSERID, 1, 10))
                else
                    exit(COPYSTR(parUSERID, 1, 10));
            //END ELSE
            //  EXIT(COPYSTR(parUSERID,1,10));

        end else
            exit(parUSERID);
    end;
}

