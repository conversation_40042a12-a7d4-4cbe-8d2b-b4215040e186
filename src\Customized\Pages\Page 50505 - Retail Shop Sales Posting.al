page 50505 "Retail Shop Sales Posting"
{
    // version CHI6.0,POS

    Caption = 'Retail Shop Sales Posting';
    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = Card;
    ApplicationArea = all;
    UsageCategory = Documents;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field(DateFilter1; DateFilter1)
                {
                    Caption = 'Date Filter';
                    trigger OnValidate()
                    var
                        AppliMange: Codeunit "Filter Tokens";
                    begin
                        AppliMange.MakeDateFilter(DateFilter1);
                    end;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action(List)
                {
                    Caption = 'List';

                    trigger OnAction();
                    var
                        UserMgt: Codeunit "User Setup Management";
                    begin
                        if DateFilter1 = '' then
                            ERROR(Text50201);

                        //IF PDate = 0D THEN
                        //  ERROR(Text50201);

                        //GETAmountIncVAT;

                        SalesHeader.RESET;
                        //SalesHeader.SETRANGE("Posting Date",PDate);
                        SalesHeader.SETFILTER("Posting Date", DateFilter1);
                        SalesHeader.SETFILTER("POS Window", '%1', true);
                        SalesHeader.SetFilter("Order Tracking", '%1|%2', SalesHeader."Order Tracking"::"Completely Shipped", SalesHeader."Order Tracking"::"Partially Shipped");
                        if UserMgt.GetSalesFilter <> '' then
                            SalesHeader.SetRange("Responsibility Center", UserMgt.GetSalesFilter);
                        //SalesHeader.SETRANGE(Status, SalesHeader.Status::Released);

                        PAGE.RUNMODAL(PAGE::"Sales Order List", SalesHeader);
                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';

                    trigger OnAction();
                    var
                        PurchaseHeader: Record "Purchase Header";
                        ApprovalMgt: Codeunit "Approvals Mgmt.";
                        SalesinvoiceHeader: Record "Sales Invoice Header";
                        UserMgt: Codeunit "User Setup Management";
                    begin
                        Usersetup.GET(USERID);
                        BuildFilter := RespCentFilter.BuildRespCentFilter2();
                        //SAA3.0 >>
                        //IF NOT Usersetup."Post Multiple Rebate Cr.Memos" THEN
                        //ERROR(Text50202);
                        //SAA3.0 <<

                        Postingtime := TIME;

                        //IF PDate = 0D THEN
                        //ERROR(Text50201);
                        if DateFilter1 = '' then
                            ERROR(Text50201);
                        if (Postingtime > 170000T) and (Postingtime < 173000T) then
                            ERROR(Text50203);

                        SalesHeaderRec.RESET;
                        SalesHeaderRec.SETFILTER("POS Window", '%1', true);
                        //SalesHeaderRec.SETFILTER(Status, '%1', SalesHeaderRec.Status::Released);
                        //SalesHeaderRec.SETRANGE("Posting Date",PDate);
                        SalesHeaderRec.SETFILTER("Posting Date", DateFilter1);
                        if UserMgt.GetSalesFilter <> '' then
                            SalesHeader.SetRange("Responsibility Center", UserMgt.GetSalesFilter);

                        //SalesHeaderRec.SETFILTER("No.", '<>%1', 'SO1817063'); //to allow other invoices to post
                        //SalesHeaderRec.SetRange("Order Tracking", SalesHeaderRec."Order Tracking"::"Completely Shipped");
                        SalesHeaderRec.SetFilter("Order Tracking", '%1|%2', SalesHeader."Order Tracking"::"Completely Shipped", SalesHeader."Order Tracking"::"Partially Shipped");
                        if SalesHeaderRec.FINDSET then begin
                            repeat
                                //CODEUNIT.RUN(CODEUNIT::"Sales-Post + Print",SalesHeaderRec);
                                SalesHeaderRec.Invoice := true;
                                SalesHeaderRec.Status := SalesHeaderRec.Status::Released;
                                SalesLine.reset;
                                salesline.SetRange("Document Type", SalesHeaderRec."Document Type");
                                SalesLine.SetRange("Document No.", SalesHeaderRec."No.");
                                if SalesLine.FindSet() then
                                    repeat
                                        SalesLine."Qty. to Invoice" := SalesLine."Quantity Shipped" - SalesLine."Quantity Invoiced";
                                        SalesLine."Qty. to Invoice (Base)" := SalesLine."Qty. Shipped (Base)" - SalesLine."Qty. Invoiced (Base)";
                                        SalesLine.Modify();
                                    until SalesLine.Next() = 0;
                                SalesinvoiceHeader.Reset();
                                SalesinvoiceHeader.SetRange("Order No.", SalesHeaderRec."No.");
                                if not SalesinvoiceHeader.FindFirst() then
                                    SalesPost.RUN(SalesHeaderRec);
                                COMMIT;
                            until SalesHeaderRec.NEXT = 0;
                        end else
                            ERROR(Text50200);
                    end;
                }
                action("P&ost2")
                {
                    Caption = 'P&ost2';
                    ShortCutKey = 'F11';

                    trigger OnAction();
                    var
                        PurchaseHeader: Record "Purchase Header";
                        ApprovalMgt: Codeunit "Approvals Mgmt.";
                        SalesinvoiceHeader: Record "Sales Invoice Header";
                        UserMgt: Codeunit "User Setup Management";
                    begin
                        Usersetup.GET(USERID);
                        BuildFilter := RespCentFilter.BuildRespCentFilter2();
                        //SAA3.0 >>
                        //IF NOT Usersetup."Post Multiple Rebate Cr.Memos" THEN
                        //ERROR(Text50202);
                        //SAA3.0 <<

                        Postingtime := TIME;

                        //IF PDate = 0D THEN
                        //ERROR(Text50201);
                        if DateFilter1 = '' then
                            ERROR(Text50201);
                        if (Postingtime > 170000T) and (Postingtime < 173000T) then
                            ERROR(Text50203);

                        SalesHeaderRec.RESET;
                        SalesHeaderRec.SETFILTER("POS Window", '%1', true);
                        //SalesHeaderRec.SETFILTER(Status, '%1', SalesHeaderRec.Status::Released);
                        //SalesHeaderRec.SETRANGE("Posting Date",PDate);
                        SalesHeaderRec.SETFILTER("Posting Date", DateFilter1);
                        if UserMgt.GetSalesFilter <> '' then
                            SalesHeader.SetRange("Responsibility Center", UserMgt.GetSalesFilter);

                        //SalesHeaderRec.SETFILTER("No.", '<>%1', 'SO1817063'); //to allow other invoices to post
                        //SalesHeaderRec.SetRange("Order Tracking", SalesHeaderRec."Order Tracking"::"Completely Shipped");
                        SalesHeaderRec.SetFilter("Order Tracking", '%1', SalesHeader."Order Tracking"::"Partially Invoiced");
                        if SalesHeaderRec.FINDSET then begin
                            repeat
                                //CODEUNIT.RUN(CODEUNIT::"Sales-Post + Print",SalesHeaderRec);
                                SalesHeaderRec.Invoice := true;
                                SalesHeaderRec.Status := SalesHeaderRec.Status::Released;
                                SalesLine.reset;
                                salesline.SetRange("Document Type", SalesHeaderRec."Document Type");
                                SalesLine.SetRange("Document No.", SalesHeaderRec."No.");
                                SalesLine.SetFilter("Qty. to Invoice", '%1', 0);
                                if SalesLine.FindSet() then
                                    repeat
                                        SalesLine."Qty. to Invoice" := SalesLine."Quantity Shipped" - SalesLine."Quantity Invoiced";
                                        SalesLine."Qty. to Invoice (Base)" := SalesLine."Qty. Shipped (Base)" - SalesLine."Qty. Invoiced (Base)";
                                        SalesLine.Modify();
                                    until SalesLine.Next() = 0;

                                SalesPost.RUN(SalesHeaderRec);
                                COMMIT;
                            until SalesHeaderRec.NEXT = 0;
                        end else
                            ERROR(Text50200);
                    end;
                }
                action("Post and &Print")
                {
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';
                    Visible = false;

                    trigger OnAction();
                    var
                        PurchaseHeader: Record "Purchase Header";
                        ApprovalMgt: Codeunit "Approvals Mgmt.";
                    begin
                        if RebatePeriod = '' then
                            ERROR(Text50201);

                        SalesHeaderRec.RESET;
                        SalesHeaderRec.SETFILTER(Select, '%1', true);
                        SalesHeaderRec.SETFILTER(Status, '%1', SalesHeaderRec.Status::Released);
                        if not SalesHeaderRec.FINDSET then
                            ERROR(Text50200, RebatePeriod);

                        SalesHeaderRec.RESET;
                        SalesHeaderRec.SETFILTER(Select, '%1', true);
                        SalesHeaderRec.SETFILTER(Status, '%1', SalesHeaderRec.Status::Released);
                        if SalesHeaderRec.FINDSET then
                            repeat
                                CODEUNIT.RUN(CODEUNIT::"Sales-Post + Print", SalesHeaderRec);
                            until SalesHeaderRec.NEXT = 0;
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        DateFilter1OnFormat;
    end;

    trigger OnOpenPage();
    var
        UserMgt: Codeunit "User Setup Management";
    begin
        //SAA3.0 >>

        Usersetup.GET(USERID);
        if not Usersetup."Retail User" then
            ERROR(Text50202);

        //SAA3.0 <<
    end;

    var
        RebatePeriod: Code[10];
        SalesHeader: Record "Sales Header";
        SalesHeaderRec: Record "Sales Header";
        Text50200: Label '"No Retail shop Sales Order  with Status Release "';
        Text50201: Label 'Posting date must not be blank.';
        SalesSetup: Record "Sales & Receivables Setup";
        SalesLine: Record "Sales Line";
        DocType: Option Quote,"Order",Invoice,"Credit Memo","Blanket Order","Return Order";
        DocNo: Code[20];
        TempVATAmountLine0: Record "VAT Amount Line" temporary;
        TempVATAmountLine1: Record "VAT Amount Line" temporary;
        SalesPost: Codeunit "Sales-Post-Copy";
        Usersetup: Record "User Setup";
        Text50202: Label 'You do not have permission to post multiple sales order';
        PDate: Date;
        BuildFilter: Text[1024];
        RespCentFilter: Codeunit "Responsibility Center Filter";
        Postingtime: Time;
        Text50203: Label 'Posting Time invalid';
        DateFilter1: Text[1024];
        itemrec: Record Item;
        Text50204: Label 'Please enter Date Filter';

    local procedure DateFilter1OnFormat();
    begin
        itemrec.SETFILTER("Date Filter", DateFilter1);
        DateFilter1 := itemrec.GETFILTER("Date Filter");
    end;
}

