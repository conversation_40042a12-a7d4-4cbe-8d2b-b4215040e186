tableextension 50053 SalesCrMemoHeader extends "Sales Cr.Memo Header"
{
    fields
    {
        field(50008; "Rebate Period Code"; Code[10])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50028; "Reason Codes"; enum ReasonCodes)
        {
            DataClassification = CustomerContent;
        }
        field(50029; "The Number"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Printable Comment 1"; Text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Printable Comment 2"; Text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            //B2B.P.K.T
        }
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;

        }


        modify("Sell-to Customer No.")
        {
            trigger OnAfterValidate()
            var

            BEGIN
                IF (CustGRec.get("Sell-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
            END;

        }
        modify("Bill-to Customer No.")
        {
            trigger OnAfterValidate()
            var

            BEGIN
                IF (CustGRec.get("Bill-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
            END;

        }

    }
    var
        myInt: Integer;
        CustGRec: Record customer;

}