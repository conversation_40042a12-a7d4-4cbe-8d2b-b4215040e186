codeunit 50038 "CHI Retail Integrations"
{
    TableNo = "Job Queue Entry";
    Permissions = tabledata "Retail sales transaction Log" = rim,
                  tabledata "Retail Banks" = r,
                  tabledata "Retail Price Change Log" = rm,
                  tabledata "Retail Stock update" = rm,
                  tabledata "Retail Outlet Areas" = rm,
                  tabledata "Retail Products Log" = rim,
                  tabledata "POSPaymentLog" = rim,
                  tabledata "POSSaleslines" = rim,
                  tabledata "Sales Header" = rimd,
                  tabledata "Sales Line" = rimd,
                  tabledata "Sales Invoice Header" = rimd,
                  tabledata "Sales Invoice Line" = rimd,
                  tabledata "Bank Account Ledger Entry" = rimd,
                  tabledata "Sales Shipment Header" = rimd,
                  tabledata "Sales shipment Line" = rimd,
                  tabledata "Sales & Receivables Setup" = r,
                  tabledata "Inventory Setup" = r,
                  tabledata "Item Barcodes Unit of Measure" = rimd,
                  tabledata "Item Expiry Date Upload" = rimd;


    trigger OnRun()
    begin
        case Rec."Parameter String" of
            'FETCHFGPRODUCTS':
                FetchItemsToRetailProduct();
            'PUSHNEWITEMS':
                CreatePayloadFromRetailProduct();
            'FETCHSALESTRANSACTIONS':
                GetSalesfromAPI();
            'CREATERETAILSALESORDERS':
                CreateSalesTransactions();
            //PostPOSSales();
            'POSTRETAILSALES':
                PostSalesTransactions();
            'ASSIGNOUTLETS':
                AssignOutletID();
            'UPDATENEWPRICE':
                UpdateProductPrices();
            'UPDATESTOCKS':
                PostStock();
            'UPDAETRETAILSTOCKREC':
                StockUpdate();


        end;
    end;

    procedure CreateSalesTransactions()
    Var
        salesHeader: Record "Sales Header";
        Salesline: Record "Sales Line";
        CustRec: Record customer;
        invsetup: record "Inventory Setup";
        DateValue: Date;
        POSSalesLine: Record POSSalesLines;
        itemrec: record item;
        itemuom, itemuom2 : Record "Item Unit of Measure";
        bankacct: Record "Bank Account";
        POSPayments: record POSPaymentLog;
        amt, Lowestunitqty, wholesaleunitqty : Decimal;
        NoSeriesMgt: Codeunit NoSeriesManagement;
        SalesSetup: Record "Sales & Receivables Setup";
        custresplines: record "Customer Resp. Cent. Lines";
        respcentre: code[20];
        POSCash, POSBank, Firstentry : boolean;
        itemNo: Code[20];
        itembarcode: Record "Item Barcodes Unit of Measure";
    begin
        // Filter for unprocessed transactions
        SalesTrans.SetRange("Processed", false);
        SalesTrans.SetRange("Sales Order Created", false);
        if SalesTrans.FindSet() then begin
            repeat
                //Create Sales Header from POS Sales Header
                respcentre := '';
                DateValue := 0D;
                POSSalesLine.reset;
                POSSalesLine.SetRange("Transaction ID", SalesTrans."Transaction ID");
                POSSalesLine.SetRange("Document Type", SalesTrans."Document Type");
                if POSSalesLine.FindSet() then begin
                    custrec.setrange("Clearwox Outlet ID", SalesTrans."Outlet ID");
                    if custrec.findfirst then begin
                        custresplines.SetRange("Customer No.", custrec."No.");
                        if custresplines.findfirst then
                            respcentre := custresplines."Resp. Center Code";
                        salesHeader.init;
                        salesHeader."No." := '';
                        salesheader."POS Window" := true;
                        salesheader.Validate("Document Type", SalesTrans."Document Type");
                        DateValue := DT2Date(SalesTrans.Date);
                        salesheader.validate("Posting Date", DateValue);
                        salesHeader.Validate("External Document No.", SalesTrans."Transaction ID");
                        salesHeader.Validate("User Wise Resp Centr", respcentre);
                        salesHeader.Validate("Sell-to Customer No.", CustRec."No.");

                        salesHeader.validate(Narration, SalesTrans."Transaction ID" + '/' + SalesTrans."Receipt No");
                        // Get Next Available No. from No. Series
                        /* SalesSetup.Get();
                        salesHeader."No. Series" := SalesSetup."Order Nos.";

                        if SalesHeader."No." = '' then begin
                            NoSeriesMgt.InitSeries(SalesSetup."Order Nos.", SalesSetup."Order Nos.", 0D, SalesHeader."No.", SalesHeader."No. Series");
                        end; */
                        invsetup.Get();
                        salesHeader.Validate("Location code", invsetup."POS Central Location");
                        salesHeader.Validate("Responsibility Center", respcentre); //  CustRec."Responsibility Center");
                        salesHeader.insert(true);
                        salesheader.validate("Document Date", today);
                        // salesheader.validate(Ship, true);
                        //salesheader.validate(Invoice, true);

                        if salesheader."Document Type" = salesHeader."Document Type"::"Credit Memo" then begin
                            salesheader."Cr. Memo Reason Type" := salesHeader."Cr. Memo Reason Type"::Others;
                            salesHeader."Printable Comment 1" := 'Sales Refund' + '/' + SalesTrans."Transaction ID" + '/' + SalesTrans."Receipt No";
                            // salesheader.validate(Ship, false);
                            //salesheader.validate(Invoice, false);
                        end;

                        salesHeader.Validate("Shortcut Dimension 1 Code", CustRec."Global Dimension 1 Code");
                        salesHeader.Validate("Shortcut Dimension 2 Code", CustRec."Global Dimension 2 Code");
                        //salesHeader.Validate("Responsibility Center", CustRec."Responsibility Center");
                        //CustRec."Responsibility Center");

                        salesHeader.Validate("Ship-to Code", '2');
                        salesHeader.validate("Created By", 'POSAPI');
                        salesHeader.validate("Created Date", CurrentDateTime);
                        salesheader.validate("Loading Slip Required", false);
                        salesHeader.modify;

                        //create sales line from POS Sales Line
                        POSSalesLine.reset;
                        POSSalesLine.SetRange("Transaction ID", SalesTrans."Transaction ID");
                        POSSalesLine.SetRange("Document Type", SalesTrans."Document Type");
                        if POSSalesLine.FindSet() then
                            repeat

                                //fetch item code using clearwox ID
                                itemrec.Reset();
                                itemrec.SetRange("Clearwox Item ID", POSSalesLine."Item ID");
                                if itemrec.FindFirst() then
                                    itemNo := itemrec."No."

                                else begin

                                    itembarcode.Reset();
                                    itembarcode.SetRange("Clearwox Product ID", POSSalesLine."Item ID");
                                    if itembarcode.FindFirst() then begin
                                        itemNo := itembarcode."Item No.";
                                    end;
                                end;


                                wholesaleunitqty := 0;
                                Lowestunitqty := 0;
                                Salesline.init;
                                salesline.Validate("Document Type", salesHeader."Document Type");
                                salesline.Validate("Document No.", salesHeader."No.");
                                Salesline.Validate("Line No.", POSSalesLine."Line No.");

                                salesline.Validate("Sell-to Customer No.", CustRec."No.");
                                Salesline.Validate(Type, 2);
                                salesline.Validate("Item Category Code", 'FG');
                                Salesline.validate("No.", itemNo);
                                Salesline.validate(Quantity, POSSalesLine.Quantity);
                                Salesline.Validate("Line Discount Amount", POSSalesLine.Discount);

                                Salesline.validate("Unit of Measure code", POSSalesline.WholesalesID);

                                Salesline.validate("Unit Price", POSSalesLine.Price);
                                if POSSalesLine.Price = 0 then begin
                                    Salesline."Gift Item" := true;
                                    Salesline."Gift Item Quantity" := POSSalesLine.Quantity;
                                end;

                                if salesHeader."Document Type" = salesHeader."Document Type"::Order then begin
                                    Salesline.Validate("Qty. to Ship", POSSalesLine.Quantity);
                                    Salesline.Validate("Qty. to Invoice", POSSalesLine.Quantity);
                                end;
                                Salesline.Validate("POS Window", true);
                                invsetup.Get();
                                salesline.Validate("Location Code", invsetup."POS Central Location");



                                Salesline.insert;



                            // Salesline.modify;
                            // end;

                            until POSSalesLine.next = 0;
                        //itemuom.reset;
                        //itemuom.SetCurrentKey("Qty. per Unit of Measure");
                        //itemuom.Ascending(true);
                        // itemuom.SetRange("Item No.", itemrec."No.");
                        // itemuom.SetRange(itemuom.code, POSSalesline.WholesalesID);
                        // if itemuom.FindFirst() then
                        //     wholesaleunitqty := itemuom."Qty. per Unit of Measure";

                        // itemuom2.reset;
                        // itemuom2.SetCurrentKey("Qty. per Unit of Measure");
                        // itemuom2.Ascending(true);
                        // itemuom2.SetRange("Item No.", itemrec."No.");
                        // // itemuom2.SetRange(itemuom.code,POSSalesline.WholesalesID);
                        // if itemuom2.FindFirst() then
                        //     Lowestunitqty := itemuom2."Qty. per Unit of Measure";



                        amt := 0;
                        POSCash := false;
                        POSBank := false;
                        FirstEntry := true;
                        POSPayments.SetRange(TransactionID, SalesTrans."Transaction ID");
                        if POSPayments.findset then
                            repeat
                                if POSPayments.PaymentTypeID = '0' then begin
                                    salesHeader.Validate("POS Cash Amount", POSPayments.Amount);
                                    POSCash := true;
                                end;
                                if POSPayments.PaymentTypeID in ['1', '2', '3', '4'] then begin
                                    amt += POSPayments.Amount;
                                    if Firstentry then
                                        salesHeader."POS Bank Names" += POSPayments.AccountNo else
                                        salesHeader."POS Bank Names" += '/' + POSPayments.AccountNo;
                                    Firstentry := false;
                                    POSBank := true;
                                end;

                            until POSPayments.Next() = 0;

                        if (POSCash) and (POSBank) then
                            salesHeader.validate("POS Transaction Type", salesHeader."POS Transaction Type"::Both);
                        if (POSCash) and (not POSBank) then
                            salesHeader.validate("POS Transaction Type", salesHeader."POS Transaction Type"::Cash);
                        if (not POSCash) and (POSBank) then
                            salesHeader.validate("POS Transaction Type", salesHeader."POS Transaction Type"::Card);

                        if amt <> 0 then
                            salesHeader.Validate("POS Card Amount", amt);

                        salesHeader.Status := salesHeader.status::Released;
                        salesHeader.modify;

                    end;
                    SalesTrans."Sales Order Created" := true;
                    SalesTrans."Document No." := salesHeader."No.";
                    SalesTrans.modify;
                end;


            until SalesTrans.Next() = 0;
        end;// else
            // Message('No unprocessed sales transactions found.');
    end;

    procedure PostSalesTransactions()

    begin

        PostPOSSales();
    end;

    procedure GetSalesfromAPI()
    var
        HttpClient: HttpClient;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        Content: HttpContent;
        header, Header2 : HttpHeaders;
        ResponseText, AText, TText : Text;
        Jsonobj: JsonObject;
        Stoken: JsonToken;
        Code: Text[20];
        chiapiset: record "CHI API Setup";
    begin

        if chiapiset.get(1) then begin
            AText := '';
            Ttext := '';
            // Define the request
            //HttpRequestMessage.SetRequestUri('https://cloud.storeapp.biz/api/transactions/list'); //'
            HttpRequestMessage.SetRequestUri(chiapiset."API Endpoint1");
            HttpRequestMessage.Method := 'GET';
            HttpRequestMessage.GetHeaders(Header);
            AText := GetAccessDetails(chiapiset, 'Authorize');
            Header.Add('Authorization', Atext);
            // Header.Add('Authorization', 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAtNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuYW1lIjoiQURNSU5JU1RSQVRPUiIsInN1YiI6ImFkbWluIiwianRpIjoiYmU1MTI1OTktOTY2MC00NTFmLTljMzEtN2IzZDc3NjI0MGI0IiwiZXhwIjoxNzYzMTEzNDIzLCJpc3MiOiJodHRwczovL3N0b3JlYXBwLmJpeiIsImF1ZCI6Imh0dHBzOi8vc3RvcmVhcHAuYml6In0.RV_J-25B9R5LNxrF-De4EGc5ohDJ5Vqx76BAHaxnE3AS7hHp-jlalQT3og_sObGIzqlFo1JLaztv8cLgJl8ApA'); // Replace {{Key}} with the actual key
            HttpRequestMessage.GetHeaders(Header);
            // Header.Add('Tenant', 'demo');
            TText := GetAccessDetails(chiapiset, 'Tenant');
            Header.Add('Tenant', Ttext);
        end;
        //header.Add('Content-Type', 'application/json');

        // Set the content for the request
        // Content.WriteFrom(JsonPayloadtext);
        //Content.GetHeaders(Header);
        //Header.clear;
        //Header.Add('Content-Type', 'application/json');
        //HttpRequestMessage.Content := Content;

        // Send the HTTP request
        if HttpClient.Send(HttpRequestMessage, HttpResponseMessage) then begin

            //  if HttpResponseMessage.Content.GetHeaders('Content-Type', 'application/json') then
            HttpResponseMessage.Content.ReadAs(ResponseText);
            Jsonobj.ReadFrom(ResponseText);
            if Jsonobj.Get('success', Stoken) then
                IF Stoken.AsValue().AsBoolean() then begin
                    // Message('Response: %1', ResponseText);
                    ProcessSalesData(ResponseText);

                end;// else
                    //message('not successful');
        end;//else begin
            // Error('Failed to send request');
            //end;
    end;

    procedure PostToUrl2(): JsonObject
    var
        HttpClient: HttpClient;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        Content: HttpContent;
        header, Header2 : HttpHeaders;
        ResponseText: Text;
        JsonResponse: JsonObject;
        DataObject: JsonObject;
        ProductId: Text;
        JsonToken, PJsonToken : JsonToken;
        Code: Text[20];
        chiapiset: record "CHI API Setup";
        AText, Ttext : Text;

    begin
        if chiapiset.get(1) then begin
            AText := '';
            Ttext := '';

            HttpRequestMessage.SetRequestUri(chiapiset."API Endpoint2");
            //'https://demo.storeapp.biz/products'
            HttpRequestMessage.Method := 'POST';
            HttpRequestMessage.GetHeaders(Header);


            AText := GetAccessDetails(chiapiset, 'Authorize');
            Header.Add('Authorization', atext);
            HttpRequestMessage.GetHeaders(Header);

            TText := GetAccessDetails(chiapiset, 'Tenant');
            Header.Add('Tenant', TText);
        end;

        // Set the content for the request
        Content.WriteFrom(JsonPayloadtext);
        Content.GetHeaders(Header);
        Header.clear;
        Header.Add('Content-Type', 'application/json');
        HttpRequestMessage.Content := Content;

        // Send the HTTP request
        if HttpClient.Send(HttpRequestMessage, HttpResponseMessage) then begin


            HttpResponseMessage.Content.ReadAs(ResponseText);
            if JsonResponse.ReadFrom(ResponseText) then begin

                exit(JsonResponse);
            end;
        end else begin

        end;
    end;

    procedure CreatePayloadFromRetailProduct()
    var
        RetailProductRec: Record "Retail Products Log";
        RetailProductRec2: Record "Retail Products Log";
        JsonPayload, JPayload2 : JsonObject;
        PriceLevelsArray: JsonArray;
        WholesalesArray: JsonArray;
        PriceProfilesArray: JsonArray;
        PriceLevelObj: JsonObject;
        WholesaleObj: JsonObject;
        PriceProfileObj, IDObj, IDObj2 : JsonObject;
        ProdArray, PLArray, WHArray, PArray, IDArray : JsonArray;
        Prodjson, Respjson, datajson : JsonObject;
        jstoken, reptoken, IDToken, IDToken2 : JsonToken;
        JsonTextWriter, JsonTextWriter2, RespTxt, SuccessMessage, IDText, IDText2 : Text;
        IDNo: code[20];
        itemrec: record item;
        retailinvupdate: codeunit "Retail Inventory Update";
        FormulaItemArray: JsonArray;
        FormulaItemObj: JsonObject;
        BaseItemBarcode: Text;
        itemBarcode: Record "Item Barcodes Unit of Measure";
        JText: Text;
    begin
        //init
        JsonPayload.Add('code', '');
        JsonPayload.Add('description', '');
        JsonPayload.Add('genericName', '');
        JsonPayload.Add('manufacturerId', '');
        JsonPayload.Add('categoryId', '');
        JsonPayload.Add('cost', 0);
        JsonPayload.Add('unit', '');
        JsonPayload.Add('recurringQty', 0);
        JsonPayload.Add('markup', 0);
        JsonPayload.Add('price', 0);
        JsonPayload.Add('taxable', false);
        JsonPayload.Add('watched', false);
        JsonPayload.Add('online', false);
        JsonPayload.Add('rawMaterial', false);
        JsonPayload.Add('allowFractions', false);
        JsonPayload.Add('produce', false);
        JsonPayload.Add('priceLevels', '');
        JsonPayload.Add('Wholesales', '');
        JsonPayload.Add('priceProfiles', '');

        PriceLevelObj.Add('description', '');
        PriceLevelObj.Add('quantity', '');
        PriceLevelObj.Add('unit', '');
        PriceLevelObj.Add('price', '');
        WholesaleObj.add('description', '');
        WholesaleObj.add('quantity', '');
        WholesaleObj.add('code', '');
        WholesaleObj.add('price', '');
        FormulaItemObj.Add('productId', '');
        FormulaItemObj.Add('quantity', '');
        PriceProfileObj.Add('profileId', '');
        PriceProfileObj.Add('price', '');

        // Loop through records in the RetailProduct table with Status not equal to 'Posted'
        RetailProductRec.Reset();
        if RetailProductRec.FindSet() then begin
            repeat
                if (RetailProductRec.Status = RetailProductRec.status::New) or (RetailProductRec.Status = RetailProductRec.status::Error) and (RetailProductRec.Code <> '') then begin
                    // Initialize the main JSON object for each record
                    clear(JsonPayload);
                    Clear(JsonTextWriter);
                    JsonPayload.Add('code', RetailProductRec."Code");
                    JsonPayload.Add('description', RetailProductRec."Description");
                    JsonPayload.Add('genericName', RetailProductRec."Generic Name");
                    JsonPayload.Add('manufacturerId', RetailProductRec."Manufacturer ID");
                    JsonPayload.Add('categoryId', RetailProductRec."Category ID");
                    JsonPayload.Add('cost', RetailProductRec."Cost");
                    JsonPayload.Add('unit', RetailProductRec."Unit");
                    JsonPayload.Add('recurringQty', RetailProductRec."Recurring Qty");
                    JsonPayload.Add('markup', RetailProductRec."Markup");
                    JsonPayload.Add('price', RetailProductRec."Price");
                    JsonPayload.Add('taxable', RetailProductRec."Taxable");
                    JsonPayload.Add('watched', RetailProductRec."Watched");
                    JsonPayload.Add('online', RetailProductRec."Online");
                    JsonPayload.Add('rawMaterial', RetailProductRec."Raw Material");
                    JsonPayload.Add('produce', RetailProductRec."Produce");
                    JsonPayload.Add('allowFractions', RetailProductRec."Allow Fraction");

                    // Clear and recreate the arrays for each record
                    Clear(PriceLevelsArray);
                    Clear(WholesalesArray);
                    Clear(PriceProfilesArray);

                    // Add Price Level data
                    if RetailProductRec."PL Description" <> '' then begin
                        clear(PriceLevelObj);
                        PriceLevelsArray.Add(PriceLevelObj);
                    end;
                    JsonPayload.Add('priceLevels', PriceLevelsArray);
                    // Add Wholesale data
                    if RetailProductRec."WS Description1" <> '' then begin
                        clear(WholesaleObj);
                        WholesaleObj.Add('description', RetailProductRec."WS Description1");
                        WholesaleObj.Add('quantity', RetailProductRec."WS Quantity1");
                        WholesaleObj.Add('code', RetailProductRec.code);
                        WholesaleObj.Add('price', RetailProductRec."WS Price1");
                        WholesalesArray.Add(WholesaleObj);

                    end;

                    // Add Wholesale data
                    if RetailProductRec."WS Description" <> '' then begin
                        clear(WholesaleObj);
                        WholesaleObj.Add('description', RetailProductRec."WS Description");
                        WholesaleObj.Add('quantity', RetailProductRec."WS Quantity");
                        WholesaleObj.Add('code', RetailProductRec."WS Code");
                        WholesaleObj.Add('price', RetailProductRec."WS Price");
                        WholesalesArray.Add(WholesaleObj);

                    end;
                    JsonPayload.Add('Wholesales', WholesalesArray);

                    //add formular item
                    Clear(BaseItemBarcode);
                    Clear(FormulaItemObj);
                    Clear(FormulaItemArray);
                    if RetailProductRec."Formular-Item" = true then begin
                        RetailProductRec2.Reset();
                        RetailProductRec2.SetRange("Item No.", RetailProductRec."Item No.");
                        RetailProductRec2.SetRange("Formular-Item", false);
                        if RetailProductRec2.FindFirst() then begin
                            BaseItemBarcode := RetailProductRec2.Code;
                            FormulaItemObj.Add('productId', BaseItemBarcode);
                            FormulaItemObj.Add('quantity', RetailProductRec.Quantity);
                            FormulaItemArray.Add(FormulaItemObj);
                        end;
                    end;

                    JsonPayload.Add('formularItems', FormulaItemArray);




                    // Add Price Profile data

                    clear(PriceProfileObj);
                    Clear(ProdArray);
                    // Add the finalized object to the product array
                    ProdArray.Add(JsonPayload);

                    ProdArray.WriteTo(JsonTextWriter);
                    Clear(JsonPayloadtext);
                    // Remove the square brackets
                    JsonPayloadtext := CopyStr(JsonTextWriter, 2, StrLen(JsonTextWriter) - 2);

                    Clear(Respjson);
                    Respjson := PostToUrl2();

                    if Respjson.Get('message', reptoken) then begin
                        RetailProductRec.ResponseMessage := reptoken.AsValue().AsText();

                        if Respjson.Get('success', reptoken) then
                            RetailProductRec.ResponseSuccess := reptoken.AsValue().AsBoolean();
                        if RetailProductRec.ResponseSuccess then begin
                            if Respjson.get('data', reptoken) then begin

                                reptoken.WriteTo(IDText);
                                datajson.ReadFrom(IDText);
                                if datajson.Get('id', IDToken) then begin
                                    itemrec.setrange(BarCode, RetailProductRec.code);
                                    if itemrec.FindFirst() then begin
                                        itemrec."Clearwox Item ID" := idtoken.AsValue().AsText();
                                        itemrec.Modify();
                                    end;
                                    itemBarcode.SetRange(Barcode, RetailProductRec.Code);
                                    if itemBarcode.FindFirst() then begin
                                        itemBarcode."Clearwox Product ID" := idtoken.AsValue().AsText();
                                        itemBarcode.Modify();
                                    end;
                                    RetailProductRec.ProductID := idtoken.AsValue().AsText();
                                    RetailProductRec.Status := RetailProductRec.Status::Posted;
                                end;
                            end;

                        end else begin
                            RetailProductRec.Status := RetailProductRec.Status::Error;
                            idtext := retailinvupdate.GetProductfromAPI(RetailProductRec.Code);
                            IDObj.ReadFrom(idtext);
                            IF idobj.Get('data', IDToken) then begin
                                idtoken.WriteTo(IDText2);
                                IDObj2.ReadFrom(idtext2);
                                if idobj2.Get('id', IDToken2) then begin
                                    itemrec.SetRange(BarCode, RetailProductRec.Code);
                                    if itemrec.FindFirst() then begin
                                        itemrec."Clearwox Item ID" := IDToken2.AsValue().AsText();
                                        RetailProductRec.ProductID := IDToken2.AsValue().AsText();
                                        itemrec.modify;
                                        RetailProductRec.Status := RetailProductRec.Status::Posted;
                                    end;
                                end;

                            end;

                        end;

                        RetailProductRec.modify();

                    end;
                end;
            until RetailProductRec.Next() = 0;
        end;

    end;


    procedure UpdateItemInRetail()
    var
        RetailProductRec: Record "Retail Products Log";
        RetailProductRec2: Record "Retail Products Log";
        JsonPayload, JPayload2 : JsonObject;
        PriceLevelsArray: JsonArray;
        WholesalesArray: JsonArray;
        PriceProfilesArray: JsonArray;
        PriceLevelObj: JsonObject;
        WholesaleObj: JsonObject;
        PriceProfileObj, IDObj, IDObj2 : JsonObject;
        ProdArray, PLArray, WHArray, PArray, IDArray : JsonArray;
        Prodjson, Respjson, datajson : JsonObject;
        jstoken, reptoken, IDToken, IDToken2 : JsonToken;
        JsonTextWriter, JsonTextWriter2, RespTxt, SuccessMessage, IDText, IDText2 : Text;
        IDNo: code[20];
        itemrec: record item;
        retailinvupdate: codeunit "Retail Inventory Update";
        FormulaItemArray: JsonArray;
        FormulaItemObj: JsonObject;
        BaseItemBarcode: Text;
        itemBarcode: Record "Item Barcodes Unit of Measure";
        JText: Text;
        PriceChangeLog: Record "Retail Price Change Log";
    begin
        //init
        JsonPayload.Add('code', '');
        JsonPayload.Add('description', '');
        JsonPayload.Add('genericName', '');
        JsonPayload.Add('manufacturerId', '');
        JsonPayload.Add('categoryId', '');
        JsonPayload.Add('cost', 0);
        JsonPayload.Add('unit', '');
        JsonPayload.Add('recurringQty', 0);
        JsonPayload.Add('markup', 0);
        JsonPayload.Add('price', 0);
        JsonPayload.Add('taxable', false);
        JsonPayload.Add('watched', false);
        JsonPayload.Add('online', false);
        JsonPayload.Add('rawMaterial', false);
        JsonPayload.Add('produce', false);
        JsonPayload.Add('allowFractions', false);
        JsonPayload.Add('priceLevels', '');
        JsonPayload.Add('Wholesales', '');
        JsonPayload.Add('priceProfiles', '');

        PriceLevelObj.Add('description', '');
        PriceLevelObj.Add('quantity', '');
        PriceLevelObj.Add('unit', '');
        PriceLevelObj.Add('price', '');
        WholesaleObj.add('description', '');
        WholesaleObj.add('quantity', '');
        WholesaleObj.add('code', '');
        WholesaleObj.add('price', '');
        FormulaItemObj.Add('productId', '');
        FormulaItemObj.Add('quantity', '');
        PriceProfileObj.Add('profileId', '');
        PriceProfileObj.Add('price', '');

        RetailProductRec.Reset();
        RetailProductRec.SetRange(Status, RetailProductRec.Status::Pending);
        RetailProductRec.Setfilter(Code, '<>%1', '');
        RetailProductRec.SetFilter(RetailProductRec.ProductID, '<>%1', '');
        if RetailProductRec.FindSet() then begin
            repeat
                // if (RetailProductRec.Status = RetailProductRec.status::Pending) and (RetailProductRec.Code <> '') and (RetailProductRec.ProductID <> '') then begin

                clear(JsonPayload);
                Clear(JsonTextWriter);
                JsonPayload.Add('code', RetailProductRec."Code");
                JsonPayload.Add('description', RetailProductRec."Description");
                JsonPayload.Add('genericName', RetailProductRec."Generic Name");
                JsonPayload.Add('manufacturerId', RetailProductRec."Manufacturer ID");
                JsonPayload.Add('categoryId', RetailProductRec."Category ID");
                JsonPayload.Add('cost', RetailProductRec."Cost");
                JsonPayload.Add('unit', RetailProductRec."Unit");
                JsonPayload.Add('recurringQty', RetailProductRec."Recurring Qty");
                JsonPayload.Add('markup', RetailProductRec."Markup");
                JsonPayload.Add('price', RetailProductRec."Price");
                JsonPayload.Add('taxable', RetailProductRec."Taxable");
                JsonPayload.Add('watched', RetailProductRec."Watched");
                JsonPayload.Add('online', RetailProductRec."Online");
                JsonPayload.Add('rawMaterial', RetailProductRec."Raw Material");
                JsonPayload.Add('produce', RetailProductRec."Produce");
                JsonPayload.Add('allowFractions', RetailProductRec."Allow Fraction");

                Clear(PriceLevelsArray);
                Clear(WholesalesArray);
                Clear(PriceProfilesArray);

                if RetailProductRec."PL Description" <> '' then begin
                    clear(PriceLevelObj);
                    PriceLevelsArray.Add(PriceLevelObj);
                end;
                JsonPayload.Add('priceLevels', PriceLevelsArray);
                if RetailProductRec."WS Description1" <> '' then begin
                    clear(WholesaleObj);
                    WholesaleObj.Add('description', RetailProductRec."WS Description1");
                    WholesaleObj.Add('quantity', RetailProductRec."WS Quantity1");
                    WholesaleObj.Add('code', RetailProductRec.code);
                    WholesaleObj.Add('price', RetailProductRec."WS Price1");
                    WholesalesArray.Add(WholesaleObj);

                end;

                if RetailProductRec."WS Description" <> '' then begin
                    clear(WholesaleObj);
                    WholesaleObj.Add('description', RetailProductRec."WS Description");
                    WholesaleObj.Add('quantity', RetailProductRec."WS Quantity");
                    WholesaleObj.Add('code', RetailProductRec."WS Code");
                    WholesaleObj.Add('price', RetailProductRec."WS Price");
                    WholesalesArray.Add(WholesaleObj);

                end;
                JsonPayload.Add('Wholesales', WholesalesArray);

                Clear(BaseItemBarcode);
                Clear(FormulaItemObj);
                Clear(FormulaItemArray);
                if RetailProductRec."Formular-Item" = true then begin
                    RetailProductRec2.Reset();
                    RetailProductRec2.SetRange("Item No.", RetailProductRec."Item No.");
                    RetailProductRec2.SetRange("Formular-Item", false);
                    if RetailProductRec2.FindFirst() then begin
                        BaseItemBarcode := RetailProductRec2.Code;
                        FormulaItemObj.Add('productId', BaseItemBarcode);
                        FormulaItemObj.Add('quantity', RetailProductRec.Quantity);
                        FormulaItemArray.Add(FormulaItemObj);
                    end;
                end;

                JsonPayload.Add('formularItems', FormulaItemArray);
                clear(PriceProfileObj);
                Clear(ProdArray);

                ProdArray.Add(JsonPayload);

                ProdArray.WriteTo(JsonTextWriter);
                Clear(JsonPayloadtext);

                JsonPayloadtext := CopyStr(JsonTextWriter, 2, StrLen(JsonTextWriter) - 2);

                Clear(Respjson);
                Respjson := PutToUrl(RetailProductRec.Code, JsonPayloadtext);

                if Respjson.Get('message', reptoken) then begin
                    RetailProductRec.ResponseMessage := reptoken.AsValue().AsText();

                    if Respjson.Get('success', reptoken) then
                        RetailProductRec.ResponseSuccess := reptoken.AsValue().AsBoolean();
                    if RetailProductRec.ResponseSuccess then begin

                        RetailProductRec.Status := RetailProductRec.Status::Posted;
                        RetailProductRec.ResponseMessage := 'Product updated';
                        PriceChangeLog.Reset();
                        PriceChangeLog.SetRange("Bar Code", RetailProductRec.Code);
                        PriceChangeLog.SetRange("Clearwox Item Code", RetailProductRec.ProductID);
                        PriceChangeLog.SetRange("Unit of Measure", RetailProductRec.Unit);
                        PriceChangeLog.SetRange("New Price", RetailProductRec.Price);
                        if PriceChangeLog.FindFirst() then begin
                            PriceChangeLog.PushedtoAPI := true;
                            PriceChangeLog."Response Message" := reptoken.AsValue().AsText();
                        end;
                    end else begin
                        RetailProductRec.Status := RetailProductRec.Status::Error;
                        RetailProductRec.ResponseMessage := reptoken.AsValue().AsText();
                        PriceChangeLog.Reset();
                        PriceChangeLog.SetRange("Bar Code", RetailProductRec.Code);
                        PriceChangeLog.SetRange("Clearwox Item Code", RetailProductRec.ProductID);
                        PriceChangeLog.SetRange("Unit of Measure", RetailProductRec.Unit);
                        PriceChangeLog.SetRange("New Price", RetailProductRec.Price);
                        if PriceChangeLog.FindFirst() then begin
                            PriceChangeLog.PushedtoAPI := false;

                            PriceChangeLog."Response Message" := reptoken.AsValue().AsText();
                        end;


                    end;
                    RetailProductRec.modify();
                end;

            until RetailProductRec.Next() = 0;
        end;
    end;

    procedure PutToUrl(ProductID: Text; JsonPayloadtext: Text): JsonObject
    var
        HttpClient: HttpClient;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        Content: HttpContent;
        header: HttpHeaders;
        ResponseText: Text;
        JsonResponse: JsonObject;
        chiapiset: record "CHI API Setup";
        AText, Ttext : Text;
    begin
        if chiapiset.get(1) then begin
            AText := '';
            Ttext := '';

            // Set the request URI with the ProductID for PUT
            HttpRequestMessage.SetRequestUri(chiapiset."API Endpoint2" + '/' + ProductID);
            HttpRequestMessage.Method := 'PUT';
            HttpRequestMessage.GetHeaders(Header);

            // Add authorization header
            AText := GetAccessDetails(chiapiset, 'Authorize');
            Header.Add('Authorization', atext);
            HttpRequestMessage.GetHeaders(Header);

            // Add tenant header
            TText := GetAccessDetails(chiapiset, 'Tenant');
            Header.Add('Tenant', TText);
        end;

        // Set the content for the request
        Content.WriteFrom(JsonPayloadtext);
        Content.GetHeaders(Header);
        Header.clear;
        Header.Add('Content-Type', 'application/json');
        HttpRequestMessage.Content := Content;

        // Send the HTTP request
        if HttpClient.Send(HttpRequestMessage, HttpResponseMessage) then begin
            HttpResponseMessage.Content.ReadAs(ResponseText);
            if JsonResponse.ReadFrom(ResponseText) then begin
                exit(JsonResponse);
            end;
        end;

        // Return empty JSON object if request fails
        exit(JsonResponse);
    end;

    procedure FetchItemsToRetailProduct()
    var
        ItemRec: Record Item;
        RetailProductRec: Record "Retail Products log";
        ItemUnit, ItemUnit2 : Record "Item Unit of Measure";
        SalesPrice, SalesPrice1 : record "Sales Price";
        invtset: Record "Inventory Setup";

    begin
        // Set filter on the Item table for items with Item Category Code = 'FG'
        ItemRec.SetRange("Item Category Code", 'FG');
        ItemRec.SetRange(blocked, false);
        Itemrec.SetFilter(BarCode, '<>%1', '');
        ItemRec.SetFilter("Unit Cost", '<>%1', 0);
        if ItemRec.FindSet() then begin
            repeat

                // Check if the item already exists in the RetailProduct table
                if not RetailProductRec.Get(ItemRec.BarCode) then begin
                    // Insert a new record into the RetailProduct table

                    RetailProductRec.Init();
                    RetailProductRec."Code" := itemrec.barcode;
                    retailproductrec."item no." := ItemRec."No.";
                    RetailProductRec."Description" := ItemRec.Description;
                    RetailProductRec."Generic Name" := '';
                    RetailProductRec."Allow Fraction" := ItemRec."Allow Fraction";
                    if COPYSTR(ItemRec."No.", 1, 3) = 'HFG' then
                        RetailProductRec."Manufacturer ID" := '0-1' ELSE
                        RetailProductRec."Manufacturer ID" := '0-1';
                    RetailProductRec."Category ID" := '0-1'; //ItemRec."Item Category Code";
                    //RetailProductRec."Cost" := ItemRec."Unit Cost";
                    //RetailProductRec."Unit" := ItemRec."Base Unit of Measure";
                    RetailProductRec."Recurring Qty" := 0;
                    RetailProductRec."Markup" := 0;
                    // RetailProductRec."Price" := ItemRec."Unit Price";
                    if itemrec."VAT Prod. Posting Group" = 'VATABLE' THEN
                        RetailProductRec."Taxable" := true ELSE
                        IF itemrec."VAT Prod. Posting Group" = 'NOTVATABLE' THEN
                            RetailProductRec."Taxable" := false;
                    RetailProductRec."Watched" := false;
                    RetailProductRec."Online" := false;
                    RetailProductRec."Raw Material" := false;
                    RetailProductRec."Produce" := false;
                    // unit, cost price
                    ItemUnit.reset;
                    ItemUnit.SetRange("Item No.", ItemRec."No.");
                    ItemUnit.Setfilter("Qty. per Unit of Measure", '<%1', 1);
                    if ItemUnit.FindLast()
                    then begin
                        RetailProductRec."Cost" := ItemRec."Unit Cost" * ItemUnit."Qty. per Unit of Measure";
                        RetailProductRec."Unit" := ItemUnit.Code;
                        RetailProductRec."Price" := ItemRec."Unit Price" * ItemUnit."Qty. per Unit of Measure";


                        invtset.Get();
                        //check if FG items for retail price group have released new prices
                        SalesPrice.SetRange("Sales Type", 1);
                        SalesPrice.SetRange("Sales Code", invtset."Retail Price Group");
                        SalesPrice.SetRange("Item No.", ItemRec."No.");
                        SalesPrice.setrange("Approval Status", 2);
                        SalesPrice.setfilter("Starting Date", '<=%1', today);
                        //SalesPrice.setfilter("Ending Date", '>=%1', today);
                        salesprice.SetRange("Unit of Measure Code", ItemUnit.Code);
                        if salesprice.findset then begin
                            // if salesprice."Unit of Measure Code" = ItemUnit.code then
                            if SalesPrice."Unit Price" <> RetailProductRec.Price then
                                RetailProductRec.Price := SalesPrice."Unit Price";

                        end;
                    end else begin
                        ItemUnit.reset;
                        ItemUnit.SetRange("Item No.", ItemRec."No.");
                        ItemUnit.Setfilter("Qty. per Unit of Measure", '>%1', 0);
                        if ItemUnit.FindFirst()
                        then begin
                            RetailProductRec."Cost" := ItemRec."Unit Cost" * ItemUnit."Qty. per Unit of Measure";
                            RetailProductRec."Unit" := ItemUnit.Code;
                            RetailProductRec."Price" := ItemRec."Unit Price" * ItemUnit."Qty. per Unit of Measure";


                            invtset.Get();
                            //check if FG items for retail price group have released new prices
                            SalesPrice.SetRange("Sales Type", 1);
                            SalesPrice.SetRange("Sales Code", invtset."Retail Price Group");
                            SalesPrice.SetRange("Item No.", ItemRec."No.");
                            SalesPrice.setrange("Approval Status", 2);
                            SalesPrice.setfilter("Starting Date", '<=%1', today);
                            //SalesPrice.setfilter("Ending Date", '>=%1', today);
                            salesprice.SetRange("Unit of Measure Code", ItemUnit.Code);
                            if salesprice.findset then begin
                                // if salesprice."Unit of Measure Code" = ItemUnit.code then
                                if SalesPrice."Unit Price" <> RetailProductRec.Price then
                                    RetailProductRec.Price := SalesPrice."Unit Price";

                            end;
                        end;
                    end;

                    ItemUnit2.reset;
                    ItemUnit2.SetRange("Item No.", ItemRec."No.");
                    ItemUnit2.Setfilter(code, ItemRec."Base Unit of Measure");
                    if ItemUnit2.Findfirst()
                    then begin
                        //   RetailProductRec."WS Description" := ItemRec."Base Unit of Measure";
                        RetailProductRec."WS Quantity" := round((ItemUnit2."Qty. per Unit of Measure" / ItemUnit."Qty. per Unit of Measure"), 1);
                        //  RetailProductRec."WS Code" := ItemRec."Base Unit of Measure";

                        //  RetailProductRec."WS Price" := ItemRec."Unit Price";

                        //  RetailProductRec."WS Description1" := ItemUnit.Code;
                        RetailProductRec."WS Quantity1" := round((RetailProductRec."WS Quantity" * ItemUnit."Qty. per Unit of Measure"), 0.1);

                        //  RetailProductRec."WS Code1" := ItemUnit.Code;
                        //  RetailProductRec."WS Price1" := RetailProductRec.Price;
                        // end;
                        // SalesPrice1.SetRange("Sales Type", 1);
                        // SalesPrice1.SetRange("Sales Code", invtset."Retail Price Group");
                        // SalesPrice1.SetRange("Item No.", ItemRec."No.");
                        // SalesPrice1.setrange("Approval Status", 2);
                        // SalesPrice1.setfilter("Starting Date", '<=%1', today);
                        // //SalesPrice1.setfilter("Ending Date", '>=%1', today);
                        // salesprice1.SetRange("Unit of Measure Code", ItemUnit2.Code);
                        // if salesprice1.findset then begin
                        //     // if salesprice."Unit of Measure Code" = ItemUnit.code then
                        //     if SalesPrice1."Unit Price" <> RetailProductRec."WS Price" then
                        //         RetailProductRec."WS Price" := SalesPrice1."Unit Price";
                        // end;
                    end;
                    //end;
                    RetailProductRec."Status" := RetailProductRec.Status::New;

                    RetailProductRec.Insert();
                    Commit(); // Commit after each record to avoid locking issues
                    CreateItemUOMLines(ItemRec, RetailProductRec."WS Quantity");
                end;

            until ItemRec.Next() = 0;

            //Message('Items with Item Category Code FG have been fetched to the Retail Product table.');
        end else begin
            //Message('No items found with Item Category Code FG.');
        end;
    end;

    procedure CreateItemUOMLines(var ItemRec: Record Item; Qty: Integer)
    var
        ItemBarcode: Record "Item Barcodes Unit of Measure";
        RetailProductRec: Record "Retail Products log";
        ItemUnit, ItemUnit2 : Record "Item Unit of Measure";
        SalesPrice, SalesPrice1 : record "Sales Price";
        invtset: Record "Inventory Setup";
        ItemUOM: Record "Item Unit of Measure";
        itembarcode2: Record "Item Barcodes Unit of Measure";
        uom: Decimal;
        barcode: Text;
    begin
        ItemUOM.SetRange("Item No.", ItemRec."No.");
        ItemUOM.SetFilter(Code, '<>%1', 'PALLETS');
        if ItemUOM.FindSet() then begin
            repeat
                itemunit.Reset();
                itemunit.SetRange(Code, ItemRec."Base Unit of Measure");
                if itemunit.FindFirst() then begin
                    Clear(uom);
                    uom := (ItemUOM."Qty. per Unit of Measure" / itemunit."Qty. per Unit of Measure");

                    barcode := ItemRec.BarCode + Format(uom);
                    itembarcode2.Reset();
                    itembarcode2.SetRange(Barcode, barcode);
                    if not itembarcode2.FindFirst() then begin
                        itembarcode2.Init();
                        itembarcode2.Barcode := barcode;
                        itembarcode2."Unit Of Measure" := ItemUOM.Code;
                        itembarcode2."Item No." := ItemRec."No.";
                        itembarcode2.Insert();
                    end;
                end;
            until ItemUOM.Next() = 0;

        end;

        ItemBarcode.Reset();
        ItemBarcode.SetRange("Item No.", ItemRec."No.");
        // ItemBarcode.SetFilter("Unit of Measure", '<>%1', ItemRec."Base Unit of Measure");
        if ItemBarcode.FindSet() then
            repeat
                RetailProductRec.SetFilter("Item No.", ItemBarcode."Item No.");
                RetailProductRec.SetFilter(Unit, ItemBarcode."Unit Of Measure");
                if not RetailProductRec.FindFirst() then begin
                    RetailProductRec.Init();
                    RetailProductRec."Item No." := ItemBarcode."Item No.";
                    RetailProductRec.Init();
                    RetailProductRec."Code" := ItemBarcode.Barcode;
                    retailproductrec."item no." := ItemBarcode."Item No.";
                    RetailProductRec."Description" := ItemBarcode."Unit Of Measure" + ' ' + ItemRec.Description;
                    RetailProductRec."Generic Name" := '';
                    if COPYSTR(ItemRec."No.", 1, 3) = 'HFG' then
                        RetailProductRec."Manufacturer ID" := '0-1' ELSE
                        RetailProductRec."Manufacturer ID" := '0-1';
                    RetailProductRec."Category ID" := '0-1';
                    RetailProductRec."Recurring Qty" := 0;
                    RetailProductRec."Markup" := 0;
                    RetailProductRec."Formular-Item" := true;
                    RetailProductRec.Quantity := Qty;
                    RetailProductRec."Product ID" := ItemRec.BarCode;

                    if itemrec."VAT Prod. Posting Group" = 'VATABLE' THEN
                        RetailProductRec."Taxable" := true ELSE
                        IF itemrec."VAT Prod. Posting Group" = 'NOTVATABLE' THEN
                            RetailProductRec."Taxable" := false;
                    RetailProductRec."Watched" := false;
                    RetailProductRec."Online" := false;
                    RetailProductRec."Raw Material" := false;
                    RetailProductRec."Produce" := false;

                    ItemUnit.reset;
                    ItemUnit.SetRange("Item No.", ItemBarcode."Item No.");
                    ItemUnit.SetRange(Code, ItemBarcode."Unit Of Measure");
                    ItemUnit.Setfilter("Qty. per Unit of Measure", '<%1', 1);
                    if ItemUnit.FindLast()
                    then begin
                        RetailProductRec."Cost" := ItemRec."Unit Cost" * ItemUnit."Qty. per Unit of Measure";
                        RetailProductRec."Unit" := ItemUnit.Code;
                        RetailProductRec."Price" := ItemRec."Unit Price" * ItemUnit."Qty. per Unit of Measure";
                        invtset.Get();
                        //check if FG items for retail price group have released new prices
                        SalesPrice.SetRange("Sales Type", 1);
                        SalesPrice.SetRange("Sales Code", invtset."Retail Price Group");
                        SalesPrice.SetRange("Item No.", ItemBarcode."Item No.");
                        SalesPrice.setrange("Approval Status", 2);
                        SalesPrice.setfilter("Starting Date", '<=%1', today);
                        salesprice.SetRange("Unit of Measure Code", ItemBarcode."Unit Of Measure");
                        if salesprice.findset then begin

                            if SalesPrice."Unit Price" <> RetailProductRec.Price then
                                RetailProductRec.Price := SalesPrice."Unit Price";

                        end;
                    end else begin
                        ItemUnit.reset;
                        ItemUnit.SetRange("Item No.", ItemBarcode."Item No.");
                        ItemUnit.Setfilter("Qty. per Unit of Measure", '>%1', 0);
                        if ItemUnit.FindFirst()
                        then begin
                            RetailProductRec."Cost" := ItemRec."Unit Cost" * ItemUnit."Qty. per Unit of Measure";
                            RetailProductRec."Unit" := ItemBarcode."Unit Of Measure";
                            RetailProductRec."Price" := ItemRec."Unit Price" * ItemUnit."Qty. per Unit of Measure";


                            invtset.Get();

                            SalesPrice.SetRange("Sales Type", 1);
                            SalesPrice.SetRange("Sales Code", invtset."Retail Price Group");
                            SalesPrice.SetRange("Item No.", ItemBarcode."Item No.");
                            SalesPrice.setrange("Approval Status", 2);
                            SalesPrice.setfilter("Starting Date", '<=%1', today);
                            salesprice.SetRange("Unit of Measure Code", ItemBarcode."Unit Of Measure");
                            if salesprice.findset then begin

                                if SalesPrice."Unit Price" <> RetailProductRec.Price then
                                    RetailProductRec.Price := SalesPrice."Unit Price";

                            end;
                        end;
                    end;

                    ItemUnit2.reset;
                    ItemUnit2.SetRange("Item No.", ItemRec."No.");
                    ItemUnit2.Setfilter(code, ItemBarcode."Unit Of Measure");
                    if ItemUnit2.Findfirst()
                    then begin
                    end;
                    RetailProductRec."Status" := RetailProductRec.Status::New;

                    RetailProductRec.Insert();
                    Commit();
                end
            until ItemBarcode.Next() = 0;
    end;


    procedure ProcessSalesData(JsonText: Text)
    var
        JsonArray, Jarray, PjArray, IJarray, Tarray, PTarray : JsonArray; // Used to hold the array of records
        JsonToken, JToken, Ptoken, Itoken, Ttoken, PTToken, oToken, refToken : JsonToken; // Token to iterate through each record in the array
        JsonObject, PayJObject, ItemJobject, TObject, PTObject : JsonObject; // Individual JSON object for each record
        CustomTransactionTable: Record "Retail Sales Transaction Log"; // Custom table to store transaction data
        LastTransactionID: Code[20]; // Variable to store the highest Transaction ID
        Value: Variant; // Temporary variable to retrieve JSON values
        TempDecimal: Decimal; // Temporary decimal variable for conversion
        TempDateTime: DateTime; // Temporary DateTime variable for conversion
        arraytext, temptext, Itemtext, Paymenttext, PTypeText : Text;
        POSPayments: record POSPaymentLog;
        q, P, I : Integer;
        CUSTOMER: Record customer;
        Invsetup: record "inventory setup";
        FirstRecord: Boolean;
        LogOutletID: Integer;
        ExistingTransactions: Dictionary of [Code[20], Boolean]; // Stores Transaction ID as key
        ExistingTransaction: Record "Retail Sales Transaction Log";
        TransactionID: Code[20];
        OutletID: Integer;

    begin
        Invsetup.Get();
        customer.reset;
        // customer.setrange("Global Dimension 1 Code", Invsetup."Retail Acct Location");
        customer.setfilter("Clearwox Outlet ID", '<>%1', 0); //<>%1', 0);
        if customer.findset then
            repeat
                LogOutletID := 0;
                LastTransactionID := '';
                FirstRecord := false;
                Clear(ExistingTransactions);
                // Retrieve the last (highest) Transaction ID from the custom table
                CustomTransactionTable.reset;
                CustomTransactionTable.SetCurrentKey("Outlet ID", "Transaction ID");
                CustomTransactionTable.SetRange("Outlet ID", CUSTOMER."Clearwox Outlet ID");
                if CustomTransactionTable.Findfirst() then begin
                    repeat
                        //   LastTransactionID := CustomTransactionTable."Transaction ID";
                        LogOutletID := CustomTransactionTable."Outlet ID";
                        ExistingTransactions.Add(CustomTransactionTable."Transaction ID", true);
                    until CustomTransactionTable.next = 0;
                end

                else begin
                    FirstRecord := true; // Default to empty if no records exist
                    LogOutletID := CUSTOMER."Clearwox Outlet ID";
                end;

                // Parse the input JSON text as an array
                if not JsonObject.ReadFrom(JsonText) then
                    Error('Invalid JSON format. Expected an array of records.');
                // JsonArray.ReadFrom(jsontext); // .Get('data',JsonToken)
                JsonObject.Get('data', JToken);
                JsonArray := JToken.AsArray();

                for q := 0 to (jsonarray.Count - 1) do begin
                    JsonArray.Get(q, JsonToken);
                    JsonToken.WriteTo(arraytext);
                    // Convert the current token to a JsonObject
                    JsonObject.ReadFrom(arraytext);

                    JsonObject.Get('type', itoken);
                    JsonObject.Get('outletId', OToken);
                    //Tarray := itoken.AsArray();
                    // for p := 0 to (Tarray.Count - 1) do begin
                    // TArray.Get(p, IToken);
                    IToken.WriteTo(Paymenttext);
                    TObject.ReadFrom(Paymenttext);
                    TObject.Get('id', Itoken);




                    // end;

                    // Get the Transaction ID of the current record
                    if (JsonObject.Get('id', JToken)) and ((otoken.AsValue().AsInteger() = LogOutletID)) then
                        if ((itoken.AsValue().AsInteger() = 1) or (itoken.AsValue().AsInteger() = 4)) then //begin
                            if FirstRecord or (ExistingTransactions.Count <> 0) then begin
                                // Map values from the JsonObject to the custom table
                                TransactionID := JToken.AsValue().AsCode();

                                // Check if transaction already exists using the dictionary
                                if ExistingTransactions.ContainsKey(TransactionID) then begin
                                    //TransactionID is already existing
                                end else begin


                                    // with CustomTransactionTable do begin
                                    CustomTransactionTable.Init();

                                    if JsonObject.Get('id', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Transaction ID" := '' else
                                            CustomTransactionTable."Transaction ID" := jtoken.AsValue().AsCode();



                                    if JsonObject.Get('receiptNo', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Receipt No" := '' else
                                            CustomTransactionTable."Receipt No" := jtoken.AsValue().AsText();

                                    if JsonObject.Get('amount', jtoken) then
                                        CustomTransactionTable."Amount" := jtoken.AsValue().AsDecimal();

                                    if JsonObject.Get('transactorId', jtoken) then
                                        CustomTransactionTable."Transactor ID" := jtoken.AsValue().AsCode();

                                    if JsonObject.Get('transactor', jtoken) then
                                        CustomTransactionTable."Transactor" := jtoken.AsValue().AsText();

                                    if JsonObject.Get('staffId', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Staff ID" := '' else
                                            CustomTransactionTable."Staff ID" := jtoken.AsValue().AsCode();

                                    if JsonObject.Get('staff', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Staff" := '' else
                                            CustomTransactionTable."Staff" := jtoken.AsValue().AsText();

                                    if JsonObject.Get('outletId', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Outlet ID" := 0 else
                                            CustomTransactionTable."Outlet ID" := jtoken.AsValue().AsInteger();

                                    if JsonObject.Get('outlet', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Outlet" := '' else
                                            CustomTransactionTable."Outlet" := jtoken.AsValue().AsText();

                                    if JsonObject.Get('remarks', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Remarks" := '' else
                                            CustomTransactionTable."Remarks" := jtoken.AsValue().AsText();

                                    if JsonObject.Get('enteredBy', jtoken) then
                                        if jtoken.AsValue().IsNull then
                                            CustomTransactionTable."Entered By" := '' else
                                            CustomTransactionTable."Entered By" := jtoken.AsValue().AsText();

                                    if JsonObject.Get('cashAmount', jtoken) then
                                        CustomTransactionTable."Cash Amount" := jtoken.AsValue().AsDecimal();
                                    if JsonObject.Get('chequeAmount', jtoken) then
                                        CustomTransactionTable."Cheque Amount" := jtoken.AsValue().AsDecimal();
                                    if JsonObject.Get('cardAmount', jtoken) then
                                        CustomTransactionTable."Card Amount" := jtoken.AsValue().AsDecimal();
                                    if JsonObject.Get('creditAmount', jtoken) then
                                        CustomTransactionTable."Credit Amount" := jtoken.AsValue().AsDecimal();
                                    if JsonObject.Get('bankAmount', jtoken) then
                                        CustomTransactionTable."Bank Amount" := jtoken.AsValue().AsDecimal();
                                    if JsonObject.Get('webAmount', jtoken) then
                                        CustomTransactionTable."Web Amount" := jtoken.AsValue().AsDecimal();
                                    if JsonObject.Get('date', jtoken) then
                                        CustomTransactionTable."Date" := jtoken.AsValue().AsDateTime();
                                    if JsonObject.Get('enteredOn', jtoken) then
                                        CustomTransactionTable."Entered On" := jtoken.AsValue().AsDateTime();

                                    if TObject.Get('id', Itoken) then
                                        if itoken.AsValue().IsNull then
                                            CustomTransactionTable."Type" := 0 else
                                            CustomTransactionTable."Type" := itoken.AsValue().AsInteger();

                                    if TObject.Get('description', Itoken) then
                                        if itoken.AsValue().IsNull then
                                            CustomTransactionTable."Type Description" := '' else
                                            CustomTransactionTable."Type Description" := itoken.AsValue().AsText();

                                    JsonObject.Get('payments', JToken);
                                    PjArray := jtoken.AsArray();
                                    for p := 0 to (pjarray.Count - 1) do begin
                                        pjArray.Get(p, PToken);
                                        pToken.WriteTo(Paymenttext);
                                        // Convert the current token to a JsonObject
                                        PayJObject.ReadFrom(Paymenttext);
                                        // JsonObject.Get('data', JToken);
                                        POSPayments.init;
                                        POSPayments.TransactionID := CustomTransactionTable."Transaction ID";
                                        if PayJObject.Get('accountId', ptoken) then
                                            if ptoken.AsValue().IsNull then
                                                POSPayments.AccountID := '' else
                                                POSPayments.AccountID := ptoken.AsValue().AsCode();

                                        if PayJObject.Get('account', ptoken) then
                                            if ptoken.AsValue().IsNull then
                                                POSPayments.AccountNo := '' else
                                                POSPayments.AccountNo := ptoken.AsValue().AsCode();
                                        if PayJObject.Get('amount', ptoken) then
                                            if ptoken.AsValue().IsNull then
                                                POSPayments.Amount := 0 else
                                                POSPayments.Amount := ptoken.AsValue().AsDecimal();

                                        PayJobject.get('paymentType', PTToken);
                                        PTToken.WriteTo(PTypeText);
                                        PTObject.ReadFrom(PTypeText);
                                        if PTObject.Get('id', Pttoken) then
                                            if PTToken.AsValue().IsNull then
                                                POSPayments.PaymentTypeID := '' else
                                                POSPayments.PaymentTypeID := PTToken.AsValue().AsCode();

                                        if PTObject.Get('description', Pttoken) then
                                            if PTToken.AsValue().IsNull then
                                                POSPayments."PaymentType Description" := '' else
                                                POSPayments."PaymentType Description" := PTToken.AsValue().AsText();

                                        if PayJObject.Get('reference', refToken) then
                                            if refToken.AsValue().IsNull then
                                                POSPayments.PaymentReference := '' else
                                                POSPayments.PaymentReference := refToken.AsValue().AsText();

                                        POSPayments.insert;
                                        // PTarray := PTToken.AsArray();

                                    end;

                                    if CustomTransactionTable.type = 1 then
                                        CustomTransactionTable."Document Type" := CustomTransactionTable."Document Type"::Order else
                                        if CustomTransactionTable.type = 4 then
                                            CustomTransactionTable."Document Type" := CustomTransactionTable."Document Type"::"Credit Memo";

                                    CustomTransactionTable.Insert(); // Insert the record into the database
                                                                     // Add newly inserted transaction to the dictionary to avoid duplicate processing
                                    ExistingTransactions.Add(CustomTransactionTable."Transaction ID", true);

                                    ProcessItemsJSON(arraytext, CustomTransactionTable.Type, CustomTransactionTable."Transaction ID");
                                end;
                            end;
                end;
            until customer.Next() = 0;
    end;

    procedure ProcessItemsJSON(JSONText: Text; TransactionType: integer; TransactionID: code[20])
    var
        JsonObject: JsonObject;
        JsonArray: JsonArray;
        ItemArray: JsonArray;
        ItemObject: JsonObject;
        ItemToken: JsonToken;
        POSSalesLine: Record POSSalesLines;
        ItemId, Itemtext : Text;
        Quantity: Decimal;
        Price: Decimal;
        Discount: Decimal;
        Cost: Decimal;
        Tax: Decimal;
        p: Integer;
        UnitofmeasureID: text;
        ItemRec: Record Item;
        ItemBarCode: Record "Item Barcodes Unit of Measure";
        Itemunit: Record "Item Unit of Measure";
        TypeObject: JsonObject;
        Typetoken: JsonToken;
        TypeText: Text;
        descToken: JsonToken;
        typeprice: text;
    begin
        Clear(UnitofmeasureID);
        // Parse the JSON
        if JsonObject.ReadFrom(JSONText) then begin
            // Access the "items" array in the JSON
            if JsonObject.Get('items', ItemToken) then begin
                ItemArray := ItemToken.AsArray();
                LineNo := 0;
                for p := 0 to (itemarray.Count - 1) do begin
                    LineNo += 10000;
                    itemArray.Get(p, itemToken);
                    itemToken.WriteTo(Itemtext);
                    // Convert the current token to a JsonObject
                    ItemObject.ReadFrom(Itemtext);
                    //  foreach ItemObject in ItemArray do begin
                    // Extract fields from the item object


                    Clear(ItemId);
                    if ItemObject.Get('itemId', Itemtoken) then begin
                        if Itemtoken.AsValue().IsNull then
                            ItemId := '' else begin
                            ItemId := itemtoken.AsValue().AsText();
                            ItemBarCode.Reset();
                            ItemBarCode.SetRange("Clearwox Product ID", ItemId);
                            if ItemBarCode.FindFirst() then
                                UnitofmeasureID := ItemBarCode."Unit Of Measure";
                        end;
                        ItemRec.Reset();
                        ItemRec.SetRange("Clearwox Item ID", ItemId);
                        if ItemRec.FindFirst() then begin
                            Itemunit.Reset();
                            Itemunit.SetCurrentKey("Qty. per Unit of Measure");
                            Itemunit.SetAscending("Qty. per Unit of Measure", true);
                            Itemunit.SetRange("Item No.", ItemRec."No.");
                            //Itemunit.SetFilter("Qty. per Unit of Measure", '<%1', 1);
                            if Itemunit.FindFirst() then
                                UnitofmeasureID := Itemunit.Code;
                        end;


                    end;



                    if ItemObject.Get('quantity', Itemtoken) then
                        if Itemtoken.AsValue().IsNull then
                            Quantity := 0 else
                            Quantity := itemtoken.AsValue().AsDecimal();

                    if ItemObject.Get('type', Typetoken) then
                        TypeObject := Typetoken.AsObject();
                    if TypeObject.Get('description', descToken) then
                        typeprice := descToken.AsValue().AsText();
                    if typeprice = 'GiftOut' then begin
                        Price := 0;
                        Discount := 0;
                    end
                    else begin
                        if ItemObject.Get('price', Itemtoken) then
                            if Itemtoken.AsValue().IsNull then
                                Price := 0 else
                                Price := itemtoken.AsValue().AsDecimal();

                        if ItemObject.Get('discount', Itemtoken) then
                            if Itemtoken.AsValue().IsNull then
                                Discount := 0 else
                                Discount := itemtoken.AsValue().AsDecimal();
                    end;

                    if ItemObject.Get('cost', Itemtoken) then
                        if Itemtoken.AsValue().IsNull then
                            Cost := 0 else
                            Cost := itemtoken.AsValue().AsDecimal();

                    if ItemObject.Get('tax', Itemtoken) then
                        if Itemtoken.AsValue().IsNull then
                            Tax := 0 else
                            Tax := itemtoken.AsValue().AsDecimal();
                    // If ItemObject.Get('wholesaleId', ItemToken) then
                    //     if ItemToken.AsValue().IsNull then
                    //         UnitofmeasureID := '' else
                    //         UnitofmeasureID := ItemToken.AsValue().AsText();


                    // Insert into the custom table
                    POSSalesLine.Init();
                    if transactiontype = 1 then
                        POSSalesLine."Document Type" := POSSalesLine."Document Type"::Order else
                        if transactiontype = 4 then
                            POSSalesLine."Document Type" := POSSalesLine."Document Type"::"Credit Memo";
                    POSSalesLine."Transaction ID" := TransactionID;
                    POSSalesLine."Line No." := LineNo;
                    POSSalesLine."Item ID" := ItemId;
                    POSSalesLine."Quantity" := Quantity;
                    POSSalesLine."Price" := Price;
                    POSSalesLine."Discount" := Discount;
                    POSSalesLine."Cost" := Cost;
                    POSSalesLine."Tax" := Tax;
                    POSSalesLine.WholesalesID := UnitofmeasureID;
                    POSSalesLine.Insert();
                end;
            end;//else
                // Error('No items array found in the JSON.');
        end;// else
            //Error('Invalid JSON format.');
    end;

    local procedure PostPOSSales()
    var
        SalesHdr4, salesheader : Record "Sales Header";
        ReservRec: Record "Reservation Entry";
        Loccode, BillNo : Code[20];
        DocType: enum "Sales Document Type"; //Option Quote,"Order",Invoice,"Credit Memo","Blanket Order","Return Order";
        QtyBase, Salesamt, TotalV : Decimal;
        ItemUOM: Record "Item Unit of Measure";
        SalesShipMent: Record "Sales Shipment Header";
        SaleRecSetup: Record "Sales & Receivables Setup";
        SalesInvoiceHdr: Record "Sales Invoice Header";
        SalesInvoiceLine: Record "Sales Invoice Line";
        SalesShipmentheader: Record "Sales Shipment Header";
        SalesShipmentLine: Record "Sales Shipment Line";
        GenJnlLine: Record "Gen. Journal Line";
        GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
        BankAccountLedgerEntries: Record "Bank Account Ledger Entry";
        ItemRec: Record item;
        Shot1: Code[20];
        Shot2: Code[20];
        CustN: Code[20];
        ILERec2: record "Item Ledger Entry";
        checksalesline: record "Sales Line";
        lotexists: Boolean;
        SalesCrHDR: record "Sales Cr.Memo Header";
        POSPayment: Record POSPaymentLog;
        paymentcount: Integer;
        RetailBank: Record "Retail Banks";
    begin
        // CurrPage.SalesLines.PAGE.CheckMixLot();

        /*  TESTFIELD("Location Code");
         TESTFIELD("Document Type");
         TESTFIELD("No."); */
        //BillNo := "No.";

        //CODEUNIT.RUN(CODEUNIT::"Sales-Post (Yes/No)",Rec);
        //IF ApprovalMgt.PrePostApprovalCheck(Rec,PurchaseHeader) THEN
        SalesTrans.SetRange("Sales Order Created", true);
        SalesTrans.SetRange("Processed", false);
        //SalesTrans.setrange("Transaction ID", '7147-3');
        if SalesTrans.FindSet() then begin
            repeat
                BillNo := '';

                salesheader.SetRange("External Document No.", SalesTrans."Transaction ID");
                salesheader.SetRange(Status, salesheader.Status::Released);
                // salesheader.Setfilter("Document Date", '>=%1', Today - 1);
                if salesheader.findset then begin

                    salesheader.CalcFields(salesheader.Total);
                    Loccode := salesheader."Location Code";
                    DocType := salesheader."Document Type";
                    CustN := salesheader."Sell-to Customer No.";
                    TotalV := salesheader.Total;
                    Shot1 := salesheader."Shortcut Dimension 1 Code";
                    Shot2 := salesheader."Shortcut Dimension 2 Code";
                    //To check for item tracking before posting
                    SaleRecSetup.Get();
                    CLEAR(SalesAmt);
                    SalesLine.RESET;
                    SalesLine.SETRANGE("Document No.", salesheader."No.");
                    IF SalesLine.FINDSET THEN BEGIN
                        REPEAT
                            if Not SalesLine."Gift Item" then
                                SalesLine.TestField("Unit Price");//PKONJ17
                            SalesLine.SetItemTracking();
                            SalesAmt += SalesLine."Amount Including VAT";
                            IF ItemRec.GET(SalesLine."No.") THEN BEGIN
                                IF ItemRec."Item Tracking Code" <> '' THEN BEGIN
                                    CLEAR(QtyBase);
                                END;
                                //END;
                                //FIX15Jun2021>>
                                if (SalesLine."Qty. to Ship" = 0) and (SalesLine."Document Type" = SalesLine."Document Type"::Order) then begin
                                    SalesLine."Qty. to Ship" := SalesLine.Quantity - SalesLine."Quantity Shipped";
                                    SalesLine."Qty. to Ship (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Shipped (Base)"; //SalesLine."Qty. Invoiced (Base)";
                                    if SaleRecSetup."Pos Ship & Invoice" then begin
                                        SalesLine."Qty. to Invoice" := SalesLine.Quantity - SalesLine."Quantity Invoiced";
                                        SalesLine."Qty. to Invoice (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Invoiced (Base)";
                                    end;
                                    SalesLine.Modify();
                                end
                                else
                                    if (SalesLine."Document Type" = SalesLine."Document Type"::"Credit Memo") then begin
                                        //  SalesLine."Qty. to Ship" := 0;
                                        salesline."Return Qty. to Receive" := salesline.Quantity - salesline."Return Qty. Received";
                                        salesline."Return Qty. to Receive (Base)" := salesline."Quantity (Base)" - SalesLine."Return Qty. Received (Base)";
                                        //SalesLine."Qty. to Ship (Base)" := 0;
                                        if SaleRecSetup."Pos Ship & Invoice" then begin
                                            SalesLine."Qty. to Invoice" := SalesLine.Quantity - SalesLine."Quantity Invoiced";
                                            SalesLine."Qty. to Invoice (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Invoiced (Base)";
                                        end;
                                        //FIX15Jun2021<<
                                        SalesLine.Modify();
                                    end;
                            END;
                        UNTIL SalesLine.NEXT = 0;
                        SalesAmt := ROUND(SalesAmt, 1);
                    END;




                    //>> NYO
                    //>>NYO
                    // salesheader.Status := Salesheader.Status::Released;
                    BillNo := salesheader."No.";
                    salesheader.Modify();
                    if salesheader."Document Type" = salesheader."Document Type"::Order then begin
                        salesheader.Ship := true;
                        if SaleRecSetup."Pos Ship & Invoice" then
                            salesheader.Invoice := true;
                        salesheader.Modify();
                        SalesInvoiceHdr.Reset();
                        SalesInvoiceHdr.SetRange("Order No.", salesheader."No.");
                        if not SalesInvoiceHdr.FindFirst() then begin
                            salesheader."Posting Date" := TODAY;
                            salesheader.Modify();
                            CODEUNIT.RUN(CODEUNIT::"Sales-Post-Copy", salesheader);


                            COMMIT;
                            //**************Delete Reservation Entry****************
                            ReservRec.RESET;
                            ReservRec.SETRANGE("Location Code", Loccode);
                            ReservRec.SETRANGE("Source Type", 37);
                            ReservRec.SETRANGE("Source Subtype", DocType);
                            ReservRec.SETRANGE("Source ID", BillNo);
                            IF ReservRec.FINDFIRST THEN
                                REPEAT
                                    ReservRec.DELETE;
                                UNTIL ReservRec.NEXT = 0;
                            //**************Delete Reservation Entry****************

                        end;
                    end else
                        if salesheader."Document Type" = salesheader."Document Type"::"Credit Memo" then begin
                            SalesCrHDR.Reset();
                            SalesCrHDR.SetRange("Pre-Assigned No.", salesheader."No.");
                            if not SalesCrHDR.FindFirst() then begin
                                salesheader.Ship := false;

                                salesheader.Invoice := false;
                                salesheader."Posting Date" := TODAY;
                                salesheader.Modify();
                                CODEUNIT.RUN(CODEUNIT::"Sales-Post-Copy", salesheader);


                                COMMIT;
                                //**************Delete Reservation Entry****************
                                ReservRec.RESET;
                                ReservRec.SETRANGE("Location Code", Loccode);
                                ReservRec.SETRANGE("Source Type", 37);
                                ReservRec.SETRANGE("Source Subtype", DocType);
                                ReservRec.SETRANGE("Source ID", BillNo);
                                IF ReservRec.FINDFIRST THEN
                                    REPEAT
                                        ReservRec.DELETE;
                                    UNTIL ReservRec.NEXT = 0;
                                //**************Delete Reservation Entry****************


                            end;
                        end else begin
                            repeat
                                SalesLine.RESET;
                                SalesLine.SETRANGE("Document No.", Salesheader."No.");
                                IF SalesLine.FINDSET THEN BEGIN
                                    REPEAT
                                        SalesLine."Qty. to Ship" := SalesLine.Quantity;
                                        SalesLine."Qty. to Ship (Base)" := SalesLine."Quantity (Base)";
                                        SalesLine."Qty. to Invoice" := SalesLine.Quantity;
                                        SalesLine."Qty. to Invoice (Base)" := SalesLine."Quantity (Base)";
                                        SalesLine.Modify();
                                    UNTIL SalesLine.NEXT = 0;
                                END;
                                SalesInvoiceLine.Reset();
                                SalesInvoiceLine.SetRange("Document No.", SalesInvoiceHdr."No.");
                                SalesInvoiceLine.SetFilter("No.", '<>%1', '');
                                SalesInvoiceLine.SetFilter(Quantity, '<>%1', 0);
                                if SalesInvoiceLine.FindSet() then
                                    repeat
                                        SalesLine.Reset();
                                        SalesLine.SetRange("Document No.", SalesInvoiceLine."Order No.");
                                        SalesLine.SetRange("Line No.", SalesInvoiceLine."Order Line No.");
                                        if SalesLine.FindFirst() then begin
                                            //if SalesLine.Quantity <> SalesInvoiceLine.Quantity then begin
                                            SalesLine."Qty. to Invoice" -= SalesInvoiceLine.Quantity;
                                            SalesLine."Qty. to Invoice (Base)" -= SalesInvoiceLine."Quantity (Base)";
                                            SalesLine.Modify();
                                        end;
                                    until SalesInvoiceLine.Next() = 0;
                                SalesShipmentheader.Reset();
                                SalesShipmentheader.SetRange("Order No.", salesheader."No.");
                                if SalesShipmentheader.FindSet() then
                                    repeat
                                        SalesShipmentLine.Reset();
                                        SalesShipmentLine.SetRange("Document No.", SalesShipmentheader."No.");
                                        SalesShipmentLine.SetFilter("No.", '<>%1', '');
                                        SalesShipmentLine.SetFilter(Quantity, '<>%1', 0);
                                        if SalesShipmentLine.FindSet() then
                                            repeat
                                                SalesLine.Reset();
                                                SalesLine.SetRange("Document No.", SalesInvoiceLine."Order No.");
                                                SalesLine.SetRange("Line No.", SalesInvoiceLine."Order Line No.");
                                                if SalesLine.FindFirst() then begin
                                                    //if SalesLine.Quantity <> SalesShipmentLine.Quantity then begin
                                                    SalesLine."Qty. to Ship" -= SalesShipmentLine.Quantity;
                                                    SalesLine."Qty. to Ship (Base)" -= SalesShipmentLine."Quantity (Base)";
                                                    SalesLine.Modify();
                                                end;
                                            until SalesInvoiceLine.Next() = 0;
                                    until SalesShipmentheader.Next() = 0;
                            until SalesInvoiceHdr.Next() = 0;
                            SalesLine.Reset();
                            SalesLine.SetRange("Document No.", salesheader."No.");
                            if SalesLine.FindSet() then begin
                                repeat
                                    if SalesLine."Qty. to Ship" > 0 then
                                        SalesLine.SetItemTracking()
                                    else
                                        if SalesLine."Qty. to Ship" < 0 then begin
                                            SalesLine.Validate("Qty. to Ship", 0);
                                            SalesLine.Modify();
                                        end;

                                until SalesLine.Next() = 0;
                                SalesLine.CalcSums("Qty. to Invoice");
                                if SalesLine."Qty. to Invoice" > 0 then begin
                                    Salesheader.Ship := true;
                                    if SaleRecSetup."Pos Ship & Invoice" then
                                        salesheader.Invoice := true;
                                    salesheader.Modify();
                                    CODEUNIT.RUN(CODEUNIT::"Sales-Post-Copy", Salesheader);
                                    salestrans.Processed := true;
                                    salestrans.Modify();
                                end;
                            end;
                            SalesInvoiceHdr.Reset();
                            SalesInvoiceHdr.SetRange("Order No.", salesheader."No.");
                            if SalesInvoiceHdr.FindSet() then
                                repeat
                                    SalesInvoiceHdr.CalcFields("Remaining Amount", "Amount Including VAT");
                                    if SalesInvoiceHdr."Remaining Amount" > 1 then begin
                                        /*        if SalesInvoiceHdr."POS Transaction Type" = SalesInvoiceHdr."POS Transaction Type"::Cash then
                                                   SalesTrans.PostCashAmount(SalesInvoiceHdr, Salesheader, SalesInvoiceHdr."Remaining Amount");
                                               if SalesInvoiceHdr."POS Transaction Type" = SalesInvoiceHdr."POS Transaction Type"::Card then
                                                   Salestrans.PostCardAmount(SalesInvoiceHdr, Salesheader, SalesInvoiceHdr."Remaining Amount");
                                               if SalesInvoiceHdr."POS Transaction Type" = SalesInvoiceHdr."POS Transaction Type"::Both then begin */
                                        if SalesInvoiceHdr."Remaining Amount" = SalesInvoiceHdr."Amount Including VAT" then begin
                                            Salestrans.PostCashAmount(SalesInvoiceHdr, Salesheader, SalesInvoiceHdr."POS Cash Amount");
                                            salestrans.PostCardAmount(SalesInvoiceHdr, Salesheader, SalesInvoiceHdr."POS Card Amount");
                                        end else begin
                                            BankAccountLedgerEntries.Reset();
                                            BankAccountLedgerEntries.SetRange("Bank Account No.", SalesInvoiceHdr."Bal. Account No.");
                                            BankAccountLedgerEntries.SetRange("Document No.", SalesInvoiceHdr."No.");
                                            if not BankAccountLedgerEntries.FindFirst() then
                                                salestrans.PostCashAmount(SalesInvoiceHdr, salesheader, SalesInvoiceHdr."POS Cash Amount")
                                            else begin
                                                BankAccountLedgerEntries.Reset();
                                                BankAccountLedgerEntries.SetRange("Bank Account No.", SalesInvoiceHdr."POS Account No.");
                                                BankAccountLedgerEntries.SetRange("Document No.", SalesInvoiceHdr."No.");
                                                if not BankAccountLedgerEntries.FindFirst() then
                                                    salestrans.PostCardAmount(SalesInvoiceHdr, salesheader, SalesInvoiceHdr."POS Card Amount");
                                            end;
                                        end;
                                    end;
                                    // end;

                                    SalesInvoiceHdr."Processed by API" := true;
                                    SalesInvoiceHdr."Processed by API On" := CurrentDateTime;
                                    POSPayment.Reset();
                                    POSPayment.SetRange(TransactionID, salesheader."External Document No.");
                                    if POSPayment.FindSet() then begin
                                        Clear(paymentcount);
                                        repeat
                                            paymentcount += 1;
                                        until POSPayment.Next() = 0;
                                        if paymentcount > 1 then
                                            SalesInvoiceHdr."POS Transaction Type" := SalesInvoiceHdr."POS Transaction Type"::Both
                                        else begin
                                            if POSPayment."PaymentType Description" = 'Cash' then begin
                                                SalesInvoiceHdr."POS Transaction Type" := SalesInvoiceHdr."POS Transaction Type"::Cash;
                                                SalesInvoiceHdr."POS Cash Amount" := POSPayment.Amount;
                                                RetailBank.Reset();
                                                RetailBank.SetRange("POS Bank Name", POSPayment.AccountNo);
                                                if RetailBank.FindFirst() then
                                                    SalesInvoiceHdr."POS Account No." := RetailBank."Bank Account";
                                                SalesInvoiceHdr."POS Transaction No." := POSPayment.PaymentReference;
                                            end;
                                            if POSPayment."PaymentType Description" = 'Card' then begin
                                                SalesInvoiceHdr."POS Transaction Type" := SalesInvoiceHdr."POS Transaction Type"::Card;
                                                SalesInvoiceHdr."POS Card Amount" := POSPayment.Amount;
                                                RetailBank.Reset();
                                                RetailBank.SetRange("POS Bank Name", POSPayment.AccountNo);
                                                if RetailBank.FindFirst() then
                                                    SalesInvoiceHdr."POS Account No." := RetailBank."Bank Account";
                                                SalesInvoiceHdr."POS Transaction No." := POSPayment.PaymentReference;
                                            end;
                                        end;

                                    end;

                                    SalesInvoiceHdr.modify;

                                    SalesTrans."Processed" := true;
                                    SalesTrans."Posted Document No." := salesinvHdr."No.";
                                    SalesTrans.Modify();

                                until SalesInvoiceHdr.Next() = 0;
                            Commit();

                            SalesLine.Reset();
                            SalesLine.SetRange("Document No.", salesheader."No.");
                            if SalesLine.FindSet() then
                                SalesLine.DeleteAll();
                            Salesheader.Delete();
                        end;

                end;

            until SalesTrans.Next() = 0;
        end;
        UpdateSalesInHdr()
    end;

    procedure UpdateSalesInHdr()
    var
        SalesInvoiceHdr: Record "Sales Invoice Header";
        PosPayment: Record POSPaymentLog;
        RetailBank: Record "Retail Banks";
    begin
        PosPayment.Reset();
        if PosPayment.FindSet() then begin
            repeat
                RetailBank.SetRange("POS Bank Name", PosPayment.AccountNo);
                if RetailBank.FindFirst() then begin
                    SalesInvoiceHdr.Reset();
                    SalesInvoiceHdr.SetRange("External Document No.", PosPayment.TransactionID);
                    if SalesInvoiceHdr.FindSet() then begin
                        SalesInvoiceHdr."POS Account No." := RetailBank."Bank Account";
                        SalesInvoiceHdr."POS Transaction No." := PosPayment.PaymentReference;
                        SalesInvoiceHdr.Modify();
                    end;
                end;
            until PosPayment.Next() = 0;
        end;



    end;

    procedure manualStockupdate()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        ValueEntry: Record "Value Entry";
        RetailStockUpdate: Record "Retail Stock Update";
        invsetup: record "Inventory Setup";
        itemrec: record item;
        itemunit: record "Item Unit of Measure";
        itemBarcode: Record "Item Barcodes Unit of Measure";
        itemrec2: Record Item;
        unitofmeasure: Code[20];
        qty: Decimal;
        itemunit2: Record "Item Unit of Measure";
        SalesPrice: Record "Sales Price";
        unitCost: Decimal;

    begin
        invsetup.Get();
        ItemLedgerEntry.SetFilter("Location Code", invsetup."POS Central Location");
        ItemLedgerEntry.SetFilter(Quantity, '>=%1', 1);
        if ItemLedgerEntry.FindSet() then begin
            repeat
                ValueEntry.Reset();
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                if ValueEntry.FindFirst() then begin
                    RetailStockUpdate.Reset();
                    RetailStockUpdate.SetRange("Ledger Entry No.", ItemLedgerEntry."Entry No.");
                    if not RetailStockUpdate.FindFirst() then begin

                        RetailStockUpdate.Init();
                        RetailStockUpdate."Document No." := ItemLedgerEntry."Document No.";
                        RetailStockUpdate."Posting Date" := ItemLedgerEntry."Posting Date";
                        RetailStockUpdate."Expiry Date" := ItemLedgerEntry."Expiration Date";
                        RetailStockUpdate."Line No." := ItemLedgerEntry."Document Line No.";
                        RetailStockUpdate."Date Created" := today;
                        RetailStockUpdate."Supplier ID" := invsetup."Retail Stock SupplierID";
                        RetailStockUpdate.StoreID := '1';
                        RetailStockUpdate."Ledger Entry No." := ItemLedgerEntry."Entry No.";
                        RetailStockUpdate."Entry No." := ItemLedgerEntry."Entry No.";

                        RetailStockUpdate.OutletID := invsetup."Retail Stock outletID";
                        RetailStockUpdate."Location code" := ItemLedgerEntry."Location Code";

                        itemBarcode.Reset();
                        itemBarcode.SetRange("Item No.", ItemLedgerEntry."Item No.");
                        itemBarcode.SetRange("Unit Of Measure", ItemLedgerEntry."Unit of Measure Code");
                        if itemBarcode.FindFirst() then begin
                            RetailStockUpdate."Clearwox Item Code" := itemBarcode."Clearwox Product ID";
                            RetailStockUpdate."Item No." := itemBarcode."Item No.";
                            RetailStockUpdate."Product ID" := itemBarcode."Clearwox Product ID";
                            if itemrec2.Get(itemBarcode."Item No.") then
                                RetailStockUpdate."Item Description" := itemBarcode."Unit Of Measure" + ' ' + itemrec2.Description;


                        end else begin
                            itemrec.Reset();
                            itemrec.SetRange("No.", ItemLedgerEntry."Item No.");
                            if itemrec.FindFirst() then begin
                                RetailStockUpdate."Clearwox Item Code" := itemrec."Clearwox Item ID";
                                RetailStockUpdate."Product ID" := itemrec."Clearwox Item ID"; //itemrec.barcode;
                                RetailStockUpdate."Item No." := itemrec."No.";
                                RetailStockUpdate."Item Description" := itemrec.Description;


                            end;

                        end;

                        RetailStockUpdate."Batch Number" := ItemLedgerEntry."Lot No.";
                        RetailStockUpdate."Location Code" := ItemLedgerEntry."Location Code";
                        RetailStockUpdate."Quantity" := ItemLedgerEntry.Quantity;
                        RetailStockUpdate."Unit of Measure Code" := ItemLedgerEntry."Unit of Measure Code";

                        RetailStockUpdate.Price := ValueEntry."Cost per Unit";
                        RetailStockUpdate."Amount Paid" := Round(ValueEntry."Cost Amount (Actual)", 0.01);

                        RetailStockUpdate."Last Updated" := CurrentDateTime();
                        RetailStockUpdate.Insert();
                    end;
                end;
            until ItemLedgerEntry.Next() = 0;
        end;

        ItemLedgerEntry.SetFilter("Location Code", invsetup."POS Central Location");
        ItemLedgerEntry.SetFilter(Quantity, '%1..%2', 0.00001, 0.999999);

        if ItemLedgerEntry.FindSet() then begin
            repeat
                qty := 0;
                unitCost := 0;
                Clear(unitofmeasure);
                itemunit2.Reset();
                itemunit2.SetCurrentKey("Qty. per Unit of Measure");
                Itemunit2.SetAscending("Qty. per Unit of Measure", true);
                itemunit2.SetRange("Item No.", ItemLedgerEntry."Item No.");
                if itemunit2.FindFirst() then begin
                    qty := Round(ItemLedgerEntry.Quantity / itemunit2."Qty. per Unit of Measure", 1);
                    unitofmeasure := itemunit2.Code;
                    unitCost := ValueEntry."Cost per Unit" * itemunit2."Qty. per Unit of Measure";
                end;

                RetailStockUpdate.Reset();
                RetailStockUpdate.SetRange("Ledger Entry No.", ItemLedgerEntry."Entry No.");
                if not RetailStockUpdate.FindFirst() then begin

                    RetailStockUpdate.Init();
                    RetailStockUpdate."Document No." := ItemLedgerEntry."Document No.";
                    RetailStockUpdate."Posting Date" := ItemLedgerEntry."Posting Date";
                    RetailStockUpdate."Expiry Date" := ItemLedgerEntry."Expiration Date";
                    RetailStockUpdate."Line No." := ItemLedgerEntry."Document Line No.";
                    RetailStockUpdate."Date Created" := today;
                    RetailStockUpdate."Supplier ID" := invsetup."Retail Stock SupplierID";
                    RetailStockUpdate.StoreID := '1';
                    RetailStockUpdate."Ledger Entry No." := ItemLedgerEntry."Entry No.";
                    RetailStockUpdate."Entry No." := ItemLedgerEntry."Entry No.";

                    RetailStockUpdate.OutletID := invsetup."Retail Stock outletID";
                    RetailStockUpdate."Location code" := ItemLedgerEntry."Location Code";
                    itemrec.Reset();
                    itemrec.SetRange("No.", ItemLedgerEntry."Item No.");
                    if itemrec.FindFirst() then begin
                        RetailStockUpdate."Clearwox Item Code" := itemrec."Clearwox Item ID";
                        RetailStockUpdate."Product ID" := itemrec."Clearwox Item ID"; //itemrec.barcode;
                        RetailStockUpdate."Item No." := itemrec."No.";
                        RetailStockUpdate."Item Description" := itemrec.Description;

                    end;

                    RetailStockUpdate."Batch Number" := ItemLedgerEntry."Lot No.";
                    RetailStockUpdate."Location Code" := ItemLedgerEntry."Location Code";
                    RetailStockUpdate."Quantity" := qty;
                    RetailStockUpdate."Unit of Measure Code" := unitofmeasure;

                    RetailStockUpdate.price := unitCost;
                    RetailStockUpdate."Amount Paid" := Round(ValueEntry."Cost Amount (Actual)", 0.01);
                    RetailStockUpdate."Last Updated" := CurrentDateTime();
                    RetailStockUpdate.Insert();
                end;


            until ItemLedgerEntry.Next() = 0;
            // end;
        end;
    end;

    procedure StockUpdate()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        ValueEntry: Record "Value Entry";
        RetailStockUpdate: Record "Retail Stock Update";
        invsetup: record "Inventory Setup";
        itemrec: record item;
        itemunit: record "Item Unit of Measure";
        itemBarcode: Record "Item Barcodes Unit of Measure";
        itemrec2: Record Item;
        unitofmeasure: Code[20];
        qty: Decimal;
        itemunit2: Record "Item Unit of Measure";
        retailstocK2: Record "Retail Stock update";
        lastEntryNo: Integer;
        SalesPrice: Record "Sales Price";
        unitCost: Decimal;


    begin
        Clear(lastEntryNo);
        unitCost := 0;

        retailstocK2.Reset();
        retailstocK2.SetCurrentKey("Ledger Entry No.");
        if retailstocK2.FindLast() then
            lastEntryNo := retailstocK2."Ledger Entry No.";
        invsetup.Get();
        ItemLedgerEntry.SetFilter("Location Code", invsetup."POS Central Location");
        ItemLedgerEntry.SetFilter("Entry No.", '>%1', lastEntryNo);
        ItemLedgerEntry.SetFilter(Quantity, '>=%1', 1);
        if ItemLedgerEntry.FindSet() then begin
            repeat
                ValueEntry.Reset();
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                if ValueEntry.FindFirst() then begin
                    RetailStockUpdate.Reset();
                    RetailStockUpdate.SetRange("Ledger Entry No.", ItemLedgerEntry."Entry No.");
                    if not RetailStockUpdate.FindFirst() then begin

                        RetailStockUpdate.Init();
                        RetailStockUpdate."Document No." := ItemLedgerEntry."Document No.";
                        RetailStockUpdate."Posting Date" := ItemLedgerEntry."Posting Date";
                        RetailStockUpdate."Expiry Date" := ItemLedgerEntry."Expiration Date";
                        RetailStockUpdate."Line No." := ItemLedgerEntry."Document Line No.";
                        RetailStockUpdate."Date Created" := today;
                        RetailStockUpdate."Supplier ID" := invsetup."Retail Stock SupplierID";
                        RetailStockUpdate.StoreID := '1';
                        RetailStockUpdate."Ledger Entry No." := ItemLedgerEntry."Entry No.";
                        RetailStockUpdate."Entry No." := ItemLedgerEntry."Entry No.";

                        RetailStockUpdate.OutletID := invsetup."Retail Stock outletID";
                        RetailStockUpdate."Location code" := ItemLedgerEntry."Location Code";
                        RetailStockUpdate.Outlet := ItemLedgerEntry."Description 2";

                        itemBarcode.Reset();
                        itemBarcode.SetRange("Item No.", ItemLedgerEntry."Item No.");
                        itemBarcode.SetRange("Unit Of Measure", ItemLedgerEntry."Unit of Measure Code");
                        if itemBarcode.FindFirst() then begin
                            RetailStockUpdate."Clearwox Item Code" := itemBarcode."Clearwox Product ID";
                            RetailStockUpdate."Item No." := itemBarcode."Item No.";
                            RetailStockUpdate."Product ID" := itemBarcode."Clearwox Product ID";

                            if itemrec2.Get(itemBarcode."Item No.") then
                                RetailStockUpdate."Item Description" := itemBarcode."Unit Of Measure" + ' ' + itemrec2.Description;


                        end else begin
                            itemrec.Reset();
                            itemrec.SetRange("No.", ItemLedgerEntry."Item No.");
                            if itemrec.FindFirst() then begin
                                RetailStockUpdate."Clearwox Item Code" := itemrec."Clearwox Item ID";
                                RetailStockUpdate."Product ID" := itemrec."Clearwox Item ID"; //itemrec.barcode;
                                RetailStockUpdate."Item No." := itemrec."No.";
                                RetailStockUpdate."Item Description" := itemrec.Description;


                            end;


                        end;

                        RetailStockUpdate."Batch Number" := ItemLedgerEntry."Lot No.";
                        RetailStockUpdate."Location Code" := ItemLedgerEntry."Location Code";
                        RetailStockUpdate."Quantity" := ItemLedgerEntry.Quantity;
                        RetailStockUpdate."Unit of Measure Code" := ItemLedgerEntry."Unit of Measure Code";

                        RetailStockUpdate.price := ValueEntry."Cost per Unit";
                        RetailStockUpdate."Amount Paid" := Round(ValueEntry."Cost Amount (Actual)", 0.01);
                        RetailStockUpdate."Last Updated" := CurrentDateTime();


                        RetailStockUpdate.Insert();
                    end;
                end;
            until ItemLedgerEntry.Next() = 0;
        end;
        //begin
        ItemLedgerEntry.SetFilter("Location Code", invsetup."POS Central Location");
        ItemLedgerEntry.SetFilter("Entry No.", '>%1', lastEntryNo);
        ItemLedgerEntry.SetFilter(Quantity, '%1..%2', 0.00001, 0.999999);
        //ItemLedgerEntry.SetFilter(Quantity, '<%1', 1);
        if ItemLedgerEntry.FindSet() then begin
            repeat
                ValueEntry.Reset();
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                if ValueEntry.FindFirst() then begin
                    qty := 0;
                    unitCost := 0;
                    Clear(unitofmeasure);
                    itemunit2.Reset();
                    itemunit2.SetCurrentKey("Qty. per Unit of Measure");
                    Itemunit2.SetAscending("Qty. per Unit of Measure", true);
                    itemunit2.SetRange("Item No.", ItemLedgerEntry."Item No.");
                    if itemunit2.FindFirst() then begin
                        qty := Round(ItemLedgerEntry.Quantity / itemunit2."Qty. per Unit of Measure", 1);
                        unitofmeasure := itemunit2.Code;
                        unitCost := ValueEntry."Cost per Unit" * itemunit2."Qty. per Unit of Measure";
                    end;

                    RetailStockUpdate.Reset();
                    RetailStockUpdate.SetRange("Ledger Entry No.", ItemLedgerEntry."Entry No.");
                    if not RetailStockUpdate.FindFirst() then begin

                        RetailStockUpdate.Init();
                        RetailStockUpdate."Document No." := ItemLedgerEntry."Document No.";
                        RetailStockUpdate."Posting Date" := ItemLedgerEntry."Posting Date";
                        RetailStockUpdate."Expiry Date" := ItemLedgerEntry."Expiration Date";
                        RetailStockUpdate."Line No." := ItemLedgerEntry."Document Line No.";
                        RetailStockUpdate."Date Created" := today;
                        RetailStockUpdate."Supplier ID" := invsetup."Retail Stock SupplierID";
                        RetailStockUpdate.StoreID := '1';
                        RetailStockUpdate."Ledger Entry No." := ItemLedgerEntry."Entry No.";
                        RetailStockUpdate."Entry No." := ItemLedgerEntry."Entry No.";
                        RetailStockUpdate.Outlet := ItemLedgerEntry."Description 2";

                        RetailStockUpdate.OutletID := invsetup."Retail Stock outletID";
                        RetailStockUpdate."Location code" := ItemLedgerEntry."Location Code";
                        itemrec.Reset();
                        itemrec.SetRange("No.", ItemLedgerEntry."Item No.");
                        if itemrec.FindFirst() then begin
                            RetailStockUpdate."Clearwox Item Code" := itemrec."Clearwox Item ID";
                            RetailStockUpdate."Product ID" := itemrec."Clearwox Item ID"; //itemrec.barcode;
                            RetailStockUpdate."Item No." := itemrec."No.";
                            RetailStockUpdate."Item Description" := itemrec.Description;

                        end;


                        RetailStockUpdate."Batch Number" := ItemLedgerEntry."Lot No.";
                        RetailStockUpdate."Location Code" := ItemLedgerEntry."Location Code";
                        RetailStockUpdate."Quantity" := qty;
                        RetailStockUpdate."Unit of Measure Code" := unitofmeasure;

                        RetailStockUpdate.price := unitCost;
                        RetailStockUpdate."Amount Paid" := Round(ValueEntry."Cost Amount (Actual)", 0.01);
                        RetailStockUpdate."Last Updated" := CurrentDateTime();
                        RetailStockUpdate.Insert();
                    end;
                end;


            until ItemLedgerEntry.Next() = 0;
            // end;
        end;

        RetailStockUpdate.Reset();
        RetailStockUpdate.SetRange("Expiry Date", 0D);
        if RetailStockUpdate.FindSet() then begin
            repeat
                RetailStockUpdate."Expiry Date" := checkExpirydate(RetailStockUpdate."Item No.");
                RetailStockUpdate.Modify();
            until RetailStockUpdate.Next() = 0;
        end;
    end;

    procedure checkExpirydate(ItemNo: Code[20]): Date
    var
        ExpiryRec: Record "Item Expiry Date Upload";
    begin
        ExpiryRec.Reset();
        ExpiryRec.SetRange("Item No.", ItemNo);
        ExpiryRec.SetRange(Used, false);
        ExpiryRec.SetFilter("Expiry Date", '>%1', Today);
        if ExpiryRec.FindLast() then begin
            exit(ExpiryRec."Expiry Date");

        end;

    end;

    procedure PostStock()
    var
        HttpClient: HttpClient;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        JsonObject, RespJObject : JsonObject;
        JsonArray, RespoArray : JsonArray;
        JsonItem: JsonObject;
        RetailStocks: Record "Retail Stock update"; // Replace with your custom table
        JsonText, RespText : Text;
        ResponseText, DocNo : Text;
        Content: HttpContent;
        header: HttpHeaders;
        Amtpaid: Decimal;
        chiapiset: record "CHI API Setup";
        Atext, Ttext : Text;
        RespToken: JsonToken;
        ItemTemp: record Item temporary;

    begin
        //StockUpdate();
        Atext := '';
        Ttext := '';
        docno := '';
        Clear(ItemTemp);

        RetailStocks.reset;
        RetailStocks.SetCurrentKey("Document No.");
        RetailStocks.SetFilter("Product ID", '<>%1', '');
        RetailStocks.setrange(PushedtoAPI, false);
        RetailStocks.SetFilter("Expiry Date", '>%1', Today);
        RetailStocks.SetFilter(Quantity, '>%1', 0);
        if RetailStocks.FindSet() then //begin
            repeat
                ItemTemp.init;
                ItemTemp."No." := RetailStocks."Document No.";
                if ItemTemp.Insert then begin

                end;
            until RetailStocks.Next() = 0;

        if ItemTemp.findset then
            repeat
                RetailStocks.reset;
                RetailStocks.SetCurrentKey("Entry No.");
                RetailStocks.SetFilter("Product ID", '<>%1', '');
                RetailStocks.setrange(PushedtoAPI, false);
                RetailStocks.SetRange("Document No.", ItemTemp."No.");
                RetailStocks.SetFilter("Expiry Date", '>%1', Today);
                RetailStocks.SetFilter(Quantity, '>%1', 0);
                if RetailStocks.FindSet() then //begin
                    repeat
                        // if docno <> RetailStocks."Document No." then begin
                        clear(JsonObject);
                        Amtpaid := 0;
                        // Initialize the JSON object
                        JsonObject.Add('supplierId', RetailStocks."Supplier ID");
                        JsonObject.Add('outletId', RetailStocks.OutletID);
                        JsonObject.Add('storeId', RetailStocks.StoreID);
                        JsonObject.Add('amountPaid', RetailStocks."Amount Paid");
                        JsonObject.Add('date', Today);
                        //end;
                        // Initialize the JSON array for items
                        //Clear(JsonArray);

                        // Loop through the retail stock table to populate the items array
                        clear(JsonItem);
                        JsonItem.Add('productId', RetailStocks."Product ID");
                        JsonItem.Add('quantity', RetailStocks.Quantity);
                        JsonItem.Add('price', RetailStocks.Price);
                        JsonItem.Add('expiry', RetailStocks."Expiry Date");
                        JsonItem.Add('batchNumber', RetailStocks."Batch Number");
                        JsonItem.Add('date', RetailStocks."Date Created");
                        JsonArray.Add(JsonItem);

                        JsonObject.Add('items', JsonArray);

                        RetailStocks."Data pushed to App" := true;
                        RetailStocks.modify;
                        docno := RetailStocks."Document No.";
                    until RetailStocks.Next() = 0;
                //end;

                // Add the items array to the main JSON object

                Clear(JsonText);
                HttpClient.Clear();
                // Convert the JSON object to text        
                JsonObject.WriteTo(JsonText);
                //  Message('%1', JsonText);
                if chiapiset.get(1) then begin

                    // Prepare the HTTP request
                    //HttpRequestMessage.SetRequestUri('https://cloud.storeapp.biz/api/stocks/purchase');
                    HttpRequestMessage.SetRequestUri(chiapiset."API Endpoint3");
                    HttpRequestMessage.Method := 'POST';
                    HttpRequestMessage.GetHeaders(Header);
                    //  if Atext = '' then begin

                    //  Header.clear;
                    Atext := GetAccessDetails(chiapiset, 'Authorize');
                    //Header.Add('Authorization', 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAtNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuYW1lIjoiQURNSU5JU1RSQVRPUiIsInN1YiI6ImFkbWluIiwianRpIjoiYmU1MTI1OTktOTY2MC00NTFmLTljMzEtN2IzZDc3NjI0MGI0IiwiZXhwIjoxNzYzMTEzNDIzLCJpc3MiOiJodHRwczovL3N0b3JlYXBwLmJpeiIsImF1ZCI6Imh0dHBzOi8vc3RvcmVhcHAuYml6In0.RV_J-25B9R5LNxrF-De4EGc5ohDJ5Vqx76BAHaxnE3AS7hHp-jlalQT3og_sObGIzqlFo1JLaztv8cLgJl8ApA'); // Replace {{Key}} with the actual key
                    Header.Add('Authorization', Atext);
                    //  end;
                    HttpRequestMessage.GetHeaders(Header);

                    //Header.Add('Tenant', 'demo');
                    //  if Ttext = '' then begin
                    Ttext := GetAccessDetails(chiapiset, 'Tenant');
                    Header.Add('Tenant', TText);
                    //   end;
                end;
                // Set the content for the request
                content.Clear();
                Content.WriteFrom(JsonText);
                Content.GetHeaders(Header);
                Header.clear;
                Header.Add('Content-Type', 'application/json');
                HttpRequestMessage.Content := Content;


                // Send the HTTP request
                HttpClient.Send(HttpRequestMessage, HttpResponseMessage);

                // Check the response
                if HttpResponseMessage.IsSuccessStatusCode() then begin
                    HttpResponseMessage.Content().ReadAs(ResponseText);
                    RespJObject.ReadFrom(ResponseText);
                    if RespJobject.Get('message', resptoken) then begin

                        RetailStocks.reset;
                        RetailStocks.SetRange(PushedtoAPI, false);
                        RetailStocks.SetRange("Data pushed to App", true);
                        RetailStocks.SetRange("Document No.", ItemTemp."No.");
                        if RetailStocks.FindSet() then
                            repeat
                                //if Respjobject.Get('success', resptoken) then begin
                                RetailStocks."Response message" := resptoken.AsValue().AsText();
                                RetailStocks.PushedtoAPI := true;
                                RetailStocks.Modify();
                            //end;

                            until RetailStocks.next() = 0;
                    end;
                    //Message('Data posted successfully: %1', ResponseText);
                end else begin
                    HttpResponseMessage.Content().ReadAs(ResponseText);
                    RespJObject.ReadFrom(ResponseText);
                    if RespJobject.Get('message', resptoken) then begin

                        RetailStocks.reset;
                        RetailStocks.SetRange(PushedtoAPI, false);
                        RetailStocks.SetRange("Data pushed to App", true);
                        RetailStocks.SetRange("Document No.", ItemTemp."No.");
                        if RetailStocks.FindSet() then
                            repeat
                                // if Respjobject.Get('success', resptoken) then begin
                                RetailStocks."Response message" := resptoken.AsValue().AsText();
                                RetailStocks."Data pushed to App" := false;
                                RetailStocks.Modify();
                            //end;

                            until RetailStocks.next() = 0;
                    end;
                    //Error('Failed to post data. Status: %1. Response: %2', HttpResponseMessage.HttpStatusCode, ResponseText);
                end;
                clear(HttpRequestMessage);
            until ItemTemp.Next() = 0;
    end;

    procedure CheckSalesPrice()
    var
        SalesPrice: Record "Sales Price";
        invset: Record "Inventory Setup";
        retailpricelog: Record "Retail Price Change Log";
        itemrec: Record Item;
        ItemUnit, ItemUnit2, ItemUnit3 : Record "Item Unit of Measure";
        BaseQty: Decimal;
        itembarcode: Record "Item Barcodes Unit of Measure";
        item2: Record Item;
        isProcessed: Boolean;
    begin
        invset.Get();
        //check if FG items for retail price group have released new prices
        SalesPrice.SetRange("Sales Type", SalesPrice."Sales Type"::"Customer Price Group");
        SalesPrice.SetRange("Sales Code", invset."Retail Price Group");
        SalesPrice.SetRange("Approval Status", SalesPrice."Approval Status"::Released);
        SalesPrice.SetFilter("Starting Date", '<=%1', Today);
        // SalesPrice.SetFilter("Unit of Measure Code", '<>%1', '');

        if SalesPrice.FindSet() then begin
            repeat
                // if SalesPrice."Item No." <> '' then begin
                retailpricelog.Reset();
                retailpricelog.SetRange("Item No.", SalesPrice."Item No.");
                retailpricelog.SetRange("Starting Date", SalesPrice."Starting Date");
                retailpricelog.SetRange("Ending Date", SalesPrice."Ending Date");
                retailpricelog.SetRange("Unit of Measure", SalesPrice."Unit of Measure Code");
                if not retailpricelog.FindSet() then begin
                    BaseQty := 1;
                    // ItemUnit2.Reset();
                    // ItemUnit2.SetRange("Item No.", SalesPrice."Item No.");
                    // ItemUnit2.SetRange(Code, itemrec."Base Unit of Measure");
                    // if ItemUnit2.FindFirst() then
                    //     BaseQty := ItemUnit2."Qty. per Unit of Measure";                       
                    itembarcode.Reset();
                    itembarcode.SetRange("Item No.", SalesPrice."Item No.");
                    itembarcode.SetRange("Unit Of Measure", SalesPrice."Unit of Measure Code");
                    if itembarcode.FindFirst() then begin
                        item2.Reset();
                        item2.SetRange("No.", itembarcode."Item No.");
                        if item2.FindFirst() then begin
                            RetailPriceLog.Init();
                            RetailPriceLog."Item No." := SalesPrice."Item No.";
                            retailpricelog."Item Description" := itembarcode."Unit Of Measure" + ' ' + item2.Description;
                            RetailPriceLog."Source Table" := SalesPrice.TableName;
                            RetailPriceLog."New Price" := SalesPrice."Unit Price";
                            RetailPriceLog."Change Date" := CurrentDateTime();
                            retailpricelog."Unit of Measure" := SalesPrice."Unit of Measure Code";
                            retailpricelog."Bar Code" := itembarcode.Barcode;
                            retailpricelog."Clearwox Item Code" := itembarcode."Clearwox Product ID";
                            retailpricelog."Starting Date" := SalesPrice."Starting Date";
                            retailpricelog."Ending Date" := SalesPrice."Ending Date";
                            retailpricelog."Unit - UOM" := SalesPrice."Unit of Measure Code";
                            retailpricelog."Unit Qty" := BaseQty;

                            // // Handle unit conversion logic
                            // if SalesPrice."Unit of Measure Code" <> item2."Base Unit of Measure" then begin
                            //     ItemUnit.Reset();
                            //     ItemUnit.SetRange("Item No.", SalesPrice."Item No.");
                            //     ItemUnit.SetRange(Code, SalesPrice."Unit of Measure Code");
                            //     if ItemUnit.FindFirst() then begin
                            //         if ItemUnit."Qty. per Unit of Measure" <> 0 then 
                            //             retailpricelog."Unit qty" := Round((BaseQty / ItemUnit."Qty. per Unit of Measure"), 1);
                            //         retailpricelog."Unit - UOM" := SalesPrice."Unit of Measure Code";
                            //     end;
                            // end else begin
                            //     ItemUnit3.Reset();
                            //     ItemUnit3.SetCurrentKey("Item No.", "Qty. per Unit of Measure");
                            //     ItemUnit3.SetRange("Item No.", SalesPrice."Item No.");
                            //     ItemUnit3.SetFilter("Qty. per Unit of Measure", '>0');  // Ensure positive conversion factor
                            //     if ItemUnit3.FindFirst() then begin
                            //         if ItemUnit3."Qty. per Unit of Measure" <> 0 then  // Protection against division by zero
                            //             retailpricelog."Wholesale Qty" := Round((BaseQty / ItemUnit3."Qty. per Unit of Measure"), 1);
                            //         retailpricelog."Wholesale - UOM" := item2."Base Unit of Measure";
                            //     end;
                            // end;

                            RetailPriceLog.Insert();
                        end;
                    end else begin

                        if itemrec.Get(SalesPrice."Item No.") then begin
                            if (not itemrec.Blocked) and (itemrec.BarCode <> '') then begin
                                // BaseQty := 1;
                                // ItemUnit2.Reset();
                                // ItemUnit2.SetRange("Item No.", SalesPrice."Item No.");
                                // ItemUnit2.SetRange(Code, itemrec."Base Unit of Measure");
                                // if ItemUnit2.FindFirst() then
                                //     BaseQty := ItemUnit2."Qty. per Unit of Measure";

                                // Initialize the retail price log record
                                BaseQty := 1;
                                RetailPriceLog.Init();
                                RetailPriceLog."Item No." := SalesPrice."Item No.";
                                retailpricelog."Item Description" := itemrec.Description;
                                RetailPriceLog."Source Table" := SalesPrice.TableName;
                                RetailPriceLog."New Price" := SalesPrice."Unit Price";
                                RetailPriceLog."Change Date" := CurrentDateTime();
                                retailpricelog."Unit of Measure" := SalesPrice."Unit of Measure Code";
                                retailpricelog."Bar Code" := itemrec.BarCode;
                                retailpricelog."Clearwox Item Code" := itemrec."Clearwox Item ID";
                                retailpricelog."Starting Date" := SalesPrice."Starting Date";
                                retailpricelog."Ending Date" := SalesPrice."Ending Date";
                                retailpricelog."Unit - UOM" := SalesPrice."Unit of Measure Code";
                                retailpricelog."Unit Qty" := BaseQty;

                                // Handle unit conversion logic
                                // if SalesPrice."Unit of Measure Code" <> itemrec."Base Unit of Measure" then begin
                                //     ItemUnit.Reset();
                                //     ItemUnit.SetRange("Item No.", SalesPrice."Item No.");
                                //     ItemUnit.SetRange(Code, SalesPrice."Unit of Measure Code");
                                //     if ItemUnit.FindFirst() then begin
                                //         if ItemUnit."Qty. per Unit of Measure" <> 0 then  // Protection against division by zero
                                //             retailpricelog."Unit qty" := Round((BaseQty / ItemUnit."Qty. per Unit of Measure"), 1);
                                //         retailpricelog."Unit - UOM" := SalesPrice."Unit of Measure Code";
                                //     end;
                                // end else begin
                                //     ItemUnit3.Reset();
                                //     ItemUnit3.SetCurrentKey("Item No.", "Qty. per Unit of Measure");
                                //     ItemUnit3.SetRange("Item No.", SalesPrice."Item No.");
                                //     ItemUnit3.SetFilter("Qty. per Unit of Measure", '>0');  // Ensure positive conversion factor
                                //     if ItemUnit3.FindFirst() then begin
                                //         if ItemUnit3."Qty. per Unit of Measure" <> 0 then  // Protection against division by zero
                                //             retailpricelog."Wholesale Qty" := Round((BaseQty / ItemUnit3."Qty. per Unit of Measure"), 1);
                                //         retailpricelog."Wholesale - UOM" := itemrec."Base Unit of Measure";
                                //     end;
                                // end;

                                RetailPriceLog.Insert();
                            end;
                        end;
                    end;
                end;
            // end;
            until SalesPrice.Next() = 0;
        end;
    end;

    procedure UpdateProductPrices()
    var
        HttpClient: HttpClient;
        HttpRequestMessage: HttpRequestMessage;
        HttpResponseMessage: HttpResponseMessage;
        JsonObject, APIJsonObject, ResJObject, WJObject, WHJobject, Wholesalesobj, JsonPayload, PriceLevelObj, WholesaleObj, PriceProfileObj : JsonObject;
        JsonArray, Warray, Whsarray, WH2, PriceLevelsArray, WholesalesArray, PriceProfilesArray : JsonArray;
        JsonPriceLevel: JsonObject;
        PriceChangeLog, PriceChangeLog2 : Record "Retail Price Change Log";
        APIEndpoint: Text;
        header, Header2 : HttpHeaders;
        ResponseText, APIDatatext, Changetext, WHText, JsonTextWriter : Text;
        ProductCode, ID, UpdatePayload : Text;
        idcode: code[100];
        retailinvupdate: codeunit "Retail Inventory Update";
        content: HttpContent;
        chiapiset: record "CHI API Setup";
        AText, Ttext : Text;
        RespToken, WHToken, WHToken2, WHT2 : jsontoken;
        WH: Integer;
        ItemRec: Record item;
        RetailProducts: Record "Retail Products Log";
    begin

        CheckSalesPrice();

        PriceChangeLog.Reset();
        PriceChangeLog.SetRange(PushedtoAPI, false);
        pricechangelog.setfilter("Bar code", '<>%1', '');
        if PriceChangeLog.FindSet() then begin
            repeat
                RetailProducts.Reset();
                RetailProducts.SetRange(Code, PriceChangeLog."Bar Code");
                RetailProducts.SetRange(Unit, PriceChangeLog."Unit of Measure");
                if RetailProducts.FindFirst() then begin
                    RetailProducts.Price := PriceChangeLog."New Price";
                    RetailProducts.Status := RetailProducts.Status::Pending;
                    RetailProducts.ProductID := PriceChangeLog."Clearwox Item Code";
                    RetailProducts.ResponseSuccess := false;
                    RetailProducts.ResponseMessage := '';
                    RetailProducts.Modify();
                end;
            until PriceChangeLog.Next() = 0;
        end;
        UpdateItemInRetail();



        // Loop through the Price Change Log
        // ID := '';
        // if RetailProducts.findset then begin
        //     repeat
        //         /* PriceChangeLog.SetRange(PushedtoAPI, false);
        //         pricechangelog.setfilter("Bar code", RetailProducts.Code);
        //         if PriceChangeLog.FindSet() then begin */
        //         // repeat
        //         clear(JsonObject);

        //         APIDatatext := retailinvupdate.GetProductfromAPI(RetailProducts.Code);
        //         // Prepare the JSON body

        //         /*  Clear(TempPriceChange);
        //         TempPriceChange.init;
        //         TempPriceChange := PriceChangeLog;
        //         temppricechange.insert;  */
        //         UpdatePayload := CreatePayloadItemUpdate(APIDatatext, RetailProducts.Code);

        //         if chiapiset.get(1) then begin
        //             Atext := '';
        //             Ttext := '';
        //             // APIEndpoint := 'https://cloud.storeapp.biz/api/products/363897002778' + PriceChangeLog."Bar Code";
        //             APIEndpoint := chiapiset."API Endpoint2" + '/' + PriceChangeLog."Bar Code";

        //             //JsonObject.WriteTo(UpdatePayload);
        //             // Prepare the HTTP request
        //             Clear(HttpRequestMessage);
        //             HttpRequestMessage.SetRequestUri(APIEndpoint);
        //             //HttpRequestMessage.Content().WriteFrom(Changetext);
        //             HttpRequestMessage.Method := 'PUT'; // Use POST or PUT based on API
        //                                                 //Header.Clear;
        //             HttpRequestMessage.GetHeaders(Header);
        //             Atext := GetAccessDetails(chiapiset, 'Authorize');
        //             // Header.Add('Authorization', 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAtNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuYW1lIjoiQURNSU5JU1RSQVRPUiIsInN1YiI6ImFkbWluIiwianRpIjoiYmU1MTI1OTktOTY2MC00NTFmLTljMzEtN2IzZDc3NjI0MGI0IiwiZXhwIjoxNzYzMTEzNDIzLCJpc3MiOiJodHRwczovL3N0b3JlYXBwLmJpeiIsImF1ZCI6Imh0dHBzOi8vc3RvcmVhcHAuYml6In0.RV_J-25B9R5LNxrF-De4EGc5ohDJ5Vqx76BAHaxnE3AS7hHp-jlalQT3og_sObGIzqlFo1JLaztv8cLgJl8ApA'); // Replace {{Key}} with the actual key
        //             Header.Add('Authorization', Atext);
        //             HttpRequestMessage.GetHeaders(Header);
        //             //Header.Add('Tenant', 'demo');
        //             Ttext := GetAccessDetails(chiapiset, 'Tenant');
        //             Header.Add('Tenant', Ttext);
        //         end;
        //         Content.WriteFrom(UpdatePayload);
        //         Content.GetHeaders(Header);
        //         Header.clear;
        //         Header.Add('Content-Type', 'application/json');
        //         HttpRequestMessage.Content := Content;

        //         // Send the request
        //         HttpClient.Send(HttpRequestMessage, HttpResponseMessage);

        //         // Check the response
        //         if HttpResponseMessage.IsSuccessStatusCode() then begin
        //             HttpResponseMessage.Content().ReadAs(ResponseText);
        //             ResJObject.ReadFrom(ResponseText);
        //             if ResJObject.Get('message', RespToken) then begin
        //                 PriceChangeLog2.reset;
        //                 PriceChangeLog2.SetRange("Bar Code", RetailProducts.Code);
        //                 if PriceChangeLog2.FindSet() then
        //                     repeat
        //                         PriceChangeLog2.PushedtoAPI := true;
        //                         PriceChangeLog2."Response Message" := RespToken.AsValue().AsText();
        //                         PriceChangeLog2.modify;
        //                     until PriceChangeLog2.Next() = 0;
        //             end;
        //             // Message('Product prices updated successfully. Response: %1', ResponseText);
        //         end else begin
        //             HttpResponseMessage.Content().ReadAs(ResponseText);
        //             ResJObject.ReadFrom(ResponseText);
        //             if ResJObject.Get('message', RespToken) then begin
        //                 PriceChangeLog2.reset;
        //                 PriceChangeLog2.SetRange("Bar Code", RetailProducts.Code);
        //                 if PriceChangeLog2.FindSet() then
        //                     repeat
        //                         PriceChangeLog2.PushedtoAPI := false;
        //                         PriceChangeLog2."Response Message" := RespToken.AsValue().AsText();
        //                         PriceChangeLog2.modify;
        //                     until PriceChangeLog2.Next() = 0;

        //             end;
        //             // Error('Failed to update product prices. Response Code: %1 --- %2', HttpResponseMessage.HttpStatusCode(), ResponseText);
        //         end;
        //     // until PriceChangeLog.Next() = 0;
        //     //end;// else
        //     //Message('No price changes to process.');
        //     until RetailProducts.Next() = 0;
        // end;
    end;

    procedure GetOutlets(): Text
    var
        HttpClient: HttpClient;
        HttpResponseMessage: HttpResponseMessage;
        header, Header2 : HttpHeaders;
        HttpRequestMessage: HttpRequestMessage;
        ResponseText: Text;
        BaseUrl: Text;
        Token: Text;
        chiapiset: record "CHI API Setup";
        btext, Ttext : text;
    begin
        if chiapiset.Get(1) then begin
            //BaseUrl := 'https://cloud.storeapp.biz/api/outlets';
            BaseUrl := chiapiset."API Endpoint4";

            HttpRequestMessage.SetRequestUri(BaseUrl);
            //HttpRequestMessage.Content().WriteFrom(Changetext);
            HttpRequestMessage.Method := 'GET'; // Use POST or PUT based on API
            HttpRequestMessage.GetHeaders(Header);
            // Header.Add('Authorization', 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAtNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuYW1lIjoiQWRtaW5pc3RyYXRvciIsInN1YiI6ImFkbWluIiwianRpIjoiZDcxZTYzMTYtODM5Yi00MTlhLWJlOTAtNDg5NzZiNWViNjhiIiwiZXhwIjoxNzc1MTM3Mjk3LCJpc3MiOiJodHRwczovL3N0b3JlYXBwLmJpeiIsImF1ZCI6Imh0dHBzOi8vc3RvcmVhcHAuYml6In0.kk42z1TZVMYah46ki7lPgT8NJt6vQjtJZHUNwUo8i7XLi7Trl2dKSolwyr3cCUTKKkv-gAz8_NgvMXTzFK2s4Q');
            // Replace {{Key}} with the actual key
            // btext := GetAccessDetails(chiapiset, 'Authorize');
            // Header.Add('Authorization', btext);

            Header.Add('Authorization', GetAccessDetails(chiapiset, 'Authorize'));
            HttpRequestMessage.GetHeaders(Header);
            ttext := GetAccessDetails(chiapiset, 'Tenant');
            //Header.Add('Tenant', 'chilimited');
            Header.Add('Tenant', Ttext);
        end;
        // HttpRequestMessage.Clear(header);
        //HttpRequestMessage.ContentHeaders().Add('Content-Type', 'application/json');
        // Set the content for the request
        //Content.WriteFrom(changetext);
        // Content.GetHeaders(Header);
        //Header.clear;
        //  Header.Add('Content-Type', 'application/json');
        // HttpRequestMessage.Content := Content;

        if HttpClient.send(HttpRequestMessage, HttpResponseMessage) then begin
            if HttpResponseMessage.IsSuccessStatusCode() then begin
                HttpResponseMessage.Content().ReadAs(ResponseText);
                //Message('%1 -------', ResponseText);
                exit(ResponseText); // Return the API response
            end;
            //else
            //  Error('Failed to fetch outlets. Response Code: %1', HttpResponseMessage.HttpStatusCode());
        end;
        // else
        //  Error('HTTP request failed.');
    end;

    procedure AssignOutletID()
    var

        JsonObject, Dobject : JsonObject;
        JsonArray, Darray : JsonArray;
        JToken, Dtoken, Datoken : JsonToken;
        JsonValue: JsonValue;
        OutletId, i : Integer;
        CustomerRec: Record Customer;
        c: Integer;
        Dtext, ID, OutlText : text;
        INVSET: Record "Inventory Setup";
    begin
        // Message('%1:', GetOutlets());
        OutlText := GetOutlets();

        JsonObject.ReadFrom(OutlText);
        invset.Get();


        if JsonObject.Get('data', JToken) then begin
            JsonArray := JToken.AsArray();
            //JsonArray.Add(JToken);
            i := JsonArray.Count;
            //Message('%1', i);
            for C := 0 to (i - 1) do begin

                JsonArray.Get(c, dToken);
                dToken.WriteTo(dtext);

                DObject.ReadFrom(Dtext);
                if Dobject.Get('name', dtoken) then begin
                    if dtoken.AsValue().IsNull() then
                        ID := '' else begin
                        // Update Customer Record
                        CustomerRec.SetRange("Global Dimension 1 Code", INVSET."Retail Acct Location");
                        customerRec.setrange("Outlet Area", dtoken.AsValue().AsCode());
                        // CustomerRec.Setfilter(customerrec."Clearwox Outlet ID", '%1', 0);
                        if customerrec.Findfirst() then begin
                            // OutletId := 
                            if Dobject.Get('id', datoken) then begin
                                if datoken.AsValue().IsNull() then
                                    CustomerRec."Clearwox Outlet ID" := 0 else
                                    CustomerRec."Clearwox Outlet ID" := datoken.AsValue().AsInteger();
                                customerRec.Modify(true);
                            end;
                            // Message('Outlet ID %1 assigned to customer %2.', OutletId, customerRec."No.");
                        end;

                    end;
                end;
            end;
        end; //else
             //Error('No outlets found in the API response.');

    end;
    //end;

    procedure GetAccessDetails(var CHIAPIsetup: Record "CHI API Setup"; field: Text[10]): Text
    var
        ChiInStream: InStream;
        CHIToken: Text[2048];

    begin
        CHIToken := '';

        case field of
            'Authorize':
                begin
                    CHIAPIsetup.Calcfields(Authorization);
                    If CHIAPIsetup.Authorization.HasValue() then begin
                        CHIAPIsetup.Authorization.CreateInStream(CHIInStream);
                        CHIInStream.Read(CHIToken);
                        exit(CHIToken);
                    end;
                end;

            'Tenant':
                begin
                    CHIAPIsetup.Calcfields("Tenant ID");
                    If CHIAPIsetup."Tenant ID".HasValue() then begin
                        CHIAPIsetup."Tenant ID".CreateInStream(CHIInStream);
                        CHIInStream.Read(CHIToken);
                        exit(CHIToken);
                    end;
                end;
        end;
    end;
    //create payload for Item update
    procedure CreatePayloadItemUpdate(var itemfromAPI: text; BCode: code[20]): Text
    var
        RetailProductRec: Record "Retail Products Log";
        JsonPayload, JPayload2, ProductPayload, WholesaleItem : JsonObject;
        PriceLevelsArray: JsonArray;
        WholesalesArray: JsonArray;
        PriceProfilesArray: JsonArray;
        PriceLevelObj: JsonObject;
        WholesaleObj: JsonObject;
        PriceProfileObj, IDObj, IDObj2 : JsonObject;
        ProdArray, PLArray, WHArray, PArray, IDArray : JsonArray;
        Prodjson, Respjson, datajson : JsonObject;
        jstoken, reptoken, IDToken, IDToken2, WholesalesToken : JsonToken;
        JsonTextWriter, JsonTextWriter2, RespTxt, SuccessMessage, IDText, IDText2, WSDescription : Text;
        IDNo: code[20];
        itemrec: record item;
        retailinvupdate: codeunit "Retail Inventory Update";
        i: Integer;
        TempPriceChange: record "Retail Price Change Log";
    begin
        //init
        JsonPayload.Add('id', '');
        JsonPayload.Add('code', '');
        JsonPayload.Add('description', '');
        JsonPayload.Add('genericName', '');
        JsonPayload.Add('manufacturerId', '');
        JsonPayload.Add('categoryId', '');
        JsonPayload.Add('cost', 0);
        JsonPayload.Add('unit', '');
        JsonPayload.Add('recurringQty', 0);
        JsonPayload.Add('markup', 0);
        JsonPayload.Add('price', 0);
        JsonPayload.Add('taxable', false);
        JsonPayload.Add('watched', false);
        JsonPayload.Add('online', false);
        JsonPayload.Add('rawMaterial', false);
        JsonPayload.Add('produce', false);
        JsonPayload.Add('priceLevels', '');
        JsonPayload.Add('Wholesales', '');
        JsonPayload.Add('priceProfiles', '');

        PriceLevelObj.Add('description', '');
        PriceLevelObj.Add('quantity', '');
        PriceLevelObj.Add('unit', '');
        PriceLevelObj.Add('price', '');
        PriceProfileObj.Add('profileId', '');
        PriceProfileObj.Add('price', '');
        WholesaleObj.add('Id', '');
        WholesaleObj.add('description', '');
        WholesaleObj.add('quantity', '');
        WholesaleObj.add('code', '');
        WholesaleObj.add('price', '');

        // Loop through records in the RetailProduct table with Status not equal to 'Posted'
        //RetailProductRec.Setfilter("Status", '<>%1', 0); //RetailProductRec.Status::Posted);
        // RetailProductRec.SetFilter(RetailProductRec.code, '%1', '8718951584648');
        //if TempPriceChange.findset then begin
        RetailProductRec.SetRange(Code, BCode);
        if RetailProductRec.FindSet() then begin
            //repeat
            //if (RetailProductRec.Status = RetailProductRec.status::New) or (RetailProductRec.Status = RetailProductRec.status::Error) and (RetailProductRec.Code <> '') then begin
            //      if RetailProductRec.Code = '6151100051011' then begin
            // Initialize the main JSON object for each record
            clear(JsonPayload);
            Clear(JsonTextWriter);
            JPayload2.ReadFrom(itemfromAPI);
            JPayload2.Get('data', jstoken);
            ProductPayload := jstoken.AsObject();
            // Create payload JSON
            ProductPayload.Get('id', JsToken);
            JsonPayload.Add('id', JsToken.AsValue().AsText());

            ProductPayload.Get('code', JsToken);
            JsonPayload.Add('code', JsToken.AsValue().AsText());

            ProductPayload.Get('description', JsToken);
            JsonPayload.Add('description', JsToken.AsValue().AsText());

            ProductPayload.Get('genericName', JsToken);
            JsonPayload.Add('genericName', JsToken.AsValue().AsText());

            ProductPayload.Get('manufacturerId', JsToken);
            JsonPayload.Add('manufacturerId', JsToken.AsValue().AsText());

            ProductPayload.Get('categoryId', JsToken);
            JsonPayload.Add('categoryId', JsToken.AsValue().AsText());

            ProductPayload.Get('cost', JsToken);
            JsonPayload.Add('cost', JsToken.AsValue().AsDecimal());

            ProductPayload.Get('unit', JsToken);
            JsonPayload.Add('unit', JsToken.AsValue().AsText());

            ProductPayload.Get('recurringQty', JsToken);
            JsonPayload.Add('recurringQty', JsToken.AsValue().AsInteger());

            ProductPayload.Get('markup', JsToken);
            JsonPayload.Add('markup', JsToken.AsValue().AsDecimal());

            /*    JsonPayload.Add('code', RetailProductRec."Code");
               JsonPayload.Add('description', RetailProductRec."Description");
               JsonPayload.Add('genericName', RetailProductRec."Generic Name");
               JsonPayload.Add('manufacturerId', RetailProductRec."Manufacturer ID");
               JsonPayload.Add('categoryId', RetailProductRec."Category ID");
               JsonPayload.Add('cost', RetailProductRec."Cost");
               JsonPayload.Add('unit', RetailProductRec."Unit");
               JsonPayload.Add('recurringQty', RetailProductRec."Recurring Qty");
               JsonPayload.Add('markup', RetailProductRec."Markup"); */
            ProductPayload.Get('price', JsToken);

            TempPriceChange.reset;
            TempPriceChange.SetRange("Bar Code", RetailProductRec.Code);
            TempPriceChange.SetFilter("Unit Qty", '>%1', 0);
            TempPriceChange.SetFilter("New Price", '>%1', 0);
            if TempPriceChange.FindFirst() then
                // if (TempPriceChange."New Price" <> 0) and (TempPriceChange."Unit Qty" <> 0) then
                JsonPayload.Add('price', TempPriceChange."New Price") else
                JsonPayload.Add('price', JsToken.AsValue().AsDecimal());


            /*   JsonPayload.Add('taxable', RetailProductRec."Taxable");
              JsonPayload.Add('watched', RetailProductRec."Watched");
              JsonPayload.Add('online', RetailProductRec."Online");
              JsonPayload.Add('rawMaterial', RetailProductRec."Raw Material");
              JsonPayload.Add('produce', RetailProductRec."Produce") */


            ProductPayload.Get('taxable', JsToken);
            JsonPayload.Add('taxable', JsToken.AsValue().AsBoolean());

            ProductPayload.Get('watched', JsToken);
            JsonPayload.Add('watched', JsToken.AsValue().AsBoolean());

            ProductPayload.Get('online', JsToken);
            JsonPayload.Add('online', JsToken.AsValue().AsBoolean());

            ProductPayload.Get('rawMaterial', JsToken);
            JsonPayload.Add('rawMaterial', JsToken.AsValue().AsBoolean());

            ProductPayload.Get('produce', JsToken);
            JsonPayload.Add('produce', JsToken.AsValue().AsBoolean());

            // Clear and recreate the arrays for each record
            Clear(PriceLevelsArray);
            Clear(WholesalesArray);
            Clear(PriceProfilesArray);

            // Add Price Level data
            if RetailProductRec."PL Description" <> '' then begin
                clear(PriceLevelObj);
                /*  PriceLevelObj.Add('description', RetailProductRec."PL Description");
                 PriceLevelObj.Add('quantity', RetailProductRec."PL Quantity");
                 PriceLevelObj.Add('unit', RetailProductRec."PL Unit");
                 PriceLevelObj.Add('price', RetailProductRec."PL Price"); */
                PriceLevelsArray.Add(PriceLevelObj);
            end;
            JsonPayload.Add('priceLevels', PriceLevelsArray);

            // Add Price Profile data
            if RetailProductRec."PP Profile ID" <> '' then begin
                clear(PriceProfileObj);
                /*  PriceProfileObj.Add('profileId', RetailProductRec."PP Profile ID");
                 PriceProfileObj.Add('price', RetailProductRec."PP Price"); */
                PriceProfilesArray.Add(PriceProfileObj);
            end;
            JsonPayload.Add('priceProfiles', PriceProfilesArray);

            // // Add Wholesale data
            // if RetailProductRec."WS Description1" <> '' then begin
            //     clear(WholesaleObj);
            //     WholesaleObj.Add('description', RetailProductRec."WS Description1");
            //     WholesaleObj.Add('quantity', RetailProductRec."WS Quantity1");
            //     WholesaleObj.Add('code', RetailProductRec.code);
            //     if (TempPriceChange."New Price" <> 0) and (TempPriceChange."Unit of Measure" = RetailProductRec."WS Description1") then
            //         WholesaleObj.Add('price', TempPriceChange."New Price") else
            //         WholesaleObj.Add('price', RetailProductRec."WS Price1");
            //     WholesalesArray.Add(WholesaleObj);

            // end;
            // // JsonPayload.Add('Wholesales', WholesalesArray);

            // // Add Wholesale data
            // if RetailProductRec."WS Description" <> '' then begin
            //     clear(WholesaleObj);
            //     WholesaleObj.Add('description', RetailProductRec."WS Description");
            //     WholesaleObj.Add('quantity', RetailProductRec."WS Quantity");
            //     WholesaleObj.Add('code', RetailProductRec."WS Code");
            //     if (TempPriceChange."New Price" <> 0) and (TempPriceChange."Unit of Measure" = RetailProductRec."WS Description") then
            //         WholesaleObj.Add('price', TempPriceChange."New Price") else
            //         WholesaleObj.Add('price', RetailProductRec."WS Price");
            //     WholesalesArray.Add(WholesaleObj);

            // end;
            /*   JsonPayload.Add('Wholesales', WholesalesArray); */

            if ProductPayload.Get('wholesaleItems', WholesalesToken) then
                if WholesalesToken.IsArray then begin
                    WholesalesArray := WholesalesToken.AsArray();
                    for i := 0 to WholesalesArray.Count() - 1 do begin

                        WholesalesArray.Get(i, WholesalesToken);
                        WholesaleItem := WholesalesToken.AsObject();

                        clear(WholesaleObj);

                        WholesaleItem.Get('id', JsToken);
                        WholesaleObj.Add('Id', JsToken.AsValue().AsText());

                        WholesaleItem.Get('code', JsToken);
                        WholesaleObj.Add('code', JsToken.AsValue().AsText());

                        WSDescription := '';
                        WholesaleItem.Get('description', JsToken);
                        WholesaleObj.Add('description', JsToken.AsValue().AsText());
                        WSDescription := JsToken.AsValue().AsText();


                        WholesaleItem.Get('quantity', JsToken);
                        WholesaleObj.Add('quantity', JsToken.AsValue().AsInteger());


                        WholesaleItem.Get('price', JsToken);

                        TempPriceChange.reset;
                        TempPriceChange.SetRange("Bar Code", RetailProductRec.Code);
                        TempPriceChange.SetRange("Unit of Measure", WSDescription);
                        TempPriceChange.SetFilter("New Price", '>%1', 0);
                        if TempPriceChange.FindFirst() then
                            // if WSDescription = TempPriceChange."Unit of Measure" then
                            WholesaleObj.Add('price', TempPriceChange."New Price") else
                            WholesaleObj.Add('price', JsToken.AsValue().AsDecimal());

                        /* RetailProductRec."WS Description1" then begin
                            if (TempPriceChange."New Price" <> 0) then
                                //and (TempPriceChange."Unit of Measure" = RetailProductRec."WS Description") then
                                WholesaleObj.Add('price', TempPriceChange."New Price")
                        end else
                            if WSDescription = RetailProductRec."WS Description" then begin
                                if (TempPriceChange."New Price" <> 0) then
                                    //and (TempPriceChange."Unit of Measure" = RetailProductRec."WS Description") then
                                    WholesaleObj.Add('price', TempPriceChange."New Price")
                            end else
                                WholesaleObj.Add('price', JsToken.AsValue().AsDecimal());
*/

                        WholesalesArray.Add(WholesaleObj);
                    end;
                end;
            JsonPayload.Add('Wholesales', WholesalesArray);




            Clear(ProdArray);
            // Add the finalized object to the product array
            ProdArray.Add(JsonPayload);

            ProdArray.WriteTo(JsonTextWriter);
            Clear(JsonPayloadtext);
            // Remove the square brackets
            JsonPayloadtext := CopyStr(JsonTextWriter, 2, StrLen(JsonTextWriter) - 2);
            exit(JsonPayloadtext);

        end;
        // end;
        //until RetailProductRec.Next() = 0;
    end;


    // end;
    // else begin
    // Message('No records found with status not equal to Posted.');
    //end;
    procedure GetProductID(var barcode: Text[100]): Text
    var
        HttpClient: HttpClient;
        HttpResponseMessage: HttpResponseMessage;
        header, Header2 : HttpHeaders;
        HttpRequestMessage: HttpRequestMessage;
        ResponseText: Text;
        BaseUrl: Text;
        Token: Text;
        chiapiset: record "CHI API Setup";
        btext, Ttext : text;
        ItemRec: record Item;
    begin

        if chiapiset.Get(1) then begin
            //BaseUrl := 'https://cloud.storeapp.biz/api/outlets';
            // BaseUrl := chiapiset."API Endpoint4";
            BaseUrl := chiapiset."API Endpoint3" + '/' + barcode;

            HttpRequestMessage.SetRequestUri(BaseUrl);
            //HttpRequestMessage.Content().WriteFrom(Changetext);
            HttpRequestMessage.Method := 'GET'; // Use POST or PUT based on API
            HttpRequestMessage.GetHeaders(Header);
            //Header.Add('Authorization', 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAtNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuYW1lIjoiQURNSU5JU1RSQVRPUiIsInN1YiI6ImFkbWluIiwianRpIjoiYmU1MTI1OTktOTY2MC00NTFmLTljMzEtN2IzZDc3NjI0MGI0IiwiZXhwIjoxNzYzMTEzNDIzLCJpc3MiOiJodHRwczovL3N0b3JlYXBwLmJpeiIsImF1ZCI6Imh0dHBzOi8vc3RvcmVhcHAuYml6In0.RV_J-25B9R5LNxrF-De4EGc5ohDJ5Vqx76BAHaxnE3AS7hHp-jlalQT3og_sObGIzqlFo1JLaztv8cLgJl8ApA'); // Replace {{Key}} with the actual key
            btext := GetAccessDetails(chiapiset, 'Authorize');
            Header.Add('Authorization', btext);

            //    Header.Add('Authorization', GetAccessDetails(chiapiset, 'Authorize'));
            HttpRequestMessage.GetHeaders(Header);
            ttext := GetAccessDetails(chiapiset, 'Tenant');
            //Header.Add('Tenant', 'demo');
            Header.Add('Tenant', Ttext);
        end;
        // HttpRequestMessage.Clear(header);
        //HttpRequestMessage.ContentHeaders().Add('Content-Type', 'application/json');
        // Set the content for the request
        //Content.WriteFrom(changetext);
        // Content.GetHeaders(Header);
        //Header.clear;
        //  Header.Add('Content-Type', 'application/json');
        // HttpRequestMessage.Content := Content;

        if HttpClient.send(HttpRequestMessage, HttpResponseMessage) then begin
            if HttpResponseMessage.IsSuccessStatusCode() then begin
                HttpResponseMessage.Content().ReadAs(ResponseText);
                //Message('%1 -------', ResponseText);
                exit(ResponseText); // Return the API response
            end;
            //else
            //  Error('Failed to fetch outlets. Response Code: %1', HttpResponseMessage.HttpStatusCode());
        end;

        // else
        //  Error('HTTP request failed.');
    end;

    procedure AssignProductID()
    var

        JsonObject, Dobject : JsonObject;
        JsonArray, Darray : JsonArray;
        JToken, Dtoken, Datoken : JsonToken;
        JsonValue: JsonValue;
        OutletId, i : Integer;
        ItemRec, ItemRec2 : Record item;
        c: Integer;
        Dtext, ID, OutlText : text;
        INVSET: Record "Inventory Setup";
        itemBarcode: Record "Item Barcodes Unit of Measure";
    begin
        itemrec.reset;
        itemrec.SetFilter(BarCode, '<>%1', '');
        if itemrec.findset then
            repeat
                OutlText := GetProductID(itemrec.BarCode);
                if OutlText <> '' then begin
                    JsonObject.ReadFrom(OutlText);
                    invset.Get();


                    if JsonObject.Get('data', JToken) then begin
                        JsonArray.Add(JToken);
                        i := JsonArray.Count;
                        //Message('%1', i);
                        for C := 0 to (i - 1) do begin

                            JsonArray.Get(c, dToken);
                            dToken.WriteTo(dtext);

                            DObject.ReadFrom(Dtext);
                            if Dobject.Get('code', dtoken) then begin
                                if dtoken.AsValue().IsNull() then
                                    ID := '' else begin
                                    // Update Customer Record
                                    ItemRec2.SetRange(barcode, dtoken.asvalue.AsCode());
                                    // customerRec.setrange("Outlet Area", dtoken.AsValue().AsCode());
                                    // CustomerRec.Setfilter(customerrec."Clearwox Outlet ID", '%1', 0);
                                    if itemrec2.Findfirst() then begin

                                        // OutletId := 
                                        if Dobject.Get('id', datoken) then begin
                                            if datoken.AsValue().IsNull() then
                                                ItemRec2."Clearwox Item ID" := '' else
                                                ItemRec2."Clearwox Item ID" := datoken.AsValue().AsCode();
                                            ItemRec2.Modify(true);
                                            itemBarcode.Reset();
                                            itemBarcode.SetRange(Barcode, ItemRec2.BarCode);
                                            if itemBarcode.FindFirst() then
                                                itemBarcode."Clearwox Product ID" := datoken.AsValue().AsCode();
                                        end;
                                        // Message('Outlet ID %1 assigned to customer %2.', OutletId, customerRec."No.");
                                    end;

                                end;
                            end;
                        end;
                    end; //else
                         //Error('No outlets found in the API response.');
                end;
            until ItemRec.next = 0;

    end;

    Procedure GetWholeSaleDescription(OutputText: Text; Itemid: Text): Text;
    var
        JsonObject: JsonObject;
        JsToken: JsonToken;
        WholeSaleUofM: Text;
        WJObject: JsonObject;
        wToken: JsonToken;
        idToken: JsonToken;
        JToken: JsonToken;
        JArray: JsonArray;
        JsObject: JsonObject;
        i: Integer;
        Jtext: Text;
        wtext: text;
        descToken: JsonToken;
        idtext: Text;
    begin

        JsonObject.ReadFrom(OutputText);
        if JsonObject.Get('data', JsToken) then begin
            JsObject := JsToken.AsObject();

            if JsObject.Get('wholesaleItems', wToken) then begin
                JArray := wToken.AsArray();

                for i := 0 to (JArray.Count - 1) do begin

                    if JArray.Get(i, JToken) then begin
                        JToken.WriteTo(wtext);

                        WJObject.ReadFrom(wtext);

                        if WJObject.Get('id', idToken) then begin
                            idtext := idToken.AsValue().AsText();

                            if idtext = Itemid then begin

                                if WJObject.Get('description', descToken) then begin

                                    WholeSaleUofM := descToken.AsValue().AsText();

                                    exit(WholeSaleUofM);

                                end;
                            end;
                        end;
                    end;
                end;
            end;
        end;
    end;

    var

        SalesTrans: Record "Retail Sales Transaction Log";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        Customer: Record Customer;
        Item: Record Item;
        SalesPost: Codeunit "Sales-Post";
        TempSalesTrans: Record "Retail Sales Transaction Log" temporary; // Temporary table to group lines
        salesinvHdr: Record "Sales Invoice Header";
        SalesPosted: Boolean;
        JsonPayloadtext: text;
        JsonPayloadtext2: Text;
        LineNo: Integer;

}
