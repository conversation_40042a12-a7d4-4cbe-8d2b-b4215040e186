/// <summary>
/// PageExtension WareHouseShipmnt (ID 50069) extends Record Warehouse Shipment.
/// </summary>
pageextension 50069 WareHouseShipmnt extends "Warehouse Shipment"
{
    layout
    {
        addafter("Posting Date")
        {
            field("Shipping No. Series"; "Shipping No. Series")
            {
                ApplicationArea = all;
                //b2bpksalecorr10
            }
        }

    }
    actions
    {
        modify("&Print")
        {
            Visible = false;
            //b2bpksalecorr10
        }
        modify("P&ost Shipment")
        {
            trigger OnBeforeAction()
            var
                WareHouseShpLneLRec: Record "Warehouse Shipment Line";
                SalHdrGRec: Record "Sales Header";
                TraHdrGRec: Record "Transfer Header";
                TrfLine: Record "Transfer Line";
                SalesHdr: Record "Sales Header";
            BEGIN
                TestField(Status, Status::Released);

                // WareHouseShpLneLRec.reset;
                // WareHouseShpLneLRec.SetRange("No.", "No.");
                // IF WareHouseShpLneLRec.findset then
                //     repeat
                //         OrderNo := WareHouseShpLneLRec."Source No.";//PKONJ9
                //         case WareHouseShpLneLRec."Source Document" of
                //             WareHouseShpLneLRec."Source Document"::"Sales Order":
                //                 BEGIN
                //                     SalHdrGRec.reset;
                //                     SalHdrGRec.SetRange("No.", WareHouseShpLneLRec."Source No.");
                //                     IF SalHdrGRec.findfirst then begin
                //                         //IF (SalHdrGRec."Loading Slip Required" OR (SalHdrGRec."Sales Type" <> SalHdrGRec."Sales Type"::Local)) THEN begin

                //                         IF SalHdrGRec."Loading Slip Required" THEN begin
                //                             //b2bpksalecorr9 start
                //                             IF WareHouseShpLneLRec."Posted Loading Slip No." = '' then begin
                //                                 WareHouseShpLneLRec.VALIDATE("Qty. to Ship", 0);
                //                                 WareHouseShpLneLRec.Modify();
                //                             end else
                //                                 WareHouseShpLneLRec.TestField("Posted Loading Slip Line No.");
                //                             //b2bpksalecorr9 end
                //                         end;
                //                     end
                //                 End;
                //             WareHouseShpLneLRec."Source Document"::"Outbound Transfer":
                //                 BEGIN
                //                     TraHdrGRec.reset;
                //                     TraHdrGRec.SetRange("No.", WareHouseShpLneLRec."Source No.");
                //                     IF TraHdrGRec.findfirst then begin
                //                         IF ((TraHdrGRec."Transfer Type" = TraHdrGRec."Transfer Type"::"Branch Request")) then BEGIN
                //                             //b2bpksalecorr9 start
                //                             //WareHouseShpLneLRec.TestField("Posted Loading Slip No.");
                //                             //WareHouseShpLneLRec.TestField("Posted Loading Slip Line No.");
                //                             IF WareHouseShpLneLRec."Posted Loading Slip No." = '' then begin
                //                                 WareHouseShpLneLRec.VALIDATE("Qty. to Ship", 0);
                //                                 WareHouseShpLneLRec.Modify();
                //                             end else
                //                                 WareHouseShpLneLRec.TestField("Posted Loading Slip Line No.");
                //                             //b2bpksalecorr9 end
                //                         END;
                //                     end;
                //                 END;
                //         end;
                //     until WareHouseShpLneLRec.next = 0;
                //G2S <<<< 230524 update
                //validate customer balance before posting
                //ValidateCustomerBalance(Rec, SalesHdr);
                //G2S >>>> 230524 update
            END;

            trigger OnAfterAction()
            var
                //g2s
                SalHdr: record "Sales Header";
                SalesShipHdr: Record "Sales Shipment Header";
                SalInvHdr: Record "Sales Invoice Header";
                WhShpLine: Record "Warehouse Shipment Line";
                SalesPost: Codeunit "Sales-Post";
            //g2s
            Begin
                //g2s
                WhShpLine.Reset();
                WhShpLine.SetRange(WhShpLine."No.", Rec."No.");
                if WhShpLine.FindFirst() then begin
                    SalHdr.Reset();
                    SalHdr.SetRange("No.", WhShpLine."Source No.");
                    if SalHdr.FindFirst() then begin
                        SalesShipHdr.Reset();
                        SalesShipHdr.SetRange("Order No.", SalHdr."No.");
                        if SalesShipHdr.FindFirst() then begin
                            //message('event publish');
                            //JSONIntegration.MyProcedure(SalHdr, SalInvHdr, SalesShipHdr);
                        end
                    end;
                end;
                //g2s
                GenerateWhseShpmntReport();//Balu 05232021
                //end
            End;
        }
        modify("Post and &Print")
        {
            Visible = false;//Balu 05232021
            trigger OnBeforeAction()
            var
                WareHouseShpLneLRec: Record "Warehouse Shipment Line";
                SalHdrGRec: Record "Sales Header";
                TraHdrGRec: Record "Transfer Header";
                //g2s
                SalHdr: record "Sales Header";
                SalesShipHdr: Record "Sales Shipment Header";
                SalInvHdr: Record "Sales Invoice Header";
                WhShpLine: Record "Warehouse Shipment Line";
            //g2s
            BEGIN
                TestField(Status, Status::Released);
                // WareHouseShpLneLRec.reset;
                // WareHouseShpLneLRec.SetRange("No.", "No.");

                // IF WareHouseShpLneLRec.findset then
                //     repeat
                //         OrderNo := WareHouseShpLneLRec."Source No.";//PKONJ9
                //         case WareHouseShpLneLRec."Source Document" of
                //             WareHouseShpLneLRec."Source Document"::"Sales Order":
                //                 BEGIN
                //                     SalHdrGRec.reset;
                //                     SalHdrGRec.SetRange("No.", WareHouseShpLneLRec."Source No.");
                //                     IF SalHdrGRec.findfirst then begin
                //                         //IF (SalHdrGRec."Loading Slip Required" OR (SalHdrGRec."Sales Type" <> SalHdrGRec."Sales Type"::Local)) THEN begin
                //                         //b2bpksalecorr9 start
                //                         IF WareHouseShpLneLRec."Posted Loading Slip No." = '' then begin
                //                             WareHouseShpLneLRec.VALIDATE("Qty. to Ship", 0);
                //                             WareHouseShpLneLRec.Modify();
                //                         end else
                //                             WareHouseShpLneLRec.TestField("Posted Loading Slip Line No.");
                //                         //b2bpksalecorr9 end
                //                     end
                //                 End;
                //             WareHouseShpLneLRec."Source Document"::"Outbound Transfer":
                //                 BEGIN
                //                     TraHdrGRec.reset;
                //                     TraHdrGRec.SetRange("No.", WareHouseShpLneLRec."Source No.");
                //                     IF TraHdrGRec.findfirst then begin
                //                         //WareHouseShpLneLRec.TestField("Posted Loading Slip Line No.");
                //                         IF WareHouseShpLneLRec."Posted Loading Slip No." = '' then begin
                //                             WareHouseShpLneLRec.VALIDATE("Qty. to Ship", 0);
                //                             WareHouseShpLneLRec.Modify();
                //                         end else
                //                             WareHouseShpLneLRec.TestField("Posted Loading Slip Line No.");
                //                         //b2bpksalecorr9 end
                //                     end;
                //                 END;
                //         end;
                //     until WareHouseShpLneLRec.next = 0;
                // //g2s
                // Begin
                //     //g2s
                //     WhShpLine.Reset();
                //     WhShpLine.SetRange(WhShpLine."No.", Rec."No.");
                //     if WhShpLine.FindFirst() then begin
                //         SalHdr.Reset();
                //         SalHdr.SetRange("No.", WhShpLine."Source No.");
                //         if SalHdr.FindFirst() then begin
                //             SalesShipHdr.Reset();
                //             SalesShipHdr.SetRange("Order No.", SalHdr."No.");
                //             if SalesShipHdr.FindFirst() then begin
                //                 // message('event publish');
                //                 //JSONIntegration.MyProcedure(SalHdr, SalInvHdr, SalesShipHdr);
                //             end
                //         end;
                //     end;
                //g2s
                GenerateWhseShpmntReport();//Balu 05232021

                // end;
            END;
        }
        addafter("F&unctions")
        {
            action("Generate Bin Reclasification Report")
            {
                ApplicationArea = All;
                Image = Report;
                trigger OnAction()
                var
                    Binrecjnl: Record "Bin Reclassfication Jnl";
                begin
                    //50476
                    Binrecjnl.Reset();
                    Binrecjnl.SetRange("Document Type", Binrecjnl."Document Type"::WhsShip);
                    Binrecjnl.SetRange("Document No.", "No.");
                    IF Binrecjnl.FindSet() then
                        Report.RunModal(50476, true, false, Binrecjnl);

                end;
            }

        }


    }
    trigger OnDeleteRecord(): Boolean
    var
        LoadSlipHdrLin: Record "Loading Slip Line";
        PLoadSlipHdrLin: Record "Posted Loading SLip Line";
        SalesReceSetup: Record "Sales & Receivables Setup";
        PostedloadHeader: record "Posted Loading SLip Header";
    begin
        SalesReceSetup.get();
        if not SalesReceSetup."Delete WareHouse Entry" then begin
            LoadSlipHdrLin.RESET;
            LoadSlipHdrLIn.SetRange("Document Type", LoadSlipHdrLIn."Document Type"::Shipment);

            LoadSlipHdrLin.SetRange("No.", "No.");
            IF LoadSlipHdrLin.FindFirst() then
                error('Loading Slip No. %1 is existing for this warehouse shipment.', LoadSlipHdrLin."Document No.");////b2bpksalecorr10
            PLoadSlipHdrLin.RESET;
            PLoadSlipHdrLin.SetRange("Document Type", PLoadSlipHdrLin."Document Type"::Shipment);
            PLoadSlipHdrLin.SetRange("No.", "No.");
            PLoadSlipHdrLin.SetRange(Cancelled, FALSE); //RKD BUG FIX 11-21-22
            IF PLoadSlipHdrLin.FindFirst() then
                IF PostedloadHeader.get(PLoadSlipHdrLin."Document No.") AND (NOT PostedloadHeader.Cancel) THEN  //RKD BUG FIX 11-21-22
                    error('Posted Loading Slip No. %1 is existing for this warehouse shipment.', pLoadSlipHdrLin."Document No.");//b2bpksalecorr10
        end;
    end;

    procedure GenerateWhseShpmntReport()//PKONJ9
    var
        Whseshpmnhdr: Record "Posted Whse. Shipment Header";
        whseShpmnhdr2: Record "Posted Whse. Shipment Header";
        Trnsphmthdr: Record "Transfer Shipment Header";
        Trnsphmthdr2: Record "Transfer Shipment Header";
        Salesshpmthdr: Record "Sales Shipment Header";
        Salesshpmthdr2: Record "Sales Shipment Header";
    begin
        Trnsphmthdr.Reset();
        Trnsphmthdr.SetRange("Transfer Order No.", OrderNo);
        IF Trnsphmthdr.FindLast() then begin
            Trnsphmthdr2.Reset();
            Trnsphmthdr2.SetRange("No.", Trnsphmthdr."No.");
            IF Trnsphmthdr2.FindFirst() then;
            Report.RunModal(50161, true, false, Trnsphmthdr2);
            exit;
        end;
        Salesshpmthdr.Reset();
        Salesshpmthdr.SetRange("Order No.", OrderNo);
        IF Salesshpmthdr.FindLast() then begin
            Salesshpmthdr2.Reset();
            Salesshpmthdr2.SetRange("No.", Salesshpmthdr."No.");
            IF Salesshpmthdr2.FindFirst() then;
            Report.RunModal(208, true, false, Salesshpmthdr2);
            exit;
        end;
        Whseshpmnhdr.Reset();
        Whseshpmnhdr.SetRange("Whse. Shipment No.", "No.");
        IF Whseshpmnhdr.FindLast() then begin
            whseShpmnhdr2.Reset();
            whseShpmnhdr2.SetRange("No.", Whseshpmnhdr."No.");
            if whseShpmnhdr2.FindFirst() then;
            Report.RunModal(7309, true, false, whseShpmnhdr2);
            exit;
        end;
    end;


    procedure ValidateCustomerBalance(var WarehouseHdrVar: Record "Warehouse Shipment Header"; var SalesHdrVar: Record "Sales Header")
    var
        SalesLineAmountTotal: Decimal;
        SalesLineRec: Record "Sales Line";
        WarehouseLine: Record "Warehouse Shipment Line";
        SalesHeader: Record "Sales Header";
        OutSalesLineAmountTotal: Decimal;
        CustLed2: Record "Cust. Ledger Entry";

    begin
        /*  SalesLineAmountTotal := 0;
         if (not WarehouseHdrVar.IsEmpty) and (SalesHdrVar.IsEmpty) then begin
             WarehouseLine.Reset();
             WarehouseLine.SetRange("No.", WarehouseHdrVar."No.");
             if WarehouseLine.FindFirst() then begin
                 SalesHeader.Reset();
                 SalesHeader.SetRange("No.", WarehouseLine."Source No.");
             end;
         end;

         if (not SalesHdrVar.IsEmpty) and (WarehouseHdrVar.IsEmpty) then begin
             SalesHeader.Reset();
             SalesHeader.Copy(SalesHdrVar);
         end;

         if SalesHeader.FindFirst() then begin
             SalesLineRec.Reset();
             SalesLineRec.SETCURRENTKEY("Document Type", "Sell-to Customer No.", "Responsibility Center");
             SalesLineRec.SETRANGE("Document Type", SalesLineRec."Document Type"::Order);
             SalesLineRec.SETRANGE("Sell-to Customer No.", SalesHeader."Sell-to Customer No.");
             SalesLineRec.SETRANGE("Responsibility Center", SalesHeader."Responsibility Center");
             SalesLineRec.SETRANGE("Order Status", SalesLineRec."Order Status"::" ");
             SalesLineRec.CalcSums("Outstanding Amount", "Shipped Not Invoiced");
             SalesLineAmountTotal := SalesLineRec."Outstanding Amount" + SalesLineRec."Shipped Not Invoiced";
             // IF SalesLineRec.FINDSET THEN
             //     REPEAT
             //         SalesLineAmountTotal += (SalesLineRec."Outstanding Amount" + SalesLineRec."Shipped Not Invoiced");
             //     UNTIL SalesLineRec.NEXT = 0;

             Clear(OutSalesLineAmountTotal);
             CustLed2.RESET;
             CustLed2.SETFILTER("Customer No.", SalesHeader."Sell-to Customer No.");
             CustLed2.SetRange(Open, true);
             CustLed2.SetRange("Responsibility Center", SalesHeader."Responsibility Center");
             IF CustLed2.FINDSET THEN
                 repeat
                     CustLed2.CalcFields("Remaining Amount");
                     OutSalesLineAmountTotal += CustLed2."Remaining Amount";
                 until CustLed2.Next = 0;
             SalesHeader.CalcFields("Amount Including VAT");
             IF (SalesLineAmountTotal + SalesHeader."Amount Including VAT") > abs(OutSalesLineAmountTotal) THEN
                 Error('Customer Outstanding balance %1 is less than the sales order Outstanding %2 and current order value %3 .', ABS(OutSalesLineAmountTotal), SalesLineAmountTotal, SalesHeader."Amount Including VAT");
         end; */

    end;

    var
        OrderNo: code[20];//PKONJ9
        //G2S
        JSONIntegration: Codeunit "Json Integration";

}

