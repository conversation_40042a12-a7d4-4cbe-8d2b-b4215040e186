pageextension 50055 GLRegistersPagEx extends "G/L Registers"
{
    layout
    {

    }

    actions
    {
        addafter("Customer &Ledger")
        {
            action("WHT Ledger Entry")
            {
                ApplicationArea = Suite;
                Caption = 'WHT Ledger';
                Image = Ledger;

                trigger OnAction()
                BEGIN
                    GenLedEntGRec.reset;
                    GenLedEntGRec.SetRange("Entry No.", "From Entry No.", "To Entry No.");
                    IF GenLedEntGRec.findfirst then begin
                        WHTLedgEntry.reset;
                        WHTLedgEntry.SetRange("DocNo.", GenLedEntGRec."Document No.");
                        IF WHTLedgEntry.findfirst then begin
                            Page.RunModal(50045, WHTLedgEntry, WHTLedgEntry."DocNo.");
                        end;
                    end;

                END;



            }
        }
    }

    var
        myInt: Integer;
        VendLedEntry: record "Vendor Ledger Entry";
        GenLedEntGRec: Record "G/L Entry";
        WHTLedgEntry: Record "WHT Buffer";
}