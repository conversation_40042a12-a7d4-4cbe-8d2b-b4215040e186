tableextension 50013 ValueEntriesTabExt extends "Value Entry"
{
    fields
    {
        field(50003; "Manual MRS No."; Code[30])
        {
            DataClassification = CustomerContent;
        }
        field(50004; "MRS No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50005; "LOT No."; code[20])
        {
            FieldClass = FlowField;
            CalcFormula = lookup ("Item Ledger Entry"."Lot No." WHERE("entry No." = FIELD("Item Ledger Entry No.")));
        }
        field(50007; "km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Previous Km Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50010; "Branch Prod. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50011; "Prod. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50015; "Production Batch No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Clearing File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50018; "FA No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50019; "Maintenance Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50020; "FA Posting Type"; Option)
        {
            OptionMembers = ,"Acquisition Cost",Depreciation,"Write-Down",Appreciation,"Custom 1","Custom 2",Disposal,Maintenance,"capital work in progress";
            DataClassification = ToBeClassified;
        }
        //Balu 05132021>>
        field(50021; "MRS Ref. No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        //Balu 05132021<<
        //FIX27May2021>>
        field(50022; "Description 2"; Text[200])
        {
            DataClassification = CustomerContent;
        }

        //FIX27May2021<<
        //Fix12Jul2021CWIP>>
        field(50023; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        //Fix12Jul2021CWIP<<
        field(50109; "Item Category Code"; Code[20])
        {
            CalcFormula = Lookup (Item."Item Category Code" WHERE("No." = FIELD("Item No.")));
            Editable = false;
            FieldClass = FlowField;
            TableRelation = "Item Category".Code;
        }
    }

}
