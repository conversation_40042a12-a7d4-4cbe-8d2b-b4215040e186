pageextension 50163 PurchInvSubForm extends "Purch. Invoice Subform"
{
    layout
    {

        addafter("VAT Prod. Posting Group")
        {
            field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
            {
                ApplicationArea = All;
            }
        }
        //FIX05Jun2021>>
        addafter("Line Amount")
        {
            field("WHT Group"; "WHT Group")
            {
                ApplicationArea = ALL;//B2BPK270521
            }

            field("WHT Applicable"; "WHT Applicable")
            {
                ApplicationArea = ALL;
            }
            field("WHT Amount"; "WHT Amount")
            {
                Caption = 'WHT Base Amount';
                ApplicationArea = ALL;
            }
            field("WHT Amount  2"; "WHT Amount 2")
            {
                Caption = 'WHT Amount';
                ApplicationArea = ALL;
            }
        }
        //FIX05Jun2021<<

        modify("Depreciation Book Code")
        {
            Visible = true;
        }

    }
    var
        myInt: Integer;
        pahe: page 5613;
}