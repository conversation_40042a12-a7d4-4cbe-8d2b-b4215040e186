pageextension 50053 BlanketPurchase extends "Blanket Purchase Order"
{
    layout
    {
        addafter(Status)
        {
            field("Contract Start Date"; "Contract Start Date")
            {
                ApplicationArea = all;
            }
            field("End Date"; "End Date")
            {
                ApplicationArea = all;
            }


        }

        modify("Order Date")
        {
            trigger OnAfterValidate()
            var
                PurchLne: Record "Purchase Line";
            BEGIN
                PurchLne.reset;
                PurchLne.SetRange("Document No.", "No.");
                IF PurchLne.findset then
                    repeat
                        PurchLne.validate("Qty. to Receive", 0);
                        PurchLne.Modify();
                    until PurchLne.next = 0;
            END;
        }

    }

    actions
    {

    }
    trigger OnNewRecord(BelowxRec: Boolean) //PKONJu7.2
    var
        DefaultOption: Integer;
        Selection: Integer;
        ShipInvoiceQst: Label '&Local,&Import';
    begin
        IF "No." = '' then begin
            if DefaultOption <= 0 then
                DefaultOption := 1;
            Selection := StrMenu(ShipInvoiceQst, DefaultOption);
            Case Selection of
                1:
                    "Purchase Type" := "Purchase Type"::"Local";
                2:
                    "Purchase Type" := "Purchase Type"::Import;
            end;
        end;
    end;
}