﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b03454b5-3969-487f-99b5-1c868c565b2b</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2.40055cm</Height>
        <Style />
      </Body>
      <Width>17.61204cm</Width>
      <Page>
        <PageHeader>
          <Height>5.922cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="G_L_Entry__Document_No__11">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Document_No_.Value</Value>
                      <Style>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <Top>4.682cm</Top>
              <Left>3.722cm</Left>
              <Height>0.661cm</Height>
              <Width>2.735cm</Width>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="G_L_Entry__Global_Dimension_1_Code_11">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!Global_Dimension_1_Code.Value</Value>
                      <Style>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <Top>1.715cm</Top>
              <Left>10.374cm</Left>
              <Height>0.661cm</Height>
              <Width>6.942cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="ChqNo11">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!ChequeNo.Value</Value>
                      <Style>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Top>4.625cm</Top>
              <Left>15.077cm</Left>
              <Height>0.662cm</Height>
              <Width>2.284cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <VerticalAlign>Bottom</VerticalAlign>
              </Style>
            </Textbox>
            <Image Name="Image1">
              <Source>Database</Source>
              <Value>=First(Fields!CompanyInfo_picture.Value, "DataSet_Result")</Value>
              <MIMEType>image/jpeg</MIMEType>
              <Sizing>FitProportional</Sizing>
              <Top>1.356cm</Top>
              <Left>1.61078cm</Left>
              <Height>1.5cm</Height>
              <Width>1.5cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
          </ReportItems>
          <Style />
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>23.39201cm</PageWidth>
        <LeftMargin>1cm</LeftMargin>
        <RightMargin>2cm</RightMargin>
        <TopMargin>1cm</TopMargin>
        <BottomMargin>0.3cm</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>8ac9afcf-9837-4f29-a615-07f8bbd764c1</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="G_L_Register_No_">
          <DataField>G_L_Register_No_</DataField>
        </Field>
        <Field Name="FORMAT_Doc_">
          <DataField>FORMAT_Doc_</DataField>
        </Field>
        <Field Name="CustName">
          <DataField>CustName</DataField>
        </Field>
        <Field Name="G_L_Entry__Document_No__">
          <DataField>G_L_Entry__Document_No__</DataField>
        </Field>
        <Field Name="G_L_Entry__Posting_Date_">
          <DataField>G_L_Entry__Posting_Date_</DataField>
        </Field>
        <Field Name="G_L_Entry__Global_Dimension_1_Code_">
          <DataField>G_L_Entry__Global_Dimension_1_Code_</DataField>
        </Field>
        <Field Name="CompanyAddr_2_">
          <DataField>CompanyAddr_2_</DataField>
        </Field>
        <Field Name="CompanyAddr_3_">
          <DataField>CompanyAddr_3_</DataField>
        </Field>
        <Field Name="CompanyAddr_4_">
          <DataField>CompanyAddr_4_</DataField>
        </Field>
        <Field Name="BatchName">
          <DataField>BatchName</DataField>
        </Field>
        <Field Name="STRSUBSTNO__Page__1__FORMAT_CurrReport_PAGENO__">
          <DataField>STRSUBSTNO__Page__1__FORMAT_CurrReport_PAGENO__</DataField>
        </Field>
        <Field Name="ChqNo">
          <DataField>ChqNo</DataField>
        </Field>
        <Field Name="TODAY">
          <DataField>TODAY</DataField>
        </Field>
        <Field Name="CompanyInfo__Picture_2_">
          <DataField>CompanyInfo__Picture_2_</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount">
          <DataField>G_L_Entry_Amount</DataField>
        </Field>
        <Field Name="G_L_Entry_AmountFormat">
          <DataField>G_L_Entry_AmountFormat</DataField>
        </Field>
        <Field Name="ACCNO2">
          <DataField>ACCNO2</DataField>
        </Field>
        <Field Name="AccName">
          <DataField>AccName</DataField>
        </Field>
        <Field Name="G_L_Entry__Global_Dimension_1_Code__Control1102152019">
          <DataField>G_L_Entry__Global_Dimension_1_Code__Control1102152019</DataField>
        </Field>
        <Field Name="G_L_Entry__Global_Dimension_2_Code_">
          <DataField>G_L_Entry__Global_Dimension_2_Code_</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount_Control1102152022">
          <DataField>G_L_Entry_Amount_Control1102152022</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount_Control1102152022Format">
          <DataField>G_L_Entry_Amount_Control1102152022Format</DataField>
        </Field>
        <Field Name="G_L_Entry__Description_2_">
          <DataField>G_L_Entry__Description_2_</DataField>
        </Field>
        <Field Name="G_L_Entry_Description">
          <DataField>G_L_Entry_Description</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount_Control1000000022">
          <DataField>G_L_Entry_Amount_Control1000000022</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount_Control1000000022Format">
          <DataField>G_L_Entry_Amount_Control1000000022Format</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount_Control1102152024">
          <DataField>G_L_Entry_Amount_Control1102152024</DataField>
        </Field>
        <Field Name="G_L_Entry_Amount_Control1102152024Format">
          <DataField>G_L_Entry_Amount_Control1102152024Format</DataField>
        </Field>
        <Field Name="AmountInword">
          <DataField>AmountInword</DataField>
        </Field>
        <Field Name="Totamt">
          <DataField>Totamt</DataField>
        </Field>
        <Field Name="TotamtFormat">
          <DataField>TotamtFormat</DataField>
        </Field>
        <Field Name="accno">
          <DataField>accno</DataField>
        </Field>
        <Field Name="accname2">
          <DataField>accname2</DataField>
        </Field>
        <Field Name="hod">
          <DataField>hod</DataField>
        </Field>
        <Field Name="AudName">
          <DataField>AudName</DataField>
        </Field>
        <Field Name="ChequeNo">
          <DataField>ChequeNo</DataField>
        </Field>
        <Field Name="PostedVoucherHead__Created_By_">
          <DataField>PostedVoucherHead__Created_By_</DataField>
        </Field>
        <Field Name="CreatedByName">
          <DataField>CreatedByName</DataField>
        </Field>
        <Field Name="PostedVoucherHead__Account_Checked_by_">
          <DataField>PostedVoucherHead__Account_Checked_by_</DataField>
        </Field>
        <Field Name="HODApprovalName">
          <DataField>HODApprovalName</DataField>
        </Field>
        <Field Name="PostedVoucherHead__HOD_Approval_">
          <DataField>PostedVoucherHead__HOD_Approval_</DataField>
        </Field>
        <Field Name="DivHeadApprovalName">
          <DataField>DivHeadApprovalName</DataField>
        </Field>
        <Field Name="PostedVoucherHead__Finance_Approval_By_">
          <DataField>PostedVoucherHead__Finance_Approval_By_</DataField>
        </Field>
        <Field Name="FinApprovalName">
          <DataField>FinApprovalName</DataField>
        </Field>
        <Field Name="AccountChkByName">
          <DataField>AccountChkByName</DataField>
        </Field>
        <Field Name="PostedVoucherHead__Division_Head_Approval_">
          <DataField>PostedVoucherHead__Division_Head_Approval_</DataField>
        </Field>
        <Field Name="USERID">
          <DataField>USERID</DataField>
        </Field>
        <Field Name="Cheque_No__Caption">
          <DataField>Cheque_No__Caption</DataField>
        </Field>
        <Field Name="B_f_form_previous_page_s__Caption">
          <DataField>B_f_form_previous_page_s__Caption</DataField>
        </Field>
        <Field Name="ContinuedCaption">
          <DataField>ContinuedCaption</DataField>
        </Field>
        <Field Name="TotamtCaption">
          <DataField>TotamtCaption</DataField>
        </Field>
        <Field Name="Doc__Created_By_Caption">
          <DataField>Doc__Created_By_Caption</DataField>
        </Field>
        <Field Name="Account_Cheked_By_Caption">
          <DataField>Account_Cheked_By_Caption</DataField>
        </Field>
        <Field Name="HOD_Approval_Caption">
          <DataField>HOD_Approval_Caption</DataField>
        </Field>
        <Field Name="Finance_Approval_Caption">
          <DataField>Finance_Approval_Caption</DataField>
        </Field>
        <Field Name="Division_Head_Approval_Caption">
          <DataField>Division_Head_Approval_Caption</DataField>
        </Field>
        <Field Name="Printed_By_Caption">
          <DataField>Printed_By_Caption</DataField>
        </Field>
        <Field Name="G_L_Entry_Entry_No_">
          <DataField>G_L_Entry_Entry_No_</DataField>
        </Field>
        <Field Name="G_L_Entry_Source_No_">
          <DataField>G_L_Entry_Source_No_</DataField>
        </Field>
        <Field Name="CompanyInfo_Pic">
          <DataField>CompanyInfo_Pic</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>