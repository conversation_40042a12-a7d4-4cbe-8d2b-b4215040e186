page 50159 "Teller Awaiting Conf. List"
{

    Caption = 'Teller Awaiting Confirmation';
    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Request Teller Receipt";
    SourceTableView = WHERE("Released for Confirmation" = FILTER(true),
                            "Teller Returned" = CONST(false));
    UsageCategory = lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnLookup(var Text: Text): Boolean;
                    begin
                        /*Prasanna
                                                RespCentRec.RESET;
                                                UserRespCentRec.SETRANGE("User ID", USERID);
                                                if UserRespCentRec.FINDSET then begin
                                                    if UserRespCentRec."Resp. Center Code" <> '' then begin
                                                        repeat
                                                            if RespCentRec.GET(UserRespCentRec."Resp. Center Code") then
                                                                RespCentRec.MARK(true);
                                                        until UserRespCentRec.NEXT = 0;
                                                        RespCentRec.MARKEDONLY(true);
                                                        if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                                                            "Responsibility Center" := RespCentRec.Code;
                                                    end else begin
                                                        RespCentRec.RESET;
                                                        RespCentRec.SETCURRENTKEY(Code);
                                                        if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                                                            "Responsibility Center" := RespCentRec.Code;
                                                    end;
                                                end else begin
                                                    RespCentRec.RESET;
                                                    RespCentRec.SETCURRENTKEY(Code);
                                                    if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                                                        "Responsibility Center" := RespCentRec.Code;
                                                end;
                        */
                        RespCentRec.RESET;
                        RespCentRec.SETCURRENTKEY(Code);
                        if PAGE.RUNMODAL(0, RespCentRec) = ACTION::LookupOK then
                            "Responsibility Center" := RespCentRec.Code;
                        //IF PAGE.RUNMODAL(PAGE::"Responsibility Center List")  = ACTION::LookupOK THEN
                    end;

                    trigger OnValidate();
                    begin
                        if "Responsibility Center" = '' then begin
                            CLEAR("Customer No.");
                            CLEAR("Customer Name");
                        end;
                    end;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = true;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Paid By"; "Paid By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank No."; "Bank No.")
                {
                    ApplicationArea = all;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Chq. Value Date"; "Chq. Value Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = true;
                }
                field("Cheque No."; "Cheque No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Cheque Date"; "Cheque Date")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Is Confirmed"; "Teller Is Confirmed")
                {
                    ApplicationArea = all;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                }
                field("Confirmed By"; "Confirmed By")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Confirmation Date"; "Confirmation Date")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Confirmation Time"; "Confirmation Time")
                {
                    ApplicationArea = all;
                    Visible = true;
                }
                field("Released By"; "Released By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released Date"; "Released Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released time"; "Released time")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Last Modified By"; "Last Modified By")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                }
                field("Last Modified Date"; "Last Modified Date")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnOpenPage();
    begin
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);


        /*
        RespCentCount :=0;
        
        UserIDRespCent.SETCURRENTKEY("User ID","Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
         REPEAT
          RespCentCount +=1;
          //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
          IF RespCentCount = 1 THEN
           BuildFilter :=UserIDRespCent."Resp. Center Code"
          ELSE
           BuildFilter+='|' + UserIDRespCent."Resp. Center Code";
         UNTIL UserIDRespCent.NEXT=0;
        
        IF RespCentCount > 0 THEN
         //SETRANGE("Responsibility Center",TempResp[1],TempResp[RespCentCount]);
         SETFILTER("Responsibility Center",BuildFilter);
        */

    end;

    var
        Employee: Record Employee;
        BankTellerConfirmationRec: Record "Request Teller Receipt";
        BankConfirmedTellersRec: Record "Confirmed Teller Receipt";
        Window: Dialog;
        Linecount: Integer;
        OldBankConfirmedTellersRec: Record "Confirmed Teller Receipt";
        Text50000: Label 'You do not have permission to post the Bank confirmation Register.';
        Text50200: Label 'You cannnot post while TellerIsConfirmed is false';
        Text50201: Label 'Posting lines         #2######';
        Text50202: Label 'You do not have permission to post to Bank Confirmed Tellers';
        no: Integer;
        UserSetup: Record "User Setup";
        //UserRespCentRec: Record "UserID Resp. Cent. Lines";Prasanna
        RespCentRec: Record "Responsibility Center";
        DimValRec: Record "Dimension Value";
        GenLedgSetup: Record "General Ledger Setup";
        Text50205: Label 'You do not have permission for Teller/Cheque Awaiting Confirmation.';
        UserMgt: Codeunit "User Setup Management";
        RespCentCount: Integer;
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";//B2BSB.1.0
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

