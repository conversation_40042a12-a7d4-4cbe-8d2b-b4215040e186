page 50002 "Service Master Card"
{
    PageType = Card;
    SourceTable = "Service Master";
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Code"; Code)
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = all;
                }
                field(Released; Released)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

