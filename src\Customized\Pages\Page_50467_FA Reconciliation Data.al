page 50467 "FA Reconciliation Data"
{
    // version TRKIT

    DeleteAllowed = false;
    Editable = true;
    PageType = List;
    SourceTable = FA_VERIFICATION_DATA;
    UsageCategory = Administration;
    ApplicationArea = All;


    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Verification Task Number"; "Verification Task Number")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Asset Number"; "Asset Number")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Tag Number"; "Tag Number")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Current Asset FA Location"; "Current Asset FA Location")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Current Asset ACC Location"; "Current Asset ACC Location")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Current Asset CC Location"; "Current Asset CC Location")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Asset Found Location"; "Asset Found Location")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Asset Found ACC Location"; "Asset Found ACC Location")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Action Taken By"; "Action Taken By")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Action to take"; "Action to take")
                {
                    ApplicationArea = All;
                }
                field("Asset Found CC Location"; "Asset Found CC Location")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Scan Date"; "Scan Date")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Scan By"; "Scan By")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Navision Process Time"; "Navision Process Time")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Navision Process Status"; "Navision Process Status")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Navision Reject Reason"; "Navision Reject Reason")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
    }
}

