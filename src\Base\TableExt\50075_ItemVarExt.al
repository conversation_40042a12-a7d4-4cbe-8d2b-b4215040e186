tableextension 50075 ItemVar extends "Item Variant"
{
    fields
    {
        field(50100; Inventory; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = Sum("Item Ledger Entry".Quantity WHERE("Item No." = FIELD("Item No."), "Location Code" = FIELD("Location Filter"), "Variant Code" = FIELD(Code)));
            Editable = false;

        }
        field(50101; "Part No."; Code[30])
        {
            DataClassification = CustomerContent;
        }
        field(50102; "Place of Use"; Text[30])
        {
            DataClassification = CustomerContent;
        }
        field(50103; "Date Of Refurbished"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50104; "Location Filter"; Code[20])
        {
            FieldClass = FlowFilter;
            TableRelation = Location;
        }
        // project leap
        field(50105; "Multiplication Factor"; Integer)
        {
            DataClassification = CustomerContent;
        }
    }
}