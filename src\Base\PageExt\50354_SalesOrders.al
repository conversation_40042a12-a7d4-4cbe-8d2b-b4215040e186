pageextension 50354 SalesOrders extends "Sales Order List"
{
    layout
    {
        // Add changes to page layout here
    }

    actions
    {
        modify(Reopen)
        {
            Visible = false;
        }
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            var
                CustGRec: Record Customer;
            BEGIN
                //CalPromtionSchemas();//PKONAU3
                CheckResponsilibilityCentre();
                CheckSalesMandValues(True);
                TestField("Order Status", 0);
                TestField("Location Code");
                IF CustGRec.GET("Sell-to Customer No.") AND (CustGRec."Customer Credit type" = CustGRec."Customer Credit type"::"Cash n Carry") then
                    TestField("Applies-to ID");

            end;
        }
        modify(Release)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Order Status", 0);
                TestField("Location Code");
                //CalPromtionSchemas();//PKONAU3
                CheckResponsilibilityCentre();
                CheckSalesMandValues(True);
            END;
        }
        modify(Post)
        {
            trigger OnBeforeAction()
            var
                Cutlr: Record Customer;
            BEGIN
                TestField("Order Status", 0);
                IF "Loading Slip Required" then
                    Error('Loading Slip is required and you can not post the orders directly. Please use Warehouse Shipment and Sales Invoice pages.');
                IF Cutlr.get("Sell-to Customer No.") AND (Cutlr."Customer Credit type" = Cutlr."Customer Credit type"::"Cash n Carry") then
                    IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') then
                        ERRor('Applies to doc no. or Applies to ID must have a value.');
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;

        }

        Modify(PostAndSend)
        {
            trigger OnBeforeAction()
            var
                Cutlr: Record Customer;
            BEGIN
                TestField("Order Status", 0);
                IF "Loading Slip Required" then
                    Error('Loading Slip is required and you can not post the orders directly. Please use Warehouse Shipment and Sales Invoice pages.');
                IF Cutlr.get("Sell-to Customer No.") AND (Cutlr."Customer Credit type" = Cutlr."Customer Credit type"::"Cash n Carry") then
                    IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') then
                        ERRor('Applies to doc no. or Applies to ID must have a value.');
                IF "Document Type" = "Document Type"::"Credit Memo" then
                    TestField("Reason Codes");
            END;
        }
        modify("Create &Warehouse Shipment")
        {
            trigger OnBeforeAction()
            begin
                TestField(Status, Status::Released);
                CheckSalesMandValues(True);
            end;
        }
    }
    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
    begin
        UserSetup.Get(UserId);
        if not UserSetup."Open Sales Order Page" then
            Error('Do not have permissions to open the page');
    end;

    var
        myInt: Integer;
}