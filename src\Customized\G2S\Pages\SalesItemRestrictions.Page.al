page 50600 CHIERP_SalesItemRestrictions
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = CHIERP_SalesItemRestriction;

    layout
    {
        area(Content)
        {
            repeater(Control1)
            {
                field(CHIERP_ParentItemNo; CHIERP_ParentItemNo)
                {
                    ToolTip = 'Specifies the Parent Item No.';
                }
                field(CHIERP_ItemNo; CHIERP_ItemNo)
                {
                    ToolTip = 'Specifies the Sales Mandatory Item No.';
                }
                field(CHIERP_QuantityRestriction; CHIERP_QuantityRestriction)
                {
                    ToolTip = 'Specifies the restriction type';
                }
                field(CHIERP_MinimumQuantity; CHIERP_MinimumQuantity)
                {
                    ToolTip = 'Specifies the minimum quantity if minimum option is selected';
                }
                field(CHIERP_StartingDate; CHIERP_StartingDate)
                {
                    ToolTip = 'Specifies the restriction start date';
                }
                field(CHIERP_EndingDate; CHIERP_EndingDate)
                {
                    ToolTip = 'Specifies the restriction end date';
                }
            }
        }
    }
}