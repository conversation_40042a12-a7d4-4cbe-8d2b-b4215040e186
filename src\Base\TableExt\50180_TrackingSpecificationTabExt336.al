tableextension 50180 TrackSpecTabExt extends "Tracking Specification"
{

    fields
    {
        //>>>> G2S 25/03/25 CAS-01412-X4D4V7
        field(50000; "Manufacturing Date"; Date)
        {
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                ErrMsg: Label 'The item %1 manufacturing date cannot be a future date.';
                Item: Record Item;
                ItemTrackingCode: Record "Item Tracking Code";
                ExpDate: Date;
                ExpErrMsg: label 'Item %1 Expiration date calculation formula not set';
                TrackingErrMsg: label 'Item %1 Tracking code not set';
            BEGIN
                IF Rec."Manufacturing Date" > Today Then
                    Error(ErrMsg, Rec."Item No.");

                InsertManufacturingDate(Rec);
                if Item.Get(Rec."Item No.") then BEGIN
                    if not ItemTrackingCode.Get(Item."Item Tracking Code") then
                        Error(TrackingErrMsg, Rec."Item No.");

                    if Format(Item."Expiration Calculation") = '' then
                        Error(ExpErrMsg, Rec."Item No.");

                    if ItemTrackingCode."Use Expiration Dates" then begin
                        ExpDate := CalcDate(Item."Expiration Calculation", Today);
                        Rec.validate("Expiration Date", ExpDate);
                    end
                END;
            END;
        }
        //>>>> G2S 25/03/25 CAS-01412-X4D4V7
    }
    //Insert Manufacturing date into Reservation Entry Table G2S 25/03/25 CAS-01412-X4D4V7
    procedure InsertManufacturingDate(TrackSpecific: Record "Tracking Specification"): Boolean
    var
        ReservationEntry: Record "Reservation Entry";
        TrackSpecification: Record "Tracking Specification";
    begin
        TrackSpecification.Reset();
        TrackSpecification.Copy(TrackSpecific);
        ReservationEntry.Reset();
        ReservationEntry.SetRange("Source Type", Database::"Purchase Line");
        ReservationEntry.SetFilter("Source ID", TrackSpecification."Source ID");
        ReservationEntry.SetFilter("Source Ref. No.", Format(TrackSpecification."Source Ref. No."));
        if ReservationEntry.FindSet then BEGIN
            ReservationEntry.ModifyAll("Manufacturing Date", TrackSpecification."Manufacturing Date");
            exit(true);
        END;
        exit(false);
    end;
    //Insert Manufacturing date into Reservation Entry Table G2S 25/03/25 CAS-01412-X4D4V7



    procedure InitfromMDV(LPAScrpJrnl: Record "MDV Line")
    var
        ItemJnlTemplate: Record "Item Journal Template";
        PageTemplate: Option Item,Transfer,"Phys Inventory",Revaluation,Consumption,Output,Capacity,"Prod. Order";
        TextST001: Label '%1 journal';
        ToTemplateName: Code[10];
        TextSt002: Label 'MDV';
    begin
        ToTemplateName := TextSt002;
        init;
        SetItemData(LPAScrpJrnl."No.", LPAScrpJrnl.Description, LPAScrpJrnl."Location Code", LPAScrpJrnl."Item Variant Code", '', 1);
        SetSource(DATABASE::"MDV Line", 2, LPAScrpJrnl."Document No.", LPAScrpJrnl."Line No.", 'DEFAULT', LPAScrpJrnl."Line No.");
        SetQuantities(LPAScrpJrnl.Quantity, LPAScrpJrnl.Quantity, LPAScrpJrnl.Quantity, LPAScrpJrnl.Quantity, LPAScrpJrnl.Quantity, LPAScrpJrnl.Quantity, LPAScrpJrnl.Quantity);
    end;

}