page 50145 "WH Posted Mat. Disposal"
{

    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    ModifyAllowed = true;
    PageType = Document;
    SourceTable = "MDV Header";
    SourceTableView = SORTING("MDV No.")
                      ORDER(Ascending)
                      WHERE(Posted = CONST(true));
    UsageCategory = Documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MDV No."; "MDV No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Manual MDV. No"; "Manual MDV. No")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Type"; "Disposal Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                /*field("Indent Dept."; "Indent Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Indent Bus. Unit"; "Indent Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }*/
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                    Editable = true;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Expected Disposal Date"; "Expected Disposal Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(PostedMatDispLineSubform; "WH Posted Mat. Disp. Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Document No." = FIELD("MDV No.");
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Requisition")
            {
                Caption = '&Requisition';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';
                    image = Dimensions;
                    trigger OnAction();
                    begin
                        ShowDocDim();
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Visible = true;
                action("Create Jounal Batch")
                {
                    ApplicationArea = all;
                    Caption = 'Create Jounal Batch';
                    Image = Create;
                    trigger OnAction();
                    begin
                        MDVLineRec.SETCURRENTKEY("Document No.", "Line No.");
                        MDVLineRec.RESET();
                        MDVLineRec.SETRANGE("Document No.", "MDV No.");
                        MDVLineRec.SETFILTER("Qty. to Dispose", '<>%1', 0);
                        if not MDVLineRec.FINDFIRST() then
                            ERROR('No Line with Qty. to Disposal > 0');


                        MDVLineRec.RESET();
                        MDVLineRec.SETCURRENTKEY("Document No.", "Line No.");
                        MDVLineRec.SETRANGE("Document No.", "MDV No.");
                        if MDVLineRec.FINDSET() then
                            repeat
                                MDVLineRec.CALCFIELDS(MDVLineRec."Available Stock");
                                MDVLineRec.CALCFIELDS(MDVLineRec."Quantity Disposed");
                                if MDVLineRec."Qty. to Dispose" > (MDVLineRec.Quantity - MDVLineRec."Quantity Disposed") then
                                    ERROR(Text50200Lbl, (MDVLineRec.Quantity - MDVLineRec."Quantity Disposed"));
                            until MDVLineRec.NEXT() = 0;

                        TESTFIELD("Date of MDV");
                        CreateItemJnlLine();
                    end;
                }
            }
        }
    }

    trigger OnOpenPage();
    begin
        if UserMg.GetMDVFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetMDVFilter());
            FILTERGROUP(0);
        end;
    end;

    var
        MDVLineRec: Record "MDV Line";

        UserMg: Codeunit Mgmt;
        Text50200Lbl: Label 'Qty to dispose must not exceed %1.';
}

