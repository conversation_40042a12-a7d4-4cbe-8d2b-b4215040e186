codeunit 50148 "Close Customer Credit Limit"
{
    trigger OnRun()
    begin
        CustomerCreditLimit.Reset();
        CustomerCreditLimit.SetFilter("Schedule Limit Expiry Date", '<%1', WorkDate());
        CustomerCreditLimit.SetRange("Customer Type", CustomerCreditLimit."Customer Type"::"Credit Customer");//FIX25May2021
        if CustomerCreditLimit.FindSet() then
            repeat
                CustomerCreditLimit.Status := CustomerCreditLimit.Status::Expired;
                CustomerCreditLimit.Modify();
            until CustomerCreditLimit.Next() = 0;
        AddCustCreditLimit.Reset();
        AddCustCreditLimit.SetFilter("Schedule Limit Expiry Date", '<%1', WorkDate());
        if AddCustCreditLimit.FindSet() then
            repeat
                AddCustCreditLimit.Status := AddCustCreditLimit.Status::Expired;
                AddCustCreditLimit.Modify();
            until AddCustCreditLimit.Next() = 0;
    end;

    var
        myInt: Integer;
        CustomerCreditLimit: Record "Cust. Cr. Limit Schedule";
        AddCustCreditLimit: Record "Add. Cust. Cr. Limit Schedule";
}