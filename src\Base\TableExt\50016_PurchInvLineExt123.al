tableextension 50016 PurchInVLin extends "Purch. Inv. Line"
{
    fields
    {
        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Sub Document Type"; Enum SubDocumentType)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Sub Document No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Sub Document Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50009; "PMS No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = PMSManagement;
        }
        field(50010; "Last Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Current Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "Km Covered"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "PMS Unit Price"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Km per Ltr"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Old_PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50019; "Date PMS Availed"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Fuel Avail"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(50026; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50027; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50028; "Budget Name"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50055; "FA Posting Group"; code[20])
        {
            DataClassification = CustomerContent;
        }

        field(50063; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50070; "WHT Applicable"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50802; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50804; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50805; "No.2"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50806; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;//B2BPK270521
        }
        field(50807; "WHT %"; Decimal)
        {
            DataClassification = CustomerContent;//B2BPK270521

        }
        //FIX05Jun2021>>
        field(50808; "WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'WHT Base Amount';

        }
        field(50811; "WHT Amount 2"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'WHT Amount';

        }
        //FIX05Jun2021<<
        field(50809; "Pms Mapped"; Boolean)
        {
            FieldClass = FlowField;
            CalcFormula = Exist ("Original PMS Statement" WHERE("Posting Date" = FIELD("Date PMS Availed"), "Card No." = FIELD("PMS card No."), "Receipt No." = FIELD("Old_PMS Card No."), Quantity = FIELD(Quantity)));

        }
        //FIX05Jul2021>>
        field(50810; "Orginal WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;

        }
        //FIX05Jul2021<<
        //Service08Jul2021>>
        field(50071; "Service Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Service Vendor Rate"."Service Code" where("Vendor Code" = field("Buy-from Vendor No."), Released = const(true));
        }
        //Service08Jul2021<<
        field(50821; "Posted Loading Slip No."; code[20])//PKONJU19
        {
            DataClassification = CustomerContent;

        }

    }
}