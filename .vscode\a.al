// report 59045 "Aged Accounts Recee New"
// {
//     DefaultLayout = RDLC;
//     RDLCLayout = './120N.rdl';
//     ApplicationArea = Basic, Suite;
//     Caption = 'Aged Accounts Receivable New_50490';
//     PreviewMode = PrintLayout;
//     UsageCategory = ReportsAndAnalysis;

//     dataset
//     {
//         dataitem(Header; "Integer")
//         {
//             DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
//             column(CompanyName; CompanyDisplayName)
//             {
//             }
//             column(FormatEndingDate; StrSubstNo(Text006, Format(EndingDate, 0, 4)))
//             {
//             }
//             column(PostingDate; StrSubstNo(Text007, SelectStr(AgingBy + 1, Text009)))
//             {
//             }
//             column(PrintAmountInLCY; PrintAmountInLCY)
//             {
//             }
//             column(TableCaptnCustFilter; Customer.TableCaption + ': ' + CustFilter)
//             {
//             }
//             column(CustFilter; CustFilter)
//             {
//             }
//             column(AgingByDueDate; AgingBy = AgingBy::"Due Date")
//             {
//             }
//             column(AgedbyDocumnetDate; StrSubstNo(Text004, SelectStr(AgingBy + 1, Text009)))
//             {
//             }
//             column(HeaderText7; HeaderText[7])
//             {

//             }
//             column(HeaderText6; HeaderText[6])
//             {
//             }
//             column(HeaderText5; HeaderText[5])
//             {
//             }
//             column(HeaderText4; HeaderText[4])
//             {
//             }
//             column(HeaderText3; HeaderText[3])
//             {
//             }
//             column(HeaderText2; HeaderText[2])
//             {
//             }
//             column(HeaderText1; HeaderText[1])
//             {
//             }
//             column(PrintDetails; PrintDetails)
//             {
//             }
//             column(AgedAccReceivableCptn; AgedAccReceivableCptnLbl)
//             {
//             }
//             column(CurrReportPageNoCptn; CurrReportPageNoCptnLbl)
//             {
//             }
//             column(AllAmtinLCYCptn; AllAmtinLCYCptnLbl)
//             {
//             }
//             column(AgedOverdueAmtCptn; AgedOverdueAmtCptnLbl)
//             {
//             }
//             column(CLEEndDateAmtLCYCptn; CLEEndDateAmtLCYCptnLbl)
//             {
//             }
//             column(CLEEndDateDueDateCptn; CLEEndDateDueDateCptnLbl)
//             {
//             }
//             column(CLEEndDateDocNoCptn; CLEEndDateDocNoCptnLbl)
//             {
//             }
//             column(CLEEndDatePstngDateCptn; CLEEndDatePstngDateCptnLbl)
//             {
//             }
//             column(CLEEndDateDocTypeCptn; CLEEndDateDocTypeCptnLbl)
//             {
//             }
//             column(OriginalAmtCptn; OriginalAmtCptnLbl)
//             {
//             }
//             column(TotalLCYCptn; TotalLCYCptnLbl)
//             {
//             }
//             column(NewPagePercustomer; NewPagePercustomer)
//             {
//             }
//             column(GrandTotalCLE7RemAmt; GrandTotalCustLedgEntry[7]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE6RemAmt; GrandTotalCustLedgEntry[6]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE5RemAmt; GrandTotalCustLedgEntry[5]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE4RemAmt; GrandTotalCustLedgEntry[4]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE3RemAmt; GrandTotalCustLedgEntry[3]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE2RemAmt; GrandTotalCustLedgEntry[2]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE1RemAmt; GrandTotalCustLedgEntry[1]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE7RemAmtFCY; GrandTotalCustLedgEntry[7]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE6RemAmtFCY; GrandTotalCustLedgEntry[6]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE5RemAmtFCY; GrandTotalCustLedgEntry[5]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE4RemAmtFCY; GrandTotalCustLedgEntry[4]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE3RemAmtFCY; GrandTotalCustLedgEntry[3]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE2RemAmtFCY; GrandTotalCustLedgEntry[2]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE1RemAmtFCY; GrandTotalCustLedgEntry[1]."Remaining Amount")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLEAmtLCY; GrandTotalCustLedgEntry[1]."Amount (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE1CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[1]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE2CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[2]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE3CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[3]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE4CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[4]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE5CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[5]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE6CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[6]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE7CustRemAmtLCY; Pct(GrandTotalCustLedgEntry[7]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE1AmtLCY; GrandTotalCustLedgEntry[1]."Amount (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE1Amt; GrandTotalCustLedgEntry[1].Amount)
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE7PctRemAmtLCY; Pct(GrandTotalCustLedgEntry[7]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE6PctRemAmtLCY; Pct(GrandTotalCustLedgEntry[6]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE5PctRemAmtLCY; Pct(GrandTotalCustLedgEntry[5]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE3PctRemAmtLCY; Pct(GrandTotalCustLedgEntry[3]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE2PctRemAmtLCY; Pct(GrandTotalCustLedgEntry[2]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE1PctRemAmtLCY; Pct(GrandTotalCustLedgEntry[1]."Remaining Amt. (LCY)", GrandTotalCustLedgEntry[1]."Amount (LCY)"))
//             {
//             }
//             column(GrandTotalCLE7RemAmtLCY; GrandTotalCustLedgEntry[7]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE6RemAmtLCY; GrandTotalCustLedgEntry[6]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE5RemAmtLCY; GrandTotalCustLedgEntry[5]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE4RemAmtLCY; GrandTotalCustLedgEntry[4]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE3RemAmtLCY; GrandTotalCustLedgEntry[3]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE2RemAmtLCY; GrandTotalCustLedgEntry[2]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             column(GrandTotalCLE1RemAmtLCY; GrandTotalCustLedgEntry[1]."Remaining Amt. (LCY)")
//             {
//                 AutoFormatType = 1;
//             }
//             dataitem(Customer; Customer)
//             {
//                 RequestFilterFields = "No.";
//                 //BaluPKOnDec15>>
//                 column(NotDue; NotDue)
//                 {

//                 }
//                 //BaluPKOnDec15<<
//                 column(PageGroupNo; PageGroupNo)
//                 {
//                 }
//                 column(CustomerPhoneNoCaption; FieldCaption("Phone No."))
//                 {
//                 }
//                 column(CustomerContactCaption; FieldCaption(Contact))
//                 {
//                 }
//                 dataitem("Cust. Ledger Entry"; "Cust. Ledger Entry")
//                 {
//                     DataItemLink = "Customer No." = FIELD("No.");
//                     DataItemTableView = SORTING("Customer No.", "Posting Date", "Currency Code");

//                     trigger OnAfterGetRecord()
//                     var
//                         CustLedgEntry: Record "Cust. Ledger Entry";
//                     begin
//                         CustLedgEntry.SetCurrentKey("Closed by Entry No.");
//                         if "Closed by Entry No." <> 0 then
//                             CustLedgEntry.SetFilter("Closed by Entry No.", '%1|%2', "Entry No.", "Closed by Entry No.")
//                         else
//                             CustLedgEntry.SetRange("Closed by Entry No.", "Entry No.");
//                         CustLedgEntry.SetRange("Posting Date", 0D, EndingDate);
//                         CopyDimFiltersFromCustomer(CustLedgEntry);
//                         if CustLedgEntry.FindSet(false, false) then
//                             repeat
//                                 InsertTemp(CustLedgEntry);
//                             until CustLedgEntry.Next = 0;

//                         CustLedgEntry.Reset();
//                         CustLedgEntry.SetRange("Entry No.", "Closed by Entry No.");
//                         CustLedgEntry.SetRange("Posting Date", 0D, EndingDate);
//                         CopyDimFiltersFromCustomer(CustLedgEntry);
//                         if CustLedgEntry.FindSet(false, false) then
//                             repeat
//                                 InsertTemp(CustLedgEntry);
//                             until CustLedgEntry.Next = 0;
//                         CurrReport.Skip();
//                     end;

//                     trigger OnPreDataItem()
//                     begin
//                         SetRange("Posting Date", EndingDate + 1, DMY2Date(31, 12, 9999));
//                         CopyDimFiltersFromCustomer("Cust. Ledger Entry");

//                     end;
//                 }
//                 dataitem(OpenCustLedgEntry; "Cust. Ledger Entry")
//                 {
//                     DataItemLink = "Customer No." = FIELD("No.");
//                     DataItemTableView = SORTING("Customer No.", Open, Positive, "Due Date", "Currency Code");

//                     trigger OnAfterGetRecord()
//                     begin
//                         InsertTemp(OpenCustLedgEntry);
//                         CurrReport.Skip();
//                     end;

//                     trigger OnPreDataItem()
//                     begin
//                         if AgingBy = AgingBy::"Posting Date" then begin
//                             SetRange("Posting Date", 0D, EndingDate);
//                             SetRange("Date Filter", 0D, EndingDate);
//                             SetAutoCalcFields("Remaining Amt. (LCY)");
//                             SetFilter("Remaining Amt. (LCY)", '<>0');
//                             //SetFilter("Remaining Amount", '<=', Amount);//BaluPKOnDec15 Need to Moove in Live
//                         end;
//                         CopyDimFiltersFromCustomer(OpenCustLedgEntry);
//                     end;
//                 }
//                 dataitem(CurrencyLoop; "Integer")
//                 {
//                     DataItemTableView = SORTING(Number) WHERE(Number = FILTER(1 ..));
//                     PrintOnlyIfDetail = true;
//                     dataitem(TempCustLedgEntryLoop; "Integer")
//                     {
//                         DataItemTableView = SORTING(Number) WHERE(Number = FILTER(1 ..));
//                         column(Name1_Cust; Customer.Name)
//                         {
//                             IncludeCaption = true;
//                         }
//                         column(No_Cust; Customer."No.")
//                         {
//                             IncludeCaption = true;
//                         }
//                         column(CustomerPostingGroup; Customer."Customer Posting Group")
//                         {
//                             IncludeCaption = true;
//                         }
//                         column(CustomerPhoneNo; Customer."Phone No.")
//                         {
//                         }
//                         column(CustomerContactName; Customer.Contact)
//                         {
//                         }
//                         column(NotDueLine; NotDueLine)
//                         {

//                         }
//                         column(CLEEndDateRemAmtLCY; CustLedgEntryEndingDate."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE1RemAmtLCY; AgedCustLedgEntry[1]."Remaining Amt. (LCY)")
//                         // { G2S
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE1RemAmtLCY; DeuRangeAmt1)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE2RemAmtLCY; AgedCustLedgEntry[2]."Remaining Amt. (LCY)")
//                         // {
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE2RemAmtLCY; DeuRangeAmt2)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE3RemAmtLCY; AgedCustLedgEntry[3]."Remaining Amt. (LCY)")
//                         // {
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE3RemAmtLCY; DeuRangeAmt3)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE4RemAmtLCY; AgedCustLedgEntry[4]."Remaining Amt. (LCY)")
//                         // {
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE4RemAmtLCY; DeuRangeAmt4)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE5RemAmtLCY; AgedCustLedgEntry[5]."Remaining Amt. (LCY)")
//                         // {
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE5RemAmtLCY; DeuRangeAmt5)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE6RemAmtLCY; AgedCustLedgEntry[6]."Remaining Amt. (LCY)")
//                         // {
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE6RemAmtLCY; DeuRangeAmt6)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE7RemAmtLCY; AgedCustLedgEntry[7]."Remaining Amt. (LCY)")
//                         // {
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE7RemAmtLCY; DeuRangeAmt7)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(CLEEndDateAmtLCY; CustLedgEntryEndingDate."Amount (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(CLEEndDueDate; Format(CustLedgEntryEndingDate."Posting Date" + 30))
//                         {
//                         }
//                         column(CLEEndDateDocNo; CustLedgEntryEndingDate."Document No.")
//                         {
//                         }
//                         column(CLEDocType; Format(CustLedgEntryEndingDate."Document Type"))
//                         {
//                         }
//                         column(CLEPostingDate; Format(CustLedgEntryEndingDate."Posting Date"))
//                         {
//                         }
//                         // column(AgedCLE7TempRemAmt; AgedCustLedgEntry[7]."Remaining Amount")
//                         // {
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE7TempRemAmt; DeuRangeAmt7)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE6TempRemAmt; AgedCustLedgEntry[6]."Remaining Amount")
//                         // {
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE6TempRemAmt; DeuRangeAmt6)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE5TempRemAmt; AgedCustLedgEntry[5]."Remaining Amount")
//                         // {
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE5TempRemAmt; DeuRangeAmt5)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE4TempRemAmt; AgedCustLedgEntry[4]."Remaining Amount")
//                         // {
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE4TempRemAmt; DeuRangeAmt4)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE3TempRemAmt; AgedCustLedgEntry[3]."Remaining Amount")
//                         // {
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE3TempRemAmt; DeuRangeAmt3)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE2TempRemAmt; AgedCustLedgEntry[2]."Remaining Amount")
//                         // {
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE2TempRemAmt; DeuRangeAmt2)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         // column(AgedCLE1TempRemAmt; AgedCustLedgEntry[1]."Remaining Amount")
//                         // { G2S
//                         //     AutoFormatExpression = CurrencyCode;
//                         //     AutoFormatType = 1;
//                         // }
//                         column(AgedCLE1TempRemAmt; DeuRangeAmt1)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         column(RemAmt_CLEEndDate; CustLedgEntryEndingDate."Remaining Amount")
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         column(CLEEndDate; CustLedgEntryEndingDate.Amount)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         column(Name_Cust; StrSubstNo(Text005, Customer.Name))
//                         {
//                         }
//                         column(TotalCLE1AmtLCY; TotalCustLedgEntry[1]."Amount (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE1RemAmtLCY; TotalCustLedgEntry[1]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE2RemAmtLCY; TotalCustLedgEntry[2]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE3RemAmtLCY; TotalCustLedgEntry[3]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE4RemAmtLCY; TotalCustLedgEntry[4]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE5RemAmtLCY; TotalCustLedgEntry[5]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE6RemAmtLCY; TotalCustLedgEntry[6]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE7RemAmtLCY; TotalCustLedgEntry[7]."Remaining Amt. (LCY)")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(CurrrencyCode; CurrencyCode)
//                         {
//                             AutoFormatExpression = CurrencyCode;
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE7RemAmt; TotalCustLedgEntry[7]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE6RemAmt; TotalCustLedgEntry[6]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE5RemAmt; TotalCustLedgEntry[5]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE4RemAmt; TotalCustLedgEntry[4]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE3RemAmt; TotalCustLedgEntry[3]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE2RemAmt; TotalCustLedgEntry[2]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE1RemAmt; TotalCustLedgEntry[1]."Remaining Amount")
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCLE1Amt; TotalCustLedgEntry[1].Amount)
//                         {
//                             AutoFormatType = 1;
//                         }
//                         column(TotalCheck; CustFilterCheck)
//                         {
//                         }

//                         trigger OnAfterGetRecord()
//                         var
//                             PeriodIndex: Integer;
//                         begin

//                             if Number = 1 then begin
//                                 if not TempCustLedgEntry.FindSet(false, false) then
//                                     CurrReport.Break();
//                             end else
//                                 if TempCustLedgEntry.Next = 0 then
//                                     CurrReport.Break();

//                             CustLedgEntryEndingDate := TempCustLedgEntry;

//                             DetailedCustomerLedgerEntry.SetRange("Cust. Ledger Entry No.", CustLedgEntryEndingDate."Entry No.");
//                             if DetailedCustomerLedgerEntry.FindSet(false, false) then
//                                 repeat
//                                     if (DetailedCustomerLedgerEntry."Entry Type" =
//                                         DetailedCustomerLedgerEntry."Entry Type"::"Initial Entry") and
//                                        (CustLedgEntryEndingDate."Posting Date" > EndingDate) and
//                                        (AgingBy <> AgingBy::"Posting Date")
//                                     then begin
//                                         /*if CustLedgEntryEndingDate."Document Date" <= EndingDate then
//                                             DetailedCustomerLedgerEntry."Posting Date" :=
//                                               CustLedgEntryEndingDate."Document Date"
//                                         else*/
//                                         if (CustLedgEntryEndingDate."Due Date" <= EndingDate) and
//                                            (AgingBy = AgingBy::"Due Date")
//                                         then
//                                             DetailedCustomerLedgerEntry."Posting Date" :=
//                                               CustLedgEntryEndingDate."Due Date"
//                                     end;

//                                     if (DetailedCustomerLedgerEntry."Posting Date" <= EndingDate) or
//                                        (TempCustLedgEntry.Open and
//                                         (AgingBy = AgingBy::"Due Date") and
//                                         (CustLedgEntryEndingDate."Due Date" > EndingDate) and
//                                         (CustLedgEntryEndingDate."Posting Date" <= EndingDate))
//                                     then begin
//                                         if DetailedCustomerLedgerEntry."Entry Type" in
//                                            [DetailedCustomerLedgerEntry."Entry Type"::"Initial Entry",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Unrealized Loss",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Unrealized Gain",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Realized Loss",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Realized Gain",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Discount",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Discount (VAT Excl.)",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Discount (VAT Adjustment)",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Tolerance",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Discount Tolerance",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Tolerance (VAT Excl.)",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Tolerance (VAT Adjustment)",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Discount Tolerance (VAT Excl.)",
//                                             DetailedCustomerLedgerEntry."Entry Type"::"Payment Discount Tolerance (VAT Adjustment)"]
//                                         then begin
//                                             CustLedgEntryEndingDate.Amount := CustLedgEntryEndingDate.Amount + DetailedCustomerLedgerEntry.Amount;
//                                             CustLedgEntryEndingDate."Amount (LCY)" :=
//                                               CustLedgEntryEndingDate."Amount (LCY)" + DetailedCustomerLedgerEntry."Amount (LCY)";
//                                         end;
//                                         if DetailedCustomerLedgerEntry."Posting Date" <= EndingDate then begin
//                                             CustLedgEntryEndingDate."Remaining Amount" :=
//                                               CustLedgEntryEndingDate."Remaining Amount" + DetailedCustomerLedgerEntry.Amount;
//                                             //Message('%1', CustLedgEntryEndingDate."Remaining Amount");
//                                             CustLedgEntryEndingDate."Remaining Amt. (LCY)" :=
//                                               CustLedgEntryEndingDate."Remaining Amt. (LCY)" + DetailedCustomerLedgerEntry."Amount (LCY)";
//                                         end;
//                                     end;
//                                 until DetailedCustomerLedgerEntry.Next = 0;

//                             if CustLedgEntryEndingDate."Remaining Amount" = 0 then
//                                 CurrReport.Skip();

//                             case AgingBy of
//                                 AgingBy::"Due Date":
//                                     PeriodIndex := GetPeriodIndex(CalcDate('31D', CustLedgEntryEndingDate."Posting Date"), 1);
//                                 // PeriodIndex := GetPeriodIndex(CustLedgEntryEndingDate."Due Date");
//                                 AgingBy::"Posting Date":
//                                     PeriodIndex := GetPeriodIndex(CustLedgEntryEndingDate."Posting Date", 2);
//                                 AgingBy::"Document Date":
//                                     begin
//                                         if CustLedgEntryEndingDate."Document Date" > EndingDate then begin
//                                             CustLedgEntryEndingDate."Remaining Amount" := 0;
//                                             CustLedgEntryEndingDate."Remaining Amt. (LCY)" := 0;
//                                             CustLedgEntryEndingDate."Document Date" := CustLedgEntryEndingDate."Posting Date";
//                                         end;
//                                         PeriodIndex := GetPeriodIndex(CustLedgEntryEndingDate."Document Date", 2);
//                                     end;
//                             end;

//                             Clear(AgedCustLedgEntry);
//                             //PKONNO5>>
//                             IF PeriodIndex = 0 THEN
//                                 PeriodIndex := 1;
//                             //PKONNO5<<
//                             AgedCustLedgEntry[PeriodIndex]."Remaining Amount" := CustLedgEntryEndingDate."Remaining Amount";
//                             AgedCustLedgEntry[PeriodIndex]."Remaining Amt. (LCY)" := CustLedgEntryEndingDate."Remaining Amt. (LCY)";
//                             TotalCustLedgEntry[PeriodIndex]."Remaining Amount" += CustLedgEntryEndingDate."Remaining Amount";
//                             TotalCustLedgEntry[PeriodIndex]."Remaining Amt. (LCY)" += CustLedgEntryEndingDate."Remaining Amt. (LCY)";
//                             GrandTotalCustLedgEntry[PeriodIndex]."Remaining Amt. (LCY)" += CustLedgEntryEndingDate."Remaining Amt. (LCY)";
//                             GrandTotalCustLedgEntry[PeriodIndex]."Remaining Amount" += CustLedgEntryEndingDate."Remaining Amount";
//                             TotalCustLedgEntry[1].Amount += CustLedgEntryEndingDate."Remaining Amount";
//                             TotalCustLedgEntry[1]."Amount (LCY)" += CustLedgEntryEndingDate."Remaining Amt. (LCY)";
//                             GrandTotalCustLedgEntry[1]."Amount (LCY)" += CustLedgEntryEndingDate."Remaining Amt. (LCY)";
//                             //BaluPkonJan4>>
//                             Clear(NotDueLine);
//                             if CustLedEntry.Get(CustLedgEntryEndingDate."Entry No.") then begin
//                                 CustLedEntry.CalcFields("Remaining Amount", Amount);
//                                 // <<<<<< G2S 6/7/2024
//                                 DueRange.Add(DeuRangeAmt1);
//                                 DueRange.Add(DeuRangeAmt2);
//                                 DueRange.Add(DeuRangeAmt3);
//                                 DueRange.Add(DeuRangeAmt4);
//                                 DueRange.Add(DeuRangeAmt5);
//                                 DueRange.Add(DeuRangeAmt6);
//                                 DueRange.Add(DeuRangeAmt7);

//                                 if CustLedEntry."Due Date" > EndingDate then begin

//                                     if CustLedEntry."Document Type" <> CustLedEntry."Document Type"::Payment then begin
//                                         IF CustLedEntry."Remaining Amount" <= CustLedEntry.Amount THEN begin
//                                             NotDueLine := CustLedEntry."Remaining Amount";
//                                         end else begin
//                                             Clear(DueRange);
//                                             DeuRangeAmt1 := AgedCustLedgEntry[1]."Remaining Amt. (LCY)";
//                                             DeuRangeAmt2 := AgedCustLedgEntry[2]."Remaining Amt. (LCY)";
//                                             DeuRangeAmt3 := AgedCustLedgEntry[3]."Remaining Amt. (LCY)";
//                                             DeuRangeAmt4 := AgedCustLedgEntry[4]."Remaining Amt. (LCY)";
//                                             DeuRangeAmt5 := AgedCustLedgEntry[5]."Remaining Amt. (LCY)";
//                                             DeuRangeAmt6 := AgedCustLedgEntry[6]."Remaining Amt. (LCY)";
//                                             DeuRangeAmt7 := AgedCustLedgEntry[7]."Remaining Amt. (LCY)";
//                                         end;
//                                     end else
//                                         if CustLedEntry."Document Type" = CustLedEntry."Document Type"::Payment then begin
//                                             NotDueLine := CustLedEntry."Remaining Amount";
//                                             Clear(DueRange);
//                                             DeuRangeAmt1 := 0;
//                                             DeuRangeAmt2 := 0;
//                                             DeuRangeAmt3 := 0;
//                                             DeuRangeAmt4 := 0;
//                                             DeuRangeAmt5 := 0;
//                                             DeuRangeAmt6 := 0;
//                                             DeuRangeAmt7 := 0;
//                                         end;

//                                 end
//                                 // >>>>>> G2S 6/7/2024
//                             end;

//                             //If Not ((CustLedgEntryEndingDate."Customer Posting Group" = 'OVERSEAS') AND (CustLedgEntryEndingDate."Currency Code" = 'NGN')) then
//                             If Not (CustLedgEntryEndingDate."Customer Posting Group" = 'OVERSEAS') then
//                                 GrandTotalCustLedgEntry[1].Amount += CustLedgEntryEndingDate."Remaining Amount";
//                         end;

//                         trigger OnPostDataItem()
//                         begin
//                             if not PrintAmountInLCY then
//                                 UpdateCurrencyTotals;
//                         end;

//                         trigger OnPreDataItem()
//                         begin
//                             if not PrintAmountInLCY then begin
//                                 if (TempCurrency.Code = '') or (TempCurrency.Code = GLSetup."LCY Code") then
//                                     TempCustLedgEntry.SetFilter("Currency Code", '%1|%2', GLSetup."LCY Code", '')
//                                 else
//                                     TempCustLedgEntry.SetRange("Currency Code", TempCurrency.Code);
//                             end;

//                             PageGroupNo := NextPageGroupNo;
//                             if NewPagePercustomer and (NumberOfCurrencies > 0) then
//                                 NextPageGroupNo := PageGroupNo + 1;
//                         end;
//                     }

//                     trigger OnAfterGetRecord()
//                     begin
//                         Clear(TotalCustLedgEntry);

//                         if Number = 1 then begin
//                             if not TempCurrency.FindSet(false, false) then
//                                 CurrReport.Break();
//                         end else
//                             if TempCurrency.Next = 0 then
//                                 CurrReport.Break();

//                         if TempCurrency.Code <> '' then
//                             CurrencyCode := TempCurrency.Code
//                         else
//                             CurrencyCode := GLSetup."LCY Code";

//                         NumberOfCurrencies := NumberOfCurrencies + 1;
//                     end;

//                     trigger OnPreDataItem()
//                     begin
//                         NumberOfCurrencies := 0;
//                     end;
//                 }

//                 trigger OnAfterGetRecord()
//                 begin
//                     Clear(NotDue);
//                     if NewPagePercustomer then
//                         PageGroupNo += 1;
//                     TempCurrency.Reset();
//                     TempCurrency.DeleteAll();
//                     TempCustLedgEntry.Reset();
//                     TempCustLedgEntry.DeleteAll();

//                     if not CustomersWithLedgerEntriesList.Contains("No.") then
//                         CurrReport.Skip();
//                     //BaluPKOnDec15>>
//                     CustLedgEntGvar.Reset();
//                     CustLedgEntGvar.SetRange("Customer No.", "No.");
//                     CustLedgEntGvar.SetRange("Document Type", CustLedgEntGvar."Document Type"::Invoice);
//                     CustLedgEntGvar.SetFilter("Due Date", '>%1', EndingDate);
//                     if CustLedgEntGvar.FindSet() then
//                         repeat
//                             CustLedgEntGvar.CalcFields("Remaining Amount", Amount);
//                             IF CustLedgEntGvar."Remaining Amount" <= CustLedgEntGvar.Amount THEN
//                                 NotDue += CustLedgEntGvar."Remaining Amount";
//                         //Message('%1...%2...%3...%4', CustLedgEntGvar."Document No.", CustLedgEntGvar."Remaining Amount", CustLedgEntGvar.Amount, NotDue);
//                         until CustLedgEntGvar.Next() = 0;
//                     //BaluPKOnDec15<<
//                 end;

//                 trigger OnPreDataItem()
//                 begin
//                     NumCustLedgEntriesperCust.SetFilter(Customer_No, GetFilter("No."));
//                     if NumCustLedgEntriesperCust.Open then
//                         while NumCustLedgEntriesperCust.Read do
//                             if not CustomersWithLedgerEntriesList.Contains(NumCustLedgEntriesperCust.Customer_No) then
//                                 CustomersWithLedgerEntriesList.Add(NumCustLedgEntriesperCust.Customer_No);
//                 end;
//             }
//             dataitem(CurrencyTotals; "Integer")
//             {
//                 DataItemTableView = SORTING(Number) WHERE(Number = FILTER(1 ..));
//                 column(CurrNo; Number = 1)
//                 {
//                 }
//                 column(TempCurrCode; TempCurrency2.Code)
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE7RemAmt; AgedCustLedgEntry[7]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE6RemAmt; AgedCustLedgEntry[6]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE1RemAmt; AgedCustLedgEntry[1]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE2RemAmt; AgedCustLedgEntry[2]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE3RemAmt; AgedCustLedgEntry[3]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE4RemAmt; AgedCustLedgEntry[4]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(AgedCLE5RemAmt; AgedCustLedgEntry[5]."Remaining Amount")
//                 {
//                     AutoFormatExpression = CurrencyCode;
//                     AutoFormatType = 1;
//                 }
//                 column(CurrSpecificationCptn; CurrSpecificationCptnLbl)
//                 {
//                 }

//                 trigger OnAfterGetRecord()
//                 begin

//                     if Number = 1 then begin
//                         if not TempCurrency2.FindSet(false, false) then
//                             CurrReport.Break();
//                     end else
//                         if TempCurrency2.Next = 0 then
//                             CurrReport.Break();

//                     Clear(AgedCustLedgEntry);
//                     TempCurrencyAmount.SetRange("Currency Code", TempCurrency2.Code);
//                     if TempCurrencyAmount.FindSet(false, false) then
//                         repeat
//                             if TempCurrencyAmount.Date <> DMY2Date(31, 12, 9999) then
//                                 AgedCustLedgEntry[GetPeriodIndex(TempCurrencyAmount.Date, 2)]."Remaining Amount" :=
//                                   TempCurrencyAmount.Amount
//                             else
//                                 AgedCustLedgEntry[6]."Remaining Amount" := TempCurrencyAmount.Amount;
//                         until TempCurrencyAmount.Next = 0;
//                 end;
//             }
//         }
//     }

//     requestpage
//     {
//         SaveValues = true;

//         layout
//         {
//             area(content)
//             {
//                 group(Options)
//                 {
//                     Caption = 'Options';
//                     field(AgedAsOf; EndingDate)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'Aged As Of';
//                         ToolTip = 'Specifies the date that you want the aging calculated for.';
//                     }
//                     field(Agingby; AgingBy)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'Aging by';
//                         OptionCaption = 'Due Date,Posting Date,Document Date';
//                         ToolTip = 'Specifies if the aging will be calculated from the due date, the posting date, or the document date.';
//                     }
//                     field(PeriodLength; PeriodLength)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'Period Length';
//                         ToolTip = 'Specifies the period for which data is shown in the report. For example, enter "1M" for one month, "30D" for thirty days, "3Q" for three quarters, or "5Y" for five years.';
//                     }
//                     field(AmountsinLCY; PrintAmountInLCY)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'Print Amounts in LCY';
//                         ToolTip = 'Specifies if you want the report to specify the aging per customer ledger entry.';
//                     }
//                     field(PrintDetails; PrintDetails)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'Print Details';
//                         ToolTip = 'Specifies if you want the report to show the detailed entries that add up the total balance for each customer.';
//                     }
//                     field(HeadingType; HeadingType)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'Heading Type';
//                         OptionCaption = 'Date Interval,Number of Days';
//                         ToolTip = 'Specifies if the column heading for the three periods will indicate a date interval or the number of days overdue.';
//                     }
//                     field(perCustomer; NewPagePercustomer)
//                     {
//                         ApplicationArea = Basic, Suite;
//                         Caption = 'New Page per Customer';
//                         ToolTip = 'Specifies if each customer''s information is printed on a new page if you have chosen two or more customers to be included in the report.';
//                     }
//                 }
//             }
//         }

//         actions
//         {
//         }

//         trigger OnOpenPage()
//         begin
//             if EndingDate = 0D then
//                 EndingDate := WorkDate;
//             if Format(PeriodLength) = '' then
//                 Evaluate(PeriodLength, '<1M>');

//         end;
//     }

//     labels
//     {
//         BalanceCaption = 'Balance';
//     }

//     trigger OnPreReport()
//     var
//         FormatDocument: Codeunit "Format Document";
//     begin
//         CustFilter := FormatDocument.GetRecordFiltersWithCaptions(Customer);
//         PrintAmountInLCY := true;
//         GLSetup.Get();

//         CalcDates;
//         CreateHeadings;

//         PageGroupNo := 1;
//         NextPageGroupNo := 1;
//         CustFilterCheck := (CustFilter <> 'No.');

//         CompanyDisplayName := COMPANYPROPERTY.DisplayName;
//     end;

//     var
//         DeuRangeAmt1: Decimal;
//         DeuRangeAmt2: Decimal;
//         DeuRangeAmt3: Decimal;
//         DeuRangeAmt4: Decimal;
//         DeuRangeAmt5: Decimal;
//         DeuRangeAmt6: Decimal;
//         DeuRangeAmt7: Decimal;
//         DueRange: list of [Decimal];
//         GLSetup: Record "General Ledger Setup";
//         TempCustLedgEntry: Record "Cust. Ledger Entry" temporary;
//         CustLedgEntryEndingDate: Record "Cust. Ledger Entry";
//         TotalCustLedgEntry: array[7] of Record "Cust. Ledger Entry";
//         GrandTotalCustLedgEntry: array[7] of Record "Cust. Ledger Entry";
//         AgedCustLedgEntry: array[7] of Record "Cust. Ledger Entry";
//         TempCurrency: Record Currency temporary;
//         TempCurrency2: Record Currency temporary;
//         TempCurrencyAmount: Record "Currency Amount" temporary;
//         DetailedCustomerLedgerEntry: Record "Detailed Cust. Ledg. Entry";
//         NumCustLedgEntriesperCust: Query "Num CustLedgEntries per Cust";
//         CustomersWithLedgerEntriesList: List of [Code[20]];
//         CustFilter: Text;
//         PrintAmountInLCY: Boolean;
//         EndingDate: Date;
//         AgingBy: Option "Due Date","Posting Date","Document Date";
//         PeriodLength: DateFormula;
//         PrintDetails: Boolean;
//         HeadingType: Option "Date Interval","Number of Days";
//         NewPagePercustomer: Boolean;
//         PeriodStartDate: array[5] of Date;
//         PeriodEndDate: array[5] of Date;
//         HeaderText: array[7] of Text[30];
//         Text000: Label 'Not Due';
//         Text001: Label 'Before';
//         CurrencyCode: Code[10];
//         Text002: Label 'days';
//         Text003: Label 'More than';
//         Text004: Label 'Aged by %1';
//         Text005: Label 'Total for %1';
//         Text006: Label 'Aged as of %1';
//         Text007: Label 'Aged by %1';
//         NumberOfCurrencies: Integer;
//         Text009: Label 'Due Date,Posting Date,Document Date';
//         Text010: Label 'The Date Formula %1 cannot be used. Try to restate it. E.g. 1M+CM instead of CM+1M.';
//         PageGroupNo: Integer;
//         NextPageGroupNo: Integer;
//         CustFilterCheck: Boolean;
//         Text032Txt: Label '-%1', Comment = 'Negating the period length: %1 is the period length';
//         AgedAccReceivableCptnLbl: Label 'Aged Accounts Receivable';
//         CurrReportPageNoCptnLbl: Label 'Page';
//         AllAmtinLCYCptnLbl: Label 'All Amounts in LCY';
//         AgedOverdueAmtCptnLbl: Label 'Aged Overdue Amounts';
//         CLEEndDateAmtLCYCptnLbl: Label 'Original Amount ';
//         CLEEndDateDueDateCptnLbl: Label 'Due Date';
//         CLEEndDateDocNoCptnLbl: Label 'Document No.';
//         CLEEndDatePstngDateCptnLbl: Label 'Posting Date';
//         CLEEndDateDocTypeCptnLbl: Label 'Document Type';
//         OriginalAmtCptnLbl: Label 'Currency Code';
//         TotalLCYCptnLbl: Label 'Total (LCY)';
//         CurrSpecificationCptnLbl: Label 'Currency Specification';
//         EnterDateFormulaErr: Label 'Enter a date formula in the Period Length field.';
//         CompanyDisplayName: Text;
//         //BaluPKOnDec15>>
//         CustLedgEntGvar: Record "Cust. Ledger Entry";
//         NotDue: Decimal;
//         NotDueLine: Decimal;
//         CustLedEntry: Record "Cust. Ledger Entry";

//     local procedure CalcDates()
//     var
//         i: Integer;
//         PeriodLength2: DateFormula;
//     begin
//         if not Evaluate(PeriodLength2, StrSubstNo(Text032Txt, PeriodLength)) then
//             Error(EnterDateFormulaErr);
//         //PKONNO2>>
//         /*if AgingBy = AgingBy::"Due Date" then begin
//             PeriodEndDate[1] := DMY2Date(31, 12, 9999);
//             PeriodStartDate[1] := EndingDate + 1;
//         end else begin
//             PeriodEndDate[1] := EndingDate;
//             PeriodStartDate[1] := CalcDate(PeriodLength2, EndingDate + 1);
//         end;*/
//         PeriodEndDate[1] := EndingDate;
//         PeriodStartDate[1] := CalcDate(PeriodLength2, EndingDate + 1);
//         //PKONNO2<<
//         for i := 2 to ArrayLen(PeriodEndDate) do begin
//             PeriodEndDate[i] := PeriodStartDate[i - 1] - 1;
//             PeriodStartDate[i] := CalcDate(PeriodLength2, PeriodEndDate[i] + 1);
//         end;
//         PeriodStartDate[i] := 0D;

//         for i := 1 to ArrayLen(PeriodEndDate) do
//             if PeriodEndDate[i] < PeriodStartDate[i] then
//                 Error(Text010, PeriodLength);
//     end;

//     local procedure CreateHeadings()
//     var
//         i: Integer;
//     begin
//         if AgingBy = AgingBy::"Due Date" then begin
//             //PKONNO2>>
//             /*HeaderText[1] := Text000;
//             i := 2;*/
//             i := 1;
//             //PKONNO2<<
//         end else
//             i := 1;
//         while i < ArrayLen(PeriodEndDate) do begin
//             if HeadingType = HeadingType::"Date Interval" then
//                 HeaderText[i] := StrSubstNo('%1\..%2', PeriodStartDate[i], PeriodEndDate[i])
//             else
//                 HeaderText[i] :=
//                   StrSubstNo('%1 - %2 %3', EndingDate - PeriodEndDate[i] + 1, EndingDate - PeriodStartDate[i] + 1, Text002);
//             i := i + 1;
//         end;
//         if HeadingType = HeadingType::"Date Interval" then
//             HeaderText[i] := StrSubstNo('%1 %2', Text001, PeriodStartDate[i - 1])
//         else
//             HeaderText[i] := StrSubstNo('%1 \%2 %3', Text003, EndingDate - PeriodStartDate[i - 1] + 1, Text002);
//     end;

//     local procedure InsertTemp(var CustLedgEntry: Record "Cust. Ledger Entry")
//     var
//         Currency: Record Currency;
//     begin
//         with TempCustLedgEntry do begin
//             if Get(CustLedgEntry."Entry No.") then
//                 exit;
//             TempCustLedgEntry := CustLedgEntry;
//             Insert;
//             if PrintAmountInLCY then begin
//                 Clear(TempCurrency);
//                 TempCurrency."Amount Rounding Precision" := GLSetup."Amount Rounding Precision";
//                 if TempCurrency.Insert() then;
//                 exit;
//             end;
//             if TempCurrency.Get("Currency Code") then
//                 exit;
//             if TempCurrency.Get('') and ("Currency Code" = GLSetup."LCY Code") then
//                 exit;
//             if TempCurrency.Get(GLSetup."LCY Code") and ("Currency Code" = '') then
//                 exit;
//             if "Currency Code" <> '' then
//                 Currency.Get("Currency Code")
//             else begin
//                 Clear(Currency);
//                 Currency."Amount Rounding Precision" := GLSetup."Amount Rounding Precision";
//             end;
//             TempCurrency := Currency;
//             TempCurrency.Insert();
//         end;
//     end;

//     local procedure GetPeriodIndex(Date: Date; type: Integer): Integer
//     var
//         i: Integer;
//         DueDateInt: Integer;
//         NotDue: Boolean;
//     begin
//         DueDateInt := Today - Date;
//         NotDue := Today < Date;
//         Message('NotDue: %1', NotDue);
//         case type of
//             1:
//                 begin
//                     if NotDue then begin
//                         if (DueDateInt > 1) and (DueDateInt <= 30) then exit(1);
//                         if (DueDateInt > 30) and (DueDateInt <= 60) then exit(2);
//                         if (DueDateInt > 60) and (DueDateInt <= 90) then exit(3);
//                         if (DueDateInt > 90) and (DueDateInt <= 120) then exit(4);
//                         if DueDateInt > 120 then exit(5);
//                     end else
//                         exit(0);
//                 end;

//             2:
//                 begin
//                     for i := 1 to ArrayLen(PeriodEndDate) do
//                         if Date in [PeriodStartDate[i] .. PeriodEndDate[i]] then begin
//                             exit(i);
//                         end;
//                 end;

//         end;
//     end;

//     local procedure Pct(a: Decimal; b: Decimal): Text[30]
//     begin
//         if b <> 0 then
//             exit(Format(Round(100 * a / b, 0.1), 0, '<Sign><Integer><Decimals,2>') + '%');
//     end;

//     local procedure UpdateCurrencyTotals()
//     var
//         i: Integer;
//     begin
//         TempCurrency2.Code := CurrencyCode;
//         if TempCurrency2.Insert() then;
//         with TempCurrencyAmount do begin
//             for i := 1 to ArrayLen(TotalCustLedgEntry) do begin
//                 "Currency Code" := CurrencyCode;
//                 //if PeriodStartDate[i] <> 0D then begin
//                 Date := PeriodStartDate[i];
//                 // Message('%1..%2..%3', PeriodStartDate[i], Date, i);
//                 if Find then begin
//                     Amount := Amount + TotalCustLedgEntry[i]."Remaining Amount";
//                     Modify;
//                 end else begin
//                     "Currency Code" := CurrencyCode;
//                     Date := PeriodStartDate[i];
//                     Amount := TotalCustLedgEntry[i]."Remaining Amount";
//                     Insert;
//                     //end;
//                 end;
//             end;
//             "Currency Code" := CurrencyCode;
//             Date := DMY2Date(31, 12, 9999);
//             if Find then begin
//                 Amount := Amount + TotalCustLedgEntry[1].Amount;
//                 Modify;
//             end else begin
//                 "Currency Code" := CurrencyCode;
//                 Date := DMY2Date(31, 12, 9999);
//                 Amount := TotalCustLedgEntry[1].Amount;
//                 Insert;
//             end;
//         end;
//     end;

//     procedure InitializeRequest(NewEndingDate: Date; NewAgingBy: Option; NewPeriodLength: DateFormula; NewPrintAmountInLCY: Boolean; NewPrintDetails: Boolean; NewHeadingType: Option; NewPagePercust: Boolean)
//     begin
//         EndingDate := NewEndingDate;
//         AgingBy := NewAgingBy;
//         PeriodLength := NewPeriodLength;
//         PrintAmountInLCY := NewPrintAmountInLCY;
//         PrintDetails := NewPrintDetails;
//         HeadingType := NewHeadingType;
//         NewPagePercustomer := NewPagePercust;
//     end;

//     local procedure CopyDimFiltersFromCustomer(var CustLedgerEntry: Record "Cust. Ledger Entry")
//     begin
//         if Customer.GetFilter("Global Dimension 1 Filter") <> '' then
//             CustLedgerEntry.SetFilter("Global Dimension 1 Code", Customer.GetFilter("Global Dimension 1 Filter"));
//         if Customer.GetFilter("Global Dimension 2 Filter") <> '' then
//             CustLedgerEntry.SetFilter("Global Dimension 2 Code", Customer.GetFilter("Global Dimension 2 Filter"));
//     end;
// }
