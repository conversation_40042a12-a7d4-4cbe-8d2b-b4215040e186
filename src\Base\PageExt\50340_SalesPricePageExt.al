pageextension 50340 SalesPriceExt extends "Sales Prices"
{
    layout
    {
        addafter("Item No.")
        {
            field("Item Description"; "Item Description")
            {
                ApplicationArea = all;
                Editable = false;
            }
        }

        addafter("Ending Date")
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
                Editable = false;
            }
        }
    }

    actions
    {
        addafter(CopyPrices)
        {
            action("Re-Open")
            {
                ApplicationArea = All;
                Image = ReOpen;
                Promoted = true;
                PromotedIsBig = true;
                trigger OnAction()
                begin
                    if "Approval Status" = "Approval Status"::"Pending for Approval" then
                        exit;
                    "Approval Status" := "Approval Status"::Open;
                    Modify(true);
                end;
            }
            action("Release")
            {
                ApplicationArea = All;
                Image = ReleaseDoc;
                Promoted = true;
                PromotedIsBig = true;
                trigger OnAction()
                var
                    ApprovalMgmt: Codeunit "Approval Process";
                    ReleaseText: TextConst ENU = 'Do you want to release the Document?';
                    NoWorkflowEnabledErr: TextConst ENU = 'This document can only be released when the approval process is complete.';
                begin
                    if ApprovalMgmt.ISCheckValuworkflowenabled(Rec) then
                        Error(NoWorkflowEnabledErr);
                    IF NOT CONFIRM(ReleaseText, FALSE) THEN
                        EXIT;
                    "Approval Status" := "Approval Status"::Released;
                    Modify(true);
                end;
            }
            action(Approve)
            {
                ApplicationArea = All;
                Image = Action;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                Visible = OpenAppEntrExistsForCurrUser;
                trigger OnAction()
                begin
                    approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                end;
            }
            /* action("Send Approval Request")
             {
                 ApplicationArea = All;
                 Image = SendApprovalRequest;
                 Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                 Promoted = true;
                 PromotedIsBig = true;
                 PromotedCategory = Process;
                 PromotedOnly = true;
                 trigger OnAction()
                 begin
                     IF allinoneCU.CheckValueBaseApprovalsWorkflowEnabled(Rec) then
                         allinoneCU.OnSendValueBaseWFForApproval(Rec);
                 end;
             }
             action("Cancel Approval Request")
             {
                 ApplicationArea = All;
                 Image = CancelApprovalRequest;
                 Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                 Promoted = true;
                 PromotedIsBig = true;
                 PromotedCategory = Process;
                 PromotedOnly = true;
                 trigger OnAction()
                 begin
                     allinoneCU.OnCancelValueBaseWFForApproval(rec);
                 end;
             }*/
            group("Request Approval")
            {
                Caption = 'Request Approval';
                group(SendApprovalRequest)
                {
                    action(SendApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        //Caption = 'Selected Journal Lines';
                        Enabled = NOT OpenApprovalEntriesOnBatchOrCurrJnlLineExist;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send selected journal lines for approval.';

                        trigger OnAction()
                        var
                            SalesPrice: Record "Sales Price";
                        begin
                            GetCurrentlySelectedLines(Rec);
                            allinoneCU.OnSendValueBaseWFForApproval(Rec);
                        end;
                    }

                }
                group(CancelApprovalRequest)
                {
                    Caption = 'Cancel Approval Request';
                    Image = Cancel;
                    action(CancelApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        //Caption = 'Journal Batch';
                        Enabled = OpenApprovalEntriesOnJnlBatchExist;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        begin
                            allinoneCU.OnCancelValueBaseWFForApproval(rec);
                        end;
                    }
                }
            }

        }
    }

    trigger OnAfterGetRecord()
    var
        Item: Record Item;
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
        if "Item Description" = '' then begin
            if Item.Get("Item No.") then
                "Item Description" := Item.Description;
        end;
    end;

    Procedure GetCurrentlySelectedLines(VAR SalesPrice: Record "Sales Price"): Boolean
    BEGIN
        CurrPage.SETSELECTIONFILTER(SalesPrice);
        EXIT(SalesPrice.FINDSET());
    END;

    var
        OpenApprovalEntriesOnJnlBatchExist: Boolean;
        approvalmngmt: Codeunit "Approvals Mgmt.";
        allinoneCU: Codeunit "Approval Process";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrAnyJnlLineExist: Boolean;

    local procedure ShortcutDimension1CodeOnAfterV()
    begin
        CurrPage.Update();
    end;

    local procedure ShortcutDimension2CodeOnAfterV()
    begin
        CurrPage.Update();
    end;
}