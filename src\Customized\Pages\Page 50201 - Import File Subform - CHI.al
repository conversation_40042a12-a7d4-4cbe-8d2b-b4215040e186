page 50201 "Import File Subform - CHI"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // UNL     : Univision Nigeria Ltd.
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       UNL      18-Feb-12    -> Form created to display the FORM M Lines.

    AutoSplitKey = true;
    DelayedInsert = true;
    Editable = false;
    PageType = ListPart;
    SourceTable = "Import File Line";

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Document Type"; "Document Type")
                {
                    Visible = false;
                }
                field("Order No."; "Order No.")
                {
                    Visible = false;
                }
                field("Order Line No."; "Order Line No.")
                {
                    Visible = false;
                }
                field(Type; Type)
                {
                }
                field("No."; "No.")
                {
                }
                field("Item Category Code"; "Item Category Code")
                {
                }
                field(Description; Description)
                {
                    Editable = true;
                }
                field("Description 2"; "Description 2")
                {
                    Editable = true;
                    Visible = false;
                }
                field(Quantity; Quantity)
                {
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                    Visible = false;
                }
                field("Amount (FCY)"; "Amount (FCY)")
                {
                    Visible = false;
                }
                field("Product Group Code"; "Product Group Code")
                {
                }
            }
        }
    }

    actions
    {
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Document Type" := "Document Type"::Order;
    end;
}

