/// <summary>
/// Page Rel. Prod Order Customized (ID 50130).
/// </summary>
page 50583 "Rel. Prod Order Customized"
{
    AutoSplitKey = true;
    Caption = 'Lines';
    DelayedInsert = true;
    LinksAllowed = false;
    MultipleNewLines = true;
    PageType = ListPart;
    SourceTable = "Prod. Order Line";
    SourceTableView = WHERE(Status = CONST(Released));
    RefreshOnActivate = true;
    layout
    {

        area(content)
        {
            repeater(Control1)
            {
                IndentationColumn = DescriptionIndent;
                IndentationControls = Description;
                ShowCaption = false;
                field("Item No."; "Item No.")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the number of the item that is to be produced.';
                    // 24.11.23 from Ext
                    Editable = FieldEditable or GlobalProdEdit;
                    //24.11.23 from Ext
                }
                field("Variant Code"; "Variant Code")
                {
                    ApplicationArea = Planning;
                    ToolTip = 'Specifies the variant of the item on the line.';
                    Visible = false;
                }
                field("Due Date"; "Due Date")
                {
                    //24.11.23 from Ext
                    Editable = FieldEditable or GlobalProdEdit;
                    //24.11.23 from Ext
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the date when the produced item must be available. The date is copied from the header of the production order.';
                }
                field("Planning Flexibility"; "Planning Flexibility")
                {
                    ApplicationArea = Planning;
                    ToolTip = 'Specifies whether the supply represented by this line is considered by the planning system when calculating action messages.';
                    Visible = false;
                }
                field(Description; Description)
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the value of the Description field on the item card. If you enter a variant code, the variant description is copied to this field instead.';
                }
                // //code merge 1
                // field("WIP QC Enabled"; "WIP QC Enabled B2B")
                // {
                //     ApplicationArea = All;
                // }
                // field("WIP Spec ID"; "WIP Spec ID B2B")
                // {
                //     ApplicationArea = All;
                // }
                // field("Quantity Sent to Quality"; "Quantity Sent to Quality B2B")
                // {
                //     ApplicationArea = All;
                // }
                // field("Quantity Sending to Quality"; "Qty Sending to Quality B2B")
                // {
                //     ApplicationArea = All;
                //     trigger OnValidate()
                //     var
                //         ProdOrderRoutingLine: Record 5409;
                //         TempQtySendingtoQuality: Decimal;
                //         i: Integer;
                //         Text33000250Txt: Label 'Quantity Sending to Quality Should not be greater than Prod. Ord Line Qty Acceted';
                //         Text33000251Txt: Label 'Quantity Sending to Quality Should not be greater than Prod. Order Line Total Qty';
                //         Text33000252Txt: Label 'Should not be greater than  Rework Qty.';
                //         Text33000253Txt: Label 'Should not be greater than ...';
                //     BEGIN
                //         ProdOrderRoutingLine.SETRANGE("Prod. Order No.", "Prod. Order No.");
                //         ProdOrderRoutingLine.SETRANGE("Routing Reference No.", "Line No.");
                //         IF ProdOrderRoutingLine.FIND('-') THEN
                //             REPEAT
                //                 IF ProdOrderRoutingLine."QC Enabled B2B" THEN BEGIN
                //                     ProdOrderRoutingLine.CALCFIELDS(ProdOrderRoutingLine."Quantity Accepted B2B");
                //                     IF i = 0 THEN BEGIN
                //                         TempQtySendingtoQuality := ProdOrderRoutingLine."Quantity Accepted B2B" + ProdOrderRoutingLine."Prev. Qty B2B";
                //                         i := i + 1;
                //                     END ELSE
                //                         IF (ProdOrderRoutingLine."Quantity Accepted B2B" + ProdOrderRoutingLine."Prev. Qty B2B") < TempQtySendingtoQuality THEN
                //                             TempQtySendingtoQuality := ProdOrderRoutingLine."Quantity Accepted B2B" + ProdOrderRoutingLine."Prev. Qty B2B";
                //                     IF ("Qty Sending to Quality B2B" + "Quantity Sent to Quality B2B") > TempQtySendingtoQuality THEN
                //                         ERROR(Text33000250Txt);
                //                 END;
                //             UNTIL ProdOrderRoutingLine.NEXT() = 0;

                //         CALCFIELDS("Quantity Accepted B2B", "Quantity Rejected B2B", "Quantity Rework B2B");
                //         IF ("Quantity Sent to Quality B2B" = 0) AND ("Quantity Accepted B2B" = 0) AND
                //            ("Quantity Rejected B2B" = 0) AND ("Quantity Rework B2B" = 0)
                //         THEN BEGIN
                //             IF "Qty Sending to Quality B2B" > Quantity THEN
                //                 ERROR(Text33000251Txt)
                //         END ELSE
                //             IF "Quantity Sent to Quality B2B" = ("Quantity Accepted B2B" + "Quantity Rejected B2B" + "Quantity Rework B2B") THEN
                //                 IF "Qty Sending to Quality B2B" <= Quantity - ("Quantity Accepted B2B" + "Quantity Rejected B2B") THEN
                //                     "Qty Sending to Quality B2B" := "Qty Sending to Quality B2B"
                //                 ELSE
                //                     ERROR(Text33000252Txt)
                //             ELSE
                //                 IF "Qty Sending to Quality B2B" <= "Quantity Rejected B2B" - ("Quantity Sent to Quality B2B" - Quantity) THEN
                //                     "Qty Sending to Quality B2B" := "Qty Sending to Quality B2B"
                //                 ELSE
                //                     ERROR(Text33000253Txt);
                //         MODIFY();
                //     END;
                // }
                // field("Quantity Accepted"; "Quantity Accepted B2B")
                // {
                //     ApplicationArea = All;
                // }
                // field("Quantity Rejected"; "Quantity Rejected B2B")
                // {
                //     ApplicationArea = All;
                // }
                // field("Quantity Rework"; "Quantity Rework B2B")
                // {
                //     ApplicationArea = All;

                // }
                //code merger1 

                field("Description 2"; "Description 2")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies an additional description.';
                    Visible = false;
                }
                field("Production BOM No."; "Production BOM No.")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the number of the production BOM that is the basis for creating the Prod. Order Component list for this line.';
                    Visible = false;
                }
                field("Routing No."; "Routing No.")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the number of the routing used as the basis for creating the production order routing for this line.';
                    Visible = false;
                }
                field("Routing Version Code"; "Routing Version Code")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the version number of the routing.';
                    Visible = false;
                }
                field("Production BOM Version Code"; "Production BOM Version Code")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the version code of the production BOM.';
                    Visible = false;
                }
                field("Location Code"; "Location Code")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the location code, if the produced items should be stored in a specific location.';
                    Visible = false;
                }
                field("Bin Code"; "Bin Code")
                {
                    ApplicationArea = Warehouse;
                    ToolTip = 'Specifies the bin that the produced item is posted to as output, and from where it can be taken to storage or cross-docked.';
                    Visible = false;
                }
                field("Starting Date-Time"; "Starting Date-Time")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the starting date and the starting time, which are combined in a format called "starting date-time".';

                    trigger OnValidate()
                    begin
                        CurrPage.Update(false);
                    end;
                }
                field("Starting Time"; StartingTime)
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the entry''s starting time, which is retrieved from the production order routing.';
                    Visible = DateAndTimeFieldVisible;

                    trigger OnValidate()
                    begin
                        Validate("Starting Time", StartingTime);
                        CurrPage.Update(true);
                    end;
                }
                field("Starting Date"; StartingDate)
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the entry''s starting date, which is retrieved from the production order routing.';
                    Visible = DateAndTimeFieldVisible;

                    trigger OnValidate()
                    begin
                        Validate("Starting Date", StartingDate);
                        CurrPage.Update(true);
                    end;
                }
                field("Ending Date-Time"; "Ending Date-Time")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the ending date and the ending time, which are combined in a format called "ending date-time".';

                    trigger OnValidate()
                    begin
                        CurrPage.Update(false);
                    end;
                }
                field("Ending Time"; EndingTime)
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the entry''s ending time, which is retrieved from the production order routing.';
                    Visible = DateAndTimeFieldVisible;

                    trigger OnValidate()
                    begin
                        Validate("Ending Time", EndingTime);
                        CurrPage.Update(true);
                    end;
                }
                field("Ending Date"; EndingDate)
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the entry''s ending date, which is retrieved from the production order routing.';
                    Visible = DateAndTimeFieldVisible;

                    trigger OnValidate()
                    begin
                        Validate("Ending Date", EndingDate);
                        CurrPage.Update(true);
                    end;
                }
                field("Scrap %"; "Scrap %")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the percentage of the item that you expect to be scrapped in the production process.';
                    Visible = false;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the quantity to be produced if you manually fill in this line.';
                    //24.11.23 from ext
                    Editable = FieldEditable or GlobalProdEdit;
                    //24.11.23 from ext
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Reserved Quantity"; "Reserved Quantity")
                {
                    ApplicationArea = Reservation;
                    ToolTip = 'Specifies how many units of this item have been reserved.';
                    Visible = false;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies how each unit of the item or resource is measured, such as in pieces or hours. By default, the value in the Base Unit of Measure field on the item or resource card is inserted.';
                    //24.11.23 from Ext
                    Editable = FieldEditable or GlobalProdEdit;
                    //24.11.23 from Ext
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Finished Quantity"; "Finished Quantity")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies how much of the quantity on this line has been produced.';
                }
                //24.11.23  from Ext.
                field("Warehouse Location"; "Warehouse Location")
                {
                    ApplicationArea = all;
                }
                field("Warehouse Bin Code"; "Warehouse Bin Code")
                {
                    ApplicationArea = all;
                }
                field("Qty. To Transfer"; "Qty. To Transfer")
                {
                    ApplicationArea = all;
                }
                field("Qty Transfered"; "Qty Transfered")
                {
                    ApplicationArea = all;
                }
                field("Transfer Ticket No."; "Transfer Ticket No.")
                {
                    ApplicationArea = all;
                }
                field("Posted Consumption Qty."; Rec."Posted Consumption Qty.")
                {
                    ApplicationArea = All;
                }
                field("Fraction Quantity"; Rec."Fraction Quantity")
                {
                    ApplicationArea = All;
                    Visible = false;
                }
                // 24.11.23 from Ext.
                field("Remaining Quantity"; "Remaining Quantity")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the difference between the finished and planned quantities, or zero if the finished quantity is greater than the remaining quantity.';
                }
                field("Unit Cost"; "Unit Cost")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the cost of one unit of the item or resource on the line.';
                }
                field("Cost Amount"; "Cost Amount")
                {
                    ApplicationArea = Manufacturing;
                    ToolTip = 'Specifies the total cost on the line by multiplying the unit cost by the quantity.';
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = Dimensions;
                    ToolTip = 'Specifies the code for Shortcut Dimension 1, which is one of two global dimension codes that you set up in the General Ledger Setup window.';
                    Visible = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = Dimensions;
                    ToolTip = 'Specifies the code for Shortcut Dimension 2, which is one of two global dimension codes that you set up in the General Ledger Setup window.';
                    Visible = false;
                }
                field("ShortcutDimCode[3]"; ShortcutDimCode[3])
                {
                    ApplicationArea = Dimensions;
                    CaptionClass = '1,2,3';
                    TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(3),
                                                                  "Dimension Value Type" = CONST(Standard),
                                                                  Blocked = CONST(false));
                    Visible = false;

                    trigger OnValidate()
                    begin
                        ValidateShortcutDimCode(3, ShortcutDimCode[3]);
                    end;
                }
                field("ShortcutDimCode[4]"; ShortcutDimCode[4])
                {
                    ApplicationArea = Dimensions;
                    CaptionClass = '1,2,4';
                    TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(4),
                                                                  "Dimension Value Type" = CONST(Standard),
                                                                  Blocked = CONST(false));
                    Visible = false;

                    trigger OnValidate()
                    begin
                        ValidateShortcutDimCode(4, ShortcutDimCode[4]);
                    end;
                }
                field("ShortcutDimCode[5]"; ShortcutDimCode[5])
                {
                    ApplicationArea = Dimensions;
                    CaptionClass = '1,2,5';
                    TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(5),
                                                                  "Dimension Value Type" = CONST(Standard),
                                                                  Blocked = CONST(false));
                    Visible = false;

                    trigger OnValidate()
                    begin
                        ValidateShortcutDimCode(5, ShortcutDimCode[5]);
                    end;
                }
                field("ShortcutDimCode[6]"; ShortcutDimCode[6])
                {
                    ApplicationArea = Dimensions;
                    CaptionClass = '1,2,6';
                    TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(6),
                                                                  "Dimension Value Type" = CONST(Standard),
                                                                  Blocked = CONST(false));
                    Visible = false;

                    trigger OnValidate()
                    begin
                        ValidateShortcutDimCode(6, ShortcutDimCode[6]);
                    end;
                }
                field("ShortcutDimCode[7]"; ShortcutDimCode[7])
                {
                    ApplicationArea = Dimensions;
                    CaptionClass = '1,2,7';
                    TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(7),
                                                                  "Dimension Value Type" = CONST(Standard),
                                                                  Blocked = CONST(false));
                    Visible = false;

                    trigger OnValidate()
                    begin
                        ValidateShortcutDimCode(7, ShortcutDimCode[7]);
                    end;
                }
                field("ShortcutDimCode[8]"; ShortcutDimCode[8])
                {
                    ApplicationArea = Dimensions;
                    CaptionClass = '1,2,8';
                    TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(8),
                                                                  "Dimension Value Type" = CONST(Standard),
                                                                  Blocked = CONST(false));
                    Visible = false;

                    trigger OnValidate()
                    begin
                        ValidateShortcutDimCode(8, ShortcutDimCode[8]);
                    end;
                }

            }
        }
    }

    actions
    {

        area(processing)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                Image = "Action";
                action("&Reserve")
                {
                    ApplicationArea = Reservation;
                    Caption = '&Reserve';
                    Image = Reserve;
                    ToolTip = 'Reserve the quantity that is required on the document line that you opened this window for.';

                    trigger OnAction()
                    begin
                        PageShowReservation;
                    end;
                }
                action("Order &Tracking")
                {
                    ApplicationArea = Manufacturing;
                    Caption = 'Order &Tracking';
                    Image = OrderTracking;
                    ToolTip = 'Tracks the connection of a supply to its corresponding demand. This can help you find the original demand that created a specific production order or purchase order.';

                    trigger OnAction()
                    begin
                        ShowTracking;
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                Image = Line;
                group("Item Availability by")
                {
                    Caption = 'Item Availability by';
                    Image = ItemAvailability;
                    action(ItemAvailabilityByEvent)
                    {
                        ApplicationArea = Manufacturing;
                        Caption = 'Event';
                        Image = "Event";
                        ToolTip = 'View how the actual and the projected available balance of an item will develop over time according to supply and demand events.';

                        trigger OnAction()
                        begin
                            ItemAvailability(ItemAvailFormsMgt.ByEvent);
                        end;
                    }
                    action(ItemAvailabilityByPeriod)
                    {
                        ApplicationArea = Manufacturing;
                        Caption = 'Period';
                        Image = Period;
                        ToolTip = 'View the projected quantity of the item over time according to time periods, such as day, week, or month.';

                        trigger OnAction()
                        begin
                            ItemAvailability(ItemAvailFormsMgt.ByPeriod);
                        end;
                    }
                    action(ItemAvailabilityByVariant)
                    {
                        ApplicationArea = Planning;
                        Caption = 'Variant';
                        Image = ItemVariant;
                        ToolTip = 'View or edit the item''s variants. Instead of setting up each color of an item as a separate item, you can set up the various colors as variants of the item.';

                        trigger OnAction()
                        begin
                            ItemAvailability(ItemAvailFormsMgt.ByVariant);
                        end;
                    }
                    action(ItemAvailabilityByLocation)
                    {
                        AccessByPermission = TableData Location = R;
                        ApplicationArea = Location;
                        Caption = 'Location';
                        Image = Warehouse;
                        ToolTip = 'View the actual and projected quantity of the item per location.';

                        trigger OnAction()
                        begin
                            ItemAvailability(ItemAvailFormsMgt.ByLocation);
                        end;
                    }
                    action(ItemAvailabilityByBOMLevel)
                    {
                        ApplicationArea = Manufacturing;
                        Caption = 'BOM Level';
                        Image = BOMLevel;
                        ToolTip = 'View availability figures for items on bills of materials that show how many units of a parent item you can make based on the availability of child items.';

                        trigger OnAction()
                        begin
                            ItemAvailability(ItemAvailFormsMgt.ByBOM);
                        end;
                    }
                }
                action(ReservationEntries)
                {
                    AccessByPermission = TableData Item = R;
                    ApplicationArea = Reservation;
                    Caption = 'Reservation Entries';
                    Image = ReservationLedger;
                    ToolTip = 'View the entries for every reservation that is made, either manually or automatically.';

                    trigger OnAction()
                    begin
                        ShowReservation;
                    end;
                }
                action(Dimensions)
                {
                    AccessByPermission = TableData Dimension = R;
                    ApplicationArea = Dimensions;
                    Caption = 'Dimensions';
                    Image = Dimensions;
                    ShortCutKey = 'Alt+D';
                    ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                    trigger OnAction()
                    begin
                        ShowDimensions;
                    end;
                }
                action(Routing)
                {
                    ApplicationArea = Manufacturing;
                    Caption = 'Ro&uting';
                    Image = Route;
                    ToolTip = 'View or edit the operations list of the parent item on the line.';

                    trigger OnAction()
                    begin
                        ShowRouting;
                    end;
                }
                action(Components)
                {
                    ApplicationArea = Manufacturing;
                    Caption = 'Components';
                    Image = Components;
                    ToolTip = 'View or edit the production order components of the parent item on the line.';

                    trigger OnAction()
                    begin
                        ShowComponents;
                    end;
                }
                action(ItemTrackingLines)
                {
                    ApplicationArea = ItemTracking;
                    Caption = 'Item &Tracking Lines';
                    Image = ItemTrackingLines;
                    ShortCutKey = 'Shift+Ctrl+I';
                    ToolTip = 'View or edit serial numbers and lot numbers that are assigned to the item on the document or journal line.';

                    trigger OnAction()
                    begin
                        OpenItemTrackingLines;
                    end;
                }
                action(ProductionJournal)
                {
                    ApplicationArea = Manufacturing;
                    Caption = '&Production Journal';
                    Image = Journal;
                    ToolTip = 'Post consumption and output for the released production order line.';

                    trigger OnAction()
                    //24.11.23 from Ext
                    var
                        IWXLP: Record "IWX LP Header";
                        LPLine: Record "IWX LP Line";
                        ProdOrder: Record "Production Order";
                        ToBePostedBaseConsumpQty: Decimal;
                        ProdOrderComp: Record "Prod. Order Component";
                        ItemJnlLine: Record "Item Journal Line";
                        Item: Record Item;
                        CustomG2S: Codeunit "Custom-G2S";
                    //end
                    begin
                        //24.11.23 from Ext
                        ProdOrder.Reset();
                        ProdOrder.SetRange("No.", Rec."Prod. Order No.");
                        if ProdOrder.FindFirst() then begin
                            Item.Reset();
                            Item.SetRange("No.", ProdOrder."Source No.");
                            if Item.FindFirst() then begin
                                if Item."License Plate Enabled?" then begin
                                    Error(Txt001);
                                end;// else
                                    //Error(Txt001);
                            end;
                        end;
                        // end
                        //>>>>> G2S PROJECT LEAP 31ST JAN. 2024
                        //validate external document no and update item jnl line
                        ProdOrder.Reset();
                        ProdOrder.SetRange("No.", Rec."Prod. Order No.");
                        if ProdOrder.FindFirst() then
                            ProdOrder.TestField("External Document No.");
                        //end

                        ShowProductionJournal;
                    end;

                }
                //24.11.23 from Ext
                action(ComponentJnl)
                {
                    Caption = 'Consumption Journal';
                    ApplicationArea = All;
                    trigger OnAction()
                    var
                        ToBePostedBaseConsumpQty: Decimal;
                        ProdOrderComp: Record "Prod. Order Component";
                        ItemJnlLine, ItemJnlLineCopy : Record "Item Journal Line";
                        Item, ItemCopy : Record Item;
                        ConsumpJnl: Page "Consumption Journal";
                        ProdOrder: Record "Production Order";
                        JnlTemp: Record "Item Journal Template";
                        LineNo: Integer;
                        ProdJnl: Page "Production Journal";
                        ItemJnlBatch: Record "Item Journal Batch";
                        DimSetEntr: Record "Dimension Set Entry";
                        DefDim: Record "Default Dimension";
                        CountDim: Integer;
                        TempDimSetEntry: Record "Dimension Set Entry" temporary;
                        DimensionManagement: Codeunit DimensionManagement;
                        DimValue: Record "Dimension Value";
                        UOMMgt: Codeunit "Unit of Measure Management";
                        ProducOrderPage: Page "Released Production Order";
                        ProdOrderLine: Record "Prod. Order Line";
                        ProDBOMLine: Record "Production BOM Line";
                        //150424
                        CustomSetup: Record "Custom Setup";
                        TxtErr001: Label 'Consumption setup is yet to be done in the custom setup table. Kindly attend to this and attempt again';
                        TxtErr002: Label 'Journal batch setup is yet to be done for %1. Kindly attend to this and attempt again';
                        CustomJnlBatch: Record "Custom Journal Batch Setup";
                    begin

                        ProdOrder.Reset();
                        ProdOrder.SetRange("No.", Rec."Prod. Order No.");
                        if ProdOrder.FindFirst() then begin
                            ProdOrder.TestField("External Document No.");
                            ProducOrderPage.ValidatePCQ(ProdOrder);
                            // CurrPage.Update(true);
                            Item.Reset();
                            Item.SetRange("No.", ProdOrder."Source No.");
                            if Item.FindFirst() then begin
                                if Item."License Plate Enabled?" then begin

                                    if Rec.FindFirst() then begin
                                        if Rec."Finished Quantity" <= Rec.Quantity then begin
                                            ToBePostedBaseConsumpQty := Rec."Finished Quantity" - Rec."Posted Consumption Qty.";
                                            if ToBePostedBaseConsumpQty = 0 then Error(Txt004);
                                        end;
                                    end;
                                    CustomSetup.Reset();
                                    CustomSetup.SetRange(Category, CustomSetup.Category::"Project LEAP");
                                    CustomSetup.SetFilter("Consumption Journal Temp.", '<>%1', '');
                                    if not CustomSetup.FindFirst() then Error(TxtErr001);
                                    CustomJnlBatch.Reset();
                                    CustomJnlBatch.SetRange("User ID", UserId);
                                    CustomJnlBatch.SetFilter("Batch Name", '<>%1', '');
                                    if not CustomJnlBatch.FindFirst() then Error(TxtErr002, UserId);
                                    ProdOrderComp.Reset();
                                    ProdOrderComp.SetCurrentKey("Item No.");
                                    ProdOrderComp.SetRange(Status, ProdOrder.Status);
                                    ProdOrderComp.SetRange("Prod. Order No.", Rec."Prod. Order No.");
                                    if ProdOrderComp.FindFirst() then begin
                                        // if JnlTemp.Get('CONSUMPTIO') then begin
                                        // if JnlTemp.Get(CustomSetup."Consumption Journal Temp.") then begin
                                        //     ItemJnlLineCopy.Reset();
                                        //     //041524 allow multiple users to post consumption
                                        //     //ItemJnlLineCopy."Journal Batch Name" := 'SCANNER';
                                        //     ItemJnlLineCopy."Journal Batch Name" := UserId;
                                        //     // ItemJnlLineCopy."Journal Template Name" := JnlTemp.Name;
                                        //     ItemJnlLineCopy."Journal Template Name" := CustomSetup."Consumption Journal Temp.";
                                        // end;
                                        // ItemJnlBatch.Init;
                                        // ItemJnlBatch."Journal Template Name" := ItemJnlLineCopy."Journal Template Name";
                                        // ItemJnlBatch.Name := ItemJnlLineCopy."Journal Batch Name";
                                        // if not ItemJnlBatch.Get(ItemJnlBatch."Journal Template Name", ItemJnlBatch.Name) then
                                        //     ItemJnlBatch.Insert;
                                        //ItemJnlLineCopy.SetRange("Journal Batch Name", CustomJnlBatch."Batch Name");
                                        ItemJnlLineCopy.SetRange("Journal Template Name", CustomSetup."Consumption Journal Temp.");
                                        ItemJnlLineCopy.SetRange("Order No.", ProdOrder."No.");
                                        ItemJnlLineCopy.DeleteAll();
                                        ItemJnlLineCopy.Reset();
                                        ItemJnlLineCopy.SetRange("Journal Batch Name", CustomJnlBatch."Batch Name");
                                        ItemJnlLineCopy.SetRange("Journal Template Name", CustomSetup."Consumption Journal Temp.");
                                        ItemJnlLineCopy.DeleteAll();
                                        repeat
                                            ItemJnlLine.Init();
                                            ItemJnlLine.IsOpenedFromBatch();
                                            //041524 allow multiple users to post consumption
                                            //ItemJnlLine."Journal Batch Name" := 'SCANNER';

                                            ItemJnlLine."Journal Template Name" := CustomSetup."Consumption Journal Temp.";
                                            ItemJnlLine.validate("Journal Template Name");
                                            ItemJnlLine."Journal Batch Name" := CustomJnlBatch."Batch Name";
                                            ItemJnlLine.validate("Journal Batch Name");
                                            LineNo += 10000;
                                            ItemJnlLine.Validate("Posting Date", Today);
                                            ItemJnlLine.Validate("Document Date", Today);
                                            ItemJnlLine.Validate("Document No.", ProdOrder."No.");
                                            ItemJnlLine.Validate("Order Type", ItemJnlLine."Order Type"::Production);
                                            //ItemJnlLine."Order Type" := ItemJnlLine."Order Type"::Production;
                                            ItemJnlLine.Validate("Order No.", ProdOrder."No.");
                                            ItemJnlLine.Validate("Order Line No.", 10000);
                                            ItemJnlLine.Validate("Line No.", LineNo);

                                            // //120723
                                            // if ItemJnlLine."Prod. Order Comp. Line No." = 0 then begin
                                            //     ProdBOMLine.Reset();
                                            //     ProdBOMLine.SetRange("Production BOM No.", Rec."Production BOM No.");
                                            //     ProdBOMLine.SetRange("Version Code", Rec."Production BOM Version Code");
                                            //     ProdBOMLine.SetRange("No.", ProdOrderComp."Item No.");
                                            //     if ProdBOMLine.FindFirst() then begin
                                            //         ItemJnlLine."Prod. Order Comp. Line No." := ProDBOMLine."Line No.";
                                            //         // ItemJnlLine.Validate(ItemJnlLine."Prod. Order Comp. Line No.");
                                            //     end;
                                            // end;
                                            //120723
                                            ItemJnlLine.Validate("Item No.", ProdOrderComp."Item No.");
                                            ItemJnlLine.Validate(Description, ProdOrderComp.Description);
                                            ItemJnlLine.Validate("Entry Type", ItemJnlLine."Entry Type"::Consumption);
                                            //ItemJnlLine."Unit Cost" := ProdOrderComp."Unit Cost";
                                            ItemJnlLine.Validate("Unit Cost", ProdOrderComp."Unit Cost");
                                            ItemJnlLine.Validate("Unit Amount", ProdOrderComp."Cost Amount");
                                            //ItemJnlLine."Unit Amount" := ProdOrderComp."Cost Amount";
                                            ItemJnlLine.Validate("Location Code", ProdOrder."Location Code");
                                            ItemJnlLine.Validate("Qty. per Unit of Measure", ProdOrderComp."Qty. per Unit of Measure");
                                            ItemJnlLine.Validate("Source No.", ProdOrder."Source No.");
                                            ItemJnlLine."Shortcut Dimension 1 Code" := ProdOrder."Shortcut Dimension 1 Code";
                                            ItemJnlLine."Shortcut Dimension 2 Code" := ProdOrder."Shortcut Dimension 2 Code";
                                            ItemJnlLine."External Document No." := ProdOrder."External Document No.";
                                            //ItemJnlLine.Validate(Quantity, ToBePostedBaseConsumpQty * ProdOrderComp."Quantity per");
                                            ItemJnlLine.Quantity := ToBePostedBaseConsumpQty * ProdOrderComp."Quantity per";

                                            // ItemJnlLine.Quantity := (ToBePostedBaseConsumpQty * ProdOrderComp."Quantity per" * ProdOrderComp."Qty. per Unit of Measure");
                                            // ItemJnlLine."Quantity (Base)" := ItemJnlLine.Quantity;
                                            ItemJnlLine.Validate("Unit of Measure Code", ProdOrderComp."Unit of Measure Code");
                                            ItemJnlLine.validate(ItemJnlLine.Quantity);
                                            // //120423
                                            // if ProdOrderComp."Is Substitue Item?" then begin
                                            //     ItemJnlLine."Quantity (Base)" := ItemJnlLine.Quantity * ItemJnlLine."Qty. per Unit of Measure";
                                            //     ItemJnlLine."Invoiced Qty. (Base)" := ItemJnlLine."Quantity (Base)";
                                            // end;
                                            // //120423

                                            //ItemJnlLine."Quantity (Base)" := UOMMgt.CalcBaseQty(ItemJnlLine."Item No.", ItemJnlLine."Variant Code", ItemJnlLine."Unit of Measure Code", ItemJnlLine.Quantity, ItemJnlLine."Qty. per Unit of Measure");
                                            // ItemJnlLine."Invoiced Qty. (Base)" := UOMMgt.CalcBaseQty(ItemJnlLine."Item No.", ItemJnlLine."Variant Code", ItemJnlLine."Unit of Measure Code", ItemJnlLine."Invoiced Quantity", ItemJnlLine."Qty. per Unit of Measure");
                                            //120723
                                            ItemJnlLine.Validate(ItemJnlLine."Prod. Order Comp. Line No.", ProdOrderComp."Line No.");
                                            //120723
                                            ItemCopy.Reset();
                                            ItemCopy.SetRange("No.", ProdOrderComp."Item No.");
                                            if ItemCopy.FindFirst() then begin
                                                DefDim.Reset();
                                                DefDim.SetRange("No.", ProdOrderComp."Item No.");
                                                DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                                                //DefDim.SetRange("Value Posting", DefDim."Value Posting"::"Code Mandatory");
                                                if DefDim.FindFirst() then begin
                                                    repeat
                                                        TempDimSetEntry.Init();
                                                        TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                                                        TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                                                        DimValue.Reset();
                                                        DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                                                        DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                                                        if DimValue.FindFirst() then begin
                                                            TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                                                        end;
                                                        // TempDimSetEntry."Dimension Set ID" := -1;
                                                        IF NOT TempDimSetEntry.INSERT
                                                        THEN
                                                            TempDimSetEntry.MODIFY;
                                                    //ItemJnlLine."Dimension Set ID" := 

                                                    until DefDim.Next() = 0;
                                                end;
                                                // ItemJnlLine.ValidateShortcutDimCode(3, DefDim."Dimension Value Code");
                                                // ItemJnlLine.ValidateShortcutDimCode(4, DefDim);
                                            end;
                                            ItemJnlLine."Dimension Set ID" := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
                                            ItemJnlLine."Gen. Prod. Posting Group" := ItemCopy."Gen. Prod. Posting Group";
                                            ProdOrderLine.Reset();
                                            ProdOrderLine.SetRange("Prod. Order No.", Rec."Prod. Order No.");
                                            ProdOrderLine.SetFilter("Prod Order Comp. Item", '%1', '');
                                            if ProdOrderLine.FindFirst() then begin
                                                if (ItemJnlLine."Qty. per Unit of Measure" = 1) then begin
                                                    ProdOrderLine."Prod Order Comp. Item" := ItemJnlLine."Item No.";
                                                    ProdOrderLine.Modify();
                                                end;
                                            end;
                                            ItemJnlLine.Insert();
                                            ProdJnl.AssignItemTracking();
                                        until ProdOrderComp.Next() = 0;
                                        Page.Run(99000846, ItemJnlLine);
                                    end;
                                end
                                else begin
                                    Error(Txt001);

                                end;
                            end;
                        end;
                    end;
                }
                action(PostOutput)
                {
                    Caption = 'Prod. Jnl Output(Fraction)';
                    ApplicationArea = All;
                    trigger OnAction()
                    var
                        ProdOrder: Record "Production Order";
                        ItemJnlLine, ItemJnlLineCopy : Record "Item Journal Line";
                        JnlTemp: Record "Item Journal Template";
                        ItemJnlBatch: Record "Item Journal Batch";
                        DefDim: Record "Default Dimension";
                        TempDimSetEntry: Record "Dimension Set Entry" temporary;
                        DimensionManagement: Codeunit DimensionManagement;
                        DimValue: Record "Dimension Value";
                        ProdOrdRoutingLine: Record "Prod. Order Routing Line";
                        IUOM: Record "Item Unit of Measure";

                    begin
                        IUOM.Reset();
                        IUOM.SetRange("Item No.", Rec."Item No.");
                        IUOM.SetRange(Code, 'PALLETS');
                        if IUOM.FindFirst() then begin
                            Rec.findfirst();
                            if Rec."Remaining Quantity" >= IUOM."Qty. per Unit of Measure" then begin
                                Error(Txt002);
                            end else begin
                                ProdOrder.Reset();
                                ProdOrder.SetRange("No.", Rec."Prod. Order No.");
                                if ProdOrder.FindFirst() then begin
                                    ProdOrder.TestField("External Document No.");
                                    // if Rec."Finished Quantity" < Rec.Quantity then begin
                                    // if not ((Rec."Finished Quantity" + Rec."Fraction Quantity") < Rec.Quantity) then Error(Txt003);

                                    if JnlTemp.Get('OUTPUT') then begin
                                        ItemJnlBatch.Init;
                                        ItemJnlLineCopy.Reset();
                                        ItemJnlLineCopy."Journal Batch Name" := 'SCANNER';
                                        ItemJnlLineCopy."Journal Template Name" := JnlTemp.Name;
                                        ItemJnlLineCopy."Journal Template Name" := 'OUTPUT';
                                    end;
                                    ItemJnlBatch."Journal Template Name" := ItemJnlLineCopy."Journal Template Name";
                                    ItemJnlBatch.Name := ItemJnlLineCopy."Journal Batch Name";
                                    if not ItemJnlBatch.Get(ItemJnlBatch."Journal Template Name", ItemJnlBatch.Name) then
                                        ItemJnlBatch.Insert;
                                    ItemJnlLineCopy.SetRange("Journal Batch Name", ItemJnlBatch.Name);
                                    ItemJnlLineCopy.SetRange("Journal Template Name", ItemJnlBatch."Journal Template Name");
                                    ItemJnlLineCopy.DeleteAll();

                                    // ItemJnlLine.Init();
                                    ItemJnlLine."Journal Batch Name" := ItemJnlBatch.Name;
                                    ItemJnlLine."Journal Template Name" := ItemJnlBatch."Journal Template Name";
                                    ItemJnlLine."Document No." := Rec."Prod. Order No.";
                                    ItemJnlLine."Order Type" := ItemJnlLine."Order Type"::Production;
                                    ItemJnlLine."Order No." := Rec."Prod. Order No.";
                                    ItemJnlLine."Entry Type" := ItemJnlLine."Entry Type"::Output;
                                    ItemJnlLine.Validate("Item No.", Rec."Item No.");
                                    ItemJnlLine.Validate("Source No.", ProdOrder."Source No.");
                                    //ItemJnlLine.Validate("Output Quantity", Rec."Fraction Quantity");
                                    ItemJnlLine.Validate("Output Quantity", Rec."Remaining Quantity");
                                    //message(format(Rec."Fraction Quantity"));
                                    ItemJnlLine."Line No." := 10000;
                                    ItemJnlLine."Order Line No." := 10000;
                                    ItemJnlLine.Validate(Type, ItemJnlLine.Type::"Machine Center");
                                    //get operation number
                                    ProdOrdRoutingLine.Reset();
                                    ProdOrdRoutingLine.SetRange("Routing No.", ProdOrder."Routing No.");
                                    If ProdOrdRoutingLine.FindFirst() then begin
                                        ItemJnlLine.Validate("Operation No.", ProdOrdRoutingLine."Operation No.");
                                        ItemJnlLine.Validate("No.", ProdOrdRoutingLine."No.");
                                    end;
                                    // ItemjnlLine.Validate("Operation No.", '10');
                                    // ItemJnlLine."No." := 'LINE01';
                                    ItemJnlLine."Shortcut Dimension 1 Code" := ProdOrder."Shortcut Dimension 1 Code";
                                    ItemJnlLine."Shortcut Dimension 2 Code" := ProdOrder."Shortcut Dimension 2 Code";
                                    ItemJnlLine."Posting Date" := Today();
                                    ItemJnlLine."Document Date" := Today();
                                    ItemJnlLine."External Document No." := ProdOrder."External Document No.";
                                    DefDim.Reset();
                                    DefDim.SetRange("No.", ProdOrder."Source No.");
                                    DefDim.SetFilter("Dimension Value Code", '<>%1', '');
                                    //DefDim.SetRange("Value Posting", DefDim."Value Posting"::"Code Mandatory");
                                    if DefDim.FindFirst() then begin
                                        repeat
                                            TempDimSetEntry.Init();
                                            TempDimSetEntry.Validate("Dimension Code", DefDim."Dimension Code");
                                            TempDimSetEntry.Validate("Dimension Value Code", DefDim."Dimension Value Code");
                                            DimValue.Reset();
                                            DimValue.SetRange(DimValue."Dimension Code", DefDim."Dimension Code");
                                            DimValue.SetRange(DimValue.Code, DefDim."Dimension Value Code");
                                            if DimValue.FindFirst() then begin
                                                TempDimSetEntry.Validate("Dimension Value ID", DimValue."Dimension Value ID");
                                            end;
                                            IF NOT TempDimSetEntry.INSERT
                                            THEN
                                                TempDimSetEntry.MODIFY;

                                        until DefDim.Next() = 0;
                                    end;
                                    ItemJnlLine."Dimension Set ID" := DimensionManagement.GetDimensionSetID(TempDimSetEntry);
                                    ItemJnlLine.Insert();
                                    Page.Run(99000823, ItemJnlLine);
                                end;
                            end;
                        end;
                        //end;
                    end;
                }
                //end

            }
        }
    }


    trigger OnAfterGetRecord()
    begin
        DescriptionIndent := 0;
        ShowShortcutDimCode(ShortcutDimCode);
        DescriptionOnFormat;
        GetStartingEndingDateAndTime(StartingTime, StartingDate, EndingTime, EndingDate);
    end;

    trigger OnDeleteRecord(): Boolean
    var
        ReserveProdOrderLine: Codeunit "Prod. Order Line-Reserve";
    begin
        Commit();
        if not ReserveProdOrderLine.DeleteLineConfirm(Rec) then
            exit(false);
    end;

    trigger OnInit()
    begin
        DateAndTimeFieldVisible := false;
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        Clear(ShortcutDimCode);
    end;

    trigger OnOpenPage()
    begin
        DateAndTimeFieldVisible := false;
    end;


    //24.11.23 from ext
    //g2s 311023 PROJECT LEAP
    trigger OnAfterGetCurrRecord()
    var
        myInt: Integer;
        PageRef: Page "Released Prod. Order Lines";
        ProdOrdLine: Record "Prod. Order Line";
        AllowModify: Boolean;
        CustomCode: Codeunit "Custom-G2S";
    begin
        FieldEditable := not GetRelProdOrderBarcodeStatus(Rec."Prod. Order No.");
        //check user setup of user
        UserSetup.Reset();
        UserSetup.SetRange("User ID", USERID);
        if UserSetup.FindFirst() then begin
            if UserSetup."Can Modify Prod. Order" then
                GlobalProdEdit := true;
        end;
        //CurrPage.SaveRecord();
        // ValidatePCQ(Rec);
        // ProdOrdLine.Reset();
        // ProdOrdLine.SetRange("Prod. Order No.", Rec."Prod. Order No.");
        // if ProdOrdLine.FindFirst() then begin
        // CurrPage.Update(true);
        // Rec.SetAutoCalcFields("Item Consumption Qty.");
        // CustomCode.ValidateRelProdOrder(Rec, Rec, AllowModify);
        CustomCode.ValidateRelProdOrder(Rec);
        // CurrPage.Update(true);
        // end;
        //ProdOrdLine.Get(Rec."Prod. Order No.")
        //CurrPage.Activate(true);

    end;


    /// <summary>
    /// ValidatePCQ.
    /// </summary>
    /// <param name="ProdOrdLine">VAR Record "Prod. Order Line".</param>
    procedure ValidatePCQ(var ProdOrdLine: Record "Prod. Order Line")
    var
        Item: Record Item;
        ItemLedgEntry, ItemLedgEntryCopy : Record "Item Ledger Entry";
        ProdOrderComp: Record "Prod. Order Component";
        PCQ, POQ : Integer;
        RelProdOrderLine, ProdOrderLine : Record "Prod. Order Line";
        ProdOrder: Record "Production Order";
    begin
        ProdOrder.Reset();
        ProdOrder.SetRange("No.", "Prod. Order No.");
        IF ProdOrder.FindFirst() then begin
            Item.Reset();
            Item.SetRange("No.", ProdOrder."Source No.");
            Item.SetRange("License Plate Enabled?", true);
            If Item.FindFirst() then begin
                ItemLedgEntry.Reset();
                ItemLedgEntry.SetRange("Document No.", ProdOrder."No.");
                ItemLedgEntry.SetRange("Source No.", ProdOrder."Source No.");
                ItemLedgEntry.SetRange("Entry Type", ItemLedgEntry."Entry Type"::Output);
                ItemLedgEntry.CalcSums(ItemLedgEntry.Quantity);
                POQ := ItemLedgEntry.Quantity;
                If POQ <> 0 then begin
                    ProdOrderComp.Reset();
                    ProdOrderComp.SetCurrentKey("Prod. Order No.");
                    ProdOrderComp.SetCurrentKey("Item No.");
                    ProdOrderComp.SetRange("Prod. Order No.", ProdOrder."No.");
                    if ProdOrderComp.FindFirst() then begin
                        ItemLedgEntryCopy.Reset();
                        ItemLedgEntry.SetCurrentKey("Document No.", "Item No.");
                        ItemLedgEntryCopy.SetRange("Document No.", ProdOrderComp."Prod. Order No.");
                        ItemLedgEntryCopy.SetRange("Source No.", ProdOrder."Source No.");
                        ItemLedgEntryCopy.SetRange("Entry Type", ItemLedgEntry."Entry Type"::Consumption);
                        ItemLedgEntryCopy.SetRange("Item No.", ProdOrderComp."Item No.");
                        ItemLedgEntryCopy.CalcSums(Quantity);
                        PCQ := ABS(Round((ItemLedgEntryCopy.Quantity / ProdOrderComp."Quantity per"), 1, '>'));

                        //if PCQ <> 0 then begin
                        RelProdOrderLine.Reset();
                        RelProdOrderLine.SetRange("Prod. Order No.", ProdOrder."No.");
                        if RelProdOrderLine.FindFirst() then begin
                            if PCQ <> RelProdOrderLine."Posted Consumption Qty." then
                                RelProdOrderLine."Posted Consumption Qty." := PCQ;
                            //check if fraction qty is <> 0 and FQ = Qty
                            // if ((RelProdOrderLine.Quantity = RelProdOrderLine."Finished Quantity") and (RelProdOrderLine."Fraction Quantity" <> 0)) then begin
                            //     RelProdOrderLine."Fraction Quantity" := 0;
                            // end;
                            RelProdOrderLine.Modify();
                        end;
                        //end;
                    end;

                    // //update IWXLP Header
                    // IWXLP.Reset();
                    // IWXLP.SetCurrentKey("No.");
                    // IWXLP.SetRange("Production Order No.", Rec."No.");
                    // if IWXLP.FindFirst() then begin
                    //     LPScannedQty := 0;
                    //     repeat
                    //         IWXLPLine.Reset();
                    //         IWXLPLine.SetRange("License Plate No.", IWXLP."No.");
                    //         if IWXLPLine.FindFirst() then begin
                    //             If LPScannedQty < PCQ then begin
                    //                 if IWXLP."LP Posted?" <> true then begin
                    //                     IWXLP."LP Posted?" := true;
                    //                     IWXLP.Modify();
                    //                 end;
                    //             end;
                    //             //LPScannedQty += 1;
                    //             LPScannedQty += IWXLPLine.Quantity;
                    //         end
                    //     until IWXLP.Next() = 0;
                    // end;
                    // CurrPage.Update(true);
                    //CurrPage.Activate(true);
                end;
            end;
            // CurrPage.Update(true);
        end;
    end;


    //end
    var
        ItemAvailFormsMgt: Codeunit "Item Availability Forms Mgt";
        ShortcutDimCode: array[8] of Code[20];
        [InDataSet]
        DescriptionIndent: Integer;
        StartingTime: Time;
        EndingTime: Time;
        StartingDate: Date;
        EndingDate: Date;
        DateAndTimeFieldVisible: Boolean;

        //24.11.23 from ext
        Txt001: Label 'This Action cannot be used for Licensed Plate Enabled SKUs’. The production journal must be open with the filter of consumption entries only. Please use the consumption journal action instead';
        Txt002: Label 'This Action cannot be used for None Fraction Quantity.';
        Txt003: Label 'The base quantity output for the order has not been met. Action cannot continue';
        Txt004: Label 'No consumption available at the moment to be posted. Action cannot continue';
        FieldEditable: Boolean;
        UserSetup: Record "User Setup";
        GlobalProdEdit: Boolean;
    //24.11.23 from ext

    local procedure ShowComponents()
    var
        ProdOrderComp: Record "Prod. Order Component";
    begin
        ProdOrderComp.SetRange(Status, Status);
        ProdOrderComp.SetRange("Prod. Order No.", "Prod. Order No.");
        ProdOrderComp.SetRange("Prod. Order Line No.", "Line No.");

        PAGE.Run(PAGE::"Prod. Order Components", ProdOrderComp);
    end;

    procedure ShowTracking()
    var
        TrackingForm: Page "Order Tracking";
    begin
        TrackingForm.SetProdOrderLine(Rec);
        TrackingForm.RunModal;
    end;

    local procedure ItemAvailability(AvailabilityType: Option)
    begin
        ItemAvailFormsMgt.ShowItemAvailFromProdOrderLine(Rec, AvailabilityType);
    end;

    local procedure ShowReservationEntries()
    begin
        ShowReservationEntries(true);
    end;

    local procedure ShowProductionJournal()
    var
        ProdOrder: Record "Production Order";
        ProductionJrnlMgt: Codeunit "Production Journal Mgt";
    begin
        CurrPage.SaveRecord;

        ProdOrder.Get(Status, "Prod. Order No.");

        Clear(ProductionJrnlMgt);
        ProductionJrnlMgt.Handling(ProdOrder, "Line No.");
    end;

    procedure UpdateForm(SetSaveRecord: Boolean)
    begin
        CurrPage.Update(SetSaveRecord);
    end;

    local procedure DescriptionOnFormat()
    begin
        DescriptionIndent := "Planning Level Code";
    end;

    procedure PageShowReservation()
    begin
        CurrPage.SaveRecord;
        ShowReservation;
    end;
}


