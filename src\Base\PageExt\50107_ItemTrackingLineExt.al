pageextension 50107 ItemTracking extends "Item Tracking Lines"
{
    layout
    {
        addafter("Quantity (Base)")
        {
            field(ExpirationDate; "Expiration Date")
            { }
            field("Manufacturing Date"; "Manufacturing Date") //>>>> G2S 25/03/25 CAS-01412-X4D4V7
            { }
            field("Lot No.2"; "Lot No.")
            {
                Caption = 'Lot No.';
            }

        }

    }

    actions
    {
        // Add changes to page actions here
        //B2B SalesReturnLotNo>>
        addafter("Assign Lot No.")
        {

            action("Update Lot No.")
            {
                //Visible = UpdateLotVisable;
                ApplicationArea = all;
                Caption = 'Update Sales Return Order Lot No.';
                trigger OnAction()
                var
                    SalesLine: Record "Sales Line";
                    SalesShipmetLine: Record "Sales Shipment Line";
                    ItemLedgerEntries: Record "Item Ledger Entry";
                begin
                    if ("Source Type" = 37) and ("Source Subtype" = 5) then
                        if SalesLine.GET(SalesLine."Document Type"::"Return Order", "Source ID", "Source Ref. No.") then begin
                            SalesShipmetLine.Reset();
                            SalesShipmetLine.SetCurrentKey("Posting Date");
                            SalesShipmetLine.SetRange("Sell-to Customer No.", SalesLine."Sell-to Customer No.");
                            SalesShipmetLine.SetRange("No.", SalesLine."No.");
                            SalesShipmetLine.SetFilter(Quantity, '>=%1', SalesLine.Quantity);
                            if SalesShipmetLine.FindLast() then begin
                                ItemLedgerEntries.Reset();
                                ItemLedgerEntries.SetRange("Entry Type", ItemLedgerEntries."Entry Type"::Sale);
                                ItemLedgerEntries.SetRange("Document Type", ItemLedgerEntries."Document Type"::"Sales Shipment");
                                ItemLedgerEntries.SetRange("Document No.", SalesShipmetLine."Document No.");
                                ItemLedgerEntries.SetRange("Document Line No.", SalesShipmetLine."Line No.");
                                if ItemLedgerEntries.FindLast() then
                                    "Lot No." := ItemLedgerEntries."Lot No."
                            end else
                                Message('There are no earlier posted shipment lines existing for this item and customer.')
                        end;
                end;
            }
        }
        //B2B SalesReturnLotNo<<
    }

    var
        myInt: Integer;
        UpdateLotVisable: Boolean;

    trigger OnAfterGetRecord()
    begin
        if ("Source Type" = 37) and ("Source Subtype" = 5) then
            UpdateLotVisable := true
        else
            UpdateLotVisable := false;
    end;
}