page 50207 Destinations
{
    PageType = list;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = Destination;
    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Destination Code"; "Destination Code")
                {
                    ApplicationArea = ALL;
                }
                field(Name; Name)
                {
                    ApplicationArea = ALL;
                }
                field("Address 1"; "Address 1")
                {
                    ApplicationArea = ALL;
                }
                field("Address 2"; "Address 2")
                {
                    ApplicationArea = ALL;
                }
                field("Nearest Location"; "Nearest Location")
                {

                }
                field("Destination Type"; "Destination Type")
                {
                    ApplicationArea = All;

                }
            }
        }
    }
}
