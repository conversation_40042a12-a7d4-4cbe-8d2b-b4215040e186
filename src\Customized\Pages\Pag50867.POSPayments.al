page 50867 POSPayments
{
    ApplicationArea = All;
    Caption = 'POSPayments';
    PageType = List;
    SourceTable = POSPaymentLog;
    UsageCategory = Lists;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(TransactionID; Rec.TransactionID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the TransactionID field.', Comment = '%';
                }
                field(AccountID; Rec.AccountID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the AccountID field.', Comment = '%';
                }
                field(AccountNo; Rec.AccountNo)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the AccountNo field.', Comment = '%';
                }
                field(Amount; Rec.Amount)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Amount field.', Comment = '%';
                }
                field(PaymentTypeID; Rec.PaymentTypeID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the PaymentTypeID field.', Comment = '%';
                }
                field("PaymentType Description"; Rec."PaymentType Description")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the PaymentType Description field.', Comment = '%';
                }
                field(PaymentReference; rec.PaymentReference)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Payment Reference field.', Comment = '%';
                }
                field("Outlet ID"; "Outlet ID")
                {
                    ApplicationArea = All;
                }
                field("Staff ID"; "Staff ID")
                {
                    ApplicationArea = All;
                }
                field("Receipt No"; "Receipt No")
                {
                    Caption = 'Receipt No.';
                    ApplicationArea = All;
                }
                field(Date; Date)
                {
                    ApplicationArea = All;

                }
            }
        }
    }
}
