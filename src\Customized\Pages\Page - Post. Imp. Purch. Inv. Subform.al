page 50703 "Post. Imp. Purch. Inv. Subform"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created for Posted Import Purchase Invoices.

    AutoSplitKey = true;
    Caption = 'Post. Imp. Purch. Inv. Subform';
    Editable = false;
    LinksAllowed = false;
    PageType = ListPart;
    SourceTable = "Purch. Inv. Line";

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field(Type; Type)
                {
                }
                field("No."; "No.")
                {
                }
                field("Cross-Reference No."; "Cross-Reference No.")
                {
                    Visible = false;
                }
                field("IC Partner Code"; "IC Partner Code")
                {
                    Visible = false;
                }
                field("Variant Code"; "Variant Code")
                {
                    Visible = false;
                }
                field(Description; Description)
                {
                }
                field("Return Reason Code"; "Return Reason Code")
                {
                    Visible = false;
                }
                field(Quantity; Quantity)
                {
                    BlankZero = true;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    Visible = false;
                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                    BlankZero = true;
                }
                field("Indirect Cost %"; "Indirect Cost %")
                {
                    Visible = false;
                }
                field("Unit Cost (LCY)"; "Unit Cost (LCY)")
                {
                    Visible = false;
                }
                field("Unit Price (LCY)"; "Unit Price (LCY)")
                {
                }
                field("Line Amount"; "Line Amount")
                {
                    BlankZero = true;
                }
                field("Line Discount %"; "Line Discount %")
                {
                    BlankZero = true;
                }
                field("Line Discount Amount"; "Line Discount Amount")
                {
                    Visible = false;
                }
                field("Allow Invoice Disc."; "Allow Invoice Disc.")
                {
                    Visible = false;
                }
                field("Job No."; "Job No.")
                {
                    Visible = false;
                }
                field("Insurance No."; "Insurance No.")
                {
                    Visible = false;
                }
                field("Budgeted FA No."; "Budgeted FA No.")
                {
                    Visible = false;
                }
                field("FA Posting Type"; "FA Posting Type")
                {
                    Visible = false;
                }
                field("Depr. until FA Posting Date"; "Depr. until FA Posting Date")
                {
                    Visible = false;
                }
                field("Depreciation Book Code"; "Depreciation Book Code")
                {
                    Visible = false;
                }
                field("Depr. Acquisition Cost"; "Depr. Acquisition Cost")
                {
                    Visible = false;
                }
                field("Appl.-to Item Entry"; "Appl.-to Item Entry")
                {
                    Visible = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    Visible = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50053. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchInvLines.FORM.*/
                        _ShowDimensions;

                    end;
                }
                action("Co&mments")
                {
                    Caption = 'Co&mments';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50053. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchInvLines.FORM.*/
                        _ShowLineComments;

                    end;
                }
                action("Item &Tracking Entries")
                {
                    Caption = 'Item &Tracking Entries';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50053. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchInvLines.FORM.*/
                        _ShowItemTrackingLines;

                    end;
                }
                action("Item Receipt &Lines")
                {
                    Caption = 'Item Receipt &Lines';

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50053. Unsupported part was commented. Please check it.
                        /*CurrPage.PurchInvLines.FORM.*/
                        _ShowItemReceiptLines;

                    end;
                }
            }
        }
    }

    procedure _ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;

    procedure ShowDimensions();
    begin
        Rec.ShowDimensions;
    end;

    procedure _ShowItemTrackingLines();
    begin
        Rec.ShowItemTrackingLines;
    end;

    procedure ShowItemTrackingLines();
    begin
        Rec.ShowItemTrackingLines;
    end;

    procedure _ShowItemReceiptLines();
    begin
        if not (Type in [Type::Item, Type::"Charge (Item)"]) then
            TESTFIELD(Type);
        Rec.ShowItemReceiptLines;
    end;

    procedure ShowItemReceiptLines();
    begin
        if not (Type in [Type::Item, Type::"Charge (Item)"]) then
            TESTFIELD(Type);
        Rec.ShowItemReceiptLines;
    end;

    procedure _ShowLineComments();
    begin
        Rec.ShowLineComments;
    end;

    procedure ShowLineComments();
    begin
        Rec.ShowLineComments;
    end;
}

