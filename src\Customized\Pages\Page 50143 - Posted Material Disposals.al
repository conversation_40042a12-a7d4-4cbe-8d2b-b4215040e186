page 50143 "Posted Material Disposal"
{
    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Document;
    SourceTable = "MDV Header";
    SourceTableView = SORTING("MDV No.")
                      ORDER(Ascending)
                      WHERE(Posted = CONST(True));
    UsageCategory = Documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MDV No."; "MDV No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Manual MDV. No"; "Manual MDV. No")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Type"; "Disposal Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                /* field("Indent Dept."; "Indent Dept.")
                 {
                     ApplicationArea = all;
                     Editable = false;
                 }
                 field("Indent Bus. Unit"; "Indent Bus. Unit")
                 {
                     ApplicationArea = all;
                     Editable = false;
                 }*/
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Expected Disposal Date"; "Expected Disposal Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(PostedMatDispLineSubform; "Posted Mat. Dis. Lines Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Document No." = FIELD("MDV No.");
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Disposal")
            {
                Caption = '&Disposal';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';
                    image = Dimensions;
                    trigger OnAction();
                    begin
                        ShowDocDim();
                    end;
                }
                group("&Print")
                {
                    Caption = '&Print';
                    action("Disposal Voucher")
                    {
                        ApplicationArea = all;
                        Caption = 'Disposal Voucher';
                        Ellipsis = true;
                        image = Voucher;
                        trigger OnAction();
                        var
                        begin
                            SETRECFILTER();
                            message('Need to add report.');
                            REPORT.RUNMODAL(REPORT::"Material Disposal Slip", TRUE, FALSE, Rec);
                        end;
                    }
                }
            }
        }
        area(processing)
        {
            action("&Navigate")
            {
                ApplicationArea = all;
                Caption = '&Navigate';
                Promoted = true;
                PromotedCategory = Process;
                image = Navigate;
                trigger OnAction();
                begin
                    Navigate.SetDoc("Posting Date", "MDV No.");
                    Navigate.RUN();
                end;
            }
        }
    }

    trigger OnOpenPage();
    begin
        if UserMg.GetMDVFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetMDVFilter());
            FILTERGROUP(0);
        end;
    end;

    var
        UserMg: Codeunit Mgmt;
        Navigate: Page Navigate;
}

