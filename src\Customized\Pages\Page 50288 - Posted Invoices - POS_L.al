page 50925 "Posted Invoices - POS_L"
{
    // version GJ_CHI_POS_1.0

    PageType = List;
    SourceTable = "Sales Invoice Line";
    UsageCategory = Lists;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Document No."; "Document No.")
                {
                    Editable = false;
                }
                field(Description; Description)
                {
                    Editable = false;
                }
                field("No."; "No.")
                {
                    Editable = false;
                }
                field("Location Code"; "Location Code")
                {
                    Editable = false;
                }
                field(Quantity; Quantity)
                {
                    Editable = false;
                }
                field("Posting Date"; "Posting Date")
                {
                    Editable = false;
                    ApplicationArea = all;//Balu 05092021
                }
                field(QtytoReturn; QtytoReturn)
                {
                    Caption = 'Qty. to Return';
                }
                field("Unit Price"; "Unit Price")
                {
                    Editable = false;
                }
                field("Line Discount %"; "Line Discount %")
                {
                    Editable = false;
                }
                field(Amount; Amount)
                {
                    Editable = false;
                }
            }
        }
    }

    actions
    {
    }

    trigger OnQueryClosePage(CloseAction: Action): Boolean;
    begin
        if CloseAction = ACTION::LookupOK then
            LookupOKOnPush;
    end;

    var
        SalesHdr: Record "Sales Header";
        SalesHdr2: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesL: Record "Sales Line";
        SLine: Record "Sales Invoice Line";
        SalesSetup: Record "Sales & Receivables Setup";
        Cust: Record Customer;
        VLE: Record "Value Entry";
        ILERec: Record "Item Ledger Entry";
        NoSeriesM: Codeunit NoSeriesManagement;
        CremoNo: Code[20];
        FilterVal: Code[20];
        ReservRec2: Record "Reservation Entry";
        ReservRec: Record "Reservation Entry";
        CustResp: Record "Customer Resp. Cent. Lines";
        ItemRec: Record Item;
        QtytoReturn: Decimal;
        OriginalQuantity: Boolean;
        LinesNotCopied: Integer;
        MissingExCostRevLink: Boolean;
        CopyDocMgt: Codeunit "Copy Document Mgt.";
        GenLedg: Record "General Ledger Setup";
        BankAcc: Record "Bank Account";

    procedure SFilter("Filter": Code[20]);
    begin
        FilterVal := Filter;
    end;

    procedure CreateVoucHdr();
    var
        VouchHdr: Record "Voucher Header";
        DocNo: Code[20];
        SalInvLine: Record "Sales Invoice Line";
        AmtVal: Decimal;
    begin
        if GenLedg.GET then;
        DocNo := NoSeriesM.GetNextNo(GenLedg."Cash Payment Voucher No", TODAY, true);
        VouchHdr.INIT;
        VouchHdr.VALIDATE("Voucher Type", VouchHdr."Voucher Type"::CPV);
        VouchHdr.VALIDATE("Document No.", DocNo);
        VouchHdr.VALIDATE("Responsibility Center", "Responsibility Center");
        VouchHdr.VALIDATE(Status, VouchHdr.Status::Open);
        VouchHdr.VALIDATE("Posting Date", TODAY);
        VouchHdr.VALIDATE("No. Series", GenLedg."Cash Payment Voucher No");
        VouchHdr.VALIDATE(Narration, 'Retail Shop' + "Location Code" + 'Reversal');
        VouchHdr.VALIDATE("Account Type", VouchHdr."Account Type"::"Bank Account");
        VouchHdr.VALIDATE("Source Code", 'CPV');
        VouchHdr.VALIDATE("Journal Template Code", 'CPV');
        VouchHdr.VALIDATE("Journal Batch Name", 'CPV');
        VouchHdr.VALIDATE("Posting No. Series", GenLedg."Posted Cash Payment Voucher No");
        VouchHdr.VALIDATE("Created By", USERID);
        VouchHdr.VALIDATE("Created By Name", USERID);
        VouchHdr.VALIDATE("Created Date", TODAY);
        VouchHdr.VALIDATE("Created Time", TIME);
        VouchHdr.VALIDATE("Dim. Document Type", VouchHdr."Dim. Document Type"::CPV);
        if Cust.GET("Sell-to Customer No.") then;
        VouchHdr.VALIDATE(ToBeCollectedBy, Cust.Name);
        VouchHdr.VALIDATE(PaymentSettlementOf, 'Retail Shop Reversal');
        VouchHdr.VALIDATE("Payable To", VouchHdr."Payable To"::Customer);
        VouchHdr.VALIDATE("Payable Code", "Sell-to Customer No.");
        VouchHdr.VALIDATE("Payable Name", Cust.Name);
        VouchHdr.VALIDATE("Cash Payments Options", VouchHdr."Cash Payments Options"::"Cash Payment(Direct)");
        VouchHdr.VALIDATE("Cash Paid", true);
        VouchHdr.VALIDATE("Cash Paid By", USERID);
        VouchHdr.VALIDATE("Cash Paid On", CURRENTDATETIME);
        VouchHdr.VALIDATE("Transaction Type", VouchHdr."Transaction Type"::General);
        VouchHdr.VALIDATE("Cash Requisition Slip No.", "Document No.");
        VouchHdr.VALIDATE("Cash Account Type", VouchHdr."Cash Account Type"::Main);
        VouchHdr.INSERT;

        BankAcc.RESET;
        BankAcc.SETRANGE("Account Type", BankAcc."Account Type"::Cash);
        BankAcc.SETRANGE("Global Dimension 1 Code", "Shortcut Dimension 1 Code");
        if BankAcc.FINDFIRST then begin
            VouchHdr.RESET;
            VouchHdr.SETRANGE("Voucher Type", VouchHdr."Voucher Type"::CPV);
            VouchHdr.SETRANGE("Document No.", DocNo);
            if VouchHdr.FINDFIRST then begin
                VouchHdr.VALIDATE("Account No.", BankAcc."No.");
                VouchHdr.VALIDATE("Account Name", BankAcc.Name);
                VouchHdr.VALIDATE("Shortcut Dimension 1 Code", BankAcc."Global Dimension 1 Code");
                VouchHdr.VALIDATE("Shortcut Dimension 2 Code", BankAcc."Global Dimension 2 Code");
                VouchHdr.MODIFY;
            end;
        end;

        COMMIT;
        CurrPage.SETSELECTIONFILTER(Rec);
        SalInvLine.RESET;
        SalInvLine.SETRANGE("Document No.", "Document No.");
        SalInvLine.SETRANGE("Line No.", "Line No.");
        if SalInvLine.FINDFIRST then begin
            repeat
                AmtVal += SalInvLine."Amount Including VAT";
            until SalInvLine.NEXT = 0
        end else
            AmtVal := 0;
        CreateGLLine(DocNo, AmtVal);
    end;

    procedure CreateGLLine(DocNo1: Code[20]; Amt: Decimal);
    var
        GLLine: Record "Gen. Journal Line";
        VoucHdr: Record "Voucher Header";
    begin
        GLLine.INIT;
        GLLine.VALIDATE("Journal Template Name", 'CPV');
        GLLine.VALIDATE("Journal Batch Name", 'CPV');
        GLLine.VALIDATE("Document No.", DocNo1);
        GLLine.VALIDATE("Line No.", 10000);
        GLLine.VALIDATE("Account Type", GLLine."Account Type"::Customer);
        GLLine.VALIDATE("Account No.", "Sell-to Customer No.");
        GLLine.VALIDATE("Posting Date", TODAY);
        GLLine.VALIDATE("Document Type", GLLine."Document Type"::Payment);
        if Cust.GET("Sell-to Customer No.") then;
        GLLine.VALIDATE(Description, Cust.Name);
        GLLine.VALIDATE("Bal. Account Type", GLLine."Bal. Account Type"::"G/L Account");
        GLLine.VALIDATE(Amount, Amt);
        GLLine.VALIDATE("Debit Amount", Amt);
        GLLine.VALIDATE("Bill-to/Pay-to No.", "Sell-to Customer No.");
        VoucHdr.RESET;
        VoucHdr.SETRANGE("Voucher Type", VoucHdr."Voucher Type"::CPV);
        VoucHdr.SETRANGE("Document No.", DocNo1);
        if VoucHdr.FINDFIRST then begin
            GLLine.VALIDATE("Shortcut Dimension 1 Code", VoucHdr."Shortcut Dimension 1 Code");
            GLLine.VALIDATE("Shortcut Dimension 2 Code", VoucHdr."Shortcut Dimension 2 Code");
            GLLine.VALIDATE("Source Code", VoucHdr."Source Code");
        end;
        GLLine.VALIDATE("Applies-to Doc. Type", GLLine."Applies-to Doc. Type"::"Credit Memo");
        GLLine.VALIDATE("Applies-to Doc. No.", CremoNo);
        GLLine.VALIDATE("VAT Posting", GLLine."VAT Posting"::"Automatic VAT Entry");
        GLLine.VALIDATE("VAT Calculation Type", GLLine."VAT Calculation Type"::"Normal VAT");
        GLLine.VALIDATE("Bal. VAT Calculation Type", GLLine."Bal. VAT Calculation Type"::"Normal VAT");
        GLLine.VALIDATE("Source Type", GLLine."Source Type"::Customer);
        GLLine.VALIDATE("Source No.", "Sell-to Customer No.");
        GLLine.VALIDATE("Sell-to/Buy-from No.", "Sell-to Customer No.");
        GLLine.VALIDATE("Voucher Type", GLLine."Voucher Type"::CPV);
        //GLLine.VALIDATE(Status,GLLine.Status::Open);
        GLLine.VALIDATE("Created By Name", USERID);
        GLLine.VALIDATE("Created Date", TODAY);
        GLLine.VALIDATE("Created Time", TIME);
        if GLLine.INSERT then
            MESSAGE('CPV %1 Create Successfully, Kindly Approve & Post.', DocNo1);
    end;

    procedure ReturnPOSSales();
    begin
        CLEAR(CremoNo);
        if CONFIRM('Are you sure, you want to raise a return order for the selected items? for Bill No. %1', false, FilterVal) then begin
            SalesHdr.INIT;
            SalesHdr.VALIDATE("Document Type", SalesHdr."Document Type"::"Credit Memo");
            if SalesSetup.GET() then;
            CremoNo := NoSeriesM.GetNextNo(SalesSetup."Credit Memo Nos.", TODAY, true);
            SalesHdr.VALIDATE("No.", CremoNo);
            SalesHdr.VALIDATE("POS Window", true);
            Cust.RESET;
            Cust.SETRANGE("No.", "Sell-to Customer No.");
            if Cust.FINDFIRST then begin
                CustResp.RESET;
                CustResp.SETRANGE("Customer No.", Cust."No.");
                if CustResp.FINDFIRST then
                    SalesHdr.VALIDATE("Responsibility Center", CustResp."Resp. Center Code");
                SalesHdr.VALIDATE("Sell-to Customer No.", Cust."No.");
            end;
            SalesHdr.VALIDATE("Order Date", "Posting Date");
            SalesHdr.VALIDATE("Posting Date", TODAY);
            SalesHdr.VALIDATE("Location Code", "Location Code");
            //SalesHdr.VALIDATE("Cr. Memo Reason Type",SalesHdr."Cr. Memo Reason Type"::"Return Order");
            SalesHdr.VALIDATE("External Document No.", FilterVal);
            SalesHdr."Cr. Memo Reason Type" := SalesHdr."Cr. Memo Reason Type"::Others;
            SalesHdr."Cr. Memo Stock Type" := SalesHdr."Cr. Memo Stock Type"::Items;
            SalesHdr.INSERT(true);
            /*
            SalesHdr2.RESET;
            SalesHdr2.SETRANGE("Document Type",SalesHdr2."Document Type"::"Credit Memo");
            SalesHdr2.SETRANGE("No.",CremoNo);
            IF SalesHdr2.FINDFIRST THEN BEGIN
              SalesHdr2.VALIDATE("Shortcut Dimension 1 Code","Shortcut Dimension 1 Code");
              SalesHdr2.VALIDATE("Shortcut Dimension 2 Code","Shortcut Dimension 2 Code");
              SalesHdr2.MODIFY;
            END;
            */
            SalesHdr2.RESET;
            SalesHdr2.SETRANGE("Document Type", SalesHdr2."Document Type"::"Credit Memo");
            SalesHdr2.SETRANGE("No.", CremoNo);
            if SalesHdr2.FINDFIRST then begin
                SalesHdr2.VALIDATE("Shortcut Dimension 1 Code", "Shortcut Dimension 1 Code");
                SalesHdr2.VALIDATE("Shortcut Dimension 2 Code", "Shortcut Dimension 2 Code");
                if SalesHdr2.MODIFY then
                    MESSAGE('Credit Memo %1 Created Against Bill Return.', CremoNo);

                CurrPage.SETSELECTIONFILTER(Rec);
                LinesNotCopied := 0;
                OriginalQuantity := true;
                CopyDocMgt.SetProperties(false, false, false, false, true, true, OriginalQuantity);
                CopyDocMgt.CopySalesInvLinesToDoc(
                SalesHdr2, Rec, LinesNotCopied, MissingExCostRevLink);
            end;
            COMMIT;
            CreateVoucHdr;

            /*
            IF FINDFIRST THEN REPEAT
              SalesLine.INIT;
              SalesLine.VALIDATE("Document Type",SalesLine."Document Type"::"Credit Memo");
              SalesLine.VALIDATE("Document No.",CremoNo);
              SalesLine.VALIDATE("Line No.","Line No.");
              SalesLine.VALIDATE("POS Window",TRUE);
              SalesLine.VALIDATE("Sell-to Customer No.","Sell-to Customer No.");
              SalesLine.INSERT(TRUE);
              SalesL.RESET;
              SalesL.SETRANGE("Document Type",SalesL."Document Type"::"Credit Memo");
              SalesL.SETRANGE("Document No.",CremoNo);
              SalesL.SETRANGE("Line No.","Line No.");
              IF SalesL.FINDFIRST THEN BEGIN
                SalesL.VALIDATE(Type,SalesLine.Type::Item);
                IF ItemRec.GET("No.") THEN BEGIN
                  SalesL.VALIDATE("Item Category Code",ItemRec."Item Category Code");
                  SalesL.VALIDATE("Product Group Code",ItemRec."Product Group Code");
                END;
                SalesL.VALIDATE("No.","No.");
                SalesL.VALIDATE("Unit of Measure Code","Unit of Measure Code");
                SalesL.VALIDATE("Location Code","Location Code");
                SalesL.VALIDATE(Quantity,Quantity);
                SalesL.VALIDATE("Unit Price","Unit Price");
                VLE.RESET;
                VLE.SETRANGE("Document No.","Document No.");
                VLE.SETRANGE("Document Line No.","Line No.");
                IF VLE.FINDFIRST THEN BEGIN
                  //SalesL.VALIDATE("Appl.-from Item Entry",VLE."Item Ledger Entry No.");
              //Reservation Entry >>>>
                  ReservRec.INIT;
                  ReservRec2.RESET;
                  IF ReservRec2.FINDLAST THEN;
                  ReservRec.VALIDATE("Entry No.",ReservRec2."Entry No." + 1);
                  ReservRec.VALIDATE(Positive,TRUE);
                  ReservRec.VALIDATE("Item No.",VLE."Item No.");
                  ReservRec.VALIDATE("Location Code",VLE."Location Code");
                  ReservRec.VALIDATE("Quantity (Base)",(Quantity*"Qty. per Unit of Measure"));
                  ReservRec.VALIDATE("Reservation Status",ReservRec."Reservation Status"::Prospect);
                  ReservRec.VALIDATE("Creation Date","Posting Date");
                  ReservRec.VALIDATE("Source Type",37);
                  ReservRec.VALIDATE("Source Subtype",3);
                  ReservRec.VALIDATE("Source ID",CremoNo);
                  ReservRec.VALIDATE("Source Ref. No.","Line No.");
                  ReservRec.VALIDATE("Shipment Date","Posting Date");
                  ReservRec.VALIDATE("Created By",USERID);
                  ReservRec.VALIDATE("Qty. per Unit of Measure","Qty. per Unit of Measure");
                  ReservRec.VALIDATE(Quantity,(Quantity*"Qty. per Unit of Measure"));
                  ReservRec.VALIDATE("Planning Flexibility",ReservRec."Planning Flexibility"::Unlimited);
                  ReservRec.VALIDATE("Qty. to Handle (Base)",(Quantity*"Qty. per Unit of Measure"));
                  ReservRec.VALIDATE("Qty. to Invoice (Base)",(Quantity*"Qty. per Unit of Measure"));
                  IF ILERec.GET(VLE."Item Ledger Entry No.") THEN;
                  ReservRec.VALIDATE("Lot No.",ILERec."Lot No.");
                  ReservRec.VALIDATE("Appl.-from Item Entry",VLE."Item Ledger Entry No.");
                  ReservRec.VALIDATE("Item Tracking",ReservRec."Item Tracking"::"Lot No.");
                  ReservRec.INSERT;
              //Reservation Entry <<<<
              END;
              SalesL.MODIFY;
            END;

            SalesHdr2.RESET;
            SalesHdr2.SETRANGE("Document Type",SalesHdr2."Document Type"::"Credit Memo");
            SalesHdr2.SETRANGE("No.",CremoNo);
            IF SalesHdr2.FINDFIRST THEN BEGIN
              SalesHdr2.VALIDATE("Shortcut Dimension 1 Code","Shortcut Dimension 1 Code");
              SalesHdr2.VALIDATE("Shortcut Dimension 2 Code","Shortcut Dimension 2 Code");
              IF SalesHdr2.MODIFY THEN
                MESSAGE('Credit Memo %1 Created Against Bill Return.',CremoNo);
            END;
            */
            //UNTIL NEXT = 0;

            SalesHdr2.RESET;
            SalesHdr2.SETRANGE("No.", CremoNo);
            IF SalesHdr2.FINDFIRST THEN
                CODEUNIT.RUN(CODEUNIT::"Sales-Post (Yes/No)", SalesHdr2);

        end;
        CurrPage.CLOSE;

    end;

    local procedure LookupOKOnPush();
    begin
        ReturnPOSSales;
    end;
}

