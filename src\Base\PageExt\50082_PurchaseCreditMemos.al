pageextension 50082 PurchaseCreditMemo extends "Purchase Credit Memo"
{
    layout
    {
        addafter("Campaign No.")
        {
            field("Reason Codes"; "Reason Codes")
            {
                ApplicationArea = all;
            }

            field("Pos Load Slip Reason Code"; "Pos Load Slip Reason Code")
            {
                ApplicationArea = ALL;
                Editable = false;
                trigger onvalidate()
                BEGIN
                    /*
                    IF (("Pos Load Slip Reason Code" = "Pos Load Slip Reason Code"::"Transport Damage") OR ("Pos Load Slip Reason Code" = "Pos Load Slip Reason Code"::"Transport Shortage")) THEN BEGIN
                        IF (xRec."Pos Load Slip Reason Code" <> Rec."Pos Load Slip Reason Code") THEN
                            ERROR('You cannot Modify Pos Load SLip Reason Code.');
                    END;*/
                END;
            }
            field("Posted Loading Slip No."; "Posted Loading Slip No.")
            {
                ApplicationArea = ALL;
            }
            field("Customer No. For Adj"; "Customer No. For Adj")
            {
                ApplicationArea = ALL; //PKONJ29 whole object
            }
        }
    }
    actions
    {
        modify(Release)
        {
            trigger OnBeforeAction()//PKONAU11
            begin
                TestField("Shortcut Dimension 1 Code");//PKONSE17
                TestField("Shortcut Dimension 2 Code");//PKONSE17
                IF "Posted Loading Slip No." <> '' then
                    TestField("Customer No. For Adj");
            end;
        }
        modify(Post)
        {
            trigger OnBeforeAction()//PKONAU11
            begin
                TestField("Shortcut Dimension 1 Code");//PKONSE17
                TestField("Shortcut Dimension 2 Code");//PKONSE17
                IF "Posted Loading Slip No." <> '' then
                    TestField("Customer No. For Adj");
            end;
        }
        modify(SendApprovalRequest)//PKONAU11
        {
            trigger OnBeforeAction()
            begin
                TestField("Shortcut Dimension 1 Code");//PKONSE17
                TestField("Shortcut Dimension 2 Code");//PKONSE17
                IF "Posted Loading Slip No." <> '' then
                    TestField("Customer No. For Adj");
            end;
        }
    }
}