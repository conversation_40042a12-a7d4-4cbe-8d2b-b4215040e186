codeunit 50002 Codeunit3
{
    Permissions = tabledata "Fixed asset" = rm, tabledata "G/L Entry" = rm, tabledata "Item Ledger Entry" = RM, tabledata "Item Journal Line" = RMDI, tabledata "Gen. Journal Line" = RMDI, tabledata "Reservation Entry" = RMDI, tabledata Item = RM, tabledata "Cust. Ledger Entry" = rm;//PKONAU9 (Last Cust Led Entry); //PKNNO3 Fixed asset permission added 
    trigger OnRun()
    begin

    end;

    //PhaniFeb182021>>
    [EventSubscriber(ObjectType::Codeunit, 22, 'OnAfterInitItemLedgEntry', '', false, false)]
    local procedure OnAfterInitItemLedgEntry1(var NewItemLedgEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; var ItemLedgEntryNo: Integer)
    begin
        NewItemLedgEntry."Description 2" := ItemJournalLine."Description 2";
    end;
    //PhaniFeb182021<<


    /*[EventSubscriber(ObjectType::Codeunit, 90, 'OnBeforePostVendorEntry', '', false, false)]
    local procedure OnBeforePostVendorEntry1(var GenJnlLine: Record "Gen. Journal Line"; var PurchHeader: Record "Purchase Header"; var TotalPurchLine: Record "Purchase Line"; var TotalPurchLineLCY: Record "Purchase Line"; PreviewMode: Boolean; CommitIsSupressed: Boolean; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        PurLne: Record "Purchase Line";
    BEGIN
        //message('enter.');
        PurLne.RESET;
        PurLne.SetRange("Document No.", PurchHeader."No.");
        IF PurLne.findset then
            repeat
                //message('%1..%2', PurLne."capex No.", PurLne."capex Line No.");
                GenJnlLine."Capex No." := PurLne."capex No.";
                GenJnlLine."Capex Line No." := PurLne."capex Line No."
            until PurLne.next = 0;
        //error('STOP');
        //message('%1..%2', GenJnlLine."capex No.", GenJnlLine."capex Line No.");
    END;*/




    //vendor
    [EventSubscriber(ObjectType::Table, 38, 'OnAfterGetNoSeriesCode', '', false, false)]
    local procedure NoSeries(VAR PurchHeader: Record "Purchase Header"; PurchSetup: Record "Purchases & Payables Setup"; VAR NoSeriesCode: Code[20])
    begin

        if PurchHeader."Purchase Type" = PurchHeader."Purchase Type"::Import THEN
            CASE PurchHeader."Document Type" OF
                PurchHeader."Document Type"::Quote:
                    NoSeriesCode := PurchSetup."Import Purchase Quote";
                PurchHeader."Document Type"::Order:
                    NoSeriesCode := PurchSetup."Import Purchase Order";
                PurchHeader."Document Type"::Invoice:
                    NoSeriesCode := PurchSetup."Import Purchase Invoice";
                PurchHeader."Document Type"::"Credit Memo":
                    NoSeriesCode := PurchSetup."Import Purchase Credit Memo";
                PurchHeader."Document Type"::"Blanket Order":
                    NoSeriesCode := PurchSetup."Import Blanket Purchase Order";

            END;
        if PurchHeader."Purchase Type" = PurchHeader."Purchase Type"::Local THEN
            CASE PurchHeader."Document Type" OF
                PurchHeader."Document Type"::Quote:
                    NoSeriesCode := PurchSetup."Local Purchase Quote";
                PurchHeader."Document Type"::Order:
                    NoSeriesCode := PurchSetup."Local Purchase Order";
                PurchHeader."Document Type"::Invoice:
                    NoSeriesCode := PurchSetup."Local Purchase Invoice";
                PurchHeader."Document Type"::"Credit Memo":
                    NoSeriesCode := PurchSetup."Local Purchase Credit Memo";
                PurchHeader."Document Type"::"Blanket Order":
                    NoSeriesCode := PurchSetup."Local Blanket Purchase Order";
            END;

        if PurchHeader."Purchase Type" = PurchHeader."Purchase Type"::PMS then
            if PurchHeader."Document Type" = PurchHeader."Document Type"::Invoice then
                NoSeriesCode := PurchSetup."PMS Purchase Invoice";
        //PKONJ8 >>
        if PurchHeader."Purchase Type" = PurchHeader."Purchase Type"::Service then
            if PurchHeader."Document Type" = PurchHeader."Document Type"::Order then
                NoSeriesCode := PurchSetup."Import Service Orders";
        //PKONJ8 <<
    end;

    //customer
    [EventSubscriber(ObjectType::Table, 36, 'OnAfterGetNoSeriesCode', '', false, false)]
    local procedure Noseriess(VAR SalesHeader: Record "Sales Header"; SalesReceivablesSetup: Record "Sales & Receivables Setup"; VAR NoSeriesCode: Code[20])
    begin
        if SalesHeader."Sales Type" = SalesHeader."Sales Type"::Direct THEN BEGIN
            CASE SalesHeader."Document Type" OF
                SalesHeader."Document Type"::Order:
                    NoSeriesCode := SalesReceivablesSetup."Direct Sales Order";
                SalesHeader."Document Type"::Quote:
                    NoSeriesCode := SalesReceivablesSetup."Direct Sales Quote";
            END;
        END;
        if SalesHeader."Sales Type" = SalesHeader."Sales Type"::Local THEN BEGIN
            CASE SalesHeader."Document Type" OF
                SalesHeader."Document Type"::Order:
                    NoSeriesCode := SalesReceivablesSetup."Local Sales Order";
                SalesHeader."Document Type"::Quote:
                    NoSeriesCode := SalesReceivablesSetup."Local Sales Quote";

            END;
        end;
        if SalesHeader."Sales Type" = SalesHeader."Sales Type"::Export THEN BEGIN
            CASE SalesHeader."Document Type" OF
                SalesHeader."Document Type"::Order:
                    NoSeriesCode := SalesReceivablesSetup."Export Sales Order";
                SalesHeader."Document Type"::Quote:
                    NoSeriesCode := SalesReceivablesSetup."Export Sales Quote";

            END;
        END;
    End;


    [EventSubscriber(ObjectType::Codeunit, 80, 'OnBeforePostCustomerEntry', '', false, false)]
    procedure OnBeforePostCustomerEnt(var GenJnlLine: Record "Gen. Journal Line"; SalesHeader: Record "Sales Header"; var TotalSalesLine: Record "Sales Line"; var TotalSalesLineLCY: Record "Sales Line")
    begin
        GenJnlLine."Responsibility Center" := SalesHeader."Responsibility Center";
        GenJnlLine."Sales Rebate Period" := SalesHeader."Rebate Period Code";

    end;

    [EventSubscriber(ObjectType::Table, 21, 'OnAfterCopyCustLedgerEntryFromGenJnlLine', '', false, false)]
    procedure CopyFromGenJouLne(var CustLedgerEntry: Record "Cust. Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    BEGIN
        CustLedgerEntry."Responsibility Center" := GenJournalLine."Responsibility Center";
        CustLedgerEntry.Narration := GenJournalLine.Narration;
        CustLedgerEntry.Narration1 := GenJournalLine.Narration1;
        CustLedgerEntry."Paid By" := GenJournalLine."Paid By";//Fix05jul2021
        CustLedgerEntry."Teller / Cheque Date" := GenJournalLine."Teller / Cheque Date";
        //Baluonsep8>>
        if CustLedgerEntry."Document Type" = CustLedgerEntry."Document Type"::"Credit Memo" then begin
            CustLedgerEntry.Narration := GenJournalLine."Printable Comment 1";
            CustLedgerEntry.Narration1 := GenJournalLine."Printable Comment 2";
        end;
        //Baluonsep8<<
    END;

    /*[EventSubscriber(ObjectType::Table, 17, 'OnAfterCopyGLEntryFromGenJnlLine', '', false, false)]
    local procedure OnAfterCopyGLEntryFromGenJnlLine(var GLEntry: Record "G/L Entry"; var GenJournalLine: Record "Gen. Journal Line")
    var
        CapBudget: Record "Budget Line";
    begin
        //Feb162021>>
        CapBudget.RESET;
        CapBudget.SetRange("Document No.", GenJournalLine."Capex No.");
        CapBudget.SetRange("Line No.", GenJournalLine."Capex Line No.");
        IF CapBudget.findfirst then;

        GLEntry."Capex No." := GenJournalLine."Capex No.";
        GLEntry."Capex Line No." := GenJournalLine."Capex Line No.";
        GLEntry."Budget Name" := CapBudget."Budget Name";
        //Feb162021<<
        GLEntry.Narration := GenJournalLine.Narration;
        GLEntry.Narration1 := GenJournalLine.Narration1;
        GLEntry."Cheque No." := GenJournalLine."Cheque No.";
        GLEntry."Cheque Date" := GenJournalLine."Cheque Date";
        GLEntry."Old Document No." := GenJournalLine."Voucher No.";
        GLEntry."Provision Entry" := GenJournalLine."Provision Entry"; //PROV1.0
        //CWIP>>
        GLEntry."CWIP No." := GenJournalLine."CWIP No.";
        //CWIP<<
        //Feb162021>>
        GLEntry."Capex No." := GenJournalLine."Capex No.";
        GLEntry."Capex Line No." := GenJournalLine."Capex Line No.";
        //Feb162021<<

    end;*/


    [EventSubscriber(ObjectType::Table, 25, 'OnAfterCopyVendLedgerEntryFromGenJnlLine', '', false, false)]
    local procedure OnAfterCopyVendLedgerEntryFromGenJnlLine(var VendorLedgerEntry: Record "Vendor Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        VendorLedgerEntry.Narration := GenJournalLine.Narration;
        VendorLedgerEntry.Narration1 := GenJournalLine.Narration1;
        VendorLedgerEntry."Description 2" := GenJournalLine."Description 2";
    end;

    [EventSubscriber(ObjectType::Table, 271, 'OnAfterCopyFromGenJnlLine', '', false, false)]
    procedure OnAfterCopyFromGenJnlLine(var BankAccountLedgerEntry: Record "Bank Account Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    var
        BankAcc: Record "Bank Account";
        BankChequeDetails: Record "Bank Cheque Details";
    begin
        BankAccountLedgerEntry.Narration := GenJournalLine.Narration;
        BankAccountLedgerEntry.Narration1 := GenJournalLine.Narration1;
        BankAccountLedgerEntry."Cheque No." := GenJournalLine."Cheque No.";
        BankAccountLedgerEntry."Cheque Date" := GenJournalLine."Cheque Date";
        //B2BFix 16Apr2021>>
        BankAccountLedgerEntry."Teller / Cheque Date" := GenJournalLine."Teller / Cheque Date";
        BankAccountLedgerEntry."Teller / Cheque No." := GenJournalLine."Teller / Cheque No.";
        BankAccountLedgerEntry."Description 2" := GenJournalLine."Description 2";
        BankAccountLedgerEntry."Description 3" := GenJournalLine."Description 3";
        //B2BFix 16Apr2021<<
    end;

    /*[EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post (Yes/No)", 'OnAfterConfirmPost', '', false, false)]
    local procedure OnAfterConfirmPost(var SalesHeader: Record "Sales Header")
    begin
        SalesHeader.TestField("Reason Codes");
    end;*/

    /*
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post (Yes/No)", 'OnAfterConfirmPost', '', false, false)]
    local procedure OnAfterConfirmPost1(PurchaseHeader: Record "Purchase Header")
    begin
        PurchaseHeader.TestField("Reason Codes");
    end;*/



    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blanket Purch. Order to Order", 'OnBeforeInsertPurchOrderHeader', '', false, false)]
    procedure InsertBlanketOrder(var PurchOrderHeader: Record "Purchase Header"; BlanketOrderPurchHeader: Record "Purchase Header")
    var
        VendorGRec: Record Vendor;
    begin
        PurchOrderHeader."Contract Start Date" := BlanketOrderPurchHeader."Contract Start Date";
        PurchOrderHeader."End Date" := BlanketOrderPurchHeader."End Date";
        PurchOrderHeader."Blanket Order Ref No" := BlanketOrderPurchHeader."No.";
        IF VendorGRec.GET(PurchOrderHeader."Buy-from Vendor No.") THEN BEGIN
            //VendorGRec.TestField("Vendor Type");
            IF VendorGRec."Vendor Type" = VendorGRec."Vendor Type"::" " then
                error('Vendor Type Must Have a value in %1', VendorGRec."No.");
            PurchOrderHeader."Purchase Type" := VendorGRec."Vendor Type";
        END;
        //PurchOrderHeader."Purchase Type" := BlanketOrderPurchHeader."Purchase Type";
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Quote to Order", 'OnBeforeInsertPurchOrderLine', '', false, false)]
    procedure OnBeforeInsertPurchOrderLineQuote(var PurchOrderLine: Record "Purchase Line"; PurchOrderHeader: Record "Purchase Header"; PurchQuoteLine: Record "Purchase Line"; PurchQuoteHeader: Record "Purchase Header")
    begin
        PurchOrderLine."Capex No." := PurchQuoteLine."Capex No.";
        PurchOrderLine."Capex Line No." := PurchQuoteLine."Capex Line No.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blanket Purch. Order to Order", 'OnBeforeInsertPurchOrderLine', '', false, false)]
    procedure OnBeforeInsertPurchOrderLine(var PurchOrderLine: Record "Purchase Line"; PurchOrderHeader: Record "Purchase Header"; var BlanketOrderPurchLine: Record "Purchase Line"; BlanketOrderPurchHeader: Record "Purchase Header")
    begin
        /*BlanketOrderPurchHeader.TestField("Order Date");
        BlanketOrderPurchHeader.Reset();
        BlanketOrderPurchHeader.SetRange("Document Type", BlanketOrderPurchHeader."Document Type"::"Blanket Order");
        BlanketOrderPurchHeader.SetRange("No.", BlanketOrderPurchLine."Document No.");
        BlanketOrderPurchHeader.SetRange("Order Date", BlanketOrderPurchLine."Contract Start Date", BlanketOrderPurchLine."End Date");
        if NOT BlanketOrderPurchHeader.FindFirst() then begin
            error('Order date Should be within creation date and date');

        end else BEGIN
            PurchOrderLine."Contract Start Date" := BlanketOrderPurchLine."Contract Start Date";
            PurchOrderLine."End Date" := BlanketOrderPurchLine."End Date";
            PurchOrderLine."Min Qty" := BlanketOrderPurchLine."Min Qty";
        END;*/
        /*
        BlanketOrderPurchLine.reset;
        BlanketOrderPurchLine.SetRange("Document Type", BlanketOrderPurchLine."Document Type"::"Blanket Order");
        BlanketOrderPurchLine.SetRange("Document No.", BlanketOrderPurchHeader."No.");
        IF BlanketOrderPurchLine.findset then
            repeat
                IF ((BlanketOrderPurchLine."Contract Start Date" < BlanketOrderPurchHeader."Order Date") OR (BlanketOrderPurchLine."End Date" > BlanketOrderPurchHeader."Order Date")) THEN
                    Error('Order date Should be within creation date and date');
            until BlanketOrderPurchLine.next = 0;*/
        PurchOrderLine."Contract Start Date" := BlanketOrderPurchLine."Contract Start Date";
        PurchOrderLine."End Date" := BlanketOrderPurchLine."End Date";
        PurchOrderLine."Min Qty" := BlanketOrderPurchLine."Min Qty";
        //PurchOrderHeader."Purch Comment Type" := BlanketOrderPurchHeader."Purch Comment Type";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blanket Purch. Order to Order", 'OnAfterPurchOrderLineInsert', '', false, false)]
    procedure OnAfterPurchOrderLneInsert(var PurchaseLine: Record "Purchase Line"; var BlanketOrderPurchLine: Record "Purchase Line")
    var
        TaskLRec: Record "Tasks Lines";
        TaskLneLRec: Record "Tasks Lines";
        LneNoLVar: Integer;
    begin
        clear(LneNoLVar);
        TaskLRec.reset;
        TaskLRec.SetRange("Document Type", GetDocumentValues(PurchaseLine));
        TaskLRec.SetRange("Document No.", PurchaseLine."Document No.");
        TaskLRec.SetRange("Document Line No.", PurchaseLine."Line No.");
        IF TaskLRec.FINDLAST then
            LneNoLVar := TaskLRec."Line No." + 10000
        else
            LneNoLVar := 10000;


        TaskLRec.reset;
        TaskLRec.SetRange("Document Type", GetDocumentValues(BlanketOrderPurchLine));
        TaskLRec.SetRange("Document No.", BlanketOrderPurchLine."Document No.");
        TaskLRec.SetRange("Document Line No.", BlanketOrderPurchLine."Line No.");
        TaskLRec.SetFilter("Qty. to Receive", '<>%1', 0);
        IF TaskLRec.findSET then
            repeat
                TaskLneLRec.INIT;
                TaskLneLRec."Document Type" := GetDocumentValues(PurchaseLine);
                TaskLneLRec."Document No." := PurchaseLine."Document No.";
                TaskLneLRec."Document Line No." := PurchaseLine."Line No.";
                TaskLneLRec."Line No." := LneNoLVar;
                TaskLneLRec.Insert();

                TaskLneLRec."Task Code" := TaskLRec."Task Code";
                TaskLneLRec."Task Description" := TaskLRec."Task Description";
                TaskLneLRec.Comments := TaskLRec.Comments;
                TaskLneLRec.Quantity := TaskLRec.Quantity;
                TaskLneLRec."Qty. to Receive" := TaskLRec."Qty. to Receive";
                TaskLneLRec."Qty. Received" := TaskLRec."Qty. to Receive";
                TaskLneLRec."Unit of Measure" := TaskLRec."Unit of Measure";
                TaskLneLRec."Job/Service Compl. Certif. No." := TaskLRec."Job/Service Compl. Certif. No.";
                TaskLneLRec."Blanket Order Ref No" := BlanketOrderPurchLine."Document No.";
                TaskLneLRec."Blanket Order Ref. Line No." := BlanketOrderPurchLine."Line No.";
                TaskLneLRec.Modify();
                LneNoLVar += 10000;
            until TaskLRec.next = 0;
    END;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterPurchRcptLineInsert', '', false, false)]
    procedure OnAfterPurchRcptLineInser(PurchaseLine: Record "Purchase Line"; var PurchRcptLine: Record "Purch. Rcpt. Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean; PurchInvHeader: Record "Purch. Inv. Header"; var TempTrackingSpecification: Record "Tracking Specification" temporary)
    var
        TaskLRec: Record "Tasks Lines";
        TaskLneLRec: Record "Tasks Lines";
        LneNoLVar: Integer;
    begin
        clear(LneNoLVar);
        TaskLRec.reset;
        TaskLRec.SetRange("Document Type", TaskLRec."Document Type"::Received);
        TaskLRec.SetRange("Document No.", PurchRcptLine."Document No.");
        TaskLRec.SetRange("Document Line No.", PurchRcptLine."Line No.");
        IF TaskLRec.FINDLAST then
            LneNoLVar := TaskLRec."Line No." + 10000
        else
            LneNoLVar := 10000;


        TaskLRec.reset;
        TaskLRec.SetRange("Document Type", GetDocumentValues(PurchaseLine));
        TaskLRec.SetRange("Document No.", PurchaseLine."Document No.");
        TaskLRec.SetRange("Document Line No.", PurchaseLine."Line No.");
        TaskLRec.SetFilter("Qty. to Receive", '<>%1', 0);
        IF TaskLRec.findSET then
            repeat
                TaskLneLRec.INIT;
                TaskLneLRec."Document Type" := TaskLneLRec."Document Type"::Received;
                TaskLneLRec."Document No." := PurchRcptLine."Document No.";
                TaskLneLRec."Document Line No." := PurchRcptLine."Line No.";
                TaskLneLRec."Line No." := LneNoLVar;
                TaskLneLRec.Insert();
                TaskLneLRec."Task Code" := TaskLRec."Task Code";
                TaskLneLRec."Task Description" := TaskLRec."Task Description";
                TaskLneLRec.Comments := TaskLRec.Comments;
                TaskLneLRec.Quantity := TaskLRec.Quantity;
                TaskLneLRec."Qty. Received" := TaskLRec."Qty. to Receive";
                TaskLneLRec."Job/Service Compl. Certif. No." := TaskLRec."Job/Service Compl. Certif. No.";
                TaskLneLRec."Unit of Measure" := TaskLRec."Unit of Measure";
                TaskLneLRec."Blanket Order Ref No" := PurchaseLine."Document No.";
                TaskLneLRec."Blanket Order Ref. Line No." := PurchaseLine."Line No.";
                TaskLneLRec.Modify();
                LneNoLVar += 10000;
            until TaskLRec.next = 0;
    END;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterPurchInvLineInsert', '', false, false)]
    procedure OnAfterPurchInvLineInser(var PurchInvLine: Record "Purch. Inv. Line"; PurchInvHeader: Record "Purch. Inv. Header"; PurchLine: Record "Purchase Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean)
    var
        TaskLRec: Record "Tasks Lines";
        TaskLneLRec: Record "Tasks Lines";
        LneNoLVar: Integer;
    begin
        clear(LneNoLVar);
        TaskLRec.reset;
        TaskLRec.SetRange("Document Type", TaskLRec."Document Type"::Invoiced);
        TaskLRec.SetRange("Document No.", PurchInvLine."Document No.");
        TaskLRec.SetRange("Document Line No.", PurchInvLine."Line No.");
        IF TaskLRec.FINDLAST then
            LneNoLVar := TaskLRec."Line No." + 10000
        else
            LneNoLVar := 10000;


        TaskLRec.reset;
        TaskLRec.SetRange("Document Type", GetDocumentValues(PurchLine));
        TaskLRec.SetRange("Document No.", PurchLine."Document No.");
        TaskLRec.SetRange("Document Line No.", PurchLine."Line No.");
        TaskLRec.SetFilter("Qty. to Receive", '<>%1', 0);
        IF TaskLRec.findSET then
            repeat
                TaskLneLRec.INIT;
                TaskLneLRec."Document Type" := TaskLneLRec."Document Type"::Received;
                TaskLneLRec."Document No." := PurchInvLine."Document No.";
                TaskLneLRec."Document Line No." := PurchInvLine."Line No.";
                TaskLneLRec."Line No." := LneNoLVar;
                TaskLneLRec.Insert();
                TaskLneLRec."Task Code" := TaskLRec."Task Code";
                TaskLneLRec."Task Description" := TaskLRec."Task Description";
                TaskLneLRec.Comments := TaskLRec.Comments;
                TaskLneLRec.Quantity := TaskLRec.Quantity;
                TaskLneLRec."Job/Service Compl. Certif. No." := TaskLRec."Job/Service Compl. Certif. No.";
                TaskLneLRec."Qty. Received" := TaskLRec."Qty. to Receive";
                TaskLneLRec."Unit of Measure" := TaskLRec."Unit of Measure";
                TaskLneLRec."Blanket Order Ref No" := PurchLine."Document No.";
                TaskLneLRec."Blanket Order Ref. Line No." := PurchLine."Line No.";
                TaskLneLRec.modify();
                LneNoLVar += 10000;
            until TaskLRec.next = 0;
    END;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", 'OnAfterInitPostedShptLine', '', false, false)]
    local procedure OnAfterInitPostedShptLne(var WhseShipmentLine: Record "Warehouse Shipment Line"; var PostedWhseShipmentLine: Record "Posted Whse. Shipment Line")
    var
        PosLoadSlpLne: Record "Posted Loading Slip Line";
    begin

        PosLoadSlpLne.reset;
        PosLoadSlpLne.SetRange("Document No.", WhseShipmentLine."Posted Loading Slip No.");
        PosLoadSlpLne.SetRange("Item No.", WhseShipmentLine."Item No.");
        PosLoadSlpLne.SetRange("Order No.", WhseShipmentLine."Source No.");//PKONJ9
        PosLoadSlpLne.SetRange("Order Line No.", WhseShipmentLine."Source Line No.");//PKONJ9
        IF PosLoadSlpLne.FindFirst() then begin
            PosLoadSlpLne.Applied := TRUE;
            PosLoadSlpLne.Modify();
            //WhseShipmentLine.VALIDATE("Posted Loading Slip No.", '');
            WhseShipmentLine."Posted Loading Slip No." := '';
            WhseShipmentLine."Posted Loading Slip Line No." := 0;
            WhseShipmentLine.modify;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", 'OnAfterInitPostedRcptLine', '', false, false)]
    local procedure OnAfterInitPostedRecptLne(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; var PostedWhseReceiptLine: Record "Posted Whse. Receipt Line")
    var
        PosLoadSlpLne: Record "Posted Loading Slip Line";
    begin

        PosLoadSlpLne.reset;
        PosLoadSlpLne.SetRange("No.", WarehouseReceiptLine."No.");
        PosLoadSlpLne.SetRange("Line No.", WarehouseReceiptLine."Line No.");
        IF PosLoadSlpLne.FindFirst() then begin
            PosLoadSlpLne."Applied for Receipt" := TRUE;
            PosLoadSlpLne.Modify();
            //WhseShipmentLine.VALIDATE("Posted Loading Slip No.", '');
            WarehouseReceiptLine."Posted Loading Slip No." := '';
            WarehouseReceiptLine."Posted Loading Slip Line No." := 0;
            WarehouseReceiptLine.modify;
        end;
    end;


    /*[EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Outbound", 'OnBeforeCheckSalesHeader', '', false, false)]
    local procedure OnBeforeCheckSalesHdr(var SalesHeader: Record "Sales Header"; var ShowError: Boolean)
    begin
        IF (NOT SalesHeader."Loading Slip Required") then
            error('Loading Slip Required is unchecked for this Sales Order %1', SalesHeader."No.");

    end;*/

    /*[EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Outbound", 'OnBeforeCreateFromOutbndTransferOrder', '', false, false)]
    local procedure OnBeforeCreateFromOutbndTransferOrd(var TransferHeader: Record "Transfer Header")
    begin
        IF TransferHeader."Transfer Type" = TransferHeader."Transfer Type"::"Branch Request" then begin
            ERROR('Loading Slip is Required for Transfer Order %1', TransferHeader."No.");
            TransferHeader.TestField("Transfer-to Code");
        end;
    end;*/
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnPostItemJnlLineOnAfterPrepareItemJnlLine', '', false, false)]
    local procedure OnAfterPostItemJnlLine(var ItemJournalLine: Record "Item Journal Line"; SalesLine: Record "Sales Line"; SalesHeader: Record "Sales Header")
    var
        InventoryPostGrpRec: Record "Inventory Posting Group";
    begin
        ItemJournalLine."Cust. Discount Code" := SalesLine."Cust. Discount Code";
        IF InventoryPostGrpRec.GET(SalesLine."Posting Group") THEN
            ItemJournalLine."Br. Cust. Discount Code" := InventoryPostGrpRec."Branch Prod. Discount Code";
        //message('PK in to IJL %1 %2', ItemJournalLine."Br. Cust. Discount Code", InventoryPostGrpRec."Branch Prod. Discount Code")
    end;


    [EventSubscriber(ObjectType::Table, 17, 'OnAfterCopyGLEntryFromGenJnlLine', '', false, false)]
    local procedure OnAfterCopyGLEntryFromGenJnl(var GLEntry: Record "G/L Entry"; var GenJournalLine: Record "Gen. Journal Line")
    var
        WHTLedgEntry: Record "WHT Buffer";
        AmtLVar: Decimal;
        AmtLVarLCY: Decimal;
        BankAcc: Record "Bank Account";
        CapBudget: Record "Budget Line";
        PuInvLne: Record "Purch. Inv. Line";
        Vendor: Record Vendor;
        FixedAsset: Record "Fixed Asset";
        Customer: Record Customer;
        BankAccount: Record "Bank Account";
        DimMgt: Codeunit DimensionManagement;
        ShortCutDimCode: array[8] of Code[20];
    begin
        //Baluonsep8>>
        if GLEntry."Document Type" = GLEntry."Document Type"::"Credit Memo" then begin
            GLEntry.Narration := GenJournalLine."Printable Comment 1";
            GLEntry.Narration1 := GenJournalLine."Printable Comment 2";
            GLEntry."Description 2" := GenJournalLine."Description 2"; //RFC #22
        end;
        //Baluonsep8<<
        //Feb182021>>
        IF GenJournalLine."Capex No." <> '' THEN BEGIN
            CapBudget.RESET;
            CapBudget.SetRange("Document No.", GenJournalLine."Capex No.");
            CapBudget.SetRange("Line No.", GenJournalLine."Capex Line No.");
            IF CapBudget.findfirst then;


            GLEntry."Capex No." := GenJournalLine."Capex No.";
            GLEntry."Capex Line No." := GenJournalLine."Capex Line No.";
            GLEntry."Budget Name" := CapBudget."Budget Name";
        end else begin
            PuInvLne.reset;
            PuInvLne.SetRange("Document No.", GenJournalLine."Document No.");
            IF PuInvLne.FindFirst() then begin

                CapBudget.RESET;
                CapBudget.SetRange("Document No.", PuInvLne."Capex No.");
                CapBudget.SetRange("Line No.", PuInvLne."Capex Line No.");
                IF CapBudget.findfirst then;

                GLEntry."Capex No." := PuInvLne."Capex No.";
                GLEntry."Capex Line No." := PuInvLne."Capex Line No.";
                GLEntry."Budget Name" := CapBudget."Budget Name";
            end;
        end;
        //Feb182021<<
        if GLEntry."Document Type" <> GLEntry."Document Type"::"Credit Memo" then begin  //RFC #22
            GLEntry.Narration := GenJournalLine.Narration;
            GLEntry.Narration1 := GenJournalLine.Narration1;
            GLEntry."Description 2" := GenJournalLine."Description 2"; //RFC #22
        end;//RFC 22
        GLEntry."Cheque No." := GenJournalLine."Cheque No.";
        GLEntry."Cheque Date" := GenJournalLine."Cheque Date";
        GLEntry."Old Document No." := GenJournalLine."Voucher No.";
        GLEntry."Provision Entry" := GenJournalLine."Provision Entry"; //PROV1.0
                                                                       //CWIP>>
        GLEntry."CWIP No." := GenJournalLine."CWIP No.";
        //CWIP<<
        GLEntry."Allocation Maintenance Code" := GenJournalLine."Maintenance Code";
        //FIX08Jun2021>>
        DimMgt.GetShortcutDimensions(GenJournalLine."Dimension Set ID", ShortCutDimCode);
        GLEntry."ShortCut Dimension code 3" := ShortCutDimCode[3];
        GLEntry."ShortCut Dimension code 4" := ShortCutDimCode[4];
        GLEntry."ShortCut Dimension code 5" := ShortCutDimCode[5];
        GLEntry."ShortCut Dimension code 6" := ShortCutDimCode[6];
        GLEntry."ShortCut Dimension code 7" := ShortCutDimCode[7];
        GLEntry."ShortCut Dimension code 8" := ShortCutDimCode[8];
        //BaluonAug
        GLEntry."Fixed Asset No." := GenJournalLine."Fixed Asset No.";
        GLEntry."FA Posting Group" := GenJournalLine."FA Posting Group";
        //FIX08Jun2021<<
        //Feb162021<<
        AmtLVar := ABS(GenJournalLine.Amount);
        AmtLVarLCY := ABS(GenJournalLine."Amount (LCY)");
        //GLEntry."Capex No." := GenJournalLine."Capex No.";
        //GLEntry."Capex Line No." := GenJournalLine."Capex Line No.";

        GLEntry."Description 2" := GenJournalLine."Description 2";
        GLEntry."Voucher Type" := GenJournalLine."Voucher type";
        GLEntry."Teller / Cheque No." := GenJournalLine."Teller / Cheque No.";
        GLEntry."Teller / Cheque Date" := GenJournalLine."Teller / Cheque Date";
        GLEntry."CWIP No." := GenJournalLine."CWIP No.";//CWIP;
        GLEntry."Loan ID" := GenJournalLine."Loan ID";//PKON22M6-CR220060
        //FIX02June2021>>
        if GLEntry."Source Type" = GLEntry."Source Type"::"Fixed Asset" then begin
            If FixedAsset.Get(GLEntry."Source No.") then
                GLEntry."Source Description" := FixedAsset.Description + FixedAsset."Description 2";
        end;
        if GLEntry."Source Type" = GLEntry."Source Type"::Vendor then begin
            If Vendor.Get(GLEntry."Source No.") then
                GLEntry."Source Description" := Vendor.Name + Vendor."Name 2";
        end;
        if GLEntry."Source Type" = GLEntry."Source Type"::Customer then begin
            if Customer.Get(GLEntry."Source No.") then
                GLEntry."Source Description" := Customer.Name + Customer."Name 2";
        end;
        if GLEntry."Source Type" = GLEntry."Source Type"::"Bank Account" then begin
            if BankAccount.Get(GLEntry."Source No.") then
                GLEntry."Source Description" := BankAccount.Name + BankAccount."Name 2";
        end;
        //FIX02June2021<<
        //FIX29MAY2021
        /*
        WHTLedgEntry.Reset();
        WHTLedgEntry.SetRange("Applies-to ID", GenJournalLine."Voucher No.");
        IF WHTLedgEntry.FindSet() then
            repeat
                IF WHTLedgEntry."Remaining Amount" > 0 THEN BEGIN
                    //Message('WHT-%1', WHTLedgEntry."WHT Amount");
                    IF AmtLVar > 0 then begin
                        IF AmtLVar > WHTLedgEntry."Remaining Amount" then begin
                            AmtLVar := AmtLVar - WHTLedgEntry."Remaining Amount";
                            AmtLVarLCY := AmtLVarLCY - WHTLedgEntry."Remaining Amount(LCY)";
                            WHTLedgEntry."Remaining Amount" := 0;
                            WHTLedgEntry."Remaining Amount(LCY)" := 0;
                            WHTLedgEntry."App. Voucher No" := GenJournalLine."Voucher No.";
                            WHTLedgEntry."Posted App. Voucher No" := GenJournalLine."Document No.";
                        end ELSE begin
                            WHTLedgEntry."Remaining Amount" -= AmtLVar;
                            WHTLedgEntry."Remaining Amount(LCY)" -= AmtLVarLCY;
                            WHTLedgEntry."App. Voucher No" := GenJournalLine."Voucher No.";
                            WHTLedgEntry."Posted App. Voucher No" := GenJournalLine."Document No.";
                            AmtLVar := 0;
                            AmtLVarLCY := 0;
                        end;
                        WHTLedgEntry.Modify();
                    end;
                end else
                    IF WHTLedgEntry."Remaining Amount" < 0 THEN BEGIN
                        //Message('WHT-%1', WHTLedgEntry."WHT Amount");
                        IF AmtLVar > 0 then begin
                            IF AmtLVar > WHTLedgEntry."Remaining Amount" then begin
                                AmtLVar := AmtLVar + WHTLedgEntry."Remaining Amount";
                                AmtLVarLCY := AmtLVarLCY + WHTLedgEntry."Remaining Amount(LCY)";
                                WHTLedgEntry."Remaining Amount" := 0;
                                WHTLedgEntry."Remaining Amount(LCY)" := 0;
                                WHTLedgEntry."App. Voucher No" := GenJournalLine."Voucher No.";
                                WHTLedgEntry."Posted App. Voucher No" := GenJournalLine."Document No.";
                            end ELSE begin
                                WHTLedgEntry."Remaining Amount" += AmtLVar;
                                WHTLedgEntry."Remaining Amount(LCY)" += AmtLVarLCY;
                                WHTLedgEntry."App. Voucher No" := GenJournalLine."Voucher No.";
                                WHTLedgEntry."Posted App. Voucher No" := GenJournalLine."Document No.";
                                AmtLVar := 0;
                                AmtLVarLCY := 0;
                            end;
                            WHTLedgEntry.Modify();
                        end;
                    end
            until WHTLedgEntry.Next = 0;*///FIX29MAY2021<<
    end;
    //Prov1.1 >>

    /*[EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnBeforePostPurchaseDoc', '', false, false)]
    procedure OnBeforePostPurchaseDoc(var PurchaseHeader: Record "Purchase Header"; PreviewMode: Boolean; CommitIsSupressed: Boolean; var HideProgressWindow: Boolean)
    var
        PurchLn: Record "Purchase Line";
        GLAcc: Record "G/L Account";
        ItmCharg: Record "Item Charge";
        InvPostingSetup: Record "Inventory Posting Setup";
        GenPostingSetup: Record "General Posting Setup";
        Cape: Record "Budget Line";
        CapBudValue: Decimal;
        PurValue: Decimal;
        PrevCap: Code[20];
    BEGIN
        PurchLn.RESET;
        PurchLn.SetRange("Document No.", PurchaseHeader."No.");
        PurchLn.Setfilter("Qty. to Receive", '<>%1', 0);
        //PurchLn.SetRange(Type,PurchLn.type::"G/L Account");
        IF PurchLn.findset then
            repeat
                IF (PurchLn.Type = PurchLn.Type::"Charge (Item)") OR (PurchLn.Type = PurchLn.Type::"G/L Account") THEN BEGIN
                    IF (PurchLn.Type = PurchLn.Type::"G/L Account") then BEGIN
                        IF GLAcc.GET(PurchLn."No.") THEN
                            IF InvPostingSetup.get(PurchLn."Location Code", GLAcc."Inventory Posting Setup") THEN;
                    end;
                    IF (PurchLn.Type = PurchLn.Type::"Charge (Item)") then
                        IF ItmCharg.GET(PurchLn."No.") THEN
                            IF InvPostingSetup.get(PurchLn."Location Code", ItmCharg."Inventory Posting Setup") THEN;
                    IF GenPostingSetup.get(PurchLn."Gen. Bus. Posting Group", PurchLn."Gen. Prod. Posting Group") THEN;
                    InvPostingSetup.TestField("GL Services (Interim)");
                    GenPostingSetup.TestField("GL Ser Accural (Interim)");
                END;
            until PurchLn.next = 0;
    END;*/
    //Prov1.1 <<





    //fix28jUN2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnBeforePostPurchaseDoc', '', false, false)]
    local procedure OnBeforePostPurchaseDoc(var PurchaseHeader: Record "Purchase Header"; PreviewMode: Boolean; CommitIsSupressed: Boolean; var HideProgressWindow: Boolean)
    var
        PurchaseLine: Record "Purchase Line";
    begin
        PurchaseHeader.TestField("Shortcut Dimension 2 Code");
        PurchaseHeader.TestField("Shortcut Dimension 1 Code");
        PurchaseLine.Reset();
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetFilter("No.", '<>%1', '');
        if PurchaseLine.FindSet() then
            repeat
                PurchaseLine.TestField("Shortcut Dimension 2 Code");
                PurchaseLine.TestField("Shortcut Dimension 1 Code");
                if (PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset") and (PurchaseLine."FA Posting Type" = PurchaseLine."FA Posting Type"::"Acquisition Cost") then begin
                    PurchaseLine.TestField("Capex No.");
                    PurchaseLine.TestField("Capex Line No.");
                end;
                if PurchaseLine."WHT Applicable" then
                    if PurchaseLine."WHT Amount" = 0 then
                        Error('WHT amount must have value');
            until PurchaseLine.Next() = 0;
    end;
    //fix28jUN2021<<
    //PROV1.0 >>
    //[EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterPostPurchaseDoc', '', false, false)]
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterFinalizePostingOnBeforeCommit', '', false, false)]
    local procedure OnAfterFinalizePostingOnBeforeCommit(var PurchHeader: Record "Purchase Header"; var PurchRcptHeader: Record "Purch. Rcpt. Header"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var ReturnShptHeader: Record "Return Shipment Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PreviewMode: Boolean; CommitIsSupressed: Boolean)

    var
        GenJnlLine: Record "Gen. Journal Line";
        GenJnlLine2: Record "Gen. Journal Line";
        LastGenJnlLine: Record "Gen. Journal Line";
        SourceCodeSetup: Record "Source Code Setup";
        //PurchaseLine: Record "Purchase Line";//PKONDE31.2
        PurchaseLine: Record "Purch. Rcpt. Line";//PKONDE31.2
        InvPostingSetup: Record "Inventory Posting Setup";
        GenPostingSetup: Record "General Posting Setup";
        ItemCharge: Record "Item Charge";
        FACrd: Record "Fixed Asset";
        GLAcc: Record "G/L Account";
        FAPostingGr: Record "FA Posting Group";
        LineNo: Integer;
        Text001: Label '%1 %2 on %3';
    begin
        if (PurchRcptHeader."No." <> '') and (PurchInvHeader."No." <> '') then
            exit;
        IF PurchRcptHeader."No." <> '' then begin
            if not (PurchHeader."Document Type" IN [PurchHeader."Document Type"::Order]) then
                exit;
            LastGenJnlLine.Reset();
            LastGenJnlLine.SetRange("Journal Template Name", 'GENERAL');
            LastGenJnlLine.SetRange("Journal Batch Name", 'DEFAULT');
            if LastGenJnlLine.FindLast() then
                LineNo := LastGenJnlLine."Line No." + 10000
            else
                LineNo := 10000;
            PurchaseLine.reset;
            //PurchaseLine.SetRange("Document Type", PurchHeader."Document Type");//PKONDE31.2
            //PurchaseLine.SetRange("Document No.", PurchHeader."No.");PKONDE31.2
            PurchaseLine.SetRange("Document No.", PurchRcptHeader."No.");//PKONDE31.2
            //PurchaseLine.SetFilter(Type, '%1|%2|%3', PurchaseLine.Type::"Charge (Item)", PurchaseLine.Type::"G/L Account", PurchaseLine.Type::"Fixed Asset");
            PurchaseLine.SetFilter(Type, '%1|%2', PurchaseLine.Type::"G/L Account", PurchaseLine.Type::"Fixed Asset");
            //PurchaseLine.SetFilter("Quantity Received", '<>%1', 0);//PKONDE31.2
            PurchaseLine.SetFilter(Quantity, '<>%1', 0);//PKONDE31.2
            if PurchaseLine.FindSet() then
                repeat
                    case PurchaseLine.Type of
                        PurchaseLine.Type::"Charge (Item)":
                            begin
                                ItemCharge.get(PurchaseLine."No.");
                                InvPostingSetup.get(PurchaseLine."Location Code", ItemCharge."Inventory Posting Setup");
                                GenPostingSetup.get(PurchaseLine."Gen. Bus. Posting Group", PurchaseLine."Gen. Prod. Posting Group");
                                InvPostingSetup.TestField("Item Charge (Interim)");
                                GenPostingSetup.TestField("Item Cha Accural (Interim)");
                            end;
                        PurchaseLine.Type::"G/L Account":
                            begin
                                GLAcc.get(PurchaseLine."No.");
                                InvPostingSetup.get(PurchaseLine."Location Code", GLAcc."Inventory Posting Setup");
                                GenPostingSetup.get(PurchaseLine."Gen. Bus. Posting Group", PurchaseLine."Gen. Prod. Posting Group");
                                InvPostingSetup.TestField("GL Services (Interim)");
                                GenPostingSetup.TestField("GL Ser Accural (Interim)");
                            end;
                        PurchaseLine.Type::"Fixed Asset":
                            begin
                                //FACrd.get(PurchaseLine."No.");
                                FAPostingGr.Get(PurchaseLine."Posting Group");
                                FAPostingGr.TestField("GRN (Interim) Acct");
                                FAPostingGr.TestField("GRN Adj. Accural Ac. (Interim)");
                            end;
                    end;

                    //Debit Entry
                    GenJnlLine.INIT;
                    GenJnlLine."Journal Template Name" := 'GENERAL';
                    GenJnlLine."Journal Batch Name" := 'DEFAULT';
                    GenJnlLine."Line No." := LineNo;
                    GenJnlLine."Document No." := PurchRcptHeader."No.";
                    //GenJnlLine."External Document No." := PurchaseLine."Document No.";//PKONDE31.2
                    GenJnlLine."External Document No." := PurchaseLine."Order No.";//PKONDE31.2
                    GenJnlLine."Source Code" := SourceCodeSetup."Inventory Post Cost";
                    GenJnlLine."System-Created Entry" := TRUE;
                    GenJnlLine."Job No." := PurchaseLine."Job No.";
                    GenJnlLine."Currency Code" := PurchaseLine."Currency Code";
                    GenJnlLine.VALIDATE("Posting Date", PurchHeader."Posting Date");
                    case PurchaseLine.Type of
                        PurchaseLine.Type::"Charge (Item)":
                            GenJnlLine."Account No." := InvPostingSetup."Item Charge (Interim)";
                        PurchaseLine.Type::"G/L Account":
                            GenJnlLine."Account No." := InvPostingSetup."GL Services (Interim)";
                        PurchaseLine.Type::"Fixed Asset":
                            GenJnlLine."Account No." := FAPostingGr."GRN Adj. Accural Ac. (Interim)";
                    end;
                    GenJnlLine."Provision Entry" := true;
                    //GenJnlLine."Dimension Set ID" := PurchaseLine."Dimension Set ID";//PKONDE14
                    GenJnlLine.VALIDATE(Amount, PurchaseLine.Quantity * PurchaseLine."Unit Cost");//PKONDE31.2
                    IF PurchaseLine.Type <> PurchaseLine.Type::"Fixed Asset" then
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'Direct Cost', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                    IF PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset" then begin
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'GRN Provision', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                        //BaluOnAug
                        GenJnlLine."Fixed Asset No." := PurchaseLine."No.";
                        GenJnlLine."FA Posting Group" := PurchaseLine."Posting Group";
                    end;
                    GenJnlLine."Shortcut Dimension 1 Code" := PurchaseLine."Shortcut Dimension 1 Code";//PKONDE14
                    GenJnlLine."Shortcut Dimension 2 Code" := PurchaseLine."Shortcut Dimension 2 Code";//PKONDE14
                    GenJnlLine."Dimension Set ID" := PurchaseLine."Dimension Set ID";//PKONDE14
                    GenJnlLine.Insert();
                    LineNo += 10000;

                    //Credit Entry
                    GenJnlLine.INIT;
                    GenJnlLine."Journal Template Name" := 'GENERAL';
                    GenJnlLine."Journal Batch Name" := 'DEFAULT';
                    GenJnlLine."Line No." := LineNo;
                    GenJnlLine."Document No." := PurchRcptHeader."No.";
                    //GenJnlLine."External Document No." := PurchaseLine."Document No.";//PKONDE31.2
                    GenJnlLine."External Document No." := PurchaseLine."Order No.";//PKONDE31.2

                    GenJnlLine."Source Code" := SourceCodeSetup."Inventory Post Cost";
                    GenJnlLine."System-Created Entry" := TRUE;
                    GenJnlLine."Job No." := PurchaseLine."Job No.";
                    GenJnlLine."Currency Code" := PurchaseLine."Currency Code";
                    GenJnlLine.VALIDATE("Posting Date", PurchHeader."Posting Date");
                    case PurchaseLine.Type of
                        PurchaseLine.Type::"Charge (Item)":
                            GenJnlLine."Account No." := GenPostingSetup."Item Cha Accural (Interim)";
                        PurchaseLine.Type::"G/L Account":
                            GenJnlLine."Account No." := GenPostingSetup."GL Ser Accural (Interim)";
                        PurchaseLine.Type::"Fixed Asset":
                            GenJnlLine."Account No." := FAPostingGr."GRN (Interim) Acct";
                    end;
                    GenJnlLine."Provision Entry" := true;
                    //GenJnlLine."Dimension Set ID" := PurchaseLine."Dimension Set ID";//PKONDE14
                    GenJnlLine.VALIDATE(Amount, -(PurchaseLine.Quantity * PurchaseLine."Unit Cost"));//PKONDE31.2
                    IF PurchaseLine.Type <> PurchaseLine.Type::"Fixed Asset" then
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'Direct Cost', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                    IF PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset" then begin
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'GRN Provision', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                        //BaluOnAug
                        GenJnlLine."Fixed Asset No." := PurchaseLine."No.";
                        GenJnlLine."FA Posting Group" := PurchaseLine."Posting Group";
                    end;
                    GenJnlLine."Shortcut Dimension 1 Code" := PurchaseLine."Shortcut Dimension 1 Code";//PKONDE14
                    GenJnlLine."Shortcut Dimension 2 Code" := PurchaseLine."Shortcut Dimension 2 Code";//PKONDE14
                    GenJnlLine."Dimension Set ID" := PurchaseLine."Dimension Set ID";//PKONDE14

                    GenJnlLine.Insert();
                    LineNo += 10000;
                until PurchaseLine.Next() = 0;
            GenJnlLine.Reset();
            GenJnlLine.Setrange("Journal Template Name", 'GENERAL');
            GenJnlLine.Setrange("Journal Batch Name", 'DEFAULT');
            GenJnlLine.SetRange("Document No.", PurchRcptHeader."No.");
            if GenJnlLine.FindSet() then
                repeat
                    GenJnlLine2 := GenJnlLine;
                    GenJnlPostLine.RunWithCheck(GenJnlLine2);
                until GenJnlLine.Next() = 0;
        end else
            if PurchInvHeader."No." <> '' then
                ReverseProvisionlEntries(PurchHeader, PurchInvHeader."No.");

        /*  //PKONDE31.2
        if (PurchRcptHeader."No." <> '') and (PurchInvHeader."No." <> '') then
            exit;
        IF PurchRcptHeader."No." <> '' then begin
            if not (PurchHeader."Document Type" IN [PurchHeader."Document Type"::Order]) then
                exit;
            LastGenJnlLine.Reset();
            LastGenJnlLine.SetRange("Journal Template Name", 'GENERAL');
            LastGenJnlLine.SetRange("Journal Batch Name", 'DEFAULT');
            if LastGenJnlLine.FindLast() then
                LineNo := LastGenJnlLine."Line No." + 10000
            else
                LineNo := 10000;
            PurchaseLine.reset;
            PurchaseLine.SetRange("Document Type", PurchHeader."Document Type");
            PurchaseLine.SetRange("Document No.", PurchHeader."No.");
            //PurchaseLine.SetFilter(Type, '%1|%2|%3', PurchaseLine.Type::"Charge (Item)", PurchaseLine.Type::"G/L Account", PurchaseLine.Type::"Fixed Asset");
            PurchaseLine.SetFilter(Type, '%1|%2', PurchaseLine.Type::"G/L Account", PurchaseLine.Type::"Fixed Asset");
            PurchaseLine.SetFilter("Quantity Received", '<>%1', 0);
            if PurchaseLine.FindSet() then
                repeat
                    case PurchaseLine.Type of
                        PurchaseLine.Type::"Charge (Item)":
                            begin
                                ItemCharge.get(PurchaseLine."No.");
                                InvPostingSetup.get(PurchaseLine."Location Code", ItemCharge."Inventory Posting Setup");
                                GenPostingSetup.get(PurchaseLine."Gen. Bus. Posting Group", PurchaseLine."Gen. Prod. Posting Group");
                                InvPostingSetup.TestField("Item Charge (Interim)");
                                GenPostingSetup.TestField("Item Cha Accural (Interim)");
                            end;
                        PurchaseLine.Type::"G/L Account":
                            begin
                                GLAcc.get(PurchaseLine."No.");
                                InvPostingSetup.get(PurchaseLine."Location Code", GLAcc."Inventory Posting Setup");
                                GenPostingSetup.get(PurchaseLine."Gen. Bus. Posting Group", PurchaseLine."Gen. Prod. Posting Group");
                                InvPostingSetup.TestField("GL Services (Interim)");
                                GenPostingSetup.TestField("GL Ser Accural (Interim)");
                            end;
                        PurchaseLine.Type::"Fixed Asset":
                            begin
                                //FACrd.get(PurchaseLine."No.");
                                FAPostingGr.Get(PurchaseLine."Posting Group");
                                FAPostingGr.TestField("GRN (Interim) Acct");
                                FAPostingGr.TestField("GRN Adj. Accural Ac. (Interim)");
                            end;
                    end;

                    //Debit Entry
                    GenJnlLine.INIT;
                    GenJnlLine."Journal Template Name" := 'GENERAL';
                    GenJnlLine."Journal Batch Name" := 'DEFAULT';
                    GenJnlLine."Line No." := LineNo;
                    GenJnlLine."Document No." := PurchRcptHeader."No.";
                    GenJnlLine."External Document No." := PurchaseLine."Document No.";
                    GenJnlLine."Source Code" := SourceCodeSetup."Inventory Post Cost";
                    GenJnlLine."System-Created Entry" := TRUE;
                    GenJnlLine."Job No." := PurchaseLine."Job No.";
                    GenJnlLine."Currency Code" := PurchaseLine."Currency Code";
                    GenJnlLine.VALIDATE("Posting Date", PurchHeader."Posting Date");
                    case PurchaseLine.Type of
                        PurchaseLine.Type::"Charge (Item)":
                            GenJnlLine."Account No." := InvPostingSetup."Item Charge (Interim)";
                        PurchaseLine.Type::"G/L Account":
                            GenJnlLine."Account No." := InvPostingSetup."GL Services (Interim)";
                        PurchaseLine.Type::"Fixed Asset":
                            GenJnlLine."Account No." := FAPostingGr."GRN Adj. Accural Ac. (Interim)";
                    end;
                    GenJnlLine."Provision Entry" := true;
                    //GenJnlLine."Dimension Set ID" := PurchaseLine."Dimension Set ID"; //PKONDE14
                    GenJnlLine.VALIDATE(Amount, PurchaseLine."Quantity Received" * PurchaseLine."Unit Cost");
                    IF PurchaseLine.Type <> PurchaseLine.Type::"Fixed Asset" then
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'Direct Cost', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                    IF PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset" then begin
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'GRN Provision', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                        //BaluOnAug
                        GenJnlLine."Fixed Asset No." := PurchaseLine."No.";
                        GenJnlLine."FA Posting Group" := PurchaseLine."Posting Group";
                    end;
                    GenJnlLine."Shortcut Dimension 1 Code" := PurchaseLine."Shortcut Dimension 1 Code"; //PKONDE14
                    GenJnlLine."Shortcut Dimension 2 Code" := PurchaseLine."Shortcut Dimension 2 Code"; //PKONDE14
                    GenJnlLine."Dimension Set ID" := purchaseline."Dimension Set ID"; //PKONDE14
                    GenJnlLine.Insert();
                    LineNo += 10000;

                    //Credit Entry
                    GenJnlLine.INIT;
                    GenJnlLine."Journal Template Name" := 'GENERAL';
                    GenJnlLine."Journal Batch Name" := 'DEFAULT';
                    GenJnlLine."Line No." := LineNo;
                    GenJnlLine."Document No." := PurchRcptHeader."No.";
                    GenJnlLine."External Document No." := PurchaseLine."Document No.";
                    GenJnlLine."Source Code" := SourceCodeSetup."Inventory Post Cost";
                    GenJnlLine."System-Created Entry" := TRUE;
                    GenJnlLine."Job No." := PurchaseLine."Job No.";
                    GenJnlLine."Currency Code" := PurchaseLine."Currency Code";
                    GenJnlLine.VALIDATE("Posting Date", PurchHeader."Posting Date");
                    case PurchaseLine.Type of
                        PurchaseLine.Type::"Charge (Item)":
                            GenJnlLine."Account No." := GenPostingSetup."Item Cha Accural (Interim)";
                        PurchaseLine.Type::"G/L Account":
                            GenJnlLine."Account No." := GenPostingSetup."GL Ser Accural (Interim)";
                        PurchaseLine.Type::"Fixed Asset":
                            GenJnlLine."Account No." := FAPostingGr."GRN (Interim) Acct";
                    end;
                    GenJnlLine."Provision Entry" := true;
                    //GenJnlLine."Dimension Set ID" := PurchaseLine."Dimension Set ID";//PKONDE14
                    GenJnlLine.VALIDATE(Amount, -(PurchaseLine."Quantity Received" * PurchaseLine."Unit Cost"));
                    IF PurchaseLine.Type <> PurchaseLine.Type::"Fixed Asset" then
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'Direct Cost', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                    IF PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset" then begin
                        GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'GRN Provision', PurchHeader."Buy-from Vendor No.", GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                        //BaluOnAug
                        GenJnlLine."Fixed Asset No." := PurchaseLine."No.";
                        GenJnlLine."FA Posting Group" := PurchaseLine."Posting Group";
                    end;
                    GenJnlLine."Shortcut Dimension 1 Code" := PurchaseLine."Shortcut Dimension 1 Code";//PKONDE14
                    GenJnlLine."Shortcut Dimension 2 Code" := PurchaseLine."Shortcut Dimension 2 Code";//PKONDE14
                    GenJnlLine."Dimension Set ID" := purchaseline."Dimension Set ID";//PKONDE14
                    GenJnlLine.Insert();
                    LineNo += 10000;
                until PurchaseLine.Next() = 0;
            GenJnlLine.Reset();
            GenJnlLine.Setrange("Journal Template Name", 'GENERAL');
            GenJnlLine.Setrange("Journal Batch Name", 'DEFAULT');
            GenJnlLine.SetRange("Document No.", PurchRcptHeader."No.");
            if GenJnlLine.FindSet() then
                repeat
                    GenJnlLine2 := GenJnlLine;
                    GenJnlPostLine.RunWithCheck(GenJnlLine2);
                until GenJnlLine.Next() = 0;
        end else
            if PurchInvHeader."No." <> '' then
                ReverseProvisionlEntries(PurchHeader, PurchInvHeader."No.");
*///PKONDE31.2
    end;

    local procedure ReverseProvisionlEntries(PurchaseHeader: Record "Purchase Header"; PurchInvHdrNo: Code[20])
    var
        GenJnlLine: Record "Gen. Journal Line";
        GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
        Text001: Label '%1 %2 on %3';
        GLEntry: Record "G/L Entry";
        PurchRcptHdr: Record "Purch. Rcpt. Header";
        PurcLine: record "Purchase Line";
        PurcInvLine: record "Purch. Inv. Line";
    begin
        //PKONDE31>>
        PurcInvLine.Reset();
        PurcInvLine.SetRange("Document No.", PurchInvHdrNo);
        PurcInvLine.SetFilter(Quantity, '<>%1', 0);
        IF PurcInvLine.FINDSET then
            repeat
                //PKONDE31<<
                GLEntry.Reset();
                GLEntry.SetRange("External Document No.", PurchaseHeader."No.");
                GLEntry.SetRange("Provision Entry", true);
                //PKONDE31>>
                If PurcInvLine.Type = PurcInvLine.Type::"Fixed Asset" THEN
                    GLEntry.SetRange("Fixed Asset No.", PurcInvLine."No.");
                //PKONDE31<<
                If GLEntry.FindSet() then
                    repeat
                        GLEntry.CalcFields("Provisional Entry Reversed");
                        IF NOT GLEntry."Provisional Entry Reversed" THEN BEGIN
                            GenJnlLine.INIT;
                            GenJnlLine."Document No." := PurchInvHdrNo;
                            GenJnlLine."External Document No." := PurchaseHeader."No.";
                            GenJnlLine."Source Code" := GLEntry."Source Code";
                            GenJnlLine."System-Created Entry" := TRUE;
                            GenJnlLine."Job No." := GLEntry."Job No.";
                            GenJnlLine."Currency Code" := PurchaseHeader."Currency Code";
                            GenJnlLine.VALIDATE("Posting Date", PurchaseHeader."Posting Date");
                            GenJnlLine."Account No." := GLEntry."G/L Account No.";
                            //GenJnlLine."Dimension Set ID" := GLEntry."Dimension Set ID"; //PKONDE14
                            GenJnlLine.VALIDATE(Amount, -(GLEntry.Amount));
                            GenJnlLine."Reversal For Prov Entry No." := GLEntry."Entry No.";
                            //GenJnlLine."Applies-to ID" := format(GLEntry."Entry No.");                
                            GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'Direct Cost', PurchaseHeader."Buy-from Vendor No.",
                            GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                            //'GRN Provision'
                            PurcLine.Reset();
                            PurcLine.SetRange("Document Type", PurchaseHeader."Document Type");
                            PurcLine.SetRange("Document No.", PurchaseHeader."No.");
                            PurcLine.SetRange(Type, PurcLine.Type::"Fixed Asset");
                            IF PurcLine.FindFirst() then
                                GenJnlLine.Description := COPYSTR(STRSUBSTNO(Text001, 'GRN Provision', PurchaseHeader."Buy-from Vendor No.",
                                GenJnlLine."Posting Date"), 1, MAXSTRLEN(GenJnlLine.Description));
                            GenJnlLine."Shortcut Dimension 1 Code" := GLEntry."Global Dimension 1 Code";//PKONDE14
                            GenJnlLine."Shortcut Dimension 2 Code" := GLEntry."Global Dimension 2 Code";//PKONDE14
                            GenJnlLine."Dimension Set ID" := GLEntry."Dimension Set ID";//PKONDE14
                            GenJnlPostLine.RunWithCheck(GenJnlLine);
                            //GLEntry."Provisional Entry reversed" := true;//PKONJ9 //PKON22AP19
                            //GLEntry.Modify(); //PKONJ9  //PKON22AP19
                        end;
                    until GLEntry.Next() = 0;
            until PurcInvLine.Next = 0;//PKONDE31
    end;
    //PROV1.0 <<
    [EventSubscriber(ObjectType::Codeunit, 5764, 'OnAfterConfirmPost', '', false, false)]
    local procedure CheckInvPostWareShp(WhseShipmentLine: Record "Warehouse Shipment Line"; Invoice: Boolean)
    begin
        IF Invoice then
            error('You can not select invoice Option.')
    end;
    //b2bpksalecorr10
    [EventSubscriber(ObjectType::Codeunit, 5765, 'OnAfterConfirmPost', '', false, false)]
    local procedure CheckInvPostWareShp2(WhseShipmentLine: Record "Warehouse Shipment Line"; Invoice: Boolean)
    begin
        IF Invoice then
            error('You can not select invoice Option.')
    end;

    /*     [EventSubscriber(ObjectType::Codeunit, 5601, 'OnBeforeFillAllocationBuffer', '', false, false)]
        local procedure OnBeforeFillAllocationBuffer(var TempFAGLPostingBuffer: Record "FA G/L Posting Buffer" temporary; var NextEntryNo: Integer; var GLEntryNo: Integer; var NumberOfEntries: Integer; var OrgGenJnlLine: Boolean; var NetDisp: Boolean; GLAccNo: Code[20]; FAPostingType: Option Acquisition,Depr,WriteDown,Appr,Custom1,Custom2,Disposal,Maintenance,Gain,Loss,"Book Value Gain","Book Value Loss"; AllocAmount: Decimal; DeprBookCode: Code[10]; PostingGrCode: Code[20]; GlobalDim1Code: Code[20]; GlobalDim2Code: Code[20]; DimSetID: Integer; AutomaticEntry: Boolean; Correction: Boolean; var IsHandled: Boolean)
        var
            FAPost: Record "FA Posting Group";
        begin
            FAPost.Reset();
            FAPost.SetRange(Code, PostingGrCode);
            if FAPost.FindFirst() then begin
                FAPost.TestField("Capital Work in Progress");
                GLAccNo := FAPost."Capital Work in Progress";
            end;
        end; */ //PK::WIP Need to Check

    /*     [EventSubscriber(ObjectType::Codeunit, 5602, 'OnAfterGetAccNo', '', false, false)]
        local procedure OnAfterGetAccNo(var FALedgEntry: Record "FA Ledger Entry"; var GLAccNo: Code[20])
        var
            FAPostingGr: record "FA Posting Group";
        begin
            FAPostingGr.Reset();
            FAPostingGr.SetRange(Code, FALedgEntry."FA Posting Group");
            if FAPostingGr.FindFirst() then
                FAPostingGr.TestField("Capital Work in Progress");
            GLAccNo := FAPostingGr."Capital Work in Progress";
        end; */ //PK::WIP Need to Check

    [EventSubscriber(ObjectType::Table, 7317, 'OnAfterInsertEvent', '', false, false)]
    local procedure UpdateClearandImp(VAR Rec: Record "Warehouse Receipt Line"; RunTrigger: Boolean)
    var
        PurchHdr: Record "Purchase Header";
        WareHouse: Record "Warehouse Receipt Header";
    begin

        if Rec."Source Document" = Rec."Source Document"::"Purchase Order" then begin
            PurchHdr.Reset();
            PurchHdr.SetRange("Document Type", PurchHdr."Document Type"::Order);
            PurchHdr.SetRange("No.", Rec."Source No.");
            if PurchHdr.FindFirst() then begin
                WareHouse.RESET;
                WareHouse.SETRANGE("No.", Rec."No.");
                IF WareHouse.findfirst then begin
                    WareHouse."Clearing File No." := PurchHdr."Clearing File No.";
                    WareHouse."Import File No." := PurchHdr."Import File No.";
                    WareHouse.Modify(true);
                end;

            end
        end;
    end;

    procedure GetDocumentValues(PurchLine: Record "Purchase Line"): Integer
    begin
        //PK
        IF PurchLine."Document Type" = 0 then
            Exit(0)
        ELSE
            IF PurchLine."Document Type" = 1 then
                Exit(1)
            ELSE
                IF PurchLine."Document Type" = 2 then
                    Exit(2)
                ELSE
                    IF PurchLine."Document Type" = 3 then
                        Exit(3)
                    ELSE
                        IF PurchLine."Document Type" = 4 then
                            Exit(4)
                        ELSE
                            IF PurchLine."Document Type" = 5 then
                                Exit(5)

    end;
    //CWIP>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnAfterInitGLEntry', '', false, false)]
    local procedure OnAfterInitGLEntry(GLEntry: Record "G/L Entry"; GenJournalLine: record "Gen. Journal Line")
    begin
        if GLEntry."CWIP No." <> '' then
            InitCwipLedgerEntry(GLEntry);
    end;

    local procedure InitCwipLedgerEntry(GlEntry: Record "G/L Entry")
    var
        CwipLedgerEntry: Record "CWIP Ledger Entries";
        EntryNo: Integer;
        CWIPMaster: Record "CWIP Masters";
        Xyz: Codeunit 12;
        PurchInvoiceHeader: Record "Purch. Inv. Header";
    begin
        CWIPMaster.Get(GlEntry."CWIP No.");
        if CWIPMaster."GL Account No." <> GlEntry."G/L Account No." then
            exit;
        if PurchInvoiceHeader.Get(GlEntry."Document No.") then
            exit;
        if CwipLedgerEntry.FindLast() then
            EntryNo := CwipLedgerEntry."Entry No." + 1
        else
            EntryNo := 1;
        CwipLedgerEntry.Init();
        CwipLedgerEntry."Entry No." := EntryNo;
        CwipLedgerEntry."G/L Entry No." := GlEntry."Entry No.";
        CwipLedgerEntry."CWIP No." := GlEntry."CWIP No.";
        //CWIPMaster.Get(CwipLedgerEntry."CWIP No.");
        CwipLedgerEntry."FA No." := CWIPMaster."FA No.";
        CwipLedgerEntry.Amount := GlEntry.Amount;
        CwipLedgerEntry."Posting Date" := GlEntry."Posting Date";
        CwipLedgerEntry.Narration := GlEntry.Narration;
        CwipLedgerEntry.Insert();
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line2", 'OnAfterInitGLEntry', '', false, false)]
    local procedure OnAfterInitGLEntry2(GLEntry: Record "G/L Entry"; GenJournalLine: record "Gen. Journal Line")
    begin
        if GLEntry."CWIP No." <> '' then
            InitCwipLedgerEntry(GLEntry);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterPurchInvLineInsert', '', false, false)]
    local procedure OnAfterPurchInvLineInsert(var PurchInvLine: Record "Purch. Inv. Line"; PurchInvHeader: Record "Purch. Inv. Header"; PurchLine: Record "Purchase Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean)
    var
        CwipLedgerEntry: Record "CWIP Ledger Entries";
        EntryNo: Integer;
        CWIPMaster: Record "CWIP Masters";
    begin
        if (PurchInvLine."CWIP No." = '') or (PurchInvLine.Quantity = 0) then
            exit;
        CWIPMaster.Get(PurchInvLine."CWIP No.");
        if CwipLedgerEntry.FindLast() then
            EntryNo := CwipLedgerEntry."Entry No." + 1
        else
            EntryNo := 1;
        CwipLedgerEntry.Init();
        CwipLedgerEntry."Entry No." := EntryNo;
        CwipLedgerEntry."CWIP No." := PurchInvLine."CWIP No.";
        //CWIPMaster.Get(CwipLedgerEntry."CWIP No.");
        CwipLedgerEntry."FA No." := CWIPMaster."FA No.";
        CwipLedgerEntry.Amount := PurchInvLine.Amount;
        CwipLedgerEntry."Posting Date" := PurchInvLine."Posting Date";
        CwipLedgerEntry.Narration := PurchInvLine.Description;
        CwipLedgerEntry.Insert();
    end;
    //CWIP<<
    //SalesShipmentXMLPort>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterPostSalesDoc', '', FALSE, FALSE)]
    local procedure OnAfterPostSalesDoc(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; SalesShptHdrNo: Code[20]; RetRcpHdrNo: Code[20]; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20]; CommitIsSuppressed: Boolean; InvtPickPutaway: Boolean; var CustLedgerEntry: Record "Cust. Ledger Entry"; WhseShip: Boolean; WhseReceiv: Boolean)
    var
        SalesRecev: Record "Sales & Receivables Setup";
        SalesShipmentHdr: Record "Sales Shipment Header";
        Customer: Record Customer;
        XMLFile: File;
        FileOutstream: OutStream;
    begin
        //Message('DMS XML Creation Code Line 783');
        if SalesShptHdrNo = '' then
            exit;
        //Message('DMS XML Creation Code Line 786');
        SalesShipmentHdr.Get(SalesShptHdrNo);
        Customer.get(SalesShipmentHdr."Sell-to Customer No.");
        if not Customer."DMS Customer" then
            exit;
        //Message('DMS XML Creation Code Line 789');
        SalesRecev.Get();
        SalesRecev.TestField("XML File Path");
        SalesShipmentHdr.Reset();
        SalesShipmentHdr.SetRange("No.", SalesShptHdrNo);
        if SalesShipmentHdr.FindFirst() then
            if XMLFile.Create(SalesRecev."XML File Path" + SalesShptHdrNo + '.xml') then begin
                //Message('DMS XML Creation Code Line 798');
                XMLFile.CreateOutStream(FileOutstream);
                Xmlport.Export(50000, FileOutstream, SalesShipmentHdr);
                XMLFile.Close();
            end;
    end;
    //SalesShipmentXMLPort<<
    //ImportFile>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Get Receipt", 'OnAfterPurchRcptLineSetFilters', '', false, false)]
    local procedure OnAfterPurchRcptLineSetFilters(var PurchRcptLine: Record "Purch. Rcpt. Line"; PurchaseHeader: Record "Purchase Header")
    begin

        IF PurchaseHeader."Purchase Type" = PurchaseHeader."Purchase Type"::Import THEN BEGIN
            PurchaseHeader.TESTFIELD("Import File No.");//SAA3.0
            PurchaseHeader.TESTFIELD("Clearing File No.");//SAA3.0
        END;

        PurchRcptLine.SETRANGE("Import File No.", PurchaseHeader."Import File No."); //SAA3.0
        PurchRcptLine.SETRANGE("Clearing File No.", PurchaseHeader."Clearing File No."); //SAA3.0
    end;

    [EventSubscriber(ObjectType::Table, 39, 'OnShowItemChargeAssgntOnBeforeCalcItemCharge', '', false, false)]
    local procedure OnShowItemChargeAssgntOnBeforeCalcItemCharge(var PurchaseLine: Record "Purchase Line"; var ItemChargeAssgntLineAmt: Decimal; Currency: Record Currency; var IsHandled: Boolean; var ItemChargeAssgntPurch: Record "Item Charge Assignment (Purch)")
    begin
        ItemChargeAssgntPurch."Import File No." := PurchaseLine."Import File No.";
    end;

    [EventSubscriber(ObjectType::Table, 83, 'OnAfterCopyItemJnlLineFromPurchHeader', '', false, false)]
    local procedure OnAfterCopyItemJnlLineFromPurchHeader(var ItemJnlLine: Record "Item Journal Line"; PurchHeader: Record "Purchase Header")
    begin
        ItemJnlLine."Import File No." := PurchHeader."Import File No.";
        ItemJnlLine."Clearing File No." := PurchHeader."Clearing File No.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnAfterInitItemLedgEntry', '', false, false)]
    local procedure OnAfterInitItemLedgEntry(var NewItemLedgEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; var ItemLedgEntryNo: Integer)
    begin
        NewItemLedgEntry."Import File No." := ItemJournalLine."Import File No.";
        NewItemLedgEntry."Clearing File No." := ItemJournalLine."Clearing File No.";
        NewItemLedgEntry."FA No." := ItemJournalLine."FA No.";
        NewItemLedgEntry.Description := ItemJournalLine.Description;//B2BPKON210521
        NewItemLedgEntry."User ID" := UserId;//B2BPKON210521
        //Fix12Jull2021Cwip>>
        NewItemLedgEntry."CWIP No." := ItemJournalLine."CWIP No.";
        NewItemLedgEntry."Capex No." := ItemJournalLine."Capex No.";
        NewItemLedgEntry."Capex Line No." := ItemJournalLine."Capex Line No.";
        //Fix12Jull2021Cwip<<
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnAfterInitValueEntry', '', false, false)]
    local procedure OnAfterInitValueEntry(var ValueEntry: Record "Value Entry"; ItemJournalLine: Record "Item Journal Line"; var ValueEntryNo: Integer)
    begin
        ValueEntry."Import File No." := ItemJournalLine."Import File No.";
        ValueEntry."Clearing File No." := ItemJournalLine."Clearing File No.";
        ValueEntry."FA No." := ItemJournalLine."FA No.";
        ValueEntry."FA Posting Type" := ItemJournalLine."FA Posting Type";
        ValueEntry."Maintenance Code" := ItemJournalLine."Maintenance Code";
        ValueEntry."Reason Code" := ItemJournalLine."Reason Code";
        //FIX21May2021>>
        ValueEntry."km Reading" := ItemJournalLine."Km Reading";
        ValueEntry."Previous Km Reading" := ItemJournalLine."Previous Km Reading";
        //FIX21May2021<<
        //FIX27May2021>>
        if ValueEntry.Description = '' then
            ValueEntry.Description := ItemJournalLine.Description;
        ValueEntry."Description 2" := ItemJournalLine."Description 2";
        //FIX21May2021<<
        //Fix12Jull2021Cwip>>
        ValueEntry."CWIP No." := ItemJournalLine."CWIP No.";
        ValueEntry."Capex No." := ItemJournalLine."Capex No.";
        ValueEntry."Capex Line No." := ItemJournalLine."Capex Line No.";
        //Fix12Jull2021Cwip<<
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Copy Document Mgt.", 'OnBeforeCopyPurchaseLinesToDoc', '', false, false)]
    local procedure OnBeforeCopyPurchaseLinesToDoc(FromDocType: Option Quote,"Blanket Order","Order",Invoice,"Return Order","Credit Memo","Posted Receipt","Posted Invoice","Posted Return Shipment","Posted Credit Memo"; var ToPurchaseHeader: Record "Purchase Header"; var FromPurchRcptLine: Record "Purch. Rcpt. Line"; var FromPurchInvLine: Record "Purch. Inv. Line"; var FromReturnShipmentLine: Record "Return Shipment Line"; var FromPurchCrMemoLine: Record "Purch. Cr. Memo Line"; var LinesNotCopied: Integer; var MissingExCostRevLink: Boolean)
    begin
        if ToPurchaseHeader."Purchase Type" = ToPurchaseHeader."Purchase Type"::Import then begin
            if FromDocType = FromDocType::"Posted Receipt" then
                if ToPurchaseHeader."Import File No." <> FromPurchRcptLine."Import File No." then
                    Error('Import files mismatching');
            if FromDocType = FromDocType::"Posted Invoice" then
                if ToPurchaseHeader."Import File No." <> FromPurchInvLine."Import File No." then
                    Error('Import files mismatching');
            if FromDocType = FromDocType::"Posted Credit Memo" then
                if ToPurchaseHeader."Import File No." <> FromPurchCrMemoLine."Import File No." then
                    Error('Import files mismatching');
        end;
    end;

    [EventSubscriber(ObjectType::Table, database::"Purch. Rcpt. Line", 'OnBeforeInsertInvLineFromRcptLine', '', false, false)]
    local procedure OnBeforeInsertInvLineFromRcptLine(var PurchRcptLine: Record "Purch. Rcpt. Line"; var PurchLine: Record "Purchase Line"; PurchOrderLine: Record "Purchase Line")
    begin
        PurchLine."Import File No." := PurchRcptLine."Import File No.";
        PurchLine."Clearing File No." := PurchRcptLine."Clearing File No.";
        //FIX05Jun2021>>
        PurchLine."WHT %" := PurchRcptLine."WHT %";
        PurchLine."WHT Group" := PurchRcptLine."WHT Group";
        PurchLine."WHT Applicable" := PurchRcptLine."WHT Applicable";
        if PurchRcptLine."WHT Applicable" then begin
            PurchLine."WHT Amount" := (PurchOrderLine."WHT Amount" / PurchOrderLine.Quantity) * PurchLine.Quantity;
            PurchLine."WHT Amount 2" := PurchLine."WHT Amount" * (PurchLine."WHT %" / 100);
        End;
        //FIX05Jun2021<<
    end;
    //ImportFile<<
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnPostBankAccOnBeforeBankAccLedgEntryInsert', '', false, false)]
    local procedure OnPostBankAccOnBeforeBankAccLedgEntryInsert(var BankAccountLedgerEntry: Record "Bank Account Ledger Entry"; var GenJournalLine: Record "Gen. Journal Line"; BankAccount: Record "Bank Account")
    begin
        if BankAccountLedgerEntry."External Document No." = '' then
            BankAccountLedgerEntry."External Document No." := GenJournalLine."External Document No.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line2", 'OnPostBankAccOnBeforeBankAccLedgEntryInsert', '', false, false)]
    local procedure OnPostBankAccOnBeforeBankAccLedgEntry(var BankAccountLedgerEntry: Record "Bank Account Ledger Entry"; var GenJournalLine: Record "Gen. Journal Line"; BankAccount: Record "Bank Account")
    begin
        if BankAccountLedgerEntry."External Document No." = '' then
            BankAccountLedgerEntry."External Document No." := GenJournalLine."External Document No.";
    end;
    //B2BFix 16Apr2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Sales Document", 'OnBeforeReleaseSalesDoc', '', false, false)]
    local procedure OnBeforeReleaseSalesDoc(VAR SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
    begin
        //if (SalesHeader."Document Type" = SalesHeader."Document Type"::Order) or (SalesHeader."Document Type" = SalesHeader."Document Type"::Invoice) or (SalesHeader."Document Type" = SalesHeader."Document Type"::Quote) then begin
        SalesHeader.TestField("Shortcut Dimension 2 Code");
        SalesHeader.TestField("Shortcut Dimension 1 Code");
        SalesLine.Reset();
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetFilter("No.", '<>%1', '');
        if SalesLine.FindSet() then
            repeat
                SalesLine.TestField("Shortcut Dimension 2 Code");
                SalesLine.TestField("Shortcut Dimension 1 Code");
            until SalesLine.Next() = 0;
        //end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnSendSalesDocForApproval', '', false, false)]
    local procedure OnSendSalesDocForApproval(VAR SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
    begin
        //if (SalesHeader."Document Type" = SalesHeader."Document Type"::Order) or (SalesHeader."Document Type" = SalesHeader."Document Type"::Invoice) or (SalesHeader."Document Type" = SalesHeader."Document Type"::Quote) then begin
        SalesHeader.TestField("Shortcut Dimension 2 Code");
        SalesHeader.TestField("Shortcut Dimension 1 Code");
        SalesLine.Reset();
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetFilter("No.", '<>%1', '');
        if SalesLine.FindSet() then
            repeat
                SalesLine.TestField("Shortcut Dimension 2 Code");
                SalesLine.TestField("Shortcut Dimension 1 Code");
            until SalesLine.Next() = 0;
        //end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnSendPurchaseDocForApproval', '', false, false)]
    local procedure OnSendPurchaseDocForApproval(VAR PurchaseHeader: Record "Purchase Header")
    var
        PurchaseLine: Record "Purchase Line";
        CwipMaster: Record "CWIP Masters";
    begin
        //Fix06Jul2021>>
        if PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Order then
            if PurchaseHeader."Payment Terms Code" = 'LC' then
                if PurchaseHeader."Prepayment %" = 0 then
                    Error('Prepaymet% must be have a value');
        //Fix06Jul2021<<
        PurchaseHeader.TestField("Shortcut Dimension 2 Code");
        PurchaseHeader.TestField("Shortcut Dimension 1 Code");
        PurchaseHeader.TestField("Location Code");//PKONJ9
        PurchaseLine.Reset();
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetFilter("No.", '<>%1', '');
        if PurchaseLine.FindSet() then
            repeat
                PurchaseLine.TestField("Shortcut Dimension 2 Code");
                PurchaseLine.TestField("Shortcut Dimension 1 Code");
                PurchaseLine.TestField("Location Code");//PKONJ9
                if (PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset") and (PurchaseLine."FA Posting Type" = PurchaseLine."FA Posting Type"::"Acquisition Cost") then begin
                    PurchaseLine.TestField("Capex No.");
                    PurchaseLine.TestField("Capex Line No.");
                    CwipMaster.Reset();
                    CwipMaster.SetRange("FA No.", PurchaseLine."No.");
                    if CwipMaster.FindFirst() then
                        Error('Fixed asset is attached to CWIP %1 cannot post Acquisition Cost for this fixed asset %2', CwipMaster."CWIP No.", PurchaseLine."No.");
                end;
                if PurchaseLine."WHT Applicable" then
                    if PurchaseLine."WHT Amount" = 0 then
                        Error('WHT amount must have value');
            until PurchaseLine.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Purchase Document", 'OnBeforeReleasePurchaseDoc', '', false, false)]
    local procedure OnBeforeReleasePurchaseDoc(VAR PurchaseHeader: Record "Purchase Header")
    var
        PurchaseLine: Record "Purchase Line";
        CwipMaster: Record "CWIP Masters";
    begin
        //Fix06Jul2021>>
        if PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Order then
            if PurchaseHeader."Payment Terms Code" = 'LC' then
                if PurchaseHeader."Prepayment %" = 0 then
                    Error('Prepaymet% must be have a value');
        //Fix06Jul2021<<
        PurchaseHeader.TestField("Shortcut Dimension 2 Code");
        PurchaseHeader.TestField("Shortcut Dimension 1 Code");
        PurchaseLine.Reset();
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetFilter("No.", '<>%1', '');
        if PurchaseLine.FindSet() then
            repeat
                PurchaseLine.TestField("Shortcut Dimension 2 Code");
                PurchaseLine.TestField("Shortcut Dimension 1 Code");
                if (PurchaseLine.Type = PurchaseLine.Type::"Fixed Asset") and (PurchaseLine."FA Posting Type" = PurchaseLine."FA Posting Type"::"Acquisition Cost") then begin
                    PurchaseLine.TestField("Capex No.");
                    PurchaseLine.TestField("Capex Line No.");
                    CwipMaster.Reset();
                    CwipMaster.SetRange("FA No.", PurchaseLine."No.");
                    if CwipMaster.FindFirst() then
                        Error('Fixed asset is attached to CWIP %1 cannot post Acquisition Cost for this fixed asset %2', CwipMaster."CWIP No.", PurchaseLine."No.");
                end;
                if PurchaseLine."WHT Applicable" then
                    if PurchaseLine."WHT Amount" = 0 then
                        Error('WHT amount must have value');
            until PurchaseLine.Next() = 0;
    end;

    //B2BFix 16Apr2021<<
    //B2BFix 20Apr2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Quote to Order", 'OnBeforeModifySalesOrderHeader', '', false, false)]
    local procedure OnBeforeModifySalesOrderHeader(var SalesOrderHeader: Record "Sales Header"; SalesQuoteHeader: Record "Sales Header")
    begin
        SalesOrderHeader.Validate("Location Code");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Quote to Invoice", 'OnBeforeInsertSalesInvoiceHeader', '', false, false)]
    local procedure OnBeforeInsertSalesInvoiceHeader(var SalesInvoiceHeader: Record "Sales Header"; QuoteSalesHeader: Record "Sales Header")
    begin
        SalesInvoiceHeader.Validate("Location Code");
    end;
    //B2BFix 20Apr2021<<
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Req. Wksh.-Make Order", 'OnBeforeValidateBuyFromVendorNo', '', false, false)]
    local procedure OnBeforeValidateBuyFromVendorNo(var PurchOrderHeader: Record "Purchase Header"; var RequisitionLine: Record "Requisition Line"; var IsHandled: Boolean)
    var
        vend: Record Vendor;
    begin
        vend.get(RequisitionLine."Vendor No.");
        PurchOrderHeader."Purchase Type" := vend."Vendor Type";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Req. Wksh.-Make Order", 'OnBeforePurchOrderHeaderInsert', '', false, false)]
    local procedure OnBeforePurchOrderHeaderInsert(var PurchaseHeader: Record "Purchase Header"; RequisitionLine: Record "Requisition Line")
    begin
        PurchaseHeader."Purchase Type" := PurchaseHeader."Purchase Type"::Local;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnBeforePostInvPostBuffer', '', false, false)]
    local procedure OnBeforePostInvPostBuffer(var GenJnlLine: Record "Gen. Journal Line"; var InvoicePostBuffer: Record "Invoice Post. Buffer"; var SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PreviewMode: Boolean)
    begin
        if SalesHeader."POS Window" then begin
            GenJnlLine."Description 2" := 'Retail' + Format(SalesHeader."No.") + SalesHeader."External Document No." + format(SalesHeader."Pos Bank Name");
            GenJnlLine.Narration := 'Pos Retail Sales';
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnBeforePostCustomerEntry', '', false, false)]
    local procedure OnBeforePostCustomerEntry(var GenJnlLine: Record "Gen. Journal Line"; var SalesHeader: Record "Sales Header"; var TotalSalesLine: Record "Sales Line"; var TotalSalesLineLCY: Record "Sales Line"; CommitIsSuppressed: Boolean; PreviewMode: Boolean; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    begin
        if SalesHeader."POS Window" then begin
            GenJnlLine."Description 2" := SalesHeader."Responsibility Center" + '/' + Format(SalesHeader."POS Transaction No.") + '/' + format(SalesHeader."Pos Bank Name");
            GenJnlLine.Narration := 'Pos Retail Sales';
            GenJnlLine."Responsibility Center" := SalesHeader."Responsibility Center";
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnBeforePostBalancingEntry', '', false, false)]
    local procedure OnBeforePostBalancingEntry(var GenJnlLine: Record "Gen. Journal Line"; SalesHeader: Record "Sales Header"; var TotalSalesLine: Record "Sales Line"; var TotalSalesLineLCY: Record "Sales Line"; CommitIsSuppressed: Boolean; PreviewMode: Boolean)
    var
        GenJnlLine2: Record "Gen. Journal Line";
        GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
    begin
        if SalesHeader."POS Window" then begin
            GenJnlLine."Description 2" := SalesHeader."Responsibility Center" + '/' + Format(SalesHeader."POS Transaction No.") + '/' + format(SalesHeader."Pos Bank Name");
            GenJnlLine.Narration := SalesHeader."Responsibility Center" + '/' + SalesHeader."POS Transaction No.";
            if SalesHeader."POS Transaction Type" = SalesHeader."POS Transaction Type"::Card then begin
                GenJnlLine.validate("Bal. Account No.", SalesHeader."POS Account No.");
            end;
            if SalesHeader."POS Transaction Type" = SalesHeader."POS Transaction Type"::Both then begin
                GenJnlLine2.TransferFields(GenJnlLine);
                GenJnlLine2.validate("Bal. Account No.", SalesHeader."POS Account No.");
                GenJnlLine2.Validate(Amount, -SalesHeader."POS Card Amount");
                GenJnlPostLine.RunWithCheck(GenJnlLine2);
                GenJnlLine.Validate(Amount, -SalesHeader."POS Cash Amount");
            end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", 'OnAfterPostWhseShipment', '', false, false)]
    local procedure OnAfterPostWhseShipment(var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        WarehouseShipLines: Record "Warehouse Shipment Line";
        PostedLoadingslipLine: Record "Posted Loading Slip Line";
    begin
        WarehouseShipLines.Reset();
        WarehouseShipLines.SetRange("No.", WarehouseShipmentHeader."No.");
        if WarehouseShipLines.FindSet() then
            repeat
                if PostedLoadingslipLine.Get(WarehouseShipLines."Posted Loading Slip No.", WarehouseShipLines."Posted Loading Slip Line No.") then begin
                    PostedLoadingslipLine.Applied := true;
                    PostedLoadingslipLine.Modify();
                end;
            until WarehouseShipLines.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", 'OnAfterCode', '', false, false)]
    local procedure OnAfterCode(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        WarehouseRecepitLines: Record "Warehouse Receipt Line";
        PostedLoadingslipLine: Record "Posted Loading Slip Line";
    begin
        WarehouseRecepitLines.Reset();
        WarehouseRecepitLines.SetRange("No.", WarehouseReceiptHeader."No.");
        if WarehouseRecepitLines.FindSet() then
            repeat
                if PostedLoadingslipLine.Get(WarehouseRecepitLines."Posted Loading Slip No.", WarehouseRecepitLines."Posted Loading Slip Line No.") then begin
                    PostedLoadingslipLine."Applied for Receipt" := true;
                    PostedLoadingslipLine.Modify();
                end;
            until WarehouseRecepitLines.Next() = 0;

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Correct Posted Purch. Invoice", 'OnBeforePurchaseHeaderInsert', '', false, false)]
    local procedure OnBeforePurchaseHeaderInsert(var PurchaseHeader: Record "Purchase Header"; PurchInvHeader: Record "Purch. Inv. Header")
    begin
        PurchaseHeader."Purchase Type" := PurchInvHeader."Purchase Type";
    end;

    [EventSubscriber(ObjectType::Table, database::"Purch. Rcpt. Line", 'OnAfterDescriptionPurchaseLineInsert', '', false, false)]

    local procedure OnAfterDescriptionPurchaseLineInsert(var PurchLine: Record "Purchase Line"; PurchRcptLine: Record "Purch. Rcpt. Line"; var NextLineNo: Integer)
    var
        PurchaseHeader: Record "Purchase Header";
    begin
        PurchLine."Location Code" := PurchRcptLine."Location Code";
        if PurchaseHeader.get(PurchLine."Document Type", PurchLine."Document No.") and (PurchaseHeader."Location Code" = '') then begin
            PurchaseHeader."Location Code" := PurchRcptLine."Location Code";
            PurchaseHeader.Modify();
        end;

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", 'OnAfterCreateRcptLineFromPurchLine', '', false, false)]
    local procedure OnAfterCreateRcptLineFromPurchLine(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; WarehouseReceiptHeader: Record "Warehouse Receipt Header"; PurchaseLine: Record "Purchase Line")
    begin
        WarehouseReceiptLine."No. 2" := PurchaseLine."No.2";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnPopulateApprovalEntryArgument', '', false, false)]
    local procedure OnPopulateApprovalEntryArgument1(var RecRef: RecordRef; var ApprovalEntryArgument: Record "Approval Entry"; WorkflowStepInstance: Record "Workflow Step Instance")
    var
        BankRec: record "Bank Acc. Reconciliation";
    begin
        case RecRef.Number of
            Database::MRSHeader:
                begin
                    RecRef.SetTable(MrsHdr);
                    MrsHdr.CalcFields("Total Amout(LCY)");
                    ApprovalEntryArgument.Amount := MrsHdr."Total Amout(LCY)";
                end;
            Database::"Bank Acc. Reconciliation":
                begin
                    RecRef.SetTable(BankRec);
                    BankRec.CalcFields("Total Applied Amount");
                    ApprovalEntryArgument.Amount := BankRec."Total Applied Amount";
                end;

        end
    end;
    //FIX26MAY2021>>
    [EventSubscriber(ObjectType::Table, database::"Purch. Inv. Header", 'OnBeforePrintRecords', '', false, false)]
    local procedure OnBeforePrintRecords(var PurchInvHeader: Record "Purch. Inv. Header"; ShowRequestPage: Boolean; var IsHandled: Boolean)
    begin
        //Report.RunModal(50038, true, false, PurchInvHeader);//PKONJ9
        //IsHandled := true;//PKONJ9
    end;

    [EventSubscriber(ObjectType::Table, database::"Purch. Cr. Memo Hdr.", 'OnBeforePrintRecords', '', false, false)]
    local procedure OnBeforePrintRecordsCredit(var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; ShowRequestPage: Boolean; var IsHandled: Boolean)
    begin
        Report.RunModal(407, true, false, PurchCrMemoHdr);
        IsHandled := true;
    end;

    [EventSubscriber(ObjectType::Table, DataBase::"Sales Cr.Memo Header", 'OnBeforePrintRecords', '', false, false)]
    local procedure OnBeforePrintRecordsSalesCreditMemo(var ReportSelections: Record "Report Selections"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; ShowRequestPage: Boolean; var IsHandled: Boolean);
    begin
        Report.RunModal(50167, true, false, SalesCrMemoHeader);
        IsHandled := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Sales-Post + Print", 'OnBeforePrintReceive', '', false, false)]
    local procedure OnBeforePrintReceive(var SalesHeader: Record "Sales Header"; SendReportAsEmail: Boolean; var IsHandled: Boolean)
    var
        ReturnRcptHeader: Record "Return Receipt Header";
    begin
        ReturnRcptHeader."No." := SalesHeader."Last Return Receipt No.";
        if ReturnRcptHeader.Find then;
        ReturnRcptHeader.SetRecFilter;
        if not SendReportAsEmail then begin
            Report.Run(6631, true, false, ReturnRcptHeader);
            IsHandled := true;
        end;
    end;
    //FIX26MAY2021<<

    //FIX29May2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"FA Insert G/L Account", 'OnInsertMaintenanceAccNoOnBeforeInsertBufferEntry', '', false, false)]
    local procedure OnInsertMaintenanceAccNoOnBeforeInsertBufferEntry(var FAGLPostBuf: Record "FA G/L Posting Buffer"; var MaintenanceLedgEntry: Record "Maintenance Ledger Entry")
    Var
        Maintenance: Record Maintenance;
    begin
        Maintenance.Get(MaintenanceLedgEntry."Maintenance Code");
        if Maintenance."Maintenance Account Code" <> '' then begin
            FAGLPostBuf."Account No." := Maintenance."Maintenance Account Code";
        end;
    end;
    //FIX29May2021<<
    //FIX15Jun2021>>
    [EventSubscriber(ObjectType::Table, database::"Sales Line", 'OnBeforeUpdateWithWarehouseShip', '', false, false)]
    local procedure OnBeforeUpdateWithWarehouseShip(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    var
        SalesHeader: Record "Sales Header";
    begin
        SalesHeader.get(SalesLine."Document Type", SalesLine."Document No.");
        if SalesHeader."POS Window" then
            IsHandled := true;
    end;
    //FIX15Jun2021<<
    //FIX16Jun2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnBeforePurchInvHeaderInsert', '', false, false)]
    local procedure OnBeforePurchInvHeaderInsert(var PurchInvHeader: Record "Purch. Inv. Header"; var PurchHeader: Record "Purchase Header"; CommitIsSupressed: Boolean)
    var
        PurchaseLine: Record "Purchase Line";
        PurchRcptHeader: Record "Purch. Rcpt. Header";
        PurchRcptLine: Record "Purch. Rcpt. Line";
    begin
        if PurchHeader."Document Type" = PurchHeader."Document Type"::Invoice then begin
            PurchaseLine.Reset();
            PurchaseLine.SetRange("Document Type", PurchHeader."Document Type");
            PurchaseLine.SetRange("Document No.", PurchHeader."No.");
            PurchaseLine.SetFilter("No.", '<>%1', '');
            if PurchaseLine.FindFirst() then begin
                PurchRcptLine.Reset();
                PurchRcptLine.SetRange("Document No.", PurchaseLine."Receipt No.");
                if PurchRcptLine.FindFirst() then
                    PurchInvHeader."Order No." := PurchRcptLine."Order No.";
            end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnBeforePurchInvLineInsert', '', false, false)]
    local procedure OnBeforePurchInvLineInsert(var PurchInvLine: Record "Purch. Inv. Line"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchaseLine: Record "Purchase Line"; CommitIsSupressed: Boolean)
    begin
        if PurchaseLine."Document Type" = PurchaseLine."Document Type"::Order then begin
            PurchInvLine."WHT Amount" := (PurchaseLine."WHT Amount" / PurchaseLine.Quantity) * PurchInvLine.Quantity;
            PurchInvLine."Orginal WHT Amount" := PurchaseLine."WHT Amount";//Fix05Jul2021
        end;
    end;
    //FIX16Jun2021<<
    //Fix05Jul2021>>
    [EventSubscriber(ObjectType::Table, Database::"Purch. Rcpt. Line", 'OnAfterInitFromPurchLine', '', false, false)]
    local procedure OnAfterInitFromPurchLine(PurchRcptHeader: Record "Purch. Rcpt. Header"; PurchLine: Record "Purchase Line"; var PurchRcptLine: Record "Purch. Rcpt. Line")
    begin
        PurchRcptLine."Orginal WHT Amount" := PurchLine."WHT Amount";
    end;
    //Fix05Jul2021<<
    [EventSubscriber(ObjectType::Table, database::"VAT Entry", 'OnAfterCopyFromGenJnlLine', '', false, false)]
    procedure OnAfterCopyFromGenJnlLineVAT(var VATEntry: Record "VAT Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        VATEntry."Global Dimension 1 Code" := GenJournalLine."Shortcut Dimension 1 Code";
        VATEntry."Global Dimension 2 Code" := GenJournalLine."Shortcut Dimension 2 Code";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales Price Calc. Mgt.", 'OnBeforeFindSalesPrice', '', false, false)]
    local procedure OnBeforeFindSalesPrice(var ToSalesPrice: Record "Sales Price"; var FromSalesPrice: Record "Sales Price"; var QtyPerUOM: Decimal; var Qty: Decimal; var CustNo: Code[20]; var ContNo: Code[20]; var CustPriceGrCode: Code[10]; var CampaignNo: Code[20]; var ItemNo: Code[20]; var VariantCode: Code[10]; var UOM: Code[10]; var CurrencyCode: Code[10]; var StartingDate: Date; var ShowAll: Boolean)
    begin
        FromSalesPrice.SetRange("Approval Status", FromSalesPrice."Approval Status"::Released);
    end;
    //Fix21Jun2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Approvals Mgmt.", 'OnBeforeApprovalEntryInsert', '', false, false)]
    local procedure OnBeforeApprovalEntryInsert(var ApprovalEntry: Record "Approval Entry"; ApprovalEntryArgument: Record "Approval Entry")
    var
        UserSetup: Record "User Setup";
        ApprovalAdminUserSetup: Record "User Setup";
        ApproverUserIdNotInSetupErr: Label 'You must set up an approver for user ID %1 in the Approval User Setup window.';
        SubstituteNotFoundErr: Label 'There is no substitute, direct approver, or approval administrator for user ID %1 in the Approval User Setup window.';
        DelegationReq: Record "Delegation Request";
    begin
        IF ApprovalEntry.Status = ApprovalEntry.Status::Approved THEN
            exit;
        DelegationReq.RESET;
        DelegationReq.SETRANGE("User ID", ApprovalEntry."Approver ID");
        DelegationReq.SETRANGE(DelegationReq.Delegate, TRUE);
        IF DelegationReq.FINDSET THEN
            REPEAT
                IF ((CURRENTDATETIME >= DelegationReq."Start Date Time") AND
                   (CURRENTDATETIME <= DelegationReq."End Date Time")) THEN begin
                    IF NOT UserSetup.GET(ApprovalEntry."Approver ID") THEN
                        ERROR(ApproverUserIdNotInSetupErr, ApprovalEntry."Sender ID");

                    IF UserSetup.Substitute = '' THEN
                        IF UserSetup."Approver ID" = '' THEN BEGIN
                            ApprovalAdminUserSetup.SETRANGE("Approval Administrator", TRUE);
                            IF ApprovalAdminUserSetup.FINDFIRST THEN
                                UserSetup.GET(ApprovalAdminUserSetup."User ID")
                            ELSE
                                ERROR(SubstituteNotFoundErr, UserSetup."User ID");
                        END ELSE
                            UserSetup.GET(UserSetup."Approver ID")
                    ELSE
                        UserSetup.GET(UserSetup.Substitute);

                    ApprovalEntry."Approver ID" := UserSetup."User ID";
                end;
            UNTIL DelegationReq.NEXT = 0;

    end;
    //Fix21Jun2021<<
    //PKONJ21 Below 
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Batch", 'OnAfterPostJnlLines', '', false, false)]
    local procedure OnAfterPostJnlLines(var ItemJournalBatch: Record "Item Journal Batch"; var ItemJournalLine: Record "Item Journal Line"; ItemRegNo: Integer; WhseRegNo: Integer)
    var
        itemjnltem: Record "Item Journal Template";
        ItemReg: Record "Item Register";
        WhseReg: Record "Warehouse Register";
    begin
        IF ItemJournalBatch."Journal Template Name" = 'PROD. ORDE' then begin
            itemjnltem.Get(ItemJournalBatch."Journal Template Name");
            commit;
            if ItemReg.Get(ItemRegNo) then begin
                ItemReg.SetRecFilter;
                REPORT.Run(itemjnltem."Posting Report ID", true, false, ItemReg);
            end;

            if WhseReg.Get(WhseRegNo) then begin
                WhseReg.SetRecFilter;
                REPORT.Run(itemjnltem."Whse. Register Report ID", true, false, WhseReg);
            end;
        end;
    end;

    //Fix21Jun2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnPostItemChargeOnBeforePostItemJnlLine', '', false, false)]
    local procedure OnPostItemChargeOnBeforePostItemJnlLine(var PurchaseLineToPost: Record "Purchase Line"; var PurchaseLine: Record "Purchase Line"; QtyToAssign: Decimal; var TempItemChargeAssgntPurch: Record "Item Charge Assignment (Purch)" temporary)
    var
        PurchRcptLine2: Record "Purch. Rcpt. Line";
        PurchRcptHdr: Record "Purch. Rcpt. Header";
    begin
        IF TempItemChargeAssgntPurch."Document Type" IN [TempItemChargeAssgntPurch."Document Type"::Order, TempItemChargeAssgntPurch."Document Type"::Invoice] THEN BEGIN
            if TempItemChargeAssgntPurch."Applies-to Doc. Type" = TempItemChargeAssgntPurch."Applies-to Doc. Type"::Receipt then BEGIN
                PurchRcptLine2.GET(TempItemChargeAssgntPurch."Applies-to Doc. No.", TempItemChargeAssgntPurch."Applies-to Doc. Line No.");
                if PurchRcptLine2."Import File No." <> '' then
                    PurchaseLineToPost."Import File No." := PurchRcptLine2."Import File No."
                else begin
                    PurchRcptHdr.Get(TempItemChargeAssgntPurch."Applies-to Doc. No.");
                    PurchaseLineToPost."Import File No." := PurchRcptHdr."Import File No."
                end;
                if PurchRcptLine2."Clearing File No." <> '' then
                    PurchaseLineToPost."Clearing File No." := PurchRcptLine2."Clearing File No."
                else begin
                    PurchRcptHdr.Get(TempItemChargeAssgntPurch."Applies-to Doc. No.");
                    PurchaseLineToPost."Clearing File No." := PurchRcptHdr."Clearing File No."
                end;
            end;
        end;
    end;

    [EventSubscriber(ObjectType::Table, database::"Item Journal Line", 'OnAfterCopyItemJnlLineFromPurchLine', '', false, false)]
    local procedure OnAfterCopyItemJnlLineFromPurchLine(var ItemJnlLine: Record "Item Journal Line"; PurchLine: Record "Purchase Line")
    begin
        if ItemJnlLine."Import File No." = '' then
            ItemJnlLine."Import File No." := PurchLine."Import File No.";
        if ItemJnlLine."Clearing File No." = '' then
            ItemJnlLine."Clearing File No." := PurchLine."Clearing File No.";
    end;
    //Fix21Jun2021<<
    //Fix29Jun2021>>
    [EventSubscriber(ObjectType::Table, database::"Document Attachment", 'OnBeforeInsertAttachment', '', false, false)]

    local procedure OnBeforeInsertAttachment(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
        LineNo: Integer;
    begin
        case RecRef.Number of
            DATABASE::"Customer Sales Price/Discount":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.Validate("No.", RecNo);
                    FieldRef := RecRef.Field(18);
                    LineNo := FieldRef.Value;
                    DocumentAttachment.Validate("Line No.", LineNo);
                    FieldRef := RecRef.Field(17);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.Validate("Shortcut Dimension 1 Code", RecNo);

                end;
        end;
    end;

    [EventSubscriber(ObjectType::Page, page::"Document Attachment Details", 'OnAfterOpenForRecRef', '', false, false)]
    local procedure OnAfterOpenForRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef; var FlowFieldsEditable: Boolean)
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
        LineNo: Integer;
    begin
        case RecRef.Number of
            DATABASE::"Customer Sales Price/Discount":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.SetRange("No.", RecNo);

                    FieldRef := RecRef.Field(18);
                    LineNo := FieldRef.Value;
                    DocumentAttachment.SetRange("Line No.", LineNo);
                    FieldRef := RecRef.Field(17);
                    RecNo := FieldRef.Value;
                    DocumentAttachment.SetRange("Shortcut Dimension 1 Code", RecNo);
                    FlowFieldsEditable := false;
                end;
        end;
    end;
    //Fix29Jun2021<<
    //FIX30Jun2021>>
    [EventSubscriber(ObjectType::Codeunit, codeunit::"Make FA Ledger Entry", 'OnAfterCopyFromFAJnlLine', '', false, false)]
    local procedure OnAfterCopyFromFAJnlLine(var FALedgerEntry: Record "FA Ledger Entry"; FAJournalLine: Record "FA Journal Line")
    begin

    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Make FA Ledger Entry", 'OnAfterCopyFromGenJnlLine', '', false, false)]
    local procedure FAOnAfterCopyFromGenJnlLine(var FALedgerEntry: Record "FA Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        FALedgerEntry."Capex No." := GenJournalLine."Capex No.";
        FALedgerEntry."Capex Line No." := GenJournalLine."Capex Line No.";
    end;
    //FIX30Jun2021<<

    //PKONJU9
    [EventSubscriber(ObjectType::Codeunit, codeunit::"Purch.-Post (Yes/No)", 'OnAfterConfirmPost', '', false, false)]
    local procedure OnAfterConfirmPost(PurchaseHeader: Record "Purchase Header")
    begin
        IF (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Order) OR (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Invoice) then
            IF PurchaseHeader.Invoice THEN
                PurchaseHeader.TESTFIELD("Arrival Date");
    end;
    //PKONJU9
    //Fix30Jul2021>>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Batch", 'OnBeforeCode', '', false, false)]
    local procedure OnBeforeCode(var ItemJournalLine: Record "Item Journal Line")
    begin
        if ItemJournalLine.FindSet() then
            repeat
                if ItemJournalLine."FA Posting Type" = ItemJournalLine."FA Posting Type"::"Acquisition Cost" then begin
                    ItemJournalLine.TestField("Capex No.");
                    ItemJournalLine.TestField("Capex Line No.");
                end;
                if ItemJournalLine."CWIP No." <> '' then begin
                    ItemJournalLine.TestField("Capex No.");
                    ItemJournalLine.TestField("Capex Line No.");
                end;
            until ItemJournalLine.Next() = 0;
    end;
    //Fix30Jul2021<<


    procedure TransferBookValuetoAcc(Item: Record Item)
    Var
        GenJournle: Record "Gen. Journal Line";
        ItmJounLne: Record "Item Journal Line";
        ItmJounLine: Record "Item Journal Line";
        PurchLne: Record "Purchase Line";
        NextLineNo: Integer;
        GenJourn: Record "Gen. Journal Line";
        DepGRec: Record "FA Depreciation Book";
        QtyPurch: Decimal;
        NextGLLineNo: Integer;
        GenPosSetup: Record "General Posting Setup";
        ItmQtyGVar: Decimal;
        ItmAmtGVar: Decimal;
        ItmLocation: COde[20];
        ItmLedEntry: Record "Item Ledger Entry";
        ItmGRec: Record "Fixed Asset";
        ItmNoGVar: code[20];
        ItemJnePost: Codeunit "Item Jnl.-Post Line";
        GenJnLne: Codeunit "Gen. Jnl.-Post Line";
        ItemError: Label 'FA Cost Value Already Transfered to Book value.';
        ItemLVar: record Item;
    BEGIN
        with Item do begin
            IF "Transfered FA Cost" = false then BEGIN
                clear(ItmNoGVar);
                TestField("Gen. Prod. Posting Group");
                ItmJounLine.reset;
                ItmJounLine.SetRange("Journal Batch Name", 'DEFAULT');
                ItmJounLine.SetRange("Journal Template Name", 'ITEM');
                IF ItmJounLine.FindLast() then
                    NextLineNo := ItmJounLine."Line No."
                else
                    NextLineNo := 0;
                clear(ItmAmtGVar);
                clear(ItmQtyGVar);
                clear(ItmLocation);

                DepGRec.reset;
                DepGRec.SetRange("FA No.", "No.");
                IF DepGRec.findfirst then;

                ItmLedEntry.Reset();
                ItmLedEntry.SetRange("Item No.", "No.");
                ItmLedEntry.SetRange("Entry Type", ItmLedEntry."Entry Type"::Purchase);
                ItmLedEntry.SetFilter("Remaining Quantity", '<>%1', 0);
                IF ItmLedEntry.findfirst then BEGIN
                    ItmLedEntry.CalcFields("Cost Amount (Actual)");
                    ItmLocation := ItmLedEntry."Location Code";
                    ItmQtyGVar := ItmLedEntry.Quantity;
                    ItmAmtGVar := ItmLedEntry."Cost Amount (Actual)";
                END;

                ItmJounLne.init;
                ItmJounLne."Journal Batch Name" := 'DEFAULT';
                ItmJounLne."Journal Template Name" := 'ITEM';
                ItmJounLne."Line No." := NextLineNo + 10000;
                ItmJounLne.VALIDATE("Posting Date", WorkDate());
                ItmJounLne."Entry Type" := ItmJounLne."Entry Type"::"Negative Adjmt.";
                ItmJounLne."Document No." := "No.";
                ItmJounLne.VALIDATE("Item No.", "No.");
                ItmJounLne.VALIDATE("Location Code", ItmLocation);
                ItmJounLne.VALIDATE(Quantity, ItmQtyGVar);
                //ItmJounLne.Insert();
                //CODEUNIT.Run(CODEUNIT::"Item Jnl.-Post", ItmJounLne);

                IF (ItemLVar.get(ItmJounLne."Item No.")) AND (ItemLVar."Item Tracking Code" <> '') THEN
                    CreateItemTracking(ItmLedEntry, ItmJounLne);

                ItemJnePost.RunWithCheck(ItmJounLne);

                GenJourn.reset;
                GenJourn.SetRange("Journal Batch Name", 'ASSET');
                GenJourn.SetRange("Journal Template Name", 'ASSETS');
                IF GenJourn.FindLast() then
                    NextGLLineNo := GenJourn."Line No." + 10000
                else
                    NextGLLineNo := 10000;

                QtyPurch := 1;
                GenPosSetup.reset;
                GenPosSetup.SetRange("Gen. Prod. Posting Group", "Gen. Prod. Posting Group");
                IF GenPosSetup.findfirst then;



                //while QtyPurch <= ItmQtyGVar do begin
                if "MRS No." <> '' then begin
                    ItmGRec.reset;
                    ItmGRec.SetRange("MRS No.", "MRS No.");
                    ItmGRec.SetRange("MRS Line No.", "MRS Line No.");
                    IF ItmGRec.FindSet() then
                        repeat
                            GenJournle.init;
                            GenJournle."Journal Template Name" := 'ASSETS';
                            GenJournle."Journal Batch Name" := 'ASSET';
                            GenJournle."Line No." := NextGLLineNo;
                            GenJournle."Posting Date" := Today;
                            GenJournle.VALIDATE("Document No.", FORMAT(NextGLLineNo));
                            //GenJournle."Account Type" := GenJournle."Bal. Account Type"::"G/L Account";
                            //GenJournle."Account No." := GenPosSetup."Inventory Adjmt. Account";

                            GenJournle.VALIDATE("Account Type", GenJournle."Bal. Account Type"::"Fixed Asset");
                            GenJournle.Validate("Account No.", ItmGRec."No.");
                            //GenJournle."Depreciation Book Code" := '';
                            GenJournle.VALIDATE("Depreciation Book Code", DepGRec."Depreciation Book Code");
                            GenJournle."FA Posting Type" := GenJournle."FA Posting Type"::"Acquisition Cost";
                            //GenJournle."Gen. Posting Type" := GenJournle."Gen. Posting Type"::Purchase;
                            GenJournle.VALIDATE("Bal. Account Type", GenJournle."Bal. Account Type"::"G/L Account");
                            GenJournle.VALIDATE("Bal. Account No.", GenPosSetup."Inventory Adjmt. Account");
                            //GenJournle."Bal. Gen. Posting Type" := GenJournle."Bal. Gen. Posting Type"::Purchase;
                            GenJournle.VALIDATE(Quantity, 1);
                            GenJournle.VALIDATE(Amount, ItmAmtGVar / ItmQtyGVar);
                            // GenJournle.Insert();
                            QtyPurch += 1;
                            NextGLLineNo += 10000;
                            GenJnLne.RunWithCheck(GenJournle);
                        until ItmGRec.next = 0;
                end else begin
                    GenJournle.init;
                    GenJournle."Journal Template Name" := 'ASSETS';
                    GenJournle."Journal Batch Name" := 'ASSET';
                    GenJournle."Line No." := NextGLLineNo;
                    GenJournle."Posting Date" := Today;
                    GenJournle.VALIDATE("Document No.", FORMAT(NextGLLineNo));
                    //GenJournle."Account Type" := GenJournle."Bal. Account Type"::"G/L Account";
                    //GenJournle."Account No." := GenPosSetup."Inventory Adjmt. Account";

                    GenJournle.VALIDATE("Account Type", GenJournle."Bal. Account Type"::"Fixed Asset");
                    GenJournle.Validate("Account No.", "No.");
                    //GenJournle."Depreciation Book Code" := '';
                    GenJournle.VALIDATE("Depreciation Book Code", DepGRec."Depreciation Book Code");
                    GenJournle."FA Posting Type" := GenJournle."FA Posting Type"::"Acquisition Cost";
                    //GenJournle."Gen. Posting Type" := GenJournle."Gen. Posting Type"::Purchase;
                    GenJournle.VALIDATE("Bal. Account Type", GenJournle."Bal. Account Type"::"G/L Account");
                    GenJournle.VALIDATE("Bal. Account No.", GenPosSetup."Inventory Adjmt. Account");
                    //GenJournle."Bal. Gen. Posting Type" := GenJournle."Bal. Gen. Posting Type"::Purchase;
                    GenJournle.VALIDATE(Quantity, 1);
                    GenJournle.VALIDATE(Amount, ItmAmtGVar / ItmQtyGVar);
                    // GenJournle.Insert();
                    QtyPurch += 1;
                    NextGLLineNo += 10000;
                    GenJnLne.RunWithCheck(GenJournle);
                end;
                //END;
                //GenJnLne.RunWithCheck(GenJournle);
                //CODEUNIT.RUN(CODEUNIT::"Gen. Jnl.-Post", GenJournle);

                Blocked := true;
                "Transfered FA Cost" := true;
            end else
                error(ItemError);
        end;
    END;

    procedure CreateItemTracking(IleLPA: Record "Item Ledger Entry"; IJLLpa: Record "Item Journal Line")
    var
        TrackingSpecLv: Record "Tracking Specification";
        ILELv: Record "Item Ledger Entry";
        ReservationEntry: Record "Reservation Entry";
        TsEntryNo: Integer;
    begin
        Clear(TrackingSpecLv);
        ILELv.Reset();
        ILELv.SetRange("Order No.", IleLPA."Order No.");
        ILELv.SetRange("Order Line No.", IleLPA."Order Line No.");
        ILELv.SetRange("Item No.", IleLPA."Item No.");
        if ILELv.FindSet() then
            repeat
                IF ReservationEntry.FINDlast() THEN
                    TsEntryNo := ReservationEntry."Entry No." + 1
                ELSE
                    TsEntryNo := 0;
                clear(ReservationEntry);
                ReservationEntry.INIT();
                ReservationEntry."Entry No." := TsEntryNo;
                if ILELv."Entry Type" = ILELv."Entry Type"::"Negative Adjmt." then
                    ReservationEntry.VALIDATE(Positive, true)
                else
                    ReservationEntry.VALIDATE(Positive, false);
                ReservationEntry.VALIDATE("Item No.", IJLLpa."Item No.");
                ReservationEntry.VALIDATE("Location Code", IJLLpa."Location Code");
                ReservationEntry.VALIDATE("Quantity (Base)", -1 * ILELv.Quantity);
                ReservationEntry.VALIDATE(Quantity, -1 * ILELv.Quantity);
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Prospect);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                ReservationEntry.VALIDATE("Source Type", 83);
                ReservationEntry.VALIDATE("Source Subtype", 5);
                ReservationEntry.VALIDATE("Source ID", IJLLpa."Journal Template Name");
                ReservationEntry.VALIDATE("Source Batch Name", IJLLpa."Journal Batch Name");
                ReservationEntry.VALIDATE("Source Ref. No.", IJLLpa."Line No.");
                ReservationEntry.VALIDATE("Shipment Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Variant code", IJLLpa."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ILELv."Lot No.");
                ReservationEntry."Created By" := copystr(USERID(), 1, 50);
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";
                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.VALIDATE("Appl.-to Item Entry", ILELv."Entry No.");
                ReservationEntry.INSERT();
                TsEntryNo += 1;
            until ILELv.next() = 0;
    end;
    //PKONAU9 >>
    [EventSubscriber(ObjectType::Table, 21, 'OnAfterCopyCustLedgerEntryFromCVLedgEntryBuffer', '', False, False)]
    local procedure OnAfterCopyCustLedgerEntryFromCVLedgEntryBuffer1(var CustLedgerEntry: Record "Cust. Ledger Entry"; CVLedgerEntryBuffer: Record "CV Ledger Entry Buffer")
    begin
        CustLedgerEntry."Bckp Applies-to ID" := CVLedgerEntryBuffer."Applies-to ID";
    end;

    [EventSubscriber(ObjectType::Codeunit, 12, 'OnAfterApplyCustLedgEntry', '', False, False)]
    local procedure OnAfterApplyCustLedgEntry1(var GenJnlLine: Record "Gen. Journal Line"; var NewCVLedgEntryBuf: Record "CV Ledger Entry Buffer"; var OldCustLedgEntry: Record "Cust. Ledger Entry")
    begin
        /*OldCustLedgEntry.CalcFields("Remaining Amount");
        //if OldCustLedgEntry."Remaining Amount" <> 0 then begin//PKONOC20
        if (OldCustLedgEntry."Remaining Amount" <> 0) AND (OldCustLedgEntry."Bckp Applies-to ID" <> '') then begin //PKONOC20
            OldCustLedgEntry."Applies-to ID" := OldCustLedgEntry."Bckp Applies-to ID";
            OldCustLedgEntry."Amount to Apply" := OldCustLedgEntry."Remaining Amount";
        end;
        OldCustLedgEntry."Bckp Applies-to ID" := '';
        OldCustLedgEntry.Modify();*/
        //PKONDE17>>
        OldCustLedgEntry.CalcFields("Remaining Amount", Amount);
        if (OldCustLedgEntry."Remaining Amount" <= OldCustLedgEntry.Amount) AND (OldCustLedgEntry."Bckp Applies-to ID" <> '') then begin //PKONOC20 //PKONDE31
            OldCustLedgEntry."Applies-to ID" := OldCustLedgEntry."Bckp Applies-to ID";
            OldCustLedgEntry."Amount to Apply" := OldCustLedgEntry."Remaining Amount";
        end;
        OldCustLedgEntry."Bckp Applies-to ID" := '';
        OldCustLedgEntry.Modify();
        //PKONDE17<<
    end;
    //PKONAU9 <<
    //BaluOnAug24>>
    [EventSubscriber(ObjectType::Codeunit, 80, 'OnBeforeSalesCrMemoHeaderInsert', '', False, False)]
    local procedure OnBeforeSalesCrMemoPrintableComments(var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean)
    begin
        SalesCrMemoHeader."Printable Comment 1" := SalesHeader."Printable Comment 1";
        SalesCrMemoHeader."Printable Comment 2" := SalesHeader."Printable Comment 2";
    end;
    //Baluonsep8>>
    [EventSubscriber(ObjectType::Table, 81, 'OnAfterCopyGenJnlLineFromSalesHeader', '', false, false)]
    local procedure OnAfterCopyGenJnlLineFromSalesHeaderInsert(SalesHeader: Record "Sales Header"; var GenJournalLine: Record "Gen. Journal Line")
    begin
        GenJournalLine."Printable Comment 1" := SalesHeader."Printable Comment 1";
        GenJournalLine."Printable Comment 2" := SalesHeader."Printable Comment 2";

    end;
    //Baluonsep8>>
    //BaluOnAug24<<
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnPostFixedAssetOnBeforePostVAT', '', false, false)]
    local procedure OnPostFixedAssetOnBeforePostVAT(var GenJournalLine: Record "Gen. Journal Line")
    var
        FixAsset3: Record "Fixed Asset";
    begin
        if (GenJournalLine."Account Type" = GenJournalLine."Account Type"::"Fixed Asset") AND (GenJournalLine."FA Posting Type" = GenJournalLine."FA Posting Type"::Disposal) then begin
            FixAsset3.reset;
            FixAsset3.SetRange("No.", GenJournalLine."Account No.");
            IF FixAsset3.FindFirst() then BEGIN
                FixAsset3.Blocked := true;
                FixAsset3."Approval Status" := FixAsset3."Approval Status"::Open;
                FixAsset3.Inactive := true;
                FixAsset3.Modify();
                //message('%1..second', FANo);
            END;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line2", 'OnPostFixedAssetOnBeforePostVAT', '', false, false)]
    local procedure OnPostFixedAssetOnBeforePostVAT2(var GenJournalLine: Record "Gen. Journal Line")
    var
        FixAsset3: Record "Fixed Asset";
    begin
        if (GenJournalLine."Account Type" = GenJournalLine."Account Type"::"Fixed Asset") AND (GenJournalLine."FA Posting Type" = GenJournalLine."FA Posting Type"::Disposal) then begin
            FixAsset3.reset;
            FixAsset3.SetRange("No.", GenJournalLine."Account No.");
            IF FixAsset3.FindFirst() then BEGIN
                FixAsset3.Blocked := true;
                FixAsset3."Approval Status" := FixAsset3."Approval Status"::Open;
                FixAsset3.Inactive := true;
                FixAsset3.Modify();
                //message('%1..second', FANo);
            END;
        end;
    end;
    //PKONSE17>>
    [EventSubscriber(ObjectType::Codeunit, codeunit::"Sales-Post (Yes/No)", 'OnBeforeConfirmSalesPost', '', false, false)]
    local procedure OnBeforeConfirmSalesPost(var SalesHeader: Record "Sales Header"; var HideDialog: Boolean; var IsHandled: Boolean; var DefaultOption: Integer; var PostAndSend: Boolean)
    begin
        SalesHeader.TestField("Shortcut Dimension 1 Code");
        SalesHeader.TestField("Shortcut Dimension 2 Code");
    end;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Purch.-Post (Yes/No)", 'OnBeforeConfirmPost', '', false, false)]
    local procedure OnBeforeConfirmPost(var PurchaseHeader: Record "Purchase Header"; var HideDialog: Boolean; var IsHandled: Boolean; var DefaultOption: Integer)
    begin
        PurchaseHeader.TestField("Shortcut Dimension 1 Code");
        PurchaseHeader.TestField("Shortcut Dimension 2 Code");
    end;
    //PKONSE17<<
    //PKON22AP11-CR220048 - Need to uncomment below message

    [EventSubscriber(ObjectType::Table, 17, 'OnAfterCopyGLEntryFromGenJnlLine', '', false, false)]
    local procedure OnAfterCopyGLEntryFromGenJnlLine(var GLEntry: Record "G/L Entry"; var GenJournalLine: Record "Gen. Journal Line")
    begin
        GLEntry."Description 2" := GenJournalLine."Description 2";
        // Message('First GL Entry Description %1 and Gen Jour Line Description %2', GLEntry."Description 2", GenJournalLine."Description 2");
    end;

    [EventSubscriber(ObjectType::Codeunit, 5802, 'OnPostInvtPostBufOnAfterInitGenJnlLine', '', false, false)]
    local procedure OnPostInvtPostBufOnAfterInitGenJnlLine(var GenJournalLine: Record "Gen. Journal Line"; var ValueEntry: Record "Value Entry")
    begin
        GenJournalLine."Description 2" := ValueEntry."Description 2";
        //Message('Second GL Entry Description %1 and Value Entry %2', GenJournalLine."Description 2", ValueEntry."Description 2");
    end;
    //PKON22AP11-CR220048

    var
        FAREC: Record "Fixed Asset";
        MrsHdr: Record MRSHeader;
        test: Report "Calculate Depreciation";
        Vend: Record Vendor;
        GenJnlLine: Record "Gen. Journal Line";
        DefaultDimension: Record "Default Dimension";
        CU: Record 7317;
        repor: Report 120;
        Pagetex: Page 1173;

}
