tableextension 50058 ItemBudgetEntryTabExt7134 extends "Item Budget Entry"
{
    fields
    {
        field(50100; "Sales Unit Price"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        modify("Budget Dimension 3 Code")
        {
            trigger OnAfterValidate()
            var
                myInt: Integer;
            begin
                //CRF:2019-0082 SAA 18-09-19 >>
                //assign global dimension 1 for sales area view SAA 13/09/19 >>
                SalesAreaDim.RESET;
                SalesAreaDim.SETRANGE("Dimension Code", 'SALESAREA');
                //SalesAreaDim.SETFILTER(SalesAreaDim."Global Dimension No.",'%1',23);
                //SalesAreaDim.SETRANGE(SalesAreaDim."AccLoc Code",DimensionValue.Code);
                SalesAreaDim.SETRANGE(Code, "Budget Dimension 3 Code");
                IF SalesAreaDim.FINDSET THEN BEGIN
                    IF SalesAreaDim."Accloc code" <> '' THEN
                        VALIDATE("Global Dimension 1 Code", SalesAreaDim."Accloc code");
                END;
                //SAA 13/09/19 <<
                //CRF:2019-0082 SAA 18-09-19 <<
            end;
        }
    }

    var
        SalesAreaDim: Record "Dimension Value";
}