/// <summary>
/// Page URL EndPoint Setup (ID 50103).
/// </summary>
//RFCOutreachAPIGo2solveJuly2023>>>>>>
page 50189 "URL EndPoint Setup"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "End Point URL Setup";
    Caption = 'URL EndPoint Setup';
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            group(General)
            {

                field("Product End Point URL"; Rec."Product End Point URL")
                {
                    ToolTip = 'Specifies the value of the Product End Point field.';
                }
                field("Shipment End Point URL"; Rec."Shipment End Point URL")
                {
                    ToolTip = 'Specifies the value of the Shipment End Point field.';
                }
            }
        }
    }

    trigger OnInit()
    begin
        If Rec.IsEmpty() then
            Rec.Insert();
    end;
}
//RFCOutreachAPIGo2solveJuly2023<<<<<<