pageextension 50268 MyExtension extends "Posted Sales Shpt. Subform"
{
    layout
    {

    }

    actions
    {
        modify(UndoShipment)
        {
            trigger OnBeforeAction()
            begin
                //PKON31052021 >>
                /*SalesShipmntGRec.Reset();
                SalesShipmntGRec.SetRange("No.", "Document No.");
                if SalesShipmntGRec.FindFirst() then begin
                    SalesShipmntGRec.TestField("Approval Status", 2);
                end;*/
                //PKON31052021 >>
                if UserSetup.GET(USERID) then
                    if not UserSetup."Undo Shipment" then
                        ERROR(Text001);
            end;
        }
    }

    var
        myInt: Integer;
        UserSetup: Record "User Setup";
        Text001: Label 'You do not have permission for Undo Shipment Please Enable UndoShipment';
        SalesShipmntGRec: Record "Sales Shipment Header";

}