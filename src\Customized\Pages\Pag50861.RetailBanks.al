page 50861 "Retail Banks"
{
    ApplicationArea = All;
    Caption = 'Retail Banks';
    PageType = List;
    SourceTable = "Retail Banks";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("POS Bank Name"; Rec."POS Bank Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the POS Bank Name field.', Comment = '%';
                }
                field("Bank Account"; Rec."Bank Account")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Bank Account field.', Comment = '%';
                }
            }
        }
    }
}
