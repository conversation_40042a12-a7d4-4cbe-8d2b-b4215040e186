page 50216 "Release Capex Budget"
{
    DeleteAllowed = false;
    Editable = false;
    PageType = Document;
    SourceTable = "Budget Header";
    SourceTableView = WHERE(Status = FILTER(Released));
    UsageCategory = Documents;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field(Description; Description)
                {
                }
                field("Description 2"; "Description 2")
                {
                }
                field("Document Date"; "Document Date")
                {
                }
                field("No. of Revisions"; "No. of Revisions")
                {
                }
                field("Utilisation Purpose"; "Utilisation Purpose")
                {
                    MultiLine = true;
                }
                field("Budget Name"; "Budget Name")
                {
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                }
                field(Status; Status)
                {
                }
            }
            part(CapexLines; "Capex Budget Subform")
            {
                SubPageLink = "Document Type" = FIELD("Document Type"),
                              "Document No." = FIELD("No.");
            }
        }

        area(factboxes)
        {
            part(CapexBudgetListFactbox; "Capex Budget List Factbox")
            {
                ApplicationArea = All;
                Provider = CapexLines;
                SubPageLink = "Document No." = field("Document No.");
            }
            //>>>>>> G2S 122023

            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50100),
                                "No." = FIELD("No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }

            systempart(Notes; Notes)
            {
                ApplicationArea = Notes;

            }
            //<<<<<< G2S 122023
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Capex Budget")
            {
                Caption = '&Capex Budget';
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1102152037)
                {
                }
                action("&Approvals")
                {
                    Caption = '&Approvals';

                    trigger OnAction();
                    var
                        ApprovalEntries: Page "Approval Entries";
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Budget Header", 12, "No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                action(Action1102152038)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        ShowDocDim();
                    end;
                }
            }
            group("&Print")
            {
                Caption = '&Print';
                action("Print Capex Budget")
                {
                    Caption = 'Print Capex Budget';

                    trigger OnAction();
                    begin
                        ReportPrinted := false;
                        CapexBudgetHeaderRec.SETRANGE(CapexBudgetHeaderRec."No.", "No.");
                        if CapexBudgetHeaderRec.FINDFIRST then
                            if CapexBudgetHeaderRec.Status <> CapexBudgetHeaderRec.Status::Open then begin
                                REPORT.RUN(50098, true, true, CapexBudgetHeaderRec);
                                ReportPrinted := true;
                            end else
                                ERROR(Text0003);

                        if ReportPrinted = true then begin
                            "No. of Reprint" := "No. of Reprint" + 1;
                            MODIFY;
                        end;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("&Revise Capex Budget")
                {
                    Caption = '&Revise Capex Budget';

                    trigger OnAction();
                    begin
                        //Commented Temporarily as requested by Fola
                        /*IF Status = Status :: Closed THEN
                          ERROR('%1 %2 is already closed',"Document Type","No.");
                        */

                        if UserSetup.GET(USERID) then begin
                            if UserSetup."Revise Capex Budget" then BEGIN
                                //PhaniFeb192021>>
                                //PerformManualReopen
                                Status := Status::Open;
                                CurrPage.Update(true);
                                //PhaniFeb192021<<
                            end else
                                ERROR(Text0003);
                        end;

                    end;
                }
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("No.", 10, TODAY, TIME, USERID, DATABASE::"Budget Header"); //HO1.0
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Document Type" := "Document Type"::Capex;
        "Dim. Document Type" := "Dim. Document Type"::Capex;
        "Document Date" := WORKDATE;
    end;

    var
        ApprovalMgt: Codeunit 1535;
        ArchiveMgt: Codeunit ArchiveManagement;
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Text0001: Label '''%1'' FA Requisition Created Successfully';
        MRSHeaderRec: Record MRSHeader;
        CapexBudgetHeaderRec: Record "Budget Header";
        Text0002: Label 'Un Approved Capex Cannot be Printed.  Capex No. ''%1'' not approved.';
        ReportPrinted: Boolean;
        UserSetup: Record "User Setup";
        Text0003: Label 'You do not have the permission to Revise the Capex Budget.';
}

