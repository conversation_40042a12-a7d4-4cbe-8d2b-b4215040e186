page 50452 "Import File Card"

{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>ben
    // NYONG   :  NYONG Atanang
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11     -> Form Created to display Import Files.
    // 
    // 1.0      HO        08-Sep-12    -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Import File No.
    // 
    //                    10-Sep-12    -> New field "LC Value Variation" added to the LC tab.
    // 
    // 1.0      NYONG     02-Feb-13    -> Code added to "Onpush()" of new menu item "Send Close Approval Request" in menu button "Function"
    // 3.0      SAA       08-Apr-13    -> Added Menu item "Costing Report History" to the "Import File" menu button.

    InsertAllowed = false;
    PageType = Card;
    RefreshOnActivate = true;
    SourceTable = "Vendor 2";
    SourceTableView = WHERE("Vendor Type" = FILTER("Import File"));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {

                    ApplicationArea = All;
                    trigger OnAssistEdit();
                    begin
                        /*IF AssistEdit(xRec) THEN
                          CurrPage.UPDATE;
                          */

                    end;
                }
                field(Name; Name)
                {
                    Caption = 'Description';
                    ApplicationArea = All;
                }
                field("ORP No."; "ORP No.")
                {
                    ApplicationArea = All;
                }
                field("Operation File No."; "Operation File No.")
                {
                    ApplicationArea = All;
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                    Editable = true;
                    ApplicationArea = All;
                }
                field("Vendor Posting Group"; "Vendor Posting Group")
                {
                    Editable = true;
                    ApplicationArea = All;
                }
                field("Search Name"; "Search Name")
                {
                    ApplicationArea = All;
                }
                field("Country/Region Code"; "Country/Region Code")
                {
                    ApplicationArea = All;
                }
                field(Blocked; Blocked)
                {
                    ApplicationArea = All;
                }
                field(Status; Status)
                {
                    ApplicationArea = All;
                }
                field("Last Date Modified"; "Last Date Modified")
                {
                    ApplicationArea = All;
                }
                /*field("No. of Revision"; "No. of Revision")
                {
                }*/
                field("Order No."; "Order No.")
                {
                    trigger OnValidate()
                    var
                        myInt: Integer;
                    begin
                        UpdateLines();
                    end;

                }
            }
            part(Control1000000006; "Import File Subform - CHI")
            {
                SubPageLink = "Document No." = FIELD("No.");
            }
            part(ImportFileSubform; "Import File subform")
            {
                SubPageLink = "Import File No." = FIELD("No.");
                Caption = 'Posted Details';
            }
            group("Form M")
            {
                Caption = 'Form M';
                field("Order Value"; "Order Value")
                {
                    ApplicationArea = All;
                }
                field("PFI No."; "PFI No.")
                {
                    ApplicationArea = All;
                }
                field("PFI Date"; "PFI Date")
                {
                    ApplicationArea = All;
                }
                field("Import Currency Code"; "Import Currency Code")
                {
                    ApplicationArea = All;
                }
                field("Form M No."; "Form M No.")
                {
                    ApplicationArea = All;
                }
                field("Form M Submitted Date"; "Form M Submitted Date")
                {
                    ApplicationArea = All;
                }
                field("Form M Approval No."; "Form M Approval No.")
                {
                    ApplicationArea = All;
                }
                field("Form M Approved Date"; "Form M Approved Date")
                {
                    ApplicationArea = All;
                }
                field("Form M Expiry Date"; "Form M Expiry Date")
                {
                    ApplicationArea = All;
                }
            }
            group(LC)
            {
                Caption = 'LC';
                field("Bank No."; "Bank No.")
                {
                    ApplicationArea = All;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = All;
                }
                field("Facility Bank No."; "Facility Bank No.")
                {
                    ApplicationArea = All;
                }
                field("Facility Bank Name"; "Facility Bank Name")
                {
                    ApplicationArea = All;
                }
                field("Facility Code"; "Facility Code")
                {
                    ApplicationArea = All;
                }
                field("LC / BC Ref No."; "LC / BC Ref No.")
                {
                    ApplicationArea = All;
                }
                field("Import Payment Term Code"; "Import Payment Term Code")
                {
                    ApplicationArea = All;
                }
                field("LC Value Variation"; "LC Value Variation")
                {
                    ApplicationArea = All;
                }
                field("LC Opening Date"; "LC Opening Date")
                {
                    ApplicationArea = All;
                }
                field("LC Expiry Period"; "LC Expiry Period")
                {
                    ApplicationArea = All;
                }
                field("LC Expiry Date"; "LC Expiry Date")
                {
                    ApplicationArea = All;
                }
                field("LC Amount"; "LC Amount")
                {
                    ApplicationArea = All;
                }
            }
            group(Insurance)
            {
                Caption = 'Insurance';
                field("Insurance Policy No."; "Insurance Policy No.")
                {
                    ApplicationArea = All;
                }
                field("Insurance Company"; "Insurance Company")
                {
                    ApplicationArea = All;
                }
                field("Insurance Date"; "Insurance Date")
                {
                    ApplicationArea = All;
                }
                field("Inspection Agent Code"; "Inspection Agent Code")
                {
                    ApplicationArea = All;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50398),
                                "No." = FIELD("No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Import File")
            {
                Caption = '&Import File';
                action("Ledger E&ntries")
                {
                    Caption = 'Ledger E&ntries';
                    RunObject = Page "Vendor Ledger Entries";
                    RunPageLink = "Vendor No." = FIELD("No.");
                    RunPageView = SORTING("Vendor No.", "Posting Date", "Currency Code");
                    ShortCutKey = 'Ctrl+F5';
                }
                action("Co&mments")
                {
                    Caption = 'Co&mments';
                    RunObject = Page 124;
                    RunPageLink = "Table Name" = CONST(Vendor),
                                  "No." = FIELD("No.");
                }
                action("Unposted Clearing File")
                {
                    Caption = 'Unposted Clearing File';
                    RunObject = Page "Clearing List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = SORTING("No.")
                                  ORDER(Ascending);
                    //WHERE(Status = FILTER(<> Posted));
                }
                action("Posted Clearing File")
                {
                    Caption = 'Posted Clearing File';
                    RunObject = Page "Clearing List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = SORTING("No.")
                                  ORDER(Ascending);
                    //WHERE(Status = FILTER(Posted));
                }
                separator(Separator11)
                {
                }
                action(Receipts)
                {
                    Caption = 'Receipts';

                    trigger OnAction();
                    begin
                        ShowFormMPostedReceipts;
                    end;
                }
                action(Invoices)
                {
                    Caption = 'Invoices';

                    trigger OnAction();
                    begin
                        ShowFormMPostedInvoices;
                    end;
                }
                action("Charge Invoices")
                {
                    Caption = 'Charge Invoices';

                    trigger OnAction();
                    begin
                        ShowFormMPostedInvoices();
                    end;
                }
                separator(Separator1000000029)
                {
                }
                action("Credit Memo's")
                {
                    Caption = 'Credit Memo''s';

                    trigger OnAction();
                    begin
                        ShowFormMPostedCrMemo;
                    end;
                }
                action("Charge Credit Memo's")
                {
                    Caption = 'Charge Credit Memo''s';

                    trigger OnAction();
                    begin
                        ShowFormMPostedChrgCrMemo;
                    end;
                }
                separator(Separator1102152064)
                {
                }
                action("&Approvals")
                {
                    Caption = '&Approvals';

                    trigger OnAction();
                    var
                        ApprovalEntries: Page 658;
                    begin

                        ApprovalEntries.Setfilters(DATABASE::"Vendor 2", 37, "No.");
                        ApprovalEntries.RUN;//CHI WF
                    end;
                }
                action("Costing Report")
                {
                    Caption = 'Costing Report';

                    trigger OnAction();
                    begin
                        valueentry.SETRANGE("Import File No.", "No.");
                        REPORT.RUNMODAL(50022, true, true, valueentry); //>>>>>> 260225 G2S CAS-01403-R5R3D6 
                    end;
                }
                action("Costing Report History")
                {
                    Caption = 'Costing Report History';

                    trigger OnAction();
                    begin
                        //valueentryhistory.SETRANGE("IPO File No.", "No.");
                        //REPORT.RUNMODAL(50213, true, true, valueentryhistory);
                    end;
                }
            }
            group("&Purchases")
            {
                Caption = '&Purchases';
                action(Quotes)
                {
                    Caption = 'Quotes';
                    RunObject = Page "Purchase List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = WHERE("Document Type" = CONST(Quote));
                }
                action(Orders)
                {
                    Caption = 'Orders';
                    RunObject = Page "Purchase List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = WHERE("Document Type" = CONST(Order));
                }
                action("Charge - Invoices")
                {
                    Caption = 'Charge - Invoices';
                    RunObject = Page "Purchase List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = WHERE("Document Type" = CONST(Invoice));
                }
                separator(Separator1000000032)
                {
                }
                action(Action1000000033)
                {
                    Caption = 'Credit Memo''s';
                    RunObject = Page "Purchase List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = WHERE("Document Type" = CONST("Credit Memo"));
                }
                action(Action1000000034)
                {
                    Caption = 'Charge Credit Memo''s';
                    RunObject = Page "Purchase List";
                    RunPageLink = "Import File No." = FIELD("No.");
                    RunPageView = WHERE("Document Type" = CONST("Credit Memo"));
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Create Clearing")
                {
                    Caption = 'Create Clearing';

                    trigger OnAction();
                    begin
                        CreateClearing;
                    end;
                }
                separator("-")
                {
                    Caption = '-';
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()

                    begin
                        CheckMandotryFileds();
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendIFCforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            VALIDATE(Status, Status::Released);
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();

                    begin
                        IF Status = Status::"Pending Approval" THEN
                            ERROR('You can not reopen the document when approval status is in %1', Status);
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50398);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                action("Send A&pproval Request")
                {
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    begin
                        CheckMandotryFileds();
                        if Status = Status::Blocked then
                            ERROR('Import File %1 is Blocked', "No.");
                        IF allinoneCU.CheckIFCApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendIFCForApproval(Rec);
                        //IF ApprovalMgt.SendImpFileApprovalRequest(Rec) THEN;   
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin
                        if Status = Status::Blocked then
                            ERROR('Import File %1 is Blocked', "No.");
                        allinoneCU.OnCancelIFCForApproval(Rec);
                        //IF ApprovalMgt.CancelImpFileApprovalRequest(Rec,TRUE,TRUE) THEN;
                    end;
                }
                separator(Separator1102152061)
                {
                }
                /*action("Re&lease")
                {
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        if Status = Status::Blocked then
                            ERROR('Import File %1 is Blocked', "No.");
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendLSPforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');
                        ImpFilePerformManualRelease;
                    end;
                }*/
                action("Re&vise Import File")
                {
                    Caption = 'Re&vise Import File';

                    trigger OnAction();
                    var
                        test: Codeunit 74;
                    begin
                        if Status = Status::Blocked then
                            ERROR('Import File %1 is Blocked', "No.");

                        ImpFilePerformManualReopen;
                    end;
                }
                action("Send Close Approval Request")
                {
                    Caption = 'Send Close Approval Request';

                    trigger OnAction();
                    begin
                        //NYONG1.0 >>
                        if Status = Status::Closed then
                            ERROR(Text003, "No.");
                        //IF ApprovalMgt.SendClImportApprovalRequest(Rec) THEN;
                        //NYONG1.0 <<
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        OnAfterGetCurrRecord;
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("No.", 29, TODAY, TIME, USERID, DATABASE::Vendor); //HO1.0
    end;

    trigger OnFindRecord(Which: Text): Boolean;
    var
        RecordFound: Boolean;
    begin
        RecordFound := FIND(Which);
        CurrPage.EDITABLE := RecordFound or (GETFILTER("No.") = '');
        exit(RecordFound);
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Vendor Type" := "Vendor Type"::"Import File";
        OnAfterGetCurrRecord;
    end;

    trigger OnOpenPage();
    begin
        ActivateFields;

        SETRANGE("Vendor Type", "Vendor Type"::"Import File");
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        //TestField("Approval Status", "Approval Status"::Open);
    END;

    var
        CalendarMgmt: Codeunit "Calendar Management";
        PaymentToleranceMgt: Codeunit "Payment Tolerance Management";
        CustomizedCalEntry: Record "Customized Calendar Entry";
        CustomizedCalendar: Record "Customized Calendar Change";
        Text001: Label 'Do you want to allow payment tolerance for entries that are currently open?';
        Text002: Label 'Do you want to remove payment tolerance from entries that are currently open?';
        ApprovalMgt: Codeunit 1535;
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        valueentry: Record "Value Entry";
        Text003: Label 'Import File No. %1 has been Closed.';
        PurchaseHeader: Record Vendor;
        allinoneCU: codeunit Codeunit1;
        RecordRest: record "Restricted Record";
        WorkflowManagement: Codeunit "Workflow Management";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
    //valueentryhistory: Record "Value Entry History";

    procedure ActivateFields();
    begin
    end;

    local procedure OnAfterGetCurrRecord();
    begin
        xRec := Rec;
        ActivateFields;
    end;

    local procedure CheckMandotryFileds()
    var
        myInt: Integer;
    begin
        TestField("PFI No.");
        TestField("PFI Date");
        TestField("Import Currency Code");
        TestField("Form M No.");
        TestField("Form M Submitted Date");
        TestField("Form M Approved Date");
        TestField("Form M Approval No.");
        TestField("Bank No.");
        TestField("Bank Name");
        TestField("Facility Bank No.");
        TestField("Facility Bank Name");
        TestField("Facility Code");
        TestField("LC / BC Ref No.");
        TestField("Import Payment Term Code");
        TestField("LC Opening Date");
        TestField("LC Expiry Period");
        TestField("LC Amount");
        TestField("Insurance Policy No.");
        TestField("Insurance Company");
        TestField("Insurance Date");
        TestField("Inspection Agent Code");
    end;

    local procedure UpdateLines()
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        ImportFileLine: Record "Import File Line";
        ImportFileLine2: Record "Import File Line";
        LineNo: Integer;
    begin
        PurchaseLine.Reset();
        PurchaseLine.SetRange("Document Type", PurchaseLine."Document Type"::Order);
        PurchaseLine.SetRange("Document No.", "Order No.");
        PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        if PurchaseLine.FindSet() then
            repeat
                ImportFileLine2.Reset();
                ImportFileLine2.SetRange("No.", "No.");
                if ImportFileLine2.FindLast() then
                    LineNo := ImportFileLine2."Line No.";
                LineNo += 1000;
                ImportFileLine2.Reset();
                ImportFileLine2.SetRange("No.", "No.");
                ImportFileLine2.SetRange("Order No.", PurchaseLine."Document No.");
                ImportFileLine2.SetRange("Order Line No.", PurchaseLine."Line No.");
                if not ImportFileLine2.FindFirst() then begin
                    ImportFileLine.Init();
                    ImportFileLine."Document No." := "No.";
                    ImportFileLine."Line No." := LineNo;
                    ImportFileLine."Document Type" := ImportFileLine."Document Type"::Order;
                    ImportFileLine."Order No." := PurchaseLine."Document No.";
                    ImportFileLine."Order Line No." := PurchaseLine."Line No.";
                    ImportFileLine.Type := ImportFileLine.Type::Item;
                    ImportFileLine."No." := PurchaseLine."No.";
                    ImportFileLine.Description := PurchaseLine.Description;
                    ImportFileLine."Description 2" := PurchaseLine."Description 2";
                    ImportFileLine.Quantity := PurchaseLine.Quantity;
                    ImportFileLine."Direct Unit Cost" := PurchaseLine."Direct Unit Cost";
                    ImportFileLine."Item Category Code" := PurchaseLine."Item Category Code";
                    ImportFileLine."Unit of Measure Code" := PurchaseLine."Unit of Measure Code";
                    ImportFileLine.Insert();
                end;
            until PurchaseLine.Next() = 0;
    end;

}

