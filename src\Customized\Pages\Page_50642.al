page 50642 MaintenanceLedEntry
{
    //AutoSplitKey = true;
    Caption = 'Maintenance Ledger Entry 2';
    //DelayedInsert = true;
    LinksAllowed = false;
    ApplicationArea = ALL;
    UsageCategory = Lists;
    PageType = List;
    Editable = TRUE;
    SourceTable = "Maintenance Ledger Entry";
    Permissions = TableData "Maintenance Ledger Entry" = rimd;    //SourceTableView = WHERE("Statement Type" = CONST("Bank Reconciliation"));

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                ShowCaption = false;
                field("FA No."; "FA No.")
                {
                    ApplicationArea = ALL;
                }
                field("G/L Entry No."; "G/L Entry No.")
                {
                    ApplicationArea = ALL;
                }
                field(Allocated; Allocated)
                {
                    ApplicationArea = ALL;
                }

            }
        }
    }

}
