page 50160 "Request Teller Receipt"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER      SIGN        DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      HO       18-Jun-13    -> Form created for Request Teller Receipt functionality.
    // CRF:2019-0090  SAA 28-10-19    -> New Controls added "Currency Code","Narration","Teller Amount (LCY)"

    Caption = 'Request Teller Receipt';
    PageType = Card;
    SourceTable = "Request Teller Receipt";
    SourceTableView = WHERE("Released for Confirmation" = FILTER(false),
                            "Teller Returned" = FILTER(false),
                            "Credit Account Type" = FILTER(" " | Customer));
    UsageCategory = tasks;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = true;
                    trigger OnValidate()
                    var
                        ResponsibilityCenter: Record "Responsibility Center";
                    begin
                        if ResponsibilityCenter.GET("Responsibility Center") then
                            "Global Dimension 1 Code" := ResponsibilityCenter."Global Dimension 1 Code"
                        else
                            clear("Global Dimension 1 Code");
                    end;

                    trigger OnLookup(var Text: Text): Boolean;
                    var
                        ResponsibilityCenter: Record "Responsibility Center";
                    begin
                        RespCent.SETCURRENTKEY(Code);
                        /*Prasanna
                        if RespCent.FINDSET then
                            repeat
                                if UserIDRespCent.GET(USERID, RespCent.Code) then
                                    RespCent.MARK(true);
                            until RespCent.NEXT = 0;

                        RespCent.MARKEDONLY(true);*/
                        if PAGE.RUNMODAL(0, RespCent) = ACTION::LookupOK then
                            "Responsibility Center" := RespCent.Code;
                        IF ResponsibilityCenter.GET("Responsibility Center") THEN
                            "Global Dimension 1 Code" := ResponsibilityCenter."Global Dimension 1 Code"
                        else
                            CLEAR("Global Dimension 1 Code");

                    end;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        VALIDATE("Responsibility Center");
                    end;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                }
                field("Paid By"; "Paid By")
                {
                    ApplicationArea = all;
                }
                field("Reason for Return"; "Reason for Return")
                {
                    ApplicationArea = all;
                    Editable = false;
                    MultiLine = true;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                    MultiLine = true;
                }
            }
            group("Teller Details")
            {
                Caption = 'Teller Details';
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                }
                field("Chq. Value Date"; "Chq. Value Date")
                {
                    ApplicationArea = all;
                }
                field("Credit Account Type"; "Credit Account Type")
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;

                    trigger OnDrillDown();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", TODAY);
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    trigger onvalidate()
                    var
                        TellNum: integer;
                        TellNumErr: label 'Please Enter Integer Value in Teller No.';
                    BEGIN
                        /*
                        IF NOT Evaluate(TellNum, "Teller No.") then
                            Error(TellNumErr);*///b2bpksalecorr11
                    END;


                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                }
                field("Teller Amount(LCY)"; "Teller Amount(LCY)")
                {
                    ApplicationArea = all;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                }
            }
            group("User Trail")
            {
                Caption = 'User Trail';
                field("Created by"; "Created by")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Last Modified By"; "Last Modified By")
                {
                    ApplicationArea = all;
                }
                field("Last Modified Date"; "Last Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Released By"; "Released By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released Date"; "Released Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Released time"; "Released time")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';

                    trigger OnAction();
                    begin
                        PerformManualRelease;
                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        //"Responsibility Center" := UserMgt.GetBankConfRegFilter();
        "Credit Account Type" := "Credit Account Type"::Customer;
    end;

    trigger OnOpenPage();
    begin
        /*
        IF UserMgt.GetBankConfRegFilter() <> '' THEN BEGIN
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMgt.GetBankConfRegFilter());
          FILTERGROUP(0);
        END;
        */

        if UserSetup.GET(USERID) then
            if not UserSetup."Request for Teller Receipt" then
                ERROR(Text001);

    end;

    var
        UserSetup: Record "User Setup";
        UserMgt: Codeunit "User Setup Management";
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";
        RespCent: Record "Responsibility Center";
        Text001: Label 'You do not have permission for Request for Teller Receipt.';
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
        ChangeExchangeRate: Page "Change Exchange Rate";
}

