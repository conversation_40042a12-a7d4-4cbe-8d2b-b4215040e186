pageextension 50128 ReclasJourExt extends "Item Reclass. Journal"
{

    actions
    {
        /*Modify(Post)
        {
            trigger OnBeforeAction()
            var
                myInt: Integer;
            begin
                CheckDimensions();
            end;
        }
        Modify("Post and &Print")
        {
            trigger OnBeforeAction()
            var
                myInt: Integer;
            begin
                CheckDimensions();
            end;
        }
        Modify("P&osting")
        {
            trigger OnBeforeAction()
            var
                myInt: Integer;
            begin
                CheckDimensions();
            end;
        }*/
        addafter("&Line")
        {
            group("Request Approval")
            {
                Caption = 'Request Approval';
                group(SendApprovalRequest)
                {
                    /*Caption = 'Send Approval Request';
                    Image = SendApprovalRequest;
                    action(SendApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Journal Batch';
                        Enabled = NOT OpenApprovalEntriesOnBatchOrAnyJnlLineExist;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        begin
                            IJLSubEvents.TrySendItemJournalBatchApprovalRequest(Rec);
                            SetControlAppearance();
                        end;
                    }*/
                    action(SendApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Selected Journal Lines';
                        Enabled = NOT OpenApprovalEntriesOnBatchOrCurrJnlLineExist;
                        Image = SendApprovalRequest;
                        ToolTip = 'Send selected journal lines for approval.';

                        trigger OnAction()
                        var
                            ItemJournalLine: Record "Item Journal Line";
                        begin
                            CheckDimensions();
                            GetCurrentlySelectedLines(ItemJournalLine);
                            IJLSubEvents.TrySendItemJournalLineApprovalRequests(ItemJournalLine);
                        end;
                    }
                }
                group(CancelApprovalRequest)
                {
                    Caption = 'Cancel Approval Request';
                    Image = Cancel;
                    action(CancelApprovalRequestJournalBatch)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Journal Batch';
                        Enabled = OpenApprovalEntriesOnJnlBatchExist;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending all journal lines for approval, also those that you may not see because of filters.';

                        trigger OnAction()
                        begin
                            IJLSubEvents.TryCancelItemJournalBatchApprovalRequest(Rec);
                            SetControlAppearance();
                        end;
                    }
                    action(CancelApprovalRequestJournalLine)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Selected Journal Lines';
                        Enabled = OpenApprovalEntriesOnJnlLineExist;
                        Image = CancelApprovalRequest;
                        ToolTip = 'Cancel sending selected journal lines for approval.';

                        trigger OnAction()
                        var
                            ItemJournalLine: Record "Item Journal Line";
                        begin
                            GetCurrentlySelectedLines(ItemJournalLine);
                            IJLSubEvents.TryCancelItemJournalLineApprovalRequests(ItemJournalLine);
                        end;
                    }
                }
                group(Approval)
                {
                    Caption = 'Approval';
                    action(Approve)
                    {
                        ApplicationArea = All;
                        Caption = 'Approve';
                        Image = Approve;
                        Promoted = true;
                        PromotedCategory = Category7;
                        PromotedIsBig = true;
                        PromotedOnly = true;
                        ToolTip = 'Approve the requested changes.';
                        Visible = OpenApprovalEntriesExistForCurrUser;

                        trigger OnAction()
                        var
                            ItemJournalBatch: Record "Item Journal Batch";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name");
                            //IF NOT ApprovalsMgmt.ApproveRecordApprovalRequest(ItemJournalBatch.RECORDID) THEN Need to clear this-Prasanna
                            ApprovalsMgmt.ApproveRecordApprovalRequest(RECORDID());
                        end;
                    }
                    action(Reject)
                    {
                        ApplicationArea = All;
                        Caption = 'Reject';
                        Image = Reject;
                        Promoted = true;
                        PromotedCategory = Category7;
                        PromotedIsBig = true;
                        PromotedOnly = true;
                        ToolTip = 'Reject the approval request.';
                        Visible = OpenApprovalEntriesExistForCurrUser;

                        trigger OnAction()
                        var
                            ItemJournalBatch: Record "Item Journal Batch";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name");
                            //IF NOT ApprovalsMgmt.RejectRecordApprovalRequest(ItemJournalBatch.RECORDID) THENNeed to clear this-Prasanna
                            ApprovalsMgmt.RejectRecordApprovalRequest(RECORDID());
                        end;
                    }
                    action(Delegate)
                    {
                        ApplicationArea = All;
                        Caption = 'Delegate';
                        Image = Delegate;
                        Promoted = true;
                        PromotedCategory = Category7;
                        PromotedOnly = true;
                        ToolTip = 'Delegate the approval to a substitute approver.';
                        Visible = OpenApprovalEntriesExistForCurrUser;

                        trigger OnAction()
                        var
                            ItemJournalBatch: Record "Item Journal Batch";
                            ApprovalsMgmt: Codeunit "Approvals Mgmt.";
                        begin
                            ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name");
                            //IF NOT ApprovalsMgmt.DelegateRecordApprovalRequest(ItemJournalBatch.RECORDID) THENTHENNeed to clear this-Prasanna
                            ApprovalsMgmt.DelegateRecordApprovalRequest(RECORDID());
                            //DelegateGenJournalLineRequest(Rec); Old code for above code in 2016
                        end;
                    }
                }
            }

        }
    }

    var
        IJLSubEvents: codeunit IJLSubEvents;
        OpenApprovalEntriesExistForCurrUser: Boolean;
        OpenApprovalEntriesOnJnlBatchExist: Boolean;
        OpenApprovalEntriesOnJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist: Boolean;
        OpenApprovalEntriesOnBatchOrAnyJnlLineExist: Boolean;

    trigger OnOpenPage()
    begin
        SetControlAppearance();
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetControlAppearance();
    end;

    Procedure SetControlAppearance()
    var
        ItemJournalBatch: Record "Item Journal Batch";
        ApprovalsMgmt: Codeunit "Approvals Mgmt.";
    Begin
        IF ItemJournalBatch.GET("Journal Template Name", "Journal Batch Name") THEN BEGIN
            //ShowWorkflowStatusOnBatch := CurrPage.WorkflowStatusBatch.PAGE.SetFilterOnWorkflowRecord(ItemJournalBatch.RECORDID()); Need to add List Part
            OpenApprovalEntriesExistForCurrUser := ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(ItemJournalBatch.RECORDID());
            OpenApprovalEntriesOnJnlBatchExist := ApprovalsMgmt.HasOpenApprovalEntries(ItemJournalBatch.RECORDID());
        END;
        OpenApprovalEntriesExistForCurrUser :=
          OpenApprovalEntriesExistForCurrUser OR
          ApprovalsMgmt.HasOpenApprovalEntriesForCurrentUser(RECORDID());

        OpenApprovalEntriesOnJnlLineExist := ApprovalsMgmt.HasOpenApprovalEntries(RECORDID());
        OpenApprovalEntriesOnBatchOrCurrJnlLineExist := OpenApprovalEntriesOnJnlBatchExist OR OpenApprovalEntriesOnJnlLineExist;

        OpenApprovalEntriesOnBatchOrAnyJnlLineExist :=
          OpenApprovalEntriesOnJnlBatchExist OR
                                            IJLSubEvents.HasAnyOpenItemJournalLineApprovalEntries("Journal Template Name", "Journal Batch Name");

        //ShowWorkflowStatusOnLine := CurrPage.WorkflowStatusLine.PAGE.SetFilterOnWorkflowRecord(RECORDID());; Need to add List Part
    end;

    Procedure GetCurrentlySelectedLines(VAR ItemJournalLine: Record "Item Journal Line"): Boolean
    BEGIN
        CurrPage.SETSELECTIONFILTER(ItemJournalLine);
        EXIT(ItemJournalLine.FINDSET());
    END;

    Procedure CheckDimensions()
    var
        IJNL: record "Item Journal line";

    begin
        IJNL.reset;
        IJNL.Setrange("Journal Template Name", "Journal Template Name");
        IJNL.setrange("Journal Batch Name", "Journal Batch Name");
        IF IJNL.Findset then
            Repeat
                IF (IJNL."Shortcut Dimension 1 Code" = '') OR (IJNL."Shortcut Dimension 2 Code" = '')
                OR (IJNL."New Shortcut Dimension 1 Code" = '') OR (IJNL."New Shortcut Dimension 2 Code" = '') then
                    ERROR('AccLocation, NewACCLoction, CC Code & New CC Code must not be Blank for Line No. %1', IJNL."Line No.");
            until IJNL.NEXT = 0;

    end;
}
