page 50967 ModifyMasterDataHistoryView
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Master Template Line";
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;
    //Permissions = tabledata "Sales Invoice Line" = rd;

    layout
    {
        area(Content)
        {
            repeater(Details)
            {
                field("Data Template Code"; "Data Template Code")
                {
                    ApplicationArea = All;

                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;

                }
                field(Type; Type)
                {
                    ApplicationArea = All;
                }
                field(FieldID; FieldID)
                {
                    ApplicationArea = All;
                }
                field("Field Name"; "Field Name")
                {
                    ApplicationArea = All;
                }
                field(TableID; TableID)
                {
                    ApplicationArea = All;
                }
                field("Table Name"; "Table Name")
                {
                    ApplicationArea = All;
                }
                field(Template; Template)
                {
                    ApplicationArea = All;
                }
                field("Template Description"; "Template Description")
                {
                    ApplicationArea = All;
                }
                field(Mandatory; Mandatory)
                {
                    ApplicationArea = All;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = All;
                }
                field("Default Value"; "Default Value")
                {
                    ApplicationArea = All;
                }
                field("Table Caption"; "Table Caption")
                {
                    ApplicationArea = All;
                }
                field("Field Caption"; "Field Caption")
                {
                    ApplicationArea = all;
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = All;
                }
                field("Sub Doc. Type"; "Sub Doc. Type")
                {
                    ApplicationArea = All;
                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = All;
                }
                field("New Value"; "New Value")
                {
                    ApplicationArea = All;
                }
                field("Master Type"; "Master Type")
                {
                    ApplicationArea = All;
                }
                field("Master No."; "Master No.")
                {
                    ApplicationArea = All;
                }
                field("Customer New Value"; "Customer New Value")
                {
                    ApplicationArea = All;
                }
                field("Vendor New Value"; "Vendor New Value")
                {
                    ApplicationArea = All;
                }
                field("Fixed Asset New Value"; "Fixed Asset New Value")
                {
                    ApplicationArea = All;
                }
                field("Bank Account New Value"; "Bank Account New Value")
                {
                    ApplicationArea = All;
                }
                field("Location New Value"; "Location New Value")
                {
                    ApplicationArea = All;
                }
                field("GL Account New Value"; "GL Account New Value")
                {
                    ApplicationArea = All;
                }
                field("Master Process Type"; "Master Process Type")
                {
                    ApplicationArea = All;
                }
                field("Reason for Modification"; "Reason for Modification")
                {
                    ApplicationArea = All;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = All;
                }
                field("Modified On"; "Modified On")
                {
                    ApplicationArea = All;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = All;
                }
                field("Created On"; "Created On")
                {
                    ApplicationArea = All;
                }
                field("Master No. Created"; "Master No. Created")
                {
                    ApplicationArea = All;
                }
                field("Master Name Created"; "Master Name Created")
                {
                    ApplicationArea = All;
                }
                field("Master Description"; "Master Description")
                {
                    ApplicationArea = All;
                }
                field("Master Created"; "Master Created")
                {
                    ApplicationArea = All;
                }
                field("Master Modified"; "Master Modified")
                {
                    ApplicationArea = all;
                }
                field("Master Edited On"; "Master Edited On")
                {
                    ApplicationArea = all;
                }
                field("Master Edited By"; "Master Edited By")
                {
                    ApplicationArea = all;
                }
                field("Master No. created by"; "Master No. created by")
                {
                    ApplicationArea = all;
                }
                field("Master No. created on"; "Master No. created on")
                {
                    ApplicationArea = all;
                }
                field("Master No. Created."; "Master No. Created.")
                {
                    ApplicationArea = all;
                }
                field("Master Name Created."; "Master Name Created.")
                {
                    ApplicationArea = all;
                }
                field("Master Description."; "Master Description.")
                {
                    ApplicationArea = all;
                }
                field("Master Created."; "Master Created.")
                {
                    ApplicationArea = all;
                }
                field("Master Modified."; "Master Modified.")
                {
                    ApplicationArea = all;
                }
                field("Master Edited On."; "Master Edited On.")
                {
                    ApplicationArea = all;
                }
                field("Master Edited By."; "Master Edited By.")
                {
                    ApplicationArea = all;
                }
                field("Master No. created by."; "Master No. created by.")
                {
                    ApplicationArea = all;
                }
                field("Master No. created on."; "Master No. created on.")
                {
                    ApplicationArea = all;
                }
                field(MasterProcessType; MasterProcessType)
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field(TableNames; TableNames)
                {
                    ApplicationArea = all;
                }
                field(MasterModified; MasterModified)
                {
                    ApplicationArea = all;
                }


            }
        }

    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    var
        MasterDatTemplateRec: Record "Master Data Template";
    begin
        MasterDatTemplateRec.Reset();
        MasterDatTemplateRec.SetRange("No.", "Data Template Code");
        MasterDatTemplateRec.SetRange(TableID, TableID);
        MasterDatTemplateRec.SetRange("Master Type", "Master Type");
        MasterDatTemplateRec.SetRange("Master No.", "Master No.");
        if MasterDatTemplateRec.FindFirst() then begin
            //NoVar := MasterDatTemplateRec."No.";
            MasterProcessType := MasterDatTemplateRec."Master Process Type";
            //MasterType := MasterDatTemplateRec."Master Type";
            //MasterNo := MasterDatTemplateRec."Master No.";
            Description := MasterDatTemplateRec.Description;
            TableNames := MasterDatTemplateRec."Table Name";
            MasterModified := MasterDatTemplateRec."Master Modified";
        end;

    end;

    var
        myInt: Integer;
        MasterDatTemplateRec: Record "Master Data Template";
        NoVar: Code[20];
        MasterProcessType: Option;
        MasterType: Option;
        MasterNo: Code[20];
        Description: Text[100];
        TableNames: Text[30];
        MasterModified: Boolean;
}