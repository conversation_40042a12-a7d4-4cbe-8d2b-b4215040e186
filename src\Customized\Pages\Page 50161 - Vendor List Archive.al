page 50907 "Vendor List Archive"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      HO        30-Jan-12      -> Form Created for Vendor List Archive.

    Caption = 'Vendor List Archive';
    Editable = false;
    PageType = List;
    SourceTable = "Vendor Archive";
    UsageCategory = Lists;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                }
                field(Name; Name)
                {
                }
                field("No. of Revision"; "No. of Revision")
                {
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                }
                field("Archive By"; "Archive By")
                {
                }
                field("Time Archive"; "Time Archive")
                {
                }
                field("Date Archive"; "Date Archive")
                {
                }
                field("Location Code"; "Location Code")
                {
                }
                field("Post Code"; "Post Code")
                {
                    Visible = false;
                }
                field("Country/Region Code"; "Country/Region Code")
                {
                    Visible = false;
                }
                field("Phone No."; "Phone No.")
                {
                }
                field("Fax No."; "Fax No.")
                {
                    Visible = false;
                }
                field("IC Partner Code"; "IC Partner Code")
                {
                    Visible = false;
                }
                field(Contact; Contact)
                {
                }
                field("Purchaser Code"; "Purchaser Code")
                {
                    Visible = false;
                }
                field("Vendor Posting Group"; "Vendor Posting Group")
                {
                    Visible = false;
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                    Visible = false;
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                    Visible = false;
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                    Visible = false;
                }
                field("Fin. Charge Terms Code"; "Fin. Charge Terms Code")
                {
                    Visible = false;
                }
                field("Currency Code"; "Currency Code")
                {
                    Visible = false;
                }
                field("Language Code"; "Language Code")
                {
                    Visible = false;
                }
                field("Search Name"; "Search Name")
                {
                }
                field(Blocked; Blocked)
                {
                    Visible = false;
                }
                field("Last Date Modified"; "Last Date Modified")
                {
                    Visible = false;
                }
                field("Application Method"; "Application Method")
                {
                    Visible = false;
                }
                field("Shipment Method Code"; "Shipment Method Code")
                {
                    Visible = false;
                }
                field("Lead Time Calculation"; "Lead Time Calculation")
                {
                    Visible = false;
                }
                field("Base Calendar Code"; "Base Calendar Code")
                {
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("&Card")
                {
                    Caption = '&Card';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        if "Vendor Type_" = "Vendor Type_"::"Import File" then
                            PAGE.RUN(PAGE::"Import File Archive", Rec)
                        else
                            PAGE.RUN(PAGE::"Vendor Card Archive", Rec);
                    end;
                }
            }
        }
    }
}

