pageextension 50366 Salesquotelist extends "Sales Quotes"
{
    layout
    {
        // Add changes to page layout here
    }

    actions
    {
        modify(MakeOrder)
        {
            trigger OnBeforeAction()
            var
                SalLine: Record "Sales Line";
                ItemLedEntry: Record "Item Ledger Entry";
                ItemQty: Decimal;
            BEGIN
                CheckSalesMandValues(True);
            END;
        }
        modify(MakeInvoice)
        {
            Visible = false;
        }
    }
    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
    begin
        UserSetup.Get(UserId);
        if not UserSetup."open Sales Quote Page" then
            Error('Do not have permissions to open the page');
    end;

    var
        myInt: Integer;
}