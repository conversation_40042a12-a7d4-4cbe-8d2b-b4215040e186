tableextension 50065 WareRcpHdrExt extends "Warehouse Receipt Header"
{
    fields
    {
        field(50000; Status; enum ReleaseReopen)
        {
            Caption = 'Status';
            Editable = false;
        }
        field(50001; "Import File No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
        }

    }
    trigger OnInsert()
    var
        Loca: Record Location;
        Ware: Record "Warehouse Setup";
    begin
        /*Loca.GET("Location Code");
        Loca.TestField("Whse. Rcpt Nos.");
        Loca.TestField("Posted Whse. Rcpt Nos.");

        Ware.get();
        Ware."Whse. Receipt Nos." := Loca."Whse. Rcpt Nos.";
        Ware."Posted Whse. Receipt Nos." := Loca."Posted Whse. Rcpt Nos.";
        Ware.Modify();*/
    end;
}