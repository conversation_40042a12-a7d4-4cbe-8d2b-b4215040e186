codeunit 50050 "Voucher Release"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL       06-Dec-11     -> Codeunit created to release voucher documents.

    TableNo = "Voucher Header";

    trigger OnRun();
    var
        VoucherLine: Record "Gen. Journal Line";
        VoucherHeader: Record "Voucher Header";
    begin
        if Status = Status::Released then
            exit;

        VoucherLine.SETRANGE("Document No.", "Document No.");
        VoucherLine.SETFILTER(Amount, '<>0');
        if not VoucherLine.FIND('-') then
            ERROR(Text001, "Document No.");

        Status := Status::Released;
        MODIFY(true);
        //SAA3.0 >>
        //IF Status = Status::Released THEN
        /*
        VoucherHeader.SETRANGE("Document No.","Document No.");
        IF VoucherHeader.FINDFIRST THEN
         REPORT.RUN(50440,FALSE,FALSE,VoucherHeader);
        */
        //SAA3.0 <<

    end;

    var
        Text001: Label 'There is nothing to release for %1 .';
        Text002: Label 'This Voucher Request can only be released when the approval process is complete.';
        Text003: Label 'The Approval Process must be cancelled or completed to reopen this voucher.';
        VchrReleaseMgt: Codeunit "Voucher Release";

    procedure Reopen(var VoucherHeader: Record "Voucher Header");
    begin
        with VoucherHeader do begin
            if Status = Status::Open then
                exit;
            Status := Status::Open;
            "Batched Report Printed" := false;
            MODIFY(true);
        end;
    end;

    procedure PerformManualRelease(var VoucherHeader: Record "Voucher Header");
    var
        ApprovalEntry: Record "Approval Entry";
        //ApprovalManagement : Codeunit 1535;//CHI9.0
        ApprovedOnly: Boolean;
    begin
        with VoucherHeader do begin
            /*IF ApprovalManagement.CheckApprVoucher(VoucherHeader) THEN BEGIN
              CASE Status OF
                Status::"Pending Approval":
                  ERROR(Text002);
                Status::Released:
                  CODEUNIT.RUN(CODEUNIT::"Voucher Release",VoucherHeader);
                Status::Open:
                BEGIN
                  ApprovedOnly := TRUE;
                  ApprovalEntry.SETCURRENTKEY("Table ID","Document Type","Document No.","Sequence No.");
                  ApprovalEntry.SETRANGE("Table ID",DATABASE::"Voucher Header");
                  IF VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type" :: JV THEN
                    ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: JV)
                  ELSE IF VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type" :: CPV THEN
                    ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: CPV)
                  ELSE IF VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type" :: CRV THEN
                    ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: CRV)
                  ELSE IF VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type" :: BPV THEN
                    ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: BPV)
                  ELSE IF VoucherHeader."Voucher Type" = VoucherHeader."Voucher Type" :: BRV THEN
                    ApprovalEntry.SETRANGE("Document Type",ApprovalEntry."Document Type" :: BRV);
                  ApprovalEntry.SETRANGE("Document No.",VoucherHeader."Document No.");
                  ApprovalEntry.SETFILTER(Status,'<>%1&<>%2',ApprovalEntry.Status::Rejected,ApprovalEntry.Status::Canceled);
                  IF ApprovalEntry.FIND('-') THEN BEGIN
                    REPEAT
                      IF (ApprovedOnly = TRUE) AND (ApprovalEntry.Status <> ApprovalEntry.Status::Approved) THEN
                        ApprovedOnly := FALSE;
                    UNTIL ApprovalEntry.NEXT = 0;
                    IF ApprovedOnly = TRUE THEN
                      CODEUNIT.RUN(CODEUNIT::"Voucher Release",VoucherHeader)
                    ELSE
                      ERROR(Text002);
                  END ELSE
                      ERROR(Text002);
                  END;
                END;
              END ELSE
                CODEUNIT.RUN(CODEUNIT::"Voucher Release",VoucherHeader);*///CHI2018
        end;

    end;

    procedure PerformManualReopen(var VoucherHeader: Record "Voucher Header");
    var
    //ApprovalManagement : Codeunit 1535;//CHI9.0
    begin
        with VoucherHeader do begin
            /*IF ApprovalManagement.CheckApprVoucher(VoucherHeader) THEN BEGIN
              CASE Status OF
                Status::"Pending Approval":
                  ERROR(Text003);
                Status::Open,Status::Released:
                  VchrReleaseMgt.Reopen(VoucherHeader);
              END;
            END ELSE
              VchrReleaseMgt.Reopen(VoucherHeader);*///CHI2018
        end;

    end;
}

