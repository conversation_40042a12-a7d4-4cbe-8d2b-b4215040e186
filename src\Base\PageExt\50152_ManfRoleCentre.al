pageextension 50152 manfRolcenExt extends "Manufacturing Manager RC"
{
    layout
    {

    }

    actions
    {
        //BaluonMar8 2022>>
        addafter(Group)
        {
            group("Approve Pages")
            {
                action("Request to Approve")
                {
                    ApplicationArea = all;
                    Caption = 'Request to Approves';
                    RunObject = page "Requests to Approve";
                }
                action("Approval Entries View")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries View';
                    RunObject = page "Approval Entries View Page";
                }
                action("Approval Entries")
                {
                    ApplicationArea = all;
                    Caption = 'Approval Entries';
                    RunObject = page "Approval Entries";
                }
            }
        }//BaluonMar8 2022<<


        addlast("Group7")
        {
            action("Transfer Orders(Material Request)")
            {
                ApplicationArea = Planning;
                RunObject = page "Transfer Orders";
                RunPageLink = "Transfer Type" = filter("Location Transfer"), "Production Batch No." = filter(<> '');

            }
            action("Transfer Ticket")
            {
                ApplicationArea = Planning;
                RunObject = page "Transfer Orders";
                RunPageLink = "Transfer Type" = filter("Transfer Ticket"), "Production Batch No." = filter(<> '');
            }
            action("Warehouse Shipments")
            {
                ApplicationArea = Planning;
                RunObject = page "Warehouse Shipment List";
            }
            action("Warehouse Receipts")
            {
                ApplicationArea = Planning;
                RunObject = page "Warehouse Receipts";
            }
        }
        addafter("Group7")
        {
            group("Customized Pages")
            {
                group("Material Requisition")
                {

                    action("Material Requisitions")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions.";
                        //RunPageLink = "Prod. Order Ref. No." = filter(<> '');
                    }
                    action("Production MRS List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production MRS List";
                    }
                    action("Approved Production MRS")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Approved Production MRS List";
                    }
                    action("Material Requisitions-Closed(Prod)")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Material Requisitions- Closed";
                        RunPageLink = "Prod. Order Ref. No." = filter(<> '');
                    }
                    action("Material Req Ack")
                    {
                        Caption = 'Material Requisation Release Ack';
                        ApplicationArea = all;
                        RunObject = page "Material Requ-Rel Ack";
                    }

                }
                group("Production Planning")
                {
                    action("Item Production Line")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Item Production Line";
                    }
                    action("Production Work Sheet List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production Work Sheet List";
                    }
                    action("Production Receipt Sheet List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production Receipt Sheet List";
                    }
                    action("Production Order Sheet List")
                    {
                        ApplicationArea = Planning;
                        RunObject = page "Production Order Sheet List";
                    }
                }
            }
            group("Customized Reports")
            {
                action("Shop Floor report")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Shop Floor report";
                }
                action("Mach. Prod. Capacity Plann.")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Mach. Prod. Capacity Plann.";
                }
                action("Production Worksheet")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Production Worksheet";
                }
                action("Production Report_SNOP")
                {
                    ApplicationArea = Planning;
                    RunObject = report "Production Report_SNOP";
                }
            }
            group(PMS)
            {
                action("PMS Voucher")
                {
                    Caption = 'PMS Voucher';
                    ApplicationArea = all;
                    RunObject = page "PMS Voucher List";
                }
            }

        }
    }
    var
        myInt: Integer;
}