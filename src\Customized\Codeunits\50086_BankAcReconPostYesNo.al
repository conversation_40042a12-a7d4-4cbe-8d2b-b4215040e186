codeunit 50086 "Bank Acc. Recon Post Y/N Cust"
{
    TableNo = "Bank Acc. Reconciliation";

    trigger OnRun()
    begin
        if BankAccReconPostYesNo(Rec) then;
    end;

    var
        PostReconciliationQst: Label 'Do you want to post the Reconciliation?';
        PostPaymentsOnlyQst: Label 'Do you want to post the payments?';
        PostPaymentsAndReconcileQst: Label 'Do you want to post the payments and reconcile the bank account?';

    procedure BankAccReconPostYesNo(var BankAccReconciliation: Record "Bank Acc. Reconciliation"): Boolean
    var
        BankAccRecon: Record "Bank Acc. Reconciliation";
        Question: Text;
    begin
    
        BankAccRecon.Copy(BankAccReconciliation);

        if BankAccRecon."Statement Type" = BankAccRecon."Statement Type"::"Payment Application" then
            if BankAccRecon."Post Payments Only" then
                Question := PostPaymentsOnlyQst
            else
                Question := PostPaymentsAndReconcileQst
        else
            Question := PostReconciliationQst;

        if not Confirm(Question, false) then
            exit(false);
        CheckData2(BankAccReconciliation."Bank Account No.", BankAccReconciliation."Statement No.");

        CODEUNIT.Run(CODEUNIT::"Bank Acc. Recon Post Cust", BankAccRecon);
        BankAccReconciliation := BankAccRecon;
        exit(true);
    end;

    PROCEDURE CheckData2(BankAccNo: Code[20]; StatementNo: Code[10]);
    VAR
        "lBankAcc.ReconciliationLine": Record "Bank Acc. Reconciliation Line";
        lOriginalBankStatement: Record "Original Bank Statement";
    BEGIN
        //lBankAccRecon.FINDFIRST;
        "lBankAcc.ReconciliationLine".SETRANGE("Bank Account No.", BankAccNo);
        "lBankAcc.ReconciliationLine".SETRANGE("Statement No.", StatementNo);
        "lBankAcc.ReconciliationLine".SETRANGE("Matching Status",
                                                   "lBankAcc.ReconciliationLine"."Matching Status"::"Not Matching");
        IF NOT "lBankAcc.ReconciliationLine".ISEMPTY THEN
            ERROR('NOT MATCHING entry found in the LEFT side');
    END;

}

