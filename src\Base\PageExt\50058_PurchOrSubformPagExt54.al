pageextension 50058 PurchOrSubformPageExt extends "Purchase Order Subform"
{
    layout
    {
        modify(Description)
        {
            Editable = false;
        }
        addafter("No.")
        {
            field("No.2"; "No.2")
            {

            }
            field("CWIP No."; "CWIP No.")
            {
                ApplicationArea = ALL;
            }
            field("Service Code"; "Service Code")
            {
                ApplicationArea = ALL;
            }

        }
        addafter("Unit of Measure")
        {
            field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
            {
                Visible = true;
            }
            field("Blanket Order Line No"; "Blanket Order Line No.")
            {
                Visible = true;
            }
            field("FA Posting Type"; "FA Posting Type")
            {
                ApplicationArea = all;
            }
            field("Maintenance Code"; "Maintenance Code")
            {
                ApplicationArea = all;
            }
            field("Capex No."; "Capex No.")
            {
                ApplicationArea = ALL;
            }
            field("Capex Line No."; "Capex Line No.")
            {
                ApplicationArea = ALL;
            }
            field("WHT Group"; "WHT Group")
            {
                ApplicationArea = ALL;//B2BPK270521
            }
            field("WHT Applicable"; "WHT Applicable")
            {
                ApplicationArea = ALL;
            }
            field("WHT Amount"; "WHT Amount")
            {
                Caption = 'WHT Base Amount';
                ApplicationArea = ALL;
            }
            field("WHT Amount  2"; "WHT Amount 2")
            {
                Caption = 'WHT Amount';
                ApplicationArea = ALL;
            }
        }
        addafter(Description)
        {
            field("Description 2"; "Description 2")
            {
                ApplicationArea = all;
            }
        }
        addafter(Quantity)
        {
            field("QTY. Excluding Tolerance"; "QTY. Excluding Tolerance")
            {
                ApplicationArea = all;
            }
            field(BinCode; "Bin Code")
            {
                ApplicationArea = ALL;
            }

            field(Select; Select)
            {
                ApplicationArea = ALL;
                trigger OnValidate()
                var
                    SelectErr: Label 'You can Select only "Charge (Item)';
                BEGIN
                    IF Type <> type::"Charge (Item)" then
                        error(SelectErr);
                END;
            }
            field("HS Code"; "HS Code")
            {

            }
            field("Tariff No."; "Tariff No.")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("Material req No.s"; "Material req No.s")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("New Vendor No."; "New Vendor No.")
            {
                ApplicationArea = ALL;
            }
            field("Sub Document Type"; "Sub Document Type")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("Sub Document No."; "Sub Document No.")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("Sub Document Line No."; "Sub Document Line No.")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("Approved PO Created"; "Approved PO Created")
            {
                ApplicationArea = ALL;
                Editable = FALSE;
            }
            field("Under Shpmnt LC Opnd-Goods Dsp"; "Under Shpmnt LC Opnd-Goods Dsp")
            {
                ApplicationArea = ALL;
            }
            field("Under Order LC Opened Aw. Desp"; "Under Order LC Opened Aw. Desp")
            {
                ApplicationArea = ALL;
            }
            field("Under Order FORM-M Opnd -No LC"; "Under Order FORM-M Opnd -No LC")
            {
                ApplicationArea = ALL;
            }
            field("Under Order No LC"; "Under Order No LC")
            {
                ApplicationArea = ALL;
            }
            field("Depreciation Book Code"; "Depreciation Book Code")
            {
                ApplicationArea = ALL;
            }
        }
        Modify("Line Amount")
        {
            Editable = false;
            Visible = ViewCommDetails;
        }
        modify("Qty. to Receive")
        {
            trigger OnBeforeValidate()
            begin
                TestField("Approved PO Created", false);
                if Rec.Type = type::Item then begin
                    // >>>>>> G2S 8399-CAS-01406-B3C2S9 05/03/2023
                    ConfirmWQtytoReceive();
                    // <<<<<< G2S 8399-CAS-01406-B3C2S9 05/03/2023
                end;
            end;

        }
        modify(Quantity)
        {
            trigger OnAfterValidate()
            begin
                ToleranceCalc();
            end;
        }

        modify(Type)
        {
            trigger OnAfterValidate()
            var
                PrchHdr: Record "Purchase Header";
            begin

                PrchHdr.Reset();
                PrchHdr.SetRange("Document Type", "Document Type");
                PrchHdr.SetRange("No.", "Document No.");
                if PrchHdr.FindFirst() then;

                case PrchHdr."Order Type" of
                    PrchHdr."Order Type"::Item:
                        BEGIN
                            if not ((rec.Type = rec.Type::Item) or (rec.Type = rec.Type::"Charge (Item)")) then
                                Error('Given Order Type in Header is not Equal to Line');
                        end;
                    PrchHdr."Order Type"::"G/L Account":
                        BEGIN
                            if not (rec.Type = rec.Type::"G/L Account") then
                                Error('Given Order Type in Header is not Equal to Line');
                        end;
                    PrchHdr."Order Type"::"Fixed Asset":
                        BEGIN
                            if not (rec.Type = rec.Type::"Fixed Asset") then
                                Error('Given Order Type in Header is not Equal to Line');
                        end;

                End;

            end;
        }

        modify("Unit Cost (LCY)")
        {
            Visible = ViewCommDetails;
            Editable = ViewCommDetails;
        }
        modify("Unit Price (LCY)")
        {
            Visible = ViewCommDetails;
            Editable = ViewCommDetails;
        }
        modify("Direct Unit Cost")
        {
            Visible = ViewCommDetails;
            Editable = ViewCommDetails;
        }
        modify("Total Amount Excl. VAT")
        {
            Visible = ViewCommDetails;
            Editable = ViewCommDetails;
        }
        modify("Total Amount Incl. VAT")
        {
            Visible = ViewCommDetails;
            Editable = ViewCommDetails;
        }
        modify(Control43)
        {
            Visible = ViewCommDetails;
        }
        modify("Expected Receipt Date")
        {
            Editable = FieldEditable;
        }
        modify("Planned Receipt Date")
        {
            Editable = FieldEditable;
        }
    }

    actions
    {
        addafter("E&xplode BOM")
        {
            action("Task Lines")
            {
                Image = List;
                RunObject = page "Task Codes Lines";
                RunPageLink = "Document Type" = field("Document Type"), "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                ApplicationArea = All;
            }
            action(TransferSettlementArchieve)
            {
                Image = List;
                RunObject = page TransportSettlementArchieve;
                RunPageLink = "Document Type" = field("Document Type"), "Document No." = field("Document No.");
                ApplicationArea = All;

            }

        }
    }

    var
        FieldEditable: Boolean;
        ViewCommDetails: Boolean;

    trigger OnOpenPage()
    begin
        SetEditable();
        EnableFields();
    end;

    trigger OnAfterGetRecord()
    begin
        SetEditable();
        EnableFields();
    end;

    local procedure EnableFields()
    var
        UserSetup: Record "User Setup";
    begin
        ViewCommDetails := false;

        if UserSetup.Get(UserId) then
            ViewCommDetails := UserSetup.CHI_ERP_ViewCommercialDetails;
    end;

    local procedure SetEditable()
    var
        PurchaseHeader: Record "Purchase Header";
    begin
        PurchaseHeader.SetCurrentKey("No.");
        PurchaseHeader.SetRange("No.", Rec."Document No.");
        if PurchaseHeader.FindFirst() then
            if PurchaseHeader.Status in [PurchaseHeader.Status::Released, PurchaseHeader.Status::"Pending Prepayment"]
            then
                FieldEditable := false else
                FieldEditable := true;
    end;

    // >>>>>> G2S 8399-CAS-01406-B3C2S9 05/03/2023
    // Funtion to check the Qty. to Receive Percentage against Prepayment Percentage on purchase line
    // If Qty. to Receive Percentage is greater than Prepayment Percentage then show error message
    local procedure ConfirmWQtytoReceive()
    var
        NoPrepmtErrMsg: Label 'No Prepayment Invoice posted for this Document.';
        PurchaseInvHeader: Record "Purch. Inv. Header";
        "Prepayment %": Decimal;
        ReceivePercentage: Decimal;
        totalQty: Decimal;
        ReceivedPercentage: Decimal;
        qtytoRecievePercent: Decimal;
        remainingQtyPercentage: Decimal;
    begin
        PurchaseInvHeader.SetCurrentKey("Prepayment Order No.");
        PurchaseInvHeader.SetRange("Prepayment Order No.", Rec."Document No.");

        if not PurchaseInvHeader.FindFirst() then Error(NoPrepmtErrMsg);

        if Rec."Prepayment %" = 0 then
            exit;

        if Rec.Type = Type::Item then begin
            totalQty := Rec.Quantity;
            "Prepayment %" := Rec."Prepayment %";

            if Rec."Qty. to Receive" = 0 then
                exit;

            if Rec."Quantity Received" <> 0 then
                ReceivedPercentage := Rec."Quantity Received" / totalQty * 100; // Revceived Percentage for the line

            remainingQtyPercentage := "Prepayment %" - ReceivedPercentage; // Remaining Percentage for the line to receive
            qtytoRecievePercent := rec."Qty. to Receive" / totalQty * 100; // Quantity to Receive Percentage

            if qtytoRecievePercent > remainingQtyPercentage then
                Error('Qty. to Recieve Percentage cannot be greater than Prepayment Percentage: %%1', Rec."Prepayment %");
        end;
    end;
    // <<<<<< G2S 8399-CAS-01406-B3C2S9 05/03/2023
}