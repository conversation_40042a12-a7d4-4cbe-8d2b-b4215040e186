codeunit 50081 Userwisefilterperm
{
    //b2bpksalecorr10
    trigger OnRun()
    begin

    end;


    Procedure BuildRespCentFilter(): Text[200]
    var
        UserIDRespCent: Record "UserID Resp. Cent. Lines";
    begin
        RespCentCount := 0;

        UserIDRespCent.SETCURRENTKEY("User ID", "Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
            REPEAT
                RespCentCount += 1;
                IF RespCentCount = 1 THEN
                    BuildFilter := UserIDRespCent."Resp. Center Code"
                ELSE
                    BuildFilter += '|' + UserIDRespCent."Resp. Center Code";
            UNTIL UserIDRespCent.NEXT = 0;

        IF RespCentCount > 0 THEN
            EXIT(BuildFilter);
    end;

    var
        RespCentCount: Integer;
        BuildFilter: Text[200];

}