/// <summary>
/// Table Treasury Payment Notification (ID 50369).
/// </summary>
/// G2S Providus Integration 7th Aug 2024 
table 50380 "Treasury Payment Notification"
{
    Caption = 'Treasury Payment Notification';
    DataClassification = ToBeClassified;
    Permissions = tabledata "Bank Payment Status" = RM, tabledata "Bank API Credentials" = R, tabledata "Bank Payment Code" = R;


    fields
    {

        field(1; "Payment Voucher No."; Code[20])
        {
            Caption = 'Payment Voucher No.';
            TableRelation = "Voucher Header" WHERE("Voucher Type" = CONST(BPV), Status = CONST(Released));
        }
        field(2; "Beneficiary Acct. No."; Code[20])
        {
            Caption = 'Beneficiary Acct. No.';
        }
        field(4; "Beneficiary Acct. Name"; Text[500])
        {
            Caption = 'Beneficiary Acct. Name';
        }
        field(5; "Source Acct Name"; Text[100])
        {
            Caption = 'Source Acct Name';
        }
        field(6; "Currency Code"; Code[20])
        {
            TableRelation = Currency.Code;
        }
        field(7; "Beneficiary Bank Code"; Code[20])
        {
            Caption = 'Beneficiary Bank Code';
        }
        field(8; "Beneficiary Bank Name"; Text[100])
        {
            Caption = 'Beneficiary Bank Name';
        }
        field(9; "Amount"; Decimal)
        {
            Caption = 'Amount';
            DecimalPlaces = 0 : 2;
        }
        field(10; "Transaction Reference"; Text[100])
        {
            Caption = 'Transaction Reference';
        }
        field(11; "Narration"; Text[150])
        {
            Caption = 'Narration';
        }
        field(12; "Session ID"; Text[50])
        {
            Caption = 'Session ID';
        }
        field(13; "Status"; Enum "Payment Status")
        {

        }
        field(14; "Transaction DateTime"; DateTime)
        {
            Caption = 'Transaction DateTime';
        }

        field(15; "Transaction Date"; Date)
        {
            DataClassification = CustomerContent;
        }

        field(16; "Date Sent to Bank"; Date)
        {
            DataClassification = CustomerContent;
        }

        field(17; "DateTime Sent to Bank"; DateTime)
        {
            DataClassification = CustomerContent;
        }
        field(18; "Source Bank Code"; Code[10])
        {
            DataClassification = CustomerContent;
        }

        field(19; "Bank Status Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(20; "Bank Status Code Desc."; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(21; "Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(22; "Bank Transaction Reference"; Text[100])
        {
            Caption = 'Bank Transaction Reference';
        }
        field(23; "Sent to Bank"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(24; "Updated from Bank"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(25; "Date Updated From Bank"; Date)
        {
            DataClassification = CustomerContent;
        }

        field(26; "DateTime Updated From Bank"; DateTime)
        {
            DataClassification = CustomerContent;
        }
        field(27; "Temp Omit"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = true;
        }

    }
    keys
    {
        key(PK; "Payment Voucher No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnDelete()
    BEGIN
        Error('Record cannot be deleted.');
    END;
}
///Providus Integration 7th Aug 2024 
