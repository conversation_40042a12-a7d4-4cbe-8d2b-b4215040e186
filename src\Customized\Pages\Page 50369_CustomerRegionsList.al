page 50369 "Customer Regions List"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Customer Regions";
    //RFC 2024-006 

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                /*   field("Customer No."; "Customer No.")
                  {
                      ApplicationArea = All;

                  } */
                field(Location; Location)
                {
                    ApplicationArea = All;

                }
                field(Region; Region)
                {
                    ApplicationArea = All;

                }
            }
        }
        area(Factboxes)
        {

        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction();
                begin

                end;
            }
        }
    }
}