tableextension 50001 PurchHdrTabExt38 extends "Purchase Header"
{

    fields
    {
        modify("Posting Date")
        {
            trigger OnBeforeValidate()
            var
                UserSetup: Record "User Setup";
            BEGIN
                IF "Document Type" = "Document Type"::Order THEN BEGIN
                    IF UserSetup.GET(UserId) THEN BEGIN
                        IF ("Posting Date" <> WorkDate()) AND (NOT UserSetup.AllowPostingDateModify) THEN BEGIN
                            ERROR('Posting date must be equal to Workdate.');
                        END;
                    end;
                END;
            END;
        }

        modify("Buy-from Vendor No.")
        {
            trigger OnAfterValidate()
            var
                VendGRec: Record Vendor;
                ContractRec: Record "Contract Procurement";
                PurchPaySetup: Record "Purchases & Payables Setup";
                ContractRec1: Record "Contract Procurement";
            begin
                IF VendGRec.GET("Buy-from Vendor No.") then begin
                    VendGRec.testfield("Approval Status", VendGRec."Approval Status"::Released);
                    VendGRec.TestField(Blocked, VendGRec.Blocked::" ");
                    if "Document Type" <> "Document Type"::"Blanket Order" then begin
                        if "Purchase Type" = "Purchase Type"::Import then
                            VendGRec.TestField("Vendor Type", VendGRec."Vendor Type"::Import)
                        else
                            if "Purchase Type" = "Purchase Type"::Local then
                                VendGRec.TestField("Vendor Type", VendGRec."Vendor Type"::local)
                            else
                                if "Purchase Type" = "Purchase Type"::PMS then
                                    if VendGRec."Service Group" <> VendGRec."Service Group"::PMS then
                                        Error('Vendor Service Must be PMS');
                    end;
                END;
                //B2BMSOn09Nov21>>
                PurchPaySetup.Get;
                if PurchPaySetup."Vendor Contract Check" then begin
                    ContractRec.Reset();
                    ContractRec.SetRange("Vendor No.", "Buy-from Vendor No.");
                    if ContractRec.FindFirst() then begin
                        ContractRec1.Reset();
                        ContractRec1.SetRange("Vendor No.", "Buy-from Vendor No.");
                        ContractRec1.SetRange(Status, ContractRec1.Status::Released);
                        ContractRec1.SetFilter("Contract Expiry Date", '<=%1', "Document Date");
                        if not ContractRec1.FindFirst() then
                            Error('No active vendor contracts are available');
                    end;
                end;
                //B2BMSOn09Nov21<<
            end;
        }
        field(50001; "Purchase Type"; Enum PurchaseType)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Order Status"; enum OrderStatus)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Contract Start Date"; Date)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                PurchLineLrec: record "Purchase Line";
            begin
                TestField("Contract Start Date");
                PurchLineLrec.Reset();
                PurchLineLrec.SetRange("Document Type", "Document Type");
                PurchLineLrec.SetRange("No.", "No.");
                PurchLineLrec.ModifyAll("Contract Start Date", "Contract Start Date");
            end;
        }
        field(50004; "End Date"; Date)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                PurchLineLrec: record "Purchase Line";
            begin
                TestField("End Date");
                PurchLineLrec.Reset();
                PurchLineLrec.SetRange("Document Type", "Document Type");
                PurchLineLrec.SetRange("No.", "No.");
                PurchLineLrec.ModifyAll("End Date", "End Date");
            end;
        }
        field(50005; "Blanket Order Ref No"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50010; "RFQ No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Quotation No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50012; "Indent Requisition No"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Purch Req. Ref. No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Purch. Req Header";
            Editable = false;
        }
        field(50007; "Service Group"; enum ServiceGroup)
        {
            DataClassification = CustomerContent;
        }

        field(50009; "Reason Codes"; Enum ReasonCodes)
        {
            DataClassification = CustomerContent;

        }
        field(50013; "Purchase Tolerance"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Purchase Tol Percentage"; Decimal)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            var
                PurchLineLRec: Record "Purchase Line";
                ToleranceMsg: Label 'Do you want to include the tolerance?';
            begin
                PurchLineLRec.Reset();
                PurchLineLRec.SetRange("Document Type", "Document Type");
                PurchLineLRec.SetRange("Document No.", "No.");
                If Not Confirm(ToleranceMsg, False) then begin
                    if PurchLineLRec.FindSet() then
                        repeat
                            PurchLineLRec."QTY. Excluding Tolerance" := Round(PurchLineLRec.Quantity, 1, '=');
                            PurchLineLRec.Modify();
                        until PurchLineLRec.Next() = 0;
                end
                else
                    IF PurchLineLRec.FindSet() then
                        repeat
                            PurchLineLRec."QTY. Excluding Tolerance" := Round(PurchLineLRec.Quantity, 1, '=');
                            PurchLineLRec.Validate(Quantity, Round((PurchLineLRec.Quantity + (PurchLineLRec.Quantity * ("Purchase Tol Percentage" / 100))), 1, '='));
                            PurchLineLRec.Modify();
                        until PurchLineLRec.Next() = 0;
            end;
        }
        field(50015; "Created From Approved PO"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = FALSE;
        }
        field(50016; "Ref PO No."; code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50018; "Mat. Requisition Ref. No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50019; "LPO Ref. No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50020; "Project code 2"; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50021; "LPO Printed"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Printable Comment 1"; Text[70])
        {
            DataClassification = CustomerContent;
        }
        field(50035; "Purchase Order Tracking"; Enum PurchaseOrderTracking)
        {
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50040; Subcontracting; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50041; "Subcon. Order No."; code[10])
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50042; "Subcon. Order Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50043; "Vendor Shipment Date"; Date)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }

        modify("Location Code")
        {
            trigger onaftervalidate()
            var
                LocationLrec: Record Location;
            begin
                IF "Location Code" <> '' THEN begin
                    LocationLrec.get("Location Code");
                    LocationLrec.TestField(Blocked, false);//Balu 05122021<<
                    CASE "Document Type" of
                        "Document Type"::Order, "Document Type"::Invoice:
                            begin
                                LocationLrec.TestField("Pur. Posted Receipt Nos.");
                                LocationLrec.TestField("Pur. Posted Invoice Nos.");
                                IF LocationLrec."Pur. Posted Receipt Nos." <> '' THEN
                                    "Receiving No. Series" := LocationLrec."Pur. Posted Receipt Nos.";
                                IF LocationLrec."Pur. Posted Invoice Nos." <> '' THEN
                                    "Posting No. Series" := LocationLrec."Pur. Posted Invoice Nos.";
                            end;
                        "Document Type"::"Return Order", "Document Type"::"Credit Memo":
                            begin
                                LocationLrec.TestField("Pur.Posted Return Shpt. Nos.");
                                LocationLrec.TestField("Pur. Posted Credit Memo Nos.");
                                IF LocationLrec."Pur.Posted Return Shpt. Nos." <> '' THEN
                                    "Return Shipment No. Series" := LocationLrec."Pur.Posted Return Shpt. Nos.";
                                IF LocationLrec."Pur. Posted Invoice Nos." <> '' THEN
                                    "Posting No. Series" := LocationLrec."Pur. Posted Credit Memo Nos.";
                            end;
                    end;
                end;
            end;
        }
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Posted Loading SLip Header"."No." where("Party No." = field("Buy-from Vendor No."), "Responsibility Center" = field("Responsibility Center"));
            //Editable = false;
            trigger OnValidate()
            Var
                PurchOrde: Record "Purchase Header";
                PostedCred: Record "Purch. Cr. Memo Hdr.";
                PSLSL: record "Posted Loading Slip Line";
            begin
                IF "Posted Loading Slip No." <> '' then begin
                    PSLSL.RESET;
                    PSLSL.SetRange("Document No.", "Posted Loading Slip No.");
                    PSLSL.SetFILTER("Customer No.", '<>%1', '');
                    IF NOT PSLSL.FINDFIRST then
                        Error('This shipment is posted against Branch request.');

                    PurchOrde.RESET;
                    PurchOrde.SetRange("Document Type", PurchOrde."Document Type"::"Credit Memo");
                    PurchOrde.SetRange("Posted Loading Slip No.", "Posted Loading Slip No.");
                    IF PurchOrde.FindLast() then
                        Error('Posted Laoding Slip No %1 is already selected in Purchase Creditmemo %2', "Posted Loading Slip No.", PurchOrde."No.");
                    PostedCred.RESET;
                    PostedCred.SetRange("Posted Loading Slip No.", "Posted Loading Slip No.");
                    IF PostedCred.FindLast() then
                        Error('Posted Laoding Slip No %1 is already selected in Posted Purchase Creditmemo %2', "Posted Loading Slip No.", PostedCred."No.");
                    "Pos Load Slip Reason Code" := "Pos Load Slip Reason Code"::"Cust. Adjustment";
                end;
            end;
            //B2B.P.K.T
        }

        field(50046; "Order Type"; Enum "Order Type")
        {

            DataClassification = CustomerContent;
            trigger OnValidate();
            var
                PurchLnvar: Record "Purchase Line";
            begin
                PurchLnvar.Reset();
                PurchLnvar.SetRange("Document No.", "No.");
                IF NOT PurchLnvar.IsEmpty() then
                    ERROR('Purchase lines exists. Please delete before change the option in  type.')
            end;
        }
        /*field(50048; "Import File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }*/
        field(50048; "Import File No."; Code[20])
        {
            Description = 'UNL1.0';
            //Editable = false;
            //TableRelation = Vendor."No." WHERE("Vendor Type" = CONST("Import File"));
            TableRelation = "Vendor 2"."No." WHERE("Vendor Type" = CONST("Import File"));//Prasanna
            trigger OnValidate();
            var
                Vend: Record "Vendor 2";
            begin
                Vend.Get("Import File No.");
                Vend.CheckBlockedVendOnDocs(Vend, false);
                Vend.TESTFIELD("Gen. Bus. Posting Group");

                UpdateImportFileNo;
                //SAA3.0
                if ("Document Type" = "Document Type"::Invoice) and ("Purchase Type" = "Purchase Type"::Import) then begin
                    TransferOrderHdrtoInvoiceHdr;
                end;
                //SAA3.0
            end;
        }
        /*field(50049; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }*/
        field(50049; "Clearing File No."; Code[20])
        {
            Description = 'CHI1.0 This field is used in booking invoice with respective to clearing file no';
            /*TableRelation = "Clearing Header"."No." WHERE("Import File No." = FIELD("Import File No."),
                                                           Status = FILTER(Released | Posted));*/
            TableRelation = "Clearing Header"."No." WHERE("Import File No." = FIELD("Import File No."),
                                                           "Approval Status" = FILTER(Released));
            trigger OnValidate();
            begin
                UpdateImportFileNo;
                if "Clearing File No." <> '' then begin
                    TESTFIELD("Import File No.");
                    if "Purchase Type" <> "Purchase Type"::"Local" then begin
                        //CheckClearingFileNoUsed;
                        if "Document Type" = "Document Type"::Invoice then begin
                            TransferOrderHdrtoInvoiceHdr;
                        end;
                    end;
                    //CreateClrFileDim(Rec);//SAA3.0
                end;
            end;
        }
        field(50055; "FA Tagging Not Required"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        /* field(50060; "Posted Loading Slip No."; code[20])
         {
             DataClassification = CustomerContent;
             Editable = false;
         }*/
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }
        field(50062; "ORP Ref No."; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        field(50063; "Old File No."; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        //Balu 
        field(50050; "Arrival Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50051; "Customer No. For Adj"; code[20])
        {
            DataClassification = CustomerContent;
            ValidateTableRelation = False;//PKONJ29
            TableRelation = "Posted Loading Slip Line"."Customer No." where("Document No." = field("Posted Loading Slip No."));
        }
        //B2BMS
        field(50052; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(50053; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(50054; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(50058; "Modified date"; DateTime)
        {
            Editable = false;
        }
        //B2BMS
        field(50059; "From Purchase Modifier"; Boolean)
        {
            Caption = 'Allow Modify';
            DataClassification = ToBeClassified;
        }
        field(50060; "isModifed"; Boolean)
        {
            DataClassification = ToBeClassified;
        }

    }
    //B2BMS
    trigger OnInsert()
    begin
        "Created By" := UserId;
        "Created Date" := CreateDateTime(WorkDate, Time);
    end;

    trigger OnModify()
    begin
        "Modified By" := UserId;
        "Modified date" := CreateDateTime(WorkDate, Time);
    end;
    //B2BMS
    trigger OnAfterModify()
    begin
        if "Order Status" = "Order Status"::"Short Closed" then
            Error('Order is short closed ');
    end;

    procedure UpdateImportFileNo();
    var
        PurchLineLRec: Record "Purchase Line";
    begin
        PurchLineLRec.RESET;
        PurchLineLRec.SETRANGE("Document Type", "Document Type");
        PurchLineLRec.SETRANGE("Document No.", "No.");
        IF PurchLineLRec.FINDSET THEN
            REPEAT
                PurchLineLRec.VALIDATE("Import File No.", "Import File No.");
                PurchLineLRec.VALIDATE("Clearing File No.", "Clearing File No.");
                PurchLineLRec.MODIFY;
            UNTIL PurchLineLRec.NEXT = 0;
    end;

    procedure TransferOrderHdrtoInvoiceHdr()
    var
        OrderHeaderRec: record "Purchase Header";
    begin
        OrderHeaderRec.SETFILTER("Document Type", '%1', OrderHeaderRec."Document Type"::Order);
        OrderHeaderRec.SETRANGE("Buy-from Vendor No.", "Buy-from Vendor No.");
        OrderHeaderRec.SETRANGE("Import File No.", "Import File No.");
        IF OrderHeaderRec.FINDSET THEN BEGIN
            //"Country of Origin" := OrderHeaderRec."Country of Origin";
            VALIDATE("Shortcut Dimension 1 Code", OrderHeaderRec."Shortcut Dimension 1 Code");
            VALIDATE("Shortcut Dimension 2 Code", OrderHeaderRec."Shortcut Dimension 2 Code");
        END;
    end;

    procedure ShortClosePurchOrder();
    var
        PurchLineLRec: Record "Purchase Line";
        PurchQtyReceived: Decimal;
        PurchQtyInvoiced: Decimal;
        PurchQty: Decimal;
    begin
        IF NOT ("Order Status" = "Order Status"::"Short Closed") THEN BEGIN
            IF NOT PurchLinesExist THEN
                ERROR(Text50204);
            IF NOT CONFIRM(Text50205, FALSE) THEN
                EXIT;
            WareHouseLineGrec.Reset();
            WareHouseLineGrec.SetRange("Source No.", "No.");
            if WareHouseLineGrec.FindSet then begin
                WarehouseNo := WareHouseLineGrec."No.";
                WareHouseLineGrec.DeleteAll();
                if WarehouseGRec.Get(WarehouseNo) then
                    WarehouseGRec.Delete();
            end;
            ArchiveManagement.ArchPurchDocumentNoConfirm(Rec);
            PurchLineLRec.RESET;
            PurchLineLRec.SETRANGE("Document Type", "Document Type");
            PurchLineLRec.SETRANGE("Document No.", "No.");
            PurchLineLRec.SetRange("Order Status", 0);
            IF PurchLineLRec.FINDSET THEN
                REPEAT
                    PurchLineLRec."Actual Order Qty." := PurchLineLRec.Quantity;
                    PurchLineLRec."Order Status" := PurchLineLRec."Order Status"::"Short Closed";//PKONDE7
                    PurchLineLRec.VALIDATE(Quantity, PurchLineLRec."Quantity Received");
                    //PurchLineLRec."Order Status" := PurchLineLRec."Order Status"::"Short Closed";//PKONDE7
                    PurchLineLRec.MODIFY;
                    PurchQtyReceived += PurchLineLRec."Quantity Received";
                    PurchQtyInvoiced += PurchLineLRec."Quantity Invoiced";
                    PurchQty += PurchLineLRec.Quantity;
                UNTIL PurchLineLRec.NEXT = 0;


            IF (PurchQtyInvoiced <> 0) THEN begin
                IF (PurchQtyInvoiced = PurchQty) THEN
                    "Purchase Order Tracking" := "Purchase Order Tracking"::"Completely Invoiced"
                else
                    "Purchase Order Tracking" := "Purchase Order Tracking"::"Partially Invoiced"

            END ELSE
                IF (PurchQty = PurchQtyReceived) THEN
                    "Purchase Order Tracking" := "Purchase Order Tracking"::"Completely Received";

            "Order Status" := "Order Status"::"Short Closed";
            MODIFY;
        END;
    end;

    local procedure ReceivedPurchLinesExist(): Boolean
    begin
        PurchLine.RESET;
        PurchLine.SETRANGE("Document Type", "Document Type");
        PurchLine.SETRANGE("Document No.", "No.");
        IF PurchLine.FINDSET THEN
            REPEAT
                IF (PurchLine."Quantity Received" - PurchLine."Quantity Invoiced") <> 0 THEN
                    EXIT(TRUE)
            UNTIL PurchLine.NEXT = 0;
        EXIT(FALSE);
    end;

    procedure ShortCloseCheck()
    begin
        if not (User.GET(UPPERCASE(USERID())) and User."Short Close Checked") then
            ERROR(UPPERCASE(USERID()) + ' Please enable ShortClosed check Mark in User Setup');
        Status := Status::Open;//PKONJU27
    end;

    procedure GetGateEntryLines()
    var
        PostedGateEntryLine: Record "Posted Gate Entry Line";
        PurchHeader2: Record "Purchase Header";
        GateEntryAttachment: Record "Gate Entry Attachment";
    begin
        PostedGateEntryLine.MODIFYALL(Mark, FALSE);
        PurchHeader2.GET("Document Type", "No.");
        PostedGateEntryLine.RESET;
        PostedGateEntryLine.SETCURRENTKEY("Entry Type", "Source Type", "Type", "Source No.", Status);
        CASE "Document Type" OF
            "Document Type"::Order:
                BEGIN
                    PostedGateEntryLine.SETRANGE("Source Type", PostedGateEntryLine."Source Type"::"Purchase Order");
                    PostedGateEntryLine.SetRange(Type, PostedGateEntryLine.Type::NRGP);
                    PostedGateEntryLine.SETRANGE("Entry Type", PostedGateEntryLine."Entry Type"::Inward);
                    PostedGateEntryLine.SETRANGE("Source No.", "No.");
                    PostedGateEntryLine.SETRANGE(Status, PostedGateEntryLine.Status::Open);
                END;
            "Document Type"::Invoice:
                BEGIN
                    PostedGateEntryLine.SETRANGE("Source Type", PostedGateEntryLine."Source Type"::" ");
                    PostedGateEntryLine.SETRANGE("Entry Type", PostedGateEntryLine."Entry Type"::Inward);
                    PostedGateEntryLine.SetRange(Type, PostedGateEntryLine.Type::NRGP);
                    PostedGateEntryLine.SETRANGE(Status, PostedGateEntryLine.Status::Open);
                END;
        END;

        GateEntryAttachment.SETCURRENTKEY("Source Type", "Source No.", "Entry Type", "Gate Entry No.", "Line No.");
        IF PostedGateEntryLine.FINDSET THEN
            REPEAT
                GateEntryAttachment.SETRANGE("Source No.", PostedGateEntryLine."Source No.");
                GateEntryAttachment.SETRANGE("Gate Entry No.", PostedGateEntryLine."Gate Entry No.");
                GateEntryAttachment.SETRANGE("Line No.", PostedGateEntryLine."Line No.");
                IF NOT GateEntryAttachment.FINDFIRST THEN BEGIN
                    PostedGateEntryLine.Mark := TRUE;
                    PostedGateEntryLine.MODIFY;
                    COMMIT;
                END;
            UNTIL PostedGateEntryLine.NEXT = 0;

        PostedGateEntryLine.RESET;
        PostedGateEntryLine.SETCURRENTKEY("Entry Type", "Source Type", "Source No.", Status);
        PostedGateEntryLine.SETRANGE(Mark, TRUE);
        IF PostedGateEntryLine.FINDFIRST THEN BEGIN
            PostedGateEntryLineList.SETTABLEVIEW(PostedGateEntryLine);
            IF PAGE.RUNMODAL(PAGE::"Posted Gate Entry Line List", PostedGateEntryLine) = ACTION::LookupOK THEN BEGIN
                GateEntryAttachment.INIT;
                GateEntryAttachment."Source Type" := PostedGateEntryLine."Source Type";
                GateEntryAttachment."Source No." := PostedGateEntryLine."Source No.";
                GateEntryAttachment."Entry Type" := PostedGateEntryLine."Entry Type";
                GateEntryAttachment."Gate Entry No." := PostedGateEntryLine."Gate Entry No.";
                GateEntryAttachment."Line No." := PostedGateEntryLine."Line No.";
                GateEntryAttachment."Purchase Invoice No." := "No.";
                GateEntryAttachment.INSERT;
            END;
        END;

    end;

    procedure CreateApprovedPurchOrder(Var VendNo: Code[20]; Var PONo: code[20]): Code[20]
    var
        PurchLne: Record "Purchase Line";
        PurchHdr: Record "Purchase Header";
        LineNoVar: Integer;
        Vend: record Vendor;
        PurcLne: record "purchase Line";
        lineNum: integer;
        PurchComment: Record "Purch. Comment Line";
        PurchaseComment: record "Purch. Comment Line";
    BEGIN
        IF Vend.GET(VendNo) THEN;
        PurchHdr.INIT;
        PurchHdr.TransferFields(Rec);
        PurchHdr."Document Type" := PurchHdr."Document Type"::Order;
        PurchHdr."No." := '';
        PurchHdr.Status := PurchHdr.Status::Open;
        PurchHdr."Purchase Type" := Vend."Vendor Type";
        PurchHdr."Buy-from Vendor No." := VendNo;
        PurchHdr."Buy-from Vendor Name" := Vend.Name;
        PurchHdr.INSERT(TRUE);


        PurchHdr."Ref PO No." := "No.";
        PurchHdr."Created From Approved PO" := TRUE;

        clear(StatusGVar);
        PurHd.RESET;
        PurHd.SetRange("Document Type", "Document Type");
        PurHd.SetRange("No.", "No.");
        IF PurHd.findfirst then BEGIN
            StatusGVar := PurHd.Status;
            IF PurHd.Status <> PurHd.status::Open then BEGIN
                PurHd.Status := PurHd.Status::Open;
                PurHd.Modify();
            END;
        END;
        PurchHdr.MODIFY();

        LineNoVar := 10000;



        PurchLne.reset;
        PurchLne.SetCurrentKey("New Vendor No.");
        PurchLne.SetRange("Document Type", PurchLne."Document Type"::Order);
        PurchLne.SetRange("Document No.", "No.");
        PurchLne.SetRange(Select, true);
        PurchLne.SetRange("New Vendor No.", VendNo);
        PurchLne.SetRange("Approved PO Created", false);
        PurchLne.SetRange(Type, PurchLne.type::"Charge (Item)");
        IF PurchLne.findset then
            repeat
                PurcLne.INIT;
                PurcLne.TransferFields(PurchLne);
                PurcLne.VALIDATE("Document No.", PurchHdr."No.");
                PurcLne.VALIDATE("Line No.", LineNoVar);
                PurcLne."Buy-from Vendor No." := VendNo;

                PurcLne.Insert(true);

                PurcLne.Validate("Sub Document Type", PurcLne."Sub Document Type"::"Purchase Order");
                PurcLne.Validate("Sub Document No.", PurchLne."Document No.");
                PurcLne.Validate("Sub Document Line No.", PurchLne."Line No.");

                PurcLne.Select := FALSE;
                PurcLne."New Vendor No." := '';
                PurcLne.Modify();
                LineNoVar += 10000;

                PurchLne."Approved PO Created" := TRUE;
                PurchLne."Actual Order Qty." := PurchLne.Quantity;
                //PurchLne.validate(quantity, 0);
                PurchLne.Modify();

            until PurchLne.next = 0;


        lineNum := 10000;
        PurchComment.RESET;
        PurchComment.SETRANGE("Document Type", PurchComment."Document Type"::Order);
        PurchComment.SETRANGE("No.", "No.");
        IF PurchComment.FINDSET THEN
            REPEAT
                PurchaseComment.INIT;
                PurchaseComment.VALIDATE("Document Type", PurchComment."Document Type"::Order);
                PurchaseComment.VALIDATE("No.", "No.");
                PurchaseComment.VALIDATE("Line No.", lineNum);
                PurchaseComment.INSERT(TRUE);
                PurchaseComment.VALIDATE(Comment, PurchComment.Comment);
                PurchaseComment.VALIDATE(Date, "Order Date");
                PurchaseComment.MODIFY;
                lineNum += 10000;
            UNTIL PurchComment.NEXT = 0;
        PurHd.status := StatusGVar;
        PurHd.Modify();
        EXIT(PurchHdr."No.");
    END;

    Procedure MatchLocations()
    VAR
        PurchLnLRec: Record "Purchase line";
        RespError: Label 'Location  in Purchase Header and Purchase Line must be same';
    BEGIN
        PurchLnLRec.reset;
        PurchLnLRec.SetRange("Document No.", "No.");
        IF PurchLnLRec.findset then begin
            IF PurchLnLRec."Location Code" <> "Location Code" then
                ERROR(RespError);
        END;
    end;

    procedure TestCapex()
    Var
        CapexBudget: Record "Budget Header";
        CapexBudgetLine: Record "Budget Line";
        PurchHdr: Record "Purchase Header";
        PurchLine: Record "Purchase Line";
    BEGIN
        PurchLine.reset;
        PurchLine.SetRange("Document No.", "No.");
        PurchLine.SetRange(Type, PurchLine.Type::"Fixed Asset");
        IF PurchLine.findset then
            repeat
                CapexBudgetLine.reset;
                CapexBudgetLine.setrange("Document No.", PurchLine."Capex No.");
                CapexBudgetLine.setrange("Line No.", PurchLine."Capex Line No.");
                IF CapexBudgetLine.FindFirst() THEN BEGIN
                    CapexBudgetLine.CalcFields("Budget Utilized");
                    //PKONJ21 below reduce logic added.
                    // IF ((CapexBudgetLine.Amount - CapexBudgetLine."Budget Utilized") < (PurchLine.Amount - ((PurchLine.Amount / PurchLine.Quantity) * PurchLine."Quantity Invoiced"))) THEN
                    //    ERROR('Purchase Order Value is Greater than Available Capex Budget');
                    //IF ((CapexBudgetLine.Amount - CapexBudgetLine."Budget Utilized") < (PurchLine.Amount - ((PurchLine.Amount / PurchLine.Quantity) * PurchLine."Quantity Invoiced"))) THEN       
                    IF ((CapexBudgetLine."Amount(LCY)" - CapexBudgetLine."Budget Utilized") < (PurchLine.Amount - ((PurchLine.Amount / PurchLine.Quantity) * PurchLine."Quantity Invoiced"))) THEN   //Bug Fix 18-12-2023  RKD
                        ERROR('Purchase Order Value is Greater than Available Capex Budget Capex Amount %1, Budget Utalised %2 , Purchase Line Amount %3, Capex No %4, Capex Line No %5', CapexBudgetLine.Amount, CapexBudgetLine."Budget Utilized",
                        (PurchLine.Amount / PurchLine.Quantity) * PurchLine."Quantity Invoiced", PurchLine."Capex No.", PurchLine."Capex Line No.");//PKONDE22

                end;
            until PurchLine.next = 0;
    END;

    Procedure ChecKposGrps()
    var
        PurchLinLvar: Record "Purchase Line";
    begin
        TestCapex();
        IF "Document Type" = "Document Type"::Order then begin
            PurchLinLvar.Reset();
            PurchLinLvar.SetRange("Document Type", "Document Type");
            PurchLinLvar.SetRange("Document No.", "No.");
            IF PurchLinLvar.findset then
                repeat
                    PurchLinLvar.TestField("No.");
                    PurchLinLvar.TestField(Quantity);
                    PurchLinLvar.TestField("Unit Cost");
                    PurchLinLvar.TestField("Gen. Bus. Posting Group");
                    PurchLinLvar.TestField("Gen. Prod. Posting Group");
                    IF PurchLinLvar.Type = PurchLinLvar.type::Item then begin
                        PurchLinLvar.TestField("Posting Group");
                        PurchLinLvar.TestField("Unit of Measure Code");
                    end;
                until PurchLinLvar.next = 0;
        end;
    end;

    procedure CreateImportFile()
    var
        PurchLineLRec: Record "Purchase Line";
        PrevVendNoLVar: Code[20];
        VendorLRec: Record "Vendor 2";
        ImforFileLineLRec: Record "Import File Line";
        RecordsInserted: Boolean;
        LineNoLvar: Integer;
        Text50209: Label 'Import file already exist for this purchase order %1.';
        Text50207: Label 'Import file No. %1 created';
        Text50208: Label 'There is nothing to create';
        DimVal: record "Dimension Value";
        GLsetup: Record "General Ledger Setup";
    begin

        IF "Import File No." <> '' THEN
            ERROR(Text50209, "No.");

        //CODE commented to allow for ORP printing with import file no NYO 28/4/2017>>
        //IF (Status IN [Status::"Pending Approval",Status::Open]) THEN  //SAA3.0
        //TESTFIELD(Status, Status:: Released);

        //IF NOT CONFIRM('Do you want to create import file for this Purchase order?') THEN
        //EXIT; NYO <<

        CLEAR(PrevVendNoLVar);

        PurchLineLRec.RESET;
        PurchLineLRec.SETCURRENTKEY("Document Type", "Buy-from Vendor No.");
        PurchLineLRec.SETRANGE("Document Type", "Document Type");
        PurchLineLRec.SETRANGE("Document No.", "No.");
        PurchLineLRec.SETFILTER(Type, '%1|%2', PurchLineLRec.Type::Item, PurchLineLRec.Type::"Fixed Asset");//PKONSE20
        IF PurchLineLRec.FINDSET THEN BEGIN
            REPEAT
                //VendorLRec.GET(PurchLineLRec."Vendor No.");
                IF PurchLineLRec."Buy-from Vendor No." <> PrevVendNoLVar THEN BEGIN
                    VendorLRec.INIT;
                    VendorLRec."Vendor Type" := VendorLRec."Vendor Type"::"Import File";
                    VendorLRec."ORP No." := "ORP Ref No.";
                    VendorLRec."Operation File No." := "Old File No."; //RKD
                    VendorLRec."Order No." := "No.";
                    VendorLRec."Approval Status" := VendorLRec."Approval Status"::Open;
                    VendorLRec.INSERT(TRUE);

                    VendorLRec.VALIDATE("No.");
                    VendorLRec.Blocked := VendorLRec.Blocked::" ";
                    VendorLRec.VALIDATE("Global Dimension 1 Code", PurchLineLRec."Shortcut Dimension 1 Code");
                    VendorLRec.VALIDATE("Global Dimension 2 Code", PurchLineLRec."Shortcut Dimension 2 Code");
                    VendorLRec.VALIDATE("Gen. Bus. Posting Group", 'OVERSEAS');
                    VendorLRec.VALIDATE("Vendor Posting Group", "Vendor Posting Group");

                    VendorLRec."Import Currency Code" := "Currency Code";
                    IF VendorLRec.MODIFY(TRUE) THEN
                        RecordsInserted := TRUE;
                    LineNoLVar := 10000;
                END;

                ImforFileLineLRec.INIT;
                ImforFileLineLRec."Document Type" := PurchLineLRec."Document Type"::Order;
                ImforFileLineLRec."Document No." := VendorLRec."No.";
                ImforFileLineLRec."Line No." := LineNoLVar;
                ImforFileLineLRec.VALIDATE(Type, PurchLineLRec.Type);
                ImforFileLineLRec.VALIDATE("Order No.", PurchLineLRec."Document No.");
                ImforFileLineLRec.VALIDATE("Order Line No.", PurchLineLRec."Line No.");
                ImforFileLineLRec."Item Category Code" := PurchLineLRec."Item Category Code";
                //ImforFileLineLRec."Product Group Code":=PurchLineLRec."Product Group Code";

                //ImforFileLineLRec.VALIDATE("No.",PurchLineLRec."No.");
                //ImforFileLineLRec."Unit of Measure Code":=PurchLineLRec."Unit of Measure Code";
                //ImforFileLineLRec.VALIDATE(Quantity,PurchLineLRec.Quantity);
                ImforFileLineLRec.INSERT(TRUE);
                //ImforFileLineLRec.MODIFY(TRUE);

                LineNoLVar += 10000;
                PrevVendNoLVar := PurchLineLRec."Buy-from Vendor No.";
            UNTIL PurchLineLRec.NEXT = 0;

            IF RecordsInserted THEN BEGIN
                GLsetup.Get();
                GLsetup.TestField("Shortcut Dimension 6 Code");
                IF NOT DimVal.GET(GLsetup."Shortcut Dimension 6 Code", VendorLRec."No.") THEN BEGIN
                    DimVal.INIT;
                    DimVal."Dimension Code" := GLsetup."Shortcut Dimension 6 Code";
                    DimVal.Code := VendorLRec."No.";
                    DimVal."Global Dimension No." := 6;
                    DimVal.Name := 'Import File No.';
                    DimVal.INSERT;
                END;
                ValidateShortcutDimCode(6, VendorLRec."No.");
                VALIDATE("Import File No.", VendorLRec."No.");
                IF MODIFY THEN
                    MESSAGE(Text50207, VendorLRec."No.");
            END;
        END ELSE
            ERROR(Text50208);
    end;

    procedure CheckExpectedReceiptDate()
    var
        PurchaseLine: Record "Purchase Line";
        ExpReceiptDateErr: Label 'Expected Receipt Date cannot be before the Order Date';
        LineExpReceiptDateErr: Label 'Expected Receipt Date cannot be before the Order Date for Line No. %1', Comment = '%1 is purchaseline no.';
    begin
        if not ("Document Type" = "Document Type"::Order) then
            exit;

        TestField("Expected Receipt Date");
        if "Expected Receipt Date" < "Order Date" then
            Error(ExpReceiptDateErr);

        PurchaseLine.SetRange("Document Type", "Document Type"::Order);
        PurchaseLine.SetRange("Document No.", "No.");
        PurchaseLine.FindSet();
        repeat
            PurchaseLine.TestField("Expected Receipt Date");
            if PurchaseLine."Expected Receipt Date" < "Order Date" then
                Error(LineExpReceiptDateErr);
        until PurchaseLine.Next() = 0;
    end;

    procedure SetEditable(PRec: Record "Purchase Header"): Boolean
    begin
        if PRec.Status in [Status::Released, Status::"Pending Prepayment"] then exit(false) else exit(true);
    end;


    // >>>>>> G2S CAS-01285-R8F1M2 20/05/2024
    procedure IsInvoicedCompleted(): Boolean
    var
        PurchaseLine: Record "Purchase Line";
        PostedImportPurchaseHeader: Record "Purch. Inv. Header";
        PostedImportPurchaseLine: Record "Purch. Inv. Line";
        totalOrderQty, totalPostedInvQty : Decimal;
    begin
        if Rec.Status = Status::Released then begin
            totalOrderQty := 0;
            totalPostedInvQty := 0;

            if Rec."Purchase Order Tracking" = "Purchase Order Tracking"::"Completely Invoiced" then
                exit(false);

            PurchaseLine.SetFilter("Document No.", "No.");
            if PurchaseLine.FindSet() then begin
                repeat
                    totalOrderQty += PurchaseLine.Quantity;
                    totalPostedInvQty += PurchaseLine."Quantity Invoiced";
                until PurchaseLine.Next() = 0;
                commit();
            end else
                Error('No Import Line exist for the Document!');

            if totalOrderQty <> totalPostedInvQty then exit(false) else exit(true);
            Commit();
        end;
    end;
    // <<<<<< G2S CAS-01285-R8F1M2 20/05/2024

    //Confirm if capex year is same as document date
    procedure ConfirmCapexYear() //110225 G2S CAS-01398-B9R3L8
    var
        PurchLn: Record "Purchase Line";
    begin
        PurchLn.Reset();
        PurchLn.SetCurrentKey("Document Type", "Document No.");
        PurchLn.SetRange("Document Type", Rec."Document Type");
        PurchLn.SetRange("Document No.", Rec."No.");
        IF PurchLn.FINDSET() THEN BEGIN
            REPEAT
                CheckCapexYear(PurchLn);
            UNTIL PurchLn.NEXT() = 0;
        END;
    end;

    local procedure CheckCapexYear(PurchLn: Record "Purchase Line") //110225 G2S CAS-01398-B9R3L8
    var
        CapBudLine: Record "Budget Line";
        CapBudHdr: Record "Budget Header";
        CapYear: Integer;
    begin
        CapBudHdr.Reset();
        CapBudHdr.SetCurrentKey("No.", Status);
        CapBudHdr.SetRange("No.", PurchLn."Capex No.");
        CapBudHdr.SetFilter(Status, 'Released');
        If CapBudHdr.FindFirst() THEN BEGIN
            CapYear := 0;
            CapYear := Date2DMY(CapBudHdr."Document Date", 3);
            IF CapYear <> Date2DMY(Rec."Document Date", 3) Then
                Error('Capex selected %1 on line=%3 is of previous Year %2', PurchLn."Capex No.", CapYear, PurchLn."Line No.");
        END;
    end;
    //110225 G2S CAS-01398-B9R3L8

    var
        User: Record "User Setup";
        PurHd: Record "Purchase Header";
        StatusGVar: Integer;
        NoSeriesMgt: Codeunit NoSeriesManagement;
        PurchHeader: Record "Purchase Header";
        Text50204: label 'There is nothing to short close.';
        Text50206: Label 'There is purchase lines with Received Quantity but not Invoiced.';
        Text50205: Label 'Do you want to short close the Purchase Order?';
        PurchLine: Record "Purchase Line";
        ArchiveManagement: Codeunit 5063;
        WarehouseGRec: record "Warehouse Receipt Header";
        WareHouseLineGrec: record "Warehouse Receipt Line";
        WarehouseNo: code[20];
        PostedGateEntryLineList: Page "Posted Gate Entry Line List";
}