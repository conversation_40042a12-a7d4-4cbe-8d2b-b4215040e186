page 50459 "Clearing SubForm"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // SAA     :  Saheed A. Adeosun
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO       15-Feb-12    -> Form created for IPO Clearing.
    // 3.0       SAA      06-Mar-13    -> Added new field "Ordered Quantity" to the form to display the
    //                                    quantity on the Order Line.
    // CRF:2019-0082 SAA  07-Oct-19  -> New Column added "Outstanding Quantity"

    AutoSplitKey = true;
    Caption = 'Clearing SubForm';
    DelayedInsert = false;
    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = ListPart;
    SourceTable = "Clearing Line";

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("Document Type"; "Document Type")
                {
                    Visible = false;
                }
                field("Order No."; "Order No.")
                {
                    Visible = false;
                }
                field("Order Line No."; "Order Line No.")
                {
                }
                field(Type; Type)
                {
                }
                field("No."; "No.")
                {
                }
                field(Description; Description)
                {
                }
                field("Description 2"; "Description 2")
                {
                    Visible = false;
                }
                field("Unit of Measure Code"; "Unit of Measure Code")
                {
                }
                field("Ordered Quantity"; "Ordered Quantity")
                {
                }
                field("Outstanding Quantity"; "Outstanding Quantity")
                {
                }
                field(Quantity; Quantity)
                {
                }
                field("Quantity Received"; "Quantity Received")
                {
                }
                field("Direct Unit Cost"; "Direct Unit Cost")
                {
                    Visible = false;
                }
                field("Amount (FCY)"; "Amount (FCY)")
                {
                    Visible = false;
                }
                field("Item Category Code"; "Item Category Code")
                {
                }
                field("Product Group Code"; "Product Group Code")
                {
                }
                field("Quantity Invoiced"; "Quantity Invoiced")
                {
                    Visible = false;
                }
                field("Qty. Rcd. Not Invoiced"; "Qty. Rcd. Not Invoiced")
                {
                    Visible = false;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("Clearing Line")
                {
                    Caption = 'Clearing Line';
                    Visible = false;

                    trigger OnAction();
                    begin
                        //This functionality was copied from page #50197. Unsupported part was commented. Please check it.
                        /*CurrPage.ClearingLines.FORM.*/
                        _ShowClearingLine;

                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Document Type" := "Document Type"::Order;
        Type := Type::Item;
    end;

    procedure _ShowClearingLine();
    begin
        ShowClearingLine;
    end;

    procedure ShowClearingLine();
    begin
        ShowClearingLine;
    end;

    procedure GetClearingLine();
    begin
        GetClearingLine;
    end;
}

