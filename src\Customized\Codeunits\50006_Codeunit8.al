
codeunit 50006 "PO Automation"
{
    trigger OnRun();
    begin
    end;

    var
        PurchaseHeader: Record "Purchase Header";
        Text005Lbl: Label '"Rating of payment term code  ''%1'' Should not be zero "';
        Text006Lbl: Label 'Payment Term Code  should not be blank in Quotation No: %1';

    procedure InsertQuotationLines(var RFQNumber: Code[20]; QuotComp: record QuotCompHdr);
    var
        QuoteCompare: Record "Quotation Comparison";
        PurchaseLine: Record "Purchase Line";
        QuoteCompare1: Record "Quotation Comparison";
        PurchaseSetup: Record 312;
        QuoteCompareAmount: Record "Quotation Comparison";
        PaymentTerms: Record 3;
        QuoteCompare2: Record "Quotation Comparison";
        PreviousItem: Code[20];
        LeastLineAmount: Decimal;
        TotalWeightage: Decimal;
        Amount: Decimal;
        MaxPayment: Decimal;
        Text0010Lbl: Label 'Item Price should  not be blank in Quotation No. %1';
        ColourCode: Code[20];
        Text0011Lbl: Label 'Quantity should  not be Zero in Quotation No. %1';
        Line: Integer;
        QuoComparsn: Record "Quotation Comparison";
        Lnum: integer;
        Linum: integer;
        Quocom: Record "Quotation Comparison";
        Vend: Record Vendor;
    begin
        clear(TotalWeightage);
        PurchaseHeader.RESET();
        PurchaseHeader.SETRANGE("Document Type", PurchaseHeader."Document Type"::Quote);
        PurchaseHeader.SETRANGE("RFQ No.", RFQNumber);
        IF PurchaseHeader.FIND('-') THEN
            REPEAT
                //PKONJU14 Below Condition Added
                IF Vend.GET(PurchaseHeader."Buy-from Vendor No.") AND (Vend.Blocked = Vend.Blocked::" ") THEN BEGIN
                    QuoteCompare.Reset();
                    QuoteCompare.SetRange("RFQ No.", RFQNumber);
                    IF QuoteCompare.FINDLAST() then
                        Line := QuoteCompare."Line No."
                    else
                        Line := 10000;
                    QuoteCompare.INIT();
                    QuoteCompare."Quot Comp No." := QuotComp."No.";
                    QuoteCompare."RFQ No." := PurchaseHeader."RFQ No.";
                    QuoteCompare."Quote No." := PurchaseHeader."No.";
                    QuoteCompare."Vendor No." := PurchaseHeader."Buy-from Vendor No.";
                    QuoteCompare."Vendor Name" := PurchaseHeader."Buy-from Vendor Name";
                    QuoteCompare."Item No." := '';
                    QuoteCompare.Description := PurchaseHeader."Buy-from Vendor Name";
                    QuoteCompare.Quantity := 0;
                    QuoteCompare.Rate := 0;
                    QuoteCompare.Amount := 0;
                    QuoteCompare."Payment Term Code" := '';
                    QuoteCompare."Parent Quote No." := '';
                    QuoteCompare."Line Amount" := 0;
                    QuoteCompare."Delivery Date" := 0D;
                    QuoteCompare.Level := 0;
                    QuoteCompare."RFQ No." := RFQNumber;
                    QuoteCompare."Line No." := Line + 10000;
                    QuoteCompare."Location Code" := PurchaseHeader."Location Code";
                    QuoteCompare.INSERT();
                    Amount := 0;
                    PurchaseLine.SETRANGE(PurchaseLine."Document Type", PurchaseHeader."Document Type");
                    PurchaseLine.SETRANGE(PurchaseLine."Document No.", PurchaseHeader."No.");
                    //PurchaseLine.SETRANGE(Type, PurchaseLine.Type::Item);//TEST
                    IF PurchaseLine.FIND('-') THEN
                        REPEAT
                            QuoteCompare."RFQ No." := PurchaseHeader."RFQ No.";
                            QuoteCompare."Quot Comp No." := QuotComp."No.";
                            QuoteCompare."Quote No." := '';
                            QuoteCompare."Vendor No." := '';
                            QuoteCompare."Vendor Name" := '';
                            QuoteCompare."Item No." := PurchaseLine."No.";
                            QuoteCompare.Description := PurchaseLine.Description;
                            QuoteCompare.Description2 := PurchaseLine."Description 2";
                            IF PurchaseLine.Quantity = 0 THEN
                                ERROR(Text0011Lbl, PurchaseLine."Document No.");
                            QuoteCompare.Quantity := PurchaseLine.Quantity;
                            QuoteCompare.Rate := PurchaseLine."Direct Unit Cost";
                            IF PurchaseLine."Direct Unit Cost" = 0 THEN
                                ERROR(Text0010Lbl, PurchaseLine."Document No.");
                            QuoteCompare."Payment Term Code" := PurchaseHeader."Payment Terms Code";
                            QuoteCompare."Purc. Req No" := PurchaseLine."Sub Document No.";
                            QuoteCompare."Purch. Req Line No" := PurchaseLine."Sub Document Line No.";
                            QuoteCompare."Location Code" := PurchaseHeader."Location Code";
                            QuoteCompare."Material req No.s" := PurchaseLine."Material req No.s";//PK on 24.10.2020
                            QuoteCompare."Shortcut Dimension 1 Code" := PurchaseLine."Shortcut Dimension 1 Code";
                            QuoteCompare."Shortcut Dimension 2 Code" := PurchaseLine."Shortcut Dimension 2 Code";
                            QuoteCompare."Dimension Set ID" := PurchaseLine."Dimension Set ID";
                            QuoteCompare."Line No." := QuoteCompare."Line No." + 10000;
                            QuoteCompare."Document Date" := PurchaseHeader."Document Date";
                            QuoteCompare."Due Date" := PurchaseHeader."Due Date";
                            QuoteCompare."Requested Receipt Date" := PurchaseHeader."Requested Receipt Date";
                            QuoteCompare."Parent Vendor" := PurchaseHeader."Buy-from Vendor No.";
                            QuoteCompare."RFQ No." := RFQNumber;
                            QuoteCompare."Capex No." := PurchaseLine."Capex No.";
                            QuoteCompare."Capex Line No." := PurchaseLine."Capex Line No.";
                            QuoteCompare."Budget Name" := PurchaseLine."Budget Name";
                            QuoteCompare."FA Posting Group" := PurchaseLine."FA Posting Group";
                            //PhaniFeb102021 >>
                            QuoteCompare."VAT Bus. Posting Group" := PurchaseLine."VAT Bus. Posting Group";
                            QuoteCompare."VAT Prod. Posting Group" := PurchaseLine."VAT Prod. Posting Group";
                            //PhaniFeb102021 <<
                            PurchaseSetup.GET();

                            IF PurchaseSetup."Delivery Required" = TRUE THEN
                                QuoteCompare.Delivery
                                 := CalculateDelivery(PurchaseLine."Buy-from Vendor No.", PurchaseLine."No.", PurchaseHeader."RFQ No.");
                            IF PurchaseSetup."Quality Required" = TRUE THEN
                                QuoteCompare.Quality := CalculateQuality(PurchaseLine."Buy-from Vendor No.", PurchaseLine."No.", PurchaseHeader."RFQ No.")
                      ;

                            QuoteCompare.Structure := '';
                            QuoteCompare."Line Amount" := 0;
                            QuoteCompare.Level := 1;
                            QuoteCompare."Parent Quote No." := PurchaseLine."Document No.";

                            IF PurchaseSetup."Payment Terms Required" = TRUE THEN BEGIN
                                IF QuoteCompare."Payment Term Code" = '' THEN
                                    ERROR(Text006Lbl, QuoteCompare."Parent Quote No.");
                                IF PaymentTerms.GET(QuoteCompare."Payment Term Code") THEN BEGIN
                                    PurchaseSetup.GET();
                                    QuoteCompare.Rating := PaymentTerms.Rating;
                                    IF QuoteCompare.Rating = 0 THEN
                                        ERROR(Text005Lbl, QuoteCompare."Payment Term Code");
                                END;
                            END;

                            QuoteCompare."Variant Code" := PurchaseLine."Variant Code";
                            QuoteCompare.Amount := (PurchaseLine.Quantity * PurchaseLine."Direct Unit Cost") - QuoteCompare.Discount;
                            Amount := Amount + QuoteCompare.Amount;

                            QuoteCompareAmount.SETRANGE("RFQ No.", QuoteCompare."RFQ No.");
                            QuoteCompareAmount.SETRANGE("Quote No.", PurchaseLine."Document No.");
                            QuoteCompareAmount.SETRANGE(QuoteCompareAmount.Level, 0);
                            IF QuoteCompareAmount.FIND('-') THEN BEGIN
                                QuoteCompareAmount."Total Amount" := Amount;
                                QuoteCompareAmount.MODIFY();
                            END;
                            QuoteCompare.Department := PurchaseLine."Shortcut Dimension 2 Code";
                            QuoteCompare."Capex No." := PurchaseLine."Capex No.";
                            QuoteCompare."Capex Line No." := PurchaseLine."Capex Line No.";
                            QuoteCompare."Budget Name" := PurchaseLine."Budget Name";
                            QuoteCompare."FA Posting Group" := PurchaseLine."FA Posting Group";
                            QuoteCompare."Service Code" := PurchaseLine."Service Code";//Service08Jul2021
                            QuoteCompare."CWIP No." := PurchaseLine."CWIP No.";//FIX21Sep2021
                            QuoteCompare.INSERT();
                        UNTIL PurchaseLine.NEXT() = 0;
                END;
            UNTIL PurchaseHeader.NEXT() = 0;

        PurchaseSetup.GET();
        QuoteCompare.RESET();
        QuoteCompare.SETRANGE("RFQ No.", RFQNumber);
        QuoteCompare.SETFILTER("Item No.", '<>%1', '');
        QuoteCompare.SETCURRENTKEY("Item No.");
        IF QuoteCompare.FIND('-') THEN
            REPEAT
                IF PreviousItem <> QuoteCompare."Item No." THEN BEGIN
                    LeastLineAmount := 0;
                    QuoteCompare1.RESET();
                    QuoteCompare1.SETRANGE("RFQ No.", RFQNumber);
                    QuoteCompare1.SETFILTER("Item No.", '<>%1', '');
                    QuoteCompare1.SETRANGE("Item No.", QuoteCompare."Item No.");
                    QuoteCompare1.SETRANGE("Variant Code", QuoteCompare."Variant Code");
                    IF QuoteCompare1.FIND('-') THEN BEGIN
                        LeastLineAmount := QuoteCompare1.Amount;
                        REPEAT
                            IF LeastLineAmount > QuoteCompare1.Amount THEN
                                LeastLineAmount := QuoteCompare1.Amount;
                        UNTIL QuoteCompare1.NEXT() = 0;
                        IF QuoteCompare1.FIND('-') THEN
                            REPEAT
                                QuoteCompare1.Price := (LeastLineAmount / QuoteCompare1.Amount * 100) * PurchaseSetup."Price Weightage" / 100;
                                QuoteCompare1.MODIFY();
                            UNTIL QuoteCompare1.NEXT() = 0;
                    END;
                END ELSE
                    PreviousItem := QuoteCompare."Item No.";
            UNTIL QuoteCompare.NEXT() = 0;


        PurchaseSetup.GET();
        IF PurchaseSetup."Payment Terms Required" = TRUE THEN BEGIN
            QuoteCompare.RESET();
            QuoteCompare.SETRANGE("RFQ No.", RFQNumber);
            QuoteCompare.SETFILTER("Item No.", '<>%1', '');
            QuoteCompare.SETCURRENTKEY("Item No.");
            IF QuoteCompare.FIND('-') THEN
                REPEAT
                    IF PreviousItem <> QuoteCompare."Item No." THEN BEGIN
                        LeastLineAmount := 0;
                        QuoteCompare1.RESET();
                        QuoteCompare1.SETRANGE("RFQ No.", RFQNumber);
                        QuoteCompare1.SETFILTER("Item No.", '<>%1', '');
                        QuoteCompare1.SETRANGE("Item No.", QuoteCompare."Item No.");
                        IF QuoteCompare1.FIND('-') THEN BEGIN
                            MaxPayment := QuoteCompare1.Rating;
                            REPEAT
                                IF MaxPayment < QuoteCompare1.Rating THEN
                                    MaxPayment := QuoteCompare1.Rating;
                            UNTIL QuoteCompare1.NEXT() = 0;
                            IF QuoteCompare1.FIND('-') THEN
                                REPEAT

                                    QuoteCompare1."Payment Terms"
                                    := (QuoteCompare1.Rating / MaxPayment * 100) * PurchaseSetup."Payment Terms Weightage" / 100;
                                    QuoteCompare1.MODIFY();
                                UNTIL QuoteCompare1.NEXT() = 0;
                        END;
                    END ELSE
                        PreviousItem := QuoteCompare."Item No.";
                UNTIL QuoteCompare.NEXT() = 0;
        END;
        QuoteCompare.RESET();
        QuoteCompare.SETRANGE("RFQ No.", RFQNumber);
        QuoteCompare.SETFILTER("Item No.", '<>%1', '');
        QuoteCompare.SETCURRENTKEY("Item No.");
        IF QuoteCompare.FIND('-') THEN
            REPEAT
                QuoteCompare."Total Weightage" := QuoteCompare.Price + QuoteCompare.Delivery + QuoteCompare.Quality +
                                                   QuoteCompare."Payment Terms";
                QuoteCompare.MODIFY();
            UNTIL QuoteCompare.NEXT() = 0;

        //For Selecting the best vendor
        PreviousItem := '';
        QuoteCompare.RESET();
        QuoteCompare.SETCURRENTKEY("RFQ No.", "Item No.", "Variant Code");
        QuoteCompare.SETRANGE("RFQ No.", RFQNumber);
        QuoteCompare.SETFILTER("Item No.", '<>%1', '');
        IF QuoteCompare.FindSet() THEN
            REPEAT
                IF (PreviousItem <> QuoteCompare."Item No.") OR (ColourCode <> QuoteCompare."Variant Code")
               THEN BEGIN
                    PreviousItem := QuoteCompare."Item No.";
                    ColourCode := QuoteCompare."Variant Code";
                    QuoteCompare1.RESET();
                    QuoteCompare1.SETRANGE("RFQ No.", RFQNumber);
                    QuoteCompare1.SETRANGE("Item No.", QuoteCompare."Item No.");
                    QuoteCompare1.SETRANGE("Variant Code", QuoteCompare."Variant Code");
                    IF QuoteCompare1.FindFirst() THEN begin
                        TotalWeightage := QuoteCompare1."Total Weightage";
                        IF TotalWeightage < QuoteCompare1."Total Weightage" THEN
                            TotalWeightage := QuoteCompare1."Total Weightage";
                    END;
                    /*QuoteCompare2.RESET();
                    QuoteCompare2.SETRANGE("RFQ No.", RFQNumber);
                    QuoteCompare2.SETRANGE("Item No.", QuoteCompare."Item No.");
                    QuoteCompare2.SETRANGE("Variant Code", QuoteCompare."Variant Code");
                    QuoteCompare2.SETRANGE("Total Weightage", TotalWeightage);
                    IF QuoteCompare2.FindSet() THEN
                        repeat
                            QuoteCompare2."Carry Out Action" := TRUE;
                            QuoteCompare2.MODIFY();
                        until QuoteCompare2.next = 0;*/

                END;
            UNTIL QuoteCompare.NEXT() = 0;

        clear(Lnum);

        QuoComparsn.RESET;
        QuoComparsn.SetCurrentKey("Purch. Req Line No");
        QuoComparsn.SetRange("RFQ No.", RFQNumber);
        QuoComparsn.SetFilter("Item No.", '<>%1', '');
        IF QuoComparsn.FindSet() then
            repeat
                IF (Lnum = 0) OR (Lnum <> QuoComparsn."Purch. Req Line No") THEN BEGIN
                    clear(TotalWeightage);
                    clear(Linum);
                    QuoteCompare2.RESET();
                    QuoteCompare2.SETRANGE("RFQ No.", RFQNumber);
                    QuoteCompare2.SETRANGE("Purch. Req Line No", QuoComparsn."Purch. Req Line No");
                    IF QuoteCompare2.FindSet() THEN
                        repeat
                            IF (TotalWeightage < QuoteCompare2."Total Weightage") then begin
                                TotalWeightage := QuoteCompare2."Total Weightage";
                                Linum := QuoteCompare2."Line No.";
                            end
                            else begin
                                TotalWeightage := TotalWeightage;
                                Linum := Linum;
                            end;
                        until QuoteCompare2.next = 0;
                    Quocom.RESET;
                    Quocom.SetRange("RFQ No.", RFQNumber);
                    Quocom.SetRange("Line No.", Linum);
                    IF Quocom.FindFirst() THEN begin
                        Quocom."Carry Out Action" := true;
                        Quocom.Modify();
                    end;
                end;
                Lnum := QuoComparsn."Purch. Req Line No";
            until QuoComparsn.next = 0;
    end;

    procedure CalculateDelivery("VendorNo.": Code[20];
        "ItemNo.": Code[20];
        RFQNo: Code[20]) Delvery: Decimal;
    var
        PPSetup: Record "Purchases & Payables Setup";
        ItemVendor: Record "Item Vendor";
        ItemVendor2: Record "Item Vendor";
        PurchaseLine: Record "Purchase Line";
        MaxDeliveryPoints: Decimal;
    begin
        MaxDeliveryPoints := 0;
        ItemVendor2.RESET();
        PPSetup.GET();
        PurchaseHeader.RESET();
        PurchaseHeader.SETRANGE(PurchaseHeader."RFQ No.", RFQNo);
        IF PurchaseHeader.FIND('-') THEN
            REPEAT
                PurchaseLine.SETRANGE("Document No.", PurchaseHeader."No.");
                PurchaseLine.SETRANGE("No.", "ItemNo.");
                IF PurchaseLine.FIND('-') THEN
                    REPEAT
                        ItemVendor2.SETRANGE("Vendor No.", PurchaseLine."Buy-from Vendor No.");
                        ItemVendor2.SETRANGE("Item No.", "ItemNo.");
                        IF ItemVendor2.FIND('-') THEN
                            REPEAT
                                ItemVendor2.CALCFIELDS("Total Qty. Supplied");
                                IF (ItemVendor2."Total Qty. Supplied" <> 0) AND (ItemVendor2."Qty. Supplied With in DueDate" <> 0) THEN BEGIN
                                    ItemVendor2."Avg. Delivery Rating" := ItemVendor2."Qty. Supplied With in DueDate" / ItemVendor2."Total Qty. Supplied";
                                    ItemVendor2.MODIFY();
                                    IF MaxDeliveryPoints < ItemVendor2."Avg. Delivery Rating" THEN
                                        MaxDeliveryPoints := ItemVendor2."Avg. Delivery Rating";
                                END ELSE BEGIN
                                    PPSetup.GET();
                                    IF PPSetup."Default Delivery Rating" > MaxDeliveryPoints THEN
                                        MaxDeliveryPoints := PPSetup."Default Delivery Rating";
                                END;
                            UNTIL ItemVendor2.NEXT() = 0
                        ELSE BEGIN
                            PPSetup.GET();
                            IF PPSetup."Default Delivery Rating" > MaxDeliveryPoints THEN
                                MaxDeliveryPoints := PPSetup."Default Delivery Rating";
                        END;
                    UNTIL PurchaseLine.NEXT() = 0;
            UNTIL PurchaseHeader.NEXT() = 0;


        ItemVendor.SETRANGE("Vendor No.", "VendorNo.");
        ItemVendor.SETRANGE("Item No.", "ItemNo.");
        IF ItemVendor.FIND('-') THEN BEGIN
            ItemVendor.CALCFIELDS("Total Qty. Supplied");
            IF ItemVendor."Qty. Supplied With in DueDate" = 0 THEN
                EXIT(((PPSetup."Default Delivery Rating" / MaxDeliveryPoints)) * PPSetup."Delivery Weightage")
        END ELSE
            IF (ItemVendor."Total Qty. Supplied" <> 0) AND (ItemVendor."Qty. Supplied With in DueDate" <> 0) THEN
                EXIT(((ItemVendor."Avg. Delivery Rating" / MaxDeliveryPoints)) * PPSetup."Delivery Weightage")
            ELSE
                EXIT((PPSetup."Default Delivery Rating" / MaxDeliveryPoints) * PPSetup."Delivery Weightage");
    end;

    procedure CalculateQuality("VendorNo.": Code[20]; "ItemNo.": Code[20]; RFQNo: Code[20]) Quality: Decimal;
    var
        PPSetup: Record "Purchases & Payables Setup";
        ItemVendor2: Record "Item Vendor";
        PurchaseLine: Record "Purchase Line";
        MaxQualityPoints: Decimal;
    begin
        MaxQualityPoints := 0;
        ItemVendor2.RESET();
        PPSetup.GET();
        PurchaseHeader.RESET();
        PurchaseHeader.SETRANGE(PurchaseHeader."RFQ No.", RFQNo);
        IF PurchaseHeader.FIND('-') THEN
            REPEAT
                PurchaseLine.SETRANGE("Document No.", PurchaseHeader."No.");
                PurchaseLine.SETRANGE("No.", "ItemNo.");
                IF PurchaseLine.FIND('-') THEN
                    REPEAT
                        ItemVendor2.SETRANGE("Vendor No.", PurchaseLine."Buy-from Vendor No.");
                        ItemVendor2.SETRANGE("Item No.", "ItemNo.");
                        IF ItemVendor2.FIND('-') THEN
                            REPEAT
                                IF MaxQualityPoints < ItemVendor2."Avg. Quality Rating" THEN
                                    MaxQualityPoints := ItemVendor2."Avg. Quality Rating"
                                ELSE BEGIN
                                    PPSetup.GET();
                                    IF PPSetup."Default Quality Rating" > MaxQualityPoints THEN
                                        MaxQualityPoints := PPSetup."Default Quality Rating";
                                END;
                            UNTIL ItemVendor2.NEXT() = 0
                        ELSE BEGIN
                            PPSetup.GET();
                            IF PPSetup."Default Quality Rating" > MaxQualityPoints THEN
                                MaxQualityPoints := PPSetup."Default Quality Rating";
                        END;
                    UNTIL PurchaseLine.NEXT() = 0;
            UNTIL PurchaseHeader.NEXT() = 0;

    end;

}


