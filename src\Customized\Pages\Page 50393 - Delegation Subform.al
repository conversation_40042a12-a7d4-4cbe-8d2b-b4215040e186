page 50960 "Delegation Subform"
{
    // version DELIGATE

    DelayedInsert = true;
    DeleteAllowed = false;
    PageType = ListPart;
    SourceTable = "Delegation Request";

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Start Date Time"; "Start Date Time")
                {
                }
                field("End Date Time"; "End Date Time")
                {
                }
                field(Delegate; Delegate)
                {
                }
            }
        }
    }

    actions
    {
    }
}

