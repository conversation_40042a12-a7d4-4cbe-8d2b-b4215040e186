page 50249 "Loan Posting Group"
{
    PageType = Document;
    SourceTable = "Loan Posting Groups";
    UsageCategory = documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Posting Group"; "Posting Group")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = ALL;
                }

                field("Loan Refundable Acc."; "Loan Refundable Acc.")
                {
                    ApplicationArea = ALL;
                }
            }
        }
    }
}

