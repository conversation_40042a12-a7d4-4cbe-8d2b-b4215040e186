/// <summary>
/// Codeunit Custom-G2S (ID 50030).
/// </summary>
codeunit 50032 "Custom-G2S"
{
    TableNo = "Job Queue Entry";
    Description = 'Custom-G2S Job Scheduler';
    Permissions = tabledata "Item Ledger Entry" = M, tabledata "Purchase Mod log" = RIMD;

    trigger OnRun()
    begin
        case Rec."Parameter String" of
            'MASTERDATAPERIODCHECK':
                RunMasterDataPeriodCheck();
            'CALLOWCODE':
                FindTopLevel();
        end;
    end;

    local procedure RunMasterDataPeriodCheck()
    var
        Customer: Record Customer;
        Vendor: Record Vendor;
        FixedAsset: Record "Fixed Asset";
        BankAccount: Record "Bank Account";
        Item: Record Item;
        RecOption: Option Customer,Vendor,FixedAsset,BankAccount,Item;
    begin
        Customer.SetRange("No.");
        if Customer.FindSet() then
            repeat
                if not ((Customer."Privacy Blocked") or (Customer.Blocked = Customer.Blocked::All)) then
                    if HasNoTrans(RecOption::Customer, Customer."No.") then begin
                        Customer.Validate("Privacy Blocked", true);
                        Customer."Approval Status" := Customer."Approval Status"::Open;
                        Customer.Modify();
                    end;
            until Customer.Next() = 0;

        Vendor.SetRange("No.");
        if Vendor.FindSet() then
            repeat
                if not ((Vendor."Privacy Blocked") or (Vendor.Blocked = Vendor.Blocked::All)) then
                    if HasNoTrans(RecOption::Vendor, Vendor."No.") then begin
                        Vendor.Validate("Privacy Blocked", true);
                        Vendor."Approval Status" := Vendor."Approval Status"::Open;
                        Vendor.Modify();
                    end;
            until Vendor.Next() = 0;

        FixedAsset.SetRange("No.");
        if FixedAsset.FindSet() then
            repeat
                if not (FixedAsset.Blocked) then
                    if HasNoTrans(RecOption::FixedAsset, FixedAsset."No.") then begin
                        FixedAsset.Validate(Blocked, true);
                        FixedAsset."Approval Status" := FixedAsset."Approval Status"::Open;
                        FixedAsset.Modify();
                    end;
            until FixedAsset.Next() = 0;

        BankAccount.SetRange("No.");
        if BankAccount.FindSet() then
            repeat
                if not (BankAccount.Blocked) then
                    if HasNoTrans(RecOption::BankAccount, BankAccount."No.") then begin
                        BankAccount.Validate(Blocked, true);
                        BankAccount."Approval Status" := BankAccount."Approval Status"::Open;
                        BankAccount.Modify();
                    end;
            until BankAccount.Next() = 0;

        Item.SetRange("No.");
        if Item.FindSet() then
            repeat
                if not (Item.Blocked) then
                    if HasNoTrans(RecOption::Item, Item."No.") then begin
                        Item.Validate(Blocked, true);
                        Item."Approval Status" := Item."Approval Status"::Open;
                        Item.Modify();
                    end;
            until Item.Next() = 0;
    end;

    local procedure HasNoTrans(RecOption: Option Customer,Vendor,FixedAsset,BankAccount,Item; RecNo: Code[20]): Boolean
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
        CustLedgerEntry: Record "Cust. Ledger Entry";
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        FALedgerEntry: Record "FA Ledger Entry";
        BankAccountLedgerEntry: Record "Bank Account Ledger Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        CustomSetup: Record "Custom Setup";
        Customer: Record Customer;
        Vendor: Record Vendor;
        Item: Record Item;
        FixedAsset: Record "Fixed Asset";
        BankAccount: Record "Bank Account";
        MasterDataTemplate: Record "Master Data Template";
        DateCreated: Date;
        TransStartDate: Date;
        ValidPeriod: DateFormula;
    begin
        GeneralLedgerSetup.Get();

        case RecOption of
            RecOption::Customer:
                begin
                    Customer.Get(RecNo);

                    CustomSetup.Reset();
                    CustomSetup.SetCurrentKey(Category, "Posting Group");
                    CustomSetup.SetRange(Category, CustomSetup.Category::"Customer Posting Group");
                    CustomSetup.SetRange("Posting Group", Customer."Customer Posting Group");
                    if not CustomSetup.FindFirst() then begin
                        if Format(GeneralLedgerSetup.CHIERP_CustomerValidityPeriod) = '' then
                            exit(false)
                        else
                            ValidPeriod := GeneralLedgerSetup.CHIERP_CustomerValidityPeriod;
                    end else
                        if Format(CustomSetup."Validity Period") = '' then
                            exit(false)
                        else
                            ValidPeriod := CustomSetup."Validity Period";

                    TransStartDate := CalcDate('-' + Format(ValidPeriod), Today);

                    MasterDataTemplate.Reset();
                    MasterDataTemplate.SetCurrentKey("Master No. created");
                    MasterDataTemplate.SetRange("Master No. created", Customer."No.");
                    if MasterDataTemplate.FindFirst() then
                        if DT2Date(MasterDataTemplate."Master No. created on") > TransStartDate then
                            exit(false);

                    CustLedgerEntry.Reset();
                    CustLedgerEntry.SetCurrentKey("Customer No.", "Posting Date");
                    CustLedgerEntry.SetRange("Customer No.", RecNo);
                    CustLedgerEntry.SetRange("Posting Date", TransStartDate, Today);
                    if CustLedgerEntry.IsEmpty then
                        exit(true);
                end;
            RecOption::Vendor:
                begin
                    Vendor.Get(RecNo);

                    CustomSetup.Reset();
                    CustomSetup.SetCurrentKey(Category, "Posting Group");
                    CustomSetup.SetRange(Category, CustomSetup.Category::"Vendor Posting Group");
                    CustomSetup.SetRange("Posting Group", Vendor."Vendor Posting Group");
                    if not CustomSetup.FindFirst() then begin
                        if Format(GeneralLedgerSetup.CHIERP_VendorValidityPeriod) = '' then
                            exit(false)
                        else
                            ValidPeriod := GeneralLedgerSetup.CHIERP_VendorValidityPeriod;
                    end else
                        if Format(CustomSetup."Validity Period") = '' then
                            exit(false)
                        else
                            ValidPeriod := CustomSetup."Validity Period";

                    TransStartDate := CalcDate('-' + Format(ValidPeriod), Today);

                    MasterDataTemplate.Reset();
                    MasterDataTemplate.SetCurrentKey("Master No. created");
                    MasterDataTemplate.SetRange("Master No. created", Vendor."No.");
                    if MasterDataTemplate.FindFirst() then
                        if DT2Date(MasterDataTemplate."Master No. created on") > TransStartDate then
                            exit(false);

                    VendorLedgerEntry.Reset();
                    VendorLedgerEntry.SetCurrentKey("Vendor No.", "Posting Date");
                    VendorLedgerEntry.SetRange("Vendor No.", RecNo);
                    VendorLedgerEntry.SetRange("Posting Date", TransStartDate, Today);
                    if VendorLedgerEntry.IsEmpty then
                        exit(true);
                end;
            RecOption::BankAccount:
                begin
                    BankAccount.Get(RecNo);

                    CustomSetup.Reset();
                    CustomSetup.SetCurrentKey(Category, "Posting Group");
                    CustomSetup.SetRange(Category, CustomSetup.Category::"Bank Acc. Posting Grp");
                    CustomSetup.SetRange("Posting Group", BankAccount."Bank Acc. Posting Group");
                    if not CustomSetup.FindFirst() then begin
                        if Format(GeneralLedgerSetup.CHIERP_BankValidityPeriod) = '' then
                            exit(false)
                        else
                            ValidPeriod := GeneralLedgerSetup.CHIERP_BankValidityPeriod;
                    end else
                        if Format(CustomSetup."Validity Period") = '' then
                            exit(false)
                        else
                            ValidPeriod := CustomSetup."Validity Period";

                    TransStartDate := CalcDate('-' + Format(ValidPeriod), Today);

                    MasterDataTemplate.Reset();
                    MasterDataTemplate.SetCurrentKey("Master No. created");
                    MasterDataTemplate.SetRange("Master No. created", BankAccount."No.");
                    if MasterDataTemplate.FindFirst() then
                        if DT2Date(MasterDataTemplate."Master No. created on") > TransStartDate then
                            exit(false);

                    BankAccountLedgerEntry.Reset();
                    BankAccountLedgerEntry.SetCurrentKey("Bank Account No.", "Posting Date");
                    BankAccountLedgerEntry.SetRange("Bank Account No.", RecNo);
                    BankAccountLedgerEntry.SetRange("Posting Date", TransStartDate, Today);
                    if BankAccountLedgerEntry.IsEmpty then
                        exit(true);
                end;
            RecOption::FixedAsset:
                begin
                    FixedAsset.Get(RecNo);

                    CustomSetup.Reset();
                    CustomSetup.SetCurrentKey(Category, "Posting Group");
                    CustomSetup.SetRange(Category, CustomSetup.Category::"FA Posting Group");
                    CustomSetup.SetRange("Posting Group", FixedAsset."FA Posting Group");
                    if not CustomSetup.FindFirst() then begin
                        if Format(GeneralLedgerSetup.CHIERP_FAValidityPeriod) = '' then
                            exit(false)
                        else
                            ValidPeriod := GeneralLedgerSetup.CHIERP_FAValidityPeriod;
                    end else
                        if Format(CustomSetup."Validity Period") = '' then
                            exit(false)
                        else
                            ValidPeriod := CustomSetup."Validity Period";

                    TransStartDate := CalcDate('-' + Format(ValidPeriod), Today);

                    MasterDataTemplate.Reset();
                    MasterDataTemplate.SetCurrentKey("Master No. created");
                    MasterDataTemplate.SetRange("Master No. created", FixedAsset."No.");
                    if MasterDataTemplate.FindFirst() then
                        if DT2Date(MasterDataTemplate."Master No. created on") > TransStartDate then
                            exit(false);

                    FALedgerEntry.Reset();
                    FALedgerEntry.SetCurrentKey("FA No.", "Posting Date");
                    FALedgerEntry.SetRange("FA No.", RecNo);
                    FALedgerEntry.SetRange("Posting Date", TransStartDate, Today);
                    if FALedgerEntry.IsEmpty then
                        exit(true);
                end;
            RecOption::Item:
                begin
                    Item.Get(RecNo);

                    CustomSetup.Reset();
                    CustomSetup.SetCurrentKey(Category, "Posting Group");
                    CustomSetup.SetRange(Category, CustomSetup.Category::"Item Posting Grp");
                    CustomSetup.SetRange("Posting Group", Item."Inventory Posting Group");
                    if not CustomSetup.FindFirst() then begin
                        if Format(GeneralLedgerSetup.CHIERP_ItemValidityPeriod) = '' then
                            exit(false)
                        else
                            ValidPeriod := GeneralLedgerSetup.CHIERP_ItemValidityPeriod;
                    end else
                        if Format(CustomSetup."Validity Period") = '' then
                            exit(false)
                        else
                            ValidPeriod := CustomSetup."Validity Period";

                    TransStartDate := CalcDate('-' + Format(ValidPeriod), Today);

                    MasterDataTemplate.Reset();
                    MasterDataTemplate.SetCurrentKey("Master No. created");
                    MasterDataTemplate.SetRange("Master No. created", Item."No.");
                    if MasterDataTemplate.FindFirst() then
                        if DT2Date(MasterDataTemplate."Master No. created on") > TransStartDate then
                            exit(false);

                    ItemLedgerEntry.Reset();
                    ItemLedgerEntry.SetCurrentKey("Item No.", "Posting Date");
                    ItemLedgerEntry.SetRange("Item No.", RecNo);
                    ItemLedgerEntry.SetRange("Posting Date", TransStartDate, Today);
                    if ItemLedgerEntry.IsEmpty then
                        exit(true);
                end;
        end;
    end;

    /// <summary>
    /// AssignItemTracking.
    /// </summary>
    /// <param name="ItemJnlLine">VAR Record "Item Journal Line".</param>
    procedure AssignItemTracking(var ItemJnlLine: Record "Item Journal Line")
    var
        ItemJournalLines: Record "Item Journal Line";
        Item: Record Item;
        ReservationEntry: Record "Reservation Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;
        ReservationEntry2: Record "Reservation Entry";
    begin
        ItemJournalLines.Reset();
        ItemJournalLines.SetRange("Journal Template Name", ItemJnlLine."Journal Template Name");
        ItemJournalLines.SetRange("Journal Batch Name", ItemJnlLine."Journal Batch Name");
        ItemJournalLines.SetRange("Document No.", ItemJnlLine."Document No.");
        ItemJournalLines.SetRange("Entry Type", ItemJournalLines."Entry Type"::Consumption);
        if ItemJournalLines.FindSet() then
            repeat
                if Item.Get(ItemJournalLines."Item No.") and (Item."Item Tracking Code" <> '') then begin
                    LineQty := ItemJournalLines."Quantity (Base)";
                    ReservationEntry2.RESET;
                    IF ReservationEntry2.FINDLAST THEN
                        EntryNum := ReservationEntry2."Entry No." + 1
                    ELSE
                        EntryNum := 1;
                    ReservationEntry2.Reset();
                    ReservationEntry2.SetRange("Source ID", ItemJournalLines."Journal Template Name");
                    ReservationEntry2.SetRange("Source Subtype", 5);
                    ReservationEntry2.SetRange("Source Type", DATABASE::"Item Journal Line");
                    ReservationEntry2.SetRange("Source Ref. No.", ItemJournalLines."Line No.");
                    ReservationEntry2.SetRange("Source Batch Name", ItemJournalLines."Journal Batch Name");
                    if ReservationEntry2.FindSet() then
                        ReservationEntry2.DeleteAll();
                    ItemLedgerEntry.RESET;
                    ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
                    ItemLedgerEntry.SETRANGE("Item No.", ItemJournalLines."Item No.");
                    ItemLedgerEntry.SETRANGE("Location Code", ItemJournalLines."Location Code");
                    ItemLedgerEntry.SETRANGE("Variant Code", ItemJournalLines."Variant Code");
                    ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
                    IF ItemLedgerEntry.FINDSET THEN
                        REPEAT

                            IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                                AssignQty := LineQty;
                                LineQty := 0;
                            END ELSE BEGIN
                                AssignQty := (ItemLedgerEntry."Remaining Quantity");
                                LineQty -= AssignQty;
                            END;


                            ReservationEntry.INIT;
                            ReservationEntry."Entry No." := EntryNum;
                            ReservationEntry.VALIDATE(Positive, FALSE);
                            ReservationEntry.VALIDATE("Item No.", ItemJournalLines."Item No.");
                            ReservationEntry.VALIDATE("Location Code", ItemJournalLines."Location Code");
                            ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                            ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / ItemJournalLines."Qty. per Unit of Measure"));
                            ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                            ReservationEntry.VALIDATE("Creation Date", WorkDate());
                            ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
                            ReservationEntry.VALIDATE("Source Subtype", 5);
                            ReservationEntry.VALIDATE("Source ID", ItemJournalLines."Journal Template Name");

                            ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLines."Line No.");
                            ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                            ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                            ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                            ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                            ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                            ReservationEntry.Validate("Source Batch Name", ItemJournalLines."Journal Batch Name");
                            ReservationEntry."Created By" := USERID;
                            ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                            ReservationEntry.VALIDATE(Correction, FALSE);
                            ReservationEntry.INSERT;
                            EntryNum += 1;
                        until (ItemLedgerEntry.Next() = 0) OR (LineQty = 0);
                end;
            until ItemJournalLines.Next() = 0;
    end;

    /// <summary>
    /// CheckExternalDocNo.
    /// /// </summary>
    /// <param name="ItemJnl">VAR Record "Item Journal Line".</param>
    procedure CheckExternalDocNo(var ItemJnl: Record "Item Journal Line")
    var

    begin
        ItemJournalLrec.Reset();
        ItemJournalLrec.SetRange("Document No.", ItemJnl."Document No.");
        ItemJournalLrec.SetRange("Journal Template Name", ItemJnl."Journal Template Name");
        ItemJournalLrec.SetRange("Journal Batch Name", ItemJnl."Journal Batch Name");
        if ItemJournalLrec.FindSet() then
            repeat
                if ItemJournalLrec."External Document No." = '' then
                    Error('Please Enter External Document Number');
            //ItemJournalLrec.TestField("External Document No.");
            until ItemJournalLrec.Next() = 0;
    end;

    /// <summary>
    /// GetLastEntryNo.
    /// </summary>
    /// <returns>Return value of type Integer.</returns>
    procedure GetLastEntryNo(): Integer
    var
        TrackSpec: Record "Tracking Specification";
    begin
        TrackSpec.Reset();
        if TrackSpec.FindLast() then
            exit(TrackSpec."Entry No." + 1) else
            exit(1);
    end;


    //311023 printer management
    /// <summary>
    /// OnDocumentPrintReady.
    /// </summary>
    /// <param name="ObjectType">Option "Report","Page".</param>
    /// <param name="ObjectId">Integer.</param>
    /// <param name="ObjectPayload">JsonObject.</param>
    /// <param name="DocumentStream">InStream.</param>
    /// <param name="Success">VAR Boolean.</param>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::ReportManagement, 'OnAfterDocumentPrintReady', '', true, true)]
    procedure OnDocumentPrintReady(ObjectType: Option "Report","Page"; ObjectId: Integer; ObjectPayload: JsonObject; DocumentStream: InStream; var Success: Boolean);
    var
        ProdOrder: Record "Production Order";
        FilterNameToken, FilterToken, FilterNameViewToken : JsonToken;
        FilterName, FilterView, PreFilter, PreFilter2, PreFilter3, FilterViewNo : Text;
        FilterNameObject: JsonObject;
        FilterArray: JsonArray;
        i: Integer;
    begin
        PreFilter := 'VERSION(1) SORTING(Field2,Field1) WHERE(Field2=1(';
        PreFilter2 := ')';
        PreFilter3 := 'VERSION(1 SORTING(Field1 WHERE(Field1=1(';
        if (ObjectType = ObjectType::Report) and (ObjectId = 50041) then begin
            ObjectPayload.Get('filterviews', FilterNameToken);
            FilterArray := FilterNameToken.AsArray();
            // for i := 0 to (FilterArray.Count() - 1) do begin
            for i := 0 to 1 do begin
                FilterArray.Get(i, FilterNameToken);
                FilterNameToken.WriteTo(FilterName);
                FilterNameObject.ReadFrom(FilterName);
                FilterNameObject.Get('view', FilterNameViewToken);
                FilterView := FilterNameViewToken.AsValue().AsText();
                FilterView := FilterView.Replace(PreFilter, '');
                FilterView := FilterView.Replace(PreFilter3, '');
                FilterView := FilterView.Replace(PreFilter2, '');
                FilterViewNo := FilterView;
                i += 1;
            end;


            ProdOrder.Reset();
            ProdOrder.SetRange("No.", FilterViewNo);
            ProdOrder.SetRange("Barcode Printed?", false);
            If ProdOrder.FindFirst() then begin
                ProdOrder."Barcode Printed?" := true;
                ProdOrder.Modify();
            end
        end;
    end;



    /// <summary>
    /// ValidateRelProdOrder.
    /// </summary>
    /// <param name="Rec">VAR Record "Prod. Order Line".</param>


    //OnAfterGetCurrRecordEvent
    // [EventSubscriber(ObjectType::Page, Page::"Rel. Prod Order Customized", 'OnAfterGetCurrRecordEvent', '', true, true)]
    [EventSubscriber(ObjectType::Page, Page::"Released Prod. Order Lines", 'OnAfterGetCurrRecordEvent', '', true, true)]
    procedure ValidateRelProdOrder(var Rec: Record "Prod. Order Line")
    begin
        Rec.SetAutoCalcFields("Item Consumption Qty.");
        Rec.CalcFields("Item Consumption Qty.");
        if Rec."Item Consumption Qty." <> 0 then begin
            Rec.Validate("Item Consumption Qty.");
            //  Message(Format(Rec."Prod. Order No."));

        end;
    end;

    // G2S CAS-01293-V3X9Q5 qty transferred >>>>>>>>>>>>>>>>>>>>
    //Update Qty Transferred OnPostWhseShipment
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"TransferOrder-Post Shipment", 'OnAfterInsertTransShptLine', '', false, false)]
    local procedure RunOnAfterInsertTransShptLine(TransLine: Record "Transfer Line"; var TransShptLine: Record "Transfer Shipment Line")
    var
        TransferHeader: Record "Transfer Header";
        ProdOrderLine: Record "Prod. Order Line";
    begin
        TransferHeader.Get(TransLine."Document No.");
        if not (TransferHeader."Transfer Type" = TransferHeader."Transfer Type"::"Transfer Ticket") then
            exit;

        if ProdOrderLine.Get(ProdOrderLine.Status::Released, TransLine."Production Order No.", TransLine."Production Order Line No.") then begin
            ProdOrderLine."Qty Transfered" += TransShptLine.Quantity;
            ProdOrderLine.Modify();
        end;
    end;
    // G2S CAS-01293-V3X9Q5 qty transferred <<<<<<<<<<<<<<<<<<<<<<<<<

    /// <summary>
    /// CreateOutputLPLineUsage.
    /// </summary>
    /// <param name="pcodLPNumber">Code[20].</param>
    /// <param name="piLPLineNumber">Integer.</param>
    /// <param name="pcodProdOrderNumber">Code[20].</param>
    /// <param name="piProdOrderLineNumber">Integer.</param>
    /// <param name="pdQuantity">Decimal.</param>
    procedure CreateOutputLPLineUsage(pcodLPNumber: Code[20]; piLPLineNumber: Integer; pcodProdOrderNumber: Code[20]; piProdOrderLineNumber: Integer; pdQuantity: Decimal)
    var
        lrecLPLineUsage: Record "IWX LP Line Usage";
    begin
        lrecLPLineUsage.Init();
        lrecLPLineUsage."License Plate No." := pcodLPNumber;
        lrecLPLineUsage."License Plate Line No." := piLPLineNumber;
        lrecLPLineUsage."Source Document" := lrecLPLineUsage."Source Document"::"Prod. Order";
        lrecLPLineUsage."Source No." := pcodProdOrderNumber;
        lrecLPLineUsage."Source Line No." := piProdOrderLineNumber;
        lrecLPLineUsage."Quantity" := pdQuantity;
        lrecLPLineUsage."Posting Date" := TODAY;
        lrecLPLineUsage.Insert(true);
    end;

    /// <summary>
    /// UpdateLPLine.
    /// </summary>
    /// <param name="pcodLicensePlateNumber">Code[20].</param>
    /// <param name="pcodItemNumber">Code[20].</param>
    /// <param name="pcodVariantCode">Code[10].</param>
    /// <param name="pcodSerialNumber">Code[50].</param>
    /// <param name="pcodLotNumber">Code[50].</param>
    /// <param name="pcodPackageNumber">Code[50].</param>
    /// <param name="pdtExpirationDate">Date.</param>
    /// <param name="psBarcodeData">Text[250].</param>
    /// <param name="pdQtyChange">Decimal.</param>
    /// <param name="pcodUOM">Code[10].</param>
    /// <param name="pcodConfigCode">Code[20].</param>
    /// <param name="pdPreviousQuantityOut">VAR Decimal.</param>
    /// <param name="pdNewQuantityOut">VAR Decimal.</param>
    /// <returns>Return value of type Integer.</returns>
    procedure UpdateLPLine(pcodLicensePlateNumber: Code[20]; pcodItemNumber: Code[20]; pcodVariantCode: Code[10]; pcodSerialNumber: Code[50]; pcodLotNumber: Code[50]; pcodPackageNumber: Code[50]; pdtExpirationDate: Date; psBarcodeData: Text[250]; pdQtyChange: Decimal; pcodUOM: Code[10]; pcodConfigCode: Code[20]; var pdPreviousQuantityOut: Decimal; var pdNewQuantityOut: Decimal): Integer
    var
        lrecLPHeader: Record "IWX LP Header";
        lrecLPLine: Record "IWX LP Line";
        liNextLineNumber: Integer;
    begin
        pdPreviousQuantityOut := 0;

        lrecLPHeader.Get(pcodLicensePlateNumber);

        lrecLPLine.SetRange("License Plate No.", lrecLPHeader."No.");
        lrecLPLine.SetRange("Type", lrecLPLine."Type"::Item);
        lrecLPLine.SetRange("No.", pcodItemNumber);
        lrecLPLine.SetRange("Variant Code", pcodVariantCode);
        lrecLPLine.SetRange("Lot No.", pcodLotNumber);
        lrecLPLine.SetRange("Serial No.", pcodSerialNumber);
        lrecLPLine.SetRange(Barcode, psBarcodeData);

        if (lrecLPLine.FindFirst()) then begin
            pdPreviousQuantityOut := lrecLPLine.Quantity;

            lrecLPLine.Validate(Quantity, lrecLPLine.Quantity + pdQtyChange);
            lrecLPLine.Modify();

            if (lrecLPLine.Quantity <= 0) then
                lrecLPLine.Delete();
        end
        else
            if (pdQtyChange > 0) then begin
                liNextLineNumber := 10000;
                lrecLPLine.Reset();
                lrecLPLine.SetRange("License Plate No.", lrecLPHeader."No.");
                if (lrecLPLine.FindLast()) then
                    liNextLineNumber := lrecLPLine."Line No." + 10000;

                Clear(lrecLPLine);
                lrecLPLine.Validate("License Plate No.", pcodLicensePlateNumber);
                lrecLPLine.Validate("Line No.", liNextLineNumber);
                lrecLPLine.Validate("No.", pcodItemNumber);
                lrecLPLine.Validate("Serial No.", pcodSerialNumber);
                lrecLPLine.Validate("Lot No.", pcodLotNumber);
                lrecLPLine.Validate("Expiration Date", pdtExpirationDate);
                lrecLPLine.Validate("Variant Code", pcodVariantCode);
                lrecLPLine.Validate("Unit of Measure Code", pcodUOM);
                lrecLPLine.Validate(Quantity, pdQtyChange);
                lrecLPLine.Validate(Barcode, psBarcodeData);
                lrecLPLine.Insert(true);
            end;

        lrecLPHeader.Get(pcodLicensePlateNumber);
        lrecLPHeader."Last Modified by PDA" := pcodConfigCode;
        lrecLPHeader.Modify(true);  // ?

        pdNewQuantityOut := lrecLPLine.Quantity;

        exit(lrecLPLine."Line No.");
    end;


    /// <summary>
    /// CreateProdOutputEntry.
    /// </summary>
    /// <param name="pcodTemplateName">Code[10].</param>
    /// <param name="pcodBatchName">Code[10].</param>
    /// <param name="precProdOrderLine">Record "Prod. Order Line".</param>
    /// <param name="pcodOperationNo">Code[10].</param>
    /// <param name="pdOutputQuantity">Decimal.</param>
    /// <param name="pdScrapQuantity">Decimal.</param>
    /// <param name="pdSetupTime">Decimal.</param>
    /// <param name="pdRunTime">Decimal.</param>
    /// <param name="pcodSerialNumber">Code[50].</param>
    /// <param name="pcodLotNumber">Code[50].</param>
    /// <param name="pcodPackageNumber">Code[50].</param>
    /// <param name="pdtExpirationDate">Date.</param>
    /// <param name="pbSetFinished">Boolean.</param>
    /// <param name="pdtPostingDate">Date.</param>
    /// <param name="lbLastOperation">Boolean.</param>
    procedure CreateProdOutputEntry(pcodTemplateName: Code[10]; pcodBatchName: Code[10]; precProdOrderLine: Record "Prod. Order Line"; pcodOperationNo: Code[10]; pdOutputQuantity: Decimal; pdScrapQuantity: Decimal; pdSetupTime: Decimal; pdRunTime: Decimal; pcodSerialNumber: Code[50]; pcodLotNumber: Code[50]; pcodPackageNumber: Code[50]; pdtExpirationDate: Date; pbSetFinished: Boolean; pdtPostingDate: Date; lbLastOperation: Boolean)
    var
        lrecItemJnlTemplate: Record "Item Journal Template";
        lrecOutJnl: Record "Item Journal Line";
        lrecCapacityLedgerEntry: Record "Capacity Ledger Entry";
        lrecItem: Record Item;
        lrecItemJnlBatch: Record "Item Journal Batch";
        lcuItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
        liLineNo: Integer;
        // lbLastOperation: Boolean;
        ldPostedOutputQty: Decimal;
        IWXResMgmt: Codeunit "IWX Reservation Mgmt.2";
        IWXCommonBase: Codeunit "IWX Common Base";
        WHIProdMgmt: Codeunit "WHI Production Mgmt.";
        WHIRegMgmt: Codeunit "WHI Registration Mgmt.";
    begin
        //lbLastOperation := IsLastOperation(precProdOrderLine, pcodOperationNo);
        lrecItemJnlBatch.Get(pcodTemplateName, pcodBatchName);

        liLineNo := 1111;
        lrecItemJnlTemplate.Get(pcodTemplateName);

        lrecOutJnl.Reset();
        lrecOutJnl.SetCurrentKey("Journal Template Name", "Journal Batch Name", "Line No.");
        lrecOutJnl.SetRange("Journal Template Name", pcodTemplateName);
        lrecOutJnl.SetRange("Journal Batch Name", pcodBatchName);
        lrecOutJnl.Ascending(true);
        if (lrecOutJnl.FindLast()) then
            liLineNo := lrecOutJnl."Line No." + 10000;

        lrecOutJnl.Init();
        lrecOutJnl."Journal Template Name" := pcodTemplateName;
        lrecOutJnl."Journal Batch Name" := pcodBatchName;
        lrecOutJnl."Line No." := liLineNo;
        lrecOutJnl."Source Code" := lrecItemJnlTemplate."Source Code";
        lrecOutJnl."Document Date" := pdtPostingDate;
        lrecOutJnl."Entry Type" := lrecOutJnl."Entry Type"::Output;
        lrecOutJnl.Validate("Order Type", lrecOutJnl."Order Type"::Production);
        lrecOutJnl.Validate("Order No.", precProdOrderLine."Prod. Order No.");
        lrecOutJnl.Validate("Order Line No.", precProdOrderLine."Line No.");
        lrecOutJnl.Validate("Routing Reference No.", precProdOrderLine."Line No.");
        lrecOutJnl.Validate("Item No.", precProdOrderLine."Item No.");
        lrecOutJnl.Validate("Variant Code", precProdOrderLine."Variant Code");
        lrecOutJnl.Validate("Operation No.", pcodOperationNo);
        lrecOutJnl."Posting Date" := pdtPostingDate;

        lrecItem.Get(precProdOrderLine."Item No.");
        ldPostedOutputQty := 0;
        lrecCapacityLedgerEntry.SetRange("Order Type", lrecCapacityLedgerEntry."Order Type"::Production);
        lrecCapacityLedgerEntry.SetRange("Order No.", precProdOrderLine."Prod. Order No.");
        lrecCapacityLedgerEntry.SetRange("Order Line No.", precProdOrderLine."Line No.");
        lrecCapacityLedgerEntry.SetRange("Operation No.", pcodOperationNo);
        if (lrecCapacityLedgerEntry.FindSet(false)) then
            repeat
                ldPostedOutputQty := ldPostedOutputQty +
                    IWXCommonBase.ConvertUnitOfMeasure(lrecItem, lrecCapacityLedgerEntry."Output Quantity", lrecItem."Base Unit of Measure", precProdOrderLine."Unit of Measure Code");
            until (lrecCapacityLedgerEntry.Next() = 0);


        lrecOutJnl.Validate("Output Quantity", pdOutputQuantity);
        lrecOutJnl.Validate("Scrap Quantity", pdScrapQuantity);

        if pbSetFinished then
            if ((ldPostedOutputQty + pdOutputQuantity) >= precProdOrderLine.Quantity) then
                lrecOutJnl.Finished := true;

        if (lrecOutJnl."Location Code" = '') then
            lrecOutJnl.Validate("Location Code", precProdOrderLine."Location Code");

        if (lrecOutJnl."Bin Code" = '') then
            lrecOutJnl.Validate("Bin Code", precProdOrderLine."Bin Code");

        lrecOutJnl.Validate("Setup Time", pdSetupTime);
        lrecOutJnl.Validate("Run Time", pdRunTime);

        if lrecItemJnlBatch."Reason Code" <> '' then
            lrecOutJnl.Validate("Reason Code", lrecItemJnlBatch."Reason Code");

        lrecOutJnl.Insert(true);

        //  if (lbLastOperation) then
        IWXResMgmt.AddOutputTracking(lrecOutJnl, pcodSerialNumber, pcodLotNumber, lrecOutJnl."Output Quantity", pdtExpirationDate);

        lrecOutJnl.SetFilter("Journal Template Name", pcodTemplateName);
        lrecOutJnl.SetFilter("Journal Batch Name", pcodBatchName);
        lrecOutJnl.SetRange("Line No.", liLineNo);
        if (lrecOutJnl.Find('-')) then begin
            if WHIRegMgmt.IsWHIInstalled() then
                WHIProdMgmt.OnBeforePostOutput(lrecOutJnl, precProdorderLine);

            lcuItemJnlPostBatch.Run(lrecOutJnl);
        end;
    end;

    //scratchpad branch 050724
    [EventSubscriber(ObjectType::Table, Database::"WHI Scratchpad Entry", 'OnAfterInsertEvent', '', false, false)]
    local procedure UpdateScratchPadTbl(var Rec: Record "WHI Scratchpad Entry")
    var
        myInt: Integer;
        SplitJobID: Text;
        SplitJobIDInt: Integer;
        SplitJobIDList: List of [Text];
        FilterView, FilterViewText : Text;
        ScratchPadEntry: Record "WHI Scratchpad Entry";
        ProductionOrder: Record "Production Order";
        TrackingSpec: Record "Tracking Specification";
        IWXLP: Record "IWX LP Header";
        IWXLPLine: Record "IWX LP Line";
        IWXLPLineUsage: Record "IWX LP Line Usage";
    begin
        SplitJobID := '';
        clear(SplitJobIDList);

        if Rec."Job ID" <> '' then begin
            SplitJobID := Rec."Job ID";
            SplitJobIDList := SplitJobID.Split('_');
            SplitJobID := SplitJobIDList.Get(SplitJobIDList.Count);
            if SplitJobID <> '' then begin
                FilterView := '';
                FilterViewText := '';
                Rec."Licence Plate" := SplitJobID;
                FilterView := SplitJobIDList.Get(1);
                FilterViewText := FilterView.Replace(' 10000', '');
                Rec."Production Order" := FilterViewText;
                //scratchpad entry posting
                Rec."Item No." := SplitJobIDList.Get(2);
                Rec."Lot No." := SplitJobIDList.Get(3);
                TrackingSpec.Reset();
                TrackingSpec.SetRange("Lot No.", Rec."Lot No.");
                TrackingSpec.SetRange("Source ID", Rec."Production Order");
                TrackingSpec.SetRange("Item No.", Rec."Item No.");
                If TrackingSpec.FindFirst() then begin
                    Rec."Expiry Date" := TrackingSpec."Expiration Date";
                end;
                ScratchPadEntry.Reset();
                ScratchPadEntry.SetRange("Licence Plate", Rec."Licence Plate");
                ScratchPadEntry.SetFilter("Line No.", '<>%1', Rec."Line No.");
                if ScratchPadEntry.FindFirst() then begin
                    Rec.Duplicate := true;
                end;

                //check already posted label that is being transferred to scratchpad
                IWXLPLineUsage.Reset();
                IWXLPLineUsage.SetRange("License Plate No.", Rec."Licence Plate");
                if IWXLPLineUsage.FindFirst() then begin
                    Rec.Duplicate := true;
                end;

                IWXLPLine.reset();
                IWXLPLine.SetRange("License Plate No.", Rec."Licence Plate");
                if IWXLPLine.FindFirst() then begin
                    Rec.Duplicate := true;
                end;
                //check production order
                ProductionOrder.Reset();
                ProductionOrder.SetRange("No.", Rec."Production Order");
                ProductionOrder.SetRange(Status, ProductionOrder.Status::Released);
                if not ProductionOrder.FindFirst() then begin
                    Rec."Released Production Order" := false;
                end else begin
                    Rec."Released Production Order" := true;
                end;
                Rec.Modify();
            end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Sales Document", 'OnBeforeReleaseSalesDoc', '', false, false)]
    local procedure RunOnBeforeReleaseSalesDoc_SalesItemRestriction(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        SalesLine2: Record "Sales Line";
        SalesLine3: Record "Sales Line";
        SalesItemRestriction: Record CHIERP_SalesItemRestriction;
        SameQtyErr: Label 'Item %1 must have the quantity base as Item %2\\Refer to Item Sales Restriction Setup', Comment = '%1 is Parent Item No. and %2 is Restriction Item No.';
        MinQuatityErr: Label 'Item %1 quantity base must not be less than %2\\Refer to Item Sales Restriction Setup', Comment = '%1 is Parent Item No. and %2 is Restriction Minimum Quantity';
        MissingItemErr: Label 'Item %1 must be included on the Sales Line\\Refer to Item Sales Restriction Setup', Comment = '%1 is Restriction Item No.';
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.FindSet() then
            repeat
                SalesItemRestriction.Reset();
                SalesItemRestriction.SetRange(CHIERP_ParentItemNo, SalesLine."No.");
                SalesItemRestriction.SetFilter(CHIERP_StartingDate, '%1..%2', 0D, SalesHeader."Order Date");
                SalesItemRestriction.SetFilter(CHIERP_EndingDate, '%1|>%2', 0D, SalesHeader."Order Date");
                if SalesItemRestriction.FindFirst() then
                    repeat
                        SalesLine2.Reset();
                        SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                        SalesLine2.SetRange("Document No.", SalesHeader."No.");
                        SalesLine2.SetRange(Type, SalesLine2.Type::Item);
                        SalesLine2.SetRange("No.", SalesItemRestriction.CHIERP_ItemNo);
                        if SalesLine2.findfirst then begin
                            case SalesItemRestriction.CHIERP_QuantityRestriction of
                                SalesItemRestriction.CHIERP_QuantityRestriction::"Minimum Quantity":
                                    begin
                                        SalesLine2.CalcSums("Quantity (Base)");
                                        if SalesLine2."Quantity (Base)" < SalesItemRestriction.CHIERP_MinimumQuantity then
                                            Error(MinQuatityErr, SalesLine."No.", SalesItemRestriction.CHIERP_MinimumQuantity);
                                    end;
                                SalesItemRestriction.CHIERP_QuantityRestriction::"Same Quantity":
                                    begin
                                        SalesLine2.CalcSums("Quantity (Base)");
                                        SalesLine3.Reset();
                                        SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                                        SalesLine3.SetRange("Document No.", SalesHeader."No.");
                                        SalesLine3.SetRange(Type, SalesLine2.Type::Item);
                                        SalesLine3.SetRange("No.", SalesItemRestriction.CHIERP_ParentItemNo);
                                        SalesLine3.CalcSums("Quantity (Base)");
                                        if SalesLine2."Quantity (Base)" <> SalesLine3."Quantity (Base)" then
                                            Error(SameQtyErr, SalesLine."No.", SalesItemRestriction.CHIERP_ItemNo);
                                    end;
                            end;
                        end else
                            Error(MissingItemErr, SalesItemRestriction.CHIERP_ItemNo);
                    until SalesItemRestriction.Next() = 0;
            until SalesLine.Next() = 0
        else
            exit;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Sales Document", 'OnBeforeManualReleaseSalesDoc', '', false, false)]
    local procedure RunOnBeforeManualReleaseSalesDoc_SalesItemRestriction(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        SalesLine2: Record "Sales Line";
        SalesLine3: Record "Sales Line";
        SalesItemRestriction: Record CHIERP_SalesItemRestriction;
        SameQtyErr: Label 'Item %1 must have the quantity base as Item %2\\Refer to Item Sales Restriction Setup', Comment = '%1 is Parent Item No. and %2 is Restriction Item No.';
        MinQuatityErr: Label 'Item %1 quantity base must not be less than %2\\Refer to Item Sales Restriction Setup', Comment = '%1 is Parent Item No. and %2 is Restriction Minimum Quantity';
        MissingItemErr: Label 'Item %1 must be included on the Sales Line\\Refer to Item Sales Restriction Setup', Comment = '%1 is Restriction Item No.';
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.FindSet() then
            repeat
                SalesItemRestriction.Reset();
                SalesItemRestriction.SetRange(CHIERP_ParentItemNo, SalesLine."No.");
                SalesItemRestriction.SetFilter(CHIERP_StartingDate, '%1..%2', 0D, SalesHeader."Order Date");
                SalesItemRestriction.SetFilter(CHIERP_EndingDate, '%1|>%2', 0D, SalesHeader."Order Date");
                if SalesItemRestriction.FindFirst() then
                    repeat
                        SalesLine2.Reset();
                        SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                        SalesLine2.SetRange("Document No.", SalesHeader."No.");
                        SalesLine2.SetRange(Type, SalesLine2.Type::Item);
                        SalesLine2.SetRange("No.", SalesItemRestriction.CHIERP_ItemNo);
                        if SalesLine2.findfirst then begin
                            case SalesItemRestriction.CHIERP_QuantityRestriction of
                                SalesItemRestriction.CHIERP_QuantityRestriction::"Minimum Quantity":
                                    begin
                                        SalesLine2.CalcSums("Quantity (Base)");
                                        if SalesLine2."Quantity (Base)" < SalesItemRestriction.CHIERP_MinimumQuantity then
                                            Error(MinQuatityErr, SalesLine."No.", SalesItemRestriction.CHIERP_MinimumQuantity);
                                    end;
                                SalesItemRestriction.CHIERP_QuantityRestriction::"Same Quantity":
                                    begin
                                        SalesLine2.CalcSums("Quantity (Base)");
                                        SalesLine3.Reset();
                                        SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                                        SalesLine3.SetRange("Document No.", SalesHeader."No.");
                                        SalesLine3.SetRange(Type, SalesLine2.Type::Item);
                                        SalesLine3.SetRange("No.", SalesItemRestriction.CHIERP_ParentItemNo);
                                        SalesLine3.CalcSums("Quantity (Base)");
                                        if SalesLine2."Quantity (Base)" <> SalesLine3."Quantity (Base)" then
                                            Error(SameQtyErr, SalesLine."No.", SalesItemRestriction.CHIERP_ItemNo);
                                    end;
                            end;
                        end else
                            Error(MissingItemErr, SalesItemRestriction.CHIERP_ItemNo);
                    until SalesItemRestriction.Next() = 0;
            until SalesLine.Next() = 0
        else
            exit;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line2", 'OnBeforePostGenJnlLine', '', false, false)]
    local procedure OnBeforePostGenJnlLine2(var GenJournalLine: Record "Gen. Journal Line")
    begin
        if GenJournalLine."FA Posting Type" = GenJournalLine."FA Posting Type"::"Acquisition Cost" then begin
            GenJournalLine.TestField("Capex No.");
            GenJournalLine.TestField("Capex Line No.");
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnBeforePostGenJnlLine', '', false, false)]
    local procedure OnBeforePostGenJnlLine(var GenJournalLine: Record "Gen. Journal Line")
    begin
        if GenJournalLine."FA Posting Type" = GenJournalLine."FA Posting Type"::"Acquisition Cost" then begin
            GenJournalLine.TestField("Capex No.");
            GenJournalLine.TestField("Capex Line No.");
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Batch2", 'OnBeforePostGenJnlLine', '', false, false)]
    local procedure OnBeforePostGenJnlLineBatch2(var GenJournalLine: Record "Gen. Journal Line")
    begin
        if GenJournalLine."FA Posting Type" = GenJournalLine."FA Posting Type"::"Acquisition Cost" then begin
            GenJournalLine.TestField("Capex No.");
            GenJournalLine.TestField("Capex Line No.");
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Batch", 'OnBeforePostGenJnlLine', '', false, false)]
    local procedure OnBeforePostGenJnlLineBatch(var GenJournalLine: Record "Gen. Journal Line")
    begin
        if GenJournalLine."FA Posting Type" = GenJournalLine."FA Posting Type"::"Acquisition Cost" then begin
            GenJournalLine.TestField("Capex No.");
            GenJournalLine.TestField("Capex Line No.");
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", 'OnBeforeInsertEvent', '', false, false)]
    local procedure OnInsertSaleHeader(var Rec: Record "Sales Header")
    begin
        Rec.ValidateDocAccess(Rec."Sales Type");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", 'OnBeforeModifyEvent', '', false, false)]
    local procedure OnModifySalesHeader(var Rec: Record "Sales Header")
    begin
        Rec.ValidateDocAccess(Rec."Sales Type");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", 'OnBeforeInsertEvent', '', false, false)]
    local procedure OnInsertSalesLine(var Rec: Record "Sales Line")
    begin
        if Rec."Document No." <> '' then
            SalesHeader.SetRange("No.", Rec."Document No.");
        if SalesHeader.FindFirst() then
            SalesHeader.ValidateDocAccess(SalesHeader."Sales Type");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", 'OnBeforeModifyEvent', '', false, false)]
    local procedure OnModifySalesLine(var Rec: Record "Sales Line")
    begin
        if Rec."Document No." <> '' then
            SalesHeader.SetRange("No.", Rec."Document No.");
        if SalesHeader.FindFirst() then
            SalesHeader.ValidateDocAccess(SalesHeader."Sales Type");
    end;
    // >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9
    [EventSubscriber(ObjectType::Table, Database::"Sales Line", 'OnBeforeInsertEvent', '', false, false)]
    local procedure RunUpdComplPriceOnSalesLineInsert(var Rec: Record "Sales Line")
    var
        Customer: Record Customer;
        CustomerPostingGroup: Record "Customer Posting Group";
        SalesHeader: Record "Sales Header";
    begin
        if not Customer.Get(Rec."Sell-to Customer No.") or (Rec."Unit Price" = 0) then
            exit;

        IF SalesHeader.Get(Rec."Document Type", Rec."Document No.") THEN BEGIN
            CustomerPostingGroup.Get(SalesHeader."Customer Posting Group");
            if CustomerPostingGroup.CHIERP_Complimentary and (SalesHeader.Status = SalesHeader.Status::Open) then
                Rec.Validate("Unit Price", 0);
        END;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", 'OnBeforeModifyEvent', '', false, false)]
    local procedure RunUpdComplPriceOnSalesLineModify(var Rec: Record "Sales Line")
    var
        Customer: Record Customer;
        CustomerPostingGroup: Record "Customer Posting Group";
        SalesHeader: Record "Sales Header";
    begin
        if not Customer.Get(Rec."Sell-to Customer No.") or (Rec."Unit Price" = 0) then
            exit;

        IF SalesHeader.Get(Rec."Document Type", Rec."Document No.") THEN BEGIN
            CustomerPostingGroup.Get(SalesHeader."Customer Posting Group");
            if CustomerPostingGroup.CHIERP_Complimentary and (SalesHeader.Status = SalesHeader.Status::Open) then
                Rec.Validate("Unit Price", 0);
        END;
    end;
    // >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9

    var
        SalesHeader: Record "Sales Header";
    //Update External Doc No in item Led Entry
    //160824 ext. doc. no branch

    [EventSubscriber(ObjectType::Table, Database::"Item Ledger Entry", 'OnAfterInsertEvent', '', false, false)]
    local procedure UpdateItemLedgerExtDocNo(var Rec: Record "Item Ledger Entry"; RunTrigger: Boolean)
    var
        ProdOrder: Record "Production Order";
        ItemLedEntry: Record "Item Ledger Entry";
        TrackingSpecification: Record "Tracking Specification";
    begin
        if Rec."Entry Type" = Rec."Entry Type"::Output then begin
            ItemLedEntry.Reset();
            ItemLedEntry.Copy(Rec);
            ProdOrder.Reset();
            Prodorder.SetRange("No.", ItemLedEntry."Document No.");
            ProdOrder.SetFilter("External Document No.", '<>%1', '');
            if ProdOrder.FindFirst() then begin
                ItemLedEntry."External Document No." := ProdOrder."External Document No.";
                ItemLedEntry.Modify();
            end;
        end;
        //         Order No.
        // Order Line No.
        // Item No.
        IF Rec."Entry Type" = Rec."Entry Type"::Purchase then BEGIN
            ItemLedEntry.Reset();
            ItemLedEntry.Copy(Rec);
            TrackingSpecification.Reset();
            TrackingSpecification.SetRange("Source ID", ItemLedEntry."Order No.");
            TrackingSpecification.SetRange("Source Ref. No.", ItemLedEntry."Order Line No.");
            TrackingSpecification.SetRange("Item No.", ItemLedEntry."Item No.");
            If TrackingSpecification.FindFirst() THEN BEGIN
                ItemLedEntry."Manufacturing Date" := TrackingSpecification."Manufacturing Date";
                ItemLedEntry.Modify();
            END;
        END;
    end;

    //G2S 071024 CAS-01341-M4Z2D5
    [EventSubscriber(ObjectType::Table, Database::"Tracking Specification", 'OnBeforeValidateEvent', 'Expiration Date', false, false)]
    local procedure RunOnValidate_ExpirationDate(var Rec: Record "Tracking Specification")
    var
        ItemTrackingManagement: Codeunit "Item Tracking Management";
        ItemLedgerEntry: Record "Item Ledger Entry";
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        ItemCategory: Record "Item Category";
        ExpDate: Date;
        ExpDateErr: Label 'Expiration Date is not the same as its Ledger Expiration Date: Expiration Date should be %1', Comment = '%1 is Ledger Expiration Date';
        ManuDateErr: Label 'Manufacturing Date must have a value';
    begin
        if Rec."Expiration Date" <> 0D then begin
            //ItemTrackingManagement.GetLotSNDataSet(rec."Item No.", rec."Variant Code", rec."Lot No.", rec."Serial No.", ItemLedgerEntry);
            if not Item.Get(Rec."Item No.") then
                exit;

            if not ItemTrackingCode.Get(Item."Item Tracking Code") then
                exit;

            if ItemTrackingCode."Use Expiration Dates" then begin
                ExpDate := CalcDate(Item."Expiration Calculation", Today);

                if ExpDate <> Rec."Expiration Date" then
                    Error(ExpDateErr, ExpDate);
            end;
            //>>>> G2S 25/03/25 CAS-01412-X4D4V7
            ItemCategory.Reset();
            ItemCategory.SetRange(Code, Item."Item Category Code");
            ItemCategory.SetRange("Complusory Manufacturing Date", true);
            IF ItemCategory.FindFirst() THEN BEGIN
                IF Rec."Manufacturing Date" = 0D THEN
                    Error(ManuDateErr);
            END;
            //>>>> G2S 25/03/25 CAS-01412-X4D4V7
        end;
    end;
    //G2S 071024 CAS-01341-M4Z2D

    //Insert Manufacturing date into Reservation Entry Table G2S 25/03/25 CAS-01412-X4D4V7
    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", 'OnRegisterChangeOnAfterCreateReservEntry', '', false, false)]
    local procedure OnAfterInitFromPurchLine(var ReservEntry: Record "Reservation Entry"; TrackingSpecification: Record "Tracking Specification")
    var
        Item: Record Item;
        ItemCategory: Record "Item Category";
    BEGIN
        if not Item.Get(TrackingSpecification."Item No.") then
            exit;

        ItemCategory.Reset();
        ItemCategory.SetRange(Code, Item."Item Category Code");
        ItemCategory.SetRange("Complusory Manufacturing Date", true);
        IF ItemCategory.FindFirst() THEN BEGIN
            ReservEntry."Manufacturing Date" := TrackingSpecification."Manufacturing Date";
            ReservEntry.Modify();
            // Message(Format(ReservEntry."Manufacturing Date"));
        END;
    END;

    //OnRegisterChangeOnAfterModify  //OnAfterCopyTrackingSpec
    //Insert Manufacturing date into Reservation Entry Table G2S 25/03/25 CAS-01412-X4D4V7
    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", 'OnRegisterChangeOnAfterModify', '', false, false)]
    local procedure OnRegisterChangeOnAfterModify(var NewTrackingSpecification: Record "Tracking Specification"; var OldTrackingSpecification: Record "Tracking Specification")
    var
        // ReservationEntry: Record "Reservation Entry";
        TrackingSpecification: Record "Tracking Specification";
    BEGIN
        IF NewTrackingSpecification."Manufacturing Date" <> OldTrackingSpecification."Manufacturing Date" then begin
            TrackingSpecification.InsertManufacturingDate(NewTrackingSpecification);
        end
    END;

    [EventSubscriber(ObjectType::Codeunit, codeunit::"Purch.-Post", 'OnAfterPurchRcptLineInsert', '', false, false)]
    local procedure OnAfterInsertEvent(var PurchRcptLine: Record "Purch. Rcpt. Line"; var TempTrackingSpecification: Record "Tracking Specification")
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
    BEGIN
        ItemLedgerEntry.SetRange("Entry No.", TempTrackingSpecification."Item Ledger Entry No.");
        ItemLedgerEntry.SetFilter("Entry Type", 'Purchase');
        IF ItemLedgerEntry.FindFirst() THEN BEGIN
            ItemLedgerEntry."Manufacturing Date" := TempTrackingSpecification."Manufacturing Date";
            ItemLedgerEntry.Modify();
        END;
    END;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Reserv. Entry", 'OnBeforeCreateRemainingReservEntry', '', false, false)]
    local procedure OnBeforeDeleteEvent(var ReservationEntry: Record "Reservation Entry"; FromReservationEntry: Record "Reservation Entry")
    var
    BEGIN
        ReservationEntry."Manufacturing Date" := FromReservationEntry."Manufacturing Date";
    END;
    //Insert Manufacturing date into Reservation Entry Table G2S 25/03/25 CAS-01412-X4D4V7

    // >>>>>> G2S 13/12/2024  CAS-01381-Y2Z6B9
    [EventSubscriber(ObjectType::Table, Database::User, 'OnAfterModifyEvent', '', false, false)]
    local procedure OnAfterModifyUserEvent(var Rec: Record User)
    var
        UserSetup: Record "User Setup";
    begin
        UserSetup.Setrange("User ID", Rec."User Name");
        If UserSetup.FindFirst() THEN BEGIN
            UserSetup."User Last Date Modified" := CurrentDateTime;
            UserSetup.Modify();
            Commit();

        END;
    end;

    [EventSubscriber(ObjectType::Table, Database::User, 'OnAfterInsertEvent', '', false, false)]
    local procedure OnAfterInsertUserEvent(var Rec: Record User)
    var
        UserSetup: Record "User Setup";
        Usercreated: Boolean;
    begin
        Usercreated := false;
        IF Rec."User Name" <> '' THEN BEGIN
            UserSetup.Setrange("User ID", Rec."User Name");
            If NOT UserSetup.FindFirst() THEN BEGIN
                UserSetup.Validate("User ID", Rec."User Name");
                UserSetup."User Created Date" := CurrentDateTime;
                if UserSetup.Insert(true) = true THEN Usercreated := True;
                Commit();
            END;
            IF Usercreated THEN Message('User %1 created successfully on UserSetup', Rec."User Name");
        END;
    END;


    [EventSubscriber(ObjectType::Table, Database::"User Group Member", 'OnAfterInsertEvent', '', false, false)]
    local procedure GroupMemberOnAfterInsertUserEvent(var Rec: Record "User Group Member")
    var
        UserSetup: Record "User Setup";
        User: Record User;
        Usercreated: Boolean;
    begin
        Usercreated := false;
        IF User.Get(Rec."User Security ID") then BEGIN
            IF User."User Name" <> '' THEN BEGIN
                UserSetup.Setrange("User ID", User."User Name");
                If UserSetup.FindFirst() THEN BEGIN
                    UserSetup."User Last Date Modified" := CurrentDateTime;
                    UserSetup.Modify();
                    Commit();
                END;
            END;
        END;
    END;

    [EventSubscriber(ObjectType::Table, Database::"User Group Member", 'OnAfterModifyEvent', '', false, false)]
    local procedure GroupMemberOnAfterModifyEvent(var Rec: Record "User Group Member")
    var
        UserSetup: Record "User Setup";
        User: Record User;
        Usercreated: Boolean;
    begin
        Usercreated := false;
        IF User.Get(Rec."User Security ID") then BEGIN
            IF User."User Name" <> '' THEN BEGIN
                UserSetup.Setrange("User ID", User."User Name");
                If UserSetup.FindFirst() THEN BEGIN
                    UserSetup."User Last Date Modified" := CurrentDateTime;
                    UserSetup.Modify();
                    Commit();
                END;
            END;
        END;
    END;


    [EventSubscriber(ObjectType::Table, Database::"Access Control", 'OnAfterInsertEvent', '', false, false)]
    local procedure AccessControlOnAfterInsertEvent(var Rec: Record "Access Control")
    var
        UserSetup: Record "User Setup";
        User: Record User;
        Usercreated: Boolean;
    begin
        Usercreated := false;
        IF User.Get(Rec."User Security ID") then BEGIN
            IF User."User Name" <> '' THEN BEGIN
                UserSetup.Setrange("User ID", User."User Name");
                If UserSetup.FindFirst() THEN BEGIN
                    UserSetup."User Last Date Modified" := CurrentDateTime;
                    UserSetup.Modify();
                    Commit();
                END;
            END;
        END;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Access Control", 'OnAfterModifyEvent', '', false, false)]
    local procedure AccessControlOnAfterModifyEvent(var Rec: Record "Access Control"; var xRec: Record "Access Control")
    var
        UserSetup: Record "User Setup";
        User: Record User;
        Usercreated: Boolean;
    begin
        Usercreated := false;
        IF User.Get(Rec."User Security ID") then BEGIN
            IF User."User Name" <> '' THEN BEGIN
                UserSetup.Setrange("User ID", User."User Name");
                If UserSetup.FindFirst() THEN BEGIN
                    UserSetup."User Last Date Modified" := CurrentDateTime;
                    UserSetup.Modify();
                    Commit();
                END;
            END;
        END;
    END;
    // >>>>>> G2S 13/12/2024  CAS-01381-Y2Z6B9

    [EventSubscriber(ObjectType::Table, Database::"Purchase Mod log", 'OnAfterInsertEvent', '', false, false)]
    local procedure MyProcedure(var Rec: Record "Purchase Mod log")
    var
        purchModLog: Record "Purchase Mod log";
        PurchaseHeader: Record "Purchase Header";
        PurchaseFieldRef: FieldRef;
        PurchaseRecRef: RecordRef;
    begin
        Sleep(10000); // Simulate a delay for 10sec to check if the purchase header is released after the purchase mod log is inserted
        purchModLog.Copy(Rec);
        PurchaseHeader.SetRange("No.", purchModLog."Document No.");
        if PurchaseHeader.FindFirst() then
            if PurchaseHeader.Status = PurchaseHeader.Status::Open then begin
                PurchaseRecRef.GetTable(PurchaseHeader);
                PurchaseHeader.SetHideValidationDialog(true);

                case purchModLog."Document Status" of
                    purchModLog."Document Status"::"Released":
                        begin
                            PurchaseFieldRef := PurchaseRecRef.Field(PurchaseHeader.FieldNo(Status));
                            PurchaseFieldRef.Value := PurchaseHeader.Status::Released;
                            PurchaseRecRef.Modify();
                        end;
                    purchModLog."Document Status"::"Pending Prepayment":
                        begin
                            PurchaseFieldRef := PurchaseRecRef.Field(PurchaseHeader.FieldNo(Status));
                            PurchaseFieldRef.Value := PurchaseHeader.Status::"Pending Prepayment";
                            PurchaseRecRef.Modify();
                        end;
                end;

                // Update the purchase mod log to indicate that it has been adjusted
                purchModLog.Adjusted := true;
                purchModLog."Adjusted Time" := CurrentDateTime;
                purchModLog.Modify(true);
            end;
    end;

    var
        Text000: Label 'Calculate low-level code';
        Text001: Label 'No. #2################## @3@@@@@@@@@@@@@';
        Text002: Label 'Top-Level Items';
        Text003: Label 'BOMs';
        HideDialogs: Boolean;

    local procedure FindTopLevel()
    var
        ProdBOMLine: Record "Production BOM Line";
        BOMComp: Record "BOM Component";
        Item: Record Item;
        ProdBOMHeader: Record "Production BOM Header";
        ProdBOMHeader2: Record "Production BOM Header";
        CalcLowLevelCode: Codeunit "Calculate Low-Level Code";
        // Window: Dialog;
        WindowUpdateDateTime: DateTime;
        NoofItems: Integer;
        CountOfRecords: Integer;
        HasProductionBOM: Boolean;
    begin
        NoofItems := 0;
        // if not GuiAllowed then
        // if not Confirm(Text000, false) then
        //     exit;
        // Window.Open(
        //   '#1################## \\' +
        //   Text001);
        // WindowUpdateDateTime := CurrentDateTime;

        // Window.Update(1, Text002);

        Item.LockTable();
        Item.ModifyAll("Low-Level Code", 0);
        ProdBOMHeader.LockTable();
        ProdBOMHeader.ModifyAll("Low-Level Code", 0);

        ProdBOMLine.SetCurrentKey(Type, "No.");
        CountOfRecords := Item.Count();
        if Item.Find('-') then
            repeat
                // if CurrentDateTime - WindowUpdateDateTime > 2000 then begin
                //     // Window.Update(2, Item."No.");
                //     // Window.Update(3, Round(NoofItems / CountOfRecords * 10000, 1));
                //     // WindowUpdateDateTime := CurrentDateTime;
                // end;

                HasProductionBOM := ProdBOMHeader.Get(Item."Production BOM No.");
                if (ProdBOMHeader."Low-Level Code" = 0) or not HasProductionBOM
                then begin
                    ProdBOMLine.SetRange("No.", Item."No.");
                    ProdBOMLine.SetRange(Type, ProdBOMLine.Type::Item);

                    BOMComp.SetRange(Type, BOMComp.Type::Item);
                    BOMComp.SetRange("No.", Item."No.");

                    if ProdBOMLine.IsEmpty and BOMComp.IsEmpty then begin
                        // handle items which are not part of any BOMs
                        Item.CalcFields("Assembly BOM");
                        if Item."Assembly BOM" then
                            CalcLowLevelCode.RecalcAsmLowerLevels(Item."No.", CalcLowLevelCode.CalcLevels(3, Item."No.", 0, 0), true);
                        if HasProductionBOM then
                            CalcLevelsForBOM(ProdBOMHeader);
                    end else
                        if HasProductionBOM then begin
                            if ProdBOMLine.Find('-') then
                                repeat
                                    // handle items which are part of un-certified, active BOMs
                                    if ProdBOMHeader2.Get(ProdBOMLine."Production BOM No.") then
                                        if ProdBOMHeader2.Status in [ProdBOMHeader2.Status::New, ProdBOMHeader2.Status::"Under Development"] then
                                            CalcLevelsForBOM(ProdBOMHeader);
                                until ProdBOMLine.Next = 0;
                        end;
                end;

                NoofItems := NoofItems + 1;
            until Item.Next = 0;

        NoofItems := 0;
        // Window.Update(1, Text003);
        ProdBOMHeader.Reset();
        ProdBOMHeader.SetCurrentKey(Status);
        ProdBOMHeader.SetRange(Status, ProdBOMHeader.Status::Certified);
        ProdBOMHeader.SetRange("Low-Level Code", 0);
        CountOfRecords := ProdBOMHeader.Count();
        if ProdBOMHeader.Find('-') then
            repeat
                // if CurrentDateTime - WindowUpdateDateTime > 2000 then begin
                //     Window.Update(2, ProdBOMHeader."No.");
                //     Window.Update(3, Round(NoofItems / CountOfRecords * 10000, 1));
                //     WindowUpdateDateTime := CurrentDateTime;
                // end;
                ProdBOMHeader2 := ProdBOMHeader;
                CalcLevelsForBOM(ProdBOMHeader2);
                NoofItems := NoofItems + 1;
            until ProdBOMHeader.Next = 0;

        // OnAfterFindTopLevel;
    end;

    local procedure CalcLevelsForBOM(var ProdBOM: Record "Production BOM Header")
    var
        ProdBOMLine: Record "Production BOM Line";
        CalcLowLevelCode: Codeunit "Calculate Low-Level Code";
    begin
        if ProdBOM.Status = ProdBOM.Status::Certified then begin
            ProdBOM."Low-Level Code" := CalcLowLevelCode.CalcLevels(ProdBOMLine.Type::"Production BOM", ProdBOM."No.", 0, 0);
            CalcLowLevelCode.RecalcLowerLevels(ProdBOM."No.", ProdBOM."Low-Level Code", true);
            ProdBOM.Modify();
        end;
    end;

    procedure SetHideDialogs(NewHideDialogs: Boolean)
    begin
        HideDialogs := NewHideDialogs;
    end;


    var
        ItemJournalLrec: Record "Item Journal Line";
}
