//G2S>>> 07/05/25>>> CAS-01435-X5M1P2
page 50615 "Vendor Routing"
{
    Caption = 'Vendor Routing';
    PageType = List;
    SourceTable = "Vendor Routing";
    ApplicationArea = All;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Routing Code"; "Routing Code")
                {
                    ApplicationArea = Basic, Suite;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = Basic, Suite;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = Basic, Suite;
                    Visible = false;
                }
                field("Bank No."; "Bank No.")
                {
                    ApplicationArea = Basic, Suite;
                }
            }
        }
    }
}
