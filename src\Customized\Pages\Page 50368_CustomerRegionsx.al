page 50368 "Customer Regions"
{  //RFC 2024-006 
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Customer Regions";

    layout
    {
        area(Content)
        {
            group(General)
            {
              /*   field("Customer No."; "Customer No.")
                {
                    ApplicationArea = All;

                } */
                field(Location; Location)
                {
                    ApplicationArea = All;

                }
                 field(Region; Region)
                {
                    ApplicationArea = All;

                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }

    var
        myInt: Integer;
}