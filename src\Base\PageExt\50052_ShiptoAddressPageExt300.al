pageextension 50052 ShiptoAddressPageExt extends "Ship-to Address"
{
    layout

    {
        addafter("Location Code")
        {
            field("Responsibility Center"; "Responsibility Center")
            {
                ApplicationArea = All;
            }
            field("Transport Location"; "Transport Location")
            {
                ApplicationArea = all;
            }
        }
        addbefore("Fax No.")
        {
            field("Global Dimension 1 Code"; "Global Dimension 1 Code")
            {
                ApplicationArea = all;
            }
        }
    }
}

