tableextension 50271 BankAccountStatementLineExt extends "Bank Account Statement Line"
{
    fields
    {
        field(50000; Description2; Text[100])
        {
            DataClassification = CustomerContent;
        }

        field(65001; "Matching Status"; Option)
        {
            OptionCaption = 'Exact,Probable,Not Matching,Cancel';
            OptionMembers = Exact,Probable,"Not Matching",Cancel;
            DataClassification = CustomerContent;
        }
        field(65002; "MatchLine No"; Integer)
        {
            DataClassification = CustomerContent;
            trigger OnLookup();
            var
                OriBnkSmnt: Record "Original Bank Statement";
            begin
                OriBnkSmnt.SETRANGE(OriBnkSmnt.Code, "Bank Account No.");
                OriBnkSmnt.SETRANGE(OriBnkSmnt."Statement No.", "Statement No.");
                OriBnkSmnt.SETRANGE(OriBnkSmnt."MatchLine No", "MatchLine No");
                if OriBnkSmnt.FINDSET then begin
                    if PAGE.RUNMODAL(0, OriBnkSmnt) = ACTION::LookupOK then;
                end;
            end;
        }
        field(65017; "Cancel No"; Integer)
        {
            DataClassification = CustomerContent;
        }


    }
}