// >>>>>> G2S CAS-01429-Q6K5N1-10087
page 50659 "Rebate Transaction Log"
{
    Caption = 'Rebate Transaction Log';
    PageType = List;
    SourceTable = "Rebate Transaction Log";
    UsageCategory = None;
    Editable = false;
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Order No."; Rec."Order No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Order No. field.';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer No. field.';
                }
                field("Customer Name "; Rec."Customer Name ")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer Name  field.';
                }
                field("Reabate Amount"; Rec."Reabate Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Rebate Amount field.';
                }
                field("Total Reabate Amount"; Rec."Total Reabate Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Total Rebate Amount field.';
                }
                field("Rebate Balance Amount"; Rec."Rebate Balance Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Rebate Balance Amount field.';
                }
                field("Entry Type"; Rec."Entry Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Entry Type field.';
                }
                field("Rebate Period"; Rec."Rebate Period")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Rebate Period field.';
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Posting Date field.';
                }
            }
        }
    }
}
// >>>>>> G2S CAS-01429-Q6K5N1-10087
