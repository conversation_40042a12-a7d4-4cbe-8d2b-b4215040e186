page 50021 "Service Vendor Rate"
{
    DeleteAllowed = false;
    PageType = Card;
    SourceTable = "Service Vendor Rate";
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Service Code"; "Service Code")
                {
                    ApplicationArea = all;
                }
                field("Service Description"; "Service Description")
                {
                    ApplicationArea = all;
                }
                field("Vendor Code"; "Vendor Code")
                {
                    ApplicationArea = all;
                }
                field("Vendor Name"; "Vendor Name")
                {
                    ApplicationArea = all;
                }
                field("Service Rate"; "Service Rate")
                {
                    ApplicationArea = all;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = all;
                }
                field(Released; Released)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
    }
}

