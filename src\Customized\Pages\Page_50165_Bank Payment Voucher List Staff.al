page 50165 "Bank Payment Voucher List Stf"
{
    // version CHI6.0

    // This page saved as from Bank Payment Voucher List (50309, List) by B2BMSOn17Jan2022

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display voucher list.
    // 3.0      SAA      06-Apr-17      -> added "'%1'" to the setfilter in OnOpenForm.
    // B2BMS    B2BMS    17-Jan-22      -> Added Staff voucher filter in SourceTableView Property.  

    Caption = 'Bank Payment Voucher List - Staff';
    Editable = false;
    PageType = List;
    CardPageId = "Bank Payment Voucher New Staff";
    SourceTable = "Voucher Header";
    UsageCategory = Lists;
    ApplicationArea = all;
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(BPV),
                            Status = FILTER(<> Released),
                            "Multiple Batch" = FILTER(false),
                            "Staff Voucher" = filter(true)); //B2BMSOn17Jan2022

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Inbox Document No."; "Inbox Document No.")
                {
                    ApplicationArea = all;
                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Posting Doc. No."; "Posting Doc. No.")
                {
                    ApplicationArea = all;
                }
            }
        }
    }


    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                action("&Show Document")
                {
                    ApplicationArea = all;
                    Caption = '&Show Document';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        VoucherRec.RESET;
                        VoucherRec.SETRANGE("Document No.", "Document No.");
                        PAGE.RUNMODAL(PAGE::"Approved Bank Receipt Vouchers", VoucherRec);
                    end;
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        //B2BMSOn17Jan2022>>
        UserSetupGRec.Get(UserId);
        if not UserSetupGRec."Open Staff Bank Pmt Voucher" then
            Error('You donot have permissions to access this page. Please Enable it in User Setup.');
        //B2BMSOn17Jan2022<<
    end;

    var
        VoucherRec: Record "Voucher Header";
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
        UserSetupGRec: Record "User Setup"; //B2BMSOn17Jan2022
}

