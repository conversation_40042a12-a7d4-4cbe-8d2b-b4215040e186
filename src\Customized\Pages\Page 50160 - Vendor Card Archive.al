page 50906 "Vendor Card Archive"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  Henry
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO       30-Jan-12      -> Form Created for Vendor Card Archive

    Caption = 'Vendor Card Archive';
    Editable = false;
    PageType = List;
    RefreshOnActivate = true;
    SourceTable = "Vendor Archive";
    UsageCategory = Lists;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {
                }
                field(Name; Name)
                {
                }
                field(Address; Address)
                {
                }
                field("Address 2"; "Address 2")
                {
                }
                field("Post Code"; "Post Code")
                {
                    Caption = 'Post Code/City';
                }
                field(City; City)
                {
                }
                field("Country/Region Code"; "Country/Region Code")
                {
                }
                field("Phone No."; "Phone No.")
                {
                }
                field("Primary Contact No."; "Primary Contact No.")
                {
                }
                field(Contact; Contact)
                {
                    Editable = ContactEditable;

                    trigger OnValidate();
                    begin
                        ContactOnAfterValidate;
                    end;
                }
                field("Search Name"; "Search Name")
                {
                }
                field("Balance (LCY)"; "Balance (LCY)")
                {

                    trigger OnDrillDown();
                    var
                        VendLedgEntry: Record "Vendor Ledger Entry";
                        DtldVendLedgEntry: Record "Detailed Vendor Ledg. Entry";
                    begin
                        DtldVendLedgEntry.SETRANGE("Vendor No.", "No.");
                        COPYFILTER("Global Dimension 1 Filter", DtldVendLedgEntry."Initial Entry Global Dim. 1");
                        COPYFILTER("Global Dimension 2 Filter", DtldVendLedgEntry."Initial Entry Global Dim. 2");
                        COPYFILTER("Currency Filter", DtldVendLedgEntry."Currency Code");
                        VendLedgEntry.DrillDownOnEntries(DtldVendLedgEntry);
                    end;
                }
                field("Purchaser Code"; "Purchaser Code")
                {
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                }
                field(Blocked; Blocked)
                {
                }
                field("Last Date Modified"; "Last Date Modified")
                {
                }
                field(Status_; Status_)
                {
                }
                field("Service Group_"; "Service Group_")
                {
                }
            }
            group(Communication)
            {
                Caption = 'Communication';
                field("Fax No."; "Fax No.")
                {
                }
                field("E-Mail"; "E-Mail")
                {
                }
                field("Home Page"; "Home Page")
                {
                }
                field("IC Partner Code"; "IC Partner Code")
                {

                    trigger OnValidate();
                    begin
                        ICPartnerCodeOnAfterValidate;
                    end;
                }
            }
            group(Invoicing)
            {
                Caption = 'Invoicing';
                field("Pay-to Vendor No."; "Pay-to Vendor No.")
                {
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                }
                field("VAT Bus. Posting Group"; "VAT Bus. Posting Group")
                {
                }
                field("Vendor Posting Group"; "Vendor Posting Group")
                {
                }
                field("Invoice Disc. Code"; "Invoice Disc. Code")
                {
                }
                field("Prices Including VAT"; "Prices Including VAT")
                {
                }
                field("Prepayment %"; "Prepayment %")
                {
                }
            }
            group(Payments)
            {
                Caption = 'Payments';
                field("Application Method"; "Application Method")
                {
                }
                field("Payment Terms Code"; "Payment Terms Code")
                {
                }
                field("Payment Method Code"; "Payment Method Code")
                {
                }
                field(Priority; Priority)
                {
                }
                field("Our Account No."; "Our Account No.")
                {
                }
                field("Block Payment Tolerance"; "Block Payment Tolerance")
                {
                }
                field("Bank No."; "Bank No.")
                {
                }
                field("Bank Name"; "Bank Name")
                {
                }
            }
            group(Receiving)
            {
                Caption = 'Receiving';
                field("Location Code"; "Location Code")
                {
                }
                field("Shipment Method Code"; "Shipment Method Code")
                {
                }
                field("Lead Time Calculation"; "Lead Time Calculation")
                {
                }
                field("Base Calendar Code"; "Base Calendar Code")
                {
                    DrillDown = false;
                }
                /*field("Customized Calendar"; CalendarMg.CustomizedCalendarExistText(CustomizedCalendar."Source Type"::Vendor, "No.", '', "Base Calendar Code"))
                {
                    Caption = 'Customized Calendar';
                    Editable = false;

                    trigger OnDrillDown();
                    begin
                        CurrPage.SAVERECORD;
                        TESTFIELD("Base Calendar Code");
                        CalendarMg.ShowCustomizedCalendar(CustomizedCalEntry."Source Type"::Vendor, "No.", '', "Base Calendar Code");
                    end;
                }*/
            }
            group("Foreign Trade")
            {
                Caption = 'Foreign Trade';
                field("Currency Code"; "Currency Code")
                {
                }
                field("Language Code"; "Language Code")
                {
                }
                field("VAT Registration No."; "VAT Registration No.")
                {
                }
            }
            group(Version)
            {
                Caption = 'Version';
                field("Version No."; "Version No.")
                {
                }
                field("Archive By"; "Archive By")
                {
                }
                field("Time Archive"; "Time Archive")
                {
                }
                field("Date Archive"; "Date Archive")
                {
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Version")
            {
                Caption = '&Version';
                /*action(Dimensions)
                {
                    Caption = 'Dimensions';
                    RunObject = Page "Default Dimensions Archive";
                    RunPageLink = "Table ID" = CONST(23),
                                  "No." = FIELD("No."),
                                  "Version No." = FIELD("Version No.");
                    ShortCutKey = 'Shift+Ctrl+D';
                }*/
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        ActivateFields;

        OnAfterGetCurrRecord;
    end;

    trigger OnFindRecord(Which: Text): Boolean;
    var
        RecordFound: Boolean;
    begin
        RecordFound := FIND(Which);
        if not RecordFound and (GETFILTER("No.") <> '') then begin
            MESSAGE(Text003, GETFILTER("No."));
            SETRANGE("No.");
            RecordFound := FIND(Which);
        end;
        exit(RecordFound);
    end;

    trigger OnInit();
    begin
        ContactEditable := true;
        MapPointVisible := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        OnAfterGetCurrRecord;
    end;

    trigger OnOpenPage();
    var
        MapMgt: Codeunit "Online Map Management";
    begin
        ActivateFields;
        if not MapMgt.TestSetup then
            MapPointVisible := false;
    end;

    var
        CalendarMgmt: Codeunit "Calendar Management";
        //CalendarMg: Codeunit "Calendar Management Ext";

        PaymentToleranceMgt: Codeunit "Payment Tolerance Management";
        CustomizedCalEntry: Record "Customized Calendar Entry";
        CustomizedCalendar: Record "Customized Calendar Change";
        Text001: Label 'Do you want to allow payment tolerance for entries that are currently open?';
        Text002: Label 'Do you want to remove payment tolerance from entries that are currently open?';
        PurchInfoPaneMgt: Codeunit "Purchases Info-Pane Management";
        Text003: Label 'The vendor %1 does not exist.';
        ApprovalMgt: Codeunit 1535;
        [InDataSet]
        MapPointVisible: Boolean;
        [InDataSet]
        ContactEditable: Boolean;

    procedure ActivateFields();
    begin
        ContactEditable := "Primary Contact No." = '';
    end;

    local procedure ContactOnAfterValidate();
    begin
        ActivateFields;
    end;

    local procedure ICPartnerCodeOnAfterValidate();
    begin
        CurrPage.UPDATE;
    end;

    local procedure OnAfterGetCurrRecord();
    begin
        xRec := Rec;
        ActivateFields;
    end;
}

