tableextension 50057 PurchaseCommentLineExt extends "Purch. Comment Line"
{
    fields
    {
        field(50000; "Purch Comment Type"; enum PurcCmmntType)
        {
            DataClassification = CustomerContent;

        }
        modify(Comment)
        {
            trigger OnBeforeValidate()
            begin
                TestField("Purch Comment Type");
            end;

        }
    }

    var
        myInt: Integer;
}