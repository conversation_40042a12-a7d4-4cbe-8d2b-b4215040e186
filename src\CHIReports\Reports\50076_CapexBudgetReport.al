report 50076 "Capex Budget Report"
{
    DefaultLayout = RDLC;
    RDLCLayout = './CHIReports\Reports\Layout\CapexBudgetReport.rdl';
    Caption = 'Capex Budget Report_50076';
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;

    dataset
    {
        dataitem("Budget Line"; "Budget Line")
        {
            DataItemTableView = SORTING("No.", "Document No.");
            RequestFilterFields = "Budget Name", Type, "Document No.", Status;
            column(FORMAT_TODAY_0_4_; FORMAT(TODAY, 0, 4))
            {
            }
            column(COMPANYNAME; COMPANYNAME)
            {
            }
            /*column(CurrReport_PAGENO; CurrReport.PAGENO)
            {
            }*/
            column(USERID; USERID)
            {
            }
            column(bnamefilter; bnamefilter)
            {
            }
            column(Budget_Line__Shortcut_Dimension_1_Code_; "Shortcut Dimension 1 Code")
            {
            }
            column(Budget_Line__Shortcut_Dimension_2_Code_; "Shortcut Dimension 2 Code")
            {
            }
            column(Budget_Line__Amount_LCY__; "Amount(LCY)")
            {
            }
            column(Budget_Line__Budget_Name_; "Budget Name")
            {
            }
            column(Budget_Line__Document_No__; "Document No.")
            {
            }
            column(Budget_Line__Line_No__; "Line No.")
            {
            }
            column(Budget_Line__FA_No__; "FA No.")
            {
            }
            column(FADescription; FADescription)
            {
            }
            column(LPONo; LPONo)
            {
            }
            column(LPODesc; LPODesc)
            {
            }
            column(GlBudamt; GlBudamt)
            {
            }
            column(BudgetBal; BudgetBal)
            {
            }
            column(POAmount; POAmount)
            {
            }
            column(POAmountLcy; POAmountLcy)
            {
            }
            column(AdvancePaid; AdvancePaid)
            {
            }
            column(OutstandingPO; OutstandingPO)
            {
            }
            column(CurrencyCode; CurrencyCode)
            {
            }
            column(CapexStatus; CapexStatus)
            {
            }
            column(UnutilisePendingLPO; UnutilisePendingLPO)
            {
            }
            column(LPOValueBalance; LPOValueBalance)
            {
            }
            column(Budget_Line__Amount_LCY___Control1000000042; "Amount(LCY)")
            {
            }
            column(Capex_Budget_ReportCaption; Capex_Budget_ReportCaptionLbl)
            {
            }
            column(CurrReport_PAGENOCaption; CurrReport_PAGENOCaptionLbl)
            {
            }
            column(Report_ID_____50173_Caption; Report_ID_____50173_CaptionLbl)
            {
            }
            column(Capex_No_Caption; Capex_No_CaptionLbl)
            {
            }
            column(LPO_No_Caption; LPO_No_CaptionLbl)
            {
            }
            column(FA_NameCaption; FA_NameCaptionLbl)
            {
            }
            column(Budget_Line__Shortcut_Dimension_1_Code_Caption; FIELDCAPTION("Shortcut Dimension 1 Code"))
            {
            }
            column(Budget_Line__Shortcut_Dimension_2_Code_Caption; FIELDCAPTION("Shortcut Dimension 2 Code"))
            {
            }
            column(Capex_AmountCaption; Capex_AmountCaptionLbl)
            {
            }
            column(AOP_Budget_BalanceCaption; AOP_Budget_BalanceCaptionLbl)
            {
            }
            column(Budget_AmountCaption; Budget_AmountCaptionLbl)
            {
            }
            column(Currency_CodeCaption; Currency_CodeCaptionLbl)
            {
            }
            column(FA_No_Caption; FA_No_CaptionLbl)
            {
            }
            column(Capex_StatusCaption; Capex_StatusCaptionLbl)
            {
            }
            column(Budget_NameCaption; Budget_NameCaptionLbl)
            {
            }
            column(Capex_Line_No_Caption; Capex_Line_No_CaptionLbl)
            {
            }
            column(LPO_DescriptionCaption; LPO_DescriptionCaptionLbl)
            {
            }
            column(PO_AmountCaption; PO_AmountCaptionLbl)
            {
            }
            column(PO_Amount__LCY_Caption; PO_Amount__LCY_CaptionLbl)
            {
            }
            column(Total_Advance_PaidCaption; Total_Advance_PaidCaptionLbl)
            {
            }
            column(OutStanding_PO_BalanceCaption; OutStanding_PO_BalanceCaptionLbl)
            {
            }
            column(LPO_Value_BalanceCaption; LPO_Value_BalanceCaptionLbl)
            {
            }
            column(Unutilize_Pending_LPOCaption; Unutilize_Pending_LPOCaptionLbl)
            {
            }
            column(Total_Amount_PaidCaption; Total_Amount_PaidCaptionLbl)
            {
            }
            column(Budget_Line_Document_Type; "Document Type")
            {
            }
            column(Budget_Line_No_; "No.")
            {
            }

            trigger OnAfterGetRecord()
            var
                CurrencyExchangeRate: Record "Currency Exchange Rate";
                PurchHeader: Record "Purchase Header";
                PurchInvHeader: Record "Purch. Inv. Header";
                FixedAsset: Record "Fixed Asset";
                BudgetNameLRec: Record "G/L Budget Name";
            begin

                CALCFIELDS("Budget Utilized Pending LPO", "Advance Paid");

                CLEAR(FADescription);
                CLEAR(LPONo);
                CLEAR(LPODesc);
                CLEAR(POAmount);
                CLEAR(POAmountLcy);
                CLEAR(AdvancePaid);
                CLEAR(OutstandingPO);
                CLEAR(UnutilisePendingLPO);
                CLEAR(PostedLPOValue);
                CLEAR(CurrencyCode);
                CLEAR(LPOValueBalance);
                CLEAR(GlBudamt);
                CLEAR(BudgetUtil);

                //To Clear Accumulated Capex Amount when Budget, CC and AccLoc are different
                IF (BudgetName <> "Budget Name") OR
                   (CC <> "Shortcut Dimension 2 Code") OR
                   (AccLoc <> "Shortcut Dimension 1 Code") THEN
                    CLEAR(CapexAmt);

                BudgetName := "Budget Name";
                CC := "Shortcut Dimension 2 Code";
                AccLoc := "Shortcut Dimension 1 Code";

                IF FARec.GET("FA No.") THEN
                    FADescription := FARec.Description;



                //Get LPO Details
                PurchaseLine.RESET;
                PurchaseLine.SETRANGE("Document Type", PurchaseLine."Document Type"::Order);
                PurchaseLine.SETRANGE("Capex No.", "Document No.");

                //PurchaseLine.SETRANGE("No.","FA No.");
                //B2B Start
                //PurchaseLine.SETRANGE("FA Posting Group", "No.");
                PurchaseLine.SetRange(Type, Type::"Fixed Asset");
                //B2B End  

                IF PurchaseLine.FINDSET THEN BEGIN
                    //LPONo:=PurchaseLine."Document No.";
                    //LPODesc:=PurchaseLine."Description 2";
                    CurrencyCode := PurchaseLine."Currency Code";
                    REPEAT
                        //B2B Start
                        IF FixedAsset.Get(PurchaseLine."No.") then
                            IF FixedAsset."FA Posting Group" = "No." then begin
                                PurchHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");
                                //B2B End                     
                                IF PurchHeader."Purchase Type" = PurchHeader."Purchase Type"::Import THEN BEGIN
                                    POAmount += PurchaseLine."Line Amount";
                                    POAmountLcy += CurrencyExchangeRate.ExchangeAmtFCYToLCY(PurchHeader."Document Date", PurchHeader."Currency Code", PurchaseLine.Amount, PurchHeader."Currency Factor")
                                END ELSE
                                    POAmountLcy += PurchaseLine.Amount;
                                AdvancePaid += PurchaseLine."Prepayment Amount";
                            end;
                    UNTIL PurchaseLine.NEXT = 0;
                    OutstandingPO := POAmountLcy - AdvancePaid;
                END;


                IF BudgetHeader.GET("Document Type", "Document No.") THEN
                    CapexStatus := BudgetHeader.Status;

                //Get Budgeted Amount
                clear(BudgetBal);
                IF FAPostingGroup.GET("No.") THEN BEGIN
                    glbentry.RESET;
                    glbentry.SETRANGE("G/L Account No.", FAPostingGroup."Acquisition Cost Account");
                    glbentry.SETFILTER("Budget Name", "Budget Name");
                    glbentry.SETRANGE("Global Dimension 1 Code", "Shortcut Dimension 1 Code");
                    glbentry.SETRANGE("Global Dimension 2 Code", "Shortcut Dimension 2 Code");
                    //FIX07Jul2021>>
                    if BudgetNameLRec.Get("Budget Name") then
                        glbentry.SetRange(Date, BudgetNameLRec."Budget Start Period", BudgetNameLRec."Budget End Period");
                    //FIX07Jul2021<<
                    IF glbentry.FINDFIRST THEN begin
                        GlBudamt += glbentry.Amount;
                        glbentry.Amount -= "Amount(LCY)";
                        glbentry.Modify();
                    end;

                    CapexAmt += "Amount(LCY)";
                    // message('%1..%2..%3', glbentry.Amount, FAPostingGroup."Acquisition Cost Account", CapexAmt);
                    BudgetBal := GlBudamt - CapexAmt
                END;

                //Get Budgeted Amount Utilized
                PurchaseLine2.RESET;
                PurchaseLine2.SETCURRENTKEY("Capex Line No.", "Capex No.", "No.");
                PurchaseLine2.SETRANGE("Capex No.", "Document No.");
                PurchaseLine2.SETRANGE("FA Posting Group", "No.");
                //PurchaseLine2.SETRANGE("No.","FA No.");
                IF PurchaseLine2.FINDSET THEN BEGIN
                    REPEAT
                    /*
                        IF (PurchaseLine2."FA Posting Type" = PurchaseLine2."FA Posting Type"::"Acquisition Cost") OR
                            (PurchaseLine2."FA Posting Type" = PurchaseLine2."FA Posting Type"::"capital work in progress") THEN
                            BudgetUtil += PurchaseLine2."Amount (LCY)";//PJ */
                    UNTIL PurchaseLine2.NEXT = 0;
                END;

                //Get Posted LPO
                PurchaseLine2.RESET;
                PurchaseLine2.SETCURRENTKEY("Capex Line No.", "Capex No.", "No.");
                PurchaseLine2.SETRANGE("Capex No.", "Document No.");
                PurchaseLine2.SETRANGE("FA Posting Group", "No.");
                IF PurchaseLine2.FINDSET THEN BEGIN
                    REPEAT
                        PurchInvHeader.Get(PurchaseLine2."Document No.");
                        PostedLPOValue += CurrencyExchangeRate.ExchangeAmtFCYToLCY(PurchInvHeader."Document Date", PurchInvHeader."Currency Code", PurchaseLine2.Amount, PurchInvHeader."Currency Factor");
                    UNTIL PurchaseLine2.NEXT = 0;
                    IF PostedLPOValue < AdvancePaid THEN
                        LPOValueBalance := POAmountLcy - PostedLPOValue;
                END;

                //Get Unutilise Pending LPO
                UnutilisePendingLPO := LPOValueBalance;
                //IF (POAmountLcy <> 0) AND (AdvancePaid =0) AND (PostedLPOValue=0) THEN
                //UnutilisePendingLPO :=POAmountLcy
                //ELSE
                //IF POAmountLcy > PostedLPOValue THEN
                //UnutilisePendingLPO :=POAmountLcy - PostedLPOValue;

                IF PrintExcel THEN BEGIN
                    /*
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT("Budget Name"), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 2, FORMAT("Document No."), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 3, FORMAT("Line No."), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT("FA No."), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 5, FORMAT(FADescription), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 6, FORMAT(LPONo), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 7, FORMAT(LPODesc), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT("Shortcut Dimension 1 Code"), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 9, FORMAT("Shortcut Dimension 2 Code"), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, FORMAT(GlBudamt), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 11, FORMAT("Amount(LCY)"), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 12, FORMAT(BudgetBal), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 13, FORMAT(POAmount), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 14, FORMAT(POAmountLcy), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 15, FORMAT(AdvancePaid), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 16, FORMAT(OutstandingPO), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 17, FORMAT(CurrencyCode), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 18, FORMAT(CapexStatus), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 19, FORMAT(BudgetUtil), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 20, FORMAT(LPOValueBalance), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 21, FORMAT(UnutilisePendingLPO), TRUE, FALSE, FALSE);
                    */
                    TempExcelBuffer.NewRow;
                    TempExcelBuffer.AddColumn(FORMAT("Budget Name"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Document No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Line No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("FA No."), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(FADescription), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(LPONo), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(LPODesc), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 1 Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Shortcut Dimension 2 Code"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(GlBudamt), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT("Amount(LCY)"), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(BudgetBal), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(POAmount), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(POAmountLcy), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(AdvancePaid), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(OutstandingPO), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(CurrencyCode), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(CapexStatus), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(BudgetUtil), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(LPOValueBalance), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(UnutilisePendingLPO), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                END;
            end;

            trigger OnPostDataItem();
            begin
                /*
                IF PrintExcel THEN BEGIN
                    
                         TempExcelBuffer.CreateBook;
                         TempExcelBuffer.CreateSheet('Capex Budget', 'Capex Budget', COMPANYNAME, USERID);
                         TempExcelBuffer.GiveUserControl;
                    
                    TempExcelBuffer.CreateNewBook('Capex Budget');
                    TempExcelBuffer.WriteSheet('Capex Budget', CompanyName, UserId);
                    TempExcelBuffer.CloseBook();
                    TempExcelBuffer.OpenExcel();
                END;
                */
            end;

            trigger OnPreDataItem();
            begin
                LastFieldNo := FIELDNO("No.");
                CreateTempBudgetEntries();
                IF PrintExcel THEN BEGIN
                    TempExcelBuffer.DELETEALL;
                    CLEAR(TempExcelBuffer);
                    /*
                    RowNo := 1;
                    EnterCell(RowNo, 1, 'Capex Budget', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 20, FORMAT(TODAY, 0, 4), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(UPPERCASE(COMPANYNAME)), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 20, FORMAT(USERID), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT('Report Filter: ' + bnamefilter), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, 'Budget Name', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 2, 'Capex No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 3, 'Capex Line No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, 'FA No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 5, 'FA Description', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 6, 'LPO No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 7, 'LPO Description', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, 'Accounting Location', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 9, 'CC', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, 'AOP', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 11, 'Approved Capex ', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 12, 'AOP Balance', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 13, 'LPO/ORP Amount', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 14, 'LPO/ORP (LCY)', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 15, 'Advance PO Paid', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 16, 'Amt. Not Yet Paid on PO', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 17, 'Currency Code', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 18, 'Capex Status', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 19, 'Budget Utilized', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 20, 'LPO Value Balance', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 21, 'Budget Unutilized', TRUE, FALSE, FALSE);
                    */
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn('Capex Budget', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(TODAY, 0, 4), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn(FORMAT(UPPERCASE(COMPANYNAME)), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn(FORMAT(USERID), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn(FORMAT('Report Filter: ' + bnamefilter), false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.NewRow();
                    TempExcelBuffer.AddColumn('Budget Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex Line No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('FA No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('FA Description', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('LPO No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('LPO Description', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Accounting Location', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('CC', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('AOP', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Approved Capex ', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('AOP Balance', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('LPO/ORP Amount', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('LPO/ORP (LCY)', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Advance PO Paid', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Amt. Not Yet Paid on PO', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Currency Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Capex Status', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Budget Utilized', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('LPO Value Balance', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                    TempExcelBuffer.AddColumn('Budget Unutilized', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
            area(content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(PrintExcel; PrintExcel)
                    {
                        ApplicationArea = All;
                        Caption = 'Copy to Excel';
                    }
                }
            }
        }

        actions
        {
        }
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        bnamefilter := "Budget Line".GETFILTERS;
    end;

    trigger OnPostReport();
    begin
        IF PrintExcel THEN BEGIN
            TempExcelBuffer.CreateNewBook('Capex Budget');
            TempExcelBuffer.WriteSheet('Capex Budget', CompanyName, UserId);
            TempExcelBuffer.CloseBook();
            TempExcelBuffer.OpenExcel();
        END;
    end;

    var
        LastFieldNo: Integer;
        FooterPrinted: Boolean;
        TotalFor: Label 'Total for';
        FARec: Record "Fixed Asset";
        FADescription: Text[80];
        LPONo: Code[20];
        LPODesc: Text[80];
        BudgetBal: Decimal;
        CapexAmt: Decimal;
        CC: Code[20];
        AccLoc: Code[20];
        BudgetName: Code[20];
        POAmount: Decimal;
        POAmountLcy: Decimal;
        CurrencyCode: Code[20];
        AdvancePaid: Decimal;
        OutstandingPO: Decimal;
        CapexStatus: Option Open,Released,"Pending Approval",Closed,"Pending Close";
        PurchaseLine2: Record "Purch. Inv. Line";
        PostedLPOValue: Decimal;
        LPOValueBalance: Decimal;
        UnutilisePendingLPO: Decimal;
        Text0001: Label 'Select Budget name';
        PrintExcel: Boolean;
        RowNo: Integer;
        ColumnNo: Integer;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        bnamefilter: Text[100];
        GlBudamt: Decimal;
        PurchaseLine: Record "Purchase Line";
        BudgetHeader: Record "Budget Header";
        FAPostingGroup: Record "FA Posting Group";
        glbentry: Record "G/L Budget Entry" temporary;
        BudgetUtil: Decimal;
        Capex_Budget_ReportCaptionLbl: Label 'Capex Budget Report';
        CurrReport_PAGENOCaptionLbl: Label 'Page';
        Report_ID_____50173_CaptionLbl: Label 'Report ID:::>>50173';
        Capex_No_CaptionLbl: Label 'Capex No.';
        LPO_No_CaptionLbl: Label 'LPO No.';
        FA_NameCaptionLbl: Label 'FA Name';
        Capex_AmountCaptionLbl: Label 'Capex Amount';
        AOP_Budget_BalanceCaptionLbl: Label 'AOP Budget Balance';
        Budget_AmountCaptionLbl: Label 'Budget Amount';
        Currency_CodeCaptionLbl: Label 'Currency Code';
        FA_No_CaptionLbl: Label 'FA No.';
        Capex_StatusCaptionLbl: Label 'Capex Status';
        Budget_NameCaptionLbl: Label 'Budget Name';
        Capex_Line_No_CaptionLbl: Label 'Capex Line No.';
        LPO_DescriptionCaptionLbl: Label 'LPO Description';
        PO_AmountCaptionLbl: Label 'PO Amount';
        PO_Amount__LCY_CaptionLbl: Label 'PO Amount (LCY)';
        Total_Advance_PaidCaptionLbl: Label 'Total Advance Paid';
        OutStanding_PO_BalanceCaptionLbl: Label 'OutStanding PO Balance';
        LPO_Value_BalanceCaptionLbl: Label 'LPO Value Balance';
        Unutilize_Pending_LPOCaptionLbl: Label 'Unutilize Pending LPO';
        Total_Amount_PaidCaptionLbl: Label 'Total Amount Paid';

    local procedure EnterCell(RowNo: Integer; ColumnNo: Integer; CellValue: Text[250]; Bold: Boolean; Italic: Boolean; UnderLine: Boolean);
    begin
        TempExcelBuffer.INIT;
        TempExcelBuffer.VALIDATE("Row No.", RowNo);
        TempExcelBuffer.VALIDATE("Column No.", ColumnNo);
        TempExcelBuffer."Cell Value as Text" := CellValue;
        TempExcelBuffer.Formula := '';
        TempExcelBuffer.Bold := Bold;
        TempExcelBuffer.Italic := Italic;
        TempExcelBuffer.Underline := UnderLine;
        TempExcelBuffer.INSERT;
    end;

    local procedure CreateTempBudgetEntries()
    var
        GLBudgetEntries: Record "G/L Budget Entry";
        BudgetNameLRec: Record "G/L Budget Name";
    begin
        glbentry.DeleteAll();
        GLBudgetEntries.Reset();
        GLBudgetEntries.SETFILTER("Budget Name", "Budget Line".GetFilter("Budget Name"));
        //FIX07Jul2021>>
        if BudgetNameLRec.Get("Budget Line".GetFilter("Budget Name")) then
            GLBudgetEntries.SetRange(Date, BudgetNameLRec."Budget Start Period", BudgetNameLRec."Budget End Period");
        //FIX07Jul2021<<
        IF GLBudgetEntries.FindSet() then
            repeat
                glbentry.Reset();
                glbentry.SETRANGE("G/L Account No.", GLBudgetEntries."G/L Account No.");
                glbentry.SETFILTER("Budget Name", GLBudgetEntries."Budget Name");
                glbentry.SETRANGE("Global Dimension 1 Code", GLBudgetEntries."Global Dimension 1 Code");
                glbentry.SETRANGE("Global Dimension 2 Code", GLBudgetEntries."Global Dimension 2 Code");
                if glbentry.FindFirst() then begin
                    glbentry.Amount += GLBudgetEntries.Amount;
                    glbentry.Modify();
                end else begin
                    glbentry.TransferFields(GLBudgetEntries);
                    glbentry.Insert();
                end;
            until GLBudgetEntries.Next() = 0;
    end;
}

