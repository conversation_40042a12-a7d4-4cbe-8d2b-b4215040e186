page 50121 "Promo. Schedule List"
{
    CardPageID = "Promo. Schedule";
    Editable = false;
    PageType = List;
    SourceTable = "Promo Schedule";
    SourceTableView = SORTING("Document Type", "No.")
                      ORDER(Ascending)
                      WHERE("Document Type" = CONST(Promo));
    UsageCategory = lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                }
                field(Description; Description)
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Retail Promo"; "Retail Promo")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Gen. Bus. Posting Group"; "Gen. Bus. Posting Group")
                {
                    ApplicationArea = all;
                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = all;
                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                action("&Card")
                {
                    ApplicationArea = all;
                    Caption = '&Card';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        if "Document Type" = "Document Type"::Promo then
                            PAGE.RUNMODAL(PAGE::"Promo. Schedule", Rec)
                        else
                            if "Document Type" = "Document Type"::"Promo Group" then
                                PAGE.RUNMODAL(PAGE::"Promotion Group", Rec);
                    end;
                }
            }
        }
    }
}

