tableextension 50277 PurchLineArchiveTabExt39 extends "Purchase Line Archive"
{
    fields
    {
        field(50000; "Order Status"; enum PurcLineOrdrStatus)
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50003; "Contract Start Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "End Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Min Qty"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50006; "Sub Document Type"; Enum SubDocumentType)
        {
            DataClassification = CustomerContent;
        }
        field(50007; "Sub Document No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Sub Document Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }

        field(50009; "PMS No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "Last Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50011; "Current Meter Reading"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "Km Covered"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "PMS Unit Price"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Km per Ltr"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "Fuel Avail"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "PMS Receipt No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50019; "Date PMS Availed"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50020; "QTY. Excluding Tolerance"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50025; "New Vendor No."; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor;
        }
        field(50035; Select; Boolean)
        {
            DataClassification = CustomerContent;
        }
        Field(50036; "Approved PO Created"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;

        }

        field(50026; "Capex No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Header"."No." WHERE(Status = CONST(Released), "Document Type" = CONST(Capex));
            //Editable = false;
        }
        field(50027; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Budget Line"."Line No." WHERE("Document No." = FIELD("Capex No."));
            //Editable = false;
        }
        field(50028; "Budget Name"; code[20])
        {
            DataClassification = CustomerContent;
        }

        field(50029; "Under Ord FORM-M Opened-No Lc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Under Order No LC"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Under Ship LC Opened-Aw Desp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Under Ship LC Opened-g Desp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50038; Status; enum SubConStatus)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50039; "Deliver Comp. For"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50040; "Qty. to Reject (Rework)"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50041; "Delivery Challan Date"; Date)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50042; "Qty. Rejected (Rework)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50043; "Qty. to Reject (V.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50044; "Qty. Rejected (V.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50045; "Delivery Challan Posted"; Integer)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50046; "Qty. to Reject (C.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50047; "Qty. Rejected (C.E.)"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            Description = 'SUBCON1.0';
        }
        field(50048; "Vendor Shipment No."; Code[20])
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50049; "Posting Date"; Date)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50050; Subcontracting; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50051; SubConSend; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50052; SubConReceive; Boolean)
        {
            DataClassification = CustomerContent;
            Description = 'SUBCON1.0';
        }
        field(50053; "Released Production Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Production Order"."No." WHERE(Status = CONST(Finished), "No." = FIELD("Prod. Order No."));
            Description = 'SUBCON1.0';
        }
        field(50055; "FA Posting Group"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "FA Posting Group";
            Editable = false;
            Description = 'FA Posting Group';
        }
        field(50056; "Tariff No."; Code[20])
        {
            FieldClass = FlowField;
            CalcFormula = lookup (Item."Tariff No." WHERE("No." = FIELD("No.")));
            Editable = false;
        }
        field(50057; "Material req No.s"; Code[250])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50060; "Under Shpmnt LC Opnd-Goods Dsp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50061; "Under Order LC Opened Aw. Desp"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50062; "Under Order FORM-M Opnd -No LC"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        /*field(50075; "Capex No."; code[20])
        {
            DataClassification = CustomerContent;

        }*/
        field(50063; "CWIP No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "CWIP Masters"."CWIP No." where(Status = const(Release), "Capitalized To FA" = const(FALSE));
        }

        field(50070; "WHT Applicable"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        //Service08Jul2021>>
        field(50071; "Service Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Service Vendor Rate"."Service Code" where("Vendor Code" = field("Buy-from Vendor No."), Released = const(true));
        }
        //Service08Jul2021<<
        field(50801; "HS Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50802; "Import File No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50803; "Clearing No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50804; "Clearing File No."; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50805; "No.2"; code[20])
        {
            DataClassification = CustomerContent;
            //B2B.P.K.T
        }
        field(50806; "WHT Group"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = WHTSetUp;//B2BPK270521
        }
        field(50807; "WHT %"; Decimal)
        {
            DataClassification = CustomerContent;//B2BPK270521

        }
        //FIX05Jun2021>>
        field(50808; "WHT Amount"; Decimal)
        {
            DataClassification = CustomerContent;

        }
        //FIX05Jun2021<<
        field(50821; "Posted Loading Slip No."; code[20])//PKONJU19
        {
            DataClassification = CustomerContent;

        }

    }
    trigger OnAfterModify()
    var
        PurcaseHeaderLRec: record "Purchase Header";
    begin
    end;

    trigger OnAfterInsert()
    var
        PurcaseHeaderLRec: record "Purchase Header";
    begin

    end;


    var
        MaintenanceLedgerEntry: Record "Maintenance Ledger Entry";
        myInt: Integer;
        PurchSetup: Record "Purchases & Payables Setup";
        UserSetup: Record "User Setup";
        Text50222: label 'You do not have permission to exceed the maximum fuel availed of %1';
        Text50207: Label 'Current Meter Reading %1 cannot be less than Last Meter Reading %2';

}