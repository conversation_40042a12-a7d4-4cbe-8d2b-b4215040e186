codeunit 50008 "Responsibility Center Filter"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0       HO      19-Aug-13    -> Codeunit created to build responsibility center filter.


    trigger OnRun();
    begin
    end;

    procedure BuildRespCentFilter(): Text[200];
    var
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";
        RespCentCount: Integer;
        BuildFilter: Text[1024];
    begin
        /*Prasanna
        RespCentCount := 0;

        UserIDRespCent.SETCURRENTKEY("User ID", "Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        if UserIDRespCent.FINDSET then
            repeat
                RespCentCount += 1;
                //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
                if RespCentCount = 1 then
                    BuildFilter := UserIDRespCent."Resp. Center Code"
                else
                    BuildFilter += '|' + UserIDRespCent."Resp. Center Code";
            until UserIDRespCent.NEXT = 0;

        if RespCentCount > 0 then
            exit(BuildFilter);

        //SETRANGE("Responsibility Center",TempResp[1],TempResp[RespCentCount]);
        //SETFILTER("Responsibility Center",BuildFilter);*/
        UserSetup.GET(UserId);
        BuildFilter := UserSetup."Gen. Jouranl Line Resp. Centre";
    end;

    procedure BuildRespCentFilter2(): Text[200];
    var
        UserIDRespCent: Record "UserID Resp. Cent. Lines";
        RespCentCount: Integer;
        BuildFilter: Text[1024];
    begin

        RespCentCount := 0;

        UserIDRespCent.SETCURRENTKEY("User ID", "Resp. Center Code");
        UserIDRespCent.SETRANGE("User ID", USERID);
        IF UserIDRespCent.FINDSET THEN
            REPEAT
                RespCentCount += 1;
                //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
                IF RespCentCount = 1 THEN
                    BuildFilter := UserIDRespCent."Resp. Center Code"
                ELSE
                    BuildFilter += '|' + UserIDRespCent."Resp. Center Code";
            UNTIL UserIDRespCent.NEXT = 0;

        IF RespCentCount > 0 THEN
            EXIT(BuildFilter);
    end;

    procedure BuildAcclocFilter(): Text[1024];
    var
        //UserIDAccloc: Record "UserID Accloc. Lines";
        RespCentCount: Integer;
        BuildFilter: Text[250];
    begin
        /*Prasanna
        RespCentCount := 0;

        UserIDAccloc.SETCURRENTKEY("User ID", "Accounting Location Code");
        UserIDAccloc.SETRANGE("User ID", USERID);
        if UserIDAccloc.FINDSET then
            repeat
                RespCentCount += 1;
                //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
                if RespCentCount = 1 then
                    BuildFilter := UserIDAccloc."Accounting Location Code"
                else
                    BuildFilter += '|' + UserIDAccloc."Accounting Location Code";
            until UserIDAccloc.NEXT = 0;

        if RespCentCount > 0 then
            exit(BuildFilter);*/
        UserSetup.GET(UserId);
        BuildFilter := UserSetup."Gen. Jouranl Line Resp. Centre";
    end;

    procedure BuildCCFilter(): Text[1024];
    var
        //serIDCC: Record "UserID Cost Center. Lines";
        RespCentCount: Integer;
        BuildFilter: Text[1024];
    begin
        /*Prasanna
        RespCentCount := 0;

        UserIDCC.SETCURRENTKEY("User ID", "Cost Center Code");
        UserIDCC.SETRANGE("User ID", USERID);
        if UserIDCC.FINDSET then
            repeat
                RespCentCount += 1;
                //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
                if RespCentCount = 1 then
                    BuildFilter := UserIDCC."Cost Center Code"
                else
                    BuildFilter += '|' + UserIDCC."Cost Center Code";
            until UserIDCC.NEXT = 0;

        if RespCentCount > 0 then
            exit(BuildFilter);*/
        UserSetup.GET(UserId);
        BuildFilter := UserSetup."Gen. Jouranl Line Resp. Centre";
    end;

    procedure BuildLocFilter(ACCLOC: Code[20]): Text[1024];
    var
        LocRec: Record Location;
        RespCentCount: Integer;
        BuildFilter: Text[1024];
    begin
        RespCentCount := 0;

        LocRec.SETRANGE(LocRec."Accounting location", ACCLOC);
        LocRec.SETRANGE(LocRec.Active, true);
        LocRec.SETRANGE(LocRec."Invoicing location", true);
        if LocRec.FINDSET then
            repeat
                RespCentCount += 1;
                //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
                if RespCentCount = 1 then
                    BuildFilter := LocRec.Code
                else
                    BuildFilter += '|' + LocRec.Code;
            until LocRec.NEXT = 0;


        if RespCentCount > 0 then
            exit(BuildFilter);
    end;

    procedure BuildIntransitLocFilter(LOCCLASS: Option " ",Branches,"Supply Chain",Engr,"Shop Floor"): Text[1024];
    var
        LocRec: Record Location;
        RespCentCount: Integer;
        BuildFilter: Text[1024];
    begin
        RespCentCount := 0;

        LocRec.SETRANGE(LocRec."Location Classification", LOCCLASS);
        // LocRec.SETRANGE(LocRec.Active,TRUE);
        //LocRec.SETRANGE(LocRec."Invoicing location",TRUE);
        if LocRec.FINDSET then
            repeat
                RespCentCount += 1;
                //TempResp[RespCentCount] :=UserIDRespCent."Resp. Center Code";
                if RespCentCount = 1 then
                    BuildFilter := LocRec.Code
                else
                    BuildFilter += '|' + LocRec.Code;
            until LocRec.NEXT = 0;


        if RespCentCount > 0 then
            exit(BuildFilter);
    end;

    var
        UserSetup: Record "User Setup";
}

