﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>10f7fcb2-da3c-4193-93a1-b0b95485298e</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.20834in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.40625in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Image Name="Barcode">
                          <Source>Database</Source>
                          <Value>=Code.code128(Fields!Bin_Content__Item_No__.Value)</Value>
                          <MIMEType>image/png</MIMEType>
                          <Sizing>Fit</Sizing>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Image>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.17708in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Bin_Content__Item_No__.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.1875in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Bin_Content__Bin_Code_.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <ZIndex>3</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Group2">
                    <GroupExpressions>
                      <GroupExpression>=Fields!Item_No______.Value</GroupExpression>
                    </GroupExpressions>
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                    </PageBreak>
                  </Group>
                  <TablixHeader>
                    <Size>0.6875in</Size>
                    <CellContents>
                      <Image Name="Image3">
                        <Source>Database</Source>
                        <Value>=Fields!Companyinfo__Picture_2_.Value</Value>
                        <MIMEType>image/png</MIMEType>
                        <Sizing>Fit</Sizing>
                        <ZIndex>1</ZIndex>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                        </Style>
                      </Image>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                  <Visibility>
                    <Hidden>=IIF(Fields!Bin_Content__Item_No__.Value= "",true,false)</Hidden>
                  </Visibility>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <Top>0.03125in</Top>
            <Height>0.77083in</Height>
            <Width>2.89584in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.20834in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.42708in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Image Name="Barcode2">
                          <Source>Database</Source>
                          <Value>=Code.code128(Fields!Bin_Code.Value)</Value>
                          <MIMEType>image/png</MIMEType>
                          <Sizing>Fit</Sizing>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Image>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.28126in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Bin_Code.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Group3">
                    <GroupExpressions>
                      <GroupExpression>=Fields!Bin_Code.Value</GroupExpression>
                    </GroupExpressions>
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                    </PageBreak>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!Bin_Code.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>0.6875in</Size>
                    <CellContents>
                      <Image Name="Image4">
                        <Source>Database</Source>
                        <Value>=Fields!Companyinfo__Picture_2__Control1000000000.Value</Value>
                        <MIMEType>image/png</MIMEType>
                        <Sizing>FitProportional</Sizing>
                        <ZIndex>1</ZIndex>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                        </Style>
                      </Image>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                  <Visibility>
                    <Hidden>=IIF(Fields!Bin_Code.Value = "", True, False)</Hidden>
                  </Visibility>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <Height>0.70834in</Height>
            <Width>2.89584in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>0.82987in</Height>
        <Style />
      </Body>
      <Width>2.89584in</Width>
      <Page>
        <PageHeight>0.99in</PageHeight>
        <PageWidth>3.15in</PageWidth>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function


Public Function StringToBarcode(value As String) As String
  Dim charPos, minCharPos As Integer
  Dim currentChar, checksum As Integer
  Dim isTableB As Boolean = True, isValid As Boolean = True
  Dim returnValue As String = String.Empty

  If (value Is Nothing OrElse value.Length = 0) Then
    Return String.Empty
  End If

  'Check for valid characters
  For charCount As Integer = 0 To value.Length - 1
    currentChar = Asc(value.Substring(charCount, 1))
    If (Not (currentChar &gt;= 32 AndAlso currentChar &lt;= 126)) Then
      isValid = False
      Exit For
    End If
  Next

  If Not (isValid) Then Return returnValue
  charPos = 0
  While (charPos &lt; value.Length)
    If (isTableB) Then
      'See if interesting to switch to table C
      'yes for 4 digits at start or end, else if 6 digits
      If (charPos = 0 OrElse charPos + 4 = value.Length) Then
        minCharPos = 4
      Else
        minCharPos = 6
      End If
      minCharPos = IsNumber(value, charPos, minCharPos)

      If (minCharPos &lt; 0) Then
        'Choice table C
        If (charPos = 0) Then
        'Starting with table C
        'char.ConvertFromUtf32(205)
          returnValue = Chr(205).ToString()
        Else
          'Switch to table C
          returnValue = returnValue + Chr(199).ToString()
        End If
        isTableB = False
      Else
        If (charPos = 0) Then
          'Starting with table B
          returnValue = Chr(204).ToString()
          'char.ConvertFromUtf32(204);
        End If
      End If
    End If

    If (Not isTableB) Then
      'We are on table C, try to process 2 digits
      minCharPos = 2
      minCharPos = IsNumber(value, charPos, minCharPos)

      If (minCharPos &lt; 0) Then
        'OK for 2 digits, process it
        currentChar = Integer.Parse(value.Substring(charPos, 2))
        If (currentChar &lt; 95) Then
          currentChar = currentChar + 32
        Else
          currentChar = currentChar + 100
        End If
        returnValue = returnValue + Chr(currentChar).ToString()
        charPos += 2
      Else
        'We haven't 2 digits, switch to table B
        returnValue = returnValue + Chr(200).ToString()
        isTableB = True
      End If
    End If

    If (isTableB) Then
      'Process 1 digit with table B
      returnValue = returnValue + value.Substring(charPos, 1)
      charPos += 1
    End If

   End While

  'Calculation of the checksum
  checksum = 0
  Dim loo As Integer
  For loo = 0 To returnValue.Length - 1
    currentChar = Asc(returnValue.Substring(loo, 1))
    If (currentChar &lt; 127) Then
      currentChar = currentChar - 32
    Else
      currentChar = currentChar - 100
    End If
    If (loo = 0) Then
      checksum = currentChar
    Else
      checksum = (checksum + (loo * currentChar)) Mod 103
    End If
  Next

  'Calculation of the checksum ASCII code
  If (checksum &lt; 95) Then
    checksum = checksum + 32
  Else
    checksum = checksum + 100
  End If

  ' Add the checksum and the STOP
  returnValue = returnValue + _
  Chr(checksum).ToString() + _
  Chr(206).ToString()

  Return returnValue
End Function

Public Function IsNumber(InputValue As String, CharPos As Integer, MinCharPos As Integer) As Integer
  MinCharPos -= 1
  If (CharPos + MinCharPos &lt; InputValue.Length) Then
    While (MinCharPos &gt;= 0)
      If (Asc(InputValue.Substring(CharPos + MinCharPos, 1)) &lt; 48 _
      OrElse Asc(InputValue.Substring(CharPos + MinCharPos, 1)) &gt; 57) Then
      Exit While
    End If
    MinCharPos -= 1
    End While
  End If
  Return MinCharPos  
End Function

Public Function Code128(ByVal stringText As String) As Byte()
  Dim result As Byte() = Nothing

  Try
  result = GenerateImage("Code 128", StringToBarcode(stringText))
  Catch ex As Exception
  End Try

  Return result
End Function

Public Function GenerateImage(ByVal fontName As String, ByVal stringText As String) As Byte()
  Dim oGraphics As System.Drawing.Graphics
  Dim barcodeSize As System.Drawing.SizeF
  Dim ms As System.IO.MemoryStream

  Using font As New System.Drawing.Font(New System.Drawing.FontFamily(fontName), 30)
  Using tmpBitmap As New System.Drawing.Bitmap(1, 1, System.Drawing.Imaging.PixelFormat.Format32bppArgb)
  oGraphics = System.Drawing.Graphics.FromImage(tmpBitmap)
  oGraphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SingleBitPerPixel
  barcodeSize = oGraphics.MeasureString(stringText, font)
  oGraphics.Dispose()
  End Using

  Using newBitmap As New System.Drawing.Bitmap(barcodeSize.Width, barcodeSize.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb)
  oGraphics = System.Drawing.Graphics.FromImage(newBitmap)
  oGraphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SingleBitPerPixel

  Using oSolidBrushWhite As New System.Drawing.SolidBrush(System.Drawing.Color.White)
  Using oSolidBrushBlack As New System.Drawing.SolidBrush(System.Drawing.Color.Black)
  oGraphics.FillRectangle(oSolidBrushWhite, New System.Drawing.Rectangle(0, 0, barcodeSize.Width, barcodeSize.Height))
  oGraphics.DrawString(stringText, font, oSolidBrushBlack, 0, 0)
  End Using

  End Using

  ms = New System.IO.MemoryStream()
  newBitmap.Save(ms, System.Drawing.Imaging.ImageFormat.Png)
  End Using
  End Using

  Return ms.ToArray()
End Function</Code>
  <EmbeddedImages>
    <EmbeddedImage Name="img">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/sBCgoKCgoKCwwMCw8QDhAPFhQTExQWIhgaGBoYIjMgJSAgJSAzLTcsKSw3LVFAODhAUV5PSk9ecWVlcY+Ij7u7+//CABEIA2YEOAMBIgACEQEDEQH/xAAxAAEAAwEBAQAAAAAAAAAAAAAAAwQFAgEGAQEBAQEBAQAAAAAAAAAAAAAAAgEDBAX/2gAMAwEAAhADEAAAAtkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB5wmRD7uSohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISohKiEqISopc0FAAAAAAAAAAAAAAHMez3x32yLvpmgoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB574yOWKXZDLAAAAAAAAAAAAAR897HMpmgoAAAAAAAAAAAAAAARYlYG5nXsVyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAee+Mjlil2QywAAAAAAAAAAAEPve83Rlg0AAAAAAAAAAAAAAABj6WDHqXqKfT9Ijk6fMDQAAAAAAAAAAAAAAAAAAAA
AAAAAAAAADz3xkcsUuyGWAAAAAAAAAAA46i2O+zKBoAAAAAAAAAAAAAAAAGVQ745fTDL0NT5/wCg6eAK4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPPfGRyxS7IZYAAAAAAAAAA8ZHJHLshlgAAAAAAAAAAAAAAAAIpa2ViDl9QB9B8/tV5rQ6eIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB574yOWKXZDLAAAAAAAAAARSw7EvplA0AAAAAAAAAAAAAV82ePAT6voe/m5Wb6hfrzqtqEwRy+oA2MfdrzTnPTxdMqlPo3nz7On0j53b3jOK4gAAAFeDLvqtpIbgAAAAAAAAAAAAAAAAAADz3xkcsUuyGWAAAAAAAAAB5w62OhlgAAAAACHEyhBnXWYnOXusLrW2yrbnaeUdm+oMq+oC+56rngRbWLy+iGdVqqT9F7ibXT5+BxoZ8e8Mv36PH1L8XmL5HnYJ7gSfQUrvT54VxAAAZ+h85Pfwc/et1Cfo/adzr80NkAAAAAAAAAAAAAAAAAB574yOWKXZDLAAAAAAAAAAilhm2AywAAAB4e1aVKPVYrkeoFAALtK7vPV+d+i+d3iE+oDdnhm6/LZmmPm2pmc/oeDOi5TJ+gwdNflyjQn03Muxn7xCfSAve6t+UL8YAAAHnzn0mFHqgEewDS0qlvp80Vdi0plXFMXEcmwAAAAAAABzShnnv5NDxm6SGauAbgAADz3xkcsUuyGWAAAAAAAAABDNDNsBlgAAAeY0+dz9gT6gAAAF2ld3nq/O/RfO7xCfUBvTQT9flhsuOxnV9lPbB5+gZeDt9t5UrMjZwPPoE98CTbMzLk6uQbAAAAADjtjCg+k5n1fO39T1gX5mBv4EemIR7QNW/Qv8AX5obzAAAAAAAy3k8euve5rIkv17Fcg2AAAHnvjI5YpdkMsAAAAAAAAACGaGbYDLAAARyZuXneHL6QNAdW9WvPkz6KvPR50CcqTRN5+d+i+dnuE+o
DZ5yFee/zSZejcwmz9Iyda/G56hTAyEe3XZA2Lnzf0VcYoZMTN12Qzrr+44+kV7F+INyGCpTj167IZev3i3E7FO589XHUZCfRrsgfQ+V+68seXwj2hnUC9ZyFcddkTI154478k9fKhn06XFBPXRsYzc3YcgnXZBuva+e2d5T0dJXHOluGhvMAAAB574yOWKXZDLAAAAAAAAAAhmhm2AywAAGHufOx6eRHtAW6m9XCYdPAAABz879F87z9gT6gAAAF+g2PpIZI+nzsEcvqAPovnfoq8vuDv51cswc/eBoamBv9PA56qbxyOTl9QGtrJ378nvz30PzzOBHsA2O+O+ngxBz94AAHW7TuX4ocd5PoDOoAAADZxtmvPbHTwgAAAAAPPfGRyxS7IZYAAAAAAAAAEM0M2wGWAAB5879F87Hr8EesDv6H576G/GF+UAADn536L53n7An1AFjU3jhtrGb4M6ga09K708GEOfvAfRfO/RV5eo5F+P5tbqcvqApu4WlXn0sjW+f3jwI9wGjpxS9fmPnvofnp78CPYBsd8Wenz/nmlQj2cDOgD21b3jcy9X52uHgj2i7s8Xbq/BTq6w+baWbHuDLbONs157Y6eEAAAAAB574yOWKXZDLAAAAAAAAAAhmhm2AywAAHz30OJHprCPaA+g+f0K8+oOnhAAA5+d+i+d5+wJ9QG1ahm6/LYe5iT2rCPcBev0L9+HCEe4B9F879FXl6HTx1Mb6P5+PZwI9SaEzaxbVWuQT2Wa2vvK6Ovznz30Pz0ergR7ANi5TudfmMDfwJ7RCPaBq36N7r82HB28SPSE+lvYP0l+UL8YEfz/0nzkevwR62zjbNee2OnhAAAAAAee+Mjlil2QywAAAAAAAAAIZoZtgMsAABn6HGX88985fSBoGno/NzV5d5mTV57qlxrQZU2bd+d+i+dnuE+oDemhm6/LYm3iT2rCPcBev0L9+HCEe4B9F879FXl6HTxszThy8EcvpgAAd
fQZetfhC/O+e+h+ej1cCPYBsXKdzr8xgb+BPaIR7QNW/Qv8AX5sOD9H87PfwR629g3q4aw6eACLAu0ufvCe7ZxtmvPbHTwgAAAAAPPfGRyxS7IZYAAAAAAAAAEM0M2wGWAAABnZn0mRHrpCPWAAAAu0ru89X536L53eIT6gN6aGbr8tibeJPasI9wF6/Qv34cIR7gH0Xzv0VeXodPGBiVtfI5fRDOoAmZrWDr8sNx899D89Hq4EewDYuU7nX5jA38Ce0Qj2ga16je6/NZGvCYL3zl9EG2reU3lq06wDOoDZxtmvPbHTwgAAAAAPPfGRyxS7IZYAAAAAAAAAEM0M2wGWAAAA89GZnfSQR6cJcpz6wygAF2ld3nq/O/RfO7xCfUBvTQT9flsTbxJ7VhHuAvX6F+/DhCPcA+i+d+iry9Dp4wOfn/osmPRREe4BqZn0FebsdPEA+e+h+ej1cCPYBsXKlvr8xgb+BPaIR7QNa9RvdfmhvOlkfSVY9GKkjj2g0AABs42zXntjp4QAAAAAHnvjI5YpdkMsAAAAAAAAACGaGbYDLAAAAAAcdilBqJ64vG6y8HrcGPbuthnaLZzWkys1pCOQ3kpXSs1pMvNaQqWO2xmtJl5rSGbpGyGwAilYzWkzrmtIUbxsBsgM/QZWa0mXmtIQzG81C+bmtJl5rSEE5vMNwDyndZWTFts640+kZSi0ic1pG5t2VshsAAAAAAPPfGRyxS7IZYAAAAAAAAAEM0M2wGWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA898ZHLFLshlgAAAAAAAAAQzQzbAZYAAAAB5k7muxtMmGaIWTMeSs1Hns0AAAAAPD156AAHHYAAAAAAAAAAAAAAAAImSsOes1Xns0AAAAAAAAAAAAA898ZHLFLshlgAAAAAAAAAQzQzbAZYAAAAGXkXKfbklin3PpBw7Pn9/5e45HWNDbwd7j0CaAAAHJl5fvP
flJufPz4+kHHr58/q4HSFms6R9R3lavDqGaAAAAAAAAAAAAAAxdj5m44HWLu9819Lytja3y5c0cJc/VsrV5dAzQAAAAAAAAHnvjI5YpdkMsAAAAAAAAACGaGbYDLAAAAQzY25mjvyblHd52I+d52T1z35C6alo4dAaAAAztD5uphHbml83pqyRcbyKHvnfmJty7sRycOoZoAAAAAAAAAAAAA8M7Gli78hoGhbOHRjbLXyjeo9Yr/AEdK7zoJoAAAAAAAAB574yOWKXZDLAAAAAAAAAAhmhm2AywAAAOPmdPK683vlyp2Zjh1Yuj89c+DrD6PK3eVjJmtKrh+dI3Z/mx9WxtnnYizaGR1z35DRNC0cOjF1Pm7nwdY6+jpafLoI42SPFpXP0k3ysuvplO5zoGgIqmJc/TyfK7JokcbJ58/XufqWbpRQNETJa2PV6TuT/ONz6vNz4c3gdI9+lytrlYoxVn35j3pP1TL1IoM0ZO5refM8VP1T5/bnZRlM72Wph9r3Ny4pXYoGgAPPfGRyxS7IZYAAAAAAAAAEM0M2wGWAAAjz8+pj4O3N3wLnNVm9cm4LeNex7j8esVE7cxY3K7vgfS/NbkVfxNT5yd8HWPfpcvZ5WIIrLz/AHzvzSxbWND15x68fOyVuvMLkk6It3BTv1b5z6DlfZ5lfPVuue/Fdpet+jwYvMBWe/T/AC/0/O5Dzncfz0tXrzC5LcWbCNx75oZutKcOjC2/mbngdYk+nx9jl0GXO16B25j3c8mi8PpZvlLHO9G35SnaW1Xh3JtDjuaDNAAee+Mjlil2QywAAAAAAAAAIZoZtgMsAAD5mK5T78h1uc9b1vnfzEf1HzNZyKlv430vO6nz9mtUvfNjXWicOmNm3aXbm28TczaVD3yse+XzWmOHRg63zlyHWJfpczU49GPd+f3A6w0Y97nXlO7kxWUO/NvZP0XO/alvAnaY7cxr5uQ1skDct/QVLfHoyr3zm54OsNunuc7V
rNCKwx35e/SZO5yseRdHDmh7ck0O+WunnHpB87PX7QL251s9OPShh6+R0gXKnYnOHXz0aAAAA898ZHLFLshlgAAAAAAAAAQzQzbAZYAApsxYj0cmrlfRxVgcumZj2K/bkFZrXOqXHpkDtzk+mwd/laKXCmqXh35e6kEU1WFS+hxPpedufMGN8rHfm98uY3Ovcrj1oQHfk98t425Th14+auUOsCS537CLz9YMCWLtzHdZo6/PvDri5/fHbms1tzF8x+XSrWO/IWDdmPP1fP6GJ0kSdI37Dnz9esNQ6QHSbH0ePscejKtYG54OsdfS427ytz1jzVGI78n0eRv87Q+/PzuzcztHNDNAAAee+Mjlil2QywAAAAAAAAAIZoZtgMsABi7WBU0x259fU/O/RcrRS0orBHo5JI7Wb9DjbORyvLHbnNv/ADSd0s00L5oYP0nzU6Fzd1vnE1PA6qeQLtJjcxPDQ3FiuPosqkmgqUsQ2smNOhWNLN35q5Su5XK8kd+Tcw2boZ6Qjd8bi1VH09DHRXvhcvfBrZ8LNDcAu28dO9cyX9Zg3Lu58t7FamUVgbmjofPIqbyP6DVjo49AAAAHnvjI5YpdkMsAAAAAAAAACGaGbYDLAAYu1BufNpI+/K/uZ+hx6K1lO/KNfK78+btPaxoVbTj0+Ua+T35+DcF7Nh+gd8rj+Y+r+drKw6QJj3WsScenyySPtzAX5NjneHQ+pw9ykLl11vTuJB9N862M9qfL9rS538ouU7h9J839PFSZuki/lFyn25CfUX0Htjj0y8j6vDrKA6Q7m25rKofVfP4qi5TT7cVBnbfEV8usV+3N7JtzvVk49Pm4NrF7cxLWcT7NjnfyqeDpD3rbneb5x6A0AAAB574yOWKXZDLAAAAAAAAAAhmhm2AywAAAAAAAAHHYp83m5DMYBrz0Z8Gu2c+90zQbDm7Dcx79kBmgV4rrc56M1x2KM87cDN8p3TKdwAajkMhmAGgccymA156K
PdtuBmgIZjPPRoCCcyjb7AN4p3zI5ABoAAAADz3xkcsUuyGWAAAAAAAAABDNDNsBlgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPPeWcSxybIZYAAAAAAAAAEMkUd8LKsbZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZVhZ5gMs9eex1BQAAAAAAAAAEEckfXzBsgAAFaq3TVLbAAAAAAAAAAAAAAAAAAAAAAAAAACPIbtsTaPQwAAAAAAAAAAAA4ym7DK1QGAAAAPfO82wOXqAAAAAAAAAAAgjkj6+YNkAADOoX6G9LunmaeSCQAAAGfLlbX0AyQAAAAAAAAAAAAAAABXIrWHt7UgyYcX6CFWLvRzAJAAAAAAAAAAAAjw9zD2+93C3WBkgAAAJorUdAjuAAAAAAAAAABBHJH18wbIAAGdQv0N6XdPM08nzK1cDWhPklT+QNad35/XybMM2NmQ+FdLctGfM1znOflCrHtz+88bujc+f0cm+MnL9q8b02qFbk1LmbpZGdFzX29vPrck89Frf9ztGYUL+Q1xz7tSXMofQKlvOfGLYqba1V62renibcxnRxwbVvRw9pkxHkeUKvO9J+o+NaVz5+/maIyM+HytvTbz63JPPRa3/c3SmFG9kNcc+1UlzKY+gU7mc48Pcw9vvdwt1gZIAAAEk/PXL0hlAAAAAAAAAAAQRyR9fMGyAABnUL9Del3TzNPJ8wN/A3Wlm7x7k6+fmZ9+he2tLz3zOeD575XX6ATyUL+SqreozVW1zksmD3zzb+gcdzywuO+K62dLmeefPQzHr2K9dLWpXtTGfn7GPtT7OLtMee1Mm2xY9rSy/fNq3q5GvMsLdwtc
bmHuNkGQxdrF2odrF2mzZmni5kN6jr7VmORnP5/2SKuu91XsTyyK1mtXS1qVrcxQztnG2ptrE22PPauZaY0W7pZfvm1a1sfYmY8Pcw93vdwt1gZIAACXizHQI7gAAAAAAAAAAAQRyR9fMGyAABnUL9Del3TzNPJ8wN/A3W/gb57n6Gfk596je29Lz3zOeD575XX6Bx3PJka+cqhNDeqomumchrjnoThcd8V13JI5J5AY9exXrpr2a1mYgxtnG2ptrF2mQY2nmNk0IdJmdQ18hVnXyNdLC3cMj3MPZbOMhi7WLtQ7WLtNmxdrKzKmxj6O1fOc548Pvlddixx3PPIrWa1XrWqtqYgxtnG2pdvE22Q4upltk0INNmfn7OM2xsY+xmR4e7hbve7gbzPRkgAPfLE170c/QDQAAAAAAAAAAAII5I+vmDZAAAzqGnR3pY08/QyPMDfxt2Hfxdo9z9ClmZl6td27wzniw7edtwSc9buxx37PPB52s/ekfcPmu7dW7mXRkYXFjiuuvJx3PIDHr3IK6aVmCeYgxtrK2vdrJ1syHF+gqGXZ4iqpYO+zvXo3plk63DMLq1BV+6+RrZMmLtZJV2srWbLDMyMBr5+9O4eOtR3O9DM9GRkVrteumjar2JiDG28nabeTrMhxfoKhl2eItqav32dbFC/MsLdgZjSScVehbp3JgGPVib87OfcGgAAAAAAAAAAAAQRyR9fMGyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAO83iSTuOvnpPQGgAAAAAAAAAAAAAQRzc9PPGkbkaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaQRpBGkEaT3ESfvKrSTMvnomwaAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAcdgAAAAAB4PXPQAPD0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAp5sudBtT6afc/m87BzXHpDNgh8VO5jZN5zjZdrmfzOtOxLZZmwbsJXsZ8arXtaUtWeeq8wbhx1j0aAAAOegAAAAAAAAAAAA4oZekzrROiMlV4W3hsFK7lEMzBxudqV3NEBONwAAAAAAAAAADyOhenpMK5gAAAPPaGVPYq2h57jt1+ec3N1O+O95hoCviTWOfuupsSvO0LGdnS/Tqbic9JGqTPtyttV7WdvGroVdXOnudYtVyqW+IGWo5MZvOrXT07t1fa52RvNWkqZ0ls8SpDZAFXNs1YbWdE/nu8w3AAAAAAAAAAAMvRzNaeyhfy2W4OqedNOlo5+zp8d1t5V78FlWLo1b09IasvrZLfHdcPKtWznS5zW82LiPpHXGXbzrZ7x9Tck88oJ0OaEOdNfytTZr+QU9zS6pestMrvL0+alM2oO8dk+rnzts8Zk5f54z9nVVZUy+R5atbulOyDypr505kjbxghq6894a9W+q31lWd5Wvc6ZlyDqDcx9/C+in0VsneoodW/N55GhFbdMG9NPl42j7eTxgbGUrZy9bjeUUsdBXNq5Rypc73Vy+aa/sR1uNIneZ9eeO5xBPabjiVvPnUbZfOJU+eeQKtdS0di3DHoM7FcQAAAAAAAAAAMm3brz29paPiZMvUj3OIrQ6oX+WUrLtWXclkyqFbT7bJ56rhi3Z5p7Z8d2c5rW28s+DS9ztWte9by5xtjzLhoavTc/u6ZkX7MbeM7YiZHm7Pja0Gl4yvDoRsih0PNyhP1Pmx427E3iwbzzZLkedKtbUz86XO++64KlvxlDq15nTO91OFVqWz4yD2STebjts/PaVyrHps8cWK41/LYz62yy/m7OrNnSncK88GL9DXzr5H1aTFh/RVFQXve95/O3rVie3z125K3J0O7GxDVj02VoNFsZnukyq
NiZsZvOoy4aOo2c+LSoZ1ntm8A3AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB4ZWnHPPQK5gAAAAAAAAAAAAAAAAAK9hmwzDA0AAABUs9M0NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAC/9oADAMBAAIAAwAAACHzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzCsoY4444444444444444444444444444444444444444444444ovzzzzzzzzzzzzzziiLczzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzymnzzzzzzzzzzzzzC6dzzzzzzzzzzzzzzzy8bzzzzzzzzzzzz
zzzzzzzzzzzzzzzzzzzymnzzzzzzzzzzzzgOXzzzzzzzzzzzzzzzzwIVPzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzymnzzzzzzzzzzzw0bzzzzzzzzzzzzzzzzyggJHzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzymnzzzzzzzzzzy0LzzzzzzzzzzzzzzzzzyoIJLTzzzzzzzzzzzzzzzzzzzzzzzzzzzzzymnzzzzzzzzzzx1XzzzzzzzzzzzzzzpmFrYIJcgjALzzzzj3Pzzzzzzzzzzzzzzzzzzzymnzzzzzzzzzzi5zzzzzzzfcMzfnPOEoIN+gK/kIK3zzzxUJL7zzzzzzzzzzzzzzzzzzymnzzzzzzzzzygrzzzzzxiMIIICsIDUEUJK5SsIYhzzzzyEIL/PHHnzzzzzzyglNbzzzymnzzzzzzzzzyirzzzziIIIIIICsIBWxmINMONNHzzzzzy83IBYIKjzzzzzzyiBvXzzzymnzzzzzzzzzyirzzzywMIInINOsIKFODGrHEDPHFbxHGdHHHvMIJDOmHPHvGfBBzzzzymnzzzzzzzzzyirzzzwYIJUTzzysIIIIJWEIIPgILHsMIUIIKgIIIIosIIIIJTzzzzzzymnzzzzzzzzzyirzzzwAIIrzzzysIIYsIJwIIPuoJHsIKUIIKwkIJcYICc4gJTzzzzzzymnzzzzzzzzzyirzzzwIIJFTzzysIDXIIL8IIPwQJKgIJwIIL1YIKAoIDzyQJTzzzzzzymnzzzzzzzzzyirzzzzgsIKMswasIBUIIL8IIPy0IIIJ/wIIL3oIKgUJRDAgJTzzzzzzymnzzzzzzzzzyirzzzyyEIIIIICsIBUIIL8IIPywIIIbbwIIL2sIIAwMKIIIJTzzzzzzymnzzzzzzzzzyirzzzzzxYQIIICsIB0oIL8IIPzz8IIPzwIILUMIIgxcMIIIJTzzzzzzymnzzzzzzzzzyirzzzzzzxz4KP8w8
99489688zzy8887zz88954887zw6kW883zzzzzzymnzzzzzzzzzyirzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzymnzzzzzzzzzyirzzzzzyRrz0nTzzzzzDjzDDzzzzzzzzzzzzzzzy0FzjDzzzzzzzzzzymnzzzzzzzzzyirzzzzziNNycN7zzzyzjVwiHTzzzzzzzzzzzzzzxEMlmNTzzzzzzzzzymnzzzzzzzzzyirzzzzzaEDihZfzzzxoNQGIXTzzzzzzzzzzzzzyglI8wxXzzzzjDzzzymnzzzzzzzzzyirzzzzwkOFsOODnXiQBKfsODW/DfzzSQFdvyFjFgMPvoVyR/vg0V/wA88pp8888888888oq88886BRABH2CBBlTDzNiTQSDJTb8rWJBesCHGBGFLDUoiFZIK3c888pp8888888888oq888sKFaYDCREjDSiChfDSvDDXBWhDIADhVD8MDD4LD1IGgKDNMc888pp8888888888oq888rhGArBSDLCh8DYrhTABDR6V3jBEqTEBVBCRnbDCTD7wBxde8888pp8888888888oq888SDDvhGFDhiGpDMhDThDRQBRjDQaDQgJBhBBQBDzIDWgDS488888pp8888888888oq888KNpzAFTEDKRADBeBFCNDLkBoLbJHFikNINBGlJXPPBcBD388888pp8888888888oq8888Mc8MM8sMd88fP8+ucs8csMcvP8dN8sPcsMc8N88tfMfd888888pp8888888888oq888888888888888888888888888888888888888888888888888888sl8888888888o3888888888888888888888888888888888888888888888888888888/8A/PPPPPPPPPKFPfffc/ffffffffffffffffffffffffffc+/ffffffffffffe9PffffblPPPP
PPPPPPKFPfffalvffffZffffffffffffffffffQfYvvffffffffffffaPfffffc3PPPPPPPPPPKFPfffalrWuVd4ava0VfVqn8hkVOl+vTxNvsOcgVPUngVLtavaPfffffaPPPPPPPPPPPKFPfffalvBzlqQfekbOFQN/Qd61faSVvQPaQ/hraHQV69eIaVqPffffc3PPPPPPPPPPPKFPfffalvFP1rQdekfvvQFfQVA1e/alOQfaQ7gvTFAVK1czY14Hfffa5fPPPPPPPPPPPKFPfffYn+Xf0vb4ZTnc/QFfCVCHm1ZXv1abDLQnTPQVIFPBRXn2VvahvPPPPPPPPPPPPKFPffffffffffffffffffffffffffffffffffffffffffffffffeurfPPPPPPPPPPPPPKEcMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMP+oDXPPPPPPPPPPPPPPPLDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDACDDDDDABAAAEPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPM+m1O9to2/52vR+N/PPOPPPPPPPPPPPPOKEcucMtqS+tPPPPPPPPPPOG1PPPPJiQV//OMrYh4qUqyDFP8ATBivDALprTzzzzzzzzzyhkb+gKBPILpycuF9mIwGX4eEpB7xDGMN6a8kHeviQK3KS157I/PKJebRVTzzzzzzzzzyiK8W0mW51BtrOjqoKuY92v8AFLDmvXSf+gixBPMgi4i0mlhPhIEhThTB5P8APPPPPPPPPPPPPPPPPPPPHPPDHPPPPPPPPPPPPCI/PPPPPPDHPPPPLHLDDDHv/PPPKM/PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP
PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP/EAAL/2gAMAwEAAgADAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADG/ecccccccccccccccccccccccccccccccccccccccccccccccfQAAAAAAAAAAAAABFFOsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFvQAAAAAAAAAAAADBG4AAAAAAAAAAAAAAAE2wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFvQAAAAAAAAAAABN2gAAAAAAAAAAAAAAAAJFbwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFvQAAAAAAAAAAAImQAAAAAAAAAAAAAAAAEIQLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFvQAAAAAAAAAAFXQAAAAAAAAAAAAAAAAAEQAEyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFvQAAA
AAAAAAAIqQAAAAAAAAAAAAAB+UoXQAEdZQFQAAABCDQAAAAAAAAAAAAAAAAAAAFvQAAAAAAAAABKYAAAAAADzftTywx3LQAlygCCQAAwAAAJYA4AAAAAAAAAAAAAAAAAAAFvQAAAAAAAAAFDQAAAABa0wAAAqgAqvxgEhAQQAAIAAAAKgAAyDTRAAAAAAAFLUogAAAFvQAAAAAAAAAFHQAAABGwAAAAA6gAokJLXL7LL3QAAAAAA4nvPwAFQAAAAAAFI3iAAAAFvQAAAAAAAAAFPQAAABwQAAvDrKgACsQgE880eM8gwMc4ms820QAEcWuo4Ec4L7YAAAAFvQAAAAAAAAAFPQAAAMwAEOAAAKgAAAAFCgAApwADAQQCaAAKQAAAPiQAAAAAwAAAAAAFvQAAAAAAAAAFPQAAAAgAFAAAAKgADzAANAAAhowBkgBMKAAKywAHrgBCcogAwAAAAAAFvQAAAAAAAAAFPQAAALgAIqAAAKgAiywAPgAAgDQFoQEgKAAKawANawBAAOQAwAAAAAAFvQAAAAAAAAAFPQAAAAiQENs/e6gAqqwAPgAAgFSAAAKgKAAKRQAPSQArDAgAwAAAAAAFvQAAAAAAAAAFPQAAAEIAAAAAA6gAqqwAPgAAgEAgAAAgKAAKUAAHZSQAkQAAwAAAAAAFvQAAAAAAAAAFPQAAAAELzAAAAqgAopwAOgAAgAJgAEQALAAKaAANMKwAAAAAwAAAAAAFvQAAAAAAAAAFPQAAAAAAIQj/8YscQ8McYEcYwAE8ccQAEMcYocccgAEVGs8YQAAAAAAFvQAAAAAAAAAFPQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFvQAAAAAAAAAFPQAAAAAF7AB+AAAAAACDADBgAAAAAAAAAAAAAAAE8oBDAAAAAAAAAAAFvQAAAAAAAAAFPQAAAABHv4FPAwAAAFFb4L3awAAAAAAAAAAAAAAK/
P/zYQAAAAAAAAAFvQAAAAAAAAAFPQAAAACPpxt7qQAAAPfNu/OagAAAAAAAAAAAAAFFLEY/AgAAABDAAAAFvQAAAAAAAAAFPQAAAABv34vNlTSj3v+OTfNC1vwgAFFO4JgG7frvJQkOIFfixBC4gAAFvQAAAAAAAAAFPQAAAHvrn/v2PvfnovLc1PrP/ADk6sRfqf4RSfjz6hvnyBFz7VdkKkAABb0AAAAAAAAABT0AABDz7zDyi+17b1zj/AC8GG8vv+4d8z/8AFM/A9fDgHrSXurvv00IAAAFvQAAAAAAAAAFPQAABPK/Pvl/HenpPLk/b33a79biPft1OafbX/Knd/HsfBX/PKgAAAAFvQAAAAAAAAAFPQAAP/PknuuvD/PJfAPfL3PPXPrPvKJPLf/rTvvrfvLc/PXvPkAAAAAFvQAAAAAAAAAFPQAAMv3Hf97cfNnefOB9uoHfKcOh988M/b8urHfvSfeUdutdZqgAAAAFvQAAAAAAAAAFPQAAAMoAIEAEEwwEM0gEMIEMYEMYMkAI0wEAYEM4AI4AAwsMgwAAAAAFvQAAAAAAAAAFPQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF8QAAAAAAAAAFEcssssssssssssssssssssssssssssssssssssssssssssssssssssssKwAAAAAAAAAFAAAAADCwAAAAAAAAAAAAAAAAAAAAAAAAACRAAAAAAAAAAAAADAwAAAAEaAAAAAAAAAAFAAAAAPwgAAAAAQAAAAAAAAAAAAAAAAAZAF2gAAAAAAAAAAAAFwQAAAAEIAAAAAAAAAAFAAAAAPwtENSHfuhPbKQBxCB+DY0sny8ar16hDCCAJ4DSxkHy1wQAAABHAAAAAAAAAAAFAAAAAPwlqAq1FgFqTdyFyYFUfYF2FSwfwAKA/gP41WVSFkfI1wQAAADIAAAAAAAAAAAFAAAAAPwlqla0HiEvc
8wFwwFQ1AJafqQfgAKB/AMylQ/KDeVwl5QAAHmgAAAAAAAAAAAFAAAAAMC3bVIgOeeWtYQFAwVAlyxpdYkOVRYwUyIwHC/6RfVSo+aQCOQAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADsQAAAAAAAAAAAAAFCgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwzsoAAAAAAAAAAAAAAAEMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMPOcMMMMONffPLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADVELCwDlyK/lb2BAgAABCQAAAAAAAAAABGrhBjBAkNRiAAAAAAAAAABF6AAAAHcvcqwBGC3NtKDdTO9L5TOXDDPypiAAAAAAAAAAFHr3NqFV8+eV0xEMWr7La9bX8IJDboVs3Hg5YLr1Mi1AXWvuEPO5oC6Cl6AAAAAAAAAAFFJzlMzDNfPLXQraqHCVTmj3XvOtGTZT9uX/Pu7vL3LjjDuekv7Xr/AA2zIAAAAAAAAAAAAAAAAAAAACAADCAAAAAAAAAAAADB4AAAAAADCAAAABCBDDDDKMAAABS0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAA6EQACAQIDBQcDAwQBAwUAAAABAgMAEQQSMRATIVJxFCAyM0FRkTBTYSJygUBCUKEjgIKxQ0RgYpL/2gAIAQIBAT8A/wChxY3bRTW5t4nUVki9Zf8AVZYfuH/81lh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4fissP3D8Vlh+4figsP3D8U65HK+39AsbMLngvuazxp4FzH3NNI7asf8iuoqbzX6/XCrGAz8T6LTuznj/QkgAk1FKJL+hH+BXUVN5r9frKBEAzeI+EUSWNybn+ixL2AQfzSOUYMKBBAI/wC6ipvNfr9WNRxdvCv+zTMXYk/0cjZ3Y7MM10t7H/ALqKm81+v1LXqU2tGNF16/wBG5sjH8HbhT+th+P8AALqKm81+v1IRZi50UXom5v8AXY5VJo4o+iChiuZfildXF1NOLow/B24bzD+3Y2JQcFF67U3IKimEhtlIPeaWNTYsKVlbipB/pV1FTea/X6mkB/8As3+h9AkDU0Z4h63rtKexrtKexpZo3NgeNNIieI2rfRc1b6LmrgRUseRz7HTYrFTcGopRIPz6ipFyOw2YcBUZzUspc2Hh2wR5EudT3cRIUUAanYrFDcGkbOob3/pF1FTea/X6knCOIfgnvyYj0T5oszG5N+5F5idaxXiXptj8C9BToJFsaeNozx+disUIIqVRKgdd
RSIXYAVNIOEa6DbDAeDOOg72KU/pbbCCsag0zKupArex84rex84oEEXH0WkLXIbKgNr6kmgzLcq5a2oIoEMARoe6uoqbzX6/Ul8MX7O9PLclFPD170XmJ1rFeJem1PAvQbCAdabDxn0tXZV5jUcW7vZiaCKt7C167KvOaGGT1JpYo00XvkAixpsNytSYcKbsb7MV4F67YvLTp9EA7sp/cHo3jMhbjdeBqIFY1B7q6ipvNfr9SXwxfs7sjZEY9yOBn4ngKGHiHpetzFyChBGGDC/CsV4l6be0OFAUAWFb6TnNLiJF141HIsg4UxspPsK7RJ+Piu0Sfj4qCVpCwa1TSyI1ha1dok/HxXaZPxSnMAfcbJMQ4chbWFdok/HxSTyM6rw4mp5GQLlrtEn4+K7RJ+Pis53Of1y3p5XkFjbas7qoAtYUJ5WIAsT0rNlS7kU+J5B/Jrfy81DESD1Bo4lyeAArtEn4+Khmd2sbaU0asb6H3FCJQbklj+T3l1FTea/X6kvhi/Z3cUf0qPc7YEDvx0HdxXiXp3kcowIpjeNj7qduF8TdKxC5kv7bcM10t7GnbKrH2G3DJclqxWiddv8A7b/s7sCBEzt6j/VSSGQ/j0Hew3mfx9BdRU3mv1+pL4Yv2d3Ff2bcLo/Xu4rxL02pC7i+gqSJoyL8QdqG8H/aduF8TdKIuCKZcrFfbZh2tJb3rEtZAPc7YlyIorFaL12qC0AA1K08bpxYbUhdsptwNYlsqBR67EUuwUUsEa6i5poY2/tt0p0MbEHZhvM/j6C6ipvNfr9SXwxfs7uKH6VPsduHcK9jo3dxXiXptj8C9BWJ8A67YvIPQ7cL4m6bMSliG99gOUg+xqd878NANkK55B7DjsxWi9dsXlJ0rFeFeu2Ly06VifGvTZhRxY7cUOKbMN5n8fQXUVN5r9fqS+GL9ndkXOhXuR4gjg/H80Joj/dW+i5xQnjZgoJuaxXiXptTwL0FYnwDrti8g9Dtwvib
pslTOjDu4ZbKW99mK0Xrti8pOlYrwr12xeWnSsUOKnZh3CvY6HbO4d7DQbMN5n8fQXUVN5r9fqS+GL9neni451HXvReYnWsV4l6bU8C9BWJ8A67YvIPQ7cL4m6bZkySH2PEbQCSAPWgAoAHpsxWi9dsXlJ0rFeFeu2Ly06VKmdCPXUbVmkXgGpppGFi23DeZ/H0F1FTea/X6kvhi/Z35MODxTgfamRl4MCO5F5idaxXiXptTwL0FYnwDrti8g9DtwvibptxKXUN7bcMt3Le23FaJ12xeUnSsV4V67YvLTpsmgzfqXWiCDYju4bzP4+guoqbzX6/Ul8MX7Poa0YIj/bRwyehNdmT3NLAikEXuKeNH8QrcRctbiLloAAACmRXFmFbiLlrcRctKiquUDhW4i5a3EXLSRonhG0gEEHStxFy1uIuWlRUFlFtrorgBhW4i5a3EXLQAUADQUyK4swrcRctbiLloAKABoNrIr+IA0cNGfcUMPGPQmjDGT4a3EXLSxIhuo+guoqbzX6/Ul8MX7P8ALrqKm81+v1JfDF+zvxwyS+AVJh5Yxdl4e42AFiABc0cLOBfJWn0iCNR/QgEkAC5NHCTqt7A/gfXXUVN5r9fqS+GL9nfwyZIUHuL09sjZtMpvswK3d2toLbMaF33DXKL96KJYkAA4+tYiBZEY2/UBwOzBRKQ0hFzewplVxZgCKmj3UjL/AEGCS7l/RdmNCCRSNSONYKJHLswBtbhT4WFx4bH3FSxtE5U/TXUVN5r9fqS+GL9negj3sqr6anZjZv8A0l/7qAJIA1NQxiKNV+akcRoWOgpmLsWOpPdwkWeXMdF2YidYlI1YjgKALEAamo0EaKo9BTMFBJNgKnk3srMNP6CCPdRKvrqakcRozn0p3LsWOpqKVoWzL/IpMXCw4tlPsaxUqyyDLoBa/wBNdRU3mv1+pL4Yv2d7BR5Y851b/wAVI4RGb2FEliSdTWChud4dBpsx0lyIwdOJqOJ5
TZR1NJgUHjYk02BiPhJFSxNC2Vv4NAEkAamoYhFGF9fWpXEaMx9KZi7Fm1NYKK7GQ6DgNmMmztkHhWgCSABc1HgWIu7W/ApsByP806NGxVhY7YMIZVzMSAdKnwhiUsrXAoAkgAXJpcC5W5cA+1SRtE2VtgBYgAXJqPA8LyN/ApsFERwLA1Dg2SUFiCo4jZjZbsIxoNaRDI6oNTXYFt5hvUkTRPlbZHE8psooYBrcZBfpUuHkh4niPcbEVFRWYAlzZQdOppwBwkRCubLmUWINOhR2U+h7i6ipvNfr9SXwxfs7seDkYgtwWgAAAKIBBBFxW5i+2nxQAHAU7hEZjoBSI+IlP5NyaRFjUKo4UzKguxAFAgi4rHKDEG9mrBRXYyH04DZjZczCMaDXrSqWIUamo0EaKo9KxEu6jY+ugrWsNhxEuZvGf9bM6ZsuYX9qmgWZQDwI0NTYd4LXNwfXYgyoo9gKkXOjL7ioMMsPHVvfZj7Wj99mGg3S5mH6zsEsZbKHW/tfZK4jRmPoKJLEk6msCl3ZvYbMa4aQKP7RUUbSuFFRxrGoVRsIV1IPEGpMDqUf+DSjOiL/AHxseGlxUgaW28Uooa9yalfeSO3oT3F1FTea/X6kvhi/Z3YmzRofdRRIAJNPjlBsiXHuaw87T5iVAA2Y6SwWMevE1hohFGOY8TU0ohQsf4FSSPIxZjWHBWCMH2rGcYgo1ZgKjQRoqD0FSOI0Zj6CiSxJOpNYKK5Mh9OA2YyTPJlGi1g4LneNoNNmKxJT/jQ8fU1h1Lzp1udmOkFhGNb3NYaPeSj2HE7JZkhF2PQVFJvUDgW2YmTeSm2i8BWDhztvG0GnXZjJyDu1P7qhBMsYHMNmNluwjHpxOzDxbqMA6niaxE26jJHiPAVqaw0O6Tj4jrTusalm0FSzPKbk8PQVgwRAL+pNqdxGpY6CmOZi3ub95dRU3mv1+pL4Yv2dxELuqj1NKAoAGgFYx8sNuY22YePdwqPXU7PP
xf4zf6GzHMTIq+gFYeIyyAeg12N/yYlF9EFz1OzHSeFB1NQQNMfZRqaVVRQqiwFSNkRm9hUaNNIB7m5NKoVQAOAp2yIzewJoksSTqTWEh3a5mH6momwJ9hRzSP7sxrDw7lLHxHWiQASalkM0hb4FRJu40X2FYmTdxEjU8BUMTSuFH8mlUIoVRwFOwVWY+gpmLEsdSawUJF5GHSibCmYuxY6k1hsLls76+g9tmNa82X0UVhIMxEjDgNNmOcl1T0AvUMRlcKNPU0AFAA0ArHScVjHU1h8OZmueCDWsQEWVggsB3V1FTea/X6kvhi/Z3MELzdFJ2Y5v1ovsL1GuaRB7sNkhyo59lNYQjfr/ADsmwyzEEkgio4kiXKoqRxGhY+lYMl9651ZtkuFWWQOzHTSgFQBQAB6bHQSIyn1FQwJCCF1Op2ModSp0ItUWEijN/EfzsIzAj3FRQRxeEcfc7MY+WEjmNqw65p0B99k0KzBQxIsaVI4VNgABrQIIBGhp1DqynQi1Jgo1N2JbakEUZuq8drYaJ5M7Ak00scZVCQCdBsmwomcNmtw41FEkS2UbJMKskmdmPSp5Vw8dl10Ud5dRU3mv1+pL4Yv2dzDyCKUMdNDQIIuCLVinDzMQbgcKVirKw1BvUUySqCp6isTIqQsL8SLCkYoysNQaimSVQVPUbHkSMXZgKnnMzeyjQVgXAzITxJuNjMFBJNhUuJLyqR4Fa4oEEAjTZicWACkZ4+prDYkMAjn9Q0PvsnxCwj3b0FYbEb5bNbONk+MIbLHaw1NI6yKGXQ1jz+hB+aifdyK/saR1cXUgimZVF2IArE4ky/pXgn/msJiFy7tza2h2TYpIgQCC1YXEmQlHP6vTZPilj/SnFv8AQqLEOkudmJvrSsrgFTcVPiEhHu3oKZmdizHiaw8oljXjxAsaJCgkmwp8cd4Mo/QDx/NKwYBgbg1NOkI48W9BTu0jFmPHvLqKm81+v1JfDF+zu3PcJJ2AkaGt
/N9xqLMxuSTs0pcTOotnp5ZJPGxOxJ5YxZW4e1PiJpBYvw9htEsoFhI1utEk60CRxFGWVhYuxGxZHTwsRTO7m7MTsBI0JFFmbUk7c76ZjbrtM0xFjI1toZhoxG0Mym6kg00kj+JydiySILK5FEkm5Nz311FTea/X6kvhi/Z/l18Q61N5r9fqCESJGSbWWuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGuyrzGhhlBBzHhTm7sfyfqReWnTuhWNEEGx/q1UtTKV+sFLUVK952yox9h9WLy06d1PCKfxd5F9T/RAXNOoVRsVwtO2b60ehqTUd7EvZQvv9WLy06d1PCKfxVGAb3ooL1lHsKdQOIpVudhVbHgNioBrVl/FMluI2ZVtpSoBTizGgosOFBANaKqfSmFjakAJ41ZR7UUU0RY2pFsKIB1pwBa1BVsOApwABYbAgGtWX8Uyeo2BRYcKCAa0VU+lMLG1IATxqy/iiimiLG1R6GpNR3pX3jk+mg+rF5adO6nhFP4qj9ac2FITmp/Ce4gu1MCRYVu27jMSdi6CnN2NRm61JqKAJPChGfelFhapPFUepqTQbI9TUmgqMXanNlrStRR1NL4R0pzdjUZuKk1FAE6Vuz70osLXqTWo9DUmo7uIlyjINTr9aLy06d1PCKfxVH61J6UniFP4T3E8VMSBwreNW8badTsXQU3iNR6HrUmoqPSnYg2pCSKk1HSo9TUmg2R6mpNBUfip/DsGgo6ml8I6U3iNR6GpNRUelOxB
tUZJFSa1Hoak9O5LKIx+TpRJJJOp+tF5adO6hGUU/iqMjjUh0pPEKcjKdiuCONHL+NiuDVl/FNlt6bAR70dikWHGm1NRkWNSaika3A1+k+1fpFOQTwpTlNXU09rC1R6mpCLChwoODVlHtTP6DYpFhxptTUZFjUmopGtwNfpPtQyinIJpGsaupprZjbZLMsYtq3tTMWJJP14vLTp/kmZVFybVJib8E+a1/oI5YxGoLDSt7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7Fzit7FzijPEP76OKQaAmmxLnSwoksbk3/8AnLMEFzQLznXKlBFSQWygAfydgIOhrOvMKuOHEcaklEf5b0FZXNi/EnRfSkiXORqAOPtc00FuMbEGhLIoym2b/wAdaBDWsjM1wcxpQQOJudlx/RNIqEA3JPoKEnBiVYWHqKzDJm9LXoyKFVuPHYrBxcUCCSAeI2KwdQw9aJA4kgfVDlpCoAyjU95my9ToKUm5ub1JII1vqfQU8mRAbfqOgoXsL67XYzShRpUjbpVRNTwFZGQZeDFvWkJnJJ4IPT3o3zGNDqbsfaiqs4RQAq6mpCiqHIuR4ahS5Mr0QJLFX09qBSPhmA61LJu1v6nSkBUe7txP4pWRRbOD/Ox2Nwq6nU+wpVA9NpNrcDxrNnPDwj/ZoCw+rFxLudS1qnNo2/NOZFjIKgC1taYcYUpzZWPsKiFolH4qIH9blza5/m1MXaMuWIFuAFILIo9hRcPJkyggak0JBnC+4q9CXMTlH6RqTUchdSxWwvwp2yKWtRmtGHtrTS2Ki2pppLNlAu1b
z9BY1vv+LPammsFNtalkyjKviOlRfo/SRwte9b3NxUcL2608gTj6DWgwYA+9PKEIUC7H0oPa4a3CkzSMX0GgrgF9gKT/AJXMh8K8BQYPIXPhXSllBQueAvSyElf02Bp2/wCNiPasKBdjUsbs6svpRRirXN2IteokkVcpIApIpVLEECooXAIfS97e9Yg5nRKkjZsqjgg1p3EeZlHplFFGQags/A3oBppLHgFFcJGKL4B4j70uV5L6ImlO4RSTQutvWRv9Vf8AUWzHKupvqaJc+pDPoPYUDZrlzlQcT7mizkakM5sB7CmOUBF8R4dKW7MACcqevufqhHVmKMLE3saZGcIGI4Nc1IhcKPZrmihMgf0AqRS6FR60gkFgStqWNgjISLEGjE7IFLDhbYsRVWF+J9a3TFwzNoLUy5lK6XFGImLICBSqFAqSMyZRey+tNEWdb2yLoK3f/IXv0/FCLK7twN/enjLCwPU00WbIvAIPSjFeQNcWA4CjH+tW9hRjBVh70qFQBwFhUkWcp7D0oC1bs7wuCOIp1N1RSSzamgtgB6D0pgWUgHUVuyEygi9q3P8AxZAePqaeHMgX2rL0A9hRANLFJGxKEEexobw8o/3WQnV2/jhTQI3qb9aXDsD47dKUMBYm9SxFyGU8RQEjcGIHSpYmYLlsLUiEcWN2pIHBa7cDSwSBGGa1/So4mUDORYelKN6+f+1dKaMM2a5va1CFQFFyQDegguxubmtyuQLc600YJU3IK1IgzfpJzNw6ClUIoUen+IRCCzN4j/SEBgQaAAAA7wUBi3qf+qD/xABIEQACAQIBBgoIBAUDAwMFAAABAgMABBEFEBIhMXEGExQVIjJBUVORIDAzNFRhc5JScoGxNUBCUKIWI2NiocE2gNFDgoOE8v/aAAgBAwEBPwD/ANjkk8UfXcCuVhvZwyP+mArjro7LbzcVxl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38
Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov31xl38Ov30ZboAk26/dUMhliRyMMR/IS3CRnRALv8AhFcVcTe1fQX8Kf8Ak0lvDH1UGPf/AHF+o241Z+7RbvXtLJOxSE4KNr//ABUUMcIwUbz2n+RjjeV0jQYsxAArKWSpcnCJi2mjjb3N/YX6jbjVn7tFu9c7NcuYkOEa9dh2/IUqqihVGAH8lwcstJ3u3Gpeim/tNXlsl5bSQt/UNR7j2GpI2ikeNxgysQR8x/YH6jbjVn7tFu9bcSN0YY+u/wD2HfUcaxIqLsH8nYW4tbOCLtVBjvOs5uEVsIrxZVGqVf8AJf7A/Ubcas/dot3rCQoJOwVbAuXnba51fJf5OzQSXduh2NKgPnn4SoDaQv2rLh5j+wP1G3GrP3aLd6y7JKLENsjBf0pQFAA2Aevgi46aOLTC6bBcTsGNJwYj/rumO5cKm4MkAmG5xPc61dWdxZvoTxle49h3GrJxHeWzHYJU/fPwkYCxRe1phSqzsFUEknAAVa8HbqUBpnWId3Wav9MQ4e9P9orKWR3ydGsnHq6FtHZgfShyZfzqHjtnK9+z96ntp7ZtGaJkPzH8q/Ubcas/dot3rD07xe6OMn9W9RHHJKwWNGdu5RjUWQ8pSgHiQn5mAocGrztlhHnTcG70bJYT+pq5yPf2qNI8YKLtKkGrexursM0ERcKcDXM+UvhW8xXM+UvhW8xR
DIxBBVlOBHaCKyVfC+tVYn/dQBXGae3huYmilQMprKeTZcny9pibqNVhci7tIZu0r0t425uEMzT3cFrGCxQbB2s9ZKyTHYoJJAGnI1n8PyGfLl8Lu64tDjHFqHzPafRyBYpdTvLKoKRYYA9rHNcW8V1E0Uq6Smru3a0uZYGOOg23vH8o/Ubcas/dot3rINdxct81HkPSAJIAGJNZP4PFwJbwkDsjH/moYIbdNCKNUXuA9DKv8Ou/pmuDPsLn6gz33vt19Z/3qyvZrGcSx7mXsYVZZQtr5A0TdLtQ7RmuraK7geGUYqw8j3ismTPkq+ksbg4I7dFuzHsO41e3cdlbvM/ZsHeayNYuS1/c65pSSvyBz5Yy0qq1tavix1O47PkPS4MzoGuICek2DLnyxMs2UbhkOIBC/aMKht57hisMTOQMSFFc15Q+El+2ua8ofCS/bTo8bMjqVZTgQfUwWMcIiR4OPupE0xGW0VRe80bWGYxxz2qW7S6opon0kLdx1mpYnhleJxgyMQfRfqNuNWfu0W71lt7S5+qfSyFksRot3MuLtrjB7B3+llX+HXf0zXBn2Fz9QZ77326+s/75ld0YMjFWGwg4GocvZRhABdZB/wBYocJrjtt4/Miso5SOUeLLwIjJsYGpLy4m4kSuZFi6oahwmnA92j8zT8Jbs9SGJfM1c5TvroESznRP9K6h6ccjxOrxsVZTiCKt+EpCAXEGk34kNXvCGWZDHbx8WDtYnFs3Bn3m4+lnyv8AxK6/P6l5ETKKXrEcRNbHRJ2YhcNGk0b+LJ6W4WMpMdOJTqXt06ynMk9/cyJ1S+o7vRfqNuNWfu0W71lt7S5+qfRydbcrvIYj1ScW3CgAAAM+UMvQWrGOFeNkH2ipcu5SkOqUIO5VFDLGUgfem8gaky3fTQSQyFGV1wJ0cDXBn2Fz9QZ+YLV5pZpndy7ltEahrpckZNUYC1Srjg/YTA8WGibsKnEeRq+yfcWEgWUYqeq42GoE
Ek8KNsaRQdxNf6dyf/yfdX+ncn/8n3VlnJdtYRxPCX6TEEE1kjJlhf2xd9PjFYhsGr/TuT/+T7qPB3J5B9r91TxNBNLE21GK+WawyDazWkMk+npuukcDX+ncn/8AJ91XmRMm21rNMTINBCR0qyLYQX8k6zaWCqCMDX+ncn/8n3V/p3J//J91cjiGVeSYsY+PCfPCrLJdrYOzw6eLLgcTjnnyJZXMzzPp6TnE4NUuQslwxtJIzqijEktRhFzdGOzicqT0QdZq14NLgGupST+BKXImTFHu4O9jU2QMnSDoo0Z71aouDlmqASO7t3g4V/p3J/8AyfdWWMk2tlarLDp6WmF1mra/uLZTGpVojtjcaSmpcp3DxtHGkUKN1hEujj6T9Rtxqz92i3estvaXP1T6PBmPG4uJPwxgeZz5cvWtLTRQ4SSnRB7h2n0eDPsLn6g9K9tY7y3khftGo9x7DVujR3sKMMGWdQd4bPwm92t/qGsgXXEXvFE9GYYfqM/CG34q9EoGqVQf1FWsBubmGEf1uBSqFUKNgGAzcJbrRjhtlPWOm24VwY9tdfkXO3/qH/8AaHo5bvJLy6Wyh1qrAED+p6ybk6KwhCgAyHrt6XCP3Bfqr6h+o241Z+7RbvWW3tLn6p9Hgxsu/wD7M/CfHjbXu0G9Hgz7C5+oM99lm1sX4shnk7VXsrJ+U4Moq+gCrptU57+MR5c3zRt54Z+E3u1v9Q0jtG6upwZSCN4q1nW5t4pl2OoObhDb8dY8YB0omDfodRrg5b8ZdPMRqiX/ALtnylc8rvZpQejpYLuFcGPbXX5FzzypBlx5X6qXGJq1yjZ3rMkEukVGJ6JH757vLFlbcdHxv+8gOC6J21wdg467luH18WP8mzXdzFZwPNIeivmTVzlu/nYlZTEvYqVBlnKMDA8eXHar66sbyO+t0mTV2MO45uEfuC/VX1D9Rtxqz92i3estvaXP1T6PBqULczx/jjB8jny/ZtcWiyoMXhJO
9Tt9Hgz7C5+oM9+Sb26J8Z/3rg179L9E/uM+Vf45F+eHPwm92t/qHNwbutKKW2Y60Oku45polmikibY6lT+tZFtGtLPBxg7Oxb9s2WLrkthKwODv0F3nNwY9tdfkXPlT+I3f1DXBn3m4+lnyv/Err89cGVAtJ27TN+wGbhO5EVsnYzs3ln4MMdG6TsBU5uEfuC/VX1D9Rtxqz92i3estvaXP1T6Nhc8ku4ZuxW6W40CGAYHEEYjPf8HkmZpLVlRjtQ9WpMjZSjOBtmP5SDS5Jyk2y1f9dVS5Fv4IHmkRQqjEjSBNcGfYXP1Bnvvfbr6z/vXBv36T6J/cZ8q/xyL88OfhN7tb/UObJl1yS9hlJ6OOi35T6PCO54y5SBTqiGJ3tm4Me2uvyLnyp/Ebv6hrgz7zcfSz5W/iV3+euDEoMVzF2hg3nmy9Zvc2YdBi8TaX6dufINm9raF5Bg8p0sO4ZuEfuC/VX1D9Rtxqz92i3estvaXP1T6WQsqKVWznbAj2bH9vSyr/AA67+ma4M+wufqDPfe+3X1n/AHrg379J9E/uM+Vf45F+eHPwm92t/qHPke65VYRMT006Db1zyyLDE8j9VFLH9KmlaeaSV+s7Fj+ubgx7a6/IufKn8Ru/qGuDPvFx9IZ8rfxK7/PWSrzkV4kjHoN0X3GgQwBBxBzT5HyfcMXaHRY7Sh0at8kWFswdIQWGwsdLPwj9wX6q+ofqNuNWfu0W71lt7S5+qfTyfwgeELFdAug2OOsKt7q2ul0oZVfdtHoZV/h139M1wZ9hc/UGe+99uvrP+9cG/fpPon9xnyr/AByL88OfhN7tb/UOfg5dcVcvATqlGI3rn4RXXFWqwA9KVtf5Vz8GPbXX5Fz5U/iN39Q1wZ94ufpjPlb+JXf1M2SMtcmC29ySYtiv2rSOkih0YMp2EHEH0eEfuC/VX1D9Rtxqz92i3estvaXP1T6hWZGDKxVhsIOBqHLWUodQuCw/6wGpeEt6OtFC
f0Io8JbzshiHnVxlu+uY3jZkCMMCFWrW/u7MMIJdENtGANc+5T8f/Fa59yn4/wDitSO0ru7nFmYkn5mra6ntJOMgfRbDCufcp+P/AIrXPuU/H/xWprqeecTyPjIMOlgBsrnzKfj/AOK1z7lPx/8AFausoXd4qrPJpBTiNQGeOR4nWRGKspxBrn3Kfj/4rXPuU/H/AMVq5u7i8cPO+kwGA1AZ7W8ubNmaCTRLDA6ga59yn4/+K1z7lPx/8VqWV55HlkOLscSatby4s3Z4H0SwwOoGufcp+P8A4rXPuU/H/wAVqaWSeV5ZDi7HEnPb3lzanGCZk+QOo7xScIr9RgRE29aly/lGQYBkT8q1HljKUS6K3BO8BjXPuU/H/wAVq5yle3cfFzS6S4g4YAeofqNuNWfu0W71lt7S5+qf7u/Ubcas/dot3rLb2lz9U+m8iJ1jSSo+w5iQBiaE8ROGl6vEH+RJAGJoTxk4Y+vfqNuNWfu0W71lt7S5+qfTmbSkalx0hhtxzXLYKFzW+PF/r6TuXYkmopCjDXqOa5c4hQaDFTiDUbaaBv5C5bBQvfmtixU47Oyrh2UKAcMaWaRe3HfSOHUEerfqNuNWfu0W71lt7S5+qfSlfQQnNbx/1n9KJwFSPpsTSKWYAUoCgAejO+imHac0URcg9gonAE07F2JoAkgCo00EC/yEr6bk0il2CilUKABUiCRcDTQSA7MagQouvtPq36jbjVn7tFu9Zbe0ufqn0rh9JtEbBSrpMB30AAABVxJgNAfrmtkwBendUGJprlj1QBQuX7QDUbiRcRRIAJNSOXYmkUuwApQFAAq5fABR25oI9FdI7TRIFNcgdUY0t1+JaVgwxBzyz6B0QMTUc4c6JGBokAYmjcqDqXEUjhxiMxIAxNPc9iChcv2gGnuAyYAEE5rZMAXPbTMEUsa5UceqMKRw4xGZ3VBiTXKh2LUcySbNRzMWLMAcAoxNKSdas2OGOB7aVtJQ3f6D9Rtx
qz92i3estvaXP1T6L3CAEDWa20CRXGSfjbzzKpZgB20zLEm7ZTMXJJoAnYMa2VbHByO8VcvgAgzW6YLpHaaJABJp2LsSaiTTcDs7c00pc4Dq5tFsMcDhUcjRnVUcqyfI5mOLE/OkbRYHuNSzGT5DNa44tmml0zgNgzFHAxKnDMil2C0AAABVy2Che85rdSEJPbTuEUk0zF2JOYEqQRtFLc/iFE6LMf6XG3upSE6jaRI2CkXRRR6D9Rtxqz92i3estvaXP1T6LjB2HzrbS2xI6TYVLGI8NeJOa2TWWqaTTf5DZUaGRsKVFQYAVKQZG31b9cnuFOxdie+kUuwFAAAAVcvsQbzmt00Ux7TVxJh0B+uaCEN0m2VKQsbbs1shxLdlTPoIe85kjZzgKdNBiuOaFNBB3mriTAaI2nNbxf1n9KkICNuzWyYAuc0r6bk9lRR8Y3yG2tlTSabatgpFLsAKSNUGrbVwQZNXdSqWYAUBgAPSfqNuNWfu0W71lt7S5+qfQZtFSe6icSTVuuMmPcM0rabk5vZwfPD981sOiT86lfi1J7ezMOjCx7WOGa2Xa1SyiMfOmYsSSaUaTAd5p2EaY0SWJJpRpMB3mgAAAKnk0mwGwUBiQKGCL3ACpZOMb5DZQGNRoI0A86dtJyahTTcU7hFxNMSxJNKMSB3mgAAAKuJP6B+tAYmgAqgdgFTTY9Fdnac1uuCY95qeXAaA29ua2XBS1SOEUmiSSSatk1F6llCDAdaoixQFtvov1G3GrP3aLd6y29pc/VPoXBwj3nNajosfnTnRRj8syDFlHzqf2RzRzGMEYYindnOJpFLsAKuAF0FGwDMkxRNECiSxJOZWKsGHZUkjSHXmUlSCOynndxhsGYHAg08rvtOrNbrpSD5VKcI2zRyGMkimZpG16zRGFKdFge401yxGAGGdpXfUTnWZ1XRFBHcFgM0c/Fro4Y07s5xOZZ2RNECo0aV8Ts7fSfqNuNWfu0W71lt7S5+q
fQlQuhA20QRtFQKVjGNMNIEU8bIcCKhUs47hTAMCD208bIcDmVWc4KKiiEY+dXKnU2YAk4AUkIVGB2kUQQSDmhgx6TjV3VNCQdJRqzRxNJuqaLQOI6uaO3xGL0ylSQates26nXTQrTKynAigCxwAqGHQ1nrVPEcdNRvzRws+s6hU0IXBlGrNFAX1tqFPErJogYd1EFTgRUcTOflSqFAAqVCjnu7KAJOApbbo6z0qIKkg1HE0h+XfSqEAA9J+o241Z+7RbvWW3tLn6p9WQDXFx/gFAAbBnMMZ/ppUVdgAzNEjnEilijXYucohOJUZiMaEaA4hRmKq20A0qquwYZiAdooADYM+A7hn4tAcdEZyAdozkA6iKCIuxQMxRG1lQaAA9N+o241Z+7RbvWW3tLn6p/u79R9xq092i/L6w3bW806hQcZCa5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8Na5yfw1rnJ/DWucn8NafKDurLoDWCKhXRhjHco9Zc+8S/nPorC7DEDVTKUOB/m44jICQQKkiMeGJBx9ckTSAkEU8bRkA+lEmnIi97AetufeJfzn0YfZLU/tDuHpQRf1MN38kqliAKlQIiAZopRGCCDUsokwwB1eutuq2+rnau70snR6Upc7FH/c+tufeJfzn0YfZLU/tDuFW6q2liAaMCFwcABhsrQT8IqaIKNJaiQO2vYMzJHot0RszJAqgFtZrCM6sFqWEAaS+Wbi0K9UbKSBF
GsAmpgBIQBhSRoUHRGykhVdoxNGNGGtRUiaDEVAqsxDDsrCMdi00SMNmG6mUoxBqGMKMe00yq3WAqdVUrogCljjIHRGyp0VVBCga6AJIApIFUaxia0YzqwWpYQAWXyzIiFF6I2CkhRdoxNGNDtUVImgxFQKrMQw7KwiHYtNFGw2YfMUylGINW3VbfVztXd6VtDxMKr27T62594l/OfRh9ktT+0O4Vbf1VcMVUAdtQMRIBjqNTeyahngGMg+VSBmQhTrNC3kBxxFdmujqJFDYKlkZmOvUMydRd1TOS5GOoVASU19hq5667qVWY4LQtj2sKjQouBONXHX/AEq22tuq56q781t1m3Vc9Rd9W4xfcKnYrHvoHAgihrA+YphgzD51H1E/KKmclyMdQqBiU3GrnrLupVZjgooWx7WFRoUXRxxq4643VbdVt9XO1d3o2FvptxrDors3+uufeJfzn0YfZLU/tDuFW39VXOxKh9qtTeyfdnOokVbnCTeKkLBSV21yh/lXKH+WYbBTdZt+ZOou6pPaPvq36h31c9Zd1W4ATHvNTSOHwBwFQMWTEnHXVx1xuq22tuq5HRXfmtus26rnqLvq3ODkd4qcYx7jW2lGAA7hTnFmPzqPqJ+UVJ7Rt9W3UbfVz1l3VbgBMe0mppHVtEHAVAzMhJOOurjrjdVt1W31c7VPoW1u074bFG00qqihVGAHrrn3iX859GFl4tdYqcgyGrcgaWJq5IOhgRUJAkXGpWXi21jNHMrAAnA0eL2nRrHA4jsNJMrbTga0YzrwWpTHoEYjMGXAaxTdY78yMugusbKk67b6tyAh1jbVwQWXA9lQyhei2yug/caGgg7BU7Bn1HHVUT6DY9lB0YbQRU+hojRwxx7KtyAzYnsq4IKjAjbQJUgiklRx3Ggsa6wFFSzDAqus5kZdBdY2CpNbtvq3ICnEjbVwQWXA9lQyhMVbZR0H24Gugg7BU7KzDA46qhkCMcdhrSRhtUipcOMb
DZmt7V5yDsTtNRxpEgVBgB6+594l/Of7kiPIcEUk1Bk8DBpvtFAAAADAfyFxbztPKRExBY9lcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4T+VcmuPCfyrk1x4TeVCzuT/8ASNLk6Y9YqtR5OiXW5LUiIgwVQB8v7uVYAEqQDsPp6JBwwONMpU4MpG/MATsH94traa6lWKJcWPkB3mpo7LIkajRWe8I2tsWpLqe6sJjItxLI7a2Ps0Ud1AEkAAkmpI5IjoyIyHuYYULS6IUi3lIbYdE66EMraeEbnQ62o9HfWTsmS37FidCFes5oT2sJkSyAjiiXGW4wDOfktXOU7nkMUikxM8pEZxJcxqNZJPeatsulwIr+JZo/xYDGnybk+4kE8JdbbZ+djsVMakie24zjrmC3gKMogj6RwP7mp3hZ8IY9CMahicWPzOYo4UMUYKdhw1eiQQcCCPW2tjPdKzropGvWkc6KipLBllgiS4glMraI0Gxw30baQXRtsVL8ZxerZjjhSWEz3M9uGTSiDFjicOjmurWS0l4qQqW0Q2r500UiIjshCvjok9uFKpdlUbWIFXNu1rPJCzAlDgSKiilmbQjRnbAnADH1stlHbWEc0zMJpjjGnYF7z6Vtb8ezlm0Yo10pG7h/8mrhIVSFkUozYnRJx6PYTWT7Fr+YoG0UVSzv3CrOwW6u5I9NlhjxLuRg
QoqTi9N+Lx0MTo47cM9pDHkbJr3Eg/3WXFt52LWT7Y5SuZrq6YmJOnIe/wCVC8gvn47F4EtwUCAYqwfUBvq7SLIsaxQnSupFxMhGtF+VR6C2qZQuoQFiQLBGTjpsf6jUNxc21lLezuz3FyQsKftgKsFvLmZ7NZGVZGJm3DbWWLtYVTJtoMFUAPh+1I0tijxXFoSsuiwD4rrWnW9vy0whdwow6C9FQOwVkyxa/uQmxF1uflV7It1Iu1LGA6EYXbIR+Gri2vppS5spEBHRUIQAozWdspRrmZSY1OCJ4j924dtXE7SEgsGJOLEbMewD5DPFCZVlbTRQi49I4E/IUIBZoun7w66WvZEneR+I1LJxjlteGwY7cB63KxMMVjaLqRYFc/NnrIqBsowk7EDOf0FWMWTpsoJJHcTM+m0mBTAd9WkhMOWLvtZNEf8A5DVpFx1zbx/ikUVlSXjMpXL7QJMPt1VlORW5JZpax6bQpo7egznYKt0tYMow2UVukrq4Ekr941nRFXknHXdxJ+KRjS2kljYi7Nw0csvQRF7QaNk4tHuMcSkiqw/Dj30Y2GiCOk2xe2pMmC2WFZ3PHykaESDEjfV/YLaTpAkum4QNIdgWrO15XcpCHADHbUeSA9/LamU4LpHEDsHaagyYZYrly5HEoWJ/pBH9NW9g0lu11M/FwDUDhiznuUU1iTcpCjHWExx2qW7Dh202SAuURacaSp7QNezEmoMkmWaaIyezDEsNigbCd9ZLyeLlzNMQtvF0mJ7cKylheMs6SFpWcIsQXqrhiAKbJawkRzykSmMuwXZGB+I1Z2D3TaGJWRgCg/cnuFS2zwySoxX/AG20Sw2Y1ZZOe6jkndxFBH1nP7CmtQ4iaDTIkZgA2o9Htq9EOToIrJenLqeXuLdgNMskkuBOlIx11djm20jsITjcTYNKR+1SW7WdglohxnuSDIw24HYoq4yXLHei0hPGOVU7sansYoUmAmLvEVUnDolieqtWUI5f
axv4q6QrhPIwS2i/pYsxrJeUbGCxuLa50umx2DrBhSXsKXNuViKW0UgbQ2k/M95rKd1k64nFxEsksuAGiRgmqrzKuTp4rVZYncqyllwKgVlPK1rIyNahjIqFVc6ggP4R31kBFhs7u7IxOvyQY1ZX0Ftyi6kxe6ckJ/09parSzkvjBDO7HpNPKSdYVsABvNJewXUpGi0VvZvxiMmzBew76eSHJOTjLGum9y2l0ho9ei72Fst5cANdyjRgQjVGvyFTNNZWPE4s97edJ+0has7SW8mWNBqx6TdijvNSKJ1bbFYWy6I7Hf8A/qjC3EpAttFym56iaI/2Y+8nvNRrZxHVFG0FoDxkpUEyuewU8QaARJaxC4u30kULrjTvJqGC1RyNBWhslLyyYa5JO7cKhXlLS31z7sjaRB2yP2CrjQtoHeWFBc3WsJojCJN3YT6031ldQwrexS8ZEugHjI1qO8GoLu2tpLtoo5AskBjTEgkE9pqwuks2ncqSzQsiYdhakukTJ8lsFOm8oYnswFWFxHa3cU0illQk4CrmXJ0iyNFFOJWbHF2GFXN/bS3MF3HHIJVZCysRo4J3UmU7GC7luIoJS0mkWLEai34c11lOGaeB1hOhGFAQ6goG3CmyjBHaSW9vD7SQuxfYO4AVbzGG5inYaei4Yg9tRZUhTKLXbo8mJO9R2ACrm5aeSRvxsWY9prJ19DYLNII2a4IwQ/0rUGU1t7S4CBzdznpymucUGT4rQIRgxL9z1JlMT2ltCxdDETjoAdLu3VY5QgtZGleIllB4pR1QT2k99W2VRALqdlZ7ubUH7FFRZSEdhJb6LabyFnYf1g0l+BZz25BUySAkr+ADq1DfyQ3EMuiNGNh0R2irq9SeaWUF3LtiFcAKv6DbVhlIWiXekGMsoAVxtFSSNIe4DYKGUImydFZurjQk0jo7HG3A1Y3CcXNezQokEKhYlH4qluC8kkuJaRySXP8A4FWsqQXMMrrpKjhiN1G+he+a
eRXZGcs3eQNi7qXLGOUuVyISgDBFH9NWuVmgupZmBPGKQT24ntp7j8DOzfjbs/KOykdo3V1ODKQQfmKmyrYZRt1jvY5EkXYyU/NyHoG4k36Kf/NC6jT2drCPm2Ln/vUGW7yEjoxFfw6AX9queEMEqBRZK+rXxlTvbyNpRQmLvXHSX9KyVlWOzjlt50LQv3VJLk2Bi1rHLI/9PG4aK/oNtZJypDatdG6DsZcDiKu7qOQCK2i4qAHHR2lj3savMtWkyWwS3JeMg9Mal3VcZayfNdW0ht2cJtZuzcKyjlKG4lka1jYNIArO23DuUdlTnm205MpwuJwGmP4V7FqHKM0NtycRxleM08WGJxqTLFy7zSaEavImjio1rTXkjQwQ6CCOJtLR/Ee9q54uBdNc8XFpFdHDDVUOUZoY7iPQjZZQAQw1DDuqxuna1LXCJye3bTxw1u52KKuJ5LqZ5pDizn+z9oq+vIpYoLa3DCCJe3UWY7Sf5SGVoZUkQDSU4jEYipJHmkeSRizscSfSe5lkhigJAjjJIAGGs9p/90H/xABAEAABAgMEBwUGBgEEAgMBAAABAgMABBEQEhMxICEyM0FRYSIwQFJxBRRCYnKBFSNQYJGxQyRTgqE0cKDB8OH/2gAIAQEAAT8C/wDgq1ioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioio/RCoCMQReWchF1wxhnnGF1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6
xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYfWMPrGH1jD6xh9Yw+sYcN5foJWBFVKgN84CQP3Ecoby8eVAR2lwEAfuU5Q3l45S+AgI4n9EvCtP2Gcoby8aVFWoQlNP0R1wNIKoDyw5frrhtwLQFD9hHKG8vGE3jQQE0/RZx2+u6Mk2STt1Vw5H9hHKG8vFqNdQhKafosw5hNk8eFo1a4ZdxGkn9gnKG8vFLVSEJp+jTzlVhHLQkXNakff9gnKG8vEk0hIvGv6O4q+tSuug0u44lXX9gnKG8vErNTSAKfoz5utLPTSZXeZbPT9gHKG8vEHUIQK6/wBHnNwrSkz+Qn7/ALAOUN5eIcPCAKD9AKgMzGM1/uJ/mA4g5LT/ADozm4OlJbj76FYxmh/kT/MYrR/yJ/mK/rJyhvLxA7S/0CaC8ElJIpFSbUPOo2VGGZ4HUvUecVrY+LzSx00pVNGG7FLSgVUdUOzyjqb1dYUtStpRNqVqTsqIiXUtTSSvPu3JlpvM64/EU+QwidaVqy9f0s5Q3l4dWUN/oB1iHm8Jwp0WJlTWo60wlQWKg2OJuLUnkdBIvEDnCRRIHKHXUtJqYdeU6qp0Wm8RYTAFBTupuYKewnPjoS8ypo0OzANf0k5Q3l4dzZhOQ8At9tGahCp9I2UkwZ93gAI97f8ANHvL/nMe8v8AnMCbfHxQPaDvECGJoPdmlDC1XElXIR7+jyGPf0eQx7+jyGPf0eQx7+jymELCxUZWTbGIio2hpS75ZV8sJIUKiJ9qhC+eehJN3nL3lhxwNpKjDrqnVVOlKM3E3jme6UaJJhRvKJPHRkF3kFPL9JOUN5eHc4d+7NNt6szDk065xoOncyO+/wCMP7lz6dKT3CLZuW/yI+40pWYwzdVs
mHEBxBSeMKSUKKTmLM4ZQGGtfqYmHy8r5RlpSsvfN9WXdva2l+ml7O2nPTxSlBAqo6o98Wo0abrHvM0naZ1QzMod1ZHl3hyhvLw69od6pQGsnVD82V9lGod3Ib7/AIw/uXPp0pPcJ0H5Ovabz5QUlJoRTRk37wuKzGUTbF8X05iyTYr+Yr7ROP1/LT99JiTJ7TmXKAKd5MNYThHDhoyTdxuvE2qm2kqKTXVHvzPWPfmese/M9Y9+Z6w26l0VSfBOEzL9wbIhx73YpbQ3wgzbiN4zQRMNi6H24l3MVsHj3ZyhvLw6tsd4TTWYmZgumg2e8kN9/wAYf3Ln06UnuE6Ljba9RTC5AfAqDJvDrBl3h8BjCc8hhLbyVAhJqIQbyQSKQuTQpd7hxEKqEEIGumqCy9XWgxhOeQxgOn4DAlHz8MJkD8SobYbbyHfPModTQw7LuNcNXOzOJeUJN5zLloP75froyG7V6+BORiS3jkTdfeG6ch/cPuOLupcTcFYUlKWCOF2PZ+yv17s5Q3l4c7Y7yde/xj797Ib7/jD+5c+nSlNwn9DLDJzQIDbaNlI0X98v10ZDdq9fBH/SzFfhMONKdeacRS6KRMsl1GrMGHnChlLXx8Ylm8NsDj3ZyhvLw52x3bq8NBVBNTXTSlSzRIrCZF050ECQTxWY9wa8yoMgngswZBfBYiVYcadqoaqQ/uXPp0mFpQwi8aQqeaGVTBn18EiPf3ukJnz8SP4huZadyOvloLUEJKjwj35n5o9+Z+aPfmfmj35n5o99Z6wkggEQ6+lml6uuPfmfmj35n5o9+Z+aPfmfmgTrJNNY0XXkM0vcY9+Z+aPfmfmj35n5o9+Z+aETbS1U12GdaBI7WqPfmfmj35n5o9+Z+aPfmfmhtxLgvDKHXA0m8rKPfmfmh1QW4pQ4nRlphDKSFVzj35n5o9+Z+aPfmfmhp9D2zW1c0yj4q+kGf5Ij39fkECf5ohE4yrjT1hx9DYBPHlHvzPzR
78z80e/M/NHvzPzQhYcTeGUONpcTRQj3Z9rdL1RdnlaiaQzKhBvK7Su8OUN5eHO2O7n17KPvpy8oXO0vUmEoSgUSKab+5c+nvGJxSOyvWmEkHWLJjcr9NKX3DX0iJpF9o8xr0pZd9lPTVoTq7ztOWjJN33a+WxzeL+o6MhuvvE/ufv3TLKnlUGXEw22ltNEiHphDPU8odfcdzOrl3cluR6+AOUN5eHO2O7mlXnlaUoxiKvHZHcv7lz6e9k36HDORysmNy56aUvuGvpFjyMNxQ0ZBetSPvao3QTCjeUTz0ZRFxoczrsc3i/qOjIbr7xP7n79ylJWoJGZhpoNICREzMYQoNqCSTU95JbkevgDlDeXhztju1mq1eukwjDaSO5f3Ln096DQ1hhV9AVExuXPTSl9w19Isn0ZL0WV3HUnrbOLutU56LSMRxKYGqxzeL+o6MhuvvE/ufv3Mi1QYh+0OuBpBVClFZJPeyW5Hr4A5Q3l4c7Y7pWRg5nRbFVoHUd0/uXPp76RVVsjkYmNy56aUvuGvpFjqL6FJg6jTRl132kmyeXVwJ5aMg3tLtc3i/qOjIbr7xP7n79wkXlAc4SkJSAOETrl5dzlotSjjmvIQJFoZ1MGSZ6w5JKTrQa6UluR6+AOUN5eHO2O6VkYOZ0WtTiPqHdP7lz6dJhgvHpCZZlHwD7w7KtrGpIB5iCCkkHhoyB7SxExuXPTSl9w19ItnG7jteB0ZBe2j7wTQQ4q+tSuZ0WUYbaU2ubxf1HRkN194fbDqbpj3Af7n/UOJuLUnlosS2Mkm9SGpMNrCr9adIyFYUq8oq5nQlGMQ31ZDRnWf8g++jJbkevgDlDeXhztju3BRavXSbVfQlXMdy/uXPp0pMUZTbNb9ejIbxXpExuXPTSl9w19ItnG77VeKdGXXceSYml3GT11aMq3fdHIaDm8X9R0ZDdfe1/fL9dGQ3Z9bJg0Zc9NFhNxlA6aLibyFDpoyW5Hr4A5Q3l4c7Y7u
bTdeV10pJ6n5Z+3cv7lz6dKT3CbZvfr0ZDeK9ImNy56aUvuGvpFp1w4jDWpPI6Mw9iJa9NejJN3W73PQc3i/qOjIbr72v75froyG7PrZN7heiNkemicjCszoSW5Hr4A5Q3l4c7Y7v2gjJenLzYPZc/nuH9y59OlJ7hNs3v16MhvFekTG5c9NKX3DX0jQn0a0r+3coTfUE84SLoA5aDm8X9R0ZDdfe1/fOeujI7s+tkwKsuemiwq+0g9NF5VxtR6aMluR6+AOUN5eHO2O7cQHEFPOFJKSQeGm2+63kqEz/mR/Ee/M8lR78z80GfRwQYM+vggCJWZW6u6rlWH9y59OlJ7hNs3v16MhvFekTG5c9NKX3DX0jQeRiNqT3Miiqyvloubxf1HRkN197X98566Mjuz62EVBEKF1RHI6Em/cNxWR0Zt++bichoyW5Hr4A5Q3l4c7Y7ycYr+YPv3shvv+MP7lz6dKT3CbZvfr0ZDeK9ImNy56aUvuGvpGjNN3HTyOvuJZGG0kcTr0XN4v6joyG6+9r+9c9dGR3Z9bZ1u65e82i1NuN6sxAn0cUmDPo4JMOzTjurIaUluR6+AOUN5eHO2O9mZbDN5Oz/XeSG+/4w/uXPp0pPcJtm9+vRkN4r0iY3LnppS+4a+kaM63ebveXTl28R1I4cdJzeL+o6Mhuvva9vXPXRkd2fW15vFQUwQUkg97JbkevgDlDeXhztjvn5P4m/4jLupDff8AGH9y59OlJ7hNs3v16MhvFekTG5c9NKX3DX0jRULwI5wtNxRTy0pFuiSvnpObxf1HRkN197Xt659WjI7s+uhNS+J2k7UZd5JbkevgDlDeXhztjv3Zdt3Ma+cOSTqNntCCCM+4kd9/xh/cufTpSh/ITbN79ejIbxXpExuXPTSl9w19I0p5FFhfPRAqQIbTcQlPIaTm8X9R0ZDdfe17eufVoyO6Provyod1jUqHGltGih3cluR6+AOUN5eHO2PAqQhe0kGF
SLRyqIPs9zgoQZN8fDHuz/kMe7vf7Zj3V/yQJF88hEvKqZXeKuELTeSU84Ps/wCePw4+ePw4+ePw4+ePw4+eGkYaAnla9J4qr96kfhx88fhx88fhx88fhx88MSpYUTerC0X0lPOPw4+ePw4+ePw4+ePw4+ePw4+eG03G0p5DSdaDqbpj8OPnj8OPnj8OPnj8OPnhqSw1hRVWmmuQqom/nH4cfPH4cfPH4cfPH4cfPDDWCm7W1yRvKKr2cfhx88fhx88fhx88fhx88MM4Kbta6RSFaiKwuRbVs9mDIPDKhj3R/wAsCSfPCEyB+Nf8QuSQU0Tq6x+HHzx+HHzx+HHzx+HHzwy1hIu1r4A5Q3l4c7Y/d5yhvLw52x+7zlDeXhztj93nKG8vDnbH7vOUN5eHO2O+UoIBJyhz2ia/lp/mE+0l/EgQzNNvDs58tB59DKaqhXtJXwohHtIfGj+IQ4laapPe3gMyIrXSxW/OP0N11LKLyoVPvE6qAQz7R4Oj7iEqChVJ8acoby8Odsd97SXS4j72sLKHUKHPQnVlT6umq2QdKXbnBXeTk2oKLaNXMwVE5mEOuNnsqMSk1jDXti0kJBJ4Q/NLeOdE8rGZp1k51HKG3A4gKTx/QfaLl5wJ5C2TfLSwk7Jtfn13iG9Q5wmefTxrDM+2vUrsnxByhvLw52x3085ffV01WyyMR5A62qUEpKjkIWq+tSuZtkEXnwfL3a1XUk8oWq+pSuZtll4byD1tnl3GFfNq0PZq9tH3/QFqCElR4QtRWtSjxNsu2XHUgc7F1uKpnTQkJg1wlfbw5yhvLw52x3r7uE0pX8RnbIy+Gm+raV/VvtB7/EPvoSjOC0OZz7v2g7dbCBmrQYQVuoA52z7t925wToezUm8tXT9A9oO0SGxxz0JSXwUa9o52zkoUqLiNnj0tla+8N05+HOUN5eHO2O99ou1WGxwztkmMVd47KbXXA0gqPCFqK1FR42yLOI5U5J7tRCQSchD7
uM4Vfxa22p1V1IiXlksD5uJsfcwm1KgmpJNrLKn10H3MNNJaQEp8eSACTDzmK4pVsgxfXfOSdF6Qbc1jsmD7Od8wiWkwx2iaq8Ocoby8Odsd44sNoUo8BClFSio5mwCppDDQaaSm2fevKwxkM9CWawWgOPG1brbe0oCD7QYHMwPaTPIwiZZcyWNH2g//AIh97c4lWAy38xzt9oPXl4YyFqEFagkZmGGUsoCR99BTzSNpYgTLB/yCAa9wp5pG0sCEutr2Vg6NQePcLm2G81/xH4m15TDc4y5kqnrZ7Qeupwxmc7QKkAQw0Gm0ptU80nNYhLra9lQPcV0SaCsGbddVdYR94uz/AJ0wicW2u6+inXujlDeXhztjvPaLuTQ9TbIovvjprtmXwy2TxOUE1NbZFnEdvHJNhNImJ/4Wv5gqKjUmuhJTZSoNrOrha64Gmys8IUorUVHM2yDN9eIck2vuBptSoJqSTbIMUGKeOVrrqGk3lGHp1xzUnsptbecaPZVEtOh7snUrSnpgti4nM2AkZRIzKnOwvMWOuoaTeUYfmnHjnRPKAtSTUExJzl83HM+B0XXkMpvKMPzbj3RPLQam3mslVHWFrU4oqVmbfZ7N5eIck/3bPPFpACc1WAkZRJzZUcNz7HQJpD8/wa/mFPOr2lmLyuZhqdeb43h1hmYS+mo+4tn1kISgfEYLRalqNm6QM4Q7MhnGxKiuRhSRNS1aayKiPZ7l9mh+HV3JyhvLw52x3bjiW0FR4QtZcWVHjahxbSryDQx79M+b/qDOTJ/yQpalmqiSdCVawWQOJ1mCaRNzZdN1Ox/enLrxGUK6WT719dwZJ/u1IKiAOMMthptKRbPvX13Bkm1louuJTAAAAGQsedSygqVDzy3l3lfxog01iJabS4gXlAL0ZtV6YX/FsgaTA9IemEMDtZ8oeeW8qqv4tBoaiG1320HmLXnUsoKjDrqnl3ldwBU0EMNBppKf5tn13n6crWq4iKc9Ccms
U3EbH96LDymFhQhuabeGpX2s9of4l8jDriQwVcCICAWb4qSk9ocIS8gsYg2QI9mijSjzPcnKG8vDnbHdO+0bqiEJh6Ycf2j9u6kmsV4ck67J6Zr+UnLjoNyrzmSIWhTarqhrt9nn8j/lEw7gtFXHhB12+z2byi4eGVsw7gtFX8QTU1t9ns3UYhzVYohIJOUTL5fX8oy0ENOObKSYcYcapfTS1mZdZOo1HKG3A6hKxxsJoCYWbylHraCUmoOuCSo1JrotJutoTyFhNBWJp/HX8oy0EST6xW7SHWHWdsaEgzeXfOSbVG6CeULVfWpXM2yDFVYpyGVs/M0/KT99AJJyEEEZi1uaebyX9jFETTGv4hCVPSnYWi+3HvzFNg/xBxpzshNxqEIDaQkZDuTlDeXhztjunk3HVp66NxZ+ExhO+RWjJNYbIPFWuJt/Bb1bRytAJNBEtJBHac1q5We0t4j0t9nbk/VE+7fcuDJNoFTSGG8JtKbZ57EcujJNrLZdcSjnCQEgAcLJ6YvHCTlx0JWTxO2vZ5QlISKAUET9Pd1eo0JAES46myccw2TzOruZNrEeHJOu2fmP8SfvoSkoGxfWO1/Vk2AZdyvK0CppEu1hNJT/ADbPu3G7nFVrDRecCRCEBCQkZCyZfwG68eEEkmptlZLE7a9SYShCBRIpE+gFm9xB0JZN1hsHlZcR5R3ZyhvLw52x3U//AOQfS1CC4oJTmYYlG2hlVXOKQtQQhSjwEKN5RUeJtYbxXUpsmncV4ngNQtkJegxVZnK2eXefPTVawcCTvdKwTUk2yDV929wTbNO4TJVxyGh7Oa1KcPoLJt/Bb1bRy0JOXxl1OyLfaTmy39zbLsF9dOHGAAkADKydexXdWSdCWkRtO/xHtFKEpboKaEmzhNDmddkw8GWyrjwgkqJJzNsixiLvnJP92+0F3WbvmtkGr7l45JtUoJBJyEPul5wq/i2TYwm6naVYTQVMTL2M5Xhwtk5bFVeVsi32
i52Ut89dsmxjOa9lOffHKG8vDnbHcnVD7mK6tXW32a3tOfYW+0XKJDfPQ9mt7Tn2iacw2FnjkLWkYjiU8zAFABY84Gm1KME3iSbEi8oDnE+q4220NCTaw2U8zrsWtKElSjQCJmYL6vlGQtAvEAcYaRhtpTyEEhIJOUTDxecKuHC0CppDDQabSn+bFrDaSo5CHFl1alnjbLNBppI48bJx/Cb1bRy0JCXr+ar7We0F1dCeQtlGsV4chrNhIAqYmn8dyvwjLQl2sJpKf5tnHsV00yGoWyrWEykcTrNs5NYpuI2P7tlm8V5A4cbZ+Y/xJ+9qElagkcYabDSAkcLFKCQSchDzhecUu2WawWgnjxseeSyi8YlJwvqKSmndnKG8vDnbHczzmGyeatWhKIuS7fUV/m2acxHlH7aEsjDZQOke0ldltPWtsgKzCbZ2YxV3Rsi2QbvvXuCYm13319NVsujEeQnrYtaW03lHVEzMqfV8vAaEii++Omuyff8A8SfvoSSb0wjprtnZnFNxOyLWRedQOtjrqGU3lGHnVPLKja2guLSkcTCEhCQkZCFG6knlDi761K5m2QbutXvNZPTV78pB1cdCURffQOtbZ2ZuJuJ2ja0KuIHWxSggVUaCJqcLvZRqR/eh7NT23DyFk1MhlNBtHKCSTU2yCav15C2emb35ScuNsk3ffHy67HXUtJvKh55T67x+0ezm7qCvn3ZyhvLw52x3PtJdXEp5C1IvKSOZgahY8q40tXTQaTecQOtntLeI+m1hzCdSqPeGaVxBE1O3xcby56Eu37vL686VME1NbZAgP6+UOzLTQ1qqeUPzC3zry5aMi4EPa+IiYnUNiiDVUEkkk6Eq6GnkqOUKm2EjbrExOKd7KdSdBlQQ6hRyBh32g2nY7Rhx1bqqqOh7ObqtS+Vk8u6weurQam2EMo7WQyh+eU52Uahoyiw2+knKMVsCt8Q/Pgams+cEkmptBoax+JdnUjtQ6+48e0dGSfSy
pV7Iw77RSB+WKmFKUslSjU6Ei4EPa+IpBUEipMTM98DX86Hs5SUuLB4iHptpoZ1PIQ88t9VVfxDLRecCRCUhKQkZDuzlDeXhztjuZ7/yVfa2UF6Yb9bZ80lz66EmKzCLPaQ1tq7iRl8Rd87KYmtUu59OndVdvU1eGkU3ZdPXXZ7SO7Gk22p1YSmHEFpakHgfBNNKdVdTDshcbvJNSM9C8TxOmAVGgziVlwwj5jn3hyhvLw52x3PtJFHgeYt9nCr5PJNs22XGFDjnoSArMD0snGsVkgZjWNOXYU+ugy4mEIS2kJTkImhWXc9NJhlT67oy4xNNJEqUpGzpSkpim+rY/uJ2WwlX0jsnRSkqIAFTD0u4xdvcdH3B3Cv11+XQltw19Is9ooqhKuR0UIUtQSka4lpcMJ+Y5mPaDFQHRwz0UIU4oJSNcPySmmwsGvPRZYW+qiR94ZlW2RlU84n2MNd8ZK/vQSkqIAGuJaXDCPmOdkwjDeWnroJSpZCUiph2UdZTeVSnTQArqESkphdpe3/XenKG8vDnbHczTAfRd48IW042aKSbJBktoKlZq0JqRqStr+IKVJzBEUrEjLloFSszbNSVSVtfcQQU6iNBiScc1q7KYbbS2m6karDEywplZ8vDQZZW8qiR94ZZSyi6IUkKSU8xDjZaWUnhoS0iT2ncuUAAQpIWkpI1GJiTW0ap1ptbaW6qiREvLIYHNXOHmg82UmHGltKuqFgBOUSknc7bm1wFk4wWnCQOybWU3WkJ5JFi0JWgpORh+TcaOoVTa1LuvHUNXOGJdDA1Z87CARSJiSWgkoFU2syrr3Cg5mGJdDA7OfOCK6ompcsr+U5Wy8mt3WrUmEIS2m6kUFjjaXUFKoflnGTrFRzsbZcdNEpiWlUsa81c7Z2WLnbRmLWmluqokRLy6GB83EwpIUkg5GH2VMLKTlwsQhThokVMSsoGe0rWv+u+OUN5eHO2O7uIHwD+NIpScwDCW0JyQBorbQva
SDBkZc8KR+HsdYQwy3soGitKVChFYX7OaOySI/DT/uQj2c0NokwlKUCiRQWvy7bw7Q184V7NV8KxCfZqviWIalWmchU8zorlmHM0R7hLjgYQhKBRIoLVoQ4KKTWDIMHnDcu01sptUARQiFSDCuYhuSZbNczoqZaVmgQJdkf4xpLaaXmgQlhpOTY0FJSoUIqIVIMHmIbkmEa6V9dHOPd2a1wxAAA1Cmi5LMu7Sdcfh7HWEIQ2KJFLVtocFFCsfh7FfihtpDQohNO/OUN5eHO2P3ecoby8Odsfu85Q3l4c7Y/d6sjDeXh1bYioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioioiohRFDDWz4dza/eCNnw7m1/6Hc2u6mHyzSgrWPfXPKmGJguqIKf1Z1V1tR6RjO/7iv5gPOj/ACK/mBrA8W4bqFEco96f88ImXryRe496ja8Q5td1PZoskt4r072YmSFXUHL9FmZi52UHXDZJbQTytf3K/S1Oyn08W7ul+lje2n171savEObXdT2aLJLeK9O8mX7gujM/osw9hJ+YwTWGd0j6RbnHu7PkEYDPkHjHd0v0sb20+vejUPEObXdT2aLJLeK9LDlBm3uYhiZWpwJVxh6YS1qzVCpp48aQJl4fFDU2FGi9XW150NJrx4QSVGps98d6Q1NLW4lJprtWtLYqowudV8ApHvL3ngTTw+KGptKtS9R0FzToUQDxhubcvAKyh15LQ158oM46cqCJV5bhUFWPzLiHCkQJt7iYW8htN4wubcOWqPeHvPDc4r4xAIUKjK1ycKSQEwZt48Y94e85gTbw
41hqaSvUdRtcWG0lRhay4oqNiZtxIA1aoTNuFQBpnY5NLStQFNUe+O9I98d6Q0orbSo8bXHkNDX/ABCpxw7OqPeHvOYE08OMNTiVal6tB+YcQ4UpgTb3OFPIQgKPGFzjhy1R7w954ROK+MVhKgoVGVrs5dUUhORgzbx4x7w95zAm3hxrDU2lepWo2u7pfpY3tp9e8bGuviXNrup7NFklvFelhyNgJSQRBNdehKuX2teY1WPFxxZJSbbi/KYl0KxUajYpQQkqPCHXFOqqbAw8r4DCkLRtJpZKPV/LP2tc21etilFZqbJHaX6WTO+VYpRVnoSTmsotmUXXT1gNOHJBjAd8hjKyUdvoocxZMlxxZF00FtxXlMIQu+nsnOx5CsVfZOdsvuUWOuBpBVClFZJOdgZdPwGClScxSyUf/wAavtbNb9X2sUoqpXhoSbnaKOds0i66euuA04ckGMB3yGKUslHb6bpzFju6X6WN7afXvEig8S5td1PZoskt4r0sORtlWE3b6hnlF1PIRMthterI2SJ1rFhyMHPRnV6kosk2garPDKxaAtJSYULpI5QhV1QPWM7HNtXrZLsYpqdmAy0PgEBKU5ACyZ3yrJZnENTkICUjICJtpIF8D1sljR5FtBytnEJLd7iLJM0d9Ra5vF+tjO6R9I0H98v1sl9yiydXVYTysk2golZ4WOthxJBjKAaGsIVeSD0smt+r7WSzOKrXkICUjICJtpN2+B62S++R620HK2bQC3e4iyUNHh1sd3S/SxvbT6922nj4pza7qezRZJbxXpYcjagUSkdLJ74LJHaX6WHIwc9GbP532sQ+42KJMe9Pc496e80ElRJNje7R6WObavWyWFGU6EzvlWSe5+9kzuVWMb5Hra9NBvUNZhUw6r4ovK5mKnnZK74WubxfrYzukfSNB/fL9bJfcosmDV5frZKj8kWvijq/WyVP5KbJrfq+1knuvvZM7hdjO9R62vTKW9Q1mFTDqvii8rmY
qedktvk2O7pfpY3tp9e6SKmB4pza7qezRZJbxXpYcjaMhZPfBZI7S/Sw5GDnoze+NiGHHBVMe6PchHuj3IR7o9yEe6PchCRRIHSxzbV62M7pHpoTO+VZKbkWTO5VYxvketkw5htk8bGmy6qggSTfEkw/LNttlQrZK74WubxfrYzukfSNB/fL9bJfcosf3y/WyV3KbX98v1sldyLJrfq+1knufvZM7ldjO9R62Prw2yeNjbZdVQQJJviSYelm0NqUK6rJbfJsd3S/SxvbT69znCRQeLc2u6ns0WSW8V6WHI2jIWT3wWSO0v0sORg5mxtV9CT0tnU60qslHQmqDxy03NtXrYzukemhM75VkpuRZM7lVjG+R62Tu7T62SJF5YsmtyqyV3wtd3i/WyWVeaT01aD++X62S+5RZOJo5XnZJL1FFilBCSowTUk2NpuoSOlk1v1fayT3P3smdyuxneo9bJ3dj6rJIi+odLJncLslt8mx3dr+k2JNCIBqK9wlNPGObXdT2aLJLeK9LDkbRkLJ74LJHaX6WvouOGxLi0bKqR7w7UVWYGsCHW8RBTCklBINiX3UZKj3t7mIU+6rNZiSXtJ+9rm2r1sZ3SPTQmd8qyU3IsmdyqxjfI9bJhGI2RxsBINRAnHhyMOTDjgoaUsld8LZtF1yvA2JWpGyaR7w8fjMNm8hJ6WP75frZL7lFj7WKinHhBFNUAkGogTjo5Q4847tGyVZvKvHIWzW/V9rJPc/eyZ3K7Gd6j1sfRiNkcbASDUQJt4cjC5lxwUNKWS2+Ta4koWUmxLzidQUYlHFLSq8a69JKaeNc2u6m21ru3RWMF3yGJRtaFkqTTVYcjBYd8hjAdPwGBkLJtta7t0VjBd8hiUbWgqvJpqtdaS6KGFyjqctcYLvkMCXePwQnUB6WOsodzz5wuUcTlrjDWPhMXF+UwGHT8BiXl1tqvG1xl2+rsHOMF3yGGgQ2kHloTDThdUQnVGC75DEskoaAI12
PpKmlAZxgu+Qwyy4HEm6aVtelQvWnUYVLup+GLi/KYuL8pgS7x+GGJZTa7xNq0JcFDC5NY2dcYLvkMCXePwQ0kobSDY8y4XFm6aVjBd8hhkFLSQc7XpdLuvJUKlnU8KxcX5TAacVkgw3JnNf8QAAKC2ZacU6ohOqMF3yGJZKkNUUONj4KmlAZxgu+Qwyy7iJN00ra9KhzWNRhUu6n4YuL8pi4vymBLvH4IYlVIWFE2vMJd9ecKlXU8Kxgu+QxKNrRevCmgBWEpp45za/c6UVzgCnj3Nr9yhBMBAH6C5tfuKhMBswEAfoakkmMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaow1RhqjDVGGqMNUYaouGMMxh9YwxFwD/3uXEDNQ72sXgciIJpnAWg5KGjeB4/vB6YQ1nnygvPzCrqdXpHu7LKLzuuJM1v0HYrq7pSgkVJ1Q7O8Gh94DClC/MOGkFu9VbSbqU8YZlkvNha1KqYcklDYNYS880aVPoYanwdSxSHXkNCphONM6ybrf8AcYSWX0qTqSBrgPl1VGxq4q/d0zM4XZTtQhC319ecNNJaTQQ9hFP5mUNOMbCDYVBOsmEvtKNAoVsXMNIzVBmGgAb41wlQUKg6oD7ZVdCtcLcDabyodeW+r+hEvLBvtK2o/wDJdP8AtoiaXdQG05qhtNxCU8hY6wh0axBZRLdpRvH4RABcq85rH9xjzKtSR/1CJV1w1dVCUpQKJGrQU4lGZgGoqO5CgeP7FUoJFSdUIdQ5smveE0FTHvCc6G7ztJpmYBBy
MKWlAqo0hC0uCqTq0ph7CR14RrWrmTEuyGUdeMPvhlPXhCSXHApzZrD4SCxdpW+IdfQ0NefKG0Lmjfc2OUXSp8JSi6lBiYmCThtw3LIaTec1mEpxnaAZmCj8u4NWqGpdDFVkw+8XldOESsvhi8raMHKGlusgoDRJrnDLBCsRw1X/AFa4sNpKjCQuae1wotst9BDAcUS4vVXIaL72HRI1rOUBGGnXrcVCE3Ugchpk01wFKmDq1N/3DYF4qA1ZDxK1hCSpR1R7xMPH8pOqP9cIfcU2ySDriVWtxoKWYfWW2lKESzjjjd5Z4w4+6JhLaTqtl33XXVgnsix19LRANddrhohR6RJrW4lRUa67VzDbagknX4mcdvqDSYYaDTYH894T7y7cG7TnDovlLQ9T6WzLuK7dB1CGQlKAEmsTCy+8G0+kISEJCRw0TEw7iuE8OESTV5V88IJugk8IqqYe9TGGjDuU7MIYSu+upDacobRiuhMAAAARNv3BcTtGJJr/ACH7ROvf4x94lkBlrEXqrCVBSQRxide/xj7xJs31XzkIUoJFSaCBNsk0vWKUlAqo0ECcZJpesm3sRd0ZCG7kq1VWZhKC4cV7UBkmPe2K0vR70zWl6trrgaQVGGB/mc2jsiEpNbys/wCtNBfLyr2pELWZlzDTsDMxn+WjIZwNQ8TNEuvJaEIQlCQkWT57CR1hlbaWkC+MonHUlqgUDriXF1lHpDfbnCeVjhuoUekSA1LMKJCSRnDq3XHk3kdocIYcfUTfRdEPTQbN1IvKh12ZuG8gBJiTFGR1srSC0w65erU+tiVXhUaGIitLwrBcSM1C2sBQOsGL6QaXhWwLSrJQsJAzMJWlWRrYFA1oYK0pzIFl9NaXhWyYdwkE8eESiNZeWYGsWBxBNAoVsCga0OWhWkBxByULJt64m6NowyLiLiNs5nlCEBA68TZNvYaKDaMSbGq+oZ5RMOBlvVmcokm83D9oSpKjqNhUE5mEqCsjWyaV
cZUbGUYbaUxOamFQy5huBRjEVNdlAIRxMTfYl6JyrSJRxDbhvcRBfv8AZa1nnwELrfVU1NYTN3WQEoNQISRiAr1iuuBem1VIo0P+4UbiSeQhRK1E8SYaQGmgOQ1w4tUy8Bwrqh5ptEuoBMFwNMgq5QrGmLy+AhamTKaqf/2HHsOWQK9ophoLKxdTUwloI/NeVVX9QVOTbl0akw4G5Zk0GsxJNXlXzkLVFU29ROwmDcYF5R1xecUkuLVcRyESynnVXirsCJmYUFBts6+MPuPNoT2qf3CXHsCuWrMxJuPLKio6omX1Xg23tRMOKbbDdarMMIuoupz+Iw+4JduiczlDRWhq+6rrDC3XllVaI5eIa1zp9VWznaebRHuLXMxMMpaWhKeMbKPQRJCq3FWThowqJNNGB1sb7c6o8qw8vDbUqJJvUXDmYnldhKeZhpN1pA6WTbxPZGUSjWG3eOZh1SloWRqSB/MSjt9unFMXiTRP3ME3RUnKHH3JhdxGoQW2pdk1HCJZBeevK101wVa7qc4GoZxOqN3OmvLnEnewykc/4iaIxQlPCK3k3l7A/wC4ZWXJoECg/wDqH3wynrwEMpL5Ljp7IhrtPKWkURSkPuVbVQ0HPnEmSCumZicokJTmrMmGby20prRIGsw4u+6gNigB1QtdxNf+oN+YeoYcZvpOuiEZQhQZYTfPCL7k25dyRC0pK222hsmpMKXrup1qh5QYNErVfOcSzi1NXnISVL15J/uHXA0i8YGJNuUJ1Q+lDbaQka7wpDjtwdaQygzD1VfeAEoHIQKq15CFruCEhUy9rgkNp/oQsqeeoTClBDdTsDZHOJMqU+pXTXExMhrUNqGEVGM8fSsS+rFcySo6oBK9eSf7id3P3hO0n1gQ4kLF05QJFsHWSYSkJFAIcQlxN05R7i3XaMJDbYoKCFSrS13r8YDeGWwNRhEi2DrNYAAibNGFQwLzyB1h1JWhSQaVhqXbayGvnC/znAgbKdaomnMR
y6MhqhlsNthMYSH5g3R2BnD6UocKU5CJVFxkddcTT+Iq4nZES7QZb158YeWZl4BOXCOxLoSmyZmKjDbNa5w3cl2tWvnDIMy5fXsjhD6y+4GkZQ66JdsIRnEu2G0F5zOGkmZdLitkRMKLzgZRlxhxxMs1dTnEsi6C85mYR/qZkk5QtaGUf/UMpMw4XV5CHnDMOBpGXGEICEhI4eIfSph/FGVYE4yRnSG30O1u8I2530sc7c6BypEwbrK/SJBP5RPM2e0D2UJ6w3NsoQlOvUIbeQ7W7wiWcQ245fNImfzJYlMS0w2hqijrEOlTjqCRqOVrYx3ypWWZh5ZuXgOyP+4de/02sUUrhEohd001A8YSkJFBE5XANIlEJbbxFcYnFqJTXUKVAhhOG2Ep2jrPSEpCYJCQSYUVTT2qFXZVnV/+MSzRecvKy4xPE9hIyhhGCnKriom0KC01NajOA2httIWdQjW58qP7iYcxVhCMhlDTaZZok58YAVMvesTP5TF1PpEs2EDFVn8Ihy8lpaztU/iJNASguHjFxazX4a5GJtBwq564lGhhXic4Br2Whq4qgJDaDSG04znayzMBN+mqiBkLJ+vY5RL3WZe/zhOK6u+UUplyEPuGqmgOOs8TDKUsNgHOKFetWXKycUUsnqaRKgNN1+JXCH1YSCTtq/6iTZvG+conalxCekMow03E5/EYeb/1F3nBS0gC8a0ygJLmtWpPBNjqAtBTC0KbVQwxNpuhKzQxit+cfzBmGh8VfSMdZ2GlffVH+rV5UwZZ9Wb0OSr6esa4bXMp2b0NzVdTibpsmG8RtQhJLaweRgTTJFb0F5b/AGWsuKoSgNNkDlFaKr1jHcmOw2KczDbYaRdEL1rV6xiOusmnZSBnCTRQPIxMTDi0Ds3UmJd1LRJKanhDSFLViuZ8Byh50NIJ/iJRn/KoZ5RMoUtqiIaafDSk5RLsPJr8P9wlhbj1VpokZRMpddIQkdniYuYbV1HKJZl9JJOq
sPsOqdF1PZgINO0df9Qw1MNrNExMtOFIu9o8YWVtMhKvsBEqxhpqdo+JIBFCI91Z8kJbQjZSBAbQFXgkVsw27167r5wpIWKEVEJSEigFBYttCz2kgx7uz/tphLaEbKQIWw0s1KBASAKAao93ZBrcEFtCiCUiotwGr166KxQER7u1Wty0gEa4DSBkIW0hzaTWAAnUBYpIWKHKENIRspAhbaHKXhWEoSjUkUhxtCxQiEpSnIQ4hChQisJZbT8MEVFISw0g1CBWFoSsUUNUIbQjZSBCkhQoRqhLaU5CCOEJaQnIWEVjAa8sACwMthV4J12qSFChFYSy2nJMOuBpBVEq1iqU4v8A/GAhI4WqQlSaEQhtCNkQtpCzVQrAASKAQttDlLwrSAANQhxtC8xCWkJyToONtr1FNY9yZ6wJRgfDCUJTkkaOA1fv3ddhSDmIApY5LtOGpGuEybI4VgAAahYZNkmtIQhKBRIpYqVZUq9SLqbt2mqBKMg1pDjKHRQw3LNN5DXB1Rrm3/kTA7taghJUYYSX3S8vIZftCYWX3g2nKEICEhI4fpD6HHE3UkDnDLQaQE95MMuPUF4BMISEJAGQ/Z5FRSG5dts1Gf8A8hL/xAAtEAABAgUDAwQCAwEBAQAAAAABABEQITFhcSBBUTChsUCBkfDB0VBg8eGgcP/aAAgBAQABPyH/AMKrOVcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArgVwK4FcCuBXArwTg/we4I7AeAKglc6MRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNR
qNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqNRqEADNVs/wADelspBch1QB/Yq2FWz6+sp6Ugr4U39krYVbPrriUcpZv4MFIDMbf0OthVs+sJRhIc/wCEOT2yuZRzdbyP9CrYVbPrD7QhDL+Fm5+6E1uHP9CrYVbPqzmoA/wvsuyKrAiQCoKaHief6DWwq2fVNGFSmTmp/hmq2ucnQ0bvJ/Qa2FWz6lg5Rin/AAxV9i0Wumx/Qa2FWz6lgFCAD+Gyl76rwC/9ArYVbPqDcKeE/wCHJrjanrR/oCthVs+ocZWgfwExAMpv9dV+Ycn0D8o1UMtBYHJRqA+xCkL7Ew/zNbCrZ9RMnj+AHJyYtwiQ5JJjsq4qFI/tEGHFIfdstV3EP8mB0IATgBnKqJvkDEm+AKqNPHTlLvAJijSromgLz/iq2FWz6cmIoZE/wAAgNCi7FtjSZGB4wh18CiHBBojltAUPUgB7oAugN8IqPgcp7MthxpMO3M8IIAoB0pr3OE7xGDufZAAEGR/ia2FWz6cvkULYvQbnuF3YEWqJOOBEq2QchC1aOzGXXAzmmIiA4oDO5Ql/w3GrlhVCDzODRMeOGhM9PJE5kERnwONUr/19K1AdFrIn0vpWnH8TWwq2fT7SHWlo2wnr2nRV8l37UEyMwg+1dU5vwFUFAQoZjGABIAVKGATSdXwEamwctL9MCANz1P8AYeqPRACLy7yq/wCApOyupWwq2fTzDx1QJAAJ+Ld3PTq5Lv3RZQjMjchsUQ50zO8wKW3NcQeNSCvHpD7GkAmQTSBu4gAAAwHTNETck9Ij4bvtGegLhfSF9IX0hfSFQs9E96f9KAHi4ET5kjYcJdHyU1LJ07Dnp1sKtn0/h9QAkjAKiwe/Uq5Lv3TY1kUTNmxVPAwKqC/81PR44kilEiJgoJAs50COTGI8Fyv81Ci+NcayV+prl/k16zR57FEJu4IAEmAcoME2zkhL1Njt
uE2fVGBwcyAhWvrlCPSp9OVsKtn0/jdR0v8AV1cl37UbfwWwKKunRF6yxfJ/rKMkVS/BdC4bOKqEgzEc5VM9OthVs+n8bpjPbCWUYxGZOt2ErKs+6vwhAvywhKzMkXiTQ4K79qNQQbdUn2Vt/lWV2YjAGeSWh1Bg2Vr4K18Fa+CtfBAxAlnwiMOCHBUqMStfBWvgrXwVr4IS90QgXmNAwnYK18Fa+CtfBWvggwWk0cQNwOJqK18Fa+CtfBWvghcrpM5M0lY+CoCOaZ9E7ArXwVr4K18E6bHIgSy5Q9yH+8w0v6ypKSNC7EiipWvgrXwVr4K18EPFmRdKJ0ZvBGTIew8J/PUK2FWz6fxum18rWwWgblNQhbX37U5OowCnk3CAgjg0PR6Xt8anhvM9tFhi2m0wf3h9JzpkPJeH0qRA9hAzQIY2EEX+Mpqcszy0909BWwq2fT+N08DlqkT+U9Hv3VM+oDoZIh1xq8saWOdJEJXYOjk9z6X0YL6DnTIeS8Pojhci97A8lB3P2RERyd+p3T0FbCrZ9P43SKeN2rlNp5PR791SACoQefHRyToGLHTxXVgxe9xtpuAZ4QAABD6DnTIeS8PouIJmWCMT2yjVuSer3T0FbCrZ9P43S7IruNNzBd+l37rfZx+jkgc4EBIVQdPJ7MciFiPnTMLgR+g50yHkvD6BRdSZUlgZNoUrzpEgs5Vae8i6M90OLDxuiCCx0909BWwq2fT+N0uyK7jSTv6P0u/aq/YalCJEvMifgchVyiY6faQ6WSeQ5dPiaAQnYIugAByyxtPMfoOdMh5JzxGFepNh3M+kdJjw6wvCEghbBGNVIdAig4xydIWbnp909BWwq2fT+N0imPfpEkMZ0Tv2oN1OIt7XjrnknAOT208VOx91eMjTO3KdH0HOmQ8o9z1m7rx86R5IcnSA/uSMtHdPQVsKtn0/jdIr5U1BN+R7uj37ovxvHXPJAAINCEYrpGo9H5aZwqfbR9BzpkPK
Pc9Z+H50t9lp7Rd2dHdPQVsKtn0/jdNwC2kdVEEBmO37oEEODr790X43j0B5KSRWbokDVJkEPQG0fQc6ZDyj3HXbWz+J6cLwcjSPT7unoK2FWz6fxumYWxAamTa+FuDMIv7yBqj9grPwW7uZI/vU0wIVOXfui/G8egPJc/NLIVOg/mOTp+g50yHlAruOuxEKEMik6kHxoEQ06R4OkbxzHk6e6egrYVbPp/G6jRuY+z1auS790X43j0J5L2uuhnQe+n6DnTIeUCu69D6fl5aQwL3kTRretAj7Mau6egrYVbPp/G6hDyRiDm26irku/dF+N49CeSaAqXbXc8+A1fQc6ZDygV33oeM5XbKGUxB6vdPQVsKtn0/jdUh3BEkYP7qkEmIY9Krku/dF+N49CeSCVoDIhbc2p49zDA1fQc6ZDyj3zo+yak7ogkQQxHU7p6CthVs+n8brgKXBVPB9hVEGAg8HoV8l37U1H8bx6I8kyFK86SCKksEERwavtOdNbKPfOlbvmOcpyMX26fdPQVsKtn0/jehGNkFWfdQ1ZmS2AcFMQtBtyrnvFMpGhghHtjIrlpcL6wvrC+sL6wjv78ouoOFfWF9YX1hfWEYEwcIR4sARwcL6wvrC+sKz+E/RpDUfyLr6wvrC+sL6wsHg2swtgJ5jlfWF9YX1hfWEeuziUc1z0X1hfWF9YX1hGPuH1CiEHBTwTPcFXHvL7yFtEe6/GED5sZOvrC+sL6wvrCMLeV9BWwq2fT+N/b62FWz6fxv7fWwq2fT+N/b62FWz6fxv7fWwq2fT+N1j1sFSnQBblBDOCynPJUq6HN4HKdSWuUYWaugCMQd+qaJZKATAg6SQKqY03Kf+CNPaHK9vUpgRB0Ag7j1tbCrZ9P43WMATWaJEKD8aAOTKVEb/ANXULzIgm5DlPwZBOAapGmEDn2RvNsQADHukqf38CYcy7xiWie2ImG2S1yPTEbhNP41P6ethVs+n8brSNSMXLjgRpqDl
GIVIfmJNnm6YDdAdEKbhifDDgxZW8nQZuJg/gKJgOq6Tkbnw4EBIO5bQc7eX+fT1sKtn0/jdUW9gfJEkiTUxkZI+ImwD3jqpoG/02cRjQDLZFoCj3OjiZg/gH8TnwiA5YKch41oi1ci4D1QWthVs+n8bqu2lPkYy3/IY7BxF9mTxkdve/TKUwHKMXp4RHXxPZSrMoATuwllGMJkuYjlP4EDKQ7+vOhIB0c7uZYjI/wDdoZdi+idyKES/S/T1sKtn0/jdSm04jKTHMCCCpLISatPMW8v5UQCSwXOaeURDrCsviRVRrfJwZaWAD1n+sQCQADkoKfkOLETlzEHrkVRjdydFAPuiLBYAcEEW6B1iXDrtpHSKQDrdSwBPEymJkLriSDKb8KJBBySwQG7CeYlWHOV2hjrdMO40gMRYCqMHTb1v+whB9NFBcOOjWwq2fT+N1Ksigd0GJ5G0KIQi5JjKDf8AeAAJJYBFn9dkaFFydDlo6jtHb0SuUeVyOYvgeDMTt2EsoxhMlzF7DOTGJUyHlHySwESTUwchxbZNzfn1Bx2HM8BEklyXKIORBTyZ5gMCtkPKLJtoE1GOUUH5pp9poblEiHtNAJrBMjtPEcwcERSm8SJJmUQciCg0jk6AAEksAp5+ejDk+6AKfMjIe1T/AIfI/wDIyRROJ+SkelioWJuKFG5H8OjWwq2fT+N0zLyBVpyfRAlr8F+kABXoUS+h8/zBQAJJYBER2DUonaqZsiE9+aIJUyYLbSE8xbTcmYibuZ4Q2mAwgWKQoOSiomNgGkhAjEIcCEp7p30P3g9kc4JPUrAqU67GwRIIjEFwrQp+Y+yGOSjwmBx0CCA5JYLjSPlF6CgtFzetiSyKQkldKeVyOQhZp+VYARxJeHsfdDNaLsRpJJlSiNi326NbCrZ9P43SK/oEnKJbQ2U6ToI3kHJLJX+NANzNyZI+ZBF5nBIeCZIiRJqYtIl3oj3KmSIQjMlzFzBwYgUJgDlP3B0H
VlLA7xCMroqdIQAQ2Dq6hGIwUAoQn4i5OgBywViEQEYiwCLJsIgEyCoQXkmSSDvtoexcGYhL0B0chuGMqfyI1HPf+NFTDhF2IDeJMOEZAmFknMFYZBCakwpJTdNCvkdGthVs+n8bolH449IpD+xX+UiCCQQx0PBzF7SUJJLmAEByaBDwLsAmZAHIwnXKen7IkEFSWQRuwnmM3Nv3iB26eEEBgDCE8ZNGWk7OSBAgbBVKx86HO3CIV3sui/DzItB7REXB0EHGB0SACpKBvDfKMtH9EfdyPAQNWBABd+Q3RERyS5MSNsg5TaIWRqQkmOdB6+n+ZplOfsdOthVs+n8bpABhRBc5JIOcN4kAFAqaTyPWhCfePE5M8BAABN0fECIRg8ERagoMQQny9xRjFSXjNI/bFqqvlOhreoD9YoSSSTWMo+e6AAAApCj/AGpEV2a0MhgDCDiC2hEBywTICc/c0y/MaaJyG+gVlgclHKcjmMpfzxXYXYRk9+yJm2A5R302Wi1g3D+oAMRgBNHPsyGMrvzlAASEGgKk7EZyG9+nWrYVbPp/G6JACTsuATJiLpOKf9ot9ecDRUsE1pmHuGNgwIAigDQ20hLKPVRLwKDqQCBwaftomble8NxAFx+MHqomCADog4TAHKI7BaJBBUlkBuw+UDXyHVek3iw2YPlBzBbP7aBG1IfZg3P+0WaVARkRgJkopA4B/MRNC39nyMZmRwf/AFAgS28lPKSpieXc+AiyHvvaNUwmVC4IEbYDko5NzIcCABJAFSg5JlA69kcoXtgcN062FWz6fxuiyhwNARt3EXFwLPbRyrMclZmXwjgQJgVMn85jPNF/dP7Yme0eOzNgQOGALgQ6Bxmgwb997aAvP8IEsHKmrO+TG6QwOcAcr2SxwI1AmlR/GCGQoDoh3kjPtT7QmLh5tocag7EQmPyWEQHKEYHggOU66IPcFHzB5j0nCIiOSXMRvOaLx2Sv8ReppAJyS8o8o7OE9YnT
gdOthVs+n8bo5H/mLe7A+UAABQCFujRLl43tFAMAEUv6nF+aAzwuDcp0d5ctE8qpkMVSXiHvZzAT+BwE4TbZpTtYNOigbthGQckuTosIj7p/NsCf/wAmdHMaEAb8RTlz40PXtYZMG4KnoAgzhHJDiG/vpPCxOHRdDeUOM5J0RyamJBBUF0Qa74J/e3tpm7ZHZNQ3IU+QN9E72CjUYF08CTP66CggMNA3HzS24L3prBCQkN062FWz6fxuj2vhFtcO+I5CA0d5w9vEdCQcyVyp/dr3lOz39M+OZglZDqGlM9lXJa9EDjn4U9NE0GqN76xgDkZBTxedSthVs+n8bo8F+ON0CIx0Se2jACgznX1HA+whFMBHFHPUM5DgKS4YRqMILAn/AARnSTsSgCpjLt40VQeEAa/LUowe576XEBLl6BqM5MedL4AlJQ/jpoRb7AvnzphH+LQc7EWATRyIcfUYOg7AlAELvN1TZ0EIAcmgQhAnLqq2FWz6fxujskMyujoEmJ2Qdm2raCVka/qjpBy4QImAcoFbbfAicSmfqEedA3iA9EdH5QoGZBAHBB3QWaYzaA7M2BD3unlGVQg+UKeZRAJkEwibbzygAAAAGyfhIOhHxxGn34UlzKp7cZ4KM2R8wIAA5KlReBCaE3FrRIaqBAQrgZERJzhUhTHyUW/x1gMCDg1XmTYRBFRAsGVGKDlUt0ACQcFETDmRMj93hDgYIU5iiOwjB5o32Qd02v6RGgUqY5CIILGsA59fhSXMkD/cDLOQciAaeRfXBbrVsKtn0/jdIgGRDoHOAnDUJbKB0WclYae7oFvvAoB5+6pgPOln8DypzZaq0+FOe2psXgER4SFAqnM0LpzJCyn2lQ44X5EkCHc90zjYiyEF0WcN91P53JmYlwBF1OgMKHZxUfTW37I24+FAABgG0lJ/2RJwfbQfDXEVce0URq9IgAxDhEtRwm8AOBpnAbhIoBLz+6ZPC0W2wVY7OHTMHXVsKtn0
/jf2+thVs+n8b+31sKtn0/jf2/sPUHhK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryvK8ryrpB6d4P7hIA9P4P7eA5AQp6fwdJkhgQPIGDy/ljDzMIS/sIt5HI3XIHqy1cCIRJ/wEcY8HgOqLhb1Hg6XYnrzJj3H+FN7pPCMg5IkxFwwCqFhWer73Ds/VmD6jwdLsT1pnf8Ai/hQSLCIRJMzoIQAIIcFEigL1l3uHZ+oJlCwPUeDpdidEkxGycFgeyLqCLFLmDjKI+JctyhgTvhE73KEcmcmFpRQsKMmwIwsEOTVEu5cyyioFe20DsAAhRGHASM5LfwqIa8BF5BYPSBJQALIYhgjCES1Ehyi9IoC3IIs8OQh87lGkXLOVsEYEIrIZBHwPxx2ee634YAuGAyHIwwD7YStLaWuAEXgc9gqqYA/MI53kImAP520H+AAbayCMwIwjJ0OByidIprchywA5FUDncoknWQOVsEYEI2nkESAvFHvcOz9R2x6nwdLsTontijUquUC4RCJFyUx4iQW5gYyBQNSP+WncRAHiFIEEU+2OIDHHgR2ZAeZyj3dAsQUencmATLNHZ3uwYeyYxI/oQ4iaTlOFWz7Ih/WiCTEMYEMf9MJtDYS7qkAUOPgVsDaEmEFiIILEMYdvAwPYclHrcoBXDeEZY5XgRwSX20ftWhNyhgmMTAikwzExW0pVs+yIf1okTEMYEKf9MO9w7P06piHqfB0uxOie2KNTBkfGgeESBi1hS3kOIewgYdsVUzpaA3mYEQeEAMZFFL1Ij4RB+yAsB5EO7wdpAd0Pl7odPk8qwbRxn/blBGCGEMtp0Q9xRJCCQcQccqgrghIeSPe
dRO9w7eHAYdzAALijMA9T2PBRBIg7IggqCrnwfatARCsHYIYQ0mINECaKJCCQJFIOOUbS8sGxwMO9w7P/CjwdLsTontijUwsIEPyQ7JDtiqmdLxDgISDA7wv2BVyiXMDfBh3eGWz1cGdyWv0SI/CFWzFgnv3InDEmzDtzHvOone4dvD4Cg1fJMWHfBy1KH2rQGY8nDsR50+dP61VshYImr8yfDPbOj97h2fpSZAwb1Xg6XYnRPbFGph20PyQ7JDtiqmdIe+BBswZ2qv9hf7C/wBhf7CtcAh3foB+/Oq0QNVIIly5XyzPCFEwU4EGh25j3nUTvcO3gLZcCe08TeDFvfh9q0PJh2f5h2+Df2DJRJJcod988IQThT22h+9w7N0QCTBSb1fg6XYnRPbFGph20PyQ7JDtF3EBhd4s/DQOEsC1u79AP356FqQNwQGh3wh25iDZsAt9jvbR3uHbws8YBdmdRCmAAjHKkugHLK18H2rQ8mHZ/mHb4OhhCKmSHiedHB4Y4uCgCChGuqb39Z4Ol2J0T2xRqYdtD8kOyROPsS4hXxHSVCJxyEA57ZQK2IgOYrXmv8heCEkaeYu79AP351WihqJhUQUQgjdAPIEXzzgQ7cxKWFPOVDUeJX/CHe4dvB5CmaEIkGIQERiN0CYv9lwhwKQMMX5jH7VoeTDs/wAw7fBt7hkKiAiMRuhlcgRrOOBo5Dgg7rZUMAbMFMQDSAJLBV5r63wdIyZWAwAsgIJsCFJQKJUmCAl6yAHsgGSO7XYopK2yaRvoZQMzsEAstiUK2yNd8a/y1S0PvFKRdEgkgZAFGI0D5SW6BkMxnGUBZuSI0GuIJjEuTeEqrGcJr9ae/WqSfuhwaW0TkckboHuiOqNwKscBAVTFIUDoGOwCPdguLuQv8tfjgnwLLghoDARLXJNP2TyJYBdAK7kER1QAUIIiRP4SuW4TX609+tUkvdAzS2iB4GiGpNWhAWsaCnJBz9d4P7OWaQIAMPX+
D+y2sLM/wPg/sQoAiKllsX8G+gsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLAsCwLGrohDnK2QTf/AHV0RYM3LIFw46bwCuWCgg5AKSFmxTxdAhgBP9wBzvsBUcuPyKn0TTN8KM7dIoCAIpPzCY8cXRXiES4zLIh29vdT9jxupOTIY3nnZOlrQcqtXEKpvGwiZKkdqU9v7cEKh9k0XJKpJjeTyhaGBkjmCsICGQF06+gIsN+AiwYQPfFumsTwRsRgEDibPJQcTfBATlSnJTY9oAcLHpDclzujLb6LqdUNBy4CE0wNgEAj24Qy0DRyXxuhiI4IcdAosQAJH9FJmwbosaavUCYjAbqnVmlkngGgGVNAHCbzB1NEdXUB7ehO8KQAN8yXNDoTk5Kc7IsAEg3CnI+xHOMFEZ5qjK/9iQjVphyTsqagU4CDMtkcIi8sKnZW7oTCL8SByHITg222IkfEVGkLgTewU1MBSCMwQ9mNLIHYkY52d7/8QBfBrAJIsAimYO+6nBwbmbf1LGAE9BsN/wDUAUTB+FtpICdgEk2UtAgSU8kktmTbNweQgVVojSEJv6hozOZiRzdARMWdxt6kmyDPK3w1y6h3TySgUG4sggGgxT0yD+AbgvNUHAWZ5VMQNMiJsiSPApU5Q6KByiDf9QTbgsCJEOD8U3DsTPCGgwAYIJicllXxpvd/1R9MA+AqSQKc/dJS8eUTDA3KY/3NJSTZtxUqXIknUy/zlMZd9tzZXPqoMoi7hpKVFw8BCHswjkojmck5g9nGookAy7J76IgAJUoxtZAAABgPUlvkCPkoSLAQZ5/gqOUd0LPjaU28vlZcJhbcyvdrISjgJBbIk3egq0CSwSKPpyUTv5EwNQlDMt2EAHBx
F12nOibBHMSADkoCyI5CIgwFs6e6OEGEcGAVwAXVB8E6l5LVZd4wp3Xbw8A75kgZCAMieUYcBcIlk4C4PAqDjmOggDksjLHGxhNv8IRhgeZtZJtVIuTcwm1woTxjyIxDRIYmxu5I0hlqtAc4xlTIQWgylTL5gOyTyntt2HdCFnAWdF+EAEJmEMRsGXTheGgTA5VLZidgj3OZuTQQTIIHZEFJNCgqYgU8QflAQSEju6KtQZclcn7OEICcAGG4RqHgAKSlH2T6wpzpgqbXbgclAwbDT3KktwZgSwdfNEplHO5jf8CdhEHFTuj8vgYTVylCfspdSO25TyzHHfwEZafzQJeczKlumZRRV3s2X1BN0eTbVbBH+YTL1CdmN8Y7r/VT0B3Ovt8RpyxghH8iYfd3CsMJZTk3NJN3AhbkBg1yvJApM32RKX2e6TtwI7xjAE6yQTKBHSn66ZOJqJ3KpCTso+47QhMxx5UjLBk8hRFuJkngvcpN0ZvYdFqDiQRyVNHF0Bshk4WHRiW2VzgJurgAA/KOtQodxgVgggWht1QhJoA3KIck3aVAEBy2MPJUlxqEJmO8BwgmaY2Rwz8AyiQpMmknebyNkUAWOf8AhFnsDlG9A38AIKsZl09tOdi5RMYkVJMEAARn9jdbQ52/6p5POZ4CHgBYC5mPYBA4mA3LuueJIMr+CCZ8h5EE8CkkfY+xz/whPwLfcFQEbhmCfIThAgQARQGZCsMOFLOI8kOZgKhACcR7aIIAAwCzxghkvoJ1oBnTEXOZFvocvwi74sVhU0Zdgzjcp21ADd5inhM+SuQkzW5Eb9kD7SDbmAhuVYImkwByHKJ7SlD6ZlbWdsKuhDz2CAzxBUZxQS27J/XG6HYWfsE+moJArdOt5fMhcy9QFwzO/YT4JFwjIuQ/O8Qyp2TT5QcEt8h3wnrYCqvkjg8KITMCx9nQa0clym+xQyyEgAingsBmcIs+LD6oinjQJWpq+PCAgSC9xB8J0QB8
EDkQILb7dyhrCu55RymADpubqWCLtV0KqAvcUGKouHKnARmJ3JS4JQmSA2WdqH/hNinMA3K2gzmszTsEclYIJBckDYfyUtegYTgBAnBA00lUU4Gh85tRAkw9h7crcCxL7kp9JclVz5BzCXm+VcE3/E3um78i8skUVmeZuUCwBtvLMGi2PYgFr0wqyOVvKH6Ipv8ArRjtKBcrfE8IR7JBlZSB/ARMBY+hQR17hHA2IR7CN+VJdaTTOJlu1dB5O/CkC9NinA1LqaOC4cIRgy/QoURBtahNvnSQuQixRBsje/hcsIvyUDDvDkaSygE2e5REg1JoBQVPkpNuwfhMGfdozwgZGYpjNrsunnmqCPBTcSW/67DdGzMhr+i4x+O6JdCjihmJMoBg3iZlARwGYn8o9zzZAUwTbgcM5U9xDOifgEhvKKlfy2t6lvAi6JS7EJbAIuAHvACbDvJmbgKa24BAWLDyv8RPEw4U2gmPBQyDTcR4igYgBA8UoIcLZD3QAFIEABwU37NLJiaYhzACDd3RzJjkLZGohTELIEDLFbIJl8Lok4B7zQiFQoQCBupinBO8w4RsMS2hYAgkHBE0zbNLQABBDgoDNONkAGAYQLgQUSYIOCj7gRjbUuUSeg8kOuJud4mQxB2QFghBwLOUMAADZSY7CCgDBMwGWRtwPzoCskL2z3XOsl0CYTA07BUBA2AcoAAAMBD30wRd3ZFDwABaBm9PZ0ziEHSrMw6AmAlZlX7YoRHIUbZEXd5FEAJNBBQMABQdOniAg4TpEP6fSXFvflUnw/iG2w7kBFdzyeoQEBrygxMD+nuDiH4TGzcf/Ql//8QALRABAAIABQMEAgIDAQEBAQAAAQARECExUWEgQZEwQHGhgbHw8VBgwdHhoHD/2gAIAQEAAT8Q/wDwqWbxHUf6/wB3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3MaRv/BXBsyQyjRtw
+IEyJ8xl8nxOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdOdHy+R/gWuWpdtiW6MB1VTQmV/sP3X+AYq1n2IsoxzB+RhT/ZPuvfMoQxo5IzbbtACgA/wSwRLXYcy9/9D+696wBbO0buwy9e9/wmtOFHeKva+JwmhIfH+hfde8ZQn5pmVXV3f8IxFuInzhVu6OMn+g/de8Yzv0ypmvd/wtXuYlKpVtcF9oCOyTuyacB/oP3Xu2UYX9RX+FWp23dC7Osj+Uhp/oH3XumJdImgmOX+GYCrQFxnO+OhLug/yZMND/QPuvdM2884Bex/huPQ/Tqa0tF8jJ/0D7r3LGz2I2ply/w6l3DqQm2+7/0D7r3LPS1W4W0Hv7hVP3QRZSLBMPhT9MA3iqu3UFOXHuAwANVjFObMssY2GgAREdpf+Y+69wzOAf8AMPfsKYs0sMHjVW3FkpDvfoZUASiskCCixMHO3P7dRGbs+c4d4ijGwdsuLuHmcbEPmI2wjl6ZV/OtlqmQ2NrZDIER0T/Ffde4bh4ZS/u+zuWy2D6Rx2CJuMbOht7vpetTWA/xrEhELQjAY1DoDu/mpVMtEp+BU/dAVE1bs9OlofWPYawtaEAbHpONdkSkqquL8cpJNkLE/wAT917hq6dwJVvl59e2aHthtib85kmWRO2vgEWmBjIBRssBI7UQFhVMO9dIiIrAjOxfDhQdyb9iIiiUnSAFVzEWO0RMnB0ToGXZ85Xy0DuuxP1xMdT5YPx6QGsh/CPNaV+elWtrXyf4n7r3DK35oaA2PVWWv0P+WIhXxUVW1V9D+f3OuZPcA4MUto5h+nUx52yZbfwdnsykwgMDLVAA7rBo
ISYlRRE/7PU1AvZ3wAPS1VgHULXb3Vq3UWOlR2FiOsnWnERGaPp/de4b+GasPUF0tq5ARNjTdPpv8fuelPgqoK42fZZrxyBXS+dRzR1qnRwlHYx7u8ud7N3pIAKvYjT3a74GsCgPTAoZVTPW4eltWX8MDNFTGugIjyxI2O47nsh7RHg1jIi8KWpixRNSO3xf9k7eh+F6f3XuG+1B6Z2BWroBEzq8j931P4/c9SfFcpdZdo2x0z94f8NRDWAl4CEcX5yYldpGXSA0CWE0qtYA1pR2j8YjkTDYyvTfnxXq5NH5Yx3f2wswOKTsFygTZrrAAAUBgz7b2wW3FqhSdmUM991FL22LUwhdopiGyen+69w334PSWLoMzR+vV/j9zrn+EvpUe4QIgj2ZfCfiak+4dP23tgZetWsASdbkYJegFxuoATuiE7Xp/wDde5bj0ta7Kbwv5Ury9ZjYwXCxP8ofX/AJlTVp/gM+uMxaCKgS1Oudz3NUZR3wiPtSwkGT85TjHs1Bx1z0TVP6fP6fP6fP6fNfkC00QUowO4xe+LUkZ/T5/T5/T5/T5bS7sggASxLHoVV2QDbP6fP6fP6fP6fFXXpSuW7xdHtP6fP6fP6fP6fGJp5GVyymTbP6/LQbgvpK9WCpn9Pn9Pn9PiRJdZgAFWg7y/Gv2MaZ/mb2I9us0DZYR+X0qx/T5/T5/T5/T4953NSXZOx7juS3czagPP8AcI/mroep917luPSZF1olzU0ImzXsPVm0BVoo6RSfm0LEWghDRH0bQNQvqW/my6BbByfSpGfvMSfzG7pYh9OwV3hsID4NXuu7EqbSJBcdmX1Vw1Ruryv2r/uvctx6Kx3dmH8dQPaeYAAAAe1n/wAly+ilBBEsSo9fQv8AJ02g5AxENydfiK1avSrtS2w/nd3SxD6VmU4AgQyLniMurdatX3i/uvctx6NQV7ER3v8Av6hqaBfMfbTRWkEYLtTv59GUdlp6fOugDwGDZhSzRum4
Zkf4NYI1AURn87u6WIfSsHU7m7Y5Q3hSlsvvV/de5bj0ckZ2266SW0c/MBQHt5um+jlQi++HzBUpA9NvN1+NwpVy+3pWB4bP53d0sQ+jZpxkfmA1RR+Iy/Lv5empN9NRhmefhAaPxzXnu/KUQIjSPtV/de5bj0RcY0Wz6RR0GYND15ub/j4QqO7F4H0LKaoQFMDk6fyC9JUVfrJ9OSjt+lmgsixHOo9CEC1aIAGpf5MGfzu7pYhk578HscIgLW+RV9LUbpokkXnIqR1KEV4JqGp/L0XwWqGFAdBTTCHtS/uvctx6IESIn2/b0pQjSNk0/gXh7nrzRAzasGHV7q8+uhUVOtK/t02m0/x8sym19p05FXCMGfzu7pYh9KoTb3+o6TSUp8y9JAWGgUjqPtF/de5bj0ibah6mdN7b9P8AAz4oVAn2gTcZ3zAOTs9A0jKdtT9HpsirLfDFn87u6WIfSrN9f9fSxRpkeOlGt3yix7SX917luPS3b+qFQjSNjO2LewwAAiWJ/gJ8UKivK3o8ZbDB80EfjFn87u6WIcJ9CXLsb6UYDb9AvSpHRB8vtV/de5bj0tAdT4ZUcYjrrRpwgH5ImZhBLR34wn20j7mDK9ekH3U+KFQfdF/MIikSkaT0KTZGugM/nd3SxD6UF2skl8JU1cw/Kroo52m06FQFWB7b37Ul/de5bj0mNpuoG3qv4/c91PihUJMrFegFpFB+XoM/nd3SxD6UXDrEIUK349NDZ6Gsh2dQRnvNBLwi69/5fbL+69y3HpkFFjkkeatb6h/H7nu58UKijV3H5ddgliIDoZ/O7uliH05uOew9hHxLCe9X917luPUMgKKRixv4do8cDSJSel/H7nu58UKg3bSvzDtzXqVEz6bGfzu7pYhwn0cowXpHmbY4ZFI6nvF/de5bj1Unw7cmGaBfGUZVqFJ6H8/udcww77MUKiuuRXwdJ12YOWaZ4dJn8Tu69PVn7DoRg9HxYMdu9z4fdv8A
uvctx66SlNrKlpL5b4aT996NK+Kxgz/5k0svlCCWYL1muRHEHacy+Vc4RLTKJaZRLTKJaZdGWxXE07ssiJaZRLTKJaZRLTIHcnRCFqBSFVARRLTKJaZRLTKJbKliZrvXUjaLEGoxDtlEtMolplEtM0pjMdQy1tLIES0yiWmUS0yiWmWq1bdVgyv/AEYwiWmUS0yiWmUS0ysjZ2qupmN1CyLK0tO38Ig6ZJ0/PMElUbzGWrNCJaZRLTKJaZRLTLMFE5K19h917luPZ1KZTK/0Cvcfde5bj/bvuvctx/t33XuW4/277r3LceqNc9ph4N82E6lWoLYOgFqcj1huX75FkHenZ0O9V+vgAh1I3G+kRUAd2C/jVYFLET/BOLkZBqtiOU9pS5aVkPR7E97917luPVyejcVfQgeU0nRb7evFrq9rYep2k6UaL90sFVHtdkL+DJubmLGU1tgWwrA3LZc3zG49Nj+T/AvqrnEcqiRuwuF+W3JCO2JOgukCCNj7f7r3LceqF5ZmMDllfOuKo0v4Caq7fyxKplrfTTump+JrkeYxCrQiYYV60x0CluX+ANlweo6FqxCpoG2gZE1MQ/NMuhNqZb7d917luPUZlZEbvSOWqKr3XF3rmoxu61WOCgC1aCGDQvTxZUl/H0K2VZ4CGBpbpPRI7L/gL/zMpihBatBDPLYrZidK9qSx5FX2/wB17luPU0YPHgzW4xDIwe8qwN3sS0I9Y0t6tc+mIEgj2CW/WaGxxTx4Q3ZR6kwV2LqO70i9FyPdcTjrX2BKmWs91u+/JoOTwTTmqOwxJLujz0W1jL65NsArONSUUPb1N+n917luPT1518nsS3ktuXApbEDlmios91jnqvfLGCC1aCCuUX52PwpK5ytn4YVqTVHOlpfRr91/54nLIADusNQJmAZvtOO2YAIYJ3YpcQrjUXLwr81Bzxoqzruc84RrUbZnQtEYSq2R60AqgEaYGUdQRcMumcgzei+GN6gwHdZq
PWPd6uL/AG2hWnNgvraEGUJ8MvoOUNU9glT+qZ1mdZ090DSASCJYno/de5bj073asZQNoi5co4qYU1KKvdcaOxQ/1wCsK1WgIx+M74bDW1LcRRsai4X1rnjpi5DwEuaGnzjnotXPHqpUHdS3o5OXEtUbEARPQ7rYI2j8wxFUXdwEb7ta+SAhKfjq5rtJipCarAZRojTAani+5hQ+dDuticvA4EC9ERFsO3p7z4Hq7Et77b4jUFfiOlxkW4532XzWL1UJe0IKKuqwEUaI0zT2rox7CtXICKHGzJF8lUUsD8pZ5DNLSfIsXjprfhDy2PuULjKvEDLm8boywlqj0X3XuW49IEx5j2COJb1icQZVgPkcP9WZGR78JYhcEJUEHsJVdAInF/56gqE1IqKyniYU13y8Vr4ocs5CtutXqtt0l7nsNWVoADYMKQGV3NonxJr8I6U8KETUSVR8Y6eZACxE6E7FW+MZH2ca3UTDb49o4tIGQ7JGfP8AgsU50y7mwSyevwHY9B5gwHdYC9bFu9cTXWfGSbaevnEQqgBaym6tLf05DbSZVH78oy5vtxj6TnwXpjwj92uVQ4rpYybY8zKv0T7r3LcegtQA7W/DKHayR6Wa1X/zMH2TDu9AMZ8XO+85i7xbjZj8yKXaKvLjsLeLdsy0buJqWJuuOUdk/HgPNpHsEVXSw6GvcyGUvzaHUY3Ld1bhGdptuYMxk6/ERVv7xxS4lopIoTdUtehCC1aCP/8AOYwCEJVewR6pHTGQAVWgIMBTeQ7sQW+hnbfe4vzSV+IiVuMMnYuMhc30ui3T/BZxpIVgKIjSRrD6pJR0Hgt2g5a6goWXUy3cC6Srlf6sej917luPQFibkcG0+HM6SrO4aCaebERA0iUj0V5pZXwteSBFVtXBxqwAtVgS9c1RoACghzauNDfSprGPKWxA5Ydmdj3WKveZ+WPlBWwzWUFEDYML3c7d6GJ3V70j0CgKIV3p0dnjDDxlKejrHLoi
Yd2bm8QVA1YUwNoaMC4aA+RxDWwAbrBjpst1iA/+L46Eq3tEqpABhdIb9xDvFkaq4hXdXvyLPzQQsnmR0FxQsPmwhESyaIfNbgB6X3XuW49BlNd3ccqCwjX22YFQHwR8M5C2X85K8XqruzAgKAoi3YjilnfTYpurHiySWlBe7cnlxu7RX+cMLeUcpVVW3GmWbwwAtaCIKm1cbbPI8m0NAAUBgC/ymWI3iOOyQ4gQHYMMwy7HQgVWgIO9OZtg6y/AdFwqw1sBfymPjQR7riT3WgYGJcuYl7cx4zWEewS8A07AxKnzOM7YBkLKewQdqfjmPeESFAoCgMDefQWNLep5ntAV6v3XuW49BQqBaxu6o+LIxH+NBxWtzXRmSXaaURipQfvnx3h10IBsYKVtN1oRoLcnlwLGwhurHbyC/DogQUWGNAK0zu9j/wCziEllDlamjmiExSR7BOAJtYylsQOWD8XYt3rhSaWoomd+B2MAVohgAB91geWu/wDaFVtxYW1ou+A3mX34kDX4QgAA0hABqNAInCX8TEFAFq0Eo5ls762C1CzRhgKgRpFF+TAYVoC1ZZZl+fiY6x95xuPlzEA9nH5gDgAXd7uBXUUdgloIVbToYEkqAB3WU/Ll+7wb+3TvuNurz0/3XuW49BndP0TDK/M82Cyl2/xnoZZVRIU3FmMLXbsEAq0BNZ646iH7XpGsLx1Fwu55jAoog/yzvxv/ANHoMZa8NYcl/roVtsv4MBZAAtWBvVPRKsAAJS6D5HsRN9cu1tY6F8EIShwWemr8RtbWxE5V9TGiA7I/ndADl2H3xu2nTxtIU3zDIg2QtVLrvce/RDvvk+C4Aq/YY4xZHuuNStfGTX1s+7iMzZLwr/jQ7rYjA1o7JjZvtenPuvctx6Cdm8t4vqLz6oY1AA4MB4SfLERarbjw/YAGgARuB++Izy1Pmgpqsi2weXfxAqBBUaRJhbUvlxXxsV3insItrMrxcvQ6W4bpUawpq3WR
qr0a2W/ji2dlmsHAt0LwUFUfi+GUju0dhsHQYvI8NR9QugFgYAuxHONr3+k+wF2XHQvwz9d8Im91pquLG0IfJDiimdsWHp2OQ6R1jPgumUu0PQRWCbV0UIErRYDl2qIL3t/zhVVcXgt6xBZ7CXFlQaWhgg6tuFeoI9P7r3LcdbGuJtuk8a4qbo4uAuBzsH0Ox6+bJ8qR8nQKNjTFVtcP+Vfw9sPfX8H299VdOfN7Dus17J8js9FvrWeXq9huxXH94gpCqI2U9Io2NMtYl8oDVWAKiN+pfde5bj0H7ffOPZDFAjYBQiKOLp3+CXy3yJ1iCUDtoImgCClbf1GIgzmA2DN1JHXkb4mQm6Z851ACgywXYO7oBQAqtASgo11tHQQwcuO18OknZ9ASq5GuSWsH/N0k7voCPw7XpaUjWicQ90YNB6+3QIGKBqrCTBqZoIiZMMEo28w6FJnUBiLaSehJ5UAFqsDoP8H1fuvctx6C2x8PwtZO9WMHUJdgik2lLsegmDU/7wdcaiEKKTQC5SdYGMD7df8A1hSFtIKcUQBV7EFd5ZB98xd3ADFgplqK56NSVd7QhY2699wnc8/AqVXvMOyYoAKugR1/W7siLkACgCBLJSM3pTNZcsGjt1ew3WFEAs+ZG157RoxhpOT2G5gjVFAFrLRrqwK/rzGidcAKgFqzUcB8hgcewTtTlJFUiOBobuHQlYNbP1cBTDQOiMdsjdGcUaQdmAuRHBO+VEyfFvoLkJEe4x+ZW/8Aliazv95dvwBgbVlrs7kHE7MycDm6dA+WWmDVscYrutaciBA0jgidd+wbsrJCxHOmhgAKm8M7BwCBFTmvb1j7r3LceiPQGyXOcaAPVUkbF+0+OaI9IFSxuyfctPmlUd+C3pc66gXE17+CZ/alMR+EHi2gVjRE4Ahk7QMcOIGVX5wPS4F+Shxcdkft8MePbiVk4yX6OOFzvUFjHV5s5Sdm7enO2d0wYO/lDwAaAUHSxU90XLcr
4soMWL4zBZFo8MA4Y93cAAAAOh4JNRLJbkwUe6AB0osRl7VbMmA/sMWvbkYzAVQXd7vr/de5bj/bvuvctx/t33XuA+/B/typuU1/n26BFjjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjzjxBBVJk3l9v+h/uH4d7f9D/b+ZGAAHb2/wCh6SbIFtdpnS1kGl/lqpWSYrb+MIVRPk/aOvqrye7og/PATYHxOrlgj6tbPl7j9D3PRyrqy+7/AIVYqapr2jTdTFzMGh8ziA/r/FT5VuWvcfoe46JJkrN2y1bf8IvGIUYnZS1Z/GbYnWBSOBDolpAr/Ez4EA1WGD2Pcfoet0UK1Ex9SfGXxKLKS1P6sFSVbGoyI/gGGnXIGJKI5W4x9S7XD+umXgzIxYD7W67EXfILDNo/GUW7exGHhbTu9ASNGVgftioS5rsjVjhY7FpSpMIDCrv/AGMVu5ipO4Zj1hd8WFsWuYsm3UkMSWxMQfWyWaWXYJm3JgpNpW4ryxeDLQbto1FqyNjYwqbSLNpW5waMMu5pZP66f10B8GLWJbN9wo6nniKsksydiYAV9JiII2OJob7TqGGX4tlRAEetiMj7tg65m9kShAUlsTELypXaaWHYJmyYLBtI8T6Pf7GfLJaft7n9D1+jb7zHRoyROSlV1VnctiQK7SLQtXRAl0S5QMf7TA3MKqMFkq4xUstO2ICtEM1zvX7QwXWlmThqbQtv1j/C7xiNREj/ANWDPbA4fdME2tPiIE0HECN4jfCCFs7ccLt/G2PFBqJWFsSsvfBQTQIkhFIiJqOAZESxFFCmaysD
2AaIoqQGo4fY/vDNJTLeIldtrhfchxnQVgG6rU9nF9b9OGbNBLsBBNBxZqyfgYn8ayANn7jhdv42xosNRKcL4Gm7+vnwFAasHyvz7n9D1+jb7zgozntARYY2IqaZKxgxusF/PbT7LpV5rwyaoqHfC5gHh3JqNl+VUa+kmGPoCfnD+F3wZC6ndQUO/g1j7yxkrVh90wL++1APtQAhkmAGBE7s8mK9GgoKYIaglAlMs7jg+xFj/B74fxm3UT7H94II6twBCoouAmGRZBlUpGMLQEhD9pw+t+nAb11zdhnagENjIAKEcFZuTHX1AQUvbBLUEHhaMmzgnYBPrz5Y3NNPdfoev0bfecAM0F4MBr6Bv57afZdLbdcKobVKnD8ThQ6FuTlwZjgfwu+AkCkr6PumBlu+BHA+rxLMesEWo+Ejqm+VFia7Krpp/B74fxm3UT7H94Ip7PwwJuNEduecE+awfW/TgZ3uAN/wMmH1GIhB67Qq0vwkStfyouNVqKrD7j60+IQ07sAgUHuv0PX6NvvOH0eGnojfz20+y6WH/jMLfd2bpNa1Lt6sfIYfwu+H1fR90w/g98PpmH1eCN15NiIiq2sH1HdNBCX1SUHn1PTT+D3w/jNuon2P7wS73TAx7jibzsjxh8pVh9b9OGng/S/p0QVTSqYiRVbVnzRJoIW+mSqARQu6GH3X1Z+MBawSGvd93+h6/Rt95w+jw09EYWW7hotngm91Xw9zFwBkr+TA89gnfoEdHH+F3w+j6PumH8Hvh9Mw+r6LUCD+D+Z36aOVwbKFmGz1CfY/vBwrkfJhl1Ww29pzHXta+WIA1WibukOH1v04aeD9L+nRD4umCyBhn1f1YfdcKB/CsOD1jS2JHh6wUAWsKxzfvP0PX6NvvOH0eGnpjNspz7jhf1zqGkHxAFCicYj5mVsub2EcqukcDDh7QY0WqW3/AIRStsx/hd8Po+j7ph/B74fTMPq8BJvyREUiUjSRs7WBpJUF5oqE
KLOkoaTJsecEy51pn64B+kVRtc/PQT7H94BvBInJVIx8brBqQyC7sVBk2zKwZqbxPrfpw08H6X9OiCnbFTEUiUjSRwLrA0kFDypLeNUwPuuBErAj8METN65OzgX7MmoQzXVnpBBawxqfr739D0htl2pn/wDOPEtRcCiNVEsbXj1QlA6gMEjPN1M+vrgNhFsaEUNPUiCjtsKqfHEChbwuqgP4MKjQDI1I+oedG6M/NgmnkxUPmJUoj5yuNFqkIRejxxQAgnQq7ChEqPHEtEquXBBJCgitPjl9sSYl2bWDaAb5yOq8mDgPJjpTN8kyJclcb+60e4xMvwYdqNko+WHHVdweGNCO0TfymJjcj4vf5jrn+RiOvkxSk/NIwGQBvNQGJhOqPgQVR44aZfU4LZBoPmK14cF+NVxLs+sH20d88dV5MHUeTHSib5IEvt4n1y1QTHLSK0eONz2lD0VQwPLPufffof7PRQBAAe//AEP9loGuRmda7n/A/of7FryykqEqUs7sr/BV0KqcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODynB5Tg8pweU4PKcHlODyn8zCQLuICrSCsmAP8A+BVKlf7k478KgkgjonqEBagRlETsDKYXdaIvwkKy2K41PNQRT/b1moxN1Mzr9yqgsbPihcsOtiv0mpMRWgifi1fojUmzvB5u15uifTqXRB3n3KasvKc08MC4I5xQ/Yy9YCvafLgGRbzcYKuLlfCD/bGNWASFKW+0N2Gf2tQW
WNl0YIXoiMXhfdVCT1oS2eW3J2bIgtCWQHfVAQrPlLsQsltIDMH8Sg25EjC1K+1Qe3S+e8olXLachGFuf/aHbN0B8PFGewWhE+UFbYIjSBKxeABaDPwIWwZDuPoJJqP4BtIN+6v1blvvBMaxQ1PugQIemUEVqyCZWYicu7C2Adg7oILQd0MJsNLYRzWMiWnSx+UcsSyL8jVZSUTI+241Bjo+iOXAOb9OnJ1Y5ajK0ZnfCTW5b9V7uXYlXzemSr5bjtfLV+R6hF3teJLGWLUzynLDbHOc0Ilwp3NwSGlO2JXqHy7RUpNb7TBpp287E+CIbCuh7CFRcLuDd/yGCyDw6zpCtWXDVpGSf8IGhCnsdv5MPcHg1rHddwHyw9pbLLcCIGxQls2u/DIMa8zSndlfBgoZCCPUTdgqFhjW6Au9GBWuuhg2YOXYodmpRRLjjw0mstvBEE9usXGwMvee4rmb+mWXjW2pA6AVNNIfLAAEYwaU6tF92DRukQe6Njf/AHiB/o4dKAq0BbCE59XBBt7Km3lNTglic6Am6ctR/cuV0LzSGeW6ltDA7jAdgglqM+2CgrVY2ptP+ELsWpfwkt3zhZTETkCoilxcm86lMEoh1IrQmQCBi4E7qAirNCSNF3lFzU1RJQqsNTZDimeRQ7yPt+DhNWoC9LYljHO0K8ATNM+u7+CXlaVAabCGFwYOA/dcd9klgI9l3ggVDIqbeWDEAAHYPc5bDmYEx0RCfLPDvNsFdYksKwmbosHHwvxlCf19RK39yIsNl3GLlNIBzpg0ZdqS2Ws7HQlfYCbDN8MFAIANViPIxTU1uS6OCDraoPw1i0LWCn8IXOKbIGDgyAAWrC6Holk0KQICxqK0JqREAalscAXdVLJONbDGk1N+rBp2lPnmlBAgRESxg9LbYuCstqU08xYJS7VvVgwyZiaJAsqBOBSAsthSF8G9DgQO6xVtMC4dn3UB9kGa7DAL3oG7uFrl1OB3Ylq5AYfduAr5
Z3bW/wC7ECNUFdRlEHuggvNqEwR5Q1YBUDVnesF+ScnG+ERvNsQ5ij8pVNksaFI2gpGDtbGE2ba9pusLaAl7oywYFJ+aXnAd9gXeR3WSqYOa4JZkk+Y00FhzqsXdHx5ugZPbs05w4J3UTOYjuuw2jL+jGKGcFBI75hAlh3YscfNELsps7IDNNKi1t5eds88BImgLWECFdVUFehx7/D2IMtgcucoZoj4ULx3hYoCi3cn5B5nsRod2CzgyBaBQIyPqoBqHJ108prP3+zdh2suP+nGPKsAEq7hgD3G6pI1jFUPYecO9EtbC7N2iNVjR+hGThBKOb8rGgZz2bwEid+fOZEEnciYXf/oTs9Bi1LzLFp3EQECsvaUVoTvA7bEqu2vk7R3U/wAB7sNzziRUqABqm8JduAM3jx2gr37JUCLrs7sFCd5SjyenqDWClU9saQ8sfTjU7qt2WSTa0qO7xA1lSugBLnrKwtzvmtNhFpoQozvGqGadYINsSHy4JaXNhrFffJPddB7AGKVerDNN1qj9iEkVlaFBlUy9gtWN+Q+SwYSM5IfCHZ52WAbRrs7wxorypXiZQIO3KxFrTb/Y4h19MtxDwzmCCp9xBIQUBV7GBzy/8hKQguC8wuTvzeI9pZGnYotV2CZ3w22macwNQZXjWThaxYw+lewwunVQicc/E1Meog1ShGQHeAlC2tP/AAhl+xKt+jP8ylhpRLTlZMAWz6NkEsHQPeVChPyQ/a0olAqsU1+YTNmiKQi4qAyAjUO+OkIl/aCSC4C84LRhzS89n7BGk2duo7sAwZWe6wipoyZxxHsgZt5x4AHbGzlBGsgclpSP/XDke4QtSXldw9S67A2imO41P3iiM9lQ+ClL9lmoKr60/wCsXJbTzdQAgyiKOHdloTYvVTDuuZTbQIwoDkk1u7K6RcbN92v+Er+FXy7vt2WEa15dSFu7yI/Ly3ZUy5qfr4bnDfgXm6qghGmBmfrSRlojUnWtGdKmm0Ip
zHVrH8Isc2B1RJ9S8PCATIMpOVfsGhEkXnZeTtBBAPxIumnuq7JorQpixnoBXtA/KWHi2DUSOwgzCKbfVbsKEsnghUclR7HH4Swre0sMb1uQ3kvZUUCn/uiMgrXNsJfhDdS+rlBwvotA/wCQ1oTUL4f00jdbCxbcYbWljaORyJ6sea7AmhyZRuzMV2QdGshtd2HEGeIgSelCioBbTyDJ/wBJSJQxZ0tWPR4VMQHK/wAXRgCjKG0d9oFjSbzdoDXBR/uWNc6bNBB0HyuwRWBt9/lNAQT1s/KLW9vWICqDjDGhH0N8OpyeZZmqUUc02JoZB29xli1ALuNsNXucyTSd3qz2Yn9XnklSB0PSBhkfhGqccXHIC8Ah7cds81/fJEDU95Z2ID8MrR26pGnbRKSQhGdoufKS+kuKjgWswbIz0aBQQfeYha0ascZYWcjcXFgp+xCpyFr33Ga64vysODTd6vYl3DIL3tcDLPpdazLoNIbpU4hKc3TuRlqb7js95eZsJysI2qIXcsWsPWlUXCZtQzePI9lmsbznGiudKNUa2GS92KZiYSviSvDs6cHBZYdxbwohpjHg9ycIWoLwK2Al1pVy+qFkzbwVntXJ3RqsahZB4hoFGD4wUJup/TYWCVbWriRpObULlJQGVTWprmsbSMzEEJLWoqHQpHSAiyPkEEAAHYwGKCkdGJoTsanwIWewVhkS7Bg+tGpF0QUhFkGbR0Lmc1boVK3SJepBUK2r3YC48XDKw0cw8wYMimaFaKZzXohVopL+7qRNWgiJX1lFDqhsQ8FIHRGZGwVjU+ECBGBSJYy9ld3/APCCwBoEoSN4iAYP3OoXBQyad6mrWKO5oS9sH8oo35s/JxIU2awWFfaKIGs1AKCOJxWmFBEyAogyboXUlP3czfL0ExB3YyxVsSyN/nRUAmwdLZSqAFABFgd7C4HYFAaGC+ruaYCUsIZmQKIg9onBtaEEB8Y4KnXYCBYgK0UyqZ76tiWEzv7b
yMFPzzYnwBavYIHeEhIAFB00SsKMU0q8zTUCej/T0gun2e6A4qof4dncOU20lXLq9SEfRYbtCHgB/p5NUCWqS9oztpVlv/6Ev//Z</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <Language>=User!Language</Language>
  <CodeModules>
    <CodeModule>System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</CodeModule>
  </CodeModules>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="Companyinfo__Picture_2_">
          <DataField>Companyinfo__Picture_2_</DataField>
        </Field>
        <Field Name="Item_No______">
          <DataField>Item_No______</DataField>
        </Field>
        <Field Name="Bin_Content__Item_No__">
          <DataField>Bin_Content__Item_No__</DataField>
        </Field>
        <Field Name="Bin_Content__Bin_Code_">
          <DataField>Bin_Content__Bin_Code_</DataField>
        </Field>
        <Field Name="Bin_Content_Location_Code">
          <DataField>Bin_Content_Location_Code</DataField>
        </Field>
        <Field Name="Bin_Content_Variant_Code">
          <DataField>Bin_Content_Variant_Code</DataField>
        </Field>
        <Field Name="Bin_Content_Unit_of_Measure_Code">
          <DataField>Bin_Content_Unit_of_Measure_Code</DataField>
        </Field>
        <Field Name="Companyinfo__Picture_2__Control1000000000">
          <DataField>Companyinfo__Picture_2__Control1000000000</DataField>
        </Field>
        <Field Name="Code____">
          <DataField>Code____</DataField>
        </Field>
        <Field Name="Bin_Code">
          <DataField>Bin_Code</DataField>
        </Field>
        <Field Name="Bin_Location_Code">
          <DataField>Bin_Location_Code</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>