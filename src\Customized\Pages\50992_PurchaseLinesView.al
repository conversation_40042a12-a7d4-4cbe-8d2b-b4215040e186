page 50992 PurchLinesView
{

    SourceTable = "Purchase Line";
    Caption = 'Purchase Line List View';//PKONJ25 Full Object
    PageType = List;
    Insertallowed = false;
    ModifyAllowed = false;
    DeleteAllowed = false;
    ApplicationArea = all;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(Control)
            {
                field(OrStatus; OrStatus)
                {
                    ApplicationArea = All;
                    Caption = 'Order Status';
                }
                field(PurchS; Purch.Status)
                {
                    ApplicationArea = All;
                    Caption = 'Approval Status';
                }
                field(Purch; Purch."Buy-from Vendor No.")
                {
                    ApplicationArea = All;
                    Caption = 'Vendor No.';
                }
                field(PurchName; Purch."Buy-from Vendor Name")
                {
                    ApplicationArea = All;
                    Caption = 'Vendor Name';
                }
                field("Order Date"; "Order Date")
                {
                    ApplicationArea = All;
                }
                field("Outstanding Amount (LCY)"; "Outstanding Amount (LCY)")
                {
                    ApplicationArea = All;
                }
                field("Document Type"; "Document Type")
                {
                    ApplicationArea = All;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;
                }
                field("Line No."; "Line No.")
                {
                    ApplicationArea = All;
                }
                field(Type; Type)
                {
                    ApplicationArea = All;
                }
                field("Item Category Code"; "Item Category Code")
                {
                    ApplicationArea = All;
                }
                field("No."; "No.")
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
                field("Description 2"; "Description 2")
                {
                    ApplicationArea = All;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                }
                field("Quantity Received"; "Quantity Received")
                {
                    ApplicationArea = All;
                }
                field("Quantity Invoiced"; "Quantity Invoiced")
                {
                    ApplicationArea = All;
                }
                field("Unit Cost (LCY)"; "Unit Cost (LCY)")
                {
                    ApplicationArea = All;
                }
                field("Outstanding Quantity"; "Outstanding Quantity")
                {
                    ApplicationArea = All;
                }
                field("Qty. Rcd. Not Invoiced"; "Qty. Rcd. Not Invoiced")
                {
                    ApplicationArea = All;
                }
                field("Sub Document Type"; "Sub Document Type")
                {
                    ApplicationArea = All;
                }
                field("Sub Document No."; "Sub Document No.")
                {
                    ApplicationArea = All;
                }
                field("Sub Document Line No."; "Sub Document Line No.")
                {
                    ApplicationArea = All;
                }

                field("Amount Including VAT"; "Amount Including VAT")
                {
                    ApplicationArea = All;
                }
                field("Capex No."; "Capex No.")
                {
                    ApplicationArea = All;
                }
                field("Capex Line No."; "Capex Line No.")
                {
                    ApplicationArea = All;
                }
                field("Purchase Order Tracking"; Purch."Purchase Order Tracking")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ActionName)
            {
                ApplicationArea = All;

                trigger OnAction()
                begin

                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        Orstatus := 'Open';
        if "Order Status" = "Order Status"::Cancelled then
            Orstatus := 'Cancelled'
        else
            if "Order Status" = "Order Status"::"Short Closed" then
                Orstatus := 'Short Closed';
        IF Purch.GET("Document Type", "Document No.") THEN;
    end;

    var
        Orstatus: text;
        Purch: Record "Purchase Header";
}