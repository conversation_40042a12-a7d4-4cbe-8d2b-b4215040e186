page 50417 "Channel Sales Team"
{
    // version Channel
    PageType = List;
    SourceTable = "Channel Sales Team and manage";
    UsageCategory = Tasks;
    ApplicationArea = All;


    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Channel No."; "Channel No.")
                {
                    Visible = false;
                    ApplicationArea = All;
                }
                field("Sales Team No."; "Sales Team No.")
                {
                    ApplicationArea = All;
                }
                field(Manager; Manager)
                {
                    ApplicationArea = All;
                }
                field(Type; Type)
                {
                    ApplicationArea = All;
                }
                field(Description; Description)
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
    }
}

