/// <summary>
/// Table Bank API Credentials (ID 50381).
/// </summary>
/// G2S Providus Integration 7th Aug 2024
table 50381 "Bank API Credentials"
{
    Caption = 'Bank API Credentials';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "No."; Integer)
        {
            AutoIncrement = true;
        }
        field(2; "Bank Account No."; Code[20])
        {
            Caption = 'Bank Account Code';
            TableRelation = "Bank Account";
            trigger OnValidate()
            var
                BankAcc: Record "Bank Account";
            begin
                BankAcc.SetRange("No.", Rec."Bank Account No.");
                If BankAcc.FINDFIRST() THEN begin
                    "Bank Account Number" := BankAcc."Bank Account No.";
                end;
            end;
        }
        field(3; Username; Blob)
        {
            Caption = 'Username';
            // Subtype = Json;

            trigger OnValidate()
            var
                myInt: Integer;
            begin
                AccessDetailsAvailable;
            end;
        }
        field(4; Password; Blob)
        {
            Caption = 'Password';
            //Subtype = Json;

            trigger OnValidate()
            var
                myInt: Integer;
            begin
                AccessDetailsAvailable;
            end;
        }
        field(5; "Base URL"; Blob)
        {
            Caption = 'Password';

            trigger OnValidate()
            var
                myInt: Integer;
            begin
                AccessDetailsAvailable;
            end;
        }

        field(6; "Date Created"; Date)
        {
            Caption = 'Date Created';
        }
        field(7; "DateTime Modified"; DateTime)
        {
            Caption = 'DateTime Modified';
        }
        field(8; "Date Modified"; Date)
        {
            Caption = 'Date Modified';
        }
        field(9; "DateTime Created"; DateTime)
        {
            Caption = 'DateTime Created';
        }
        field(10; "Created By"; Code[50])
        {
            Caption = 'Created By';
        }
        field(11; "Modified By"; Code[50])
        {
            Caption = 'Modified By';
        }
        field(12; "Access Details Provided"; Boolean)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(13; "Fund Trf. Endpoint"; Text[200])
        {
            caption = 'Inter Fund Trf. Endpoint';
            DataClassification = ToBeClassified;
        }
        field(14; "NIP Account Endpoint"; Text[200])
        {
            caption = 'Inter NIP Account Endpoint';
            DataClassification = ToBeClassified;

        }
        field(15; "NIP Txn. Status Endpoint"; Text[200])
        {
            Caption = 'Inter NIP Txn. Status Endpoint';
            DataClassification = ToBeClassified;
        }
        field(16; "Providus Bank?"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(17; "First Bank?"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(18; "Bank Account Number"; Code[20])
        {
            DataClassification = CustomerContent;
        }


        field(19; "Intra Fund Trf. Endpoint"; Text[200])
        {
            DataClassification = ToBeClassified;

        }
        field(20; "Intra NIP Account Endpoint"; Text[200])
        {
            DataClassification = ToBeClassified;

        }
        field(21; "Intra NIP Txn. Status Endpoint"; Text[200])
        {
            DataClassification = ToBeClassified;
        }
        field(22; "Currency Code"; Code[20])
        {
            DataClassification = ToBeClassified;
        }
        // field(23; "Bulk Payment"; Text[200])
        // {
        //     DataClassification = ToBeClassified;
        // }
        field(24; "Session Endpoint"; Text[200])
        {
            Caption = 'Session Endpoint';
            DataClassification = ToBeClassified;
        }
        field(25; "Zenith Bank?"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(26; "Company Code"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(27; "Inter Bank Payment Type"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(28; "Intra Bank Payment Type"; Text[200])
        {
            DataClassification = CustomerContent;
        }


    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        myInt: Integer;
    begin
        "Created By" := UserId;
        "Date Created" := Today;
        "DateTime Created" := CurrentDateTime;
    end;

    trigger OnModify()
    var
        myInt: Integer;
    begin
        "Modified By" := UserId;
        "Date Modified" := Today;
        "DateTime Modified" := CurrentDateTime;
    end;

    /// <summary>
    /// AccessDetailsAvailable.
    /// </summary>
    procedure AccessDetailsAvailable()
    var
        myInt: Integer;
    begin
        Rec.CalcFields(Username, Password, "Base Url");
        if Rec.Username.HasValue and Rec.Password.HasValue and Rec."Base Url".HasValue then begin
            Rec."Access Details Provided" := true;
            Rec.Modify(true);
        end;
    end;
}
/// Providus Integration 7th Aug 2024
