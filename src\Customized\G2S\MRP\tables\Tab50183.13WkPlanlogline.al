table 50283 "13Wk log line"
{
    Caption = '13Wk log line';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Document No"; Code[20])
        {
            Caption = 'Document No';
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
        }
        field(3; Description; Text[100])
        {
            Caption = 'Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup(Item.Description WHERE("No." = FIELD("Item No.")));
        }
        field(4; "Item Category"; Code[20])
        {
            Caption = 'Item Category';
        }
        field(5; "Line No."; Integer)
        {
            Caption = 'Line No.';
        }
        field(6; "Version"; Code[50])
        {
            Caption = 'Version';
        }
        field(7; Status; Enum ApprovalStatus)
        {
            Caption = 'Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Lookup("13Wk Header"."Approval Status" WHERE("No." = FIELD("Document No")));
        }
        field(8; "Week 1"; Decimal)
        {
            Caption = 'Week 1';
        }
        field(9; "Week 2"; Decimal)
        {
            Caption = 'Week 2';
        }
        field(10; "Week 3"; Decimal)
        {
            Caption = 'Week 3';
        }
        field(11; "Week 4"; Decimal)
        {
            Caption = 'Week 4';
        }
        field(12; "Week 5"; Decimal)
        {
            Caption = 'Week 5';
        }
        field(13; "Week 6"; Decimal)
        {
            Caption = 'Week 6';
        }
        field(14; "Week 7"; Decimal)
        {
            Caption = 'Week 7';
        }
        field(15; "Week 8"; Decimal)
        {
            Caption = 'Week 8';
        }
        field(16; "Week 9"; Decimal)
        {
            Caption = 'Week 9';
        }
        field(17; "Week 10"; Decimal)
        {
            Caption = 'Week 10';
        }
        field(18; "Week 11"; Decimal)
        {
            Caption = 'Week 11';
        }
        field(19; "Week 12"; Decimal)
        {
            Caption = 'Week 12';
        }
        field(20; "Week 13"; Decimal)
        {
            Caption = 'Week 13';
        }
        field(21; "Week 14"; Decimal)
        {
            Caption = 'Week 14';
        }
        field(22; "Week 15"; Decimal)
        {
            Caption = 'Week 15';
        }
        field(23; "Week 16"; Decimal)
        {
            Caption = 'Week 16';
        }
        field(24; "Week 17"; Decimal)
        {
            Caption = 'Week 17';
        }
        field(25; "Week 18"; Decimal)
        {
            Caption = 'Week 18';
        }
        field(26; "Week 19"; Decimal)
        {
            Caption = 'Week 19';
        }
        field(27; "Week 20"; Decimal)
        {
            Caption = 'Week 20';
        }
        field(28; "Week 21"; Decimal)
        {
            Caption = 'Week 21';
        }
        field(29; "Week 22"; Decimal)
        {
            Caption = 'Week 22';
        }
        field(30; "Week 23"; Decimal)
        {
            Caption = 'Week 23';
        }
        field(31; "Week 24"; Decimal)
        {
            Caption = 'Week 24';
        }
        field(32; "Week 25"; Decimal)
        {
            Caption = 'Week 25';
        }
        field(33; "Week 26"; Decimal)
        {
            Caption = 'Week 26';
        }
    }
    keys
    {
        key(PK; "Document No")
        {
            Clustered = true;
        }
    }
}
