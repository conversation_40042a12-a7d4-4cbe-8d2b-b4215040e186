codeunit 50013 "Scrap Item Tracking"
{
    trigger OnRun()
    begin

    end;

    var
        myInt: Integer;


    procedure CallItemTracking(VAR ScrapItems: Record "Scrap Items")
    var
        TrackingSpecification: Record "Tracking Specification";
        ItemTrackingForm: Page "Item Tracking Lines";
    begin
        InitTrackingSpecification(ScrapItems, TrackingSpecification);
        ItemTrackingForm.SetSourceSpec(TrackingSpecification, WorkDate());
        ItemTrackingForm.SetInbound(false);
        ItemTrackingForm.RUNMODAL;
    end;


    procedure InitTrackingSpecification(VAR ScrapItems: Record "Scrap Items"; VAR TrackingSpecification: Record "Tracking Specification")
    var
    begin
        TrackingSpecification.INIT;
        TrackingSpecification."Source Type" := DATABASE::"Scrap Items";
        WITH ScrapItems DO BEGIN
            TrackingSpecification."Item No." := "Scrap Item";
            TrackingSpecification."Location Code" := "Location Code";
            TrackingSpecification.Description := Description;
            TrackingSpecification."Variant Code" := "Variant Code";
            TrackingSpecification."Source Subtype" := 0;
            TrackingSpecification."Source ID" := "Warehouse Doc No.";
            TrackingSpecification."Source Batch Name" := 'DEFAULT';
            TrackingSpecification.Positive := true;
            TrackingSpecification."Source Prod. Order Line" := 0;
            TrackingSpecification."Source Ref. No." := "Line No.";
            TrackingSpecification."Quantity (Base)" := "Quantity(Base)";
            TrackingSpecification."Qty. to Invoice (Base)" := "Qty. to Receive(Base)";
            TrackingSpecification."Qty. to Invoice" := "Qty. to Receive";
            TrackingSpecification."Quantity Invoiced (Base)" := "Received Quantity(Base)";
            TrackingSpecification."Qty. per Unit of Measure" := "Qty. per Unit of Measure";
            TrackingSpecification."Bin Code" := "Bin Code";
            TrackingSpecification."Qty. to Handle (Base)" := "Qty. to Receive(Base)";
            TrackingSpecification."Quantity Handled (Base)" := "Received Quantity(Base)";
            TrackingSpecification."Qty. to Handle" := "Qty. to Receive";
        END;
    end;

    [EventSubscriber(ObjectType::Codeunit, 99000830, 'OnAfterSignFactor', '', false, false)]
    procedure ItemTrackSignFactor(ReservationEntry: Record "Reservation Entry"; var Sign: Integer)
    begin
        CASE ReservationEntry."Source Type" OF
            DATABASE::"Scrap Items":
                IF ReservationEntry."Source Subtype" IN [0] THEN
                    Sign := 1
                ELSE
                    Sign := -1;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, 99000830, 'OnAfterSignFactor', '', false, false)]
    procedure ItemTrackSignFactorDisposal(ReservationEntry: Record "Reservation Entry"; var Sign: Integer)
    begin
        CASE ReservationEntry."Source Type" OF
            DATABASE::"MDV Line":
                IF ReservationEntry."Source Subtype" IN [0] THEN
                    Sign := 1
                ELSE
                    Sign := -1;
        end;
    end;

    [EventSubscriber(ObjectType::page, 6510, 'OnAfterSetSourceSpec', '', false, false)]
    local procedure ScrapItemTracking(VAR TrackingSpecification: Record "Tracking Specification"; VAR CurrTrackingSpecification: Record "Tracking Specification"; VAR AvailabilityDate: Date; VAR BlockCommit: Boolean)
    begin
        IF TrackingSpecification."Source Type" = DATABASE::"Scrap Items" THEN
            CollectPostedScrapItemEntries(TrackingSpecification, CurrTrackingSpecification);
    end;

    procedure CollectPostedScrapItemEntries(TrackingSpecification: Record "Tracking Specification"; VAR TempTrackingSpecification: Record "Tracking Specification" temporary)
    var
        ItemEntryRelation: Record "Item Entry Relation";
        ItemLedgerEntry: Record "Item Ledger Entry";
    begin
        IF TrackingSpecification."Source Type" <> DATABASE::"Scrap Items" THEN
            EXIT;

        ItemEntryRelation.SETCURRENTKEY("Order No.", "Order Line No.");
        ItemEntryRelation.SETRANGE("Order No.", TrackingSpecification."Source ID");
        ItemEntryRelation.SETRANGE("Order Line No.", TrackingSpecification."Source Ref. No.");
        ItemEntryRelation.SETRANGE("Source Type", DATABASE::"Scrap Items");
        IF ItemEntryRelation.FIND('-') THEN
            REPEAT
                ItemLedgerEntry.GET(ItemEntryRelation."Item Entry No.");
                TempTrackingSpecification := TrackingSpecification;
                TempTrackingSpecification."Entry No." := ItemLedgerEntry."Entry No.";
                TempTrackingSpecification."Item No." := ItemLedgerEntry."Item No.";
                TempTrackingSpecification.CopyTrackingFromItemLedgEntry(ItemLedgerEntry);
                TempTrackingSpecification."Quantity (Base)" := ItemLedgerEntry.Quantity;
                TempTrackingSpecification."Quantity Handled (Base)" := ItemLedgerEntry.Quantity;
                TempTrackingSpecification."Quantity Invoiced (Base)" := ItemLedgerEntry.Quantity;
                TempTrackingSpecification."Qty. per Unit of Measure" := ItemLedgerEntry."Qty. per Unit of Measure";
                TempTrackingSpecification.InitQtyToShip;
                TempTrackingSpecification.INSERT;
            UNTIL ItemEntryRelation.NEXT = 0;
    end;

}