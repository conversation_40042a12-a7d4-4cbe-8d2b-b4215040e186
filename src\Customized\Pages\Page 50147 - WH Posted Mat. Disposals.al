page 50147 "WH Posted Mat. Disposals"
{
    ApplicationArea = all;
    CardPageID = "WH Posted Mat. Disposal";
    Editable = false;
    DeleteAllowed = false;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "MDV Header";
    SourceTableView = SORTING("MDV No.")
                      ORDER(Ascending)
                      WHERE(Posted = CONST(true));
    UsageCategory = Lists;

    layout
    {
        area(content)
        {
            repeater(Control1102152000)
            {
                field("MDV No."; "MDV No.")
                {
                    ApplicationArea = all;
                }
                field("Date of MDV"; "Date of MDV")
                {
                    ApplicationArea = all;
                }
                field("Manual MDV. No"; "Manual MDV. No")
                {
                    ApplicationArea = all;
                }/*
                field("Disposal Dept."; "Disposal Dept.")
                {
                    ApplicationArea = all;
                }
                field("Disposal Bus. Unit"; "Disposal Bus. Unit")
                {
                    ApplicationArea = all;
                }
                field("Indent Dept."; "Indent Dept.")
                 {
                     ApplicationArea = all;
                 }
                 field("Indent Bus. Unit"; "Indent Bus. Unit")
                 {
                     ApplicationArea = all;
                 }*/
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Approval Status"; "Approval Status")
                {
                    ApplicationArea = all;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                }
            }
        }
    }
}