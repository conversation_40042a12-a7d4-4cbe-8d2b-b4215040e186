tableextension 50035 FixedAssetTabExt5600 extends "Fixed Asset"
{
    fields
    {

        field(50000; "PMS No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = PMSManagement where("Approval Status" = filter(Released));//PMS-PK
            trigger OnValidate()
            begin
                //B2B
                PmsManegementRec.Reset();
                PmsManegementRec.SetRange("No.", "PMS No.");
                if PmsManegementRec.FindFirst() then begin
                    PmsManegementRec.TestField("PMS Card No.");
                    PmsManegementRec.Testfield("Vehicle Tank Capacity");
                    PmsManegementRec.Testfield("PMS Vehicle");
                    "PMS Card No." := PmsManegementRec."PMS Card No.";
                    "Vehicle Tank Capacity" := PmsManegementRec."Vehicle Tank Capacity";
                    "Fuel Type" := PmsManegementRec."Fuel Type";
                    //"PMS Card Weekly Limit" := PmsManegementRec."PMS Card Weekly Limit";
                    //"PMS CARD BAL." := PmsManegementRec."Balance Amount";
                    "PMS Vehicle" := PmsManegementRec."PMS Vehicle";
                    //B2B
                end else begin
                    Clear("PMS Card No.");
                    Clear("Vehicle Tank Capacity");
                    Clear("Fuel Type");
                    clear("Vehicle No.");
                    Clear("PMS Vehicle");
                    Clear("PMS No.");
                    "PMS Card Issued" := false;
                end;
                if ("PMS No." <> '') and ("PMS No." = xRec."PMS No.") then begin
                    FixedAssetGRec.Reset();
                    FixedAssetGRec.SetRange("PMS No.", "PMS No.");
                    if FixedAssetGRec.FindFirst() then
                        Error('Already PMS Card No is Used-%1', FixedAssetGRec."No.");
                end;
                IF FASetup.GET THEN
                    FASetup.TESTFIELD("PMS Dim Code");

                IF GLSetupRec.GET THEN BEGIN
                    GLSetupRec.TESTFIELD("Shortcut Dimension 4 Code");
                    Dimcodecaption := GLSetupRec."Shortcut Dimension 4 Code";
                END;
                /*
                IF NOT (("Fuel Type" = 0) or ("Fuel Type" = 1)) THEN
                    ERROR(Text024);
                */
                //CLEAR(dimcode);
                //CLEAR(dimname);
                //CLEAR(Dimcodecaption);

                IF "PMS No." <> '' THEN BEGIN
                    //delete default SAA3.0 17/1/2018>>
                    dimcode := FASetup."PMS Dim Code" + xRec."PMS No.";
                    dimdef.SETCURRENTKEY("Dimension Code");
                    dimdef.RESET;
                    dimdef.SETFILTER("Table ID", '%1', 5600);
                    dimdef.SETFILTER("No.", "No.");
                    dimdef.SETFILTER("Dimension Code", '%1', Dimcodecaption);
                    //dimdef.SETFILTER("Dimension Value Code",'%1',dimcode);
                    //dimdef.SETFILTER("Value Posting",'%1',dimdef."Value Posting"::"Same Code");
                    IF dimdef.FINDFIRST THEN
                        dimdef.DELETE;
                    // <<
                    dimvaluerec.SETFILTER(company, '<>%1', ' ');
                    IF dimvaluerec.FINDFIRST THEN
                        nameofcompany := dimvaluerec.company;

                    IF GLSetupRec.FINDFIRST THEN
                        IF GLSetupRec."Shortcut Dimension 4 Code" <> '' THEN
                            Dimcodecaption := GLSetupRec."Shortcut Dimension 4 Code";

                    "PMS Card Issued" := TRUE;

                    //create dimension for pms card no
                    dimcode := FASetup."PMS Dim Code" + "PMS No.";
                    dimname := "No." + '-' + Description;

                    dimval.SETCURRENTKEY(Code, "Global Dimension No.");
                    dimval.RESET;
                    dimval.SETFILTER("Global Dimension No.", '%1', 4);//PKONJ22.2
                    dimval.SETFILTER(Code, '%1', dimcode);
                    IF NOT dimval.FINDFIRST THEN BEGIN
                        dimval.INIT;
                        dimval.VALIDATE(Code, dimcode);
                        dimval.VALIDATE("Dimension Code", Dimcodecaption);
                        dimval.VALIDATE("Global Dimension No.", 4);//PKONJ22.2
                        dimval.VALIDATE(Blocked, FALSE);
                        dimval.VALIDATE(Active, TRUE);
                        dimval.VALIDATE(Name, dimname);
                        dimval.company := nameofcompany;
                        dimval.INSERT;

                        // create default dimensions for the pms in the FA
                        dimdef.SETCURRENTKEY("Dimension Code");
                        dimdef.RESET;
                        dimdef.SETFILTER("Table ID", '%1', 5600);
                        dimdef.SETFILTER("No.", "No.");
                        dimdef.SETFILTER("Dimension Code", '%1', Dimcodecaption);
                        dimdef.SETFILTER("Dimension Value Code", '%1', dimcode);
                        dimdef.SETFILTER("Value Posting", '%1', dimdef."Value Posting"::"Same Code");
                        IF NOT dimdef.FINDFIRST THEN BEGIN
                            dimdef.INIT;
                            dimdef.VALIDATE("Table ID", 5600);
                            dimdef.VALIDATE("No.", "No.");
                            dimdef.VALIDATE("Dimension Code", Dimcodecaption);
                            dimdef.VALIDATE("Dimension Value Code", dimcode);
                            dimdef.VALIDATE("Value Posting", dimdef."Value Posting"::"Same Code");
                            dimdef.INSERT;
                        END;
                    END;

                END ELSE BEGIN
                    dimcode := FASetup."PMS Dim Code" + xRec."PMS No.";
                    dimname := "No." + ' ' + Description;

                    dimval.SETCURRENTKEY(Code, "Global Dimension No.");
                    dimval.RESET;
                    dimval.SETFILTER("Global Dimension No.", '%1', 4);//PKONJ22.2
                    dimval.SETFILTER(Code, '%1', dimcode);
                    IF dimval.FINDFIRST THEN
                        dimval.DELETE;

                    dimdef.SETCURRENTKEY("Dimension Code");
                    dimdef.RESET;
                    dimdef.SETFILTER("Table ID", '%1', 5600);
                    dimdef.SETFILTER("No.", "No.");
                    dimdef.SETFILTER("Dimension Code", '%1', Dimcodecaption);
                    dimdef.SETFILTER("Dimension Value Code", '%1', dimcode);
                    dimdef.SETFILTER("Value Posting", '%1', dimdef."Value Posting"::"Same Code");
                    IF dimdef.FINDFIRST THEN
                        dimdef.DELETE;

                    "PMS Card Issued" := FALSE;
                END;
                //HO1.0>>
                //NYO 07/08/2018 REQ No.140
                //This is to ensure old cards are nill and the balance will move to the new card
                PMSBAl := 0;
                IF (xRec."PMS No." <> '') AND (Rec."PMS No." <> xRec."PMS No.") THEN BEGIN
                    PMSCardLedger.SETCURRENTKEY("FA No.", "PMS No.");
                    PMSCardLedger.SETRANGE(PMSCardLedger."FA No.", "No.");
                    PMSCardLedger.SETRANGE(PMSCardLedger."PMS No.", xRec."PMS No.");
                    IF PMSCardLedger.FINDSET THEN BEGIN
                        PMSCardLedger.CALCSUMS(Amount);
                        PMSBAl := PMSCardLedger.Amount;
                    END;

                    IF PMSBAl <> 0 THEN BEGIN
                        PMSCardLedger.INIT;
                        IF PMSCardLedger1.FINDLAST THEN
                            PMSCardLedger."Entry No." := PMSCardLedger1."Entry No." + 1
                        ELSE
                            PMSCardLedger."Entry No." := 1;
                        PMSCardLedger.VALIDATE("FA No.", "No.");
                        PMSCardLedger."PMS Card No." := xRec."PMS Card No.";
                        PMSCardLedger."Pms No." := "PMS No.";
                        PMSCardLedger.Description := 'Clossing entry';
                        PMSCardLedger."Posting Date" := TODAY;
                        PMSCardLedger."Documen type" := PMSCardLedger."Documen type"::Settlement;
                        PMSCardLedger.Amount := -PMSBAl;
                        PMSCardLedger."USER ID" := USERID;
                        PMSCardLedger.INSERT;

                        PMSCardLedger.INIT;
                        IF PMSCardLedger1.FINDLAST THEN
                            PMSCardLedger."Entry No." := PMSCardLedger1."Entry No." + 1
                        ELSE
                            PMSCardLedger."Entry No." := 1;
                        PMSCardLedger.VALIDATE("FA No.", "No.");
                        PMSCardLedger."PMS Card No." := "PMS Card No.";
                        PMSCardLedger."Pms No." := "PMS No.";
                        PMSCardLedger.Description := 'Opening entry';
                        PMSCardLedger."Posting Date" := TODAY;
                        PMSCardLedger."Documen type" := PMSCardLedger."Documen type"::Payment;
                        PMSCardLedger.Amount := PMSBAl;
                        PMSCardLedger."USER ID" := USERID;
                        //                        PMSCardLedger."Date PMS Availed" := 
                        PMSCardLedger.INSERT;
                    END;
                END;


            end;



        }
        field(50001; "Fuel Type"; Enum FuelType)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50002; "PMS Vehicle"; Code[50])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50005; "Vehicle Tank Capacity"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Vehicle No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }

        field(50009; "PMS Card Weekly Limit"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        /*
        field(50010; "PMS CARD BAL."; Decimal)
        {

            FieldClass = FlowField;
            CalcFormula = sum (PMSCardLedger.Amount where("FA No." = field("No."), "PMS No." = field("PMS No."), "Posting Date" = field("Date Filter")));

        }*/

        field(50012; "PMS Card Issued"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "Date Filter"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "PMS Card No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50018; "Capex No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50019; "Capex Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50020; "MRS No."; code[20])
        {
            DataClassification = CustomerContent;
        }


        field(50021; "MRS Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50022; "Item Card Created"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50023; "Purchase Type"; Enum PurchaseType)
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Item Categories"; text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Vat Prod. Posting Group"; Text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50026; "Item No."; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50027; "Approval Status"; enum ApprovalStatus)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50028; FAQRData; BLOB)
        {
            Caption = 'FA QR Picture';
            SubType = Bitmap;
            DataClassification = CustomerContent;
        }
        field(50030; "FA Tagging Code"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(50031; "Version No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Vehicle Type"; Enum VehicleTypes)
        {
            DataClassification = CustomerContent;

        }
        field(50035; "Vehicle Capacity (In Tonns)"; Decimal)
        {
            DataClassification = CustomerContent;

        }
        field(50036; "Vehicle Reg No."; CODE[50])
        {
            DataClassification = CustomerContent;

        }
        field(50037; "Acquisition Date"; date)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Lookup ("FA Ledger Entry"."Posting Date" WHERE("FA Posting Type" = FILTER("Acquisition Cost"), "FA No." = FIELD("No.")));
        }
        field(50038; "Depreciation Start Date"; date)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Lookup ("FA Depreciation Book"."Depreciation Starting Date" WHERE("FA No." = FIELD("No.")));
        }
        field(50039; "Depreciation No. Of Years"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Lookup ("FA Depreciation Book"."No. of Depreciation Years" WHERE("FA No." = FIELD("No.")));
        }
        field(50040; "Depreciation End Date"; date)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Lookup ("FA Depreciation Book"."Depreciation Ending Date" WHERE("FA No." = FIELD("No.")));
        }
        //PhaniFeb192021>>
        field(50041; "Total Acquisition Cost"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Sum ("FA Ledger Entry".Amount WHERE("FA No." = FIELD("No."), "FA Posting Type" = FILTER("Acquisition Cost")));
        }
        field(50042; "Book Value"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Sum ("FA Ledger Entry".Amount WHERE("FA No." = FIELD("No."), "FA Posting Type" = FILTER("Acquisition Cost" | Depreciation), "FA Posting Date" = FIELD("FA Posting Date Filter")));
        }
        field(50043; "Depreciation Cost"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Sum ("FA Ledger Entry".Amount WHERE("FA No." = FIELD("No."), "FA Posting Type" = FILTER(Depreciation)));

        }
        field(50044; "Salvage Value"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Sum ("FA Ledger Entry".Amount WHERE("FA No." = FIELD("No."), "FA Posting Type" = FILTER("Salvage Value")));

        }
        field(50045; "Maintenance Cost"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = Sum ("Maintenance Ledger Entry".Amount WHERE("FA No." = FIELD("No.")));
        }
        //PhaniFeb192021<<
        field(50047; "FA Tagging Required"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50048; "Depreciation Book Code"; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50049; "Depreciation Start Date2"; Date)
        {
            DataClassification = CustomerContent;
            Enabled = false;
        }
        //Balu 05132021>>
        field(65008; "Residence Maint. Register No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65009; "Residence TPA. Register No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(65010; "Description 1"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        //Balu 05132021<<
        //Baluonsep14>>
        field(50050; "Global Dimension 1 Filter"; Code[20])
        {
            CaptionClass = '1,3,1';
            Caption = 'Global Dimension 1 Filter';
            FieldClass = FlowFilter;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));
        }
        field(50051; "Global Dimension 2 Filter"; Code[20])
        {
            CaptionClass = '1,3,2';
            Caption = 'Global Dimension 2 Filter';
            FieldClass = FlowFilter;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(2));
        }
        field(50052; "Old_Vehicle No."; Code[20])
        {
        }
        field(50053; "Old_Vehicle Type"; Option)
        {
            OptionMembers = ,Commercial,Private;
        }
        field(50054; "User grade"; Code[20])
        {
        }
        field(50055; "User Name"; Text[50])
        {
        }
        //Baluonsep14<<


    }

    var
        PMSBAl: Decimal;
        myInt: Integer;
        FASetup: Record "FA Setup";
        GLSetupRec: record "General Ledger Setup";
        Dimcodecaption: Code[20];
        Text024: Label 'Specify if Deisel or Petrol';
        dimcode: code[100];
        dimdef: record "Default Dimension";
        dimvaluerec: Record "Dimension Value";
        nameofcompany: Code[200];
        dimname: Code[100];
        dimval: Record "Dimension Value";
        PMSCardLedger: Record PMSCardLedger;
        PMSCardLedger1: Record PMSCardLedger;
        PmsManegementRec: Record PMSManagement;
        FixedAssetGRec: record "Fixed Asset";
        DPB: Record "Depreciation Book";

}

