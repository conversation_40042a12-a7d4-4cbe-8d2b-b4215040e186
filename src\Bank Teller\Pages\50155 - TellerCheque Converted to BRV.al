page 50155 "Teller/Cheque Converted to BRV"
{

    Caption = 'Bank Confirmed Tellers Registered';
    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    PageType = List;
    SourceTable = "Confirmed Teller Receipt";
    SourceTableView = WHERE("Bank Receipt Created" = FILTER(true),
                            "Original Teller Posted" = FILTER(false));
    UsageCategory = lists;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("No."; "No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Company; Company)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posted By"; "Posted By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posted Date"; "Posted Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 1 Code"; "Global Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Global Dimension 2 Code"; "Global Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Customer No."; "Customer No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Customer Name"; "Customer Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Issued"; "Bank Issued")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Code"; "Bank Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Location"; "Bank Location")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Confirmation No."; "Confirmation No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Type"; "Teller Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller No."; "Teller No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Date"; "Teller Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Teller Amount"; "Teller Amount")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cheque No."; "Cheque No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Cheque Date"; "Cheque Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Chq Value Date"; "Chq Value Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Created BRV No."; "Created BRV No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Original Teller Recpt."; "Original Teller Recpt.")
                {
                    ApplicationArea = all;
                }
                field("Original Teller Recvd. by"; "Original Teller Recvd. by")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Original Teller Recpt. Date"; "Original Teller Recpt. Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Created by"; "Created by")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Original Teller Recpt. Time"; "Original Teller Recpt. Time")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("&Post")
            {
                ApplicationArea = all;
                Caption = '&Post';
                Promoted = true;
                PromotedCategory = Process;
                Visible = false;
                trigger OnAction();
                begin
                    OriginalTellerReceiptAndPosted;
                end;
            }
            action(CreatedBankReceiptVoucher)
            {
                ApplicationArea = all;
                Caption = 'Created Bank Receipt Voucher';
                Promoted = true;
                PromotedCategory = Process;
                trigger OnAction()
                var
                    VoucherLine: Record "Gen. Journal Line";
                    GLEntry: Record "G/L Entry";
                BEGIN
                    VoucherLine.reset;
                    VoucherLine.SetRange("Voucher type", VoucherLine."Voucher type"::BRV);
                    VoucherLine.SetRange("Journal Template Name", 'BRV');
                    VoucherLine.SetRange("Journal Batch Name", 'BRV');
                    VoucherLine.SetRange("Source Code", 'BRV');
                    VoucherLine.SetRange("Document No.", Rec."Created BRV No.");
                    //>>>>>>>>>G2S 19/7/24 CAS-01321-Z9Q3Y7 
                    IsCustomer := false;
                    IF VoucherLine.findset then begin
                        repeat
                            if VoucherLine."Account Type" = VoucherLine."Account Type"::Customer THEN
                                IsCustomer := true;
                        until (VoucherLine.Next() = 0) or IsCustomer;
                        If NOT IsCustomer then
                            VerifyDocumentType();
                        //>>>>>>>>>G2S 19/7/24 CAS-01321-Z9Q3Y7 
                        page.RunModal(50241, VoucherLine);
                    end;
                    //Fix14May2021>>
                    GLEntry.Reset();
                    GLEntry.SetRange("Old Document No.", "Created BRV No.");
                    if GLEntry.FindFirst() then begin
                        Validate("Original Teller Posted", true);
                        Modify()
                    end;
                    //Fix14May2021<<
                END;
            }
        }
    }
    trigger OnOpenPage();
    begin
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then
            SETFILTER("Responsibility Center", BuildFilter);
    end;

    //>>>>>>>>>G2S 19/7/24 CAS-01321-Z9Q3Y7 
    procedure VerifyDocumentType()
    var
        GenJnlLn: Record "Gen. Journal Line";
        Count: Integer;
    begin
        GenJnlLn.Reset();
        GenJnlLn.SetRange("Voucher type", GenJnlLn."Voucher type"::BRV);
        GenJnlLn.SetRange("Journal Template Name", 'BRV');
        GenJnlLn.SetRange("Journal Batch Name", 'BRV');
        GenJnlLn.SetRange("Source Code", 'BRV');
        GenJnlLn.SetRange("Document No.", Rec."Created BRV No.");
        if GenJnlLn.FindSet() then
            repeat
                if GenJnlLn."Document Type" <> GenJnlLn."Document Type"::" " then begin
                    GenJnlLn.VALIDATE("Document Type", GenJnlLn."Document Type"::" ");
                    GenJnlLn.Modify();
                    Commit(); //>>>17/01/25 Commit error Fix
                end;
            until GenJnlLn.Next = 0;
    end;
    //>>>>>>>>>G2S 19/7/24 CAS-01321-Z9Q3Y7 
    var
        BankConfirmTellersRec: Record "Confirmed Teller Receipt";
        BankTellerConfirmationRec: Record "Request Teller Receipt";
        Linecount: Integer;
        Window: Dialog;
        IsCustomer: Boolean;
        OldBankTellerConfirmationRec: Record "Request Teller Receipt";
        Text50200: Label 'Reverse Confirmed Bank Teller must not be false';
        Text50201: Label 'You do not have permission to Reverse Confirmed Bank Tellers';
        Text50202: Label 'Posting lines         #2######';
        UserSetup: Record "User Setup";
        Text50203: Label 'You do not have permission For confirmed Teller/Cheque.';
        UserMgt: Codeunit "User Setup Management";
        RespCentCount: Integer;
        //UserIDRespCent: Record "UserID Resp. Cent. Lines";//B2BSB.1.0
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}