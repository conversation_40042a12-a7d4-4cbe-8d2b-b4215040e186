//RFC#39 BANK API
//Signed on 31/10/2023
//Published on 11th November 2023
//updated on 15th November 2023
//G2S

/// <summary>
/// Page Bank Payment Notification (ID 50228).
/// </summary>
//RFCBankAPIGo2solveJuly2023>>>>>>
page 50228 "Bank Payment Notification Card"
{
    Caption = 'Bank Payment Notification Card';
    PageType = Card;
    SourceTable = "Bank Payment Notification";
    UsageCategory = ReportsAndAnalysis;
    Editable = false;
    DeleteAllowed = true;
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field(ID; Rec.ID)
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Customer ID"; Rec."Customer ID")
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field("Date Entered"; Rec."Date Entered")
                {
                    ApplicationArea = All;
                    Editable = false;

                }

                field(Narration; Rec.Narration)
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field("Payment Reference"; Rec."Payment Reference")
                {
                    ApplicationArea = All;
                    Editable = false;
                }

                field("Session ID"; Rec."Session ID")
                {
                    ApplicationArea = All;
                    Editable = false;

                }
                field(SourceBankCode; Rec.SourceBankCode)
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field(Amount; Rec.Amount)
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field(Processed; Processed)
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Date Posted"; "Date Posted")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Document No."; Rec."Document No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                }

                field("Duplicate Session ID?"; Rec."Duplicate Session ID?")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("Temp. Omit record from Batch"; Rec."Temp. Omit record from Batch")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Process)
            {
                Caption = 'Post Payment';
                ApplicationArea = All;
                Image = PostedPayment;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Visible = true;
                trigger OnAction()
                var
                    BankAPICodeunit: Codeunit "BANKAPI-Process Cust. Payment";
                    ErrorLog: Record "Nibss Notifications Error Log";
                    RecordExist, ErrorExist : Boolean;
                    Customer: Record Customer;
                    GenJnl: Record "Gen. Journal Line";
                    BankPymtCopy, BankPymntRec, BankPymntRecPost : Record "Bank Payment Notification";

                begin
                    ErrorExist := false;

                    Rec.TestField(SourceBankCode);
                    Rec.TestField(Amount);
                    Rec.TestField("Customer ID");
                    Rec.TestField("Session ID");
                    //validate bank records

                    // if BankPymtCopy."Temp. Omit record from Batch" = true then error('transaction cannot be posted now as the record has been flagged');
                    //Rec.TestField("Payment Reference");

                    if Rec."Temp. Omit record from Batch" = true then error('transaction cannot be posted now as the record has been flagged');
                    Rec.TestField("Payment Reference");
                    if not (Rec.Processed = true) then begin
                        if Confirm(Text003, false) then begin
                            ErrorLog.DeleteAll();
                            if BankAPICodeunit.ValidateBankPaymentRec(Rec) then begin
                                //ClearLastError();
                                BankAPICodeunit.InitGLLines(Rec);
                                if BankAPICodeunit.ProcessCustPayment(Rec) then begin
                                    BankAPICodeunit.InsertGLJnlLine(GenJnl, 1);
                                    //InsertGLJnlLine(GenJnlLineCopy2, 2);
                                    //if PostGenJnlLine(GenJnlLineCopy) then begin
                                    if BankAPICodeunit.PostGenJnlLine() then begin
                                        BankAPICodeunit.UpdateBankPymntRec(Rec);
                                        ErrorExist := false;
                                        Message(Text002);
                                    end else begin
                                        ErrorExist := true;
                                    end;
                                end else begin
                                    ErrorExist := true;
                                end;

                                //log error if error exist 
                                if not ErrorExist then begin
                                    //update payment
                                    BankAPICodeunit.UpdateBankPymntRec(Rec);
                                    //******** delete all existing error log details upon successful posting
                                end;
                            end else
                                Message('An error has occurred. Please check error log for details');
                        end else
                            Message(Text004);
                    end
                    else
                        Message(Text001);

                end;
            }
        }
    }

    var
        Text001: Label 'Record has been processed already';
        Text002: Label 'Transaction has been sucessfully posted';
        Text003: Label 'Are you sure you want to post this transaction?';
        Text004: Label 'Process has been aborted.';
}

//RFCBankAPIGo2solveJuly2023<<<<<<