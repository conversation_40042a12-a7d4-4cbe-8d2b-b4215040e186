/// <summary>
/// Table ProdOrderRep (ID 50059).
/// </summary>
table 50059 "Production Order Report"
{
    Caption = 'ProdOrderRep';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; ID; Integer)
        {
            Caption = 'ID';
            AutoIncrement = true;
        }
        field(2; SessionID; Integer)
        {
            Caption = 'SessionID';
        }
        field(3; UserID; Code[50])
        {
            Caption = 'UserID';
        }
        field(4; "Production Order No."; Code[20])
        {
            Caption = 'ProductionOrder';
        }
    }
    keys
    {
        key(PK; ID)
        {
            Clustered = true;
        }
    }


}
