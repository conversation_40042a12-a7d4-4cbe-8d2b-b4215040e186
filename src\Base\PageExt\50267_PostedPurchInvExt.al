pageextension 50267 PostPurchInv extends "Posted Purchase Invoice"
{
    layout
    {
        addafter("Location Code")
        {
            field("Import File No."; "Import File No.")
            {
                ApplicationArea = all;
            }
            field("Clearing File No."; "Clearing File No.")
            {
                ApplicationArea = all;
            }
        }
        //FIX04Jun2021>>
        addafter("Posting Date")
        {
            field("Arrival Date"; "Arrival Date")
            {

            }
        }
        //FIX04Jun2021<<
        // Add changes to page layout here
    }

    actions
    {
        addafter(Print)
        {
            action("Purchase Invoice")
            {
                ApplicationArea = Suite;
                Caption = 'Purchase Invoice';
                Ellipsis = true;
                Image = Report;
                trigger OnAction()
                var
                    PurHdr: Record "Purch. Inv. Header";
                begin
                    PurHdr.reset;
                    PurHdr.SetRange("No.", "No.");
                    IF PurHdr.findfirst then
                        Report.RunModal(50038, true, false, PurHdr);
                end;
            }
        }
    }

    var
        myInt: Integer;
}