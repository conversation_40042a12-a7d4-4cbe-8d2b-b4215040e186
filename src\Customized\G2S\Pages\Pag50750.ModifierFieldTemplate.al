//<<<<<< G2S CAS-01334-J2M7C2 8/30/2024
page 50750 "Modifier Field Template"
{
    ApplicationArea = All;
    Caption = 'Modifier Field Template';
    PageType = List;
    SourceTable = "Modifier Field Template";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Record Type"; Rec."Record Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the RecordType field.', Comment = '%';
                }
                field("Field Name"; Rec."Field Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Field Name field.', Comment = '%';
                }
                field(FieldID; Rec.FieldID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the FieldID field.', Comment = '%';
                }
                field(TableID; Rec.TableID)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the TableID field.', Comment = '%';
                }
                field("Allow Modify"; Rec."Allow Modify")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Allow Modify field.', Comment = '%';
                }
            }
        }
    }
}
// >>>>>> G2S CAS-01334-J2M7C2 8/30/2024
