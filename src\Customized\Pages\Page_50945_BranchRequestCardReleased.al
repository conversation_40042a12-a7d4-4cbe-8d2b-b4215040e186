page 50945 BranchRequestCardReleased
{
    Caption = 'Branch Request Card Released';
    PageType = Document;
    //ApplicationArea = All;//PKON1605
    //UsageCategory = Documents;//PKON1605
    DeleteAllowed = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    SourceTable = BranchRequest;
    DelayedInsert = true;
    SourceTableView = where("Transfer Order No." = filter(<> ''));

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field(No; No)
                {
                    ApplicationArea = All;
                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE();
                    end;

                }
                field("From Location"; "From Location")
                {
                    ApplicationArea = All;
                }
                field("To Location"; "To Location")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Branch Req Mail Send"; "Branch Req Mail Send")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Transfer Order No."; "Transfer Order No.")
                {
                    ApplicationArea = all;
                }
                field("Mail Send"; "Mail Send")
                {
                    Caption = 'Mail Sent';
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(BranchRequestLine; BranchRequestSubformRel)
            {
                ApplicationArea = Basic, Suite;
                SubPageLink = "Document No." = field(No);
                UpdatePropagation = Both;
            }
        }
    }

    actions
    {
        area(Processing)
        {

            action("Send Br Notification")
            {
                Image = SendMail;
                ApplicationArea = all;
                trigger OnAction()
                var
                    UserSetLVar: Record "User Setup";
                begin
                    if "Mail Send" then begin
                        UserSetLVar.Get(UserId);
                        if not UserSetLVar."Resend Branch Request" then
                            Error('You are not Authorized Person to resend the Email');
                    end;
                    CountMail();
                    //TestField("Transfer Type", "Transfer Type"::"Branch Request");
                    sendmail.SendAppBrStkRequestAlertFromBR(Rec, Email);
                    "Mail Send" := true;
                    Message('Sent Successfully');
                end;
            }
            action("Create Transfer Order")
            {
                Image = Post;
                ApplicationArea = all;
                trigger OnAction()
                begin
                    CreateTransferOrder();
                end;

            }

        }

    }
    procedure CreateTransferOrder()
    var
        transferHdr: Record "Transfer Header";
        transferLine: Record "Transfer Line";
        BranchRequestLine: Record BranchRequestLine;
        BranchRequest: Record BranchRequest;
        Validations: Codeunit Validations;
        BranchRqstLine: Record BranchRequestLine;
    begin
        //Balu 05142021>>
        if "Transfer Order No." <> '' then
            Error('Already Transfer Order is Created.');
        BranchRqstLine.Reset();
        BranchRequestLine.SetRange("Document No.", No);
        BranchRequestLine.SetFilter("Issued Quantity", '%1', 0);
        if BranchRequestLine.FindFirst() then
            if not Confirm('There are some lines with zero Quantity Issue Do you want to skip and Continue', true, false) then
                exit;
        //Balu 05142021<<
        if not Confirm('Do you want to create transfer order', false) then
            exit;
        transferHdr.Init();
        transferHdr."No." := '';
        transferHdr.Insert(true);
        transferHdr.Validate("Transfer-from Code", Rec."From Location");
        transferHdr.Validate("Transfer-to Code", Rec."To Location");
        transferHdr.Validate("In-Transit Code", 'INTRANSIT');
        transferHdr."Transfer Type" := transferHdr."Transfer Type"::"Branch Request";
        transferHdr."Branch Request No" := No;
        transferHdr.Modify();
        "Transfer Order No." := transferHdr."No.";
        TransferOrderCreated := true;
        Modify(false);
        BranchRequestLine.Reset();
        BranchRequestLine.SetRange("Document No.", No);
        //BranchRequestLine.SetRange("Issued Quantity",<> '');
        BranchRequestLine.SetFilter("Issued Quantity", '<>%1', 0);//Balu 05142021
        if BranchRequestLine.FindSet() then
            repeat
                transferLine.Init();
                transferLine."Document No." := transferHdr."No.";
                transferLine."Line No." := BranchRequestLine."Line No.";
                transferLine.Insert();
                transferLine.validate("Item No.", BranchRequestLine."Item No.");
                //transferLine.Description := BranchRequestLine.Description;
                transferLine.Validate("Unit of Measure Code", BranchRequestLine."Unit Of Measure");
                //transferLine.Validate(Quantity, BranchRequestLine."Requested Quantity");
                transferLine.Validate(Quantity, BranchRequestLine."Issued Quantity");//Balu 05142021
                Validations.CheckQtyValidations(transferLine);//FIX 10May2021
                transferLine.Modify();
            until BranchRequestLine.next = 0;
        Message('Transfer Order no. %1 is created.', transferHdr."No.");
    end;

    var

        myInt: Integer;
        sendmail: Codeunit "Send Mail";
        Email: Text[200];


    trigger OnModifyRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not modify branch request. Already transfer order is created.');
    end;

    trigger OnDeleteRecord(): Boolean
    begin
        if TransferOrderCreated then
            Error('you can not deleted branch request. Already transfer order is created.');
    end;


}