page 50499 "Retail Items"
{
    Caption = 'Retail Items';
    PageType = List;
    SourceTable = Item;
    SourceTableView = where("Item Category Code" = filter('FG'));

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of the item.';
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies what you are selling.';
                }
            }
        }
    }
}
