codeunit 50078 CTCSubscription
{
    trigger OnRun()
    begin

    end;

    [EventSubscriber(ObjectType::Table, 81, 'OnAfterCopyGenJnlLineFromGenJnlAllocation', '', false, false)]
    local procedure OnAfterCopyGenJnlLineFromGenJnlAllProc(GenJnlAllocation: Record "Gen. Jnl. Allocation"; var GenJournalLine: Record "Gen. Journal Line")
    begin
        GenJournalLine."Line Account No." := GenJnlAllocation."FA Account No.";
        GenJournalLine."Fixed Asset No." := GenJnlAllocation."Fixed Asset No.";
        GenJournalLine.Description := GenJnlAllocation.Description;
        IF GenJnlAllocation."Line Account Type" = GenJnlAllocation."Line Account Type"::"IC Partner" then begin
            GenJournalLine."Journal Template Name" := GenJnlAllocation."Journal Template Name";
            GenJournalLine."Journal Batch Name" := GenJnlAllocation."Journal Batch Name";
            GenJournalLine."IC Partner Code" := GenJnlAllocation."FA Account No.";
            GenJournalLine.Description := GenJnlAllocation.Description;
            GenJournalLine."IC Partner G/L Acc. No." := GenJnlAllocation."Account No.";
            GenJournalLine."Account Type" := GenJournalLine."Account Type"::"IC Partner";
            GenJournalLine."Account No." := GenJnlAllocation."FA Account No.";
        End Else
            IF GenJnlAllocation."Line Account Type" = GenJnlAllocation."Line Account Type"::"G/L Account" then begin
                GenJournalLine."Journal Template Name" := GenJnlAllocation."Journal Template Name";
                GenJournalLine."Journal Batch Name" := GenJnlAllocation."Journal Batch Name";
                GenJournalLine."IC Partner Code" := '';
                GenJournalLine."IC Partner G/L Acc. No." := '';
                GenJournalLine."Account Type" := GenJournalLine."Account Type"::"G/L Account";
                GenJournalLine."FA Posting Type" := GenJournalLine."FA Posting Type"::" ";
                GenJournalLine."Depreciation Book Code" := '';
                GenJournalLine."Account No." := GenJnlAllocation."FA Account No.";
                GenJournalLine.Description := GenJnlAllocation.Description;
                GenJournalLine."Fixed Asset No." := GenJnlAllocation."Fixed Asset No.";
            End Else begin
                GenJournalLine."Journal Template Name" := GenJnlAllocation."Journal Template Name";
                GenJournalLine."Journal Batch Name" := GenJnlAllocation."Journal Batch Name";
                GenJournalLine."IC Partner Code" := '';
                GenJournalLine."IC Partner G/L Acc. No." := '';
                IF GenJnlAllocation."FA Account No." <> '' then begin
                    if GenJnlAllocation."Line Account Type" = GenJnlAllocation."Line Account Type"::"Fixed Asset" then
                        GenJournalLine."Account Type" := GenJournalLine."Account Type"::"Fixed Asset";
                    GenJournalLine."Account No." := GenJnlAllocation."FA Account No.";
                    GenJournalLine.Description := GenJnlAllocation.Description;
                end Else begin
                    GenJournalLine."Account Type" := GenJournalLine."Account Type"::"G/L Account";
                    GenJournalLine."Account No." := GenJnlAllocation."Account No.";
                    GenJournalLine.Description := GenJnlAllocation.Description;
                    GenJournalLine."Depreciation Book Code" := '';
                end;
                GenJournalLine."Fixed Asset No." := GenJnlAllocation."Fixed Asset No.";
            end;
    end;

}