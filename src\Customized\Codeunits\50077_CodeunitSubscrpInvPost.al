codeunit 50077 InvPostingGrpandOtherSub
{
    Permissions = TableData "Sales Header" = rm,
                  TableData "Item Journal Line" = rm,
                TableData "Gen. Journal Line" = rmi,
        TableData "Posted Whse. Shipment Header" = rmi,
        TableData "Posted Whse. Receipt Header" = rmi,
        TableData "FA Journal Line" = rm,
        TableData "Purchase Header" = rm;

    trigger OnRun()
    begin

    end;


    Procedure CheckAllowedItemBackdating(InvPostingDate: Date): Boolean
    var
        UsersetupGRec: Record "User Setup";
    begin
        IF UsersetupGRec.GET(USERID) THEN BEGIN
            IF (InvPostingDate >= UsersetupGRec."Allow Inv. Posting From") AND (InvPostingDate <= UsersetupGRec."Allow Inv. Posting To") THEN
                EXIT(TRUE) ELSE
                EXIT(FALSE);
        END;
    end;
    //Inventory Setup Posting Dates Subscription in CU 12 and saved CU.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnBeforeCode', '', false, false)]
    local procedure GEnjnlLineOnBeforeCode1(var GenJnlLine: Record "Gen. Journal Line"; CheckLine: Boolean; var IsPosted: Boolean; var GLReg: Record "G/L Register")
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
        GenJourTemplate: Record "Gen. Journal Template";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                if GenJourTemplate.Get(GenJnlLine."Journal Template Name") then;//FIX29Sep2021
                if not GenJourTemplate.Recurring then//FIX29Sep2021
                    IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                      (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                        //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                        IF CheckAllowedItemBackdating(GenJnlLine."Posting Date") THEN BEGIN
                            UsersetupGGRec.GET(USERID);
                            UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                            GenJnlLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                        END ELSE
                            GenJnlLine."Posting Date" := TODAY;
                    END ELSE
                        GenJnlLine."Posting Date" := TODAY;
            END;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line2", 'OnBeforeCode', '', false, false)]
    local procedure GEnjnlLineOnBeforeCode2(var GenJnlLine: Record "Gen. Journal Line"; CheckLine: Boolean; var IsPosted: Boolean; var GLReg: Record "G/L Register")
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
        GenJourTemplate: Record "Gen. Journal Template";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                if GenJourTemplate.Get(GenJnlLine."Journal Template Name") then;//FIX29Sep2021
                if not GenJourTemplate.Recurring then//FIX29Sep2021
                    IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                      (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                        //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                        IF CheckAllowedItemBackdating(GenJnlLine."Posting Date") THEN BEGIN
                            UsersetupGGRec.GET(USERID);
                            UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                            GenJnlLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                        END ELSE
                            GenJnlLine."Posting Date" := TODAY;
                    END ELSE
                        GenJnlLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 21 and saved CU.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Check Line", 'OnAfterCheckItemJnlLine', '', false, false)]
    local procedure Onafterchckitemjli(var ItemJnlLine: Record "Item Journal Line"; CalledFromInvtPutawayPick: Boolean; CalledFromAdjustment: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(ItemJnlLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        ItemJnlLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        ItemJnlLine."Posting Date" := TODAY;
                END ELSE
                    ItemJnlLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 22 and saved CU.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", 'OnBeforePostItemJnlLine', '', false, false)]
    local procedure OnBeforePostItemJnlLineProc(var ItemJournalLine: Record "Item Journal Line"; CalledFromAdjustment: Boolean; CalledFromInvtPutawayPick: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(ItemJournalLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        ItemJournalLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        ItemJournalLine."Posting Date" := TODAY;
                END ELSE
                    ItemJournalLine."Posting Date" := TODAY;
            END;
        //PKONAU20>>

        IF ItemJournalLine."Journal Template Name" = 'TRANSFER' THEN begin
            ItemJournalLine.TestField("Shortcut Dimension 1 Code");
            ItemJournalLine.TestField("Shortcut Dimension 2 Code");
            ItemJournalLine.TestField("New Shortcut Dimension 1 Code");
            ItemJournalLine.TestField("New Shortcut Dimension 2 Code");
        end;
        //PKONAU20<<
    end;

    //PKONAU31>>
    //Inventory Setup Posting Dates Subscription in CU 80 and saved CU.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnBeforePostSalesDoc', '', false, false)]
    local procedure OnBeforePostSalesDoc(var SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean; PreviewMode: Boolean; var HideProgressWindow: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    IF CheckAllowedItemBackdating(SalesHeader."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        SalesHeader.VALIDATE("Posting Date", UsersetupGGRec."Allow Inv. Posting To");
                    END ELSE
                        SalesHeader.VALIDATE("Posting Date", TODAY);
                END ELSE
                    SalesHeader.VALIDate("Posting Date", TODAY);
            END;
    end;
    //PKONAU31>>
    //Inventory Setup Posting Dates Subscription in CU 80 and saved CU.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterCheckAndUpdate', '', false, false)]
    local procedure OnAfterCheckAndUpdateSalesProc(var SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean; PreviewMode: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(SalesHeader."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        SalesHeader.VALIDATE("Posting Date", UsersetupGGRec."Allow Inv. Posting To");
                    END ELSE
                        SalesHeader.VALIDATE("Posting Date", TODAY);
                    //PKonj8validated above fields
                END ELSE
                    SalesHeader.VALIDate("Posting Date", TODAY);
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 90 and saved CU.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnAfterCheckAndUpdate', '', false, false)]
    local procedure OnAfterCheckAndUpdatePurchProc(var PurchaseHeader: Record "Purchase Header"; CommitIsSuppressed: Boolean; PreviewMode: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(PurchaseHeader."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        PurchaseHeader."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        PurchaseHeader."Posting Date" := TODAY;
                END ELSE
                    PurchaseHeader."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 231.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post", 'OnBeforeGenJnlPostBatchRun', '', false, false)]
    local procedure OnBeforeGenJnlPostBatchRunProc(var GenJnlLine: Record "Gen. Journal Line"; var IsHandled: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(GenJnlLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        GenJnlLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        GenJnlLine."Posting Date" := TODAY;
                END ELSE
                    GenJnlLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 232.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post+Print", 'OnBeforePostJournalBatch', '', false, false)]
    local procedure OnBeforePostJournalBatchProc(var GenJournalLine: Record "Gen. Journal Line"; var HideDialog: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(GenJournalLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        GenJournalLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        GenJournalLine."Posting Date" := TODAY;
                END ELSE
                    GenJournalLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 241.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post", 'OnBeforeCode', '', false, false)]
    local procedure OnBeforeCodeProc(var ItemJournalLine: Record "Item Journal Line"; var HideDialog: Boolean; var SuppressCommit: Boolean; var IsHandled: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(ItemJournalLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        ItemJournalLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        ItemJournalLine."Posting Date" := TODAY;
                END ELSE
                    ItemJournalLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 242.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post+Print", 'OnBeforePostJournalBatch', '', false, false)]
    local procedure OnBeforePostJournalBatch(var ItemJournalLine: Record "Item Journal Line"; var HideDialog: Boolean; var SuppressCommit: Boolean; var IsHandled: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    // IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(ItemJournalLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        ItemJournalLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        ItemJournalLine."Posting Date" := TODAY;
                END ELSE
                    ItemJournalLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 444.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purchase-Post Prepayments", 'OnAfterCheckPrepmtDoc', '', false, false)]
    local procedure OnAfterCheckPrepmtDocProc(PurchHeader: Record "Purchase Header"; DocumentType: Option Invoice,"Credit Memo")
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(PurchHeader."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        PurchHeader."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        PurchHeader."Posting Date" := TODAY;
                END ELSE
                    PurchHeader."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 5631.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"FA Jnl.-Check Line", 'OnAfterCheckFAJnlLine', '', false, false)]
    local procedure OnAfterCheckFAJnlLineProc(var FAJnlLine: Record "FA Journal Line")
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(FAJnlLine."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        FAJnlLine."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        FAJnlLine."Posting Date" := TODAY;
                END ELSE
                    FAJnlLine."Posting Date" := TODAY;
            END;
    end;

    //Inventory Setup Posting Dates Subscription in CU 5760.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", 'OnBeforePostedWhseRcptHeaderInsert', '', false, false)]
    local procedure OnBeforePostedWhseRcptHeaderInsertProc(var PostedWhseReceiptHeader: Record "Posted Whse. Receipt Header"; WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        InventorySetupRec: Record "Inventory Setup";
    begin
        //mandate posting date as current date 
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                IF InventorySetupRec."Allowed Date for Inv. BackDate" = TODAY THEN BEGIN
                    IF NOT CheckAllowedItemBackdating(WarehouseReceiptHeader."Posting Date") THEN
                        PostedWhseReceiptHeader."Posting Date" := TODAY;
                    //WarehouseReceiptHeader."Posting Date" := TODAY;
                    //PostedWhseRcptHeader.MODIFY;
                END ELSE BEGIN
                    PostedWhseReceiptHeader."Posting Date" := TODAY;
                END;
            END; //
    end;

    //Inventory Setup Posting Dates Subscription in CU 5763.
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", 'OnAfterPostedWhseShptHeaderInsert', '', false, false)]
    local procedure OnAfterPostedWhseShptHeaderInsertProc(PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; LastShptNo: Code[20])
    var
        InventorySetupRec: Record "Inventory Setup";
    begin
        //mandate posting date as current date 
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                IF InventorySetupRec."Allowed Date for Inv. BackDate" = TODAY THEN BEGIN
                    IF NOT CheckAllowedItemBackdating(PostedWhseShipmentHeader."Posting Date") THEN
                        PostedWhseShipmentHeader."Posting Date" := TODAY;
                END ELSE BEGIN
                    PostedWhseShipmentHeader."Posting Date" := TODAY;
                END;
            END; //
                 //
    end;

    //PK Added On 06022021 >>
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", 'OnBeforePostPurchaseDoc', '', false, false)]
    local procedure OnBeforePostPurchaseDoc(var PurchaseHeader: Record "Purchase Header"; PreviewMode: Boolean; CommitIsSupressed: Boolean; var HideProgressWindow: Boolean)
    var
        InventorySetupRec: Record "Inventory Setup";
        UsersetupGGRec: Record "User Setup";
    begin
        IF InventorySetupRec.GET THEN
            IF InventorySetupRec."No Backdating Allowed" THEN BEGIN
                InventorySetupRec.TESTFIELD("Allowed Date for Inv. BackDate");
                InventorySetupRec.TESTFIELD("Allowed Date for Backdate To");
                IF (TODAY >= InventorySetupRec."Allowed Date for Inv. BackDate") AND
                  (TODAY <= InventorySetupRec."Allowed Date for Backdate To") THEN BEGIN
                    //IF NOT CheckAllowedItemBackdating("Posting Date") THEN
                    IF CheckAllowedItemBackdating(PurchaseHeader."Posting Date") THEN BEGIN
                        UsersetupGGRec.GET(USERID);
                        UsersetupGGRec.TESTFIELD("Allow Inv. Posting To");
                        PurchaseHeader."Posting Date" := UsersetupGGRec."Allow Inv. Posting To";
                    END ELSE
                        PurchaseHeader."Posting Date" := TODAY;
                END ELSE
                    PurchaseHeader."Posting Date" := TODAY;
            END;
    end;
    //PK Added On 06022021 <<

    var
        myInt: Integer;
}