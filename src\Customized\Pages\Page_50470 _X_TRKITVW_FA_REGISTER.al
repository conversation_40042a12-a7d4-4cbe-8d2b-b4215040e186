page 50470 X_TRKITVW_FA_REGISTER
{
    // version TRKIT

    Caption = 'X_TRKITVW_FA_REGISTER';
    Editable = false;
    PageType = List;
    Permissions = TableData "Fixed Asset" = rim;
    SourceTable = XX_TRKIT_FA_REGISTER;
    SourceTableView = SORTING(ASSET_NUMBER)
                      WHERE(STATUS = FILTER('TAGGED'));
    UsageCategory = Tasks;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(UID; UID)
                {
                    ApplicationArea = all;
                }
                field(ASSET_NUMBER; ASSET_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(TAG_NUMBER; TAG_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(ASSET_DESCRIPTION; ASSET_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(FA_LOCATION_CODE; FA_LOCATION_CODE)
                {
                    ApplicationArea = all;
                }
                field(FA_LOCATION_DESCRIPTION; FA_LOCATION_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(ACC_LOCATION_CODE; ACC_LOCATION_CODE)
                {
                    ApplicationArea = all;
                }
                field(ACC_LOCATION_DESCRIPTION; ACC_LOCATION_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(CC_LOCATION_CODE; CC_LOCATION_CODE)
                {
                    ApplicationArea = all;
                }
                field(CC_LOCATION_DESCRIPTION; CC_LOCATION_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(CATEGORY_CODE; CATEGORY_CODE)
                {
                    ApplicationArea = all;
                }
                field(CATEGORY_DESCRIPTION; CATEGORY_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(SUB_CATEGORY_CODE; SUB_CATEGORY_CODE)
                {
                    ApplicationArea = all;
                }
                field(SUB_CATEGORY_DESCRIPTION; SUB_CATEGORY_DESCRIPTION)
                {
                    ApplicationArea = all;
                }
                field(CUSTODIAN_ID; CUSTODIAN_ID)
                {
                    ApplicationArea = all;
                }
                field(MAIN_ASSET_COMPONENT_ASSET; MAIN_ASSET_COMPONENT_ASSET)
                {
                    ApplicationArea = all;
                }
                field(COMPONENT_OF_MAIN_ASSET_NO; COMPONENT_OF_MAIN_ASSET_NO)
                {
                    ApplicationArea = all;
                }
                field(ACQUISITION_DATE; ACQUISITION_DATE)
                {
                    ApplicationArea = all;
                }
                field(INVOICE_NUMBER; INVOICE_NUMBER)
                {
                    ApplicationArea = all;
                }
                field(INVOICE_VALUE; INVOICE_VALUE)
                {
                    ApplicationArea = all;
                }
                field(HHT_CREATED_TIME; HHT_CREATED_TIME)
                {
                    ApplicationArea = all;
                }
                field(HHT_CREATED_USER; HHT_CREATED_USER)
                {
                    ApplicationArea = all;
                }
                field(HHT_UPDATED_DATE; HHT_UPDATED_DATE)
                {
                    ApplicationArea = all;
                }
                field(HHT_UPDATED_USER; HHT_UPDATED_USER)
                {
                    ApplicationArea = all;
                }
                field(STATUS; STATUS)
                {
                    ApplicationArea = all;
                }
                field(NAVISION_UPDATED_TIME; NAVISION_UPDATED_TIME)
                {
                    ApplicationArea = all;
                }
                field(NAVISION_UPDATED_USER; NAVISION_UPDATED_USER)
                {
                    ApplicationArea = all;
                }
                field(REMARKS; REMARKS)
                {
                    ApplicationArea = all;
                }
                field(BLOCKED; BLOCKED)
                {
                    ApplicationArea = all;
                }
                field(SERIAL_NUMBER; SERIAL_NUMBER)
                {
                    ApplicationArea = all;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action(Sychronize)
            {
                ApplicationArea = all;
                Caption = 'Sychronize';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    recweresychronized := false;
                    FaStaging.SETFILTER(FaStaging.STATUS, '%1', 'TAGGED');
                    if FaStaging.FINDSET then begin
                        repeat
                            if FaRec.GET(FaStaging.ASSET_NUMBER) then begin
                                //To check for duplicate tag number     SAA3.0 19/7/2017
                                NotTaggedFaStaging.SETRANGE(TAG_NUMBER, FaStaging.TAG_NUMBER);
                                NotTaggedFaStaging.SETFILTER(STATUS, '%1', 'SUCCESS');
                                if not NotTaggedFaStaging.ISEMPTY then begin
                                    FaStaging.STATUS := 'REJECT';
                                    FaStaging.NAVISION_UPDATED_TIME := CURRENTDATETIME;
                                    FaStaging.NAVISION_UPDATED_USER := USERID;
                                    FaStaging.MODIFY;
                                end else   //end   SAA3.0 19/7/2017
                                    begin
                                    //FaRec."Bar Code" := FaStaging.TAG_NUMBER;CHI2.0
                                    if FaRec.MODIFY(true) then
                                        FaStaging.STATUS := 'SUCCESS' else
                                        FaStaging.STATUS := 'REJECT';
                                    FaStaging.NAVISION_UPDATED_TIME := CURRENTDATETIME;
                                    FaStaging.NAVISION_UPDATED_USER := USERID;
                                    FaStaging.MODIFY;
                                    recweresychronized := true;
                                end;
                            end;
                        until FaStaging.NEXT = 0;
                        if recweresychronized then
                            MESSAGE(text01);
                    end else
                        ERROR(text02);
                end;
            }
        }
    }

    var
        FaStaging: Record XX_TRKIT_FA_REGISTER;
        FaRec: Record "Fixed Asset";
        text01: Label 'The system has successfully sychronized';
        text02: Label 'There are no tagged records to sychronize';
        recweresychronized: Boolean;
        NotTaggedFaStaging: Record XX_TRKIT_FA_REGISTER;
}

